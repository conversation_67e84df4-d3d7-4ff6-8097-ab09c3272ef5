"""
数据转换器模块

为实时数据处理提供各种数据转换功能，包括K线生成、技术指标计算等。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple, Union
import datetime as dt
import pandas as pd
import numpy as np
from collections import defaultdict, deque


class DataTransformer(ABC):
    """
    数据转换器基类
    
    定义了数据转换器的标准接口，所有具体转换器都应该继承此类。
    """
    
    def __init__(self, name: str = None):
        """
        初始化数据转换器
        
        参数:
            name: 转换器名称，如果为None则使用类名
        """
        self.name = name or self.__class__.__name__
    
    @abstractmethod
    def transform(self, data_type: str, symbol: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        转换数据
        
        参数:
            data_type: 数据类型，如 'tick', 'bar', 'quote' 等
            symbol: 资产代码
            data: 原始数据
            
        返回:
            转换后的数据，如果返回None则表示不生成转换结果
        """
        pass
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.name}"


class KLineGenerator(DataTransformer):
    """
    K线生成器
    
    根据Tick数据生成不同周期的K线。
    """
    
    def __init__(self, timeframe: str = "1m", name: str = None):
        """
        初始化K线生成器
        
        参数:
            timeframe: 时间周期，支持的格式有：
                       - 分钟: '1m', '5m', '15m', '30m'
                       - 小时: '1h', '2h', '4h'
                       - 日: '1d'
            name: 转换器名称
        """
        super().__init__(name or f"KLineGenerator_{timeframe}")
        self.timeframe = timeframe
        
        # 解析时间周期
        self.period_value, self.period_unit = self._parse_timeframe(timeframe)
        
        # K线缓存，格式: {symbol: {timeframe: current_bar}}
        self.bars = defaultdict(lambda: {})
        
        # 历史K线缓存，用于技术指标计算
        self.history = defaultdict(lambda: defaultdict(lambda: deque(maxlen=100)))
    
    def _parse_timeframe(self, timeframe: str) -> Tuple[int, str]:
        """
        解析时间周期字符串
        
        参数:
            timeframe: 时间周期字符串，如 '1m', '5m', '1h', '1d'
            
        返回:
            (周期值, 周期单位)
        """
        if not timeframe:
            raise ValueError("时间周期不能为空")
            
        # 提取数字和单位
        value = ""
        unit = ""
        
        for char in timeframe:
            if char.isdigit():
                value += char
            else:
                unit += char
        
        if not value or not unit:
            raise ValueError(f"无效的时间周期格式: {timeframe}")
            
        return int(value), unit
    
    def _get_bar_period(self, timestamp: dt.datetime) -> dt.datetime:
        """
        计算时间戳所属的K线周期的开始时间
        
        参数:
            timestamp: 数据时间戳
            
        返回:
            K线周期的开始时间
        """
        if self.period_unit == 'm':
            # 分钟级别
            minutes = timestamp.minute // self.period_value * self.period_value
            return timestamp.replace(minute=minutes, second=0, microsecond=0)
        elif self.period_unit == 'h':
            # 小时级别
            hours = timestamp.hour // self.period_value * self.period_value
            return timestamp.replace(hour=hours, minute=0, second=0, microsecond=0)
        elif self.period_unit == 'd':
            # 日级别
            return timestamp.replace(hour=0, minute=0, second=0, microsecond=0)
        else:
            raise ValueError(f"不支持的时间周期单位: {self.period_unit}")
    
    def transform(self, data_type: str, symbol: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理数据并生成K线
        
        参数:
            data_type: 数据类型，必须是 'tick' 或 'trade'
            symbol: 资产代码
            data: 原始数据
            
        返回:
            如果生成了新的K线，返回K线数据；否则返回None
        """
        # 只处理tick和trade类型的数据
        if data_type not in ['tick', 'trade']:
            return None
            
        # 获取时间戳和价格
        timestamp = data.get('timestamp', dt.datetime.now())
        if isinstance(timestamp, str):
            try:
                timestamp = dt.datetime.fromisoformat(timestamp)
            except ValueError:
                timestamp = dt.datetime.now()
        
        # 根据数据类型获取价格
        if data_type == 'tick':
            price = data.get('price', 0)
            volume = data.get('volume', 0)
        elif data_type == 'trade':
            price = data.get('price', 0)
            volume = data.get('quantity', 0)
        else:
            return None
            
        if price <= 0:
            return None
            
        # 计算当前时间所属的K线周期
        bar_time = self._get_bar_period(timestamp)
        
        # 获取当前K线
        if symbol not in self.bars or self.timeframe not in self.bars[symbol]:
            # 创建新K线
            self.bars[symbol][self.timeframe] = {
                'symbol': symbol,
                'timestamp': bar_time,
                'open': price,
                'high': price,
                'low': price,
                'close': price,
                'volume': volume,
                'tick_count': 1,
                'timeframe': self.timeframe
            }
            return None
            
        current_bar = self.bars[symbol][self.timeframe]
        
        # 检查是否需要创建新K线
        if bar_time > current_bar['timestamp']:
            # 当前K线已完成，保存到历史
            completed_bar = current_bar.copy()
            self.history[symbol][self.timeframe].append(completed_bar)
            
            # 创建新K线
            self.bars[symbol][self.timeframe] = {
                'symbol': symbol,
                'timestamp': bar_time,
                'open': price,
                'high': price,
                'low': price,
                'close': price,
                'volume': volume,
                'tick_count': 1,
                'timeframe': self.timeframe
            }
            
            # 返回完成的K线
            return completed_bar
        else:
            # 更新当前K线
            current_bar['high'] = max(current_bar['high'], price)
            current_bar['low'] = min(current_bar['low'], price)
            current_bar['close'] = price
            current_bar['volume'] += volume
            current_bar['tick_count'] += 1
            return None
    
    def get_current_bar(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        获取当前正在形成的K线
        
        参数:
            symbol: 资产代码
            
        返回:
            当前K线数据，如果不存在则返回None
        """
        if symbol in self.bars and self.timeframe in self.bars[symbol]:
            return self.bars[symbol][self.timeframe].copy()
        return None
    
    def get_history_bars(self, symbol: str, count: int = 10) -> List[Dict[str, Any]]:
        """
        获取历史K线数据
        
        参数:
            symbol: 资产代码
            count: 获取的K线数量
            
        返回:
            历史K线列表，按时间从旧到新排序
        """
        if symbol not in self.history or self.timeframe not in self.history[symbol]:
            return []
            
        # 获取历史K线并转换为列表
        bars = list(self.history[symbol][self.timeframe])
        
        # 如果当前有未完成的K线，也加入列表
        current_bar = self.get_current_bar(symbol)
        if current_bar:
            bars.append(current_bar)
            
        # 返回最近的count个K线
        return bars[-count:]


class TechnicalIndicatorCalculator(DataTransformer):
    """
    技术指标计算器
    
    基于K线数据计算常用技术指标。
    """
    
    def __init__(self, indicator_type: str, params: Dict[str, Any] = None, name: str = None):
        """
        初始化技术指标计算器
        
        参数:
            indicator_type: 指标类型，如 'MA', 'EMA', 'RSI', 'MACD' 等
            params: 指标参数，如 {'window': 14} 等
            name: 转换器名称
        """
        super().__init__(name or f"{indicator_type}Calculator")
        self.indicator_type = indicator_type
        self.params = params or {}
        
        # 历史数据缓存
        self.data_cache = defaultdict(lambda: defaultdict(lambda: deque(maxlen=500)))
    
    def transform(self, data_type: str, symbol: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        计算技术指标
        
        参数:
            data_type: 数据类型，必须是 'bar'
            symbol: 资产代码
            data: K线数据
            
        返回:
            计算出的技术指标，如果无法计算则返回None
        """
        # 只处理bar类型数据
        if data_type != 'bar':
            return None
            
        # 更新数据缓存
        self.data_cache[symbol][data_type].append(data)
        
        # 检查是否有足够的数据计算指标
        min_required = max(
            self.params.get('window', 20),
            self.params.get('long_window', 26),
            50  # 确保有足够的数据计算某些需要更多历史的指标
        )
        
        if len(self.data_cache[symbol][data_type]) < min_required:
            return None
            
        # 创建DataFrame
        bars = list(self.data_cache[symbol][data_type])
        df = pd.DataFrame([
            {
                'timestamp': bar['timestamp'],
                'open': bar['open'],
                'high': bar['high'],
                'low': bar['low'],
                'close': bar['close'],
                'volume': bar['volume']
            }
            for bar in bars
        ])
        
        # 根据指标类型计算
        result = None
        
        try:
            if self.indicator_type == 'MA':
                result = self._calculate_ma(df)
            elif self.indicator_type == 'EMA':
                result = self._calculate_ema(df)
            elif self.indicator_type == 'RSI':
                result = self._calculate_rsi(df)
            elif self.indicator_type == 'MACD':
                result = self._calculate_macd(df)
            elif self.indicator_type == 'BOLL':
                result = self._calculate_bollinger(df)
            # 可以添加更多指标类型
        except Exception as e:
            print(f"计算技术指标 {self.indicator_type} 出错: {e}")
            return None
            
        if result is None:
            return None
            
        # 构建最终结果
        latest = result.iloc[-1].to_dict()
        return {
            'symbol': symbol,
            'timestamp': data['timestamp'],
            'indicator_type': self.indicator_type,
            'params': self.params,
            'values': latest
        }
    
    def _calculate_ma(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算移动平均线"""
        window = self.params.get('window', 20)
        df['MA'] = df['close'].rolling(window=window).mean()
        return df
    
    def _calculate_ema(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算指数移动平均线"""
        window = self.params.get('window', 20)
        df['EMA'] = df['close'].ewm(span=window, adjust=False).mean()
        return df
    
    def _calculate_rsi(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算相对强弱指标"""
        window = self.params.get('window', 14)
        
        # 计算价格变化
        df['change'] = df['close'].diff()
        
        # 计算上涨和下跌
        df['gain'] = df['change'].apply(lambda x: x if x > 0 else 0)
        df['loss'] = df['change'].apply(lambda x: -x if x < 0 else 0)
        
        # 计算平均上涨和下跌
        avg_gain = df['gain'].rolling(window=window).mean()
        avg_loss = df['loss'].rolling(window=window).mean()
        
        # 计算相对强弱
        rs = avg_gain / avg_loss
        
        # 计算RSI
        df['RSI'] = 100 - (100 / (1 + rs))
        
        return df
    
    def _calculate_macd(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算MACD指标"""
        fast_window = self.params.get('fast_window', 12)
        slow_window = self.params.get('slow_window', 26)
        signal_window = self.params.get('signal_window', 9)
        
        # 计算快线和慢线EMA
        df['EMA_fast'] = df['close'].ewm(span=fast_window, adjust=False).mean()
        df['EMA_slow'] = df['close'].ewm(span=slow_window, adjust=False).mean()
        
        # 计算MACD线
        df['MACD'] = df['EMA_fast'] - df['EMA_slow']
        
        # 计算信号线
        df['MACD_signal'] = df['MACD'].ewm(span=signal_window, adjust=False).mean()
        
        # 计算柱状图
        df['MACD_hist'] = df['MACD'] - df['MACD_signal']
        
        return df
    
    def _calculate_bollinger(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算布林带指标"""
        window = self.params.get('window', 20)
        num_std = self.params.get('num_std', 2)
        
        # 计算移动平均线
        df['BOLL_mid'] = df['close'].rolling(window=window).mean()
        
        # 计算标准差
        df['BOLL_std'] = df['close'].rolling(window=window).std()
        
        # 计算上轨和下轨
        df['BOLL_upper'] = df['BOLL_mid'] + (df['BOLL_std'] * num_std)
        df['BOLL_lower'] = df['BOLL_mid'] - (df['BOLL_std'] * num_std)
        
        return df


class DataFilter(ABC):
    """
    数据过滤器基类
    
    定义了数据过滤器的标准接口，所有具体过滤器都应该继承此类。
    """
    
    def __init__(self, name: str = None):
        """
        初始化数据过滤器
        
        参数:
            name: 过滤器名称，如果为None则使用类名
        """
        self.name = name or self.__class__.__name__
    
    @abstractmethod
    def filter(self, data_type: str, symbol: str, data: Dict[str, Any]) -> bool:
        """
        过滤数据
        
        参数:
            data_type: 数据类型，如 'tick', 'bar', 'quote' 等
            symbol: 资产代码
            data: 原始数据
            
        返回:
            True表示保留数据，False表示过滤掉数据
        """
        pass
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.name}"


class OutlierFilter(DataFilter):
    """
    异常值过滤器
    
    过滤掉异常的价格数据，如超过历史均值正负N个标准差的价格。
    """
    
    def __init__(self, threshold: float = 3.0, window: int = 100, name: str = None):
        """
        初始化异常值过滤器
        
        参数:
            threshold: 标准差阈值，超过均值±threshold个标准差被视为异常
            window: 计算均值和标准差的历史窗口大小
            name: 过滤器名称
        """
        super().__init__(name or f"OutlierFilter_{threshold}std")
        self.threshold = threshold
        self.window = window
        
        # 历史价格缓存
        self.price_history = defaultdict(lambda: deque(maxlen=window))
        
        # 缓存的统计量
        self.stats = defaultdict(lambda: {'mean': 0, 'std': 0, 'count': 0})
    
    def filter(self, data_type: str, symbol: str, data: Dict[str, Any]) -> bool:
        """
        过滤异常价格数据
        
        参数:
            data_type: 数据类型
            symbol: 资产代码
            data: 原始数据
            
        返回:
            True表示正常数据，False表示异常数据
        """
        # 获取价格
        price = None
        if data_type == 'tick':
            price = data.get('price')
        elif data_type == 'bar':
            price = data.get('close')
        elif data_type == 'quote':
            bid = data.get('bid')
            ask = data.get('ask')
            if bid and ask:
                price = (bid + ask) / 2
        
        if price is None or price <= 0:
            # 无效价格，不做过滤
            return True
            
        # 更新价格历史
        self.price_history[symbol].append(price)
        
        # 若数据量不足，不做过滤
        if len(self.price_history[symbol]) < self.window * 0.5:  # 至少需要窗口大小的一半
            return True
            
        # 计算或更新统计量
        prices = np.array(self.price_history[symbol])
        mean = np.mean(prices)
        std = np.std(prices)
        
        self.stats[symbol] = {
            'mean': mean,
            'std': std,
            'count': len(prices)
        }
        
        # 判断是否异常
        if std > 0:  # 避免除以零
            z_score = abs(price - mean) / std
            return z_score <= self.threshold
        
        return True


class PriceRangeFilter(DataFilter):
    """
    价格范围过滤器
    
    过滤掉不在指定价格范围内的数据。
    """
    
    def __init__(self, min_price: float = 0, max_price: float = float('inf'), name: str = None):
        """
        初始化价格范围过滤器
        
        参数:
            min_price: 最小价格
            max_price: 最大价格
            name: 过滤器名称
        """
        super().__init__(name or f"PriceRangeFilter_{min_price}_{max_price}")
        self.min_price = min_price
        self.max_price = max_price
    
    def filter(self, data_type: str, symbol: str, data: Dict[str, Any]) -> bool:
        """
        过滤价格范围外的数据
        
        参数:
            data_type: 数据类型
            symbol: 资产代码
            data: 原始数据
            
        返回:
            True表示在范围内，False表示在范围外
        """
        # 获取价格
        price = None
        if data_type == 'tick':
            price = data.get('price')
        elif data_type == 'bar':
            price = data.get('close')
        elif data_type == 'quote':
            bid = data.get('bid')
            ask = data.get('ask')
            if bid and ask:
                price = (bid + ask) / 2
        
        if price is None:
            # 无价格数据，默认不过滤
            return True
            
        # 检查价格是否在范围内
        return self.min_price <= price <= self.max_price


class StaleDataFilter(DataFilter):
    """
    过时数据过滤器
    
    过滤掉时间戳太旧的数据。
    """
    
    def __init__(self, max_age_seconds: float = 60.0, name: str = None):
        """
        初始化过时数据过滤器
        
        参数:
            max_age_seconds: 数据最大年龄(秒)
            name: 过滤器名称
        """
        super().__init__(name or f"StaleDataFilter_{max_age_seconds}s")
        self.max_age_seconds = max_age_seconds
    
    def filter(self, data_type: str, symbol: str, data: Dict[str, Any]) -> bool:
        """
        过滤过时数据
        
        参数:
            data_type: 数据类型
            symbol: 资产代码
            data: 原始数据
            
        返回:
            True表示数据新鲜，False表示数据过时
        """
        # 获取时间戳
        timestamp = data.get('timestamp')
        
        if timestamp is None:
            # 无时间戳，默认不过滤
            return True
            
        # 如果时间戳是字符串，转换为datetime对象
        if isinstance(timestamp, str):
            try:
                timestamp = dt.datetime.fromisoformat(timestamp)
            except ValueError:
                # 无法解析时间戳，默认不过滤
                return True
        
        # 计算数据年龄
        now = dt.datetime.now()
        if timestamp.tzinfo:
            now = now.replace(tzinfo=timestamp.tzinfo)
            
        age = (now - timestamp).total_seconds()
        
        # 检查是否过时
        return age <= self.max_age_seconds 