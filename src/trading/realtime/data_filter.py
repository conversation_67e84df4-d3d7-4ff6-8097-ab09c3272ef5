"""
数据过滤器管理模块

提供数据过滤器的组织和管理功能，支持多过滤器链式处理。
"""

import logging
from typing import Dict, List, Any, Optional, Set, Callable, Union
from abc import ABC, abstractmethod
import datetime as dt

# {{ AURA-X: Add - 增强实时数据处理能力. Approval: 寸止(ID:实时数据处理). }}
# 移除错误的导入，DataFilter类在本文件中定义


class DataFilter:
    """
    数据过滤器基类

    定义数据过滤器的基本接口和行为。
    """

    def __init__(self, name: str):
        """
        初始化数据过滤器

        参数:
            name: 过滤器名称
        """
        self.name = name
        self.enabled = True
        self.stats = {
            'total_processed': 0,
            'total_filtered': 0,
            'filter_rate': 0.0
        }

    def should_process(self, data_type: str, symbol: str, data: Dict[str, Any]) -> bool:
        """
        判断是否应该处理数据

        参数:
            data_type: 数据类型
            symbol: 资产代码
            data: 数据内容

        返回:
            bool: True表示应该处理，False表示应该过滤掉
        """
        if not self.enabled:
            return True

        self.stats['total_processed'] += 1

        # 子类需要重写此方法
        result = self._filter_logic(data_type, symbol, data)

        if not result:
            self.stats['total_filtered'] += 1

        # 更新过滤率
        if self.stats['total_processed'] > 0:
            self.stats['filter_rate'] = self.stats['total_filtered'] / self.stats['total_processed']

        return result

    def _filter_logic(self, data_type: str, symbol: str, data: Dict[str, Any]) -> bool:
        """
        过滤逻辑实现（子类重写）

        参数:
            data_type: 数据类型
            symbol: 资产代码
            data: 数据内容

        返回:
            bool: True表示通过过滤，False表示被过滤
        """
        return True

    def enable(self):
        """启用过滤器"""
        self.enabled = True

    def disable(self):
        """禁用过滤器"""
        self.enabled = False

    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_processed': 0,
            'total_filtered': 0,
            'filter_rate': 0.0
        }

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()


class FilterChain:
    """
    过滤器链
    
    管理多个数据过滤器，按顺序应用它们。
    """
    
    def __init__(self, name: str = "FilterChain"):
        """
        初始化过滤器链
        
        参数:
            name: 过滤器链名称
        """
        self.name = name
        self.filters: List[DataFilter] = []
        self.logger = logging.getLogger(f"trading.realtime.{name}")
        
        # 统计信息
        self.stats = {
            'processed': 0,
            'passed': 0,
            'filtered': 0,
            'by_filter': {},  # 各过滤器过滤的数据计数
            'by_symbol': {},  # 各资产过滤的数据计数
            'by_type': {}     # 各数据类型过滤的数据计数
        }
    
    def add_filter(self, filter_obj: DataFilter) -> None:
        """
        添加过滤器到链
        
        参数:
            filter_obj: 数据过滤器对象
        """
        self.filters.append(filter_obj)
        self.stats['by_filter'][filter_obj.name] = 0
        self.logger.info(f"添加过滤器: {filter_obj.name}")
    
    def remove_filter(self, filter_name: str) -> bool:
        """
        从链中移除过滤器
        
        参数:
            filter_name: 过滤器名称
            
        返回:
            是否成功移除
        """
        for i, filter_obj in enumerate(self.filters):
            if filter_obj.name == filter_name:
                del self.filters[i]
                self.logger.info(f"移除过滤器: {filter_name}")
                return True
                
        self.logger.warning(f"尝试移除不存在的过滤器: {filter_name}")
        return False
    
    def apply(self, data_type: str, symbol: str, data: Dict[str, Any]) -> bool:
        """
        应用所有过滤器
        
        参数:
            data_type: 数据类型
            symbol: 资产代码
            data: 数据内容
            
        返回:
            是否通过所有过滤器
        """
        self.stats['processed'] += 1
        
        # 更新资产和类型统计
        if symbol not in self.stats['by_symbol']:
            self.stats['by_symbol'][symbol] = 0
            
        if data_type not in self.stats['by_type']:
            self.stats['by_type'][data_type] = 0
        
        # 检查是否有过滤器
        if not self.filters:
            self.stats['passed'] += 1
            return True
            
        # 应用所有过滤器
        for filter_obj in self.filters:
            try:
                if not filter_obj.filter(data_type, symbol, data):
                    # 数据被过滤掉
                    self.stats['filtered'] += 1
                    self.stats['by_filter'][filter_obj.name] += 1
                    self.stats['by_symbol'][symbol] += 1
                    self.stats['by_type'][data_type] += 1
                    
                    if self.logger.isEnabledFor(logging.DEBUG):
                        self.logger.debug(
                            f"数据被过滤: {data_type}.{symbol} "
                            f"(过滤器: {filter_obj.name})"
                        )
                    
                    return False
            except Exception as e:
                self.logger.error(
                    f"过滤器异常: {filter_obj.name}, {e}", 
                    exc_info=True
                )
                # 继续应用下一个过滤器，不因一个过滤器异常而中断
        
        # 通过所有过滤器
        self.stats['passed'] += 1
        return True
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        # 保留过滤器名称
        filter_names = list(self.stats['by_filter'].keys())
        
        # 清空统计
        self.stats = {
            'processed': 0,
            'passed': 0,
            'filtered': 0,
            'by_filter': {name: 0 for name in filter_names},
            'by_symbol': {},
            'by_type': {}
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取过滤器链统计信息
        
        返回:
            统计信息字典
        """
        # 复制基本统计信息
        stats = {
            'processed': self.stats['processed'],
            'passed': self.stats['passed'],
            'filtered': self.stats['filtered'],
            'pass_rate': (self.stats['passed'] / self.stats['processed'] * 100) 
                         if self.stats['processed'] > 0 else 100,
            'filter_count': len(self.filters),
            'filters': [f.name for f in self.filters],
            'by_filter': dict(sorted(
                self.stats['by_filter'].items(), 
                key=lambda x: x[1], 
                reverse=True
            )),
            'top_filtered_symbols': dict(sorted(
                self.stats['by_symbol'].items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:10]),
            'top_filtered_types': dict(sorted(
                self.stats['by_type'].items(), 
                key=lambda x: x[1], 
                reverse=True
            ))
        }
        
        return stats
    
    def __str__(self) -> str:
        """字符串表示"""
        if self.stats['processed'] == 0:
            rate = 100
        else:
            rate = self.stats['passed'] / self.stats['processed'] * 100
            
        return (
            f"{self.name}[过滤器数量: {len(self.filters)}, "
            f"处理: {self.stats['processed']}, "
            f"通过率: {rate:.1f}%]"
        )


class FilterManager:
    """
    过滤器管理器
    
    管理多个过滤器链，针对不同数据类型应用不同的过滤器链。
    """
    
    def __init__(self, name: str = "FilterManager"):
        """
        初始化过滤器管理器
        
        参数:
            name: 管理器名称
        """
        self.name = name
        
        # 按数据类型组织的过滤器链
        self.filter_chains: Dict[str, FilterChain] = {}
        
        # 默认过滤器链，用于未指定特定过滤器链的数据类型
        self.default_chain = FilterChain(name="DefaultChain")
        
        self.logger = logging.getLogger(f"trading.realtime.{name}")
    
    def create_filter_chain(self, data_type: str, name: Optional[str] = None) -> FilterChain:
        """
        为指定数据类型创建过滤器链
        
        参数:
            data_type: 数据类型
            name: 过滤器链名称，如果为None则使用数据类型作为名称
            
        返回:
            创建的过滤器链
        """
        chain_name = name or f"{data_type}Chain"
        chain = FilterChain(name=chain_name)
        
        self.filter_chains[data_type] = chain
        self.logger.info(f"为数据类型 {data_type} 创建过滤器链: {chain_name}")
        
        return chain
    
    def get_filter_chain(self, data_type: str) -> FilterChain:
        """
        获取指定数据类型的过滤器链
        
        参数:
            data_type: 数据类型
            
        返回:
            过滤器链，如果不存在则返回默认链
        """
        return self.filter_chains.get(data_type, self.default_chain)
    
    def remove_filter_chain(self, data_type: str) -> bool:
        """
        移除指定数据类型的过滤器链
        
        参数:
            data_type: 数据类型
            
        返回:
            是否成功移除
        """
        if data_type in self.filter_chains:
            del self.filter_chains[data_type]
            self.logger.info(f"移除数据类型 {data_type} 的过滤器链")
            return True
            
        return False
    
    def add_filter(self, filter_obj: DataFilter, data_types: Optional[List[str]] = None) -> None:
        """
        添加过滤器到指定数据类型的过滤器链
        
        参数:
            filter_obj: 数据过滤器对象
            data_types: 应用过滤器的数据类型列表，如果为None则添加到默认链
        """
        if data_types is None:
            # 添加到默认链
            self.default_chain.add_filter(filter_obj)
            self.logger.info(f"添加过滤器 {filter_obj.name} 到默认链")
        else:
            # 添加到指定数据类型的链
            for data_type in data_types:
                chain = self.get_filter_chain(data_type)
                
                # 如果该数据类型没有过滤器链，先创建一个
                if chain is self.default_chain:
                    chain = self.create_filter_chain(data_type)
                
                chain.add_filter(filter_obj)
                self.logger.info(f"添加过滤器 {filter_obj.name} 到数据类型 {data_type} 的链")
    
    def apply(self, data_type: str, symbol: str, data: Dict[str, Any]) -> bool:
        """
        应用指定数据类型的过滤器链
        
        参数:
            data_type: 数据类型
            symbol: 资产代码
            data: 数据内容
            
        返回:
            是否通过所有过滤器
        """
        # 获取对应数据类型的过滤器链
        chain = self.get_filter_chain(data_type)
        
        # 应用过滤器链
        return chain.apply(data_type, symbol, data)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取过滤器管理器统计信息
        
        返回:
            统计信息字典
        """
        # 收集所有链的统计信息
        chains_stats = {
            'default': self.default_chain.get_stats()
        }
        
        for data_type, chain in self.filter_chains.items():
            chains_stats[data_type] = chain.get_stats()
        
        # 计算总体统计
        total_processed = sum(chain.stats['processed'] for chain in [self.default_chain] + list(self.filter_chains.values()))
        total_passed = sum(chain.stats['passed'] for chain in [self.default_chain] + list(self.filter_chains.values()))
        total_filtered = sum(chain.stats['filtered'] for chain in [self.default_chain] + list(self.filter_chains.values()))
        
        # 构建管理器级别统计
        stats = {
            'total': {
                'processed': total_processed,
                'passed': total_passed,
                'filtered': total_filtered,
                'pass_rate': (total_passed / total_processed * 100) if total_processed > 0 else 100,
                'chain_count': len(self.filter_chains) + 1  # +1 for default chain
            },
            'chains': chains_stats
        }
        
        return stats
    
    def reset_stats(self) -> None:
        """重置所有统计信息"""
        self.default_chain.reset_stats()
        
        for chain in self.filter_chains.values():
            chain.reset_stats()
            
        self.logger.info("重置所有过滤器统计信息")
    
    def __str__(self) -> str:
        """字符串表示"""
        return (
            f"{self.name}[链数量: {len(self.filter_chains) + 1}, "
            f"默认链: {str(self.default_chain)}, "
            f"数据类型: {list(self.filter_chains.keys())}]"
        ) 