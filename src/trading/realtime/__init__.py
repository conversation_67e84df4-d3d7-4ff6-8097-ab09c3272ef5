"""
实时数据处理模块

提供企业级实时数据处理能力，支持：
1. 实时数据缓存和管理
2. 数据转换和技术指标计算
3. 数据过滤和验证
4. 实时策略执行
5. WebSocket数据流处理
6. 性能监控和统计

主要组件：
- RealTimeDataManager: 实时数据管理器
- RealTimeDataStream: 实时数据流处理器
- RealTimeStrategyExecutor: 实时策略执行器
- DataCache: 高性能数据缓存
- DataTransformer: 数据转换器
- DataFilter: 数据过滤器

使用示例：
```python
from src.trading.realtime import (
    get_realtime_data_manager,
    get_realtime_data_stream,
    RealTimeStrategyExecutor,
    RealTimeDataConfig,
    DataType
)

# 创建实时数据管理器
config = RealTimeDataConfig(
    cache_max_size=100000,
    enable_kline_generation=True,
    enable_indicator_calculation=True
)
data_manager = get_realtime_data_manager(config)

# 启动数据管理器
data_manager.start()

# 创建数据流处理器
data_stream = get_realtime_data_stream(data_manager)

# 启动WebSocket数据流
await data_stream.start_websocket_stream(
    url="ws://localhost:8080/ws",
    symbols=["AAPL", "GOOGL", "MSFT"],
    data_types=[DataType.TICK, DataType.TRADE]
)

# 创建实时策略执行器
from src.strategy.factory import StrategyFactory
strategy = StrategyFactory.create_strategy('moving_average')
executor = RealTimeStrategyExecutor(strategy, data_manager)

# 启动策略执行
executor.start()

# 获取性能统计
stats = data_manager.get_performance_stats()
print(f"数据处理统计: {stats}")

# 清理资源
executor.cleanup()
data_stream.stop_stream()
data_manager.cleanup()
```
"""

# 核心组件
from .data_cache import DataCache, SymbolDataCache
from .data_transformer import KLineGenerator, TechnicalIndicatorCalculator
from .data_filter import DataFilter

# 实时数据管理
from .realtime_data_manager import (
    RealTimeDataManager,
    RealTimeDataStream,
    RealTimeDataFactory,
    RealTimeDataConfig,
    DataType,
    get_realtime_data_manager,
    get_realtime_data_stream
)

# 实时策略执行
from .realtime_strategy_executor import (
    RealTimeStrategyExecutor,
    TradingSignal,
    Order,
    Position,
    SignalType,
    OrderStatus
)

# 导出所有公共接口
__all__ = [
    # 数据缓存
    'DataCache',
    'SymbolDataCache',
    
    # 数据转换
    'KLineGenerator',
    'TechnicalIndicatorCalculator',
    
    # 数据过滤
    'DataFilter',
    
    # 实时数据管理
    'RealTimeDataManager',
    'RealTimeDataStream',
    'RealTimeDataFactory',
    'RealTimeDataConfig',
    'DataType',
    'get_realtime_data_manager',
    'get_realtime_data_stream',
    
    # 实时策略执行
    'RealTimeStrategyExecutor',
    'TradingSignal',
    'Order',
    'Position',
    'SignalType',
    'OrderStatus'
]

# 版本信息
__version__ = "1.0.0"
__author__ = "Quantification Platform Team"
__description__ = "Enterprise-grade real-time data processing for quantitative trading"
