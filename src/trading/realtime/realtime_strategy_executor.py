"""
实时策略执行器

支持实时策略信号生成和交易执行，集成实时数据处理和风险控制。
"""

import time
import threading
import asyncio
import logging
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from .realtime_data_manager import RealTimeDataManager, DataType, get_realtime_data_manager
from src.strategy.strategies.base_strategy import BaseStrategy
from src.strategy.strategies.strategy_interface import StrategyInterface


class SignalType(Enum):
    """信号类型"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    CLOSE = "close"


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


@dataclass
class TradingSignal:
    """交易信号"""
    symbol: str
    signal_type: SignalType
    price: float
    quantity: int
    timestamp: datetime
    confidence: float = 1.0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class Order:
    """订单"""
    order_id: str
    symbol: str
    signal_type: SignalType
    price: float
    quantity: int
    status: OrderStatus
    created_time: datetime
    filled_time: Optional[datetime] = None
    filled_price: Optional[float] = None
    filled_quantity: int = 0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class Position:
    """持仓"""
    symbol: str
    quantity: int
    avg_price: float
    market_value: float
    unrealized_pnl: float
    last_update: datetime


class RealTimeStrategyExecutor:
    """
    实时策略执行器
    
    功能：
    1. 实时数据接收和处理
    2. 策略信号生成
    3. 风险控制检查
    4. 订单生成和管理
    5. 持仓管理
    6. 性能监控
    """
    
    def __init__(self, 
                 strategy: BaseStrategy,
                 data_manager: Optional[RealTimeDataManager] = None,
                 initial_capital: float = 1000000.0):
        """
        初始化实时策略执行器
        
        参数:
            strategy: 策略实例
            data_manager: 实时数据管理器
            initial_capital: 初始资金
        """
        self.strategy = strategy
        self.data_manager = data_manager or get_realtime_data_manager()
        self.initial_capital = initial_capital
        self.logger = logging.getLogger(__name__)
        
        # 交易状态
        self.is_running = False
        self.current_capital = initial_capital
        self.positions: Dict[str, Position] = {}
        self.orders: Dict[str, Order] = {}
        self.signals_history: List[TradingSignal] = []
        
        # 风险控制参数
        self.max_position_size = 0.1  # 单个持仓最大占比
        self.max_daily_loss = 0.05    # 最大日损失
        self.max_total_positions = 20  # 最大持仓数量
        
        # 性能统计
        self.stats = {
            'signals_generated': 0,
            'orders_placed': 0,
            'orders_filled': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'last_update': datetime.now()
        }
        
        # 线程管理
        self.execution_lock = threading.RLock()
        self.monitor_thread = None
        
        # 注册数据事件处理器
        self._register_data_handlers()
    
    def _register_data_handlers(self):
        """注册数据事件处理器"""
        # 注册K线数据处理器
        self.data_manager.add_event_handler(DataType.KLINE, self._on_kline_data)
        
        # 注册技术指标处理器
        self.data_manager.add_event_handler(DataType.INDICATOR, self._on_indicator_data)
        
        # 注册tick数据处理器（用于价格更新）
        self.data_manager.add_event_handler(DataType.TICK, self._on_tick_data)
    
    def start(self):
        """启动实时策略执行"""
        if self.is_running:
            self.logger.warning("策略执行器已在运行")
            return
        
        self.is_running = True
        
        # 启动数据管理器
        self.data_manager.start()
        
        # 启动策略
        try:
            context = {
                'current_date': datetime.now().strftime('%Y-%m-%d'),
                'current_time': datetime.now(),
                'initial_capital': self.initial_capital
            }
            self.strategy.before_trading_start(context)
        except (TypeError, Exception) as e:
            # 兼容不需要context参数的策略或其他错误
            self.logger.warning(f"策略启动警告: {e}")
            pass
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_performance, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info("实时策略执行器已启动")
    
    def stop(self):
        """停止实时策略执行"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 停止策略
        try:
            context = {
                'current_date': datetime.now().strftime('%Y-%m-%d'),
                'current_time': datetime.now(),
                'final_capital': self.current_capital
            }
            self.strategy.after_trading_end(context)
        except (TypeError, Exception) as e:
            # 兼容不需要context参数的策略或其他错误
            self.logger.warning(f"策略停止警告: {e}")
            pass
        
        # 停止数据管理器
        self.data_manager.stop()
        
        # 等待监控线程结束
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5.0)
        
        self.logger.info("实时策略执行器已停止")
    
    def _on_kline_data(self, symbol: str, data: Dict[str, Any]):
        """处理K线数据"""
        try:
            with self.execution_lock:
                # 更新策略数据
                self.strategy.handle_data(symbol, data)
                
                # 生成交易信号
                signals = self.strategy.generate_signals([symbol])
                
                if signals and symbol in signals:
                    signal_value = signals[symbol]
                    if signal_value != 0:  # 有信号
                        signal_type = SignalType.BUY if signal_value > 0 else SignalType.SELL
                        
                        # 创建交易信号
                        signal = TradingSignal(
                            symbol=symbol,
                            signal_type=signal_type,
                            price=data.get('close', 0),
                            quantity=self._calculate_position_size(symbol, signal_value),
                            timestamp=datetime.now(),
                            confidence=abs(signal_value),
                            metadata={'source': 'kline', 'data': data}
                        )
                        
                        # 处理信号
                        self._process_signal(signal)
                        
        except Exception as e:
            self.logger.error(f"处理K线数据失败 {symbol}: {e}")
    
    def _on_indicator_data(self, symbol: str, data: Dict[str, Any]):
        """处理技术指标数据"""
        try:
            # 可以基于技术指标生成额外的信号
            # 这里暂时只记录日志
            self.logger.debug(f"收到技术指标数据 {symbol}: {data.get('indicator_type')}")
        except Exception as e:
            self.logger.error(f"处理技术指标数据失败 {symbol}: {e}")
    
    def _on_tick_data(self, symbol: str, data: Dict[str, Any]):
        """处理tick数据"""
        try:
            # 更新持仓市值
            if symbol in self.positions:
                current_price = data.get('price', 0)
                if current_price > 0:
                    self._update_position_value(symbol, current_price)
        except Exception as e:
            self.logger.error(f"处理tick数据失败 {symbol}: {e}")
    
    def _process_signal(self, signal: TradingSignal):
        """处理交易信号"""
        try:
            # 风险控制检查
            if not self._risk_check(signal):
                self.logger.warning(f"信号未通过风险检查: {signal.symbol} {signal.signal_type.value}")
                return
            
            # 生成订单
            order = self._create_order(signal)
            if order:
                # 模拟订单执行（实际环境中会发送到交易系统）
                self._execute_order(order)
                
                # 记录信号
                self.signals_history.append(signal)
                self.stats['signals_generated'] += 1
                
        except Exception as e:
            self.logger.error(f"处理交易信号失败: {e}")
    
    def _risk_check(self, signal: TradingSignal) -> bool:
        """风险控制检查"""
        # 检查持仓数量限制
        if len(self.positions) >= self.max_total_positions and signal.symbol not in self.positions:
            return False
        
        # 检查单个持仓大小限制
        position_value = signal.price * signal.quantity
        if position_value > self.current_capital * self.max_position_size:
            return False
        
        # 检查日损失限制
        daily_pnl = self._calculate_daily_pnl()
        if daily_pnl < -self.current_capital * self.max_daily_loss:
            return False
        
        return True
    
    def _calculate_position_size(self, symbol: str, signal_strength: float) -> int:
        """计算持仓大小"""
        # 基于信号强度和风险控制计算持仓大小
        max_value = self.current_capital * self.max_position_size
        signal_value = max_value * abs(signal_strength)
        
        # 获取最新价格
        latest_data = self.data_manager.get_latest_data(symbol, 'tick')
        if latest_data:
            price = latest_data.get('price', 1)
            quantity = int(signal_value / price)
            return max(100, quantity)  # 最小100股
        
        return 100
    
    def _create_order(self, signal: TradingSignal) -> Optional[Order]:
        """创建订单"""
        import uuid
        
        order_id = str(uuid.uuid4())
        order = Order(
            order_id=order_id,
            symbol=signal.symbol,
            signal_type=signal.signal_type,
            price=signal.price,
            quantity=signal.quantity,
            status=OrderStatus.PENDING,
            created_time=datetime.now(),
            metadata=signal.metadata
        )
        
        self.orders[order_id] = order
        return order
    
    def _execute_order(self, order: Order):
        """执行订单（模拟）"""
        try:
            # 模拟订单执行
            order.status = OrderStatus.FILLED
            order.filled_time = datetime.now()
            order.filled_price = order.price
            order.filled_quantity = order.quantity
            
            # 更新持仓
            self._update_position(order)
            
            # 更新统计
            self.stats['orders_placed'] += 1
            self.stats['orders_filled'] += 1
            
            self.logger.info(f"订单执行成功: {order.symbol} {order.signal_type.value} "
                           f"{order.quantity}@{order.price}")
            
        except Exception as e:
            order.status = OrderStatus.REJECTED
            self.logger.error(f"订单执行失败: {e}")
    
    def _update_position(self, order: Order):
        """更新持仓"""
        symbol = order.symbol
        
        if symbol not in self.positions:
            # 新建持仓
            self.positions[symbol] = Position(
                symbol=symbol,
                quantity=0,
                avg_price=0,
                market_value=0,
                unrealized_pnl=0,
                last_update=datetime.now()
            )
        
        position = self.positions[symbol]
        
        if order.signal_type == SignalType.BUY:
            # 买入
            total_cost = position.quantity * position.avg_price + order.filled_quantity * order.filled_price
            total_quantity = position.quantity + order.filled_quantity
            position.avg_price = total_cost / total_quantity if total_quantity > 0 else 0
            position.quantity = total_quantity
        elif order.signal_type == SignalType.SELL:
            # 卖出
            position.quantity -= order.filled_quantity
            if position.quantity <= 0:
                # 清仓
                del self.positions[symbol]
                return
        
        position.last_update = datetime.now()
        self._update_position_value(symbol, order.filled_price)
    
    def _update_position_value(self, symbol: str, current_price: float):
        """更新持仓市值"""
        if symbol in self.positions:
            position = self.positions[symbol]
            position.market_value = position.quantity * current_price
            position.unrealized_pnl = position.market_value - position.quantity * position.avg_price
            position.last_update = datetime.now()
    
    def _calculate_daily_pnl(self) -> float:
        """计算当日盈亏"""
        total_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        return total_pnl
    
    def _monitor_performance(self):
        """性能监控线程"""
        while self.is_running:
            try:
                time.sleep(10)  # 每10秒更新一次
                
                # 更新统计
                self._update_performance_stats()
                
            except Exception as e:
                self.logger.error(f"性能监控异常: {e}")
    
    def _update_performance_stats(self):
        """更新性能统计"""
        # 计算总盈亏
        total_pnl = self._calculate_daily_pnl()
        self.stats['total_pnl'] = total_pnl
        
        # 更新当前资金
        self.current_capital = self.initial_capital + total_pnl
        
        # 更新时间戳
        self.stats['last_update'] = datetime.now()
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return {
            **self.stats,
            'current_capital': self.current_capital,
            'positions_count': len(self.positions),
            'orders_count': len(self.orders),
            'signals_count': len(self.signals_history),
            'is_running': self.is_running
        }
    
    def get_positions(self) -> Dict[str, Position]:
        """获取当前持仓"""
        return self.positions.copy()
    
    def get_recent_signals(self, count: int = 10) -> List[TradingSignal]:
        """获取最近的信号"""
        return self.signals_history[-count:] if self.signals_history else []
    
    def cleanup(self):
        """清理资源"""
        self.stop()
        
        # 清理持仓和订单
        self.positions.clear()
        self.orders.clear()
        self.signals_history.clear()
        
        self.logger.info("实时策略执行器资源已清理")
