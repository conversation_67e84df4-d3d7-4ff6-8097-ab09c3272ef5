"""
数据缓存管理模块

提供高效的内存数据缓存管理，支持LRU和TTL策略。
"""

import time
import threading
from typing import Dict, Any, Optional, List, Tuple, Callable
from collections import OrderedDict
import datetime as dt


class DataCache:
    """
    数据缓存管理类
    
    实现高效的内存数据缓存，支持LRU（最近最少使用）和TTL（生存时间）缓存淘汰策略。
    """
    
    def __init__(self, 
                max_size: int = 10000, 
                ttl: float = 60.0, 
                cleanup_interval: float = 5.0):
        """
        初始化数据缓存管理器
        
        参数:
            max_size: 缓存最大容量
            ttl: 缓存项默认生存时间（秒）
            cleanup_interval: 自动清理间隔（秒）
        """
        self.max_size = max_size
        self.default_ttl = ttl
        self.cleanup_interval = cleanup_interval
        
        # 使用OrderedDict实现LRU
        self.cache = OrderedDict()
        
        # 存储项过期时间
        self.expire_times = {}
        
        # 统计信息
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'evictions': {
                'lru': 0,   # LRU淘汰计数
                'ttl': 0,   # TTL过期计数
                'manual': 0 # 手动删除计数
            },
            'last_cleanup': None
        }
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 启动自动清理线程
        self._start_cleanup_thread()
    
    def _start_cleanup_thread(self) -> None:
        """启动自动清理线程"""
        cleanup_thread = threading.Thread(
            target=self._cleanup_loop,
            name="DataCache-Cleanup",
            daemon=True
        )
        cleanup_thread.start()
    
    def _cleanup_loop(self) -> None:
        """定时清理循环"""
        while True:
            time.sleep(self.cleanup_interval)
            self.evict_expired()
    
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存项
        
        参数:
            key: 缓存键
            
        返回:
            缓存值，如果不存在或已过期则返回None
        """
        with self.lock:
            # 检查键是否存在
            if key not in self.cache:
                self.stats['misses'] += 1
                return None
                
            # 检查是否过期
            if self._is_expired(key):
                self._evict_item(key, reason='ttl')
                self.stats['misses'] += 1
                return None
                
            # 更新LRU顺序
            value = self.cache.pop(key)
            self.cache[key] = value
            
            self.stats['hits'] += 1
            return value
    
    def put(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """
        设置缓存项
        
        参数:
            key: 缓存键
            value: 缓存值
            ttl: 该项的生存时间（秒），如果为None则使用默认ttl
        """
        with self.lock:
            # 如果键已存在，先移除它
            if key in self.cache:
                self.cache.pop(key)
                
            # 如果达到容量上限，淘汰最旧的项
            if len(self.cache) >= self.max_size:
                self._evict_oldest()
                
            # 设置新值
            self.cache[key] = value
            
            # 设置过期时间
            expiration = time.time() + (ttl if ttl is not None else self.default_ttl)
            self.expire_times[key] = expiration
            
            self.stats['sets'] += 1
    
    def delete(self, key: str) -> bool:
        """
        删除缓存项
        
        参数:
            key: 缓存键
            
        返回:
            是否成功删除
        """
        with self.lock:
            if key in self.cache:
                self._evict_item(key, reason='manual')
                return True
            return False
    
    def exists(self, key: str) -> bool:
        """
        检查键是否存在且未过期
        
        参数:
            key: 缓存键
            
        返回:
            键是否存在且未过期
        """
        with self.lock:
            return key in self.cache and not self._is_expired(key)
    
    def clear(self) -> None:
        """清空所有缓存项"""
        with self.lock:
            # 获取当前缓存数量用于统计
            evicted_count = len(self.cache)
            
            # 清空缓存和过期时间
            self.cache.clear()
            self.expire_times.clear()
            
            # 更新统计
            self.stats['evictions']['manual'] += evicted_count
    
    def _is_expired(self, key: str) -> bool:
        """检查缓存项是否过期"""
        if key not in self.expire_times:
            return False
            
        return time.time() > self.expire_times[key]
    
    def _evict_oldest(self) -> bool:
        """淘汰最久未使用的缓存项"""
        if not self.cache:
            return False
            
        oldest_key, _ = next(iter(self.cache.items()))
        self._evict_item(oldest_key, reason='lru')
        return True
    
    def _evict_item(self, key: str, reason: str = 'manual') -> None:
        """
        淘汰指定的缓存项
        
        参数:
            key: 缓存键
            reason: 淘汰原因，可以是 'lru', 'ttl', 'manual'
        """
        if key in self.cache:
            del self.cache[key]
            
        if key in self.expire_times:
            del self.expire_times[key]
            
        # 更新统计
        if reason in self.stats['evictions']:
            self.stats['evictions'][reason] += 1
    
    def evict_expired(self) -> int:
        """
        淘汰所有过期缓存项
        
        返回:
            淘汰项数量
        """
        with self.lock:
            now = time.time()
            expired_keys = [
                key for key, expire_time in self.expire_times.items()
                if now > expire_time
            ]
            
            for key in expired_keys:
                self._evict_item(key, reason='ttl')
                
            self.stats['last_cleanup'] = dt.datetime.now()
            return len(expired_keys)
    
    def get_keys(self) -> List[str]:
        """
        获取所有有效的缓存键
        
        返回:
            有效缓存键列表
        """
        with self.lock:
            return [key for key in self.cache.keys() if not self._is_expired(key)]
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        返回:
            统计信息字典
        """
        with self.lock:
            stats = self.stats.copy()
            
            # 添加当前使用情况
            stats['size'] = len(self.cache)
            stats['capacity'] = self.max_size
            stats['usage_percent'] = (len(self.cache) / self.max_size * 100) if self.max_size > 0 else 0
            
            # 计算命中率
            total_requests = stats['hits'] + stats['misses']
            stats['hit_ratio'] = (stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            # 汇总淘汰信息
            stats['total_evictions'] = sum(stats['evictions'].values())
            
            return stats
    
    def update_ttl(self, key: str, new_ttl: float) -> bool:
        """
        更新缓存项的生存时间
        
        参数:
            key: 缓存键
            new_ttl: 新的生存时间（秒）
            
        返回:
            是否成功更新
        """
        with self.lock:
            if key not in self.cache or self._is_expired(key):
                return False
                
            # 更新过期时间
            self.expire_times[key] = time.time() + new_ttl
            return True
    
    def get_with_update(self, key: str, update_ttl: bool = True) -> Tuple[Optional[Any], bool]:
        """
        获取缓存项并可选更新其TTL
        
        参数:
            key: 缓存键
            update_ttl: 是否更新TTL
            
        返回:
            (缓存值, 是否命中)
        """
        with self.lock:
            value = self.get(key)
            hit = value is not None
            
            if hit and update_ttl:
                # 重置TTL
                self.expire_times[key] = time.time() + self.default_ttl
                
            return value, hit
    
    def get_or_compute(self, key: str, compute_func: Callable[[], Any], ttl: Optional[float] = None) -> Any:
        """
        获取缓存项，如果不存在则计算并缓存
        
        参数:
            key: 缓存键
            compute_func: 计算函数，当缓存不存在时调用
            ttl: 该项的生存时间（秒），如果为None则使用默认ttl
            
        返回:
            缓存值或计算结果
        """
        with self.lock:
            # 尝试获取缓存
            value = self.get(key)
            
            if value is not None:
                return value
                
            # 缓存不存在，调用计算函数
            value = compute_func()
            
            # 缓存计算结果
            self.put(key, value, ttl)
            
            return value
    
    def __len__(self) -> int:
        """返回缓存项数量"""
        with self.lock:
            return len(self.cache)
    
    def __contains__(self, key: str) -> bool:
        """检查键是否在缓存中且未过期"""
        return self.exists(key)
    
    def __str__(self) -> str:
        """返回缓存的字符串表示"""
        stats = self.get_stats()
        return (
            f"DataCache[size={stats['size']}/{stats['capacity']} "
            f"({stats['usage_percent']:.1f}%), "
            f"hit_ratio={stats['hit_ratio']:.1f}%, "
            f"evictions={stats['total_evictions']}]"
        )


class SymbolDataCache:
    """
    按资产代码组织的数据缓存
    
    为不同资产的不同类型数据提供缓存管理。
    """
    
    def __init__(self, 
                max_symbols: int = 1000, 
                max_data_per_symbol: int = 100,
                ttl: float = 300.0):
        """
        初始化资产数据缓存
        
        参数:
            max_symbols: 最大资产数量
            max_data_per_symbol: 每个资产的最大数据项数量
            ttl: 缓存项默认生存时间（秒）
        """
        self.max_symbols = max_symbols
        self.max_data_per_symbol = max_data_per_symbol
        self.ttl = ttl
        
        # 创建主缓存
        self.cache = DataCache(max_size=max_symbols, ttl=ttl * 2)
        
        # 资产访问频率统计
        self.symbol_access_count = {}
        
        # 线程锁
        self.lock = threading.RLock()
    
    def get(self, symbol: str, data_type: str) -> Optional[Any]:
        """
        获取特定资产的特定类型数据
        
        参数:
            symbol: 资产代码
            data_type: 数据类型
            
        返回:
            缓存数据，如果不存在则返回None
        """
        with self.lock:
            # 更新访问统计
            self.symbol_access_count[symbol] = self.symbol_access_count.get(symbol, 0) + 1
            
            # 检查符号缓存是否存在
            symbol_cache = self.cache.get(symbol)
            if symbol_cache is None:
                return None
                
            # 获取特定类型数据
            return symbol_cache.get(data_type)
    
    def put(self, symbol: str, data_type: str, data: Any, ttl: Optional[float] = None) -> None:
        """
        设置特定资产的特定类型数据
        
        参数:
            symbol: 资产代码
            data_type: 数据类型
            data: 数据内容
            ttl: 该项的生存时间（秒），如果为None则使用默认ttl
        """
        with self.lock:
            # 更新访问统计
            self.symbol_access_count[symbol] = self.symbol_access_count.get(symbol, 0) + 1
            
            # 获取符号缓存，如果不存在则创建
            symbol_cache = self.cache.get(symbol)
            if symbol_cache is None:
                symbol_cache = DataCache(
                    max_size=self.max_data_per_symbol, 
                    ttl=ttl if ttl is not None else self.ttl
                )
                
                # 如果达到最大资产数，淘汰访问频率最低的资产
                if len(self.cache) >= self.max_symbols:
                    self._evict_least_accessed_symbol()
                
                # 添加到主缓存
                self.cache.put(symbol, symbol_cache)
            
            # 设置数据
            symbol_cache.put(data_type, data, ttl)
    
    def delete(self, symbol: str, data_type: Optional[str] = None) -> bool:
        """
        删除特定资产的缓存数据
        
        参数:
            symbol: 资产代码
            data_type: 数据类型，如果为None则删除该资产的所有缓存
            
        返回:
            是否成功删除
        """
        with self.lock:
            # 如果仅删除特定类型数据
            if data_type is not None:
                symbol_cache = self.cache.get(symbol)
                if symbol_cache is None:
                    return False
                    
                return symbol_cache.delete(data_type)
            
            # 删除整个资产缓存
            result = self.cache.delete(symbol)
            if result and symbol in self.symbol_access_count:
                del self.symbol_access_count[symbol]
                
            return result
    
    def exists(self, symbol: str, data_type: Optional[str] = None) -> bool:
        """
        检查特定资产的缓存是否存在
        
        参数:
            symbol: 资产代码
            data_type: 数据类型，如果为None则检查该资产是否有任何缓存
            
        返回:
            缓存是否存在
        """
        # 检查符号缓存是否存在
        symbol_cache = self.cache.get(symbol)
        if symbol_cache is None:
            return False
            
        # 如果没有指定数据类型，则符号存在即可
        if data_type is None:
            return True
            
        # 检查特定类型数据是否存在
        return symbol_cache.exists(data_type)
    
    def _evict_least_accessed_symbol(self) -> bool:
        """淘汰访问频率最低的资产"""
        if not self.symbol_access_count:
            return False
            
        # 找出访问频率最低的符号
        min_symbol = min(self.symbol_access_count.items(), key=lambda x: x[1])[0]
        
        # 删除该符号
        self.delete(min_symbol)
        return True
    
    def clear(self) -> None:
        """清空所有缓存"""
        with self.lock:
            self.cache.clear()
            self.symbol_access_count.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        返回:
            统计信息字典
        """
        with self.lock:
            # 主缓存统计
            main_stats = self.cache.get_stats()
            
            # 汇总所有符号缓存的统计
            total_data_items = 0
            symbol_stats = {}
            
            for symbol in self.cache.get_keys():
                symbol_cache = self.cache.get(symbol)
                if symbol_cache:
                    symbol_stats[symbol] = {
                        'size': len(symbol_cache),
                        'capacity': symbol_cache.max_size,
                        'access_count': self.symbol_access_count.get(symbol, 0)
                    }
                    total_data_items += len(symbol_cache)
            
            # 构建最终统计信息
            return {
                'symbols': {
                    'count': len(self.cache),
                    'max': self.max_symbols,
                    'usage_percent': (len(self.cache) / self.max_symbols * 100) if self.max_symbols > 0 else 0
                },
                'data_items': {
                    'total': total_data_items,
                    'avg_per_symbol': total_data_items / len(self.cache) if len(self.cache) > 0 else 0
                },
                'main_cache': main_stats,
                'symbols': symbol_stats
            }
    
    def __len__(self) -> int:
        """返回缓存的资产数量"""
        return len(self.cache)
    
    def __str__(self) -> str:
        """返回缓存的字符串表示"""
        return (
            f"SymbolDataCache[symbols={len(self.cache)}/{self.max_symbols}, "
            f"data_per_symbol={self.max_data_per_symbol}]"
        ) 