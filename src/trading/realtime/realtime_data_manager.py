"""
实时数据处理管理器

统一管理实时数据处理组件，包括数据缓存、数据转换、数据过滤等功能。
提供企业级实时数据处理能力，支持高频交易和实时策略执行。
"""

import time
import threading
import asyncio
import logging
from typing import Dict, Any, Optional, List, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from .data_cache import DataCache, SymbolDataCache
from .data_transformer import KLineGenerator, TechnicalIndicatorCalculator
from .data_filter import DataFilter


class DataType(Enum):
    """数据类型枚举"""
    TICK = "tick"
    TRADE = "trade"
    KLINE = "kline"
    INDICATOR = "indicator"
    ORDER_BOOK = "order_book"
    NEWS = "news"


@dataclass
class RealTimeDataConfig:
    """实时数据处理配置"""
    # 缓存配置
    cache_max_size: int = 100000
    cache_ttl: float = 300.0  # 5分钟
    cache_cleanup_interval: float = 30.0
    
    # 数据处理配置
    enable_kline_generation: bool = True
    enable_indicator_calculation: bool = True
    enable_data_filtering: bool = True
    
    # 性能配置
    max_symbols: int = 1000
    max_concurrent_processing: int = 50
    processing_timeout: float = 5.0
    
    # 监控配置
    enable_performance_monitoring: bool = True
    stats_update_interval: float = 10.0


class RealTimeDataManager:
    """
    实时数据处理管理器
    
    统一管理实时数据处理流程，包括：
    1. 数据接收和缓存
    2. 数据转换和处理
    3. 数据过滤和验证
    4. 技术指标计算
    5. 事件通知和分发
    """
    
    def __init__(self, config: Optional[RealTimeDataConfig] = None):
        """
        初始化实时数据管理器
        
        参数:
            config: 配置对象
        """
        self.config = config or RealTimeDataConfig()
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self._init_components()
        
        # 状态管理
        self.is_running = False
        self.processing_lock = threading.RLock()
        
        # 事件处理
        self.event_handlers: Dict[DataType, List[Callable]] = {
            data_type: [] for data_type in DataType
        }
        
        # 性能统计
        self.stats = {
            'data_received': 0,
            'data_processed': 0,
            'data_filtered': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'processing_errors': 0,
            'avg_processing_time': 0.0,
            'last_update': datetime.now()
        }
        
        # 监控线程
        self.monitor_thread = None
        
    def _init_components(self):
        """初始化各个组件"""
        # 数据缓存
        self.data_cache = SymbolDataCache(
            max_symbols=self.config.max_symbols,
            max_data_per_symbol=self.config.cache_max_size // self.config.max_symbols,
            ttl=self.config.cache_ttl
        )
        
        # 数据转换器
        self.transformers = {}
        if self.config.enable_kline_generation:
            self.transformers['kline'] = KLineGenerator(timeframe='1m')

        if self.config.enable_indicator_calculation:
            # 默认添加一些常用技术指标
            self.transformers['ma5'] = TechnicalIndicatorCalculator('MA', {'period': 5})
            self.transformers['ma20'] = TechnicalIndicatorCalculator('MA', {'period': 20})
            self.transformers['rsi'] = TechnicalIndicatorCalculator('RSI', {'period': 14})
        
        # 数据过滤器
        if self.config.enable_data_filtering:
            self.data_filter = DataFilter('default_filter')
        else:
            self.data_filter = None
    
    def start(self):
        """启动实时数据处理"""
        if self.is_running:
            self.logger.warning("实时数据管理器已在运行")
            return
        
        self.is_running = True
        
        # 启动监控线程
        if self.config.enable_performance_monitoring:
            self.monitor_thread = threading.Thread(target=self._monitor_performance, daemon=True)
            self.monitor_thread.start()
        
        self.logger.info("实时数据管理器已启动")
    
    def stop(self):
        """停止实时数据处理"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 等待监控线程结束
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5.0)
        
        self.logger.info("实时数据管理器已停止")
    
    def process_data(self, symbol: str, data_type: DataType, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理实时数据
        
        参数:
            symbol: 资产代码
            data_type: 数据类型
            data: 原始数据
            
        返回:
            处理后的数据，如果处理失败返回None
        """
        start_time = time.time()
        
        try:
            with self.processing_lock:
                # 更新统计
                self.stats['data_received'] += 1
                
                # 数据验证和过滤
                if self.data_filter and not self.data_filter.should_process(data_type.value, symbol, data):
                    self.stats['data_filtered'] += 1
                    return None
                
                # 缓存原始数据
                self.data_cache.put(symbol, data_type.value, data)
                
                # 数据转换处理
                processed_data = self._transform_data(symbol, data_type, data)
                
                # 触发事件处理器
                self._trigger_event_handlers(data_type, symbol, processed_data or data)
                
                # 更新统计
                self.stats['data_processed'] += 1
                processing_time = time.time() - start_time
                self._update_avg_processing_time(processing_time)
                
                return processed_data
                
        except Exception as e:
            self.stats['processing_errors'] += 1
            self.logger.error(f"处理数据失败 {symbol} {data_type.value}: {e}")
            return None
    
    def _transform_data(self, symbol: str, data_type: DataType, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """数据转换处理"""
        results = {}
        
        # K线生成
        if data_type in [DataType.TICK, DataType.TRADE] and 'kline' in self.transformers:
            kline_result = self.transformers['kline'].transform(data_type.value, symbol, data)
            if kline_result:
                results['kline'] = kline_result
                
                # 基于新生成的K线计算技术指标
                for indicator_name, transformer in self.transformers.items():
                    if indicator_name != 'kline':
                        indicator_result = transformer.transform('kline', symbol, kline_result)
                        if indicator_result:
                            results[indicator_name] = indicator_result
        
        return results if results else None
    
    def _trigger_event_handlers(self, data_type: DataType, symbol: str, data: Dict[str, Any]):
        """触发事件处理器"""
        handlers = self.event_handlers.get(data_type, [])
        for handler in handlers:
            try:
                handler(symbol, data)
            except Exception as e:
                self.logger.error(f"事件处理器执行失败 {data_type.value}: {e}")
    
    def add_event_handler(self, data_type: DataType, handler: Callable[[str, Dict[str, Any]], None]):
        """
        添加事件处理器
        
        参数:
            data_type: 数据类型
            handler: 处理函数，接收(symbol, data)参数
        """
        self.event_handlers[data_type].append(handler)
        self.logger.info(f"已添加 {data_type.value} 事件处理器")
    
    def remove_event_handler(self, data_type: DataType, handler: Callable):
        """移除事件处理器"""
        if handler in self.event_handlers[data_type]:
            self.event_handlers[data_type].remove(handler)
            self.logger.info(f"已移除 {data_type.value} 事件处理器")
    
    def get_cached_data(self, symbol: str, data_type: str, max_count: int = 100) -> List[Dict[str, Any]]:
        """
        获取缓存的数据
        
        参数:
            symbol: 资产代码
            data_type: 数据类型
            max_count: 最大返回数量
            
        返回:
            数据列表
        """
        try:
            cached_data = self.data_cache.get(symbol, data_type)
            if cached_data:
                self.stats['cache_hits'] += 1
                return cached_data if isinstance(cached_data, list) else [cached_data]
            else:
                self.stats['cache_misses'] += 1
                return []
        except Exception as e:
            self.logger.error(f"获取缓存数据失败 {symbol} {data_type}: {e}")
            return []
    
    def get_latest_data(self, symbol: str, data_type: str) -> Optional[Dict[str, Any]]:
        """获取最新数据"""
        data_list = self.get_cached_data(symbol, data_type, max_count=1)
        return data_list[0] if data_list else None
    
    def add_technical_indicator(self, name: str, indicator_type: str, params: Dict[str, Any]):
        """
        添加技术指标计算器
        
        参数:
            name: 指标名称
            indicator_type: 指标类型 (MA, EMA, RSI, MACD, BOLL等)
            params: 指标参数
        """
        self.transformers[name] = TechnicalIndicatorCalculator(indicator_type, params)
        self.logger.info(f"已添加技术指标: {name} ({indicator_type})")
    
    def remove_technical_indicator(self, name: str):
        """移除技术指标计算器"""
        if name in self.transformers and name != 'kline':
            del self.transformers[name]
            self.logger.info(f"已移除技术指标: {name}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        cache_stats = self.data_cache.get_stats()
        
        return {
            **self.stats,
            'cache_stats': cache_stats,
            'active_symbols': len(self.data_cache.cache),
            'transformers_count': len(self.transformers),
            'event_handlers_count': sum(len(handlers) for handlers in self.event_handlers.values()),
            'is_running': self.is_running
        }
    
    def _update_avg_processing_time(self, processing_time: float):
        """更新平均处理时间"""
        current_avg = self.stats['avg_processing_time']
        processed_count = self.stats['data_processed']
        
        if processed_count == 1:
            self.stats['avg_processing_time'] = processing_time
        else:
            # 指数移动平均
            alpha = 0.1
            self.stats['avg_processing_time'] = alpha * processing_time + (1 - alpha) * current_avg
    
    def _monitor_performance(self):
        """性能监控线程"""
        while self.is_running:
            try:
                time.sleep(self.config.stats_update_interval)
                
                # 更新统计时间戳
                self.stats['last_update'] = datetime.now()
                
                # 记录性能日志
                stats = self.get_performance_stats()
                self.logger.debug(f"实时数据处理统计: 接收={stats['data_received']}, "
                                f"处理={stats['data_processed']}, "
                                f"平均耗时={stats['avg_processing_time']:.4f}s")
                
            except Exception as e:
                self.logger.error(f"性能监控异常: {e}")
    
    def cleanup(self):
        """清理资源"""
        self.stop()
        
        # 清理缓存
        if hasattr(self.data_cache, 'cleanup'):
            self.data_cache.cleanup()
        
        # 清理转换器
        self.transformers.clear()
        
        # 清理事件处理器
        for handlers in self.event_handlers.values():
            handlers.clear()
        
        self.logger.info("实时数据管理器资源已清理")


class RealTimeDataStream:
    """
    实时数据流处理器

    支持多种数据源接入：WebSocket、消息队列、HTTP推送等
    """

    def __init__(self, data_manager: RealTimeDataManager):
        """
        初始化数据流处理器

        参数:
            data_manager: 实时数据管理器
        """
        self.data_manager = data_manager
        self.logger = logging.getLogger(__name__)

        # 连接管理
        self.connections: Dict[str, Any] = {}
        self.is_streaming = False

        # 消息队列
        self.message_queue = asyncio.Queue(maxsize=10000)
        self.processing_tasks: List[asyncio.Task] = []

    async def start_websocket_stream(self, url: str, symbols: List[str],
                                   data_types: List[DataType] = None):
        """
        启动WebSocket数据流

        参数:
            url: WebSocket服务器地址
            symbols: 订阅的资产代码列表
            data_types: 订阅的数据类型列表
        """
        if data_types is None:
            data_types = [DataType.TICK, DataType.TRADE]

        try:
            import websockets

            async with websockets.connect(url) as websocket:
                self.connections['websocket'] = websocket
                self.is_streaming = True

                # 发送订阅消息
                subscribe_msg = {
                    'action': 'subscribe',
                    'symbols': symbols,
                    'data_types': [dt.value for dt in data_types]
                }
                await websocket.send(str(subscribe_msg))

                self.logger.info(f"已连接WebSocket: {url}, 订阅 {len(symbols)} 个资产")

                # 启动消息处理任务
                self.processing_tasks.append(
                    asyncio.create_task(self._process_message_queue())
                )

                # 接收消息
                async for message in websocket:
                    if not self.is_streaming:
                        break

                    try:
                        await self.message_queue.put(message)
                    except asyncio.QueueFull:
                        self.logger.warning("消息队列已满，丢弃消息")

        except Exception as e:
            self.logger.error(f"WebSocket连接失败: {e}")
            self.is_streaming = False

    async def _process_message_queue(self):
        """处理消息队列"""
        while self.is_streaming:
            try:
                # 获取消息（超时1秒）
                message = await asyncio.wait_for(
                    self.message_queue.get(),
                    timeout=1.0
                )

                # 解析和处理消息
                await self._process_message(message)

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"处理消息失败: {e}")

    async def _process_message(self, message: str):
        """处理单个消息"""
        try:
            import json
            data = json.loads(message)

            # 提取消息字段
            symbol = data.get('symbol')
            data_type_str = data.get('type', 'tick')
            timestamp = data.get('timestamp', datetime.now().isoformat())

            # 转换数据类型
            try:
                data_type = DataType(data_type_str)
            except ValueError:
                data_type = DataType.TICK

            # 构建标准化数据格式
            processed_data = {
                'symbol': symbol,
                'timestamp': timestamp,
                'price': data.get('price', 0),
                'volume': data.get('volume', 0),
                'bid': data.get('bid'),
                'ask': data.get('ask'),
                'bid_size': data.get('bid_size'),
                'ask_size': data.get('ask_size'),
                'raw_data': data
            }

            # 提交给数据管理器处理
            if symbol:
                self.data_manager.process_data(symbol, data_type, processed_data)

        except Exception as e:
            self.logger.error(f"解析消息失败: {e}")

    def stop_stream(self):
        """停止数据流"""
        self.is_streaming = False

        # 取消处理任务
        for task in self.processing_tasks:
            if not task.done():
                task.cancel()

        # 关闭连接
        if 'websocket' in self.connections:
            # WebSocket会在上下文管理器中自动关闭
            pass

        self.logger.info("数据流已停止")

    def get_stream_stats(self) -> Dict[str, Any]:
        """获取数据流统计"""
        return {
            'is_streaming': self.is_streaming,
            'connections_count': len(self.connections),
            'queue_size': self.message_queue.qsize(),
            'processing_tasks': len(self.processing_tasks),
            'active_tasks': sum(1 for task in self.processing_tasks if not task.done())
        }


class RealTimeDataFactory:
    """实时数据处理工厂类"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, '_initialized'):
            self.data_manager = None
            self.data_stream = None
            self._initialized = True

    def create_data_manager(self, config: Optional[RealTimeDataConfig] = None) -> RealTimeDataManager:
        """创建数据管理器"""
        if self.data_manager is None:
            self.data_manager = RealTimeDataManager(config)
        return self.data_manager

    def create_data_stream(self, data_manager: Optional[RealTimeDataManager] = None) -> RealTimeDataStream:
        """创建数据流处理器"""
        if data_manager is None:
            data_manager = self.create_data_manager()

        if self.data_stream is None:
            self.data_stream = RealTimeDataStream(data_manager)
        return self.data_stream

    def get_data_manager(self) -> Optional[RealTimeDataManager]:
        """获取数据管理器实例"""
        return self.data_manager

    def get_data_stream(self) -> Optional[RealTimeDataStream]:
        """获取数据流处理器实例"""
        return self.data_stream

    def cleanup_all(self):
        """清理所有实例"""
        if self.data_stream:
            self.data_stream.stop_stream()
            self.data_stream = None

        if self.data_manager:
            self.data_manager.cleanup()
            self.data_manager = None


# 便捷函数
def get_realtime_data_manager(config: Optional[RealTimeDataConfig] = None) -> RealTimeDataManager:
    """获取实时数据管理器实例"""
    factory = RealTimeDataFactory()
    return factory.create_data_manager(config)


def get_realtime_data_stream(data_manager: Optional[RealTimeDataManager] = None) -> RealTimeDataStream:
    """获取实时数据流处理器实例"""
    factory = RealTimeDataFactory()
    return factory.create_data_stream(data_manager)
