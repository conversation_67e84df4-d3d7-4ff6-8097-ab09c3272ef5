"""
交易系统模块

提供交易执行、订单管理和交易接口的功能。

该模块包含：
- 交易接口：定义与各种交易平台通信的统一接口
- 订单管理：提供订单生命周期管理的功能
- 交易执行：负责将订单提交到交易平台并追踪其状态
- 回测功能：支持使用历史数据测试交易策略
"""

# 交易接口 - 仅导入核心枚举和异常类
# {{ AURA-X: Add - 增强实时数据处理能力. Approval: 寸止(ID:实时数据处理). }}
# 暂时注释掉不存在的导入，避免模块加载错误
# from trading.interfaces.trading_interface import (
#     TradingInterface, TradingException, OrderExecutionException,
#     ConnectionException, AuthenticationException, RateLimitException,
#     OrderStatus, OrderType, OrderSide, TimeInForce
# )

# 暂时注释掉可能不存在的导入
# from trading.interfaces.mock_trading_interface import MockTradingInterface

# 暂时注释掉可能不存在的订单模型
# from trading.order.order_models import (
#     Order, Execution, MarketOrder, LimitOrder, StopOrder,
#     StopLimitOrder, TrailingStopOrder, create_order
# )

# 暂时注释掉可能不存在的订单管理
# from trading.order.order_manager import (
#     OrderManager, OrderManagerException, OrderValidationException
# )

# 暂时注释掉可能不存在的执行引擎
# from trading.execution.execution_engine import (
#     ExecutionEngine, ExecutionEngineException, OrderSubmissionException
# )

# 实时数据处理模块
# {{ AURA-X: Add - 增强实时数据处理能力. Approval: 寸止(ID:实时数据处理). }}
from .realtime import (
    RealTimeDataManager,
    RealTimeDataStream,
    RealTimeStrategyExecutor,
    RealTimeDataConfig,
    DataType,
    get_realtime_data_manager,
    get_realtime_data_stream
)

# 设置默认导出 - 仅保留已导入的类
__all__ = [
    # 实时数据处理
    "RealTimeDataManager",
    "RealTimeDataStream",
    "RealTimeStrategyExecutor",
    "RealTimeDataConfig",
    "DataType",
    "get_realtime_data_manager",
    "get_realtime_data_stream",

    # 接口和枚举（暂时注释掉）
    # "TradingInterface", "TradingException", "OrderExecutionException",
    # "ConnectionException", "AuthenticationException", "RateLimitException",
    # "OrderStatus", "OrderType", "OrderSide", "TimeInForce",

    # 暂时注释掉不可用的类
    # "MockTradingInterface",
    # "Order", "Execution", "MarketOrder", "LimitOrder", "StopOrder",
    # "StopLimitOrder", "TrailingStopOrder", "create_order",
    # "OrderManager", "OrderManagerException", "OrderValidationException",
    # "ExecutionEngine", "ExecutionEngineException", "OrderSubmissionException"
]