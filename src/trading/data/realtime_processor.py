"""
实时数据处理器

接收、处理和分发实时市场数据，提供数据转换和过滤功能。
"""

import logging
import threading
import time
import queue
from typing import Dict, List, Any, Callable, Optional, Set, Union
import datetime as dt

from trading.data.event_bus import EventBus


class RealTimeDataProcessor:
    """
    实时数据处理器
    
    接收实时市场数据，进行处理和转换，然后通过事件总线分发给订阅者。
    """
    
    def __init__(self, 
                event_bus: EventBus,
                name: str = "RealTimeDataProcessor",
                max_queue_size: int = 10000,
                batch_size: int = 100):
        """
        初始化实时数据处理器
        
        参数:
            event_bus: 事件总线，用于分发处理后的数据
            name: 处理器名称
            max_queue_size: 数据队列最大容量
            batch_size: 每批处理的最大数据项数
        """
        self.event_bus = event_bus
        self.name = name
        self.max_queue_size = max_queue_size
        self.batch_size = batch_size
        
        # 数据队列
        self.data_queue = queue.Queue(maxsize=max_queue_size)
        
        # 数据处理器
        self._data_processors: Dict[str, List[Callable]] = {}
        
        # 运行状态
        self._running = False
        self._processor_thread = None
        self._lock = threading.RLock()
        
        # 统计信息
        self._stats = {
            'received_data': 0,
            'processed_data': 0,
            'dropped_data': 0,
            'start_time': None
        }
        
        # 最新数据缓存
        self._latest_data: Dict[str, Dict[str, Any]] = {}
        
        self.logger = logging.getLogger(f"trading.data.{name}")
        self.logger.info(
            f"初始化实时数据处理器: {name}, "
            f"队列容量: {max_queue_size}, "
            f"批处理大小: {batch_size}"
        )
    
    def start(self) -> None:
        """启动实时数据处理器"""
        with self._lock:
            if self._running:
                self.logger.warning("实时数据处理器已经在运行")
                return
            
            self._running = True
            self._stats['start_time'] = dt.datetime.now()
            
            # 创建并启动处理线程
            self._processor_thread = threading.Thread(
                target=self._processor_loop,
                name=f"{self.name}-Thread",
                daemon=True
            )
            self._processor_thread.start()
            
            self.logger.info(f"启动实时数据处理器: {self.name}")
    
    def stop(self) -> None:
        """停止实时数据处理器"""
        with self._lock:
            if not self._running:
                self.logger.warning("实时数据处理器未运行")
                return
            
            self._running = False
            
            if self._processor_thread:
                self._processor_thread.join(timeout=2.0)
                self._processor_thread = None
            
            self.logger.info(f"停止实时数据处理器: {self.name}")
    
    def is_running(self) -> bool:
        """检查处理器是否正在运行"""
        with self._lock:
            return self._running
    
    def _processor_loop(self) -> None:
        """处理线程主循环"""
        self.logger.info(f"实时数据处理线程启动: {threading.current_thread().name}")
        
        try:
            while self._running:
                # 批量处理数据
                self._process_data_batch()
                
                # 短暂休眠，避免CPU占用过高
                time.sleep(0.001)
        except Exception as e:
            self.logger.error(f"处理线程异常: {e}", exc_info=True)
            with self._lock:
                self._running = False
        finally:
            self.logger.info("处理线程结束")
    
    def _process_data_batch(self) -> None:
        """批量处理数据队列中的数据"""
        processed = 0
        
        while not self.data_queue.empty() and processed < self.batch_size:
            try:
                data_item = self.data_queue.get_nowait()
                self._process_data_item(data_item)
                self.data_queue.task_done()
                processed += 1
                self._stats['processed_data'] += 1
            except queue.Empty:
                break
            except Exception as e:
                self.logger.error(f"处理数据异常: {e}", exc_info=True)
    
    def _process_data_item(self, data_item: Dict[str, Any]) -> None:
        """
        处理单个数据项
        
        参数:
            data_item: 数据项
        """
        data_type = data_item.get('type', '')
        symbol = data_item.get('symbol', '')
        data = data_item.get('data', {})
        
        if not data_type or not symbol:
            self.logger.warning(f"数据项缺少类型或符号: {data_item}")
            return
        
        # 应用数据处理器
        if data_type in self._data_processors:
            for processor in self._data_processors[data_type]:
                try:
                    data = processor(symbol, data)
                    if data is None:
                        # 处理器返回None表示过滤掉该数据
                        return
                except Exception as e:
                    self.logger.error(f"数据处理器异常: {data_type}, {e}", exc_info=True)
        
        # 更新最新数据缓存
        if symbol not in self._latest_data:
            self._latest_data[symbol] = {}
        
        self._latest_data[symbol][data_type] = data
        
        # 通过事件总线发布处理后的数据
        event_type = f"market_data.{data_type}.{symbol}"
        self.event_bus.publish(event_type, {
            'symbol': symbol,
            'data': data,
            'timestamp': dt.datetime.now()
        })
        
        # 同时发布一个通用类型的事件，便于批量订阅
        self.event_bus.publish(f"market_data.{data_type}", {
            'symbol': symbol,
            'data': data,
            'timestamp': dt.datetime.now()
        })
    
    def add_data(self, data_type: str, symbol: str, data: Dict[str, Any]) -> bool:
        """
        添加数据到处理队列
        
        参数:
            data_type: 数据类型，如'tick', 'bar', 'trade'等
            symbol: 资产代码
            data: 数据内容
            
        返回:
            是否成功添加
        """
        if not self._running:
            self.logger.warning("实时数据处理器未运行，无法添加数据")
            return False
        
        data_item = {
            'type': data_type,
            'symbol': symbol,
            'data': data,
            'timestamp': dt.datetime.now()
        }
        
        try:
            self.data_queue.put(data_item, block=False)
            self._stats['received_data'] += 1
            return True
        except queue.Full:
            self._stats['dropped_data'] += 1
            self.logger.warning(f"数据队列已满，丢弃数据: {data_type}.{symbol}")
            return False
    
    def register_processor(self, data_type: str, processor: Callable[[str, Dict[str, Any]], Optional[Dict[str, Any]]]) -> None:
        """
        注册数据处理器
        
        参数:
            data_type: 数据类型
            processor: 处理函数，接收符号和数据参数，返回处理后的数据或None
        """
        with self._lock:
            if data_type not in self._data_processors:
                self._data_processors[data_type] = []
            
            self._data_processors[data_type].append(processor)
            self.logger.info(f"注册数据处理器: {data_type} -> {processor.__qualname__}")
    
    def unregister_processor(self, data_type: str, processor: Callable) -> bool:
        """
        注销数据处理器
        
        参数:
            data_type: 数据类型
            processor: 处理函数
            
        返回:
            是否成功注销
        """
        with self._lock:
            if data_type in self._data_processors and processor in self._data_processors[data_type]:
                self._data_processors[data_type].remove(processor)
                
                # 如果该类型没有处理器了，删除该键
                if not self._data_processors[data_type]:
                    del self._data_processors[data_type]
                
                self.logger.info(f"注销数据处理器: {data_type} -> {processor.__qualname__}")
                return True
            
            self.logger.warning(f"尝试注销不存在的数据处理器: {data_type} -> {processor.__qualname__}")
            return False
    
    def get_latest_data(self, symbol: str, data_type: Optional[str] = None) -> Union[Dict[str, Any], Dict[str, Dict[str, Any]], None]:
        """
        获取最新数据
        
        参数:
            symbol: 资产代码
            data_type: 数据类型，如果为None则返回该符号的所有类型数据
            
        返回:
            最新数据，如果没有则返回None
        """
        if symbol not in self._latest_data:
            return None
        
        if data_type:
            return self._latest_data[symbol].get(data_type)
        
        return self._latest_data[symbol]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取处理器统计信息"""
        with self._lock:
            stats = self._stats.copy()
            
            # 添加运行时间
            if stats['start_time']:
                run_time = dt.datetime.now() - stats['start_time']
                stats['run_time_seconds'] = run_time.total_seconds()
            
            # 添加队列信息
            stats['queue_size'] = self.data_queue.qsize()
            stats['queue_capacity'] = self.max_queue_size
            stats['queue_usage_percent'] = (self.data_queue.qsize() / self.max_queue_size) * 100 if self.max_queue_size > 0 else 0
            
            # 添加处理器信息
            stats['processor_types'] = len(self._data_processors)
            stats['total_processors'] = sum(len(processors) for processors in self._data_processors.values())
            
            # 添加数据信息
            stats['symbol_count'] = len(self._latest_data)
            
            # 添加处理率信息
            if stats.get('run_time_seconds', 0) > 0:
                stats['data_per_second'] = stats['processed_data'] / stats['run_time_seconds']
            
            return stats
    
    def clear_data(self) -> None:
        """清除所有缓存的最新数据"""
        with self._lock:
            self._latest_data.clear()
            self.logger.info("清除所有缓存的最新数据")
    
    def __str__(self) -> str:
        """字符串表示"""
        stats = self.get_stats()
        status = "运行中" if self._running else "已停止"
        
        return (
            f"{self.name} [{status}] [队列: {stats['queue_size']}/{stats['queue_capacity']}, "
            f"符号数: {stats['symbol_count']}, 处理器类型: {stats['processor_types']}, "
            f"已接收: {stats['received_data']}, 已处理: {stats['processed_data']}, "
            f"丢弃: {stats['dropped_data']}]"
        ) 