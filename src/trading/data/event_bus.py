"""
事件总线

提供事件发布和订阅功能，用于系统组件间的解耦通信。
"""

import logging
import threading
import queue
import time
from typing import Dict, List, Any, Callable, Optional, Set, Union
import datetime as dt


class EventBus:
    """
    事件总线
    
    提供发布-订阅模式的事件处理机制，支持异步事件处理。
    """
    
    def __init__(self, 
                name: str = "EventBus",
                max_queue_size: int = 10000,
                worker_count: int = 1,
                batch_size: int = 100):
        """
        初始化事件总线
        
        参数:
            name: 事件总线名称
            max_queue_size: 事件队列最大容量
            worker_count: 工作线程数量
            batch_size: 每批处理的最大事件数
        """
        self.name = name
        self.max_queue_size = max_queue_size
        self.worker_count = max(1, worker_count)
        self.batch_size = batch_size
        
        # 事件队列
        self.event_queue = queue.Queue(maxsize=max_queue_size)
        
        # 订阅者字典，键为事件类型，值为订阅者集合
        self._subscribers: Dict[str, Set[Callable]] = {}
        
        # 通配符订阅者，订阅所有事件
        self._wildcard_subscribers: Set[Callable] = set()
        
        # 工作线程
        self._workers: List[threading.Thread] = []
        
        # 运行状态
        self._running = False
        self._lock = threading.RLock()
        
        # 统计信息
        self._stats = {
            'published_events': 0,
            'delivered_events': 0,
            'dropped_events': 0,
            'start_time': None
        }
        
        self.logger = logging.getLogger(f"trading.data.{name}")
        self.logger.info(
            f"初始化事件总线: {name}, "
            f"队列容量: {max_queue_size}, "
            f"工作线程: {worker_count}, "
            f"批处理大小: {batch_size}"
        )
    
    def start(self) -> None:
        """启动事件总线"""
        with self._lock:
            if self._running:
                self.logger.warning("事件总线已经在运行")
                return
            
            self._running = True
            self._stats['start_time'] = dt.datetime.now()
            
            # 创建并启动工作线程
            for i in range(self.worker_count):
                worker = threading.Thread(
                    target=self._worker_loop,
                    name=f"{self.name}-Worker-{i}",
                    daemon=True
                )
                worker.start()
                self._workers.append(worker)
            
            self.logger.info(f"启动事件总线: {self.name}, 工作线程: {len(self._workers)}")
    
    def stop(self) -> None:
        """停止事件总线"""
        with self._lock:
            if not self._running:
                self.logger.warning("事件总线未运行")
                return
            
            self._running = False
            
            # 等待工作线程结束
            for worker in self._workers:
                worker.join(timeout=2.0)
            
            self._workers.clear()
            self.logger.info(f"停止事件总线: {self.name}")
    
    def is_running(self) -> bool:
        """检查事件总线是否正在运行"""
        with self._lock:
            return self._running
    
    def _worker_loop(self) -> None:
        """工作线程主循环"""
        self.logger.info(f"事件总线工作线程启动: {threading.current_thread().name}")
        
        try:
            while self._running:
                # 批量处理事件
                self._process_events_batch()
                
                # 短暂休眠，避免CPU占用过高
                time.sleep(0.001)
        except Exception as e:
            self.logger.error(f"工作线程异常: {e}", exc_info=True)
        finally:
            self.logger.info(f"工作线程结束: {threading.current_thread().name}")
    
    def _process_events_batch(self) -> None:
        """批量处理事件队列中的事件"""
        processed = 0
        
        while not self.event_queue.empty() and processed < self.batch_size:
            try:
                event = self.event_queue.get_nowait()
                self._dispatch_event(event)
                self.event_queue.task_done()
                processed += 1
            except queue.Empty:
                break
            except Exception as e:
                self.logger.error(f"处理事件异常: {e}", exc_info=True)
    
    def _dispatch_event(self, event: Dict[str, Any]) -> None:
        """
        分发事件到订阅者
        
        参数:
            event: 事件对象
        """
        event_type = event.get('type', '')
        
        # 获取该事件类型的订阅者
        subscribers = self._subscribers.get(event_type, set())
        
        # 调用订阅者处理事件
        for subscriber in list(subscribers) + list(self._wildcard_subscribers):
            try:
                subscriber(event)
                self._stats['delivered_events'] += 1
            except Exception as e:
                self.logger.error(f"订阅者处理事件异常: {e}", exc_info=True)
    
    def publish(self, event_type: str, data: Any = None) -> bool:
        """
        发布事件
        
        参数:
            event_type: 事件类型
            data: 事件数据
            
        返回:
            是否成功发布
        """
        if not event_type:
            self.logger.warning("尝试发布事件类型为空的事件")
            return False
        
        event = {
            'type': event_type,
            'data': data,
            'timestamp': dt.datetime.now()
        }
        
        try:
            self.event_queue.put(event, block=False)
            self._stats['published_events'] += 1
            return True
        except queue.Full:
            self._stats['dropped_events'] += 1
            self.logger.warning(f"事件队列已满，丢弃事件: {event_type}")
            return False
    
    def subscribe(self, event_type: str, handler: Callable[[Dict[str, Any]], None]) -> None:
        """
        订阅特定类型的事件
        
        参数:
            event_type: 事件类型，使用'*'表示订阅所有事件
            handler: 事件处理函数，接收一个事件对象参数
        """
        with self._lock:
            if event_type == '*':
                self._wildcard_subscribers.add(handler)
                self.logger.info(f"添加通配符订阅者: {handler.__qualname__}")
            else:
                if event_type not in self._subscribers:
                    self._subscribers[event_type] = set()
                
                self._subscribers[event_type].add(handler)
                self.logger.info(f"添加订阅者: {event_type} -> {handler.__qualname__}")
    
    def unsubscribe(self, event_type: str, handler: Callable[[Dict[str, Any]], None]) -> bool:
        """
        取消订阅
        
        参数:
            event_type: 事件类型，使用'*'表示取消订阅所有事件
            handler: 事件处理函数
            
        返回:
            是否成功取消订阅
        """
        with self._lock:
            if event_type == '*':
                if handler in self._wildcard_subscribers:
                    self._wildcard_subscribers.remove(handler)
                    self.logger.info(f"移除通配符订阅者: {handler.__qualname__}")
                    return True
            elif event_type in self._subscribers:
                if handler in self._subscribers[event_type]:
                    self._subscribers[event_type].remove(handler)
                    self.logger.info(f"移除订阅者: {event_type} -> {handler.__qualname__}")
                    
                    # 如果该事件类型没有订阅者了，删除该键
                    if not self._subscribers[event_type]:
                        del self._subscribers[event_type]
                    
                    return True
            
            self.logger.warning(f"尝试移除不存在的订阅者: {event_type} -> {handler.__qualname__}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取事件总线统计信息"""
        with self._lock:
            stats = self._stats.copy()
            
            # 添加运行时间
            if stats['start_time']:
                run_time = dt.datetime.now() - stats['start_time']
                stats['run_time_seconds'] = run_time.total_seconds()
            
            # 添加队列信息
            stats['queue_size'] = self.event_queue.qsize()
            stats['queue_capacity'] = self.max_queue_size
            stats['queue_usage_percent'] = (self.event_queue.qsize() / self.max_queue_size) * 100 if self.max_queue_size > 0 else 0
            
            # 添加订阅者信息
            total_subscribers = sum(len(subs) for subs in self._subscribers.values()) + len(self._wildcard_subscribers)
            stats['total_subscribers'] = total_subscribers
            stats['event_types'] = len(self._subscribers)
            stats['wildcard_subscribers'] = len(self._wildcard_subscribers)
            
            # 添加吞吐率信息
            if stats.get('run_time_seconds', 0) > 0:
                stats['events_per_second'] = stats['published_events'] / stats['run_time_seconds']
                stats['delivery_rate'] = (stats['delivered_events'] / stats['published_events']) * 100 if stats['published_events'] > 0 else 0
            
            return stats
    
    def clear_queue(self) -> int:
        """
        清空事件队列
        
        返回:
            清除的事件数量
        """
        cleared = 0
        while not self.event_queue.empty():
            try:
                self.event_queue.get_nowait()
                self.event_queue.task_done()
                cleared += 1
            except queue.Empty:
                break
        
        self.logger.info(f"清空事件队列, 清除事件数: {cleared}")
        return cleared
    
    def __str__(self) -> str:
        """字符串表示"""
        stats = self.get_stats()
        status = "运行中" if self._running else "已停止"
        
        return (
            f"{self.name} [{status}] [队列: {stats['queue_size']}/{stats['queue_capacity']}, "
            f"订阅者: {stats['total_subscribers']}, 事件类型: {stats['event_types']}, "
            f"已发布: {stats['published_events']}, 已投递: {stats['delivered_events']}, "
            f"丢弃: {stats['dropped_events']}]"
        ) 