"""
交易执行引擎模块

提供交易执行引擎，负责将订单提交给交易接口并管理执行状态。
"""

import datetime as dt
import logging
import threading
import time
import uuid
from decimal import Decimal
from typing import Dict, List, Optional, Any, Union, Callable, Set, Tuple

from trading.interfaces.trading_interface import (
    TradingInterface, OrderStatus, OrderType, OrderSide, 
    TimeInForce, TradingException, ConnectionException
)
from trading.order.order_models import (
    Order, Execution, create_order, MarketOrder, LimitOrder, 
    StopOrder, StopLimitOrder, TrailingStopOrder
)
from trading.order.order_manager import OrderManager, OrderManagerException


logger = logging.getLogger(__name__)


class ExecutionEngineException(TradingException):
    """执行引擎异常"""
    pass


class OrderSubmissionException(ExecutionEngineException):
    """订单提交异常"""
    pass


class ExecutionEngine:
    """
    交易执行引擎
    
    负责将订单提交给交易接口并管理执行状态。
    """
    
    def __init__(
        self, 
        trading_interface: TradingInterface,
        order_manager: Optional[OrderManager] = None,
        auto_connect: bool = True,
        keep_alive: bool = True,
        poll_interval: float = 1.0
    ):
        """
        初始化执行引擎
        
        参数:
            trading_interface: 交易接口
            order_manager: 订单管理器，如果为None则创建新的
            auto_connect: 是否自动连接到交易接口
            keep_alive: 是否保持连接
            poll_interval: 轮询间隔（秒）
        """
        self._trading_interface = trading_interface
        self._order_manager = order_manager or OrderManager()
        self._is_running = False
        self._lock = threading.RLock()
        self._poll_thread: Optional[threading.Thread] = None
        self._poll_interval = poll_interval
        self._keep_alive = keep_alive
        self._pending_orders: Set[str] = set()  # 等待提交的订单ID
        self._submitted_orders: Dict[str, str] = {}  # 订单ID -> 交易平台订单ID
        self._platform_to_local: Dict[str, str] = {}  # 交易平台订单ID -> 订单ID
        self._order_update_callbacks: List[Callable[[Order], None]] = []  # 订单更新回调
        
        # 注册订单管理器全局回调
        self._order_manager.add_global_callback(self._handle_order_update)
        
        # 如果需要自动连接
        if auto_connect:
            self.connect()
    
    @property
    def trading_interface(self) -> TradingInterface:
        """获取交易接口"""
        return self._trading_interface
    
    @property
    def order_manager(self) -> OrderManager:
        """获取订单管理器"""
        return self._order_manager
    
    @property
    def is_connected(self) -> bool:
        """是否已连接到交易接口"""
        return self._trading_interface.is_connected
    
    @property
    def is_running(self) -> bool:
        """执行引擎是否正在运行"""
        return self._is_running
    
    def connect(self) -> bool:
        """
        连接到交易接口
        
        返回:
            连接是否成功
        """
        with self._lock:
            if self.is_connected:
                logger.info("已经连接到交易接口")
                return True
            
            try:
                success = self._trading_interface.connect()
                if success:
                    logger.info(f"成功连接到交易接口: {self._trading_interface.name}")
                    
                    # 启动轮询线程
                    if not self._is_running and self._keep_alive:
                        self._start_polling()
                else:
                    logger.error(f"连接交易接口失败: {self._trading_interface.name}")
                
                return success
            except Exception as e:
                logger.exception(f"连接交易接口时发生异常: {e}")
                return False
    
    def disconnect(self) -> bool:
        """
        断开与交易接口的连接
        
        返回:
            断开连接是否成功
        """
        with self._lock:
            if not self.is_connected:
                logger.info("未连接到交易接口")
                return True
            
            # 停止轮询线程
            self._stop_polling()
            
            try:
                success = self._trading_interface.disconnect()
                if success:
                    logger.info(f"成功断开与交易接口的连接: {self._trading_interface.name}")
                else:
                    logger.error(f"断开与交易接口的连接失败: {self._trading_interface.name}")
                
                return success
            except Exception as e:
                logger.exception(f"断开与交易接口的连接时发生异常: {e}")
                return False
    
    def submit_order(
        self,
        order: Order,
        wait_for_status: bool = False,
        timeout: float = 10.0
    ) -> Tuple[bool, Optional[str]]:
        """
        提交订单
        
        参数:
            order: 订单对象
            wait_for_status: 是否等待订单状态更新
            timeout: 等待超时时间（秒）
            
        返回:
            (是否成功, 交易平台订单ID)
            
        抛出:
            OrderSubmissionException: 订单提交失败时
        """
        if not self.is_connected:
            if not self.connect():
                raise OrderSubmissionException("未连接到交易接口")
        
        # 将订单添加到待处理队列
        with self._lock:
            if order.order_id in self._pending_orders:
                raise OrderSubmissionException(f"订单已在等待提交队列中: {order.order_id}")
            
            self._pending_orders.add(order.order_id)
        
        try:
            # 根据订单类型构建参数
            kwargs = {}
            
            if isinstance(order, LimitOrder):
                kwargs["price"] = float(order.price)
            
            if isinstance(order, StopOrder):
                kwargs["stop_price"] = float(order.stop_price)
            
            if isinstance(order, StopLimitOrder):
                kwargs["stop_price"] = float(order.stop_price)
                kwargs["price"] = float(order.limit_price)
            
            if isinstance(order, TrailingStopOrder):
                if order.trail_percent is not None:
                    kwargs["trail_percent"] = float(order.trail_percent)
                else:
                    kwargs["trail_amount"] = float(order.trail_amount)
            
            # 提交订单到交易接口
            platform_order_id = self._trading_interface.place_order(
                symbol=order.symbol,
                order_type=order.order_type,
                side=order.side,
                quantity=float(order.quantity),
                time_in_force=order.time_in_force,
                client_order_id=order.client_order_id,
                **kwargs
            )
            
            if platform_order_id:
                # 更新订单状态并建立ID映射
                with self._lock:
                    self._submitted_orders[order.order_id] = platform_order_id
                    self._platform_to_local[platform_order_id] = order.order_id
                    self._pending_orders.remove(order.order_id)
                
                # 更新订单状态为"等待中"
                self._order_manager.update_order_status(
                    order_id=order.order_id,
                    status=OrderStatus.PENDING,
                    reason=f"订单已提交到交易平台，平台订单ID: {platform_order_id}"
                )
                
                # 如果需要等待状态更新
                if wait_for_status:
                    start_time = time.time()
                    while (time.time() - start_time) < timeout:
                        # 获取最新订单状态
                        updated_order = self._order_manager.get_order(order.order_id)
                        if updated_order and updated_order.status != OrderStatus.PENDING:
                            break
                        time.sleep(0.1)
                
                return True, platform_order_id
            else:
                with self._lock:
                    self._pending_orders.remove(order.order_id)
                
                self._order_manager.reject_order(
                    order_id=order.order_id,
                    reason="交易平台未返回有效的订单ID"
                )
                raise OrderSubmissionException("交易平台未返回有效的订单ID")
            
        except TradingException as e:
            with self._lock:
                if order.order_id in self._pending_orders:
                    self._pending_orders.remove(order.order_id)
            
            self._order_manager.reject_order(
                order_id=order.order_id,
                reason=f"订单提交失败: {str(e)}"
            )
            raise OrderSubmissionException(f"订单提交失败: {str(e)}") from e
            
        except Exception as e:
            with self._lock:
                if order.order_id in self._pending_orders:
                    self._pending_orders.remove(order.order_id)
            
            self._order_manager.reject_order(
                order_id=order.order_id,
                reason=f"订单提交过程中发生异常: {str(e)}"
            )
            raise OrderSubmissionException(f"订单提交过程中发生异常: {str(e)}") from e
    
    def create_and_submit_order(
        self,
        order_type: OrderType,
        symbol: str,
        side: OrderSide,
        quantity: Union[Decimal, float, str],
        price: Optional[Union[Decimal, float, str]] = None,
        stop_price: Optional[Union[Decimal, float, str]] = None,
        limit_price: Optional[Union[Decimal, float, str]] = None,
        trail_amount: Optional[Union[Decimal, float, str]] = None,
        trail_percent: Optional[Union[Decimal, float, str]] = None,
        time_in_force: TimeInForce = TimeInForce.DAY,
        client_order_id: Optional[str] = None,
        wait_for_status: bool = False,
        timeout: float = 10.0,
        **kwargs
    ) -> Tuple[Order, Optional[str]]:
        """
        创建并提交订单
        
        参数:
            order_type: 订单类型
            symbol: 证券代码
            side: 交易方向
            quantity: 数量
            price: 价格（限价单）
            stop_price: 止损价格（止损单、止损限价单）
            limit_price: 限价（止损限价单）
            trail_amount: 追踪金额（追踪止损单）
            trail_percent: 追踪百分比（追踪止损单）
            time_in_force: 订单有效期
            client_order_id: 客户端订单ID
            wait_for_status: 是否等待订单状态更新
            timeout: 等待超时时间（秒）
            **kwargs: 其他参数
            
        返回:
            (订单对象, 交易平台订单ID)
            
        抛出:
            OrderManagerException: 订单创建失败时
            OrderSubmissionException: 订单提交失败时
        """
        # 创建订单
        order = self._order_manager.create_order(
            order_type=order_type,
            symbol=symbol,
            side=side,
            quantity=quantity,
            price=price,
            stop_price=stop_price,
            limit_price=limit_price,
            trail_amount=trail_amount,
            trail_percent=trail_percent,
            time_in_force=time_in_force,
            client_order_id=client_order_id,
            **kwargs
        )
        
        # 提交订单
        success, platform_order_id = self.submit_order(
            order=order,
            wait_for_status=wait_for_status,
            timeout=timeout
        )
        
        return order, platform_order_id if success else None
    
    def cancel_order(
        self, 
        order_id: str,
        wait_for_status: bool = False,
        timeout: float = 10.0
    ) -> bool:
        """
        取消订单
        
        参数:
            order_id: 订单ID
            wait_for_status: 是否等待订单状态更新
            timeout: 等待超时时间（秒）
            
        返回:
            是否成功取消
            
        抛出:
            ExecutionEngineException: 取消订单失败时
        """
        if not self.is_connected:
            if not self.connect():
                raise ExecutionEngineException("未连接到交易接口")
        
        # 获取订单
        order = self._order_manager.get_order(order_id)
        if order is None:
            raise ExecutionEngineException(f"订单不存在: {order_id}")
        
        if not order.is_active:
            logger.warning(f"尝试取消非活跃订单: {order_id}, 状态: {order.status.name}")
            return False
        
        # 获取平台订单ID
        platform_order_id = None
        with self._lock:
            platform_order_id = self._submitted_orders.get(order_id)
        
        if not platform_order_id:
            raise ExecutionEngineException(f"找不到对应的平台订单ID: {order_id}")
        
        try:
            # 调用交易接口取消订单
            success = self._trading_interface.cancel_order(platform_order_id)
            
            if success:
                # 如果需要等待状态更新
                if wait_for_status:
                    start_time = time.time()
                    while (time.time() - start_time) < timeout:
                        # 获取最新订单状态
                        updated_order = self._order_manager.get_order(order_id)
                        if updated_order and updated_order.status == OrderStatus.CANCELLED:
                            break
                        time.sleep(0.1)
                
                return success
            else:
                logger.warning(f"交易平台取消订单失败: {platform_order_id}")
                return False
                
        except TradingException as e:
            raise ExecutionEngineException(f"取消订单失败: {str(e)}") from e
            
        except Exception as e:
            raise ExecutionEngineException(f"取消订单过程中发生异常: {str(e)}") from e
    
    def get_platform_order_id(self, order_id: str) -> Optional[str]:
        """
        获取平台订单ID
        
        参数:
            order_id: 订单ID
            
        返回:
            平台订单ID，如果不存在则返回None
        """
        with self._lock:
            return self._submitted_orders.get(order_id)
    
    def get_local_order_id(self, platform_order_id: str) -> Optional[str]:
        """
        获取本地订单ID
        
        参数:
            platform_order_id: 平台订单ID
            
        返回:
            本地订单ID，如果不存在则返回None
        """
        with self._lock:
            return self._platform_to_local.get(platform_order_id)
    
    def update_orders(self) -> int:
        """
        更新所有活跃订单状态
        
        返回:
            更新的订单数量
        """
        if not self.is_connected:
            try:
                if not self.connect():
                    logger.warning("无法连接到交易接口，订单状态更新失败")
                    return 0
            except Exception as e:
                logger.exception(f"连接交易接口时发生异常: {e}")
                return 0
        
        updated_count = 0
        
        try:
            # 获取所有活跃订单
            active_orders = self._order_manager.get_orders(active_only=True)
            platform_order_ids = []
            
            with self._lock:
                for order in active_orders:
                    platform_id = self._submitted_orders.get(order.order_id)
                    if platform_id:
                        platform_order_ids.append(platform_id)
            
            if not platform_order_ids:
                return 0
            
            # 批量获取订单状态
            for platform_id in platform_order_ids:
                try:
                    # 获取平台订单状态
                    platform_order = self._trading_interface.get_order(platform_id)
                    
                    if not platform_order:
                        continue
                    
                    # 获取本地订单ID
                    local_id = self.get_local_order_id(platform_id)
                    if not local_id:
                        continue
                    
                    # 更新订单状态
                    self._sync_order_with_platform(local_id, platform_order)
                    updated_count += 1
                    
                except Exception as e:
                    logger.exception(f"更新订单状态时发生异常，平台订单ID: {platform_id}, 错误: {e}")
            
            return updated_count
            
        except Exception as e:
            logger.exception(f"批量更新订单状态时发生异常: {e}")
            return 0
    
    def update_order(self, order_id: str) -> bool:
        """
        更新单个订单状态
        
        参数:
            order_id: 订单ID
            
        返回:
            是否成功更新
        """
        if not self.is_connected:
            try:
                if not self.connect():
                    logger.warning("无法连接到交易接口，订单状态更新失败")
                    return False
            except Exception as e:
                logger.exception(f"连接交易接口时发生异常: {e}")
                return False
        
        try:
            # 获取平台订单ID
            platform_id = self.get_platform_order_id(order_id)
            if not platform_id:
                logger.warning(f"找不到对应的平台订单ID: {order_id}")
                return False
            
            # 获取平台订单状态
            platform_order = self._trading_interface.get_order(platform_id)
            if not platform_order:
                logger.warning(f"交易平台未返回订单信息: {platform_id}")
                return False
            
            # 更新订单状态
            self._sync_order_with_platform(order_id, platform_order)
            return True
            
        except Exception as e:
            logger.exception(f"更新订单状态时发生异常，订单ID: {order_id}, 错误: {e}")
            return False
    
    def add_order_update_callback(self, callback: Callable[[Order], None]) -> None:
        """
        添加订单更新回调
        
        参数:
            callback: 回调函数
        """
        self._order_update_callbacks.append(callback)
    
    def remove_order_update_callback(self, callback: Callable[[Order], None]) -> bool:
        """
        移除订单更新回调
        
        参数:
            callback: 回调函数
            
        返回:
            是否成功移除
        """
        try:
            self._order_update_callbacks.remove(callback)
            return True
        except ValueError:
            return False
    
    def _start_polling(self) -> None:
        """启动轮询线程"""
        if self._poll_thread and self._poll_thread.is_alive():
            return
        
        self._is_running = True
        self._poll_thread = threading.Thread(
            target=self._polling_loop,
            name="OrderStatusPolling",
            daemon=True
        )
        self._poll_thread.start()
        logger.info("订单状态轮询线程已启动")
    
    def _stop_polling(self) -> None:
        """停止轮询线程"""
        self._is_running = False
        
        if self._poll_thread and self._poll_thread.is_alive():
            self._poll_thread.join(timeout=5.0)
            logger.info("订单状态轮询线程已停止")
    
    def _polling_loop(self) -> None:
        """轮询循环"""
        while self._is_running:
            try:
                if self.is_connected:
                    self.update_orders()
                else:
                    # 尝试重新连接
                    if self._keep_alive:
                        try:
                            self.connect()
                        except Exception as e:
                            logger.error(f"自动重连失败: {e}")
                
                # 轮询间隔
                time.sleep(self._poll_interval)
                
            except Exception as e:
                logger.exception(f"轮询过程中发生异常: {e}")
                time.sleep(self._poll_interval)
    
    def _sync_order_with_platform(self, order_id: str, platform_order: Dict[str, Any]) -> None:
        """
        使用平台订单信息同步本地订单状态
        
        参数:
            order_id: 本地订单ID
            platform_order: 平台订单信息
        """
        order = self._order_manager.get_order(order_id)
        if not order:
            logger.warning(f"找不到本地订单: {order_id}")
            return
        
        # 解析平台订单状态
        status_mapping = {
            "pending": OrderStatus.PENDING,
            "open": OrderStatus.PENDING,
            "partially_filled": OrderStatus.PARTIAL_FILLED,
            "filled": OrderStatus.FILLED,
            "canceled": OrderStatus.CANCELLED,
            "cancelled": OrderStatus.CANCELLED,
            "rejected": OrderStatus.REJECTED,
            "expired": OrderStatus.EXPIRED,
            "error": OrderStatus.ERROR
        }
        
        platform_status = platform_order.get("status", "").lower()
        new_status = status_mapping.get(platform_status, None)
        
        if new_status is not None and new_status != order.status:
            self._order_manager.update_order_status(
                order_id=order_id,
                status=new_status,
                reason=f"平台订单状态更新: {platform_status}"
            )
        
        # 处理成交信息
        executions = platform_order.get("executions", [])
        if executions:
            for exec_info in executions:
                # 避免重复添加成交记录
                if any(e.execution_id == exec_info.get("execution_id") for e in order.executions):
                    continue
                
                self._order_manager.add_execution(
                    order_id=order_id,
                    execution_id=exec_info.get("execution_id", str(uuid.uuid4())),
                    price=exec_info.get("price", 0),
                    quantity=exec_info.get("quantity", 0),
                    timestamp=exec_info.get("timestamp", dt.datetime.now()),
                    commission=exec_info.get("commission", 0),
                    exchange=exec_info.get("exchange", "")
                )
    
    def _handle_order_update(self, order: Order) -> None:
        """
        处理订单更新
        
        参数:
            order: 更新后的订单
        """
        # 触发订单更新回调
        for callback in self._order_update_callbacks:
            try:
                callback(order)
            except Exception as e:
                logger.error(f"订单更新回调异常: {e}", exc_info=True)
        
        # 如果订单已完成，清理映射
        if order.is_done:
            with self._lock:
                platform_id = self._submitted_orders.get(order.order_id)
                if platform_id:
                    self._platform_to_local.pop(platform_id, None)
                    self._submitted_orders.pop(order.order_id, None) 