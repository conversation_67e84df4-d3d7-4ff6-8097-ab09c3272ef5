"""
事件总线模块

为量化交易系统提供事件驱动架构的核心组件，负责事件的发布和订阅。
该模块实现了一个灵活的事件总线，用于在系统各组件间传递消息和触发操作。
"""

import threading
import queue
import logging
import time
import uuid
from enum import Enum, auto
from typing import Dict, List, Any, Callable, Optional, Set, Union
from abc import ABC, abstractmethod
import datetime as dt

# 设置日志
logger = logging.getLogger(__name__)


class EventType(Enum):
    """事件类型枚举"""
    TICK = auto()           # 市场TICK数据
    BAR = auto()            # K线数据
    SIGNAL = auto()         # 交易信号
    ORDER = auto()          # 订单事件
    TRADE = auto()          # 成交事件
    POSITION = auto()       # 持仓事件
    ACCOUNT = auto()        # 账户事件
    STRATEGY = auto()       # 策略事件
    RISK = auto()           # 风险事件
    MARKET = auto()         # 市场事件
    SYSTEM = auto()         # 系统事件
    CUSTOM = auto()         # 自定义事件


class EventPriority(Enum):
    """事件优先级枚举"""
    LOW = 0
    NORMAL = 1
    HIGH = 2
    URGENT = 3


class Event:
    """事件基类"""
    
    def __init__(
        self,
        event_type: EventType,
        data: Dict[str, Any] = None,
        timestamp: Optional[dt.datetime] = None,
        sender: str = "",
        priority: EventPriority = EventPriority.NORMAL,
        event_id: str = None
    ):
        """
        初始化事件
        
        参数:
            event_type: 事件类型
            data: 事件数据
            timestamp: 事件时间戳
            sender: 事件发送者
            priority: 事件优先级
            event_id: 事件唯一ID，如果不提供则自动生成
        """
        self.event_type = event_type
        self.data = data or {}
        self.timestamp = timestamp or dt.datetime.now()
        self.sender = sender
        self.priority = priority
        self.event_id = event_id or str(uuid.uuid4())
        self.processed = False
        self.processing_time = 0.0
        
    def __str__(self) -> str:
        return f"Event({self.event_id}, {self.event_type.name}, {self.timestamp}, {self.priority.name})"
    
    def to_dict(self) -> Dict[str, Any]:
        """将事件转换为字典"""
        return {
            "event_id": self.event_id,
            "event_type": self.event_type.name,
            "timestamp": self.timestamp.isoformat(),
            "sender": self.sender,
            "priority": self.priority.name,
            "data": self.data,
            "processed": self.processed,
            "processing_time": self.processing_time
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Event':
        """从字典创建事件"""
        event_type = EventType[data["event_type"]]
        priority = EventPriority[data["priority"]]
        timestamp = dt.datetime.fromisoformat(data["timestamp"])
        
        event = cls(
            event_type=event_type,
            data=data["data"],
            timestamp=timestamp,
            sender=data["sender"],
            priority=priority,
            event_id=data["event_id"]
        )
        event.processed = data.get("processed", False)
        event.processing_time = data.get("processing_time", 0.0)
        return event


class MarketEvent(Event):
    """市场数据事件"""
    
    def __init__(
        self,
        symbol: str,
        data: Dict[str, Any],
        sub_type: str = "tick",
        timestamp: Optional[dt.datetime] = None,
        sender: str = "",
        priority: EventPriority = EventPriority.HIGH,
        event_id: str = None
    ):
        """
        初始化市场数据事件
        
        参数:
            symbol: 证券代码
            data: 市场数据
            sub_type: 子类型，如'tick', 'bar', 'quote'等
            timestamp: 事件时间戳
            sender: 事件发送者
            priority: 事件优先级
            event_id: 事件唯一ID
        """
        data = data or {}
        data.update({"symbol": symbol, "sub_type": sub_type})
        super().__init__(
            event_type=EventType.TICK if sub_type == "tick" else EventType.BAR,
            data=data,
            timestamp=timestamp,
            sender=sender,
            priority=priority,
            event_id=event_id
        )
        
    @property
    def symbol(self) -> str:
        """获取证券代码"""
        return self.data["symbol"]
    
    @property
    def sub_type(self) -> str:
        """获取子类型"""
        return self.data["sub_type"]


class SignalEvent(Event):
    """交易信号事件"""
    
    def __init__(
        self,
        symbol: str,
        signal_type: str,
        direction: int,
        strength: float = 1.0,
        price: Optional[float] = None,
        strategy_id: str = "",
        timestamp: Optional[dt.datetime] = None,
        sender: str = "",
        priority: EventPriority = EventPriority.NORMAL,
        event_id: str = None
    ):
        """
        初始化交易信号事件
        
        参数:
            symbol: 证券代码
            signal_type: 信号类型，如'entry', 'exit'等
            direction: 方向，1表示买入，-1表示卖出，0表示平仓
            strength: 信号强度，范围[0.0, 1.0]
            price: 信号价格
            strategy_id: 策略ID
            timestamp: 事件时间戳
            sender: 事件发送者
            priority: 事件优先级
            event_id: 事件唯一ID
        """
        data = {
            "symbol": symbol,
            "signal_type": signal_type,
            "direction": direction,
            "strength": strength,
            "strategy_id": strategy_id
        }
        
        if price is not None:
            data["price"] = price
            
        super().__init__(
            event_type=EventType.SIGNAL,
            data=data,
            timestamp=timestamp,
            sender=sender,
            priority=priority,
            event_id=event_id
        )
    
    @property
    def symbol(self) -> str:
        """获取证券代码"""
        return self.data["symbol"]
    
    @property
    def signal_type(self) -> str:
        """获取信号类型"""
        return self.data["signal_type"]
    
    @property
    def direction(self) -> int:
        """获取信号方向"""
        return self.data["direction"]
    
    @property
    def strength(self) -> float:
        """获取信号强度"""
        return self.data["strength"]
    
    @property
    def price(self) -> Optional[float]:
        """获取信号价格"""
        return self.data.get("price")
    
    @property
    def strategy_id(self) -> str:
        """获取策略ID"""
        return self.data["strategy_id"]


class OrderEvent(Event):
    """订单事件"""
    
    def __init__(
        self,
        order_id: str,
        symbol: str,
        order_type: str,
        direction: str,
        quantity: float,
        price: Optional[float] = None,
        status: str = "created",
        timestamp: Optional[dt.datetime] = None,
        sender: str = "",
        priority: EventPriority = EventPriority.HIGH,
        event_id: str = None
    ):
        """
        初始化订单事件
        
        参数:
            order_id: 订单ID
            symbol: 证券代码
            order_type: 订单类型，如'market', 'limit'等
            direction: 交易方向，'buy'或'sell'
            quantity: 订单数量
            price: 订单价格
            status: 订单状态
            timestamp: 事件时间戳
            sender: 事件发送者
            priority: 事件优先级
            event_id: 事件唯一ID
        """
        data = {
            "order_id": order_id,
            "symbol": symbol,
            "order_type": order_type,
            "direction": direction,
            "quantity": quantity,
            "status": status
        }
        
        if price is not None:
            data["price"] = price
            
        super().__init__(
            event_type=EventType.ORDER,
            data=data,
            timestamp=timestamp,
            sender=sender,
            priority=priority,
            event_id=event_id
        )
    
    @property
    def order_id(self) -> str:
        """获取订单ID"""
        return self.data["order_id"]
    
    @property
    def symbol(self) -> str:
        """获取证券代码"""
        return self.data["symbol"]
    
    @property
    def order_type(self) -> str:
        """获取订单类型"""
        return self.data["order_type"]
    
    @property
    def direction(self) -> str:
        """获取交易方向"""
        return self.data["direction"]
    
    @property
    def quantity(self) -> float:
        """获取订单数量"""
        return self.data["quantity"]
    
    @property
    def price(self) -> Optional[float]:
        """获取订单价格"""
        return self.data.get("price")
    
    @property
    def status(self) -> str:
        """获取订单状态"""
        return self.data["status"]


class TradeEvent(Event):
    """成交事件"""
    
    def __init__(
        self,
        trade_id: str,
        order_id: str,
        symbol: str,
        direction: str,
        quantity: float,
        price: float,
        timestamp: Optional[dt.datetime] = None,
        sender: str = "",
        priority: EventPriority = EventPriority.HIGH,
        event_id: str = None
    ):
        """
        初始化成交事件
        
        参数:
            trade_id: 成交ID
            order_id: 订单ID
            symbol: 证券代码
            direction: 交易方向，'buy'或'sell'
            quantity: 成交数量
            price: 成交价格
            timestamp: 事件时间戳
            sender: 事件发送者
            priority: 事件优先级
            event_id: 事件唯一ID
        """
        data = {
            "trade_id": trade_id,
            "order_id": order_id,
            "symbol": symbol,
            "direction": direction,
            "quantity": quantity,
            "price": price
        }
        
        super().__init__(
            event_type=EventType.TRADE,
            data=data,
            timestamp=timestamp,
            sender=sender,
            priority=priority,
            event_id=event_id
        )
    
    @property
    def trade_id(self) -> str:
        """获取成交ID"""
        return self.data["trade_id"]
    
    @property
    def order_id(self) -> str:
        """获取订单ID"""
        return self.data["order_id"]
    
    @property
    def symbol(self) -> str:
        """获取证券代码"""
        return self.data["symbol"]
    
    @property
    def direction(self) -> str:
        """获取交易方向"""
        return self.data["direction"]
    
    @property
    def quantity(self) -> float:
        """获取成交数量"""
        return self.data["quantity"]
    
    @property
    def price(self) -> float:
        """获取成交价格"""
        return self.data["price"]


class EventHandler(ABC):
    """事件处理器接口"""
    
    def __init__(self, name: str = ""):
        """
        初始化事件处理器
        
        参数:
            name: 处理器名称
        """
        self.name = name or f"handler_{id(self)}"
        self.event_types: Set[EventType] = set()
        self.enabled = True
    
    def subscribe(self, event_type: EventType) -> None:
        """
        订阅事件类型
        
        参数:
            event_type: 事件类型
        """
        self.event_types.add(event_type)
    
    def unsubscribe(self, event_type: EventType) -> None:
        """
        取消订阅事件类型
        
        参数:
            event_type: 事件类型
        """
        self.event_types.discard(event_type)
    
    def is_subscribed(self, event_type: EventType) -> bool:
        """
        检查是否订阅了事件类型
        
        参数:
            event_type: 事件类型
            
        返回:
            是否已订阅
        """
        return not self.event_types or event_type in self.event_types
    
    @abstractmethod
    def handle_event(self, event: Event) -> None:
        """
        处理事件
        
        参数:
            event: 事件对象
        """
        pass
    
    def __str__(self) -> str:
        status = "enabled" if self.enabled else "disabled"
        types = ", ".join(t.name for t in self.event_types) if self.event_types else "all"
        return f"{self.name} ({status}, types: {types})"


class EventBus:
    """事件总线"""
    
    def __init__(
        self,
        name: str = "main_event_bus",
        max_queue_size: int = 10000,
        worker_count: int = 2,
        process_timeout: float = 5.0
    ):
        """
        初始化事件总线
        
        参数:
            name: 事件总线名称
            max_queue_size: 最大队列大小
            worker_count: 工作线程数
            process_timeout: 处理事件超时时间(秒)
        """
        self.name = name
        self.max_queue_size = max_queue_size
        self.worker_count = worker_count
        self.process_timeout = process_timeout
        
        # 事件队列，按优先级分为多个队列
        self.queues = {
            EventPriority.URGENT: queue.PriorityQueue(maxsize=max_queue_size),
            EventPriority.HIGH: queue.PriorityQueue(maxsize=max_queue_size),
            EventPriority.NORMAL: queue.PriorityQueue(maxsize=max_queue_size),
            EventPriority.LOW: queue.PriorityQueue(maxsize=max_queue_size)
        }
        
        # 事件处理器，按事件类型分组
        self.handlers: Dict[EventType, List[EventHandler]] = {
            event_type: [] for event_type in EventType
        }
        
        # 全局处理器，处理所有类型的事件
        self.global_handlers: List[EventHandler] = []
        
        # 事件历史，用于调试和分析
        self.event_history: List[Event] = []
        self.max_history_size = 1000
        
        # 统计信息
        self.stats = {
            'events_published': 0,
            'events_processed': 0,
            'events_dropped': 0,
            'processing_errors': 0,
            'start_time': None,
            'last_event_time': None
        }
        
        # 控制标志
        self.running = False
        self.workers: List[threading.Thread] = []
        self.lock = threading.RLock()
        
    def register_handler(self, handler: EventHandler, event_types: List[EventType] = None) -> None:
        """
        注册事件处理器
        
        参数:
            handler: 事件处理器
            event_types: 订阅的事件类型列表，如果为None则视为全局处理器
        """
        with self.lock:
            if event_types:
                for event_type in event_types:
                    handler.subscribe(event_type)
                    self.handlers[event_type].append(handler)
                    logger.info(f"注册处理器 {handler.name} 订阅事件类型 {event_type.name}")
            else:
                self.global_handlers.append(handler)
                logger.info(f"注册全局处理器 {handler.name}")
    
    def unregister_handler(self, handler: EventHandler) -> None:
        """
        取消注册事件处理器
        
        参数:
            handler: 事件处理器
        """
        with self.lock:
            # 从事件类型特定处理器中移除
            for event_type in EventType:
                if handler in self.handlers[event_type]:
                    self.handlers[event_type].remove(handler)
                    logger.info(f"取消注册处理器 {handler.name} 订阅事件类型 {event_type.name}")
            
            # 从全局处理器中移除
            if handler in self.global_handlers:
                self.global_handlers.remove(handler)
                logger.info(f"取消注册全局处理器 {handler.name}")
    
    def publish(self, event: Event) -> bool:
        """
        发布事件
        
        参数:
            event: 事件对象
            
        返回:
            是否成功发布
        """
        if not self.running:
            logger.warning("事件总线未运行，无法发布事件")
            return False
            
        try:
            # 将事件加入对应优先级的队列
            # 使用(优先级取反, 时间戳)作为排序键，确保高优先级和早到达的事件优先处理
            priority_value = -event.priority.value  # 取反，使得高优先级的值更小
            timestamp_value = time.time()  # 使用当前时间作为次要排序键
            
            self.queues[event.priority].put((priority_value, timestamp_value, event), block=False)
            self.stats['events_published'] += 1
            self.stats['last_event_time'] = dt.datetime.now()
            
            # 保存事件历史
            self._add_to_history(event)
            
            return True
        except queue.Full:
            logger.warning(f"事件队列 {event.priority.name} 已满，丢弃事件")
            self.stats['events_dropped'] += 1
            return False
    
    def start(self) -> bool:
        """启动事件总线"""
        with self.lock:
            if self.running:
                logger.warning("事件总线已经在运行")
                return False
                
            self.running = True
            self.stats['start_time'] = dt.datetime.now()
            
            # 创建工作线程
            for i in range(self.worker_count):
                worker = threading.Thread(
                    target=self._process_events,
                    name=f"{self.name}_worker_{i}",
                    daemon=True
                )
                self.workers.append(worker)
                worker.start()
                
            logger.info(f"启动事件总线: {self.name}, 工作线程数: {self.worker_count}")
            return True
    
    def stop(self) -> bool:
        """停止事件总线"""
        with self.lock:
            if not self.running:
                logger.warning("事件总线未运行")
                return False
                
            self.running = False
            
            # 等待所有工作线程完成
            for worker in self.workers:
                if worker.is_alive():
                    worker.join(timeout=5.0)
                    
            self.workers.clear()
            
            logger.info(f"停止事件总线: {self.name}")
            return True
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            stats = self.stats.copy()
            
            # 添加队列大小信息
            stats['queue_sizes'] = {
                priority.name: q.qsize() for priority, q in self.queues.items()
            }
            
            # 添加处理器信息
            handler_count = sum(len(handlers) for handlers in self.handlers.values())
            stats['handler_count'] = handler_count + len(self.global_handlers)
            stats['global_handler_count'] = len(self.global_handlers)
            
            # 添加事件历史信息
            stats['history_size'] = len(self.event_history)
            
            # 计算事件处理速率
            if stats['start_time']:
                uptime = dt.datetime.now() - stats['start_time']
                stats['uptime_seconds'] = uptime.total_seconds()
                
                if uptime.total_seconds() > 0:
                    eps = stats['events_processed'] / uptime.total_seconds()
                    stats['events_per_second'] = round(eps, 2)
            
            return stats
    
    def clear_history(self) -> None:
        """清除事件历史"""
        with self.lock:
            self.event_history.clear()
            logger.info("清除事件历史")
    
    def get_history(self, limit: int = 100, event_type: Optional[EventType] = None) -> List[Event]:
        """
        获取事件历史
        
        参数:
            limit: 返回的最大事件数
            event_type: 筛选的事件类型
            
        返回:
            事件列表
        """
        with self.lock:
            if event_type:
                events = [e for e in self.event_history if e.event_type == event_type]
            else:
                events = self.event_history.copy()
                
            return events[-limit:] if limit > 0 else events
    
    def _process_events(self) -> None:
        """工作线程函数，处理队列中的事件"""
        logger.info(f"工作线程启动: {threading.current_thread().name}")
        
        while self.running:
            # 按优先级顺序检查队列
            processed = False
            for priority in [EventPriority.URGENT, EventPriority.HIGH, 
                           EventPriority.NORMAL, EventPriority.LOW]:
                try:
                    # 非阻塞方式检查队列
                    if not self.queues[priority].empty():
                        # 获取事件
                        _, _, event = self.queues[priority].get(block=False)
                        
                        # 处理事件
                        self._process_single_event(event)
                        
                        # 标记任务完成
                        self.queues[priority].task_done()
                        
                        # 标记已处理事件
                        processed = True
                        break
                except queue.Empty:
                    continue
                except Exception as e:
                    logger.error(f"处理事件队列时出错: {e}", exc_info=True)
                    self.stats['processing_errors'] += 1
            
            # 如果没有处理任何事件，则短暂休眠
            if not processed:
                time.sleep(0.001)  # 1毫秒
                
        logger.info(f"工作线程停止: {threading.current_thread().name}")
    
    def _process_single_event(self, event: Event) -> None:
        """处理单个事件"""
        start_time = time.time()
        
        try:
            # 首先调用特定事件类型的处理器
            handlers = self.handlers[event.event_type].copy()
            for handler in handlers:
                if handler.enabled and handler.is_subscribed(event.event_type):
                    try:
                        handler.handle_event(event)
                    except Exception as e:
                        logger.error(f"处理器 {handler.name} 处理事件时出错: {e}", exc_info=True)
                        self.stats['processing_errors'] += 1
            
            # 然后调用全局处理器
            global_handlers = self.global_handlers.copy()
            for handler in global_handlers:
                if handler.enabled:
                    try:
                        handler.handle_event(event)
                    except Exception as e:
                        logger.error(f"全局处理器 {handler.name} 处理事件时出错: {e}", exc_info=True)
                        self.stats['processing_errors'] += 1
            
            # 更新事件处理时间
            end_time = time.time()
            event.processing_time = end_time - start_time
            event.processed = True
            
            # 更新统计信息
            self.stats['events_processed'] += 1
            
        except Exception as e:
            logger.error(f"处理事件时出错: {e}", exc_info=True)
            self.stats['processing_errors'] += 1
            
            # 更新事件处理时间，即使出错
            end_time = time.time()
            event.processing_time = end_time - start_time
    
    def _add_to_history(self, event: Event) -> None:
        """添加事件到历史记录"""
        with self.lock:
            self.event_history.append(event)
            
            # 限制历史记录大小
            if len(self.event_history) > self.max_history_size:
                self.event_history = self.event_history[-self.max_history_size:] 