"""
交易接口模块

定义交易系统的核心接口和异常类。
该模块提供了交易执行、订单管理和交易状态跟踪的标准接口。
"""

from abc import ABC, abstractmethod
from enum import Enum, auto
from typing import Dict, List, Optional, Union, Any, Tuple
import datetime as dt
import pandas as pd


class TradingException(Exception):
    """交易系统异常基类"""
    pass


class OrderExecutionException(TradingException):
    """订单执行异常"""
    pass


class ConnectionException(TradingException):
    """连接异常"""
    pass


class AuthenticationException(TradingException):
    """认证异常"""
    pass


class RateLimitException(TradingException):
    """速率限制异常"""
    pass


class OrderStatus(Enum):
    """订单状态枚举"""
    CREATED = auto()         # 已创建
    PENDING = auto()         # 等待中
    PARTIAL_FILLED = auto()  # 部分成交
    FILLED = auto()          # 全部成交
    CANCELLED = auto()       # 已取消
    REJECTED = auto()        # 已拒绝
    EXPIRED = auto()         # 已过期
    ERROR = auto()           # 错误


class OrderType(Enum):
    """订单类型枚举"""
    MARKET = auto()           # 市价单
    LIMIT = auto()            # 限价单
    STOP = auto()             # 止损单
    STOP_LIMIT = auto()       # 止损限价单
    TRAILING_STOP = auto()    # 追踪止损单
    IOC = auto()              # 立即成交否则取消 (Immediate or Cancel)
    FOK = auto()              # 全部成交否则取消 (Fill or Kill)
    MOC = auto()              # 收盘市价单 (Market on Close)
    LOC = auto()              # 收盘限价单 (Limit on Close)


class OrderSide(Enum):
    """交易方向枚举"""
    BUY = auto()              # 买入
    SELL = auto()             # 卖出
    SELL_SHORT = auto()       # 卖空
    BUY_TO_COVER = auto()     # 买入平仓


class TimeInForce(Enum):
    """订单有效期类型枚举"""
    DAY = auto()              # 当日有效
    GTC = auto()              # 撤销前有效 (Good Till Cancelled)
    IOC = auto()              # 立即成交否则取消 (Immediate or Cancel)
    FOK = auto()              # 全部成交否则取消 (Fill or Kill)
    GTD = auto()              # 指定日期前有效 (Good Till Date)


class TradingInterface(ABC):
    """
    交易接口抽象基类
    
    定义了交易系统的标准接口，包括订单管理、执行和查询功能。
    所有特定交易平台的实现都应继承此类。
    """
    
    def __init__(
        self,
        name: str = "",
        description: str = "",
        **kwargs
    ):
        """
        初始化交易接口
        
        参数:
            name: 交易接口名称
            description: 交易接口描述
            **kwargs: 其他参数
        """
        self._name = name
        self._description = description
        self._is_connected = False
        self._account_info = {}
        self._config = kwargs.get("config", {})
        
    @property
    def name(self) -> str:
        """获取交易接口名称"""
        return self._name
    
    @property
    def description(self) -> str:
        """获取交易接口描述"""
        return self._description
    
    @property
    def is_connected(self) -> bool:
        """获取连接状态"""
        return self._is_connected
    
    @property
    def account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        return self._account_info.copy()
    
    @abstractmethod
    def connect(self) -> bool:
        """
        连接到交易平台
        
        返回:
            连接是否成功
        """
        pass
    
    @abstractmethod
    def disconnect(self) -> bool:
        """
        断开与交易平台的连接
        
        返回:
            断开连接是否成功
        """
        pass
    
    @abstractmethod
    def is_market_open(self, market: str = "CN") -> bool:
        """
        检查市场是否开市
        
        参数:
            market: 市场代码
            
        返回:
            市场是否开市
        """
        pass
    
    @abstractmethod
    def get_account_balance(self) -> Dict[str, float]:
        """
        获取账户资金余额
        
        返回:
            账户资金余额字典，键为币种，值为金额
        """
        pass
    
    @abstractmethod
    def get_positions(self) -> pd.DataFrame:
        """
        获取当前持仓
        
        返回:
            持仓信息DataFrame，包含证券代码、数量、成本价等信息
        """
        pass
    
    @abstractmethod
    def place_order(
        self,
        symbol: str,
        order_type: OrderType,
        side: OrderSide,
        quantity: float,
        price: Optional[float] = None,
        time_in_force: TimeInForce = TimeInForce.DAY,
        stop_price: Optional[float] = None,
        client_order_id: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        下单
        
        参数:
            symbol: 证券代码
            order_type: 订单类型
            side: 交易方向
            quantity: 数量
            price: 价格（对于限价单）
            time_in_force: 订单有效期
            stop_price: 止损价（对于止损单）
            client_order_id: 客户端订单ID
            **kwargs: 其他参数
            
        返回:
            订单ID
        """
        pass
    
    @abstractmethod
    def cancel_order(self, order_id: str) -> bool:
        """
        取消订单
        
        参数:
            order_id: 订单ID
            
        返回:
            是否成功取消
        """
        pass
    
    @abstractmethod
    def get_order(self, order_id: str) -> Dict[str, Any]:
        """
        查询订单状态
        
        参数:
            order_id: 订单ID
            
        返回:
            订单信息字典
        """
        pass
    
    @abstractmethod
    def get_orders(
        self,
        status: Optional[OrderStatus] = None,
        symbol: Optional[str] = None,
        start_time: Optional[dt.datetime] = None,
        end_time: Optional[dt.datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        查询多个订单
        
        参数:
            status: 订单状态过滤
            symbol: 证券代码过滤
            start_time: 开始时间
            end_time: 结束时间
            
        返回:
            订单信息列表
        """
        pass
    
    @abstractmethod
    def get_executions(
        self,
        order_id: Optional[str] = None,
        symbol: Optional[str] = None,
        start_time: Optional[dt.datetime] = None,
        end_time: Optional[dt.datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        查询成交记录
        
        参数:
            order_id: 订单ID过滤
            symbol: 证券代码过滤
            start_time: 开始时间
            end_time: 结束时间
            
        返回:
            成交记录列表
        """
        pass
    
    @abstractmethod
    def get_market_data(
        self,
        symbols: List[str],
        data_type: str = "quote",
        **kwargs
    ) -> Dict[str, Any]:
        """
        获取市场数据
        
        参数:
            symbols: 证券代码列表
            data_type: 数据类型，如 'quote', 'trade', 'bar'
            **kwargs: 其他参数
            
        返回:
            市场数据字典
        """
        pass
    
    def validate_order(
        self,
        symbol: str,
        order_type: OrderType,
        side: OrderSide,
        quantity: float,
        price: Optional[float] = None,
        **kwargs
    ) -> Tuple[bool, str]:
        """
        验证订单参数
        
        参数:
            symbol: 证券代码
            order_type: 订单类型
            side: 交易方向
            quantity: 数量
            price: 价格
            **kwargs: 其他参数
            
        返回:
            (是否有效, 错误信息)
        """
        # 基本验证逻辑，子类可扩展
        if not symbol:
            return False, "证券代码不能为空"
        
        if quantity <= 0:
            return False, "数量必须大于0"
        
        if order_type in [OrderType.LIMIT, OrderType.STOP_LIMIT] and (price is None or price <= 0):
            return False, "限价单必须指定有效价格"
            
        return True, ""
    
    def __str__(self) -> str:
        """返回交易接口描述"""
        status = "已连接" if self._is_connected else "未连接"
        return f"{self._name} - {self._description} [状态: {status}]"
    
    def __repr__(self) -> str:
        """返回交易接口表示"""
        return f"{self.__class__.__name__}(name='{self._name}', connected={self._is_connected})" 