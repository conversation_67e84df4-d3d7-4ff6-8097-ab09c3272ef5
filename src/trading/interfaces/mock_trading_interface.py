"""
模拟交易接口模块

提供一个模拟交易环境，用于测试交易策略和系统功能，无需连接实际交易平台。
该接口模拟市场行为，提供虚拟账户、订单簿和执行环境。
"""

import datetime as dt
import logging
import random
import uuid
from decimal import Decimal
from typing import Dict, List, Optional, Union, Any, Tuple
import threading
import time
import pandas as pd

from trading.interfaces.trading_interface import (
    TradingInterface, OrderStatus, OrderType, OrderSide, TimeInForce,
    OrderExecutionException, ConnectionException
)


class MockTradingInterface(TradingInterface):
    """
    模拟交易接口
    
    模拟真实交易环境，包括市场数据、订单处理和账户管理。
    用于开发和测试交易策略，无需连接实际交易平台。
    """
    
    def __init__(
        self,
        name: str = "模拟交易接口",
        description: str = "用于测试的模拟交易环境",
        initial_balance: Dict[str, float] = None,
        market_data: pd.DataFrame = None,
        simulation_delay: float = 0.1,
        fill_probability: float = 0.9,
        slippage_std: float = 0.001,
        market_hours: Tuple[Tuple[int, int], Tuple[int, int]] = ((9, 30), (15, 0)),
        trade_day_of_week: List[int] = None,  # 0=周一, 1=周二, ... 6=周日
        **kwargs
    ):
        """
        初始化模拟交易接口
        
        参数:
            name: 接口名称
            description: 接口描述
            initial_balance: 初始账户余额，格式为 {'CNY': 100000.0, 'USD': 10000.0}
            market_data: 市场数据DataFrame
            simulation_delay: 模拟处理延迟（秒）
            fill_probability: 订单成交概率（0-1）
            slippage_std: 价格滑点标准差
            market_hours: 市场交易时间，格式为 ((开始小时, 开始分钟), (结束小时, 结束分钟))
            trade_day_of_week: 交易日列表，默认为周一至周五
            **kwargs: 其他参数
        """
        super().__init__(name=name, description=description, **kwargs)
        
        # 设置初始账户余额
        self._initial_balance = initial_balance or {'CNY': 1000000.0, 'USD': 0.0}
        self._account_balance = self._initial_balance.copy()
        
        # 市场数据和设置
        self._market_data = market_data
        self._current_prices = {}
        self._simulation_delay = simulation_delay
        self._fill_probability = min(max(fill_probability, 0.0), 1.0)
        self._slippage_std = max(slippage_std, 0.0)
        
        # 市场时间设置
        self._market_hours = market_hours
        self._trade_day_of_week = trade_day_of_week or [0, 1, 2, 3, 4]  # 默认周一至周五
        
        # 订单和持仓管理
        self._orders = {}  # 订单字典，键为订单ID
        self._positions = {}  # 持仓字典，键为证券代码
        self._executions = []  # 成交记录列表
        self._order_id_mapping = {}  # 客户端订单ID到系统订单ID的映射
        
        # 模拟交易线程
        self._processing_thread = None
        self._stop_event = threading.Event()
        
        # 日志设置
        self._logger = logging.getLogger(__name__)
        
        # 更新账户信息
        self._update_account_info()
    
    def connect(self) -> bool:
        """
        连接到模拟交易系统
        
        返回:
            连接是否成功
        """
        if self._is_connected:
            self._logger.info("模拟交易接口已经连接")
            return True
        
        try:
            self._logger.info("正在连接到模拟交易系统...")
            time.sleep(0.5)  # 模拟连接延迟
            self._is_connected = True
            self._start_processing_thread()
            self._logger.info("已成功连接到模拟交易系统")
            return True
        except Exception as e:
            self._logger.error(f"连接模拟交易系统失败: {str(e)}")
            return False
    
    def disconnect(self) -> bool:
        """
        断开与模拟交易系统的连接
        
        返回:
            断开连接是否成功
        """
        if not self._is_connected:
            self._logger.info("模拟交易接口已经断开连接")
            return True
        
        try:
            self._logger.info("正在断开与模拟交易系统的连接...")
            self._stop_processing_thread()
            self._is_connected = False
            self._logger.info("已成功断开与模拟交易系统的连接")
            return True
        except Exception as e:
            self._logger.error(f"断开连接失败: {str(e)}")
            return False
    
    def is_market_open(self, market: str = "CN") -> bool:
        """
        检查市场是否开市
        
        参数:
            market: 市场代码
            
        返回:
            市场是否开市
        """
        now = dt.datetime.now()
        current_day = now.weekday()
        
        # 检查是否为交易日
        if current_day not in self._trade_day_of_week:
            return False
        
        # 检查当前时间是否在交易时间内
        current_time = now.time()
        start_hour, start_min = self._market_hours[0]
        end_hour, end_min = self._market_hours[1]
        
        market_open = dt.time(start_hour, start_min)
        market_close = dt.time(end_hour, end_min)
        
        return market_open <= current_time <= market_close
    
    def get_account_balance(self) -> Dict[str, float]:
        """
        获取账户资金余额
        
        返回:
            账户资金余额字典，键为币种，值为金额
        """
        return self._account_balance.copy()
    
    def get_positions(self) -> pd.DataFrame:
        """
        获取当前持仓
        
        返回:
            持仓信息DataFrame，包含证券代码、数量、成本价等信息
        """
        positions_data = []
        
        for symbol, position in self._positions.items():
            current_price = self._get_current_price(symbol)
            market_value = position['quantity'] * current_price
            unrealized_pnl = market_value - position['cost_basis']
            
            positions_data.append({
                'symbol': symbol,
                'quantity': position['quantity'],
                'avg_price': position['avg_price'],
                'cost_basis': position['cost_basis'],
                'current_price': current_price,
                'market_value': market_value,
                'unrealized_pnl': unrealized_pnl
            })
        
        if not positions_data:
            return pd.DataFrame(columns=[
                'symbol', 'quantity', 'avg_price', 'cost_basis',
                'current_price', 'market_value', 'unrealized_pnl'
            ])
        
        return pd.DataFrame(positions_data)
    
    def place_order(
        self,
        symbol: str,
        order_type: OrderType,
        side: OrderSide,
        quantity: float,
        price: Optional[float] = None,
        time_in_force: TimeInForce = TimeInForce.DAY,
        stop_price: Optional[float] = None,
        client_order_id: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        下单
        
        参数:
            symbol: 证券代码
            order_type: 订单类型
            side: 交易方向
            quantity: 数量
            price: 价格（对于限价单）
            time_in_force: 订单有效期
            stop_price: 止损价（对于止损单）
            client_order_id: 客户端订单ID
            **kwargs: 其他参数
            
        返回:
            订单ID
        """
        if not self._is_connected:
            raise ConnectionException("未连接到交易系统，无法下单")
        
        # 验证订单参数
        is_valid, error_msg = self.validate_order(
            symbol, order_type, side, quantity, price, **kwargs
        )
        
        if not is_valid:
            raise OrderExecutionException(f"订单验证失败: {error_msg}")
        
        # 对于市价单，使用当前价格
        if order_type == OrderType.MARKET:
            price = self._get_current_price(symbol)
        elif order_type in [OrderType.LIMIT, OrderType.STOP_LIMIT] and price is None:
            raise OrderExecutionException("限价单必须指定价格")
            
        # 生成订单ID
        order_id = str(uuid.uuid4())
        
        # 存储客户端订单ID映射
        if client_order_id:
            self._order_id_mapping[client_order_id] = order_id
        
        # 创建订单对象
        order = {
            'order_id': order_id,
            'client_order_id': client_order_id,
            'symbol': symbol,
            'order_type': order_type,
            'side': side,
            'quantity': quantity,
            'price': price,
            'stop_price': stop_price,
            'time_in_force': time_in_force,
            'status': OrderStatus.PENDING,
            'created_time': dt.datetime.now(),
            'updated_time': dt.datetime.now(),
            'filled_quantity': 0.0,
            'filled_price': None,
            'filled_time': None,
            'executions': []
        }
        
        # 存储订单
        self._orders[order_id] = order
        
        self._logger.info(f"已提交订单 {order_id}: {side.name} {quantity} {symbol} @ {price}")
        
        # 模拟处理延迟
        time.sleep(self._simulation_delay)
        
        return order_id
    
    def cancel_order(self, order_id: str) -> bool:
        """
        取消订单
        
        参数:
            order_id: 订单ID
            
        返回:
            是否成功取消
        """
        if not self._is_connected:
            raise ConnectionException("未连接到交易系统，无法取消订单")
        
        if order_id not in self._orders:
            self._logger.warning(f"订单 {order_id} 不存在")
            return False
        
        order = self._orders[order_id]
        
        # 检查订单是否可以取消
        if order['status'] in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED, OrderStatus.EXPIRED]:
            self._logger.warning(f"订单 {order_id} 无法取消，当前状态为 {order['status'].name}")
            return False
        
        # 模拟处理延迟
        time.sleep(self._simulation_delay)
        
        # 随机模拟取消失败的情况
        if random.random() < 0.05:  # 5%的概率取消失败
            self._logger.warning(f"订单 {order_id} 取消失败")
            return False
        
        # 更新订单状态
        order['status'] = OrderStatus.CANCELLED
        order['updated_time'] = dt.datetime.now()
        
        self._logger.info(f"已取消订单 {order_id}")
        
        return True
    
    def get_order(self, order_id: str) -> Dict[str, Any]:
        """
        查询订单状态
        
        参数:
            order_id: 订单ID
            
        返回:
            订单信息字典
        """
        if not self._is_connected:
            raise ConnectionException("未连接到交易系统，无法查询订单")
        
        # 检查是否使用客户端订单ID
        if order_id in self._order_id_mapping:
            order_id = self._order_id_mapping[order_id]
        
        if order_id not in self._orders:
            raise OrderExecutionException(f"订单 {order_id} 不存在")
        
        # 返回订单副本
        return self._orders[order_id].copy()
    
    def get_orders(
        self,
        status: Optional[OrderStatus] = None,
        symbol: Optional[str] = None,
        start_time: Optional[dt.datetime] = None,
        end_time: Optional[dt.datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        查询多个订单
        
        参数:
            status: 订单状态过滤
            symbol: 证券代码过滤
            start_time: 开始时间
            end_time: 结束时间
            
        返回:
            订单信息列表
        """
        if not self._is_connected:
            raise ConnectionException("未连接到交易系统，无法查询订单")
        
        filtered_orders = []
        
        for order in self._orders.values():
            # 应用过滤条件
            if status is not None and order['status'] != status:
                continue
                
            if symbol is not None and order['symbol'] != symbol:
                continue
                
            if start_time is not None and order['created_time'] < start_time:
                continue
                
            if end_time is not None and order['created_time'] > end_time:
                continue
                
            filtered_orders.append(order.copy())
            
        return filtered_orders
    
    def get_executions(
        self,
        order_id: Optional[str] = None,
        symbol: Optional[str] = None,
        start_time: Optional[dt.datetime] = None,
        end_time: Optional[dt.datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        查询成交记录
        
        参数:
            order_id: 订单ID过滤
            symbol: 证券代码过滤
            start_time: 开始时间
            end_time: 结束时间
            
        返回:
            成交记录列表
        """
        if not self._is_connected:
            raise ConnectionException("未连接到交易系统，无法查询成交记录")
        
        filtered_executions = []
        
        for execution in self._executions:
            # 应用过滤条件
            if order_id is not None and execution['order_id'] != order_id:
                continue
                
            if symbol is not None and execution['symbol'] != symbol:
                continue
                
            if start_time is not None and execution['time'] < start_time:
                continue
                
            if end_time is not None and execution['time'] > end_time:
                continue
                
            filtered_executions.append(execution.copy())
            
        return filtered_executions
    
    def get_market_data(
        self,
        symbols: List[str],
        data_type: str = "quote",
        **kwargs
    ) -> Dict[str, Any]:
        """
        获取市场数据
        
        参数:
            symbols: 证券代码列表
            data_type: 数据类型，如 'quote', 'trade', 'bar'
            **kwargs: 其他参数
            
        返回:
            市场数据字典
        """
        if not self._is_connected:
            raise ConnectionException("未连接到交易系统，无法获取市场数据")
        
        result = {}
        
        for symbol in symbols:
            price = self._get_current_price(symbol)
            
            if data_type == "quote":
                # 生成随机的买卖盘报价
                bid_price = price * (1 - random.uniform(0, 0.001))
                ask_price = price * (1 + random.uniform(0, 0.001))
                bid_size = random.randint(100, 10000)
                ask_size = random.randint(100, 10000)
                
                result[symbol] = {
                    'price': price,
                    'bid_price': bid_price,
                    'ask_price': ask_price,
                    'bid_size': bid_size,
                    'ask_size': ask_size,
                    'time': dt.datetime.now()
                }
            elif data_type == "trade":
                result[symbol] = {
                    'price': price,
                    'size': random.randint(100, 5000),
                    'time': dt.datetime.now(),
                    'exchange': "MOCK"
                }
            elif data_type == "bar":
                # 生成随机的K线数据
                open_price = price * (1 - random.uniform(0, 0.01))
                high_price = max(open_price, price) * (1 + random.uniform(0, 0.005))
                low_price = min(open_price, price) * (1 - random.uniform(0, 0.005))
                close_price = price
                volume = random.randint(10000, 1000000)
                
                result[symbol] = {
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'volume': volume,
                    'time': dt.datetime.now()
                }
            else:
                result[symbol] = {
                    'price': price,
                    'time': dt.datetime.now()
                }
        
        return result
    
    def _get_current_price(self, symbol: str) -> float:
        """
        获取当前价格
        
        参数:
            symbol: 证券代码
            
        返回:
            当前价格
        """
        # 如果已有缓存价格，使用缓存
        if symbol in self._current_prices:
            # 添加小的随机波动
            price = self._current_prices[symbol]
            price *= (1 + random.uniform(-0.001, 0.001))
            self._current_prices[symbol] = price
            return price
        
        # 如果提供了市场数据，使用市场数据
        if self._market_data is not None and symbol in self._market_data.columns:
            # 使用最新的收盘价
            price = self._market_data[symbol].iloc[-1]
            self._current_prices[symbol] = price
            return price
        
        # 否则生成一个随机价格
        price = round(random.uniform(10, 1000), 2)
        self._current_prices[symbol] = price
        return price
    
    def _update_account_info(self) -> None:
        """更新账户信息"""
        self._account_info = {
            'account_id': '模拟账户',
            'balance': self._account_balance,
            'equity': sum(self._account_balance.values()),
            'positions': len(self._positions),
            'currency': 'CNY',
            'margin': 0.0,
            'unrealized_pnl': 0.0,
            'realized_pnl': 0.0
        }
        
        # 计算持仓的未实现盈亏
        unrealized_pnl = 0.0
        for symbol, position in self._positions.items():
            current_price = self._get_current_price(symbol)
            market_value = position['quantity'] * current_price
            position_pnl = market_value - position['cost_basis']
            unrealized_pnl += position_pnl
        
        self._account_info['unrealized_pnl'] = unrealized_pnl
        self._account_info['equity'] += unrealized_pnl
    
    def _start_processing_thread(self) -> None:
        """启动订单处理线程"""
        if self._processing_thread is not None and self._processing_thread.is_alive():
            return
        
        self._stop_event.clear()
        self._processing_thread = threading.Thread(target=self._process_orders_loop)
        self._processing_thread.daemon = True
        self._processing_thread.start()
    
    def _stop_processing_thread(self) -> None:
        """停止订单处理线程"""
        if self._processing_thread is None or not self._processing_thread.is_alive():
            return
        
        self._stop_event.set()
        self._processing_thread.join(timeout=5.0)
    
    def _process_orders_loop(self) -> None:
        """订单处理循环"""
        while not self._stop_event.is_set():
            # 处理所有待处理的订单
            for order_id, order in list(self._orders.items()):
                if order['status'] == OrderStatus.PENDING:
                    self._process_order(order_id)
            
            # 更新账户信息
            self._update_account_info()
            
            # 等待一段时间
            time.sleep(self._simulation_delay)
    
    def _process_order(self, order_id: str) -> None:
        """
        处理单个订单
        
        参数:
            order_id: 订单ID
        """
        if order_id not in self._orders:
            return
        
        order = self._orders[order_id]
        
        # 检查订单状态
        if order['status'] != OrderStatus.PENDING:
            return
            
        symbol = order['symbol']
        order_type = order['order_type']
        side = order['side']
        quantity = order['quantity']
        price = order['price']
        stop_price = order['stop_price']
        
        # 获取当前价格
        current_price = self._get_current_price(symbol)
        
        # 检查是否满足执行条件
        can_execute = False
        
        if order_type == OrderType.MARKET:
            can_execute = True
            execution_price = current_price
        elif order_type == OrderType.LIMIT:
            if side in [OrderSide.BUY, OrderSide.BUY_TO_COVER]:
                can_execute = current_price <= price
            else:
                can_execute = current_price >= price
            execution_price = price
        elif order_type == OrderType.STOP:
            if side in [OrderSide.BUY, OrderSide.BUY_TO_COVER]:
                can_execute = current_price >= stop_price
            else:
                can_execute = current_price <= stop_price
            execution_price = current_price
        elif order_type == OrderType.STOP_LIMIT:
            if side in [OrderSide.BUY, OrderSide.BUY_TO_COVER]:
                can_execute = current_price >= stop_price and current_price <= price
            else:
                can_execute = current_price <= stop_price and current_price >= price
            execution_price = price
        
        # 随机因素影响成交概率
        if can_execute and random.random() <= self._fill_probability:
            # 添加滑点
            if self._slippage_std > 0:
                slippage = random.normalvariate(0, self._slippage_std)
                if side in [OrderSide.BUY, OrderSide.BUY_TO_COVER]:
                    execution_price *= (1 + slippage)
                else:
                    execution_price *= (1 - slippage)
            
            # 创建成交记录
            execution_id = str(uuid.uuid4())
            execution_time = dt.datetime.now()
            
            execution = {
                'execution_id': execution_id,
                'order_id': order_id,
                'symbol': symbol,
                'side': side,
                'quantity': quantity,
                'price': execution_price,
                'time': execution_time,
                'commission': quantity * execution_price * 0.0005,  # 模拟佣金
                'exchange': "MOCK"
            }
            
            # 更新订单状态
            order['status'] = OrderStatus.FILLED
            order['filled_quantity'] = quantity
            order['filled_price'] = execution_price
            order['filled_time'] = execution_time
            order['updated_time'] = execution_time
            order['executions'].append(execution)
            
            # 添加到成交记录列表
            self._executions.append(execution)
            
            # 更新持仓
            self._update_position(symbol, side, quantity, execution_price)
            
            self._logger.info(f"订单 {order_id} 已成交: {quantity} {symbol} @ {execution_price}")
    
    def _update_position(self, symbol: str, side: OrderSide, quantity: float, price: float) -> None:
        """
        更新持仓信息
        
        参数:
            symbol: 证券代码
            side: 交易方向
            quantity: 数量
            price: 价格
        """
        # 计算交易金额
        trade_value = quantity * price
        
        # 添加佣金
        commission = trade_value * 0.0005
        
        # 根据交易方向更新持仓
        if side in [OrderSide.BUY, OrderSide.BUY_TO_COVER]:
            # 买入或买入平仓
            if symbol not in self._positions:
                self._positions[symbol] = {
                    'quantity': quantity,
                    'avg_price': price,
                    'cost_basis': trade_value + commission
                }
            else:
                existing_position = self._positions[symbol]
                total_quantity = existing_position['quantity'] + quantity
                total_cost = existing_position['cost_basis'] + trade_value + commission
                
                # 更新平均价格和成本基础
                existing_position['quantity'] = total_quantity
                existing_position['avg_price'] = total_cost / total_quantity
                existing_position['cost_basis'] = total_cost
            
            # 从账户余额中扣除资金
            self._account_balance['CNY'] -= (trade_value + commission)
        else:
            # 卖出或卖空
            if symbol in self._positions:
                existing_position = self._positions[symbol]
                
                if side == OrderSide.SELL:
                    # 正常卖出，减少持仓
                    if existing_position['quantity'] >= quantity:
                        # 计算卖出部分的成本
                        position_ratio = quantity / existing_position['quantity']
                        cost_to_remove = existing_position['cost_basis'] * position_ratio
                        
                        # 计算实现盈亏
                        realized_pnl = trade_value - cost_to_remove - commission
                        
                        # 更新持仓
                        remaining_quantity = existing_position['quantity'] - quantity
                        
                        if remaining_quantity > 0:
                            # 仍有剩余持仓
                            existing_position['quantity'] = remaining_quantity
                            existing_position['cost_basis'] -= cost_to_remove
                        else:
                            # 全部卖出
                            del self._positions[symbol]
                        
                        # 添加资金到账户余额
                        self._account_balance['CNY'] += (trade_value - commission)
                    else:
                        # 卖出量超过持仓量，不应该发生
                        self._logger.error(f"卖出量 {quantity} 超过持仓量 {existing_position['quantity']}")
                else:  # SELL_SHORT
                    # 卖空，创建负持仓
                    if existing_position['quantity'] > 0:
                        # 已有多头持仓，先平仓
                        if existing_position['quantity'] >= quantity:
                            # 多头足够平仓
                            self._update_position(symbol, OrderSide.SELL, quantity, price)
                        else:
                            # 多头不足，先平仓再做空
                            self._update_position(symbol, OrderSide.SELL, existing_position['quantity'], price)
                            self._update_position(symbol, OrderSide.SELL_SHORT, quantity - existing_position['quantity'], price)
                    else:
                        # 已有空头持仓或无持仓，增加空头
                        new_quantity = existing_position.get('quantity', 0) - quantity
                        
                        # 更新持仓
                        if symbol not in self._positions:
                            self._positions[symbol] = {
                                'quantity': -quantity,
                                'avg_price': price,
                                'cost_basis': trade_value + commission
                            }
                        else:
                            total_cost = existing_position['cost_basis'] + trade_value + commission
                            
                            # 更新平均价格和成本基础
                            existing_position['quantity'] = new_quantity
                            existing_position['avg_price'] = total_cost / abs(new_quantity)
                            existing_position['cost_basis'] = total_cost
                        
                        # 添加资金到账户余额
                        self._account_balance['CNY'] += (trade_value - commission) 