"""
TuShare交易接口模块

提供与TuShare API通信的交易接口，用于A股交易和数据获取。
"""

import datetime as dt
import time
import hmac
import hashlib
import base64
import json
from typing import Dict, List, Optional, Union, Any, Tuple

import pandas as pd
import requests

from trading.interfaces.real_trading_interface import (
    RealTradingInterface, CredentialType
)
from trading.interfaces.trading_interface import (
    OrderStatus, OrderType, OrderSide, TimeInForce,
    TradingException, OrderExecutionException, ConnectionException,
    AuthenticationException, RateLimitException
)


class TushareError(TradingException):
    """TuShare API错误"""
    
    def __init__(self, code: int, message: str):
        """
        初始化TuShare错误
        
        参数:
            code: 错误代码
            message: 错误信息
        """
        self.code = code
        self.message = message
        super().__init__(f"TuShare错误 {code}: {message}")


class TushareTradingInterface(RealTradingInterface):
    """
    TuShare交易接口
    
    实现与TuShare API的通信，用于A股交易和数据获取。
    """
    
    def __init__(
        self,
        name: str = "TuShare交易接口",
        description: str = "通过TuShare API进行A股交易",
        api_key: str = None,
        credentials: Dict[str, Any] = None,
        credential_file: str = None,
        timeout: int = 30,
        max_retries: int = 3,
        **kwargs
    ):
        """
        初始化TuShare交易接口
        
        参数:
            name: 接口名称
            description: 接口描述
            api_key: TuShare API令牌
            credentials: 认证凭据字典
            credential_file: 认证凭据文件路径
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            **kwargs: 其他参数
        """
        base_url = "https://api.tushare.pro"
        
        super().__init__(
            name=name,
            description=description,
            credentials=credentials,
            credential_file=credential_file,
            base_url=base_url,
            timeout=timeout,
            max_retries=max_retries,
            **kwargs
        )
        
        # 设置API令牌
        if api_key:
            self._credentials["api_key"] = api_key
        
        # 设置请求计数和频率限制
        self._request_count = 0
        self._last_request_time = 0
        self._rate_limit = kwargs.get("rate_limit", 500)  # 默认每分钟500次请求
        self._min_request_interval = 60.0 / self._rate_limit if self._rate_limit > 0 else 0
        
        # 设置市场数据缓存
        self._market_data_cache = {}
        self._market_data_cache_time = {}
        self._market_data_cache_duration = kwargs.get("market_data_cache_duration", 60)  # 缓存60秒
        
        # 交易相关的设置
        self._market_hours = kwargs.get("market_hours", {
            "CN": ((9, 30), (15, 0)),  # 中国A股市场交易时间
        })
        self._trade_days = {}
        self._last_trade_day_update = 0
        self._trade_day_update_interval = 86400  # 交易日历每天更新一次
        
        # 设置市场状态
        self._market_status = {
            "CN": False,  # 默认为关闭状态
        }
    
    def _authenticate(self) -> bool:
        """
        进行TuShare API认证
        
        返回:
            认证是否成功
        """
        if "api_key" not in self._credentials:
            self._logger.error("缺少TuShare API令牌")
            return False
        
        api_key = self._credentials["api_key"]
        
        try:
            # 验证API令牌有效性
            response = requests.post(
                f"{self._base_url}/token",
                json={"token": api_key},
                timeout=self._timeout
            )
            
            response.raise_for_status()
            data = response.json()
            
            if data.get("code") != 0:
                self._logger.error(f"TuShare API认证失败: {data.get('msg', '未知错误')}")
                return False
            
            self._auth_token = api_key
            self._auth_expiry = dt.datetime.now() + dt.timedelta(days=1)  # 临时设置为1天有效期
            
            # 更新账户信息
            self._update_account_info()
            
            self._logger.info("TuShare API认证成功")
            return True
        
        except Exception as e:
            self._logger.error(f"TuShare API认证时发生错误: {str(e)}")
            return False
    
    def _add_auth_headers(self, headers: Dict[str, str]) -> None:
        """
        添加认证头信息
        
        参数:
            headers: 请求头字典
        """
        if self._auth_token:
            # TuShare API使用token而非headers认证，这里不做任何操作
            pass
    
    def _update_account_info(self) -> None:
        """更新账户信息"""
        try:
            # 获取账户信息，这里的实现取决于TuShare API是否提供账户接口
            # 目前TuShare API主要是数据接口，不包含实盘交易功能
            # 所以这里仅做演示用途
            self._account_info = {
                "account_id": "DEMO_ACCOUNT",
                "account_type": "DEMO",
                "status": "ACTIVE",
                "created_at": dt.datetime.now().isoformat(),
                "permissions": ["READ", "TRADE"]
            }
        except Exception as e:
            self._logger.error(f"更新账户信息时发生错误: {str(e)}")
    
    def _call_tushare_api(
        self,
        api_name: str,
        params: Dict[str, Any] = None,
        fields: List[str] = None
    ) -> pd.DataFrame:
        """
        调用TuShare API
        
        参数:
            api_name: API名称
            params: 查询参数
            fields: 返回字段列表
            
        返回:
            API响应数据DataFrame
        """
        if not self._is_authenticated():
            self._authenticate()
        
        # 构建请求数据
        request_data = {
            "api_name": api_name,
            "token": self._auth_token,
            "params": params or {},
            "fields": fields or []
        }
        
        # 检查频率限制
        self._check_rate_limit()
        
        try:
            response = self._session.post(
                f"{self._base_url}/api",
                json=request_data,
                timeout=self._timeout
            )
            
            # 更新请求计数
            self._request_count += 1
            self._last_request_time = time.time()
            
            response.raise_for_status()
            data = response.json()
            
            if data.get("code") != 0:
                error_msg = data.get("msg", "未知错误")
                error_code = data.get("code", -1)
                raise TushareError(error_code, error_msg)
            
            # 转换为DataFrame
            if "data" in data and "items" in data["data"]:
                df = pd.DataFrame(data["data"]["items"], columns=data["data"]["fields"])
                return df
            else:
                return pd.DataFrame()
        
        except TushareError as e:
            self._logger.error(f"TuShare API错误: {str(e)}")
            raise
        except requests.exceptions.RequestException as e:
            self._logger.error(f"TuShare API请求失败: {str(e)}")
            raise ConnectionException(f"TuShare API请求失败: {str(e)}")
        except Exception as e:
            self._logger.error(f"调用TuShare API时发生错误: {str(e)}")
            raise TradingException(f"调用TuShare API时发生错误: {str(e)}")
    
    def _check_rate_limit(self) -> None:
        """检查API请求频率限制"""
        if self._min_request_interval <= 0:
            return
        
        current_time = time.time()
        elapsed = current_time - self._last_request_time
        
        if elapsed < self._min_request_interval:
            sleep_time = self._min_request_interval - elapsed
            self._logger.debug(f"等待频率限制 {sleep_time:.3f} 秒")
            time.sleep(sleep_time)
    
    def is_market_open(self, market: str = "CN") -> bool:
        """
        检查市场是否开市
        
        参数:
            market: 市场代码
            
        返回:
            市场是否开市
        """
        if not self._is_connected:
            raise ConnectionException("未连接到交易系统")
        
        # 检查缓存
        current_time = time.time()
        if market in self._market_status_last_update and \
           current_time - self._market_status_last_update[market] < self._market_status_cache_time:
            return self._market_status[market]
        
        try:
            # 获取当前日期
            now = dt.datetime.now()
            today = now.strftime("%Y%m%d")
            
            # 检查是否为交易日
            if not self._is_trade_day(today, market):
                self._market_status[market] = False
                return False
            
            # 检查当前时间是否在交易时间内
            if market in self._market_hours:
                market_hours = self._market_hours[market]
                current_time = now.time()
                
                start_time = dt.time(market_hours[0][0], market_hours[0][1])
                end_time = dt.time(market_hours[1][0], market_hours[1][1])
                
                is_open = start_time <= current_time <= end_time
                self._market_status[market] = is_open
                self._market_status_last_update[market] = time.time()
                
                return is_open
            else:
                self._logger.warning(f"未知市场: {market}")
                return False
        
        except Exception as e:
            self._logger.error(f"检查市场状态时发生错误: {str(e)}")
            return False
    
    def _is_trade_day(self, date: str, market: str = "CN") -> bool:
        """
        检查是否为交易日
        
        参数:
            date: 日期字符串 (YYYYMMDD)
            market: 市场代码
            
        返回:
            是否为交易日
        """
        # 检查是否需要更新交易日历
        current_time = time.time()
        if market not in self._trade_days or \
           current_time - self._last_trade_day_update > self._trade_day_update_interval:
            self._update_trade_days(market)
        
        # 检查是否为交易日
        if market in self._trade_days and date in self._trade_days[market]:
            return self._trade_days[market][date]
        
        # 默认不是交易日
        return False
    
    def _update_trade_days(self, market: str = "CN") -> None:
        """
        更新交易日历
        
        参数:
            market: 市场代码
        """
        try:
            # 获取今年的交易日历
            start_date = dt.datetime.now().strftime("%Y0101")
            end_date = dt.datetime.now().strftime("%Y1231")
            
            df = self._call_tushare_api(
                "trade_cal",
                params={"exchange": "SSE", "start_date": start_date, "end_date": end_date},
                fields=["cal_date", "is_open"]
            )
            
            if df.empty:
                self._logger.warning("获取交易日历失败，使用默认配置")
                return
            
            # 更新交易日历
            trade_days = {}
            for _, row in df.iterrows():
                trade_days[row["cal_date"]] = row["is_open"] == 1
            
            self._trade_days[market] = trade_days
            self._last_trade_day_update = time.time()
            
            self._logger.info(f"已更新{market}市场的交易日历")
        
        except Exception as e:
            self._logger.error(f"更新交易日历时发生错误: {str(e)}")
    
    def get_account_balance(self) -> Dict[str, float]:
        """
        获取账户资金余额
        
        返回:
            账户资金余额字典，键为币种，值为金额
        """
        if not self._is_connected:
            raise ConnectionException("未连接到交易系统")
        
        # 注意：TuShare API目前没有提供实盘交易功能，
        # 这里仅作为示例，返回模拟余额
        return {"CNY": 1000000.0}
    
    def get_positions(self) -> pd.DataFrame:
        """
        获取当前持仓
        
        返回:
            持仓信息DataFrame，包含证券代码、数量、成本价等信息
        """
        if not self._is_connected:
            raise ConnectionException("未连接到交易系统")
        
        # 注意：TuShare API目前没有提供实盘交易功能，
        # 这里仅作为示例，返回模拟持仓
        positions = [
            {
                "symbol": "000001.SZ",
                "quantity": 1000,
                "avg_price": 12.5,
                "cost_basis": 12500.0,
                "current_price": 13.0,
                "market_value": 13000.0,
                "unrealized_pnl": 500.0
            },
            {
                "symbol": "600000.SH",
                "quantity": 2000,
                "avg_price": 8.2,
                "cost_basis": 16400.0,
                "current_price": 8.5,
                "market_value": 17000.0,
                "unrealized_pnl": 600.0
            }
        ]
        
        return pd.DataFrame(positions)
    
    def place_order(
        self,
        symbol: str,
        order_type: OrderType,
        side: OrderSide,
        quantity: float,
        price: Optional[float] = None,
        time_in_force: TimeInForce = TimeInForce.DAY,
        stop_price: Optional[float] = None,
        client_order_id: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        下单
        
        参数:
            symbol: 证券代码
            order_type: 订单类型
            side: 交易方向
            quantity: 数量
            price: 价格（对于限价单）
            time_in_force: 订单有效期
            stop_price: 止损价（对于止损单）
            client_order_id: 客户端订单ID
            **kwargs: 其他参数
            
        返回:
            订单ID
        """
        if not self._is_connected:
            raise ConnectionException("未连接到交易系统")
        
        # 验证订单参数
        is_valid, error_msg = self.validate_order(
            symbol, order_type, side, quantity, price, **kwargs
        )
        
        if not is_valid:
            raise OrderExecutionException(f"订单验证失败: {error_msg}")
        
        # 注意：TuShare API目前没有提供实盘交易功能，
        # 这里仅作为示例，返回模拟订单ID
        order_id = f"TS_{int(time.time())}_{symbol}"
        
        self._logger.info(f"已提交订单 {order_id}: {side.name} {quantity} {symbol} @ {price}")
        
        return order_id
    
    def cancel_order(self, order_id: str) -> bool:
        """
        取消订单
        
        参数:
            order_id: 订单ID
            
        返回:
            是否成功取消
        """
        if not self._is_connected:
            raise ConnectionException("未连接到交易系统")
        
        # 注意：TuShare API目前没有提供实盘交易功能，
        # 这里仅作为示例，始终返回成功
        self._logger.info(f"已取消订单 {order_id}")
        
        return True
    
    def get_order(self, order_id: str) -> Dict[str, Any]:
        """
        查询订单状态
        
        参数:
            order_id: 订单ID
            
        返回:
            订单信息字典
        """
        if not self._is_connected:
            raise ConnectionException("未连接到交易系统")
        
        # 注意：TuShare API目前没有提供实盘交易功能，
        # 这里仅作为示例，返回模拟订单状态
        return {
            "order_id": order_id,
            "symbol": "000001.SZ",
            "order_type": OrderType.LIMIT.name,
            "side": OrderSide.BUY.name,
            "quantity": 1000,
            "price": 12.5,
            "status": OrderStatus.FILLED.name,
            "filled_quantity": 1000,
            "avg_fill_price": 12.5,
            "created_time": dt.datetime.now().isoformat(),
            "updated_time": dt.datetime.now().isoformat()
        }
    
    def get_orders(
        self,
        status: Optional[OrderStatus] = None,
        symbol: Optional[str] = None,
        start_time: Optional[dt.datetime] = None,
        end_time: Optional[dt.datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        查询多个订单
        
        参数:
            status: 订单状态过滤
            symbol: 证券代码过滤
            start_time: 开始时间
            end_time: 结束时间
            
        返回:
            订单信息列表
        """
        if not self._is_connected:
            raise ConnectionException("未连接到交易系统")
        
        # 注意：TuShare API目前没有提供实盘交易功能，
        # 这里仅作为示例，返回模拟订单列表
        orders = [
            {
                "order_id": f"TS_{int(time.time())}_000001.SZ",
                "symbol": "000001.SZ",
                "order_type": OrderType.LIMIT.name,
                "side": OrderSide.BUY.name,
                "quantity": 1000,
                "price": 12.5,
                "status": OrderStatus.FILLED.name,
                "filled_quantity": 1000,
                "avg_fill_price": 12.5,
                "created_time": dt.datetime.now().isoformat(),
                "updated_time": dt.datetime.now().isoformat()
            },
            {
                "order_id": f"TS_{int(time.time())}_600000.SH",
                "symbol": "600000.SH",
                "order_type": OrderType.MARKET.name,
                "side": OrderSide.SELL.name,
                "quantity": 500,
                "price": None,
                "status": OrderStatus.FILLED.name,
                "filled_quantity": 500,
                "avg_fill_price": 8.3,
                "created_time": dt.datetime.now().isoformat(),
                "updated_time": dt.datetime.now().isoformat()
            }
        ]
        
        # 应用过滤条件
        filtered_orders = orders
        
        if status:
            filtered_orders = [o for o in filtered_orders if OrderStatus[o["status"]] == status]
        
        if symbol:
            filtered_orders = [o for o in filtered_orders if o["symbol"] == symbol]
        
        if start_time:
            filtered_orders = [o for o in filtered_orders if dt.datetime.fromisoformat(o["created_time"]) >= start_time]
        
        if end_time:
            filtered_orders = [o for o in filtered_orders if dt.datetime.fromisoformat(o["created_time"]) <= end_time]
        
        return filtered_orders
    
    def get_executions(
        self,
        order_id: Optional[str] = None,
        symbol: Optional[str] = None,
        start_time: Optional[dt.datetime] = None,
        end_time: Optional[dt.datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        查询成交记录
        
        参数:
            order_id: 订单ID过滤
            symbol: 证券代码过滤
            start_time: 开始时间
            end_time: 结束时间
            
        返回:
            成交记录列表
        """
        if not self._is_connected:
            raise ConnectionException("未连接到交易系统")
        
        # 注意：TuShare API目前没有提供实盘交易功能，
        # 这里仅作为示例，返回模拟成交记录
        executions = [
            {
                "execution_id": f"EXEC_{int(time.time())}_1",
                "order_id": f"TS_{int(time.time())}_000001.SZ",
                "symbol": "000001.SZ",
                "side": OrderSide.BUY.name,
                "quantity": 1000,
                "price": 12.5,
                "time": dt.datetime.now().isoformat()
            },
            {
                "execution_id": f"EXEC_{int(time.time())}_2",
                "order_id": f"TS_{int(time.time())}_600000.SH",
                "symbol": "600000.SH",
                "side": OrderSide.SELL.name,
                "quantity": 500,
                "price": 8.3,
                "time": dt.datetime.now().isoformat()
            }
        ]
        
        # 应用过滤条件
        filtered_executions = executions
        
        if order_id:
            filtered_executions = [e for e in filtered_executions if e["order_id"] == order_id]
        
        if symbol:
            filtered_executions = [e for e in filtered_executions if e["symbol"] == symbol]
        
        if start_time:
            filtered_executions = [e for e in filtered_executions if dt.datetime.fromisoformat(e["time"]) >= start_time]
        
        if end_time:
            filtered_executions = [e for e in filtered_executions if dt.datetime.fromisoformat(e["time"]) <= end_time]
        
        return filtered_executions
    
    def get_market_data(
        self,
        symbols: List[str],
        data_type: str = "quote",
        **kwargs
    ) -> Dict[str, Any]:
        """
        获取市场数据
        
        参数:
            symbols: 证券代码列表
            data_type: 数据类型，如 'quote', 'trade', 'bar'
            **kwargs: 其他参数
            
        返回:
            市场数据字典
        """
        if not self._is_connected:
            raise ConnectionException("未连接到交易系统")
        
        # 检查缓存
        cache_key = f"{data_type}_{','.join(symbols)}"
        current_time = time.time()
        
        if cache_key in self._market_data_cache and \
           current_time - self._market_data_cache_time.get(cache_key, 0) < self._market_data_cache_duration:
            return self._market_data_cache[cache_key]
        
        try:
            result = {}
            
            if data_type == "quote":
                # 获取实时行情
                df = self._call_tushare_api(
                    "daily",
                    params={"ts_code": ",".join(symbols), "trade_date": dt.datetime.now().strftime("%Y%m%d")},
                    fields=["ts_code", "open", "high", "low", "close", "pre_close", "vol", "amount"]
                )
                
                if not df.empty:
                    for _, row in df.iterrows():
                        symbol = row["ts_code"]
                        result[symbol] = {
                            "symbol": symbol,
                            "price": row["close"],
                            "open": row["open"],
                            "high": row["high"],
                            "low": row["low"],
                            "close": row["close"],
                            "volume": row["vol"],
                            "amount": row["amount"],
                            "bid_price": row["close"] * 0.999,  # 模拟买一价
                            "ask_price": row["close"] * 1.001,  # 模拟卖一价
                            "bid_size": row["vol"] / 100,       # 模拟买一量
                            "ask_size": row["vol"] / 100,       # 模拟卖一量
                            "timestamp": dt.datetime.now().isoformat()
                        }
            
            elif data_type == "bar":
                # 获取K线数据
                end_date = dt.datetime.now().strftime("%Y%m%d")
                start_date = (dt.datetime.now() - dt.timedelta(days=30)).strftime("%Y%m%d")
                
                for symbol in symbols:
                    df = self._call_tushare_api(
                        "daily",
                        params={"ts_code": symbol, "start_date": start_date, "end_date": end_date},
                        fields=["ts_code", "trade_date", "open", "high", "low", "close", "vol", "amount"]
                    )
                    
                    if not df.empty:
                        df = df.sort_values("trade_date")
                        result[symbol] = {
                            "symbol": symbol,
                            "open": df["open"].tolist(),
                            "high": df["high"].tolist(),
                            "low": df["low"].tolist(),
                            "close": df["close"].tolist(),
                            "volume": df["vol"].tolist(),
                            "datetime": df["trade_date"].tolist()
                        }
            
            # 更新缓存
            self._market_data_cache[cache_key] = result
            self._market_data_cache_time[cache_key] = current_time
            
            return result
        
        except Exception as e:
            self._logger.error(f"获取市场数据时发生错误: {str(e)}")
            raise 