"""
交易接口子模块

该子模块提供了与各种交易平台通信的统一接口，包括：
1. 抽象交易接口定义
2. 模拟交易接口实现
3. 真实交易接口基类
4. 特定平台的交易接口适配器
5. 交易接口工厂
"""

from trading.interfaces.trading_interface import (
    TradingInterface, OrderType, OrderSide, OrderStatus, 
    TimeInForce, TradingException, 
    ConnectionException, AuthenticationException, 
    RateLimitException, OrderExecutionException
)

# 临时注释掉可能不存在的导入
# from trading.interfaces.mock_trading_interface import MockTradingInterface
# from trading.interfaces.real_trading_interface import RealTradingInterface, CredentialType
# from trading.interfaces.tushare_trading_interface import TushareTradingInterface, TushareError
# from trading.interfaces.trading_interface_factory import TradingInterfaceFactory

__all__ = [
    # 抽象接口
    'TradingInterface',
    
    # 订单相关枚举和类
    'OrderType', 'OrderSide', 'OrderStatus', 'TimeInForce',
    
    # 异常类
    'TradingException', 'ConnectionException', 'AuthenticationException',
    'RateLimitException', 'OrderExecutionException',
    
    # 以下类暂时注释掉，因为可能不存在
    # 'ExecutionReport', 'ValidationException', 'OrderNotFoundException',
    # 'MarketClosedException', 'PermissionDeniedException',
    # 'MockTradingInterface', 'RealTradingInterface', 'CredentialType',
    # 'TushareTradingInterface', 'TushareError', 'TradingInterfaceFactory'
] 