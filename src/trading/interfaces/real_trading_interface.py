"""
实盘交易接口模块

为各种实际交易平台提供统一的接口实现基类，包括认证管理、错误处理和共享功能。
所有特定交易平台的实现都应继承此类。
"""

import datetime as dt
import logging
import time
import uuid
import json
import os
import threading
from abc import abstractmethod
from enum import Enum
from typing import Dict, List, Optional, Union, Any, Tuple, Callable

import pandas as pd
import requests

from trading.interfaces.trading_interface import (
    TradingInterface, OrderStatus, OrderType, OrderSide, TimeInForce,
    TradingException, OrderExecutionException, ConnectionException,
    AuthenticationException, RateLimitException
)


class CredentialType(Enum):
    """认证类型枚举"""
    API_KEY = "api_key"                 # API密钥
    ACCESS_TOKEN = "access_token"       # 访问令牌
    PASSWORD = "password"               # 密码
    CERTIFICATE = "certificate"         # 证书
    CUSTOM = "custom"                   # 自定义


class RealTradingInterface(TradingInterface):
    """
    实盘交易接口基类
    
    为所有实盘交易平台实现提供通用的基础功能，包括认证管理、错误处理和日志记录。
    特定交易平台的实现应继承此类。
    """
    
    def __init__(
        self,
        name: str,
        description: str,
        credentials: Dict[str, Any] = None,
        credential_file: str = None,
        base_url: str = None,
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        rate_limit_wait: bool = True,
        log_requests: bool = False,
        **kwargs
    ):
        """
        初始化实盘交易接口
        
        参数:
            name: 接口名称
            description: 接口描述
            credentials: 认证凭据字典
            credential_file: 认证凭据文件路径
            base_url: API基础URL
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            rate_limit_wait: 是否等待频率限制解除
            log_requests: 是否记录请求日志
            **kwargs: 其他参数
        """
        super().__init__(name=name, description=description, **kwargs)
        
        # 基础URL和请求配置
        self._base_url = base_url
        self._timeout = timeout
        self._max_retries = max_retries
        self._retry_delay = retry_delay
        self._rate_limit_wait = rate_limit_wait
        self._log_requests = log_requests
        
        # 认证和会话管理
        self._credentials = {}
        self._session = requests.Session()
        self._auth_token = None
        self._auth_expiry = None
        self._lock = threading.RLock()
        
        # 加载认证凭据
        if credentials:
            self._credentials = credentials
        elif credential_file:
            self._load_credentials(credential_file)
        
        # 市场状态监控
        self._market_status = {}
        self._market_status_last_update = {}
        self._market_status_cache_time = 60  # 缓存市场状态的时间（秒）
        
        # 设置日志
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self._setup_logging()
    
    def _setup_logging(self):
        """设置日志记录"""
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        
        if not self._logger.handlers:
            self._logger.addHandler(handler)
        
        self._logger.setLevel(logging.INFO)
        
        # 请求日志记录器
        if self._log_requests:
            requests_log = logging.getLogger("requests.packages.urllib3")
            requests_log.setLevel(logging.DEBUG)
            requests_log.propagate = True
    
    def _load_credentials(self, credential_file: str):
        """
        从文件加载认证凭据
        
        参数:
            credential_file: 认证凭据文件路径
        """
        if not os.path.exists(credential_file):
            self._logger.error(f"认证凭据文件不存在: {credential_file}")
            return
        
        try:
            # {{ AURA-X: Modify - 使用ConfigFactory统一配置管理，消除直接JSON读取. Approval: 寸止(ID:深度架构复查修复). }}
            from src.utils.config.config_factory import config_factory
            import os

            # 解析文件路径
            config_dir = os.path.dirname(credential_file)
            config_name = os.path.splitext(os.path.basename(credential_file))[0]

            # 使用ConfigFactory加载配置
            self._credentials = config_factory.load_config(config_name, config_dir)
            self._logger.info(f"已从文件加载认证凭据: {credential_file}")
        except Exception as e:
            self._logger.error(f"加载认证凭据失败: {str(e)}")
    
    def _save_credentials(self, credential_file: str):
        """
        保存认证凭据到文件
        
        参数:
            credential_file: 认证凭据文件路径
        """
        try:
            # {{ AURA-X: Modify - 使用ConfigFactory统一配置管理，消除直接JSON写入. Approval: 寸止(ID:深度架构复查修复). }}
            from src.utils.config.config_factory import config_factory
            import os

            # 解析文件路径
            config_dir = os.path.dirname(credential_file)
            config_name = os.path.splitext(os.path.basename(credential_file))[0]

            # 使用ConfigFactory保存配置
            config_factory.save_config(config_name, self._credentials, config_dir)
            self._logger.info(f"已保存认证凭据到文件: {credential_file}")
        except Exception as e:
            self._logger.error(f"保存认证凭据失败: {str(e)}")
    
    def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Dict[str, Any] = None,
        data: Dict[str, Any] = None,
        headers: Dict[str, str] = None,
        auth_required: bool = True,
        retry_count: int = 0
    ) -> Dict[str, Any]:
        """
        发送API请求
        
        参数:
            method: 请求方法 (GET, POST, PUT, DELETE)
            endpoint: API端点
            params: 查询参数
            data: 请求数据
            headers: 请求头
            auth_required: 是否需要认证
            retry_count: 当前重试次数
            
        返回:
            API响应数据
        """
        if auth_required and not self._is_authenticated():
            self._authenticate()
        
        url = f"{self._base_url}/{endpoint.lstrip('/')}"
        headers = headers or {}
        
        if auth_required and self._auth_token:
            self._add_auth_headers(headers)
        
        try:
            response = self._session.request(
                method=method,
                url=url,
                params=params,
                json=data,
                headers=headers,
                timeout=self._timeout
            )
            
            # 检查频率限制
            if response.status_code == 429 and self._rate_limit_wait and retry_count < self._max_retries:
                retry_after = int(response.headers.get('Retry-After', self._retry_delay))
                self._logger.warning(f"触发频率限制，将在 {retry_after} 秒后重试")
                time.sleep(retry_after)
                return self._make_request(
                    method, endpoint, params, data, headers, auth_required, retry_count + 1
                )
            
            # 检查认证错误
            if response.status_code == 401:
                if retry_count < self._max_retries:
                    self._logger.warning("认证失败，尝试重新认证")
                    self._authenticate()
                    return self._make_request(
                        method, endpoint, params, data, headers, auth_required, retry_count + 1
                    )
                else:
                    raise AuthenticationException("认证失败，请检查凭据")
            
            # 检查其他错误
            response.raise_for_status()
            
            return response.json()
        
        except requests.exceptions.RequestException as e:
            if retry_count < self._max_retries:
                self._logger.warning(f"请求失败，将在 {self._retry_delay} 秒后重试: {str(e)}")
                time.sleep(self._retry_delay)
                return self._make_request(
                    method, endpoint, params, data, headers, auth_required, retry_count + 1
                )
            else:
                self._logger.error(f"请求失败，达到最大重试次数: {str(e)}")
                if isinstance(e, requests.exceptions.Timeout):
                    raise ConnectionException(f"请求超时: {str(e)}")
                elif isinstance(e, requests.exceptions.ConnectionError):
                    raise ConnectionException(f"连接错误: {str(e)}")
                else:
                    raise TradingException(f"请求错误: {str(e)}")
    
    def _is_authenticated(self) -> bool:
        """
        检查认证状态
        
        返回:
            是否已认证且认证有效
        """
        if not self._auth_token:
            return False
        
        if self._auth_expiry and dt.datetime.now() >= self._auth_expiry:
            self._logger.info("认证已过期")
            return False
        
        return True
    
    @abstractmethod
    def _authenticate(self) -> bool:
        """
        进行认证
        
        子类必须实现此方法，处理特定平台的认证逻辑。
        
        返回:
            认证是否成功
        """
        pass
    
    @abstractmethod
    def _add_auth_headers(self, headers: Dict[str, str]) -> None:
        """
        添加认证头信息
        
        子类必须实现此方法，向请求头添加认证信息。
        
        参数:
            headers: 请求头字典
        """
        pass
    
    def connect(self) -> bool:
        """
        连接到交易平台
        
        返回:
            连接是否成功
        """
        if self._is_connected:
            self._logger.info("已经连接到交易平台")
            return True
        
        try:
            self._logger.info(f"正在连接到交易平台: {self._name}")
            result = self._authenticate()
            
            if result:
                self._is_connected = True
                self._logger.info(f"已成功连接到交易平台: {self._name}")
                return True
            else:
                self._logger.error(f"连接到交易平台失败: {self._name}")
                return False
        except Exception as e:
            self._logger.error(f"连接到交易平台时发生错误: {str(e)}")
            return False
    
    def disconnect(self) -> bool:
        """
        断开与交易平台的连接
        
        返回:
            断开连接是否成功
        """
        if not self._is_connected:
            self._logger.info("已经断开与交易平台的连接")
            return True
        
        try:
            self._logger.info(f"正在断开与交易平台的连接: {self._name}")
            
            # 尝试登出或清理会话
            try:
                self._logout()
            except Exception as e:
                self._logger.warning(f"登出时发生错误: {str(e)}")
            
            # 清理会话和认证信息
            self._session = requests.Session()
            self._auth_token = None
            self._auth_expiry = None
            self._is_connected = False
            
            self._logger.info(f"已成功断开与交易平台的连接: {self._name}")
            return True
        except Exception as e:
            self._logger.error(f"断开与交易平台的连接时发生错误: {str(e)}")
            return False
    
    def _logout(self) -> bool:
        """
        登出交易平台
        
        子类可以重写此方法以实现特定平台的登出逻辑。
        
        返回:
            登出是否成功
        """
        # 默认实现，子类可以重写
        return True
    
    def validate_order(
        self,
        symbol: str,
        order_type: OrderType,
        side: OrderSide,
        quantity: float,
        price: Optional[float] = None,
        **kwargs
    ) -> Tuple[bool, str]:
        """
        验证订单参数
        
        参数:
            symbol: 证券代码
            order_type: 订单类型
            side: 交易方向
            quantity: 数量
            price: 价格
            **kwargs: 其他参数
            
        返回:
            (是否有效, 错误信息)
        """
        # 基本验证
        result, message = super().validate_order(
            symbol, order_type, side, quantity, price, **kwargs
        )
        
        if not result:
            return result, message
        
        # 实盘特定验证
        try:
            # 1. 检查市场是否开市
            market = kwargs.get("market", "CN")
            if not self.is_market_open(market):
                return False, f"市场 {market} 当前未开市"
            
            # 2. 检查交易限额
            if not self._check_trading_limits(symbol, quantity, price):
                return False, "超出交易限额"
            
            # 3. 检查账户余额（对于买入订单）
            if side in [OrderSide.BUY, OrderSide.BUY_TO_COVER]:
                if not self._check_account_balance(symbol, quantity, price):
                    return False, "账户余额不足"
            
            # 4. 检查持仓（对于卖出订单）
            if side == OrderSide.SELL:
                if not self._check_position(symbol, quantity):
                    return False, "持仓不足"
            
            # 5. 检查证券交易状态
            if not self._check_symbol_status(symbol):
                return False, f"证券 {symbol} 当前不可交易"
            
        except Exception as e:
            self._logger.error(f"订单验证时发生错误: {str(e)}")
            return False, f"订单验证失败: {str(e)}"
        
        return True, ""
    
    def _check_trading_limits(self, symbol: str, quantity: float, price: Optional[float]) -> bool:
        """
        检查交易限额
        
        参数:
            symbol: 证券代码
            quantity: 数量
            price: 价格
            
        返回:
            是否在限额内
        """
        # 默认实现，子类可以重写
        return True
    
    def _check_account_balance(self, symbol: str, quantity: float, price: Optional[float]) -> bool:
        """
        检查账户余额是否足够
        
        参数:
            symbol: 证券代码
            quantity: 数量
            price: 价格
            
        返回:
            余额是否足够
        """
        # 默认实现，子类可以重写
        return True
    
    def _check_position(self, symbol: str, quantity: float) -> bool:
        """
        检查持仓是否足够
        
        参数:
            symbol: 证券代码
            quantity: 数量
            
        返回:
            持仓是否足够
        """
        # 默认实现，子类可以重写
        return True
    
    def _check_symbol_status(self, symbol: str) -> bool:
        """
        检查证券交易状态
        
        参数:
            symbol: 证券代码
            
        返回:
            是否可交易
        """
        # 默认实现，子类可以重写
        return True 