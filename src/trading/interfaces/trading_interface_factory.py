"""
交易接口工厂模块

该模块提供创建和管理各种交易接口实例的工厂类，简化交易接口的初始化和配置过程。
通过工厂模式，用户可以轻松创建不同平台的交易接口，而无需了解具体实现细节。
"""

import os
import logging
from typing import Dict, Any, Optional, Type, List, Union
import importlib
import json

from trading.interfaces.trading_interface import TradingInterface
from trading.interfaces.mock_trading_interface import MockTradingInterface
from trading.interfaces.real_trading_interface import RealTradingInterface
from trading.interfaces.tushare_trading_interface import TushareTradingInterface

# 可以根据需要导入更多交易接口实现类

logger = logging.getLogger(__name__)


class TradingInterfaceFactory:
    """
    交易接口工厂类
    
    提供创建和管理各种交易平台接口的功能，支持按名称创建接口，
    管理接口配置，以及动态加载自定义接口实现。
    """
    
    # 注册的交易接口类型映射
    _registered_interfaces: Dict[str, Type[TradingInterface]] = {
        "mock": MockTradingInterface,
        "tushare": TushareTradingInterface,
        # 可根据需要添加更多接口
    }
    
    # 接口实例缓存
    _interface_instances: Dict[str, TradingInterface] = {}
    
    # 接口配置
    _interface_configs: Dict[str, Dict[str, Any]] = {}
    
    # 默认接口配置目录
    _default_config_dir = os.path.expanduser("~/.quantification/configs")
    
    @classmethod
    def register_interface(cls, name: str, interface_class: Type[TradingInterface]) -> None:
        """
        注册新的交易接口类型
        
        Args:
            name: 交易接口名称
            interface_class: 交易接口类
        """
        if name in cls._registered_interfaces:
            logger.warning(f"交易接口 '{name}' 已存在，将被覆盖")
        
        cls._registered_interfaces[name] = interface_class
        logger.info(f"已注册交易接口: {name}")
    
    @classmethod
    def unregister_interface(cls, name: str) -> bool:
        """
        注销已注册的交易接口类型
        
        Args:
            name: 要注销的交易接口名称
            
        Returns:
            bool: 是否成功注销
        """
        if name in cls._registered_interfaces:
            del cls._registered_interfaces[name]
            logger.info(f"已注销交易接口: {name}")
            return True
        return False
    
    @classmethod
    def get_registered_interfaces(cls) -> List[str]:
        """
        获取所有已注册的交易接口名称
        
        Returns:
            List[str]: 接口名称列表
        """
        return list(cls._registered_interfaces.keys())
    
    @classmethod
    def load_interface_configs(cls, config_dir: Optional[str] = None) -> None:
        """
        从配置目录加载所有交易接口配置
        
        Args:
            config_dir: 配置文件目录，默认为~/.quantification/configs
        """
        config_dir = config_dir or cls._default_config_dir
        
        if not os.path.exists(config_dir):
            os.makedirs(config_dir, exist_ok=True)
            logger.info(f"已创建配置目录: {config_dir}")
            return
        
        # {{ AURA-X: Modify - 使用ConfigFactory统一配置管理，消除直接JSON读取. Approval: 寸止(ID:深度架构复查修复). }}
        # 加载所有.json配置文件
        from src.utils.config.config_factory import config_factory

        for filename in os.listdir(config_dir):
            if filename.endswith('.json') and not filename.startswith('.'):
                try:
                    # 使用ConfigFactory加载配置
                    config_name = filename.split('.')[0]
                    config = config_factory.load_config(config_name, config_dir)

                    # 配置文件名应为接口类型.json，如tushare.json
                    interface_type = config_name
                    cls._interface_configs[interface_type] = config
                    logger.info(f"已加载{interface_type}接口配置: {os.path.join(config_dir, filename)}")
                except Exception as e:
                    logger.error(f"加载配置文件 {filename} 失败: {e}")
    
    @classmethod
    def save_interface_config(cls, interface_type: str, config: Dict[str, Any], 
                             config_dir: Optional[str] = None) -> None:
        """
        保存交易接口配置到文件
        
        Args:
            interface_type: 接口类型名称
            config: 接口配置字典
            config_dir: 配置文件目录，默认为~/.quantification/configs
        """
        config_dir = config_dir or cls._default_config_dir
        
        if not os.path.exists(config_dir):
            os.makedirs(config_dir, exist_ok=True)
        
        filepath = os.path.join(config_dir, f"{interface_type}.json")
        
        # {{ AURA-X: Modify - 使用ConfigFactory统一配置管理，消除直接JSON写入. Approval: 寸止(ID:深度架构复查修复). }}
        # 保存到配置文件
        try:
            from src.utils.config.config_factory import config_factory

            # 使用ConfigFactory保存配置
            config_factory.save_config(interface_type, config, config_dir)

            # 更新内存中的配置
            cls._interface_configs[interface_type] = config
            logger.info(f"已保存{interface_type}接口配置: {filepath}")
        except Exception as e:
            logger.error(f"保存配置文件 {interface_type}.json 失败: {e}")
            raise
    
    @classmethod
    def create_interface(cls, interface_type: str, 
                        config: Optional[Dict[str, Any]] = None,
                        cache: bool = True, **kwargs) -> TradingInterface:
        """
        创建指定类型的交易接口实例
        
        Args:
            interface_type: 接口类型名称
            config: 接口配置字典，覆盖默认配置
            cache: 是否缓存接口实例
            **kwargs: 传递给接口构造函数的额外参数
            
        Returns:
            TradingInterface: 创建的交易接口实例
            
        Raises:
            ValueError: 如果接口类型未注册
        """
        # 检查接口类型是否已注册
        if interface_type not in cls._registered_interfaces:
            raise ValueError(f"未注册的交易接口类型: {interface_type}，可用类型: {', '.join(cls._registered_interfaces.keys())}")
        
        # 如果已缓存且不需要新配置，返回缓存实例
        instance_key = f"{interface_type}_{hash(str(kwargs))}"
        if cache and instance_key in cls._interface_instances and not config:
            logger.debug(f"使用缓存的交易接口实例: {interface_type}")
            return cls._interface_instances[instance_key]
        
        # 合并配置：默认配置和传入配置
        merged_config = {}
        
        # 加载接口默认配置
        if interface_type in cls._interface_configs:
            merged_config.update(cls._interface_configs[interface_type])
        
        # 添加传入的配置（优先级更高）
        if config:
            merged_config.update(config)
        
        # 合并kwargs参数（优先级最高）
        final_kwargs = {**merged_config, **kwargs}
        
        # 获取接口类并创建实例
        interface_class = cls._registered_interfaces[interface_type]
        interface = interface_class(**final_kwargs)
        
        # 缓存实例
        if cache:
            cls._interface_instances[instance_key] = interface
            logger.debug(f"已缓存交易接口实例: {interface_type}")
        
        return interface
    
    @classmethod
    def get_interface(cls, interface_type: str) -> Optional[TradingInterface]:
        """
        获取已创建的交易接口实例
        
        Args:
            interface_type: 接口类型名称
            
        Returns:
            Optional[TradingInterface]: 交易接口实例，如果不存在则返回None
        """
        for key, interface in cls._interface_instances.items():
            if key.startswith(f"{interface_type}_"):
                return interface
        return None
    
    @classmethod
    def clear_interface_cache(cls, interface_type: Optional[str] = None) -> None:
        """
        清除交易接口实例缓存
        
        Args:
            interface_type: 要清除的接口类型，不指定则清除所有
        """
        if interface_type:
            # 清除指定类型的接口缓存
            keys_to_remove = [k for k in cls._interface_instances.keys() if k.startswith(f"{interface_type}_")]
            for key in keys_to_remove:
                interface = cls._interface_instances.pop(key)
                # 尝试断开连接
                try:
                    if hasattr(interface, 'disconnect'):
                        interface.disconnect()
                except Exception as e:
                    logger.warning(f"断开接口连接失败: {e}")
            logger.info(f"已清除{len(keys_to_remove)}个{interface_type}接口缓存")
        else:
            # 清除所有接口缓存
            for interface in cls._interface_instances.values():
                # 尝试断开连接
                try:
                    if hasattr(interface, 'disconnect'):
                        interface.disconnect()
                except Exception as e:
                    logger.warning(f"断开接口连接失败: {e}")
            
            count = len(cls._interface_instances)
            cls._interface_instances.clear()
            logger.info(f"已清除所有接口缓存，共{count}个实例")
    
    @classmethod
    def load_custom_interfaces(cls, module_name: str) -> int:
        """
        从指定模块加载自定义交易接口实现
        
        加载模块中的所有继承自TradingInterface或RealTradingInterface的类
        
        Args:
            module_name: 模块名称，例如'custom_interfaces.binance'
            
        Returns:
            int: 加载的接口数量
        """
        try:
            module = importlib.import_module(module_name)
            count = 0
            
            # 查找所有继承自TradingInterface的类
            for name in dir(module):
                item = getattr(module, name)
                if (isinstance(item, type) and 
                    issubclass(item, TradingInterface) and 
                    item not in [TradingInterface, RealTradingInterface]):
                    
                    # 使用类名作为接口名称（小写）
                    interface_name = item.__name__.lower()
                    if interface_name.endswith('tradinginterface'):
                        # 移除通用后缀，如XYZTradingInterface -> xyz
                        interface_name = interface_name.replace('tradinginterface', '')
                    
                    cls.register_interface(interface_name, item)
                    count += 1
            
            logger.info(f"从模块 {module_name} 加载了 {count} 个自定义交易接口")
            return count
            
        except ImportError as e:
            logger.error(f"加载模块 {module_name} 失败: {e}")
            return 0
        except Exception as e:
            logger.error(f"从模块 {module_name} 加载接口失败: {e}")
            return 0
    
    @classmethod
    def create_mock_interface(cls, **kwargs) -> MockTradingInterface:
        """
        创建模拟交易接口的快捷方法
        
        Args:
            **kwargs: 传递给MockTradingInterface构造函数的参数
            
        Returns:
            MockTradingInterface: 模拟交易接口实例
        """
        return cls.create_interface("mock", **kwargs)
    
    @classmethod
    def create_tushare_interface(cls, api_key: Optional[str] = None, 
                               credential_file: Optional[str] = None,
                               **kwargs) -> TushareTradingInterface:
        """
        创建TuShare交易接口的快捷方法
        
        Args:
            api_key: TuShare API令牌
            credential_file: 凭据文件路径
            **kwargs: 传递给TushareTradingInterface构造函数的额外参数
            
        Returns:
            TushareTradingInterface: TuShare交易接口实例
        """
        config = {}
        if api_key:
            config["api_key"] = api_key
        if credential_file:
            config["credential_file"] = credential_file
            
        return cls.create_interface("tushare", config=config, **kwargs)


# 启动时加载配置
TradingInterfaceFactory.load_interface_configs() 