"""
订单分析模块

该模块提供订单执行分析功能，用于评估订单执行质量、计算滑点、分析交易成本和执行效率等。
"""

import logging
import datetime as dt
from typing import Dict, List, Optional, Any, Union, Tuple
from decimal import Decimal
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
from collections import defaultdict

from trading.interfaces.trading_interface import OrderStatus, OrderType, OrderSide
from trading.order.order_models import Order, Execution

logger = logging.getLogger(__name__)

matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False


class OrderAnalysisResult:
    """订单分析结果类"""
    
    def __init__(self, order_id: str, symbol: str):
        """
        初始化订单分析结果
        
        参数:
            order_id: 订单ID
            symbol: 交易标的代码
        """
        self.order_id = order_id
        self.symbol = symbol
        self.metrics: Dict[str, Any] = {}
        self.details: Dict[str, Any] = {}
        self.created_at = dt.datetime.now()
        
    def add_metric(self, name: str, value: Any, description: str = ""):
        """
        添加分析指标
        
        参数:
            name: 指标名称
            value: 指标值
            description: 指标描述
        """
        self.metrics[name] = {
            "value": value,
            "description": description
        }
        
    def add_detail(self, name: str, value: Any):
        """
        添加详细信息
        
        参数:
            name: 信息名称
            value: 信息值
        """
        self.details[name] = value
        
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        返回:
            包含分析结果的字典
        """
        return {
            "order_id": self.order_id,
            "symbol": self.symbol,
            "created_at": self.created_at.isoformat(),
            "metrics": self.metrics,
            "details": self.details
        }
        
    def to_dataframe(self) -> pd.DataFrame:
        """
        将指标转换为DataFrame格式
        
        返回:
            包含指标的DataFrame
        """
        data = []
        for name, info in self.metrics.items():
            data.append({
                "metric": name,
                "value": info["value"],
                "description": info["description"]
            })
        return pd.DataFrame(data)
    
    def summary(self) -> str:
        """
        生成分析结果摘要
        
        返回:
            分析结果摘要文本
        """
        lines = [
            f"订单分析结果摘要 - {self.order_id} ({self.symbol})",
            f"生成时间: {self.created_at.strftime('%Y-%m-%d %H:%M:%S')}",
            "="*50,
            "关键指标:"
        ]
        
        for name, info in self.metrics.items():
            value = info["value"]
            # 格式化数值
            if isinstance(value, (float, Decimal)):
                if abs(value) < 0.01:
                    value_str = f"{float(value):.6f}"
                else:
                    value_str = f"{float(value):.4f}"
            else:
                value_str = str(value)
                
            lines.append(f"- {name}: {value_str}")
            if info["description"]:
                lines.append(f"  {info['description']}")
        
        return "\n".join(lines)


class OrderAnalyzer:
    """订单分析器"""
    
    def __init__(self, market_data_provider=None):
        """
        初始化订单分析器
        
        参数:
            market_data_provider: 市场数据提供者，用于获取基准价格
        """
        self.market_data_provider = market_data_provider
        
    def analyze_order(self, order: Order, 
                      benchmark_price: Optional[Union[Decimal, float]] = None,
                      expected_time: Optional[dt.datetime] = None) -> OrderAnalysisResult:
        """
        分析单个订单
        
        参数:
            order: 订单对象
            benchmark_price: 基准价格，用于计算滑点，如果不提供则使用市场数据
            expected_time: 预期完成时间，用于计算延迟
            
        返回:
            订单分析结果
        """
        result = OrderAnalysisResult(order.order_id, order.symbol)
        
        # 基本信息分析
        self._analyze_basic_info(order, result)
        
        # 执行时间分析
        self._analyze_timing(order, result, expected_time)
        
        # 价格分析（滑点）
        self._analyze_price_slippage(order, result, benchmark_price)
        
        # 成交效率分析
        self._analyze_fill_efficiency(order, result)
        
        # 交易成本分析
        self._analyze_trading_costs(order, result)
        
        # 订单分段分析
        if len(order.executions) > 1:
            self._analyze_executions(order, result)
        
        return result
    
    def analyze_orders(self, orders: List[Order]) -> Dict[str, OrderAnalysisResult]:
        """
        批量分析多个订单
        
        参数:
            orders: 订单对象列表
            
        返回:
            订单ID到分析结果的映射
        """
        results = {}
        for order in orders:
            results[order.order_id] = self.analyze_order(order)
        return results
    
    def analyze_market_impact(self, orders: List[Order], 
                             market_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        分析订单对市场的影响
        
        参数:
            orders: 订单对象列表
            market_data: 市场数据（时间序列），格式为{symbol: DataFrame}
            
        返回:
            市场影响分析结果
        """
        # 按交易标的分组
        orders_by_symbol = defaultdict(list)
        for order in orders:
            orders_by_symbol[order.symbol].append(order)
            
        results = {}
        
        for symbol, symbol_orders in orders_by_symbol.items():
            if symbol not in market_data:
                logger.warning(f"缺少{symbol}的市场数据，无法分析市场影响")
                continue
                
            df = market_data[symbol]
            
            # 分析市场影响
            impact_results = self._calculate_market_impact(symbol_orders, df)
            results[symbol] = impact_results
            
        return results
    
    def generate_performance_report(self, orders: List[Order], 
                                  output_path: Optional[str] = None) -> str:
        """
        生成订单执行性能报告
        
        参数:
            orders: 订单对象列表
            output_path: 报告输出路径，如果提供则保存到文件
            
        返回:
            报告文本内容
        """
        if not orders:
            return "无订单数据可供分析"
            
        # 分析所有订单
        analysis_results = self.analyze_orders(orders)
        
        # 生成报告
        report = self._generate_report(orders, analysis_results)
        
        # 保存报告
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"订单执行性能报告已保存到 {output_path}")
            
        return report
    
    def create_visualization(self, order: Order, show: bool = False, 
                           save_path: Optional[str] = None) -> None:
        """
        创建订单执行可视化
        
        参数:
            order: 订单对象
            show: 是否显示图表
            save_path: 图表保存路径
        """
        if not order.executions:
            logger.warning(f"订单 {order.order_id} 没有成交记录，无法创建可视化")
            return
            
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8), sharex=True)
        
        # 准备数据
        timestamps = [e.timestamp for e in order.executions]
        prices = [float(e.price) for e in order.executions]
        quantities = [float(e.quantity) for e in order.executions]
        cumulative_qty = np.cumsum(quantities)
        
        # 绘制价格图
        ax1.plot(timestamps, prices, 'o-', color='blue')
        ax1.set_title(f"订单执行价格 - {order.symbol} ({order.order_id})")
        ax1.set_ylabel("价格")
        ax1.grid(True)
        
        # 标注VWAP
        if order.avg_fill_price:
            vwap = float(order.avg_fill_price)
            ax1.axhline(y=vwap, color='red', linestyle='--', 
                      label=f'VWAP: {vwap:.4f}')
            ax1.legend()
        
        # 绘制成交量图
        ax2.bar(timestamps, quantities, color='green', alpha=0.6, label='成交量')
        ax2.set_xlabel("时间")
        ax2.set_ylabel("成交量")
        
        # 绘制累计成交量线
        ax3 = ax2.twinx()
        ax3.plot(timestamps, cumulative_qty, 'r-', label='累计成交量')
        ax3.set_ylabel("累计成交量")
        
        # 合并图例
        lines1, labels1 = ax2.get_legend_handles_labels()
        lines2, labels2 = ax3.get_legend_handles_labels()
        ax3.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
        
        plt.tight_layout()
        
        # 保存图表
        if save_path:
            plt.savefig(save_path)
            logger.info(f"订单执行可视化已保存到 {save_path}")
            
        # 显示图表
        if show:
            plt.show()
        else:
            plt.close()
    
    def _analyze_basic_info(self, order: Order, result: OrderAnalysisResult) -> None:
        """分析基本订单信息"""
        result.add_detail("order_type", order.order_type.name)
        result.add_detail("side", order.side.name)
        result.add_detail("status", order.status.name)
        result.add_detail("quantity", str(order.quantity))
        result.add_detail("filled_quantity", str(order.filled_quantity))
        result.add_detail("fill_percentage", order.fill_percentage)
        
        # 添加基本指标
        fill_rate = float(order.filled_quantity / order.quantity) if order.quantity > 0 else 0
        result.add_metric("fill_rate", fill_rate * 100, "订单成交比例(%)")
        
        is_completed = order.status == OrderStatus.FILLED
        result.add_metric("is_completed", is_completed, "订单是否完全成交")
        
    def _analyze_timing(self, order: Order, result: OrderAnalysisResult, 
                       expected_time: Optional[dt.datetime] = None) -> None:
        """分析订单执行时间"""
        if not order.executions:
            return
            
        # 订单提交时间
        submit_time = order.timestamp
        
        # 首次成交时间
        first_exec_time = min(e.timestamp for e in order.executions)
        
        # 最后成交时间
        last_exec_time = max(e.timestamp for e in order.executions)
        
        # 计算指标
        time_to_first_fill = (first_exec_time - submit_time).total_seconds()
        execution_duration = (last_exec_time - first_exec_time).total_seconds()
        total_execution_time = (last_exec_time - submit_time).total_seconds()
        
        # 添加指标
        result.add_metric("time_to_first_fill", time_to_first_fill, 
                        "从下单到首次成交的时间(秒)")
        result.add_metric("execution_duration", execution_duration, 
                        "从首次成交到最后成交的持续时间(秒)")
        result.add_metric("total_execution_time", total_execution_time, 
                        "从下单到最后成交的总时间(秒)")
        
        # 如果有预期完成时间，计算时间偏差
        if expected_time:
            time_deviation = (last_exec_time - expected_time).total_seconds()
            result.add_metric("time_deviation", time_deviation, 
                            "与预期完成时间的偏差(秒，负值表示提前完成)")
    
    def _analyze_price_slippage(self, order: Order, result: OrderAnalysisResult,
                              benchmark_price: Optional[Union[Decimal, float]] = None) -> None:
        """分析价格滑点"""
        if not order.executions or not order.avg_fill_price:
            return
            
        # 如果没有提供基准价格，尝试获取
        if benchmark_price is None and self.market_data_provider:
            try:
                # 这里假设market_data_provider有一个get_price方法
                benchmark_price = self.market_data_provider.get_price(
                    order.symbol, order.timestamp)
            except Exception as e:
                logger.warning(f"获取基准价格失败: {e}")
                # 使用第一笔成交价格作为基准
                benchmark_price = order.executions[0].price
        elif benchmark_price is None:
            # 使用第一笔成交价格作为基准
            benchmark_price = order.executions[0].price
            
        # 确保基准价格是Decimal类型
        if not isinstance(benchmark_price, Decimal):
            benchmark_price = Decimal(str(benchmark_price))
            
        # 计算滑点
        avg_price = order.avg_fill_price
        
        # 根据买卖方向计算滑点
        if order.side == OrderSide.BUY:
            # 买入订单：实际价格高于基准价格是不利的
            slippage_decimal = avg_price - benchmark_price
        else:
            # 卖出订单：实际价格低于基准价格是不利的
            slippage_decimal = benchmark_price - avg_price
            
        # 转换为货币值
        slippage_value = float(slippage_decimal)
        
        # 计算相对滑点(%)
        if benchmark_price > Decimal('0'):
            relative_slippage = float(slippage_decimal / benchmark_price * 100)
        else:
            relative_slippage = 0
            
        # 总滑点成本
        total_slippage_cost = float(slippage_decimal * order.filled_quantity)
        
        # 添加指标
        result.add_metric("benchmark_price", float(benchmark_price), "基准价格")
        result.add_metric("avg_fill_price", float(avg_price), "平均成交价格")
        result.add_metric("price_slippage", slippage_value, "价格滑点(正值表示对交易者不利)")
        result.add_metric("relative_slippage", relative_slippage, "相对滑点(%)")
        result.add_metric("total_slippage_cost", total_slippage_cost, "总滑点成本")
        
        # 价格离散度分析
        if len(order.executions) > 1:
            prices = [float(e.price) for e in order.executions]
            price_variance = np.var(prices)
            price_range = max(prices) - min(prices)
            
            result.add_metric("price_variance", price_variance, "成交价格方差")
            result.add_metric("price_range", price_range, "成交价格范围")
            result.add_metric("price_range_percent", 100 * price_range / float(avg_price), 
                            "成交价格范围占平均价格的百分比")
    
    def _analyze_fill_efficiency(self, order: Order, result: OrderAnalysisResult) -> None:
        """分析成交效率"""
        if not order.executions:
            return
            
        # 计算平均成交大小
        quantities = [float(e.quantity) for e in order.executions]
        avg_execution_size = np.mean(quantities)
        
        # 计算成交分布
        execution_count = len(order.executions)
        
        # 计算分批效率
        if execution_count > 0:
            batch_efficiency = float(order.filled_quantity) / (execution_count * avg_execution_size)
        else:
            batch_efficiency = 0
            
        # 添加指标
        result.add_metric("execution_count", execution_count, "成交次数")
        result.add_metric("avg_execution_size", avg_execution_size, "平均每次成交量")
        result.add_metric("batch_efficiency", batch_efficiency, "分批效率")
        
        # 如果有多次成交，分析成交速率
        if execution_count > 1:
            first_exec_time = min(e.timestamp for e in order.executions)
            last_exec_time = max(e.timestamp for e in order.executions)
            
            duration = (last_exec_time - first_exec_time).total_seconds()
            if duration > 0:
                fill_rate_per_second = float(order.filled_quantity) / duration
                result.add_metric("fill_rate_per_second", fill_rate_per_second, 
                                "每秒成交量")
                
                execs_per_second = execution_count / duration
                result.add_metric("execs_per_second", execs_per_second, 
                                "每秒成交次数")
    
    def _analyze_trading_costs(self, order: Order, result: OrderAnalysisResult) -> None:
        """分析交易成本"""
        # 汇总所有成交的手续费
        total_commission = sum(Decimal(e.commission) for e in order.executions)
        
        # 计算交易额
        if order.avg_fill_price:
            trade_value = order.filled_quantity * order.avg_fill_price
        else:
            trade_value = sum(e.quantity * e.price for e in order.executions)
            
        # 计算相对手续费率
        if trade_value > Decimal('0'):
            commission_rate = float(total_commission / trade_value * 100)
        else:
            commission_rate = 0
            
        # 添加指标
        result.add_metric("total_commission", float(total_commission), "总手续费")
        result.add_metric("trade_value", float(trade_value), "交易金额")
        result.add_metric("commission_rate", commission_rate, "手续费率(%)")
        
        # 如果有滑点数据，计算总交易成本
        if "total_slippage_cost" in result.metrics:
            total_cost = float(total_commission) + result.metrics["total_slippage_cost"]["value"]
            total_cost_rate = 100 * total_cost / float(trade_value) if trade_value > 0 else 0
            
            result.add_metric("total_cost", total_cost, "总交易成本(滑点+手续费)")
            result.add_metric("total_cost_rate", total_cost_rate, "总成本率(%)")
    
    def _analyze_executions(self, order: Order, result: OrderAnalysisResult) -> None:
        """分析订单的多次成交"""
        executions = sorted(order.executions, key=lambda e: e.timestamp)
        
        # 提取时间序列数据
        timestamps = [e.timestamp for e in executions]
        prices = [float(e.price) for e in executions]
        quantities = [float(e.quantity) for e in executions]
        
        # 计算时间间隔
        time_diffs = []
        for i in range(1, len(timestamps)):
            diff_seconds = (timestamps[i] - timestamps[i-1]).total_seconds()
            time_diffs.append(diff_seconds)
            
        # 计算量价相关性
        if len(prices) > 1 and len(quantities) > 1:
            price_qty_correlation = np.corrcoef(prices, quantities)[0, 1]
            result.add_metric("price_qty_correlation", price_qty_correlation, 
                            "价格与成交量的相关性")
            
        # 时间序列统计
        if time_diffs:
            avg_time_between_execs = np.mean(time_diffs)
            max_time_between_execs = np.max(time_diffs)
            
            result.add_metric("avg_time_between_execs", avg_time_between_execs, 
                            "相邻成交之间的平均时间间隔(秒)")
            result.add_metric("max_time_between_execs", max_time_between_execs, 
                            "相邻成交之间的最大时间间隔(秒)")
        
        # 添加详细的执行记录
        execution_details = []
        cumulative_qty = 0
        
        for i, exec_record in enumerate(executions):
            cumulative_qty += float(exec_record.quantity)
            fill_percent = 100 * cumulative_qty / float(order.quantity)
            
            time_from_start = (exec_record.timestamp - executions[0].timestamp).total_seconds()
            
            detail = {
                "execution_id": exec_record.execution_id,
                "seq": i + 1,
                "timestamp": exec_record.timestamp.isoformat(),
                "price": float(exec_record.price),
                "quantity": float(exec_record.quantity),
                "cumulative_quantity": cumulative_qty,
                "fill_percent": fill_percent,
                "time_from_start": time_from_start
            }
            
            execution_details.append(detail)
            
        result.add_detail("execution_details", execution_details)
    
    def _calculate_market_impact(self, orders: List[Order], 
                                market_data: pd.DataFrame) -> Dict[str, Any]:
        """
        计算市场影响
        
        参数:
            orders: 相同交易标的的订单列表
            market_data: 市场数据DataFrame
            
        返回:
            市场影响分析结果
        """
        # 简单实现：计算订单前后的价格变化
        results = {}
        
        for order in orders:
            # 寻找订单提交前的价格
            pre_order_price = None
            post_order_price = None
            
            # 获取订单时间前后的市场数据
            if 'timestamp' in market_data.columns:
                # 假设market_data有timestamp列
                pre_data = market_data[market_data['timestamp'] < order.timestamp]
                post_data = market_data[market_data['timestamp'] > 
                                       max(e.timestamp for e in order.executions)] if order.executions else None
                
                if not pre_data.empty:
                    pre_order_price = pre_data.iloc[-1]['price']
                    
                if post_data is not None and not post_data.empty:
                    post_order_price = post_data.iloc[0]['price']
            
            # 计算市场影响
            if pre_order_price is not None and post_order_price is not None:
                price_change = post_order_price - pre_order_price
                relative_impact = price_change / pre_order_price if pre_order_price > 0 else 0
                
                # 根据买卖方向判断影响是否符合预期
                expected_direction = 1 if order.side == OrderSide.BUY else -1
                actual_direction = 1 if price_change > 0 else (-1 if price_change < 0 else 0)
                
                is_expected_impact = expected_direction == actual_direction
                
                # 记录结果
                results[order.order_id] = {
                    "pre_order_price": pre_order_price,
                    "post_order_price": post_order_price,
                    "price_change": price_change,
                    "relative_impact": relative_impact,
                    "is_expected_impact": is_expected_impact
                }
                
        return results
    
    def _generate_report(self, orders: List[Order], 
                        analysis_results: Dict[str, OrderAnalysisResult]) -> str:
        """
        生成分析报告
        
        参数:
            orders: 订单列表
            analysis_results: 分析结果字典
            
        返回:
            报告文本
        """
        report_lines = [
            "==================================================",
            "              订单执行性能分析报告                ",
            "==================================================",
            f"生成时间: {dt.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"分析订单数: {len(orders)}",
            ""
        ]
        
        # 统计订单信息
        report_lines.extend(self._generate_summary_statistics(orders, analysis_results))
        
        # 详细订单分析
        report_lines.append("\n订单详细分析:\n")
        
        for order_id, result in analysis_results.items():
            report_lines.append("-" * 50)
            report_lines.append(result.summary())
            report_lines.append("")
        
        return "\n".join(report_lines)
    
    def _generate_summary_statistics(self, orders: List[Order], 
                                   analysis_results: Dict[str, OrderAnalysisResult]) -> List[str]:
        """生成汇总统计信息"""
        lines = [
            "\n汇总统计:\n",
            f"总订单数: {len(orders)}"
        ]
        
        # 按状态分类
        status_counts = defaultdict(int)
        for order in orders:
            status_counts[order.status.name] += 1
            
        lines.append("\n订单状态分布:")
        for status, count in status_counts.items():
            percent = 100 * count / len(orders)
            lines.append(f"- {status}: {count} ({percent:.1f}%)")
            
        # 提取关键指标做统计分析
        if analysis_results:
            metric_data = defaultdict(list)
            
            for result in analysis_results.values():
                for metric_name, metric_info in result.metrics.items():
                    value = metric_info["value"]
                    # 只处理数值类型
                    if isinstance(value, (int, float, Decimal)):
                        metric_data[metric_name].append(float(value))
            
            # 计算并展示统计数据
            lines.append("\n关键指标统计(均值):")
            for metric_name, values in metric_data.items():
                if not values:
                    continue
                    
                mean_value = np.mean(values)
                description = next((r.metrics[metric_name]["description"] 
                                  for r in analysis_results.values() 
                                  if metric_name in r.metrics), "")
                
                if abs(mean_value) < 0.01:
                    mean_str = f"{mean_value:.6f}"
                else:
                    mean_str = f"{mean_value:.4f}"
                    
                lines.append(f"- {metric_name}: {mean_str}" + 
                           (f" ({description})" if description else ""))
        
        return lines 