"""
订单模型模块

定义交易系统的各种订单模型和相关数据结构。
该模块提供了市价单、限价单、止损单等多种订单类型的实现。
"""

import datetime as dt
import uuid
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from decimal import Decimal

from trading.interfaces.trading_interface import OrderStatus, OrderType, OrderSide, TimeInForce


@dataclass
class Execution:
    """成交记录"""
    execution_id: str
    order_id: str
    symbol: str
    timestamp: dt.datetime
    price: Decimal
    quantity: Decimal
    commission: Decimal = Decimal('0')
    exchange: str = ""
    
    def __post_init__(self):
        """将数值转换为Decimal类型"""
        if not isinstance(self.price, Decimal):
            self.price = Decimal(str(self.price))
        if not isinstance(self.quantity, Decimal):
            self.quantity = Decimal(str(self.quantity))
        if not isinstance(self.commission, Decimal):
            self.commission = Decimal(str(self.commission))


@dataclass
class Order:
    """订单基类"""
    symbol: str
    side: OrderSide
    quantity: Decimal
    timestamp: dt.datetime = field(default_factory=dt.datetime.now)
    order_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    client_order_id: Optional[str] = None
    status: OrderStatus = OrderStatus.CREATED
    time_in_force: TimeInForce = TimeInForce.DAY
    filled_quantity: Decimal = Decimal('0')
    remaining_quantity: Optional[Decimal] = None
    avg_fill_price: Optional[Decimal] = None
    last_update_time: Optional[dt.datetime] = None
    expiry_time: Optional[dt.datetime] = None
    executions: List[Execution] = field(default_factory=list)
    exchange: str = ""
    account_id: str = ""
    tags: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if not isinstance(self.quantity, Decimal):
            self.quantity = Decimal(str(self.quantity))
        
        if not isinstance(self.filled_quantity, Decimal):
            self.filled_quantity = Decimal(str(self.filled_quantity))
            
        if self.remaining_quantity is None:
            self.remaining_quantity = self.quantity
        elif not isinstance(self.remaining_quantity, Decimal):
            self.remaining_quantity = Decimal(str(self.remaining_quantity))
            
        if self.avg_fill_price is not None and not isinstance(self.avg_fill_price, Decimal):
            self.avg_fill_price = Decimal(str(self.avg_fill_price))
            
    @property
    def order_type(self) -> OrderType:
        """获取订单类型"""
        return self._get_order_type()
    
    def _get_order_type(self) -> OrderType:
        """获取订单类型，子类需重写该方法"""
        raise NotImplementedError("子类必须实现_get_order_type方法")
    
    @property
    def is_filled(self) -> bool:
        """订单是否完全成交"""
        return self.status == OrderStatus.FILLED
    
    @property
    def is_active(self) -> bool:
        """订单是否处于活跃状态"""
        active_statuses = [
            OrderStatus.CREATED,
            OrderStatus.PENDING,
            OrderStatus.PARTIAL_FILLED
        ]
        return self.status in active_statuses
    
    @property
    def is_done(self) -> bool:
        """订单是否已完成（包括成交、取消、拒绝、过期等）"""
        done_statuses = [
            OrderStatus.FILLED,
            OrderStatus.CANCELLED,
            OrderStatus.REJECTED,
            OrderStatus.EXPIRED,
            OrderStatus.ERROR
        ]
        return self.status in done_statuses
    
    @property
    def fill_percentage(self) -> float:
        """获取订单成交百分比"""
        if self.quantity == Decimal('0'):
            return 0.0
        return float(self.filled_quantity / self.quantity * 100)
    
    def add_execution(self, execution: Execution) -> None:
        """
        添加成交记录
        
        参数:
            execution: 成交记录对象
        """
        self.executions.append(execution)
        self.filled_quantity += execution.quantity
        self.remaining_quantity = self.quantity - self.filled_quantity
        
        # 更新平均成交价格
        total_value = Decimal('0')
        total_qty = Decimal('0')
        
        for exec_record in self.executions:
            total_value += exec_record.price * exec_record.quantity
            total_qty += exec_record.quantity
            
        if total_qty > Decimal('0'):
            self.avg_fill_price = total_value / total_qty
            
        # 更新订单状态
        if self.remaining_quantity <= Decimal('0'):
            self.status = OrderStatus.FILLED
        elif self.filled_quantity > Decimal('0'):
            self.status = OrderStatus.PARTIAL_FILLED
            
        self.last_update_time = dt.datetime.now()
    
    def cancel(self) -> bool:
        """
        取消订单
        
        返回:
            是否成功取消
        """
        if self.is_done:
            return False
        
        self.status = OrderStatus.CANCELLED
        self.last_update_time = dt.datetime.now()
        return True
    
    def reject(self, reason: str = "") -> None:
        """
        拒绝订单
        
        参数:
            reason: 拒绝原因
        """
        self.status = OrderStatus.REJECTED
        self.last_update_time = dt.datetime.now()
        self.tags["reject_reason"] = reason
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将订单转换为字典
        
        返回:
            包含订单信息的字典
        """
        result = {
            "order_id": self.order_id,
            "client_order_id": self.client_order_id,
            "symbol": self.symbol,
            "order_type": self.order_type.name,
            "side": self.side.name,
            "quantity": str(self.quantity),
            "status": self.status.name,
            "time_in_force": self.time_in_force.name,
            "filled_quantity": str(self.filled_quantity),
            "remaining_quantity": str(self.remaining_quantity),
            "timestamp": self.timestamp.isoformat(),
            "exchange": self.exchange,
            "account_id": self.account_id
        }
        
        if self.avg_fill_price is not None:
            result["avg_fill_price"] = str(self.avg_fill_price)
            
        if self.last_update_time is not None:
            result["last_update_time"] = self.last_update_time.isoformat()
            
        if self.expiry_time is not None:
            result["expiry_time"] = self.expiry_time.isoformat()
            
        result["executions"] = [
            {
                "execution_id": e.execution_id,
                "price": str(e.price),
                "quantity": str(e.quantity),
                "timestamp": e.timestamp.isoformat(),
                "commission": str(e.commission)
            }
            for e in self.executions
        ]
        
        if self.tags:
            result["tags"] = self.tags
            
        return result
    
    def __str__(self) -> str:
        """返回订单描述"""
        return (
            f"订单 {self.order_id} [{self.order_type.name}]: "
            f"{self.side.name} {self.symbol} {self.quantity} @ {self.avg_fill_price or '未成交'} "
            f"状态: {self.status.name}, 已成交: {self.fill_percentage:.1f}%"
        )


@dataclass
class MarketOrder(Order):
    """市价单"""
    
    def _get_order_type(self) -> OrderType:
        """获取订单类型"""
        return OrderType.MARKET


@dataclass
class LimitOrder(Order):
    """限价单"""
    price: Decimal
    
    def __post_init__(self):
        """初始化后处理"""
        super().__post_init__()
        if not isinstance(self.price, Decimal):
            self.price = Decimal(str(self.price))
    
    def _get_order_type(self) -> OrderType:
        """获取订单类型"""
        return OrderType.LIMIT
    
    def to_dict(self) -> Dict[str, Any]:
        """将订单转换为字典"""
        result = super().to_dict()
        result["price"] = str(self.price)
        return result


@dataclass
class StopOrder(Order):
    """止损单"""
    stop_price: Decimal
    
    def __post_init__(self):
        """初始化后处理"""
        super().__post_init__()
        if not isinstance(self.stop_price, Decimal):
            self.stop_price = Decimal(str(self.stop_price))
    
    def _get_order_type(self) -> OrderType:
        """获取订单类型"""
        return OrderType.STOP
    
    def to_dict(self) -> Dict[str, Any]:
        """将订单转换为字典"""
        result = super().to_dict()
        result["stop_price"] = str(self.stop_price)
        return result


@dataclass
class StopLimitOrder(Order):
    """止损限价单"""
    stop_price: Decimal
    limit_price: Decimal
    
    def __post_init__(self):
        """初始化后处理"""
        super().__post_init__()
        if not isinstance(self.stop_price, Decimal):
            self.stop_price = Decimal(str(self.stop_price))
        if not isinstance(self.limit_price, Decimal):
            self.limit_price = Decimal(str(self.limit_price))
    
    def _get_order_type(self) -> OrderType:
        """获取订单类型"""
        return OrderType.STOP_LIMIT
    
    def to_dict(self) -> Dict[str, Any]:
        """将订单转换为字典"""
        result = super().to_dict()
        result["stop_price"] = str(self.stop_price)
        result["limit_price"] = str(self.limit_price)
        return result


@dataclass
class TrailingStopOrder(Order):
    """追踪止损单"""
    trail_amount: Decimal
    trail_percent: Optional[Decimal] = None
    last_price: Optional[Decimal] = None
    stop_price: Optional[Decimal] = None
    
    def __post_init__(self):
        """初始化后处理"""
        super().__post_init__()
        if not isinstance(self.trail_amount, Decimal):
            self.trail_amount = Decimal(str(self.trail_amount))
        
        if self.trail_percent is not None and not isinstance(self.trail_percent, Decimal):
            self.trail_percent = Decimal(str(self.trail_percent))
            
        if self.last_price is not None and not isinstance(self.last_price, Decimal):
            self.last_price = Decimal(str(self.last_price))
            
        if self.stop_price is not None and not isinstance(self.stop_price, Decimal):
            self.stop_price = Decimal(str(self.stop_price))
    
    def _get_order_type(self) -> OrderType:
        """获取订单类型"""
        return OrderType.TRAILING_STOP
    
    def update_stop_price(self, current_price: Union[Decimal, float, str]) -> None:
        """
        更新追踪止损价格
        
        参数:
            current_price: 当前市场价格
        """
        if not isinstance(current_price, Decimal):
            current_price = Decimal(str(current_price))
            
        # 初始化最后价格和止损价格
        if self.last_price is None:
            self.last_price = current_price
            if self.side == OrderSide.BUY or self.side == OrderSide.BUY_TO_COVER:
                # 买入追踪止损：当价格下跌到一定程度时买入
                if self.trail_percent is not None:
                    self.stop_price = current_price * (Decimal('1') + self.trail_percent / Decimal('100'))
                else:
                    self.stop_price = current_price + self.trail_amount
            else:
                # 卖出追踪止损：当价格上涨到一定程度时卖出
                if self.trail_percent is not None:
                    self.stop_price = current_price * (Decimal('1') - self.trail_percent / Decimal('100'))
                else:
                    self.stop_price = current_price - self.trail_amount
        
        # 根据价格变动更新止损价格
        if self.side == OrderSide.BUY or self.side == OrderSide.BUY_TO_COVER:
            # 如果价格下降，更新止损价格
            if current_price < self.last_price:
                if self.trail_percent is not None:
                    new_stop = current_price * (Decimal('1') + self.trail_percent / Decimal('100'))
                else:
                    new_stop = current_price + self.trail_amount
                
                if new_stop < self.stop_price:
                    self.stop_price = new_stop
        else:
            # 如果价格上升，更新止损价格
            if current_price > self.last_price:
                if self.trail_percent is not None:
                    new_stop = current_price * (Decimal('1') - self.trail_percent / Decimal('100'))
                else:
                    new_stop = current_price - self.trail_amount
                
                if new_stop > self.stop_price:
                    self.stop_price = new_stop
        
        self.last_price = current_price
    
    def is_triggered(self, current_price: Union[Decimal, float, str]) -> bool:
        """
        检查追踪止损是否触发
        
        参数:
            current_price: 当前市场价格
            
        返回:
            是否触发
        """
        if self.stop_price is None:
            return False
            
        if not isinstance(current_price, Decimal):
            current_price = Decimal(str(current_price))
            
        if self.side == OrderSide.BUY or self.side == OrderSide.BUY_TO_COVER:
            # 买入追踪止损：当价格上涨到止损价格以上时触发
            return current_price >= self.stop_price
        else:
            # 卖出追踪止损：当价格下跌到止损价格以下时触发
            return current_price <= self.stop_price
    
    def to_dict(self) -> Dict[str, Any]:
        """将订单转换为字典"""
        result = super().to_dict()
        result["trail_amount"] = str(self.trail_amount)
        
        if self.trail_percent is not None:
            result["trail_percent"] = str(self.trail_percent)
            
        if self.last_price is not None:
            result["last_price"] = str(self.last_price)
            
        if self.stop_price is not None:
            result["stop_price"] = str(self.stop_price)
            
        return result


def create_order(
    order_type: OrderType,
    symbol: str,
    side: OrderSide,
    quantity: Union[Decimal, float, str],
    price: Optional[Union[Decimal, float, str]] = None,
    stop_price: Optional[Union[Decimal, float, str]] = None,
    limit_price: Optional[Union[Decimal, float, str]] = None,
    trail_amount: Optional[Union[Decimal, float, str]] = None,
    trail_percent: Optional[Union[Decimal, float, str]] = None,
    time_in_force: TimeInForce = TimeInForce.DAY,
    **kwargs
) -> Order:
    """
    工厂函数：创建订单对象
    
    参数:
        order_type: 订单类型
        symbol: 证券代码
        side: 交易方向
        quantity: 数量
        price: 价格（限价单）
        stop_price: 止损价格（止损单、止损限价单）
        limit_price: 限价（止损限价单）
        trail_amount: 追踪金额（追踪止损单）
        trail_percent: 追踪百分比（追踪止损单）
        time_in_force: 订单有效期
        **kwargs: 其他参数
        
    返回:
        订单对象
    
    抛出:
        ValueError: 参数无效时
    """
    if not isinstance(quantity, Decimal):
        quantity = Decimal(str(quantity))
    
    common_kwargs = {
        "symbol": symbol,
        "side": side,
        "quantity": quantity,
        "time_in_force": time_in_force,
        **kwargs
    }
    
    if order_type == OrderType.MARKET:
        return MarketOrder(**common_kwargs)
    
    elif order_type == OrderType.LIMIT:
        if price is None:
            raise ValueError("限价单必须指定价格")
        if not isinstance(price, Decimal):
            price = Decimal(str(price))
        return LimitOrder(price=price, **common_kwargs)
    
    elif order_type == OrderType.STOP:
        if stop_price is None:
            raise ValueError("止损单必须指定止损价格")
        if not isinstance(stop_price, Decimal):
            stop_price = Decimal(str(stop_price))
        return StopOrder(stop_price=stop_price, **common_kwargs)
    
    elif order_type == OrderType.STOP_LIMIT:
        if stop_price is None:
            raise ValueError("止损限价单必须指定止损价格")
        if limit_price is None:
            raise ValueError("止损限价单必须指定限价")
        if not isinstance(stop_price, Decimal):
            stop_price = Decimal(str(stop_price))
        if not isinstance(limit_price, Decimal):
            limit_price = Decimal(str(limit_price))
        return StopLimitOrder(
            stop_price=stop_price,
            limit_price=limit_price,
            **common_kwargs
        )
    
    elif order_type == OrderType.TRAILING_STOP:
        if trail_amount is None and trail_percent is None:
            raise ValueError("追踪止损单必须指定追踪金额或追踪百分比")
        
        kwargs = {}
        if trail_amount is not None:
            if not isinstance(trail_amount, Decimal):
                trail_amount = Decimal(str(trail_amount))
            kwargs["trail_amount"] = trail_amount
        else:
            kwargs["trail_amount"] = Decimal('0')
            
        if trail_percent is not None:
            if not isinstance(trail_percent, Decimal):
                trail_percent = Decimal(str(trail_percent))
            kwargs["trail_percent"] = trail_percent
            
        return TrailingStopOrder(**kwargs, **common_kwargs)
    
    else:
        raise ValueError(f"不支持的订单类型: {order_type}") 