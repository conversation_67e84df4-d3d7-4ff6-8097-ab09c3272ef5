"""
订单管理器模块

提供订单管理和跟踪功能，负责订单的创建、跟踪、更新和查询。
"""

import datetime as dt
import logging
from typing import Dict, List, Optional, Any, Callable, Union, Set
from decimal import Decimal
import threading
import uuid
import json

from trading.interfaces.trading_interface import (
    OrderStatus, OrderType, OrderSide, TimeInForce, TradingException
)
from trading.order.order_models import (
    Order, Execution, create_order, MarketOrder, LimitOrder, 
    StopOrder, StopLimitOrder, TrailingStopOrder
)


logger = logging.getLogger(__name__)


class OrderManagerException(TradingException):
    """订单管理器异常"""
    pass


class OrderValidationException(OrderManagerException):
    """订单验证异常"""
    pass


class OrderManager:
    """
    订单管理器
    
    负责订单的创建、跟踪、更新和查询。
    提供订单状态更新的回调机制。
    """
    
    def __init__(self):
        """初始化订单管理器"""
        self._orders: Dict[str, Order] = {}  # 订单ID -> 订单对象
        self._orders_by_client_id: Dict[str, Order] = {}  # 客户端订单ID -> 订单对象
        self._symbol_orders: Dict[str, Set[str]] = {}  # 证券代码 -> 订单ID集合
        self._active_orders: Set[str] = set()  # 活跃订单ID集合
        self._order_callbacks: Dict[str, List[Callable[[Order], None]]] = {}  # 订单ID -> 回调函数列表
        self._global_callbacks: List[Callable[[Order], None]] = []  # 全局回调函数列表
        self._lock = threading.RLock()  # 用于线程安全
        self._order_history: Dict[str, List[Dict[str, Any]]] = {}  # 订单ID -> 历史状态列表
        
    def create_order(
        self,
        order_type: OrderType,
        symbol: str,
        side: OrderSide,
        quantity: Union[Decimal, float, str],
        price: Optional[Union[Decimal, float, str]] = None,
        stop_price: Optional[Union[Decimal, float, str]] = None,
        limit_price: Optional[Union[Decimal, float, str]] = None,
        trail_amount: Optional[Union[Decimal, float, str]] = None,
        trail_percent: Optional[Union[Decimal, float, str]] = None,
        time_in_force: TimeInForce = TimeInForce.DAY,
        client_order_id: Optional[str] = None,
        **kwargs
    ) -> Order:
        """
        创建订单
        
        参数:
            order_type: 订单类型
            symbol: 证券代码
            side: 交易方向
            quantity: 数量
            price: 价格（限价单）
            stop_price: 止损价格（止损单、止损限价单）
            limit_price: 限价（止损限价单）
            trail_amount: 追踪金额（追踪止损单）
            trail_percent: 追踪百分比（追踪止损单）
            time_in_force: 订单有效期
            client_order_id: 客户端订单ID
            **kwargs: 其他参数
            
        返回:
            订单对象
            
        抛出:
            OrderValidationException: 订单验证失败时
        """
        with self._lock:
            # 验证订单
            self._validate_order_params(
                order_type, symbol, side, quantity, price, 
                stop_price, limit_price, trail_amount, trail_percent
            )
            
            # 确保客户端订单ID唯一
            if client_order_id is not None and client_order_id in self._orders_by_client_id:
                raise OrderValidationException(f"客户端订单ID已存在: {client_order_id}")
                
            # 创建订单
            try:
                order = create_order(
                    order_type=order_type,
                    symbol=symbol,
                    side=side,
                    quantity=quantity,
                    price=price,
                    stop_price=stop_price,
                    limit_price=limit_price,
                    trail_amount=trail_amount,
                    trail_percent=trail_percent,
                    time_in_force=time_in_force,
                    client_order_id=client_order_id,
                    **kwargs
                )
            except ValueError as e:
                raise OrderValidationException(str(e))
            
            # 添加到管理器
            self._orders[order.order_id] = order
            
            if client_order_id is not None:
                self._orders_by_client_id[client_order_id] = order
                
            if symbol not in self._symbol_orders:
                self._symbol_orders[symbol] = set()
            self._symbol_orders[symbol].add(order.order_id)
            
            if order.is_active:
                self._active_orders.add(order.order_id)
                
            # 记录订单状态历史
            self._record_order_history(order)
            
            # 触发回调
            self._trigger_callbacks(order)
            
            logger.info(f"创建订单: {order}")
            return order
            
    def get_order(self, order_id: str) -> Optional[Order]:
        """
        通过订单ID获取订单
        
        参数:
            order_id: 订单ID
            
        返回:
            订单对象，如果不存在则返回None
        """
        with self._lock:
            return self._orders.get(order_id)
            
    def get_order_by_client_id(self, client_order_id: str) -> Optional[Order]:
        """
        通过客户端订单ID获取订单
        
        参数:
            client_order_id: 客户端订单ID
            
        返回:
            订单对象，如果不存在则返回None
        """
        with self._lock:
            return self._orders_by_client_id.get(client_order_id)
            
    def get_orders(
        self,
        symbol: Optional[str] = None,
        status: Optional[OrderStatus] = None,
        side: Optional[OrderSide] = None,
        order_type: Optional[OrderType] = None,
        active_only: bool = False
    ) -> List[Order]:
        """
        获取订单列表
        
        参数:
            symbol: 证券代码过滤
            status: 订单状态过滤
            side: 交易方向过滤
            order_type: 订单类型过滤
            active_only: 是否只返回活跃订单
            
        返回:
            订单列表
        """
        with self._lock:
            # 基于条件过滤订单
            result = []
            
            # 确定基础订单集合
            if active_only:
                order_ids = list(self._active_orders)
            elif symbol is not None:
                order_ids = list(self._symbol_orders.get(symbol, set()))
            else:
                order_ids = list(self._orders.keys())
                
            # 应用过滤条件
            for order_id in order_ids:
                order = self._orders.get(order_id)
                if order is None:
                    continue
                    
                if symbol is not None and order.symbol != symbol:
                    continue
                    
                if status is not None and order.status != status:
                    continue
                    
                if side is not None and order.side != side:
                    continue
                    
                if order_type is not None and order.order_type != order_type:
                    continue
                    
                result.append(order)
                
            return result
            
    def update_order_status(
        self, 
        order_id: str, 
        status: OrderStatus,
        reason: str = ""
    ) -> Optional[Order]:
        """
        更新订单状态
        
        参数:
            order_id: 订单ID
            status: 新状态
            reason: 状态变更原因
            
        返回:
            更新后的订单，如果订单不存在则返回None
        """
        with self._lock:
            order = self._orders.get(order_id)
            if order is None:
                logger.warning(f"尝试更新不存在的订单状态: {order_id}")
                return None
                
            old_status = order.status
            order.status = status
            order.last_update_time = dt.datetime.now()
            
            if reason:
                order.tags["status_change_reason"] = reason
                
            # 更新活跃订单集合
            if old_status != status:
                if status in [
                    OrderStatus.FILLED, 
                    OrderStatus.CANCELLED, 
                    OrderStatus.REJECTED, 
                    OrderStatus.EXPIRED,
                    OrderStatus.ERROR
                ]:
                    if order_id in self._active_orders:
                        self._active_orders.remove(order_id)
                else:
                    if status == OrderStatus.CREATED or status == OrderStatus.PENDING or status == OrderStatus.PARTIAL_FILLED:
                        self._active_orders.add(order_id)
            
            # 记录订单状态历史
            self._record_order_history(order)
            
            # 触发回调
            self._trigger_callbacks(order)
            
            logger.info(f"订单状态更新: {order_id} {old_status.name} -> {status.name}")
            return order
            
    def add_execution(
        self,
        order_id: str,
        execution_id: str,
        price: Union[Decimal, float, str],
        quantity: Union[Decimal, float, str],
        timestamp: Optional[dt.datetime] = None,
        commission: Union[Decimal, float, str] = 0,
        exchange: str = ""
    ) -> Optional[Order]:
        """
        为订单添加成交记录
        
        参数:
            order_id: 订单ID
            execution_id: 成交ID
            price: 成交价格
            quantity: 成交数量
            timestamp: 成交时间
            commission: 手续费
            exchange: 交易所
            
        返回:
            更新后的订单，如果订单不存在则返回None
        """
        with self._lock:
            order = self._orders.get(order_id)
            if order is None:
                logger.warning(f"尝试为不存在的订单添加成交记录: {order_id}")
                return None
                
            if not timestamp:
                timestamp = dt.datetime.now()
                
            execution = Execution(
                execution_id=execution_id,
                order_id=order_id,
                symbol=order.symbol,
                timestamp=timestamp,
                price=price,
                quantity=quantity,
                commission=commission,
                exchange=exchange
            )
            
            order.add_execution(execution)
            
            # 记录订单状态历史
            self._record_order_history(order)
            
            # 触发回调
            self._trigger_callbacks(order)
            
            logger.info(f"添加成交记录: {order_id} 价格: {price} 数量: {quantity}")
            return order
            
    def cancel_order(self, order_id: str, reason: str = "") -> bool:
        """
        取消订单
        
        参数:
            order_id: 订单ID
            reason: 取消原因
            
        返回:
            是否成功取消
        """
        with self._lock:
            order = self._orders.get(order_id)
            if order is None:
                logger.warning(f"尝试取消不存在的订单: {order_id}")
                return False
                
            if not order.is_active:
                logger.warning(f"尝试取消非活跃订单: {order_id} 状态: {order.status.name}")
                return False
                
            success = order.cancel()
            if success:
                if reason:
                    order.tags["cancel_reason"] = reason
                    
                if order_id in self._active_orders:
                    self._active_orders.remove(order_id)
                    
                # 记录订单状态历史
                self._record_order_history(order)
                
                # 触发回调
                self._trigger_callbacks(order)
                
                logger.info(f"订单已取消: {order_id}")
                
            return success
            
    def reject_order(self, order_id: str, reason: str = "") -> Optional[Order]:
        """
        拒绝订单
        
        参数:
            order_id: 订单ID
            reason: 拒绝原因
            
        返回:
            更新后的订单，如果订单不存在则返回None
        """
        with self._lock:
            order = self._orders.get(order_id)
            if order is None:
                logger.warning(f"尝试拒绝不存在的订单: {order_id}")
                return None
                
            order.reject(reason)
            
            if order_id in self._active_orders:
                self._active_orders.remove(order_id)
                
            # 记录订单状态历史
            self._record_order_history(order)
            
            # 触发回调
            self._trigger_callbacks(order)
            
            logger.info(f"订单已拒绝: {order_id} 原因: {reason}")
            return order
            
    def expire_order(self, order_id: str) -> Optional[Order]:
        """
        使订单过期
        
        参数:
            order_id: 订单ID
            
        返回:
            更新后的订单，如果订单不存在则返回None
        """
        with self._lock:
            order = self._orders.get(order_id)
            if order is None:
                logger.warning(f"尝试使不存在的订单过期: {order_id}")
                return None
                
            if not order.is_active:
                logger.warning(f"尝试使非活跃订单过期: {order_id} 状态: {order.status.name}")
                return None
                
            order.status = OrderStatus.EXPIRED
            order.last_update_time = dt.datetime.now()
            
            if order_id in self._active_orders:
                self._active_orders.remove(order_id)
                
            # 记录订单状态历史
            self._record_order_history(order)
            
            # 触发回调
            self._trigger_callbacks(order)
            
            logger.info(f"订单已过期: {order_id}")
            return order
            
    def clear_inactive_orders(self, before_time: Optional[dt.datetime] = None) -> int:
        """
        清除非活跃订单
        
        参数:
            before_time: 指定时间之前的订单才会被清除
            
        返回:
            清除的订单数量
        """
        with self._lock:
            count = 0
            to_remove = []
            
            for order_id, order in self._orders.items():
                if order.is_active:
                    continue
                    
                if before_time is not None and order.last_update_time is not None:
                    if order.last_update_time > before_time:
                        continue
                        
                to_remove.append(order_id)
                
            for order_id in to_remove:
                order = self._orders.pop(order_id, None)
                if order is None:
                    continue
                    
                # 清除相关的引用
                if order.client_order_id is not None:
                    self._orders_by_client_id.pop(order.client_order_id, None)
                    
                if order.symbol in self._symbol_orders:
                    if order_id in self._symbol_orders[order.symbol]:
                        self._symbol_orders[order.symbol].remove(order_id)
                        
                if order_id in self._active_orders:
                    self._active_orders.remove(order_id)
                    
                # 存储历史记录但移除回调
                self._order_callbacks.pop(order_id, None)
                
                count += 1
                
            if count > 0:
                logger.info(f"清除了 {count} 个非活跃订单")
                
            return count
            
    def add_order_callback(self, order_id: str, callback: Callable[[Order], None]) -> bool:
        """
        为特定订单添加回调函数
        
        参数:
            order_id: 订单ID
            callback: 回调函数
            
        返回:
            是否成功添加
        """
        with self._lock:
            if order_id not in self._orders:
                logger.warning(f"尝试为不存在的订单添加回调: {order_id}")
                return False
                
            if order_id not in self._order_callbacks:
                self._order_callbacks[order_id] = []
                
            self._order_callbacks[order_id].append(callback)
            return True
            
    def add_global_callback(self, callback: Callable[[Order], None]) -> None:
        """
        添加全局订单回调函数
        
        参数:
            callback: 回调函数
        """
        with self._lock:
            self._global_callbacks.append(callback)
            
    def remove_order_callback(self, order_id: str, callback: Callable[[Order], None]) -> bool:
        """
        移除特定订单的回调函数
        
        参数:
            order_id: 订单ID
            callback: 回调函数
            
        返回:
            是否成功移除
        """
        with self._lock:
            if order_id not in self._order_callbacks:
                return False
                
            try:
                self._order_callbacks[order_id].remove(callback)
                return True
            except ValueError:
                return False
                
    def remove_global_callback(self, callback: Callable[[Order], None]) -> bool:
        """
        移除全局回调函数
        
        参数:
            callback: 回调函数
            
        返回:
            是否成功移除
        """
        with self._lock:
            try:
                self._global_callbacks.remove(callback)
                return True
            except ValueError:
                return False
                
    def get_order_history(self, order_id: str) -> List[Dict[str, Any]]:
        """
        获取订单历史状态记录
        
        参数:
            order_id: 订单ID
            
        返回:
            订单历史状态记录列表
        """
        with self._lock:
            return self._order_history.get(order_id, []).copy()
            
    def export_orders(self, active_only: bool = False) -> List[Dict[str, Any]]:
        """
        导出订单信息
        
        参数:
            active_only: 是否只导出活跃订单
            
        返回:
            订单信息列表
        """
        with self._lock:
            result = []
            
            order_ids = list(self._active_orders) if active_only else list(self._orders.keys())
            
            for order_id in order_ids:
                order = self._orders.get(order_id)
                if order is not None:
                    result.append(order.to_dict())
                    
            return result
            
    def _validate_order_params(
        self,
        order_type: OrderType,
        symbol: str,
        side: OrderSide,
        quantity: Union[Decimal, float, str],
        price: Optional[Union[Decimal, float, str]] = None,
        stop_price: Optional[Union[Decimal, float, str]] = None,
        limit_price: Optional[Union[Decimal, float, str]] = None,
        trail_amount: Optional[Union[Decimal, float, str]] = None,
        trail_percent: Optional[Union[Decimal, float, str]] = None
    ) -> None:
        """
        验证订单参数
        
        参数:
            order_type: 订单类型
            symbol: 证券代码
            side: 交易方向
            quantity: 数量
            price: 价格（限价单）
            stop_price: 止损价格（止损单、止损限价单）
            limit_price: 限价（止损限价单）
            trail_amount: 追踪金额（追踪止损单）
            trail_percent: 追踪百分比（追踪止损单）
            
        抛出:
            OrderValidationException: 参数无效时
        """
        if not symbol:
            raise OrderValidationException("证券代码不能为空")
            
        # 将数量转换为Decimal进行验证
        try:
            qty = Decimal(str(quantity))
            if qty <= Decimal('0'):
                raise OrderValidationException("数量必须大于0")
        except Exception as e:
            raise OrderValidationException(f"无效的数量: {quantity}, 错误: {str(e)}")
            
        # 根据订单类型验证参数
        if order_type == OrderType.LIMIT:
            if price is None:
                raise OrderValidationException("限价单必须指定价格")
            try:
                p = Decimal(str(price))
                if p <= Decimal('0'):
                    raise OrderValidationException("价格必须大于0")
            except Exception as e:
                raise OrderValidationException(f"无效的价格: {price}, 错误: {str(e)}")
                
        elif order_type == OrderType.STOP:
            if stop_price is None:
                raise OrderValidationException("止损单必须指定止损价格")
            try:
                sp = Decimal(str(stop_price))
                if sp <= Decimal('0'):
                    raise OrderValidationException("止损价格必须大于0")
            except Exception as e:
                raise OrderValidationException(f"无效的止损价格: {stop_price}, 错误: {str(e)}")
                
        elif order_type == OrderType.STOP_LIMIT:
            if stop_price is None:
                raise OrderValidationException("止损限价单必须指定止损价格")
            if limit_price is None:
                raise OrderValidationException("止损限价单必须指定限价")
            try:
                sp = Decimal(str(stop_price))
                lp = Decimal(str(limit_price))
                if sp <= Decimal('0'):
                    raise OrderValidationException("止损价格必须大于0")
                if lp <= Decimal('0'):
                    raise OrderValidationException("限价必须大于0")
            except Exception as e:
                raise OrderValidationException(f"无效的价格参数, 错误: {str(e)}")
                
        elif order_type == OrderType.TRAILING_STOP:
            if trail_amount is None and trail_percent is None:
                raise OrderValidationException("追踪止损单必须指定追踪金额或追踪百分比")
            try:
                if trail_amount is not None:
                    ta = Decimal(str(trail_amount))
                    if ta <= Decimal('0'):
                        raise OrderValidationException("追踪金额必须大于0")
                if trail_percent is not None:
                    tp = Decimal(str(trail_percent))
                    if tp <= Decimal('0'):
                        raise OrderValidationException("追踪百分比必须大于0")
            except Exception as e:
                raise OrderValidationException(f"无效的追踪参数, 错误: {str(e)}")
                
    def _record_order_history(self, order: Order) -> None:
        """
        记录订单状态历史
        
        参数:
            order: 订单对象
        """
        if order.order_id not in self._order_history:
            self._order_history[order.order_id] = []
            
        snapshot = order.to_dict()
        snapshot["timestamp"] = dt.datetime.now().isoformat()
        
        self._order_history[order.order_id].append(snapshot)
        
    def _trigger_callbacks(self, order: Order) -> None:
        """
        触发订单回调
        
        参数:
            order: 订单对象
        """
        # 触发订单特定回调
        if order.order_id in self._order_callbacks:
            for callback in self._order_callbacks[order.order_id]:
                try:
                    callback(order)
                except Exception as e:
                    logger.error(f"订单回调异常: {e}", exc_info=True)
                    
        # 触发全局回调
        for callback in self._global_callbacks:
            try:
                callback(order)
            except Exception as e:
                logger.error(f"全局订单回调异常: {e}", exc_info=True)
                
    def __len__(self) -> int:
        """返回管理的订单数量"""
        with self._lock:
            return len(self._orders)
            
    def __contains__(self, order_id: str) -> bool:
        """检查是否包含指定订单ID"""
        with self._lock:
            return order_id in self._orders 