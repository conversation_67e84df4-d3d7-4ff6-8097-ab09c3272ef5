"""
投资组合优化与分析工具包

该包提供了一系列投资组合优化算法和分析工具，
用于构建和分析不同类型的投资组合策略。
"""

# 版本信息
__version__ = '0.1.0'

# 从组合优化模块导入组件
from src.portfolio.optimization import (
    PortfolioOptimizerInterface,
    PortfolioOptimizationException,
    OptimizerFactory,
    create_optimizer,
    MeanVarianceOptimizer,
    RiskParityOptimizer,
    MaxDiversificationOptimizer,
    HierarchicalRiskParityOptimizer,
    
    # 约束相关组件
    ConstraintInterface,
    BaseConstraint,
    OptimizationConstraintException,
    WeightSumConstraint,
    WeightBoundsConstraint,
    GroupWeightConstraint,
    MaximumVolatilityConstraint,
    TargetVolatilityConstraint,
    FactorExposureConstraint,
    MinimumReturnConstraint,
    ConstraintManager
)

# 导入分析工具
from src.portfolio.utils.analysis.portfolio_analyzer import PortfolioAnalyzer
from src.portfolio.utils.analysis.visualization import (
    plot_efficient_frontier,
    plot_portfolio_weights,
    plot_risk_contribution,
    plot_performance_metrics,
    plot_correlation_matrix,
    plot_portfolio_comparison,
    plot_risk_return_scatter
)

__all__ = [
    # 组合优化组件
    "PortfolioOptimizerInterface",
    "PortfolioOptimizationException",
    "OptimizerFactory",
    "create_optimizer",
    "MeanVarianceOptimizer",
    "RiskParityOptimizer",
    "MaxDiversificationOptimizer",
    "HierarchicalRiskParityOptimizer",
    
    # 约束相关组件
    "ConstraintInterface",
    "BaseConstraint",
    "OptimizationConstraintException",
    "WeightSumConstraint",
    "WeightBoundsConstraint",
    "GroupWeightConstraint",
    "MaximumVolatilityConstraint",
    "TargetVolatilityConstraint",
    "FactorExposureConstraint",
    "MinimumReturnConstraint",
    "ConstraintManager",
    
    # 分析工具
    'PortfolioAnalyzer',
    'plot_efficient_frontier',
    'plot_portfolio_weights',
    'plot_risk_contribution',
    'plot_performance_metrics',
    'plot_correlation_matrix',
    'plot_portfolio_comparison',
    'plot_risk_return_scatter'
] 