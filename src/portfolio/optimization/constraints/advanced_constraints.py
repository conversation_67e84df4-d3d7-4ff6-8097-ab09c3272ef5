"""
高级约束模块

实现更复杂的优化约束，如风险约束、因子暴露约束等。
"""
from typing import Dict, List, Optional, Union, Any, Tuple
import numpy as np
import pandas as pd

from src.portfolio.optimization.constraints.interfaces import BaseConstraint, OptimizationConstraintException


class MaximumVolatilityConstraint(BaseConstraint):
    """
    最大波动率约束
    
    确保投资组合波动率不超过指定值
    """
    
    def __init__(
        self,
        name: str = "最大波动率约束",
        description: str = "确保投资组合波动率不超过指定值",
        max_volatility: float = 0.2,  # 默认20%年化波动率上限
        **kwargs
    ):
        """
        初始化最大波动率约束
        
        参数:
            name: 约束名称
            description: 约束描述
            max_volatility: 最大允许的投资组合波动率
            **kwargs: 其他参数
        """
        super().__init__(name, description, max_volatility=max_volatility, **kwargs)
    
    def get_constraint(
        self,
        asset_names: List[str] = None,
        covariance_matrix: pd.DataFrame = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成最大波动率约束
        
        参数:
            asset_names: 资产名称列表
            covariance_matrix: 协方差矩阵，必须提供
            **kwargs: 其他参数
            
        返回:
            约束字典
        """
        # 准备资产索引
        self._prepare_assets(asset_names)
        
        # 验证协方差矩阵
        if covariance_matrix is None:
            raise OptimizationConstraintException("必须提供协方差矩阵")
        
        # 确保协方差矩阵的索引与资产名称匹配
        if not all(asset in covariance_matrix.index for asset in self._asset_names):
            raise OptimizationConstraintException("协方差矩阵索引与资产名称不匹配")
        
        # 获取按照资产名称列表排序的协方差矩阵
        cov_matrix = covariance_matrix.loc[self._asset_names, self._asset_names].values
        
        # 获取最大波动率
        max_volatility = self.get_param('max_volatility')
        
        # 创建约束函数：波动率 <= max_volatility
        def volatility_constraint(weights):
            # 计算组合方差
            portfolio_variance = weights.T @ cov_matrix @ weights
            # 计算组合波动率
            portfolio_volatility = np.sqrt(portfolio_variance)
            # 返回约束条件：max_volatility - portfolio_volatility >= 0
            return max_volatility - portfolio_volatility
        
        # 返回不等式约束
        return {
            'type': 'ineq',
            'fun': volatility_constraint
        }


class TargetVolatilityConstraint(BaseConstraint):
    """
    目标波动率约束
    
    确保投资组合波动率接近指定值
    """
    
    def __init__(
        self,
        name: str = "目标波动率约束",
        description: str = "确保投资组合波动率接近指定值",
        target_volatility: float = 0.15,  # 默认15%年化波动率目标
        **kwargs
    ):
        """
        初始化目标波动率约束
        
        参数:
            name: 约束名称
            description: 约束描述
            target_volatility: 目标投资组合波动率
            **kwargs: 其他参数
        """
        super().__init__(name, description, target_volatility=target_volatility, **kwargs)
    
    def get_constraint(
        self,
        asset_names: List[str] = None,
        covariance_matrix: pd.DataFrame = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成目标波动率约束
        
        参数:
            asset_names: 资产名称列表
            covariance_matrix: 协方差矩阵，必须提供
            **kwargs: 其他参数
            
        返回:
            约束字典
        """
        # 准备资产索引
        self._prepare_assets(asset_names)
        
        # 验证协方差矩阵
        if covariance_matrix is None:
            raise OptimizationConstraintException("必须提供协方差矩阵")
        
        # 确保协方差矩阵的索引与资产名称匹配
        if not all(asset in covariance_matrix.index for asset in self._asset_names):
            raise OptimizationConstraintException("协方差矩阵索引与资产名称不匹配")
        
        # 获取按照资产名称列表排序的协方差矩阵
        cov_matrix = covariance_matrix.loc[self._asset_names, self._asset_names].values
        
        # 获取目标波动率
        target_volatility = self.get_param('target_volatility')
        
        # 创建约束函数：波动率 = target_volatility
        def volatility_constraint(weights):
            # 计算组合方差
            portfolio_variance = weights.T @ cov_matrix @ weights
            # 计算组合波动率
            portfolio_volatility = np.sqrt(portfolio_variance)
            # 返回约束条件：portfolio_volatility - target_volatility = 0
            return portfolio_volatility - target_volatility
        
        # 返回等式约束
        return {
            'type': 'eq',
            'fun': volatility_constraint
        }


class FactorExposureConstraint(BaseConstraint):
    """
    因子暴露约束
    
    确保投资组合对特定因子的暴露在指定范围内
    """
    
    def __init__(
        self,
        name: str = "因子暴露约束",
        description: str = "确保投资组合对特定因子的暴露在指定范围内",
        factor_exposures: pd.DataFrame = None,
        factor_lower_bounds: Dict[str, float] = None,
        factor_upper_bounds: Dict[str, float] = None,
        **kwargs
    ):
        """
        初始化因子暴露约束
        
        参数:
            name: 约束名称
            description: 约束描述
            factor_exposures: 因子暴露矩阵，每行是一个资产，每列是一个因子
            factor_lower_bounds: 因子暴露下限，格式为 {因子名称: 下限}
            factor_upper_bounds: 因子暴露上限，格式为 {因子名称: 上限}
            **kwargs: 其他参数
        """
        super().__init__(
            name, 
            description, 
            factor_exposures=factor_exposures,
            factor_lower_bounds=factor_lower_bounds or {},
            factor_upper_bounds=factor_upper_bounds or {},
            **kwargs
        )
    
    def get_constraint(
        self,
        asset_names: List[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成因子暴露约束
        
        参数:
            asset_names: 资产名称列表
            **kwargs: 其他参数
            
        返回:
            约束字典列表
        """
        # 准备资产索引
        self._prepare_assets(asset_names)
        
        # 获取因子暴露矩阵和边界
        factor_exposures = self.get_param('factor_exposures')
        factor_lower_bounds = self.get_param('factor_lower_bounds', {})
        factor_upper_bounds = self.get_param('factor_upper_bounds', {})
        
        # 验证因子暴露矩阵
        if factor_exposures is None:
            raise OptimizationConstraintException("必须提供因子暴露矩阵")
        
        # 确保因子暴露矩阵的索引与资产名称匹配
        if not all(asset in factor_exposures.index for asset in self._asset_names):
            raise OptimizationConstraintException("因子暴露矩阵索引与资产名称不匹配")
        
        # 获取按照资产名称列表排序的因子暴露矩阵
        factor_matrix = factor_exposures.loc[self._asset_names]
        
        # 创建约束列表
        constraints = []
        
        # 添加下限约束
        for factor, lower_bound in factor_lower_bounds.items():
            if factor in factor_matrix.columns:
                factor_values = factor_matrix[factor].values
                
                def factor_lower_constraint(weights, factor_values=factor_values, bound=lower_bound):
                    # 计算组合因子暴露
                    portfolio_exposure = np.dot(weights, factor_values)
                    # 返回约束条件：portfolio_exposure - lower_bound >= 0
                    return portfolio_exposure - bound
                
                constraints.append({
                    'type': 'ineq',
                    'fun': factor_lower_constraint
                })
        
        # 添加上限约束
        for factor, upper_bound in factor_upper_bounds.items():
            if factor in factor_matrix.columns:
                factor_values = factor_matrix[factor].values
                
                def factor_upper_constraint(weights, factor_values=factor_values, bound=upper_bound):
                    # 计算组合因子暴露
                    portfolio_exposure = np.dot(weights, factor_values)
                    # 返回约束条件：upper_bound - portfolio_exposure >= 0
                    return bound - portfolio_exposure
                
                constraints.append({
                    'type': 'ineq',
                    'fun': factor_upper_constraint
                })
        
        # 返回约束列表
        return {
            'constraints': constraints
        }


class MinimumReturnConstraint(BaseConstraint):
    """
    最小收益率约束
    
    确保投资组合预期收益率不低于指定值
    """
    
    def __init__(
        self,
        name: str = "最小收益率约束",
        description: str = "确保投资组合预期收益率不低于指定值",
        min_return: float = 0.05,  # 默认5%的最小年化收益率
        **kwargs
    ):
        """
        初始化最小收益率约束
        
        参数:
            name: 约束名称
            description: 约束描述
            min_return: 最小允许的投资组合预期收益率
            **kwargs: 其他参数
        """
        super().__init__(name, description, min_return=min_return, **kwargs)
    
    def get_constraint(
        self,
        asset_names: List[str] = None,
        expected_returns: pd.Series = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成最小收益率约束
        
        参数:
            asset_names: 资产名称列表
            expected_returns: 预期收益率，必须提供
            **kwargs: 其他参数
            
        返回:
            约束字典
        """
        # 准备资产索引
        self._prepare_assets(asset_names)
        
        # 验证预期收益率
        if expected_returns is None:
            raise OptimizationConstraintException("必须提供预期收益率")
        
        # 确保预期收益率的索引与资产名称匹配
        if not all(asset in expected_returns.index for asset in self._asset_names):
            raise OptimizationConstraintException("预期收益率索引与资产名称不匹配")
        
        # 获取按照资产名称列表排序的预期收益率
        returns = expected_returns.loc[self._asset_names].values
        
        # 获取最小收益率
        min_return = self.get_param('min_return')
        
        # 创建约束函数：收益率 >= min_return
        def return_constraint(weights):
            # 计算组合预期收益率
            portfolio_return = np.dot(weights, returns)
            # 返回约束条件：portfolio_return - min_return >= 0
            return portfolio_return - min_return
        
        # 返回不等式约束
        return {
            'type': 'ineq',
            'fun': return_constraint
        } 