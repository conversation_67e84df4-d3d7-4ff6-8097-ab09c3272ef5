"""
约束管理器模块

提供管理和组合多个约束的功能。
"""
from typing import Dict, List, Optional, Union, Any, Tuple
import numpy as np
import pandas as pd
import logging

from src.portfolio.optimization.constraints.interfaces import BaseConstraint, ConstraintInterface, OptimizationConstraintException

# 配置日志
logger = logging.getLogger(__name__)


class ConstraintManager:
    """
    约束管理器
    
    管理和组合多个优化约束
    """
    
    def __init__(self, name: str = "约束管理器"):
        """
        初始化约束管理器
        
        参数:
            name: 管理器名称
        """
        self._name = name
        self._constraints = {}
        self._enabled_constraints = set()
    
    def add_constraint(self, constraint_id: str, constraint: ConstraintInterface) -> None:
        """
        添加约束
        
        参数:
            constraint_id: 约束ID，用于引用此约束
            constraint: 约束对象
        """
        if constraint_id in self._constraints:
            logger.warning(f"约束ID '{constraint_id}' 已存在，将被覆盖")
        
        self._constraints[constraint_id] = constraint
        self._enabled_constraints.add(constraint_id)
        logger.debug(f"已添加约束: {constraint_id}")
    
    def remove_constraint(self, constraint_id: str) -> None:
        """
        移除约束
        
        参数:
            constraint_id: 约束ID
        """
        if constraint_id in self._constraints:
            del self._constraints[constraint_id]
            self._enabled_constraints.discard(constraint_id)
            logger.debug(f"已移除约束: {constraint_id}")
        else:
            logger.warning(f"尝试移除不存在的约束: {constraint_id}")
    
    def enable_constraint(self, constraint_id: str) -> None:
        """
        启用约束
        
        参数:
            constraint_id: 约束ID
        """
        if constraint_id in self._constraints:
            self._enabled_constraints.add(constraint_id)
            logger.debug(f"已启用约束: {constraint_id}")
        else:
            logger.warning(f"尝试启用不存在的约束: {constraint_id}")
    
    def disable_constraint(self, constraint_id: str) -> None:
        """
        禁用约束
        
        参数:
            constraint_id: 约束ID
        """
        if constraint_id in self._constraints:
            self._enabled_constraints.discard(constraint_id)
            logger.debug(f"已禁用约束: {constraint_id}")
        else:
            logger.warning(f"尝试禁用不存在的约束: {constraint_id}")
    
    def is_constraint_enabled(self, constraint_id: str) -> bool:
        """
        检查约束是否启用
        
        参数:
            constraint_id: 约束ID
            
        返回:
            是否启用
        """
        return constraint_id in self._enabled_constraints
    
    def get_constraint(self, constraint_id: str) -> Optional[ConstraintInterface]:
        """
        获取约束对象
        
        参数:
            constraint_id: 约束ID
            
        返回:
            约束对象，如果不存在则返回None
        """
        return self._constraints.get(constraint_id)
    
    def get_all_constraints(self) -> Dict[str, ConstraintInterface]:
        """
        获取所有约束
        
        返回:
            约束ID到约束对象的映射
        """
        return self._constraints.copy()
    
    def get_enabled_constraints(self) -> Dict[str, ConstraintInterface]:
        """
        获取所有启用的约束
        
        返回:
            约束ID到约束对象的映射
        """
        return {
            constraint_id: self._constraints[constraint_id]
            for constraint_id in self._enabled_constraints
            if constraint_id in self._constraints
        }
    
    def clear_constraints(self) -> None:
        """
        清除所有约束
        """
        self._constraints.clear()
        self._enabled_constraints.clear()
        logger.debug("已清除所有约束")
    
    def generate_constraints(
        self,
        asset_names: List[str],
        expected_returns: pd.Series = None,
        covariance_matrix: pd.DataFrame = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成所有启用的约束
        
        参数:
            asset_names: 资产名称列表
            expected_returns: 预期收益率
            covariance_matrix: 协方差矩阵
            **kwargs: 其他参数
            
        返回:
            约束字典，可直接传递给优化器
        """
        if not asset_names:
            raise OptimizationConstraintException("必须提供资产名称列表")
        
        # 获取启用的约束
        enabled_constraints = self.get_enabled_constraints()
        
        if not enabled_constraints:
            logger.warning("没有启用的约束")
            return {}
        
        # 存储各类约束
        eq_constraints = []
        ineq_constraints = []
        bounds = None
        
        # 生成每个约束
        for constraint_id, constraint in enabled_constraints.items():
            try:
                # 调用约束的get_constraint方法
                constraint_dict = constraint.get_constraint(
                    asset_names=asset_names,
                    expected_returns=expected_returns,
                    covariance_matrix=covariance_matrix,
                    **kwargs
                )
                
                # 处理约束类型
                if 'type' in constraint_dict and 'fun' in constraint_dict:
                    # 单个约束
                    if constraint_dict['type'] == 'eq':
                        eq_constraints.append(constraint_dict)
                    elif constraint_dict['type'] == 'ineq':
                        ineq_constraints.append(constraint_dict)
                    else:
                        logger.warning(f"未知的约束类型: {constraint_dict['type']}")
                
                # 处理约束列表
                elif 'constraints' in constraint_dict:
                    for c in constraint_dict['constraints']:
                        if c['type'] == 'eq':
                            eq_constraints.append(c)
                        elif c['type'] == 'ineq':
                            ineq_constraints.append(c)
                        else:
                            logger.warning(f"未知的约束类型: {c['type']}")
                
                # 处理边界约束
                elif 'bounds' in constraint_dict:
                    if bounds is not None:
                        logger.warning("多个边界约束，使用最后一个")
                    bounds = constraint_dict['bounds']
                
                else:
                    logger.warning(f"未知的约束格式: {constraint_dict}")
            
            except Exception as e:
                logger.error(f"生成约束 '{constraint_id}' 时出错: {str(e)}")
        
        # 组合所有约束
        combined_constraints = []
        combined_constraints.extend(eq_constraints)
        combined_constraints.extend(ineq_constraints)
        
        result = {
            'constraints': combined_constraints
        }
        
        if bounds is not None:
            result['bounds'] = bounds
        
        return result
    
    def __str__(self) -> str:
        """返回约束管理器的字符串表示"""
        enabled_count = len(self._enabled_constraints)
        total_count = len(self._constraints)
        return f"{self._name}: {enabled_count}/{total_count} 约束已启用"
    
    def __repr__(self) -> str:
        """返回约束管理器的详细表示"""
        return f"ConstraintManager(name='{self._name}', constraints={len(self._constraints)}, enabled={len(self._enabled_constraints)})" 