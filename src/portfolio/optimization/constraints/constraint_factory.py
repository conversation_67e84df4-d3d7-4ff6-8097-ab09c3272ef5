"""
投资组合优化约束工厂模块

提供创建优化约束的工厂类，支持生成各种类型的投资组合约束。
"""

import cvxpy as cp
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple


class ConstraintFactory:
    """
    约束工厂类
    
    负责创建各种投资组合优化约束条件。
    """
    
    def __init__(self):
        """初始化约束工厂"""
        pass
    
    def create_weight_sum_constraint(
        self, 
        weights_var: cp.Variable, 
        sum_value: float = 1.0
    ) -> List[cp.Constraint]:
        """
        创建权重和约束
        
        参数:
            weights_var: 权重变量
            sum_value: 权重和，默认为1.0
            
        返回:
            权重和约束列表
        """
        return [cp.sum(weights_var) == sum_value]
    
    def create_weight_bounds_constraint(
        self, 
        weights_var: cp.Variable, 
        bounds: Tuple[float, float] = (0, 1)
    ) -> List[cp.Constraint]:
        """
        创建权重上下界约束
        
        参数:
            weights_var: 权重变量
            bounds: (下界, 上界) 元组，默认为(0, 1)
            
        返回:
            权重边界约束列表
        """
        lower_bound, upper_bound = bounds
        constraints = []
        
        if lower_bound is not None:
            constraints.append(weights_var >= lower_bound)
            
        if upper_bound is not None:
            constraints.append(weights_var <= upper_bound)
            
        return constraints
    
    def create_sector_constraint(
        self, 
        weights_var: cp.Variable,
        sector_mapper: Dict[int, str],
        sector_bounds: Dict[str, Tuple[float, float]]
    ) -> List[cp.Constraint]:
        """
        创建行业约束
        
        参数:
            weights_var: 权重变量
            sector_mapper: 资产索引到行业的映射
            sector_bounds: 行业权重上下界 {行业: (下界, 上界)}
            
        返回:
            行业约束列表
        """
        constraints = []
        
        # 按行业分组创建约束
        sectors = set(sector_mapper.values())
        
        for sector in sectors:
            # 获取该行业的资产索引
            sector_indices = [i for i, s in sector_mapper.items() if s == sector]
            
            if sector in sector_bounds:
                lower_bound, upper_bound = sector_bounds[sector]
                
                # 计算行业权重和
                sector_weight = sum(weights_var[i] for i in sector_indices)
                
                # 添加行业权重约束
                if lower_bound is not None:
                    constraints.append(sector_weight >= lower_bound)
                    
                if upper_bound is not None:
                    constraints.append(sector_weight <= upper_bound)
                    
        return constraints
    
    def create_turnover_constraint(
        self, 
        weights_var: cp.Variable,
        current_weights: np.ndarray,
        max_turnover: float
    ) -> List[cp.Constraint]:
        """
        创建换手率约束
        
        参数:
            weights_var: 权重变量
            current_weights: 当前权重
            max_turnover: 最大换手率
            
        返回:
            换手率约束列表
        """
        return [cp.sum(cp.abs(weights_var - current_weights)) <= max_turnover]
    
    def create_return_constraint(
        self, 
        weights_var: cp.Variable,
        expected_returns: np.ndarray,
        target_return: float
    ) -> List[cp.Constraint]:
        """
        创建收益率约束
        
        参数:
            weights_var: 权重变量
            expected_returns: 期望收益率
            target_return: 目标收益率
            
        返回:
            收益率约束列表
        """
        return [weights_var @ expected_returns >= target_return]
    
    def create_risk_constraint(
        self, 
        weights_var: cp.Variable,
        covariance_matrix: np.ndarray,
        max_risk: float
    ) -> List[cp.Constraint]:
        """
        创建风险约束
        
        参数:
            weights_var: 权重变量
            covariance_matrix: 协方差矩阵
            max_risk: 最大风险
            
        返回:
            风险约束列表
        """
        portfolio_variance = cp.quad_form(weights_var, covariance_matrix)
        return [portfolio_variance <= max_risk ** 2]  # 使用方差而不是标准差
    
    def create_constraints_from_dict(
        self,
        weights_var: cp.Variable,
        constraints_dict: Dict[str, Any],
        **kwargs
    ) -> List[cp.Constraint]:
        """
        从字典创建约束
        
        参数:
            weights_var: 权重变量
            constraints_dict: 约束字典，例如 {
                "weight_sum": 1.0,
                "weight_bounds": (0, 0.2),
                "sector_bounds": {"Tech": (0.1, 0.3)},
                "max_turnover": 0.2,
                "target_return": 0.08,
                "max_risk": 0.15
            }
            **kwargs: 其他参数
            
        返回:
            约束列表
        """
        all_constraints = []
        
        # 权重和约束
        if "weight_sum" in constraints_dict:
            sum_value = constraints_dict["weight_sum"]
            all_constraints.extend(self.create_weight_sum_constraint(weights_var, sum_value))
        
        # 权重上下界约束
        if "weight_bounds" in constraints_dict:
            bounds = constraints_dict["weight_bounds"]
            all_constraints.extend(self.create_weight_bounds_constraint(weights_var, bounds))
        
        # 行业约束
        if "sector_bounds" in constraints_dict and "sector_mapper" in kwargs:
            sector_bounds = constraints_dict["sector_bounds"]
            sector_mapper = kwargs["sector_mapper"]
            all_constraints.extend(self.create_sector_constraint(weights_var, sector_mapper, sector_bounds))
        
        # 换手率约束
        if "max_turnover" in constraints_dict and "current_weights" in kwargs:
            max_turnover = constraints_dict["max_turnover"]
            current_weights = kwargs["current_weights"]
            all_constraints.extend(self.create_turnover_constraint(weights_var, current_weights, max_turnover))
        
        # 收益率约束
        if "target_return" in constraints_dict and "expected_returns" in kwargs:
            target_return = constraints_dict["target_return"]
            expected_returns = kwargs["expected_returns"]
            all_constraints.extend(self.create_return_constraint(weights_var, expected_returns, target_return))
        
        # 风险约束
        if "max_risk" in constraints_dict and "covariance_matrix" in kwargs:
            max_risk = constraints_dict["max_risk"]
            covariance_matrix = kwargs["covariance_matrix"]
            all_constraints.extend(self.create_risk_constraint(weights_var, covariance_matrix, max_risk))
        
        return all_constraints 