"""
组合优化约束模块

提供投资组合优化所需的各种约束类和约束管理器。
"""

from src.portfolio.optimization.constraints.interfaces import (
    ConstraintInterface,
    BaseConstraint,
    OptimizationConstraintException
)

from src.portfolio.optimization.constraints.basic_constraints import (
    WeightSumConstraint,
    WeightBoundsConstraint,
    GroupWeightConstraint
)

from src.portfolio.optimization.constraints.advanced_constraints import (
    MaximumVolatilityConstraint,
    TargetVolatilityConstraint,
    FactorExposureConstraint,
    MinimumReturnConstraint
)

from src.portfolio.optimization.constraints.constraint_manager import (
    ConstraintManager
)

__all__ = [
    # 接口和基类
    "ConstraintInterface",
    "BaseConstraint",
    "OptimizationConstraintException",
    
    # 基本约束
    "WeightSumConstraint",
    "WeightBoundsConstraint",
    "GroupWeightConstraint",
    
    # 高级约束
    "MaximumVolatilityConstraint",
    "TargetVolatilityConstraint",
    "FactorExposureConstraint",
    "MinimumReturnConstraint",
    
    # 约束管理器
    "ConstraintManager"
] 