"""
基本约束模块

实现常用的优化约束，如权重和为1、权重上下界等。
"""
from typing import Dict, List, Optional, Union, Any, Tuple
import numpy as np
import pandas as pd

from src.portfolio.optimization.constraints.interfaces import BaseConstraint, OptimizationConstraintException


class WeightSumConstraint(BaseConstraint):
    """
    权重和约束
    
    确保权重之和等于指定值（默认为1）
    """
    
    def __init__(
        self,
        name: str = "权重和约束",
        description: str = "确保权重之和等于指定值",
        target_sum: float = 1.0,
        **kwargs
    ):
        """
        初始化权重和约束
        
        参数:
            name: 约束名称
            description: 约束描述
            target_sum: 目标权重和，默认为1.0
            **kwargs: 其他参数
        """
        super().__init__(name, description, target_sum=target_sum, **kwargs)
    
    def get_constraint(
        self,
        asset_names: List[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成权重和约束
        
        参数:
            asset_names: 资产名称列表
            **kwargs: 其他参数
            
        返回:
            约束字典
        """
        # 准备资产索引
        self._prepare_assets(asset_names)
        
        # 获取目标权重和
        target_sum = self.get_param('target_sum', 1.0)
        
        # 创建约束函数：权重之和等于目标值
        def weight_sum_constraint(weights):
            return np.sum(weights) - target_sum
        
        # 返回等式约束
        return {
            'type': 'eq',
            'fun': weight_sum_constraint
        }


class WeightBoundsConstraint(BaseConstraint):
    """
    权重边界约束
    
    确保每个资产的权重在指定的范围内
    """
    
    def __init__(
        self,
        name: str = "权重边界约束",
        description: str = "确保每个资产的权重在指定的范围内",
        lower_bound: float = 0.0,
        upper_bound: float = 1.0,
        asset_specific_bounds: Dict[str, Tuple[float, float]] = None,
        **kwargs
    ):
        """
        初始化权重边界约束
        
        参数:
            name: 约束名称
            description: 约束描述
            lower_bound: 默认下限，适用于所有资产
            upper_bound: 默认上限，适用于所有资产
            asset_specific_bounds: 特定资产的边界，格式为 {资产名称: (下限, 上限)}
            **kwargs: 其他参数
        """
        super().__init__(
            name, 
            description, 
            lower_bound=lower_bound,
            upper_bound=upper_bound,
            asset_specific_bounds=asset_specific_bounds or {},
            **kwargs
        )
    
    def get_constraint(
        self,
        asset_names: List[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成权重边界约束
        
        参数:
            asset_names: 资产名称列表
            **kwargs: 其他参数
            
        返回:
            约束字典
        """
        # 准备资产索引
        self._prepare_assets(asset_names)
        
        # 获取默认边界
        lower_bound = self.get_param('lower_bound', 0.0)
        upper_bound = self.get_param('upper_bound', 1.0)
        
        # 获取特定资产的边界
        asset_specific_bounds = self.get_param('asset_specific_bounds', {})
        
        # 为每个资产创建边界
        bounds = []
        for asset in self._asset_names:
            if asset in asset_specific_bounds:
                asset_bound = asset_specific_bounds[asset]
                bounds.append(asset_bound)
            else:
                bounds.append((lower_bound, upper_bound))
        
        # 返回边界约束
        return {
            'bounds': bounds
        }


class GroupWeightConstraint(BaseConstraint):
    """
    组权重约束
    
    确保特定组（如行业、国家等）的权重在指定范围内
    """
    
    def __init__(
        self,
        name: str = "组权重约束",
        description: str = "确保特定组的权重在指定范围内",
        group_mapping: Dict[str, str] = None,
        group_lower_bounds: Dict[str, float] = None,
        group_upper_bounds: Dict[str, float] = None,
        **kwargs
    ):
        """
        初始化组权重约束
        
        参数:
            name: 约束名称
            description: 约束描述
            group_mapping: 资产到组的映射，格式为 {资产名称: 组名称}
            group_lower_bounds: 组的下限，格式为 {组名称: 下限}
            group_upper_bounds: 组的上限，格式为 {组名称: 上限}
            **kwargs: 其他参数
        """
        super().__init__(
            name, 
            description, 
            group_mapping=group_mapping or {},
            group_lower_bounds=group_lower_bounds or {},
            group_upper_bounds=group_upper_bounds or {},
            **kwargs
        )
    
    def get_constraint(
        self,
        asset_names: List[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成组权重约束
        
        参数:
            asset_names: 资产名称列表
            **kwargs: 其他参数
            
        返回:
            约束字典列表
        """
        # 准备资产索引
        self._prepare_assets(asset_names)
        
        # 获取组映射和边界
        group_mapping = self.get_param('group_mapping', {})
        group_lower_bounds = self.get_param('group_lower_bounds', {})
        group_upper_bounds = self.get_param('group_upper_bounds', {})
        
        # 验证每个资产都有组映射
        for asset in self._asset_names:
            if asset not in group_mapping:
                raise OptimizationConstraintException(f"资产 '{asset}' 没有组映射")
        
        # 根据组映射构建组索引映射
        group_indices = {}
        for group in set(group_mapping.values()):
            group_indices[group] = [
                i for i, asset in enumerate(self._asset_names)
                if group_mapping.get(asset, None) == group
            ]
        
        # 创建约束列表
        constraints = []
        
        # 添加下限约束
        for group, lower_bound in group_lower_bounds.items():
            if group in group_indices and group_indices[group]:
                indices = group_indices[group]
                
                def group_lower_constraint(weights, indices=indices, bound=lower_bound):
                    return sum(weights[i] for i in indices) - bound
                
                constraints.append({
                    'type': 'ineq',
                    'fun': group_lower_constraint
                })
        
        # 添加上限约束
        for group, upper_bound in group_upper_bounds.items():
            if group in group_indices and group_indices[group]:
                indices = group_indices[group]
                
                def group_upper_constraint(weights, indices=indices, bound=upper_bound):
                    return bound - sum(weights[i] for i in indices)
                
                constraints.append({
                    'type': 'ineq',
                    'fun': group_upper_constraint
                })
        
        # 返回约束列表
        return {
            'constraints': constraints
        } 