"""
组合优化约束接口模块

定义组合优化所需的约束接口和基础约束类。
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union, Any, Tuple, Callable
import numpy as np
import pandas as pd


class OptimizationConstraintException(Exception):
    """优化约束异常基类"""
    pass


class ConstraintInterface(ABC):
    """
    投资组合优化约束接口类
    
    所有优化约束必须实现此接口，提供统一的约束生成方法。
    """
    
    def __init__(
        self, 
        name: str = None,
        description: str = "",
        **kwargs
    ):
        """
        初始化约束
        
        参数:
            name: 约束名称
            description: 约束描述
            **kwargs: 其他参数
        """
        self._name = name or self.__class__.__name__
        self._description = description
        self._params = kwargs
        self._asset_names = None
        self._asset_indices = None
    
    @property
    def name(self) -> str:
        """获取约束名称"""
        return self._name
    
    @property
    def description(self) -> str:
        """获取约束描述"""
        return self._description
    
    @property
    def params(self) -> Dict[str, Any]:
        """获取约束参数"""
        return self._params.copy()
    
    def set_param(self, param_name: str, param_value: Any) -> None:
        """
        设置约束参数
        
        参数:
            param_name: 参数名
            param_value: 参数值
        """
        self._params[param_name] = param_value
        
    def get_param(self, param_name: str, default: Any = None) -> Any:
        """
        获取约束参数
        
        参数:
            param_name: 参数名
            default: 默认值（如果参数不存在）
            
        返回:
            参数值或默认值
        """
        return self._params.get(param_name, default)
    
    @abstractmethod
    def get_constraint(
        self,
        asset_names: List[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成优化约束
        
        参数:
            asset_names: 资产名称列表
            **kwargs: 其他参数
            
        返回:
            约束字典，用于优化器的constraints参数
        """
        pass
    
    def __str__(self) -> str:
        """返回约束的字符串表示"""
        return f"{self._name}: {self._description}"
    
    def __repr__(self) -> str:
        """返回约束的详细表示"""
        return f"{self.__class__.__name__}(name='{self._name}', description='{self._description}', params={self._params})"


class BaseConstraint(ConstraintInterface):
    """
    基础约束类
    
    提供约束通用功能，派生类只需实现特定的约束逻辑。
    """
    
    def __init__(
        self, 
        name: str = None,
        description: str = "",
        **kwargs
    ):
        """
        初始化约束
        
        参数:
            name: 约束名称
            description: 约束描述
            **kwargs: 其他参数
        """
        super().__init__(name, description, **kwargs)
    
    def _prepare_assets(self, asset_names: List[str]) -> None:
        """
        准备资产名称和索引
        
        参数:
            asset_names: 资产名称列表
        """
        if asset_names is None:
            raise OptimizationConstraintException("必须提供资产名称列表")
            
        self._asset_names = asset_names
        self._asset_indices = {name: i for i, name in enumerate(asset_names)}
    
    def get_constraint(
        self,
        asset_names: List[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成优化约束
        
        参数:
            asset_names: 资产名称列表
            **kwargs: 其他参数
            
        返回:
            约束字典，用于优化器的constraints参数
        """
        # 子类应实现此方法
        raise NotImplementedError("子类必须实现get_constraint方法") 