"""
组合优化工厂模块

提供创建不同类型优化器的工厂类，以及便捷的创建函数。
"""
from typing import Dict, Type, Any, Optional, List
import logging

from src.portfolio.optimization.interfaces import PortfolioOptimizerInterface

logger = logging.getLogger(__name__)


class OptimizerFactory:
    """
    组合优化器工厂类
    
    用于管理和创建不同类型的组合优化器
    """
    
    _optimizers: Dict[str, Type[PortfolioOptimizerInterface]] = {}
    
    @classmethod
    def register(cls, optimizer_type: str, optimizer_class: Type[PortfolioOptimizerInterface]) -> None:
        """
        注册优化器类型
        
        参数:
            optimizer_type: 优化器类型名称
            optimizer_class: 优化器类
        """
        if optimizer_type in cls._optimizers:
            logger.warning(f"优化器类型 '{optimizer_type}' 已存在，将被覆盖")
        
        cls._optimizers[optimizer_type] = optimizer_class
        logger.debug(f"已注册优化器类型: {optimizer_type}")
    
    @classmethod
    def create(cls, optimizer_type: str, **kwargs) -> PortfolioOptimizerInterface:
        """
        创建指定类型的优化器实例
        
        参数:
            optimizer_type: 优化器类型名称
            **kwargs: 传递给优化器构造函数的参数
            
        返回:
            优化器实例
            
        抛出:
            ValueError: 如果优化器类型未注册
        """
        if optimizer_type not in cls._optimizers:
            registered_types = ", ".join(cls._optimizers.keys())
            raise ValueError(
                f"未知的优化器类型: {optimizer_type}. "
                f"已注册的类型: {registered_types or '无'}"
            )
        
        optimizer_class = cls._optimizers[optimizer_type]
        return optimizer_class(**kwargs)
    
    @classmethod
    def get_available_types(cls) -> List[str]:
        """
        获取所有可用的优化器类型
        
        返回:
            可用优化器类型列表
        """
        return list(cls._optimizers.keys())
    
    @classmethod
    def create_from_config(cls, config: Dict[str, Any]) -> PortfolioOptimizerInterface:
        """
        根据配置创建优化器
        
        配置字典结构:
        {
            "type": "优化器类型",
            "params": {
                "参数1": 值1,
                "参数2": 值2,
                ...
            }
        }
        
        参数:
            config: 配置字典
            
        返回:
            优化器实例
            
        抛出:
            ValueError: 如果配置无效或优化器类型未注册
        """
        if not isinstance(config, dict):
            raise ValueError(f"配置必须是字典，而不是 {type(config)}")
        
        optimizer_type = config.get("type")
        if not optimizer_type:
            raise ValueError("配置中缺少 'type' 字段")
        
        params = config.get("params", {})
        return cls.create(optimizer_type, **params)


def create_optimizer(optimizer_type: str, **kwargs) -> PortfolioOptimizerInterface:
    """
    创建指定类型的优化器实例的便捷函数
    
    参数:
        optimizer_type: 优化器类型名称
        **kwargs: 传递给优化器构造函数的参数
        
    返回:
        优化器实例
    """
    return OptimizerFactory.create(optimizer_type, **kwargs)


# 注册默认优化器
from src.portfolio.optimization.mean_variance import MeanVarianceOptimizer
from src.portfolio.optimization.risk_parity import RiskParityOptimizer
from src.portfolio.optimization.max_diversification import MaxDiversificationOptimizer
# from portfolio.optimization.hierarchical_risk_parity import HierarchicalRiskParityOptimizer

# 在这里注册默认的优化器
OptimizerFactory.register("mean_variance", MeanVarianceOptimizer)
OptimizerFactory.register("risk_parity", RiskParityOptimizer)
OptimizerFactory.register("max_diversification", MaxDiversificationOptimizer)
# OptimizerFactory.register("hierarchical_risk_parity", HierarchicalRiskParityOptimizer) 