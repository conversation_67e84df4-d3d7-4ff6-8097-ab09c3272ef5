"""
分层风险平价优化器模块

实现基于层次聚类的风险平价（Hierarchical Risk Parity, HRP）算法，该算法由<PERSON> de Prado提出。
它解决了传统风险平价方法对协方差矩阵估计敏感的问题，通过对资产进行层次聚类并分层分配风险。

参考文献:
- <PERSON> de P<PERSON>, M. (2016). Building Diversified Portfolios that Outperform Out of Sample.
  Journal of Portfolio Management, 42(4), 59-69.
"""
import numpy as np
import pandas as pd
from scipy.cluster import hierarchy
from scipy.spatial.distance import squareform
from typing import Dict, Any, Optional, Tuple, List, Union
import logging

from src.portfolio.optimization.interfaces import (
    PortfolioOptimizerInterface,
    PortfolioOptimizationException,
    InvalidInputException
)

logger = logging.getLogger(__name__)


class HierarchicalRiskParityOptimizer(PortfolioOptimizerInterface):
    """
    分层风险平价优化器
    
    基于层次聚类的风险平价（Hierarchical Risk Parity, HRP）方法，通过对资产
    进行层次聚类，并在聚类的基础上分配风险，从而构建出更稳健的投资组合。
    
    相比传统的风险平价方法，HRP对协方差矩阵估计噪音更加稳健，且不需要求解
    复杂的优化问题，在大规模资产配置中表现出优势。
    
    特点：
    1. 不依赖协方差矩阵的逆，减少估计误差的影响
    2. 使用层次聚类捕捉资产间的相似性结构
    3. 递归二分配置方案，平衡类内和类间风险
    
    参数:
        linkage_method: 层次聚类的连接方法，支持'single', 'complete', 'average', 'ward'等
        distance_metric: 距离度量方式，默认使用相关性距离
        weight_method: 权重计算方法，支持'inverse_variance'和'equal_risk_contribution'
        use_variance_adjustment: 是否使用方差调整加权，控制最终的风险分配
        weight_bounds: 权重约束 (最小权重, 最大权重)
    """
    
    # 支持的连接方法
    VALID_LINKAGE_METHODS = {'single', 'complete', 'average', 'weighted', 'centroid', 'median', 'ward'}
    
    # 支持的权重方法
    VALID_WEIGHT_METHODS = {'inverse_variance', 'equal_risk_contribution'}
    
    def __init__(
        self,
        name: str = "分层风险平价优化器",
        description: str = "基于层次聚类的风险平价算法，解决协方差矩阵估计噪音问题",
        linkage_method: str = "single",
        distance_metric: str = "correlation",
        weight_method: str = "inverse_variance",
        use_variance_adjustment: bool = True,
        weight_bounds: Tuple[float, float] = (0.0, 1.0),
        **kwargs
    ):
        """
        初始化分层风险平价优化器
        
        参数:
            name: 优化器名称
            description: 优化器描述
            linkage_method: 层次聚类的连接方法
            distance_metric: 距离度量方式
            weight_method: 权重计算方法
            use_variance_adjustment: 是否使用方差调整
            weight_bounds: 权重约束 (最小权重, 最大权重)
            **kwargs: 其他参数
        """
        super().__init__(name=name, description=description, **kwargs)
        
        # 验证输入参数
        if linkage_method not in self.VALID_LINKAGE_METHODS:
            raise ValueError(
                f"无效的连接方法: {linkage_method}，"
                f"有效选项: {', '.join(self.VALID_LINKAGE_METHODS)}"
            )
        
        if weight_method not in self.VALID_WEIGHT_METHODS:
            raise ValueError(
                f"无效的权重方法: {weight_method}，"
                f"有效选项: {', '.join(self.VALID_WEIGHT_METHODS)}"
            )
        
        self._params.update({
            'linkage_method': linkage_method,
            'distance_metric': distance_metric,
            'weight_method': weight_method,
            'use_variance_adjustment': use_variance_adjustment,
            'weight_bounds': weight_bounds
        })
        
        # 实例变量
        self._covariance_matrix = None
        self._asset_names = None
        self._weights = None
        self._linkage = None
        self._clusters = None
        self._ordered_indices = None
        self._quasi_diag = None
        self._is_optimized = False
        self._metrics = {}
    
    def _validate_inputs(self, covariance_matrix: Optional[pd.DataFrame]) -> None:
        """
        验证输入数据的有效性
        
        参数:
            covariance_matrix: 协方差矩阵
            
        抛出:
            InvalidInputException: 如果输入数据无效
        """
        if covariance_matrix is None:
            raise InvalidInputException("协方差矩阵不能为空")
            
        if not isinstance(covariance_matrix, pd.DataFrame):
            raise InvalidInputException(
                f"协方差矩阵必须是pandas DataFrame类型，实际类型: {type(covariance_matrix)}"
            )
            
        # 验证协方差矩阵是否为方阵
        if covariance_matrix.shape[0] != covariance_matrix.shape[1]:
            raise InvalidInputException(
                f"协方差矩阵必须是方阵，实际形状: {covariance_matrix.shape}"
            )
        
        # 检查协方差矩阵是否包含NaN值
        if covariance_matrix.isnull().any().any():
            raise InvalidInputException("协方差矩阵包含NaN值")
        
        # 检查协方差矩阵是否对称
        if not np.allclose(covariance_matrix, covariance_matrix.T):
            raise InvalidInputException("协方差矩阵必须是对称矩阵")
        
        # 检查协方差矩阵是否半正定（所有特征值≥0）
        try:
            # 使用Cholesky分解检查，如果矩阵非半正定会抛出异常
            np.linalg.cholesky(covariance_matrix.values)
        except np.linalg.LinAlgError:
            logger.warning("协方差矩阵似乎不是半正定的，可能会影响优化结果")
    
    def _prepare_data(self, covariance_matrix: pd.DataFrame) -> None:
        """
        准备优化所需的数据
        
        参数:
            covariance_matrix: 协方差矩阵
        """
        self._covariance_matrix = covariance_matrix.copy()
        self._asset_names = covariance_matrix.index.tolist()
        self._is_optimized = False
        self._metrics = {}
    
    def _get_correlation_distance(self) -> np.ndarray:
        """
        计算基于相关性的距离矩阵
        
        相关性距离定义为 sqrt(0.5 * (1 - correlation))
        
        返回:
            距离矩阵
        """
        # 如果协方差矩阵没有准备好，抛出异常
        if self._covariance_matrix is None:
            raise PortfolioOptimizationException("协方差矩阵未初始化")
        
        # 计算相关系数矩阵
        cov = self._covariance_matrix.values
        std = np.sqrt(np.diag(cov))
        corr = cov / np.outer(std, std)
        
        # 计算距离矩阵: sqrt(0.5 * (1 - corr))
        distance = np.sqrt(0.5 * (1 - corr))
        
        return distance
    
    def _get_clusters(self) -> Tuple[np.ndarray, List[int]]:
        """
        执行层次聚类，构建资产聚类结构
        
        返回:
            (linkage, ordered_indices): 聚类结果和排序后的资产索引
        """
        distance = self._get_correlation_distance()
        
        # 将方阵转换为压缩的距离向量
        distance_vector = squareform(distance)
        
        # 执行层次聚类
        linkage_method = self.get_param('linkage_method')
        linkage = hierarchy.linkage(distance_vector, method=linkage_method)
        
        # 获取排序后的聚类索引
        ordered_indices = hierarchy.leaves_list(
            hierarchy.optimal_leaf_ordering(linkage, distance_vector)
        )
        
        return linkage, ordered_indices.tolist()
    
    def _get_quasi_diag(self, linkage: np.ndarray) -> List[List[int]]:
        """
        获取准对角矩阵，用于递归二分算法
        
        参数:
            linkage: 层次聚类结果
            
        返回:
            准对角聚类序列
        """
        # 资产数量
        n_assets = self._covariance_matrix.shape[0]
        
        # 准对角聚类列表
        quasi_diag = [list(range(n_assets))]
        
        # 聚类数量（包括叶节点和内部节点）
        n_clusters = n_assets
        
        # 遍历层次聚类结果
        for k in range(n_assets - 1):
            # 当前聚类步骤
            cluster_step = linkage[k]
            
            # 获取要合并的两个聚类的索引
            idx1, idx2 = int(cluster_step[0]), int(cluster_step[1])
            
            # 找到这两个聚类在准对角列表中的位置
            for i, cluster in enumerate(quasi_diag):
                if idx1 in cluster and idx2 in cluster:
                    # 从当前聚类中移除这两个子聚类
                    quasi_diag.pop(i)
                    
                    # 创建新的聚类（按顺序添加）
                    indices = np.sort([idx1, idx2])
                    if indices[0] < indices[1]:
                        new_cluster = [indices[0], indices[1]]
                    else:
                        new_cluster = [indices[1], indices[0]]
                    
                    quasi_diag.append(new_cluster)
                    break
                    
                if idx1 in cluster:
                    # 将第一个聚类替换为新聚类索引
                    quasi_diag[i] = [n_clusters + k if x == idx1 else x for x in cluster]
                
                if idx2 in cluster:
                    # 将第二个聚类替换为新聚类索引
                    quasi_diag[i] = [n_clusters + k if x == idx2 else x for x in cluster]
        
        return quasi_diag
    
    def _recursive_bisection(self, cov: np.ndarray, indices: List[int]) -> np.ndarray:
        """
        递归二分算法，计算每个资产的权重
        
        参数:
            cov: 协方差矩阵
            indices: 资产索引列表
            
        返回:
            权重数组
        """
        # 如果只有一个资产，返回1.0
        if len(indices) == 1:
            return np.array([1.0])
        
        # 对聚类进行二分
        mid = len(indices) // 2
        left_indices = indices[:mid]
        right_indices = indices[mid:]
        
        # 提取子协方差矩阵
        cov_left = cov[np.ix_(left_indices, left_indices)]
        cov_right = cov[np.ix_(right_indices, right_indices)]
        
        # 递归计算左右子聚类的权重
        weights_left = self._recursive_bisection(cov, left_indices)
        weights_right = self._recursive_bisection(cov, right_indices)
        
        # 计算左右子聚类的风险
        left_risk = self._calculate_cluster_risk(cov_left, weights_left)
        right_risk = self._calculate_cluster_risk(cov_right, weights_right)
        
        # 计算权重调整因子
        alpha = 1 - (left_risk / (left_risk + right_risk))
        
        # 合并左右子聚类的权重
        weights = np.zeros(len(indices))
        weights[:mid] = alpha * weights_left
        weights[mid:] = (1 - alpha) * weights_right
        
        return weights
    
    def _calculate_cluster_risk(self, cov: np.ndarray, weights: np.ndarray) -> float:
        """
        计算聚类的风险度量
        
        参数:
            cov: 协方差矩阵
            weights: 权重数组
            
        返回:
            风险度量（聚类组合方差）
        """
        # 计算聚类内组合方差
        return np.dot(weights.T, np.dot(cov, weights))
    
    def _adjust_weights_by_variance(self, weights: np.ndarray, cov: np.ndarray) -> np.ndarray:
        """
        根据资产方差调整权重
        
        参数:
            weights: 初始权重
            cov: 协方差矩阵
            
        返回:
            调整后的权重
        """
        # 如果不使用方差调整，直接返回原始权重
        if not self.get_param('use_variance_adjustment'):
            return weights
        
        # 提取资产方差（协方差矩阵对角线）
        asset_variance = np.diag(cov)
        
        # 权重方法
        weight_method = self.get_param('weight_method')
        
        if weight_method == 'inverse_variance':
            # 使用方差倒数加权
            inverse_variance = 1 / asset_variance
            weights = weights * inverse_variance
        elif weight_method == 'equal_risk_contribution':
            # 使用方差平方根倒数加权（标准差倒数）
            inverse_std = 1 / np.sqrt(asset_variance)
            weights = weights * inverse_std
        
        # 归一化权重，使其和为1
        weights = weights / np.sum(weights)
        
        return weights
    
    def _apply_weight_bounds(self, weights: np.ndarray) -> np.ndarray:
        """
        应用权重约束
        
        参数:
            weights: 原始权重
            
        返回:
            应用约束后的权重
        """
        min_weight, max_weight = self.get_param('weight_bounds')
        
        # 应用下限约束
        if min_weight > 0:
            # 小于下限的设为0
            active = weights >= min_weight
            
            if np.sum(active) == 0:
                # 如果所有权重都低于下限，则使用等权重
                logger.warning("所有权重都低于最小权重限制，将使用等权重")
                weights = np.ones(len(weights)) / len(weights)
            else:
                # 将低于下限的权重设为0，并重新归一化其余权重
                weights[~active] = 0
                weights[active] = weights[active] / np.sum(weights[active])
        
        # 应用上限约束
        if max_weight < 1:
            while np.any(weights > max_weight):
                # 找出超过上限的资产
                excess_indices = np.where(weights > max_weight)[0]
                
                # 计算超额权重
                excess_weight = np.sum(weights[excess_indices] - max_weight)
                
                # 将超过上限的权重设为上限
                weights[excess_indices] = max_weight
                
                # 将超额权重分配给其他非上限资产
                non_capped_indices = np.where(weights < max_weight)[0]
                
                if len(non_capped_indices) > 0:
                    # 按比例分配超额权重
                    current_sum = np.sum(weights[non_capped_indices])
                    if current_sum > 0:
                        weights[non_capped_indices] += excess_weight * weights[non_capped_indices] / current_sum
                else:
                    # 如果所有资产都达到上限，则无法满足约束
                    logger.warning("无法满足最大权重约束，尝试重新分配权重")
                    # 等权重分配，但仍可能违反约束
                    weights = np.ones(len(weights)) / len(weights)
                    break
        
        # 确保权重和为1
        weights = weights / np.sum(weights)
        
        return weights
    
    def optimize(
        self,
        expected_returns: pd.Series = None,
        covariance_matrix: pd.DataFrame = None,
        asset_data: pd.DataFrame = None,
        constraints: Dict[str, Any] = None,
        **kwargs
    ) -> pd.Series:
        """
        执行分层风险平价优化算法
        
        参数:
            expected_returns: 预期收益率（不使用但保留参数保持接口一致）
            covariance_matrix: 协方差矩阵，必须提供
            asset_data: 资产数据（不使用但保留参数保持接口一致）
            constraints: 额外约束条件（不使用但保留参数保持接口一致）
            **kwargs: 其他参数
            
        返回:
            权重序列
            
        抛出:
            PortfolioOptimizationException: 如果优化失败
        """
        # 验证输入数据
        self._validate_inputs(covariance_matrix)
        
        # 准备数据
        self._prepare_data(covariance_matrix)
        
        try:
            # 1. 执行层次聚类
            self._linkage, self._ordered_indices = self._get_clusters()
            
            # 2. 获取准对角聚类序列
            self._quasi_diag = self._get_quasi_diag(self._linkage)
            
            # 3. 递归二分计算权重
            cov = self._covariance_matrix.values
            inverse_indices = np.argsort(self._ordered_indices)
            ordered_cov = cov[self._ordered_indices, :][:, self._ordered_indices]
            
            # 计算原始权重
            raw_weights = self._recursive_bisection(
                ordered_cov, list(range(len(self._asset_names)))
            )
            
            # 将权重映射回原始资产顺序
            weights = raw_weights[inverse_indices]
            
            # 4. 根据方差调整权重（可选）
            weights = self._adjust_weights_by_variance(weights, cov)
            
            # 5. 应用权重约束
            weights = self._apply_weight_bounds(weights)
            
            # 保存权重结果
            self._weights = pd.Series(
                weights,
                index=self._asset_names,
                name="weight"
            )
            
            # 设置优化状态为已完成
            self._is_optimized = True
            
            # 计算指标
            self.calculate_metrics()
            
            return self._weights
            
        except Exception as e:
            self._is_optimized = False
            error_msg = f"分层风险平价优化失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise PortfolioOptimizationException(error_msg) from e
    
    def calculate_metrics(self) -> Dict[str, Any]:
        """
        计算并返回优化结果的相关指标
        
        指标包括:
        - 组合波动率 (portfolio_volatility)
        - 风险贡献 (risk_contribution)
        - 风险贡献百分比 (risk_contribution_percent)
        - 有效资产数 (effective_assets)
        - 集中度指标: HHI (hhi)
        - 集中度指标: Gini (gini)
        
        返回:
            指标字典
        """
        if not self._is_optimized or self._weights is None:
            raise PortfolioOptimizationException("请先运行优化")
        
        # 提取协方差矩阵和权重
        cov = self._covariance_matrix.values
        weights = self._weights.values
        
        # 1. 计算组合波动率
        portfolio_variance = np.dot(weights.T, np.dot(cov, weights))
        portfolio_volatility = np.sqrt(portfolio_variance)
        
        # 2. 计算风险贡献
        marginal_risk = np.dot(cov, weights) / portfolio_volatility
        risk_contribution = weights * marginal_risk
        risk_contribution_pct = risk_contribution / np.sum(risk_contribution)
        
        # 3. 计算有效资产数 (effective number of assets)
        # 基于权重的平方和的倒数
        effective_assets = 1 / np.sum(np.square(weights))
        
        # 4. 计算集中度指标: Herfindahl-Hirschman Index (HHI)
        hhi = np.sum(np.square(weights))
        
        # 5. 计算集中度指标: Gini系数
        sorted_weights = np.sort(weights)
        n = len(sorted_weights)
        cumsum = np.cumsum(sorted_weights)
        gini = (n + 1 - 2 * np.sum(cumsum) / np.sum(sorted_weights)) / n
        
        # 存储和返回指标
        self._metrics = {
            'volatility': portfolio_volatility,
            'risk_contribution': pd.Series(
                risk_contribution, index=self._asset_names, name="risk_contribution"
            ),
            'risk_contribution_pct': pd.Series(
                risk_contribution_pct, index=self._asset_names, name="risk_contribution_percent"
            ),
            'effective_assets': effective_assets,
            'hhi': hhi,
            'gini': gini
        }
        
        return self._metrics
    
    def plot_dendogram(self, figsize=(12, 8), title="资产层次聚类", **kwargs):
        """
        绘制聚类树状图
        
        参数:
            figsize: 图形大小
            title: 图表标题
            **kwargs: 传递给hierarchy.dendrogram的参数
            
        返回:
            matplotlib图形对象
        """
        import matplotlib.pyplot as plt
        
        if self._linkage is None:
            raise PortfolioOptimizationException("请先运行优化")
        
        fig, ax = plt.subplots(figsize=figsize)
        
        # 创建树状图
        dn = hierarchy.dendrogram(
            self._linkage,
            labels=self._asset_names,
            ax=ax,
            **kwargs
        )
        
        plt.title(title, fontsize=14)
        plt.xlabel("资产", fontsize=12)
        plt.ylabel("距离", fontsize=12)
        plt.tight_layout()
        
        return fig
    
    def get_cluster_info(self) -> Dict[str, Any]:
        """
        获取聚类信息
        
        返回:
            包含聚类信息的字典
        """
        if self._linkage is None:
            raise PortfolioOptimizationException("请先运行优化")
        
        return {
            'linkage': self._linkage,
            'ordered_indices': self._ordered_indices,
            'quasi_diag': self._quasi_diag,
            'asset_names': self._asset_names
        } 