"""
最大分散化优化模块

提供基于分散化比率最大化的投资组合优化方法。
最大分散化策略旨在创建风险分散度最高的投资组合，而不依赖于预期收益估计。

原理：
最大化分散化比率 D(w) = (w^T * σ) / sqrt(w^T * Σ * w)
其中：
- w 是权重向量
- σ 是各资产波动率向量
- Σ 是协方差矩阵

相比均值方差优化，最大分散化优化在以下情况更有优势：
1. 对预期收益估计不准确的情况更稳健
2. 专注于减少集中风险
3. 在市场动荡时期表现更好

参考文献：
- Choueifaty, Y., & Coignard, Y. (2008). Toward maximum diversification. 
  Journal of Portfolio Management, 35(1), 40-51.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional, Union
import logging
from scipy.optimize import minimize

from src.portfolio.optimization.interfaces import (
    PortfolioOptimizerInterface,
    PortfolioOptimizationException,
    OptimizationFailedException,
    InvalidInputException
)

logger = logging.getLogger(__name__)


class MaxDiversificationOptimizer(PortfolioOptimizerInterface):
    """
    最大分散化优化器
    
    通过最大化投资组合的分散化比率来构建投资组合。
    分散化比率定义为各资产波动率的加权和除以投资组合波动率。
    
    D(w) = (w^T * σ) / sqrt(w^T * Σ * w)
    
    参数:
        name: 优化器名称
        description: 优化器描述
        weight_bounds: 权重范围约束，默认为(0, 1)，即只允许做多
        min_weight: 单个资产最小权重，为None时使用weight_bounds下限
        max_weight: 单个资产最大权重，为None时使用weight_bounds上限
        risk_aversion: 风险厌恶系数，在结合预期收益时使用
        include_returns: 是否在优化中考虑预期收益
        returns_weight: 预期收益在目标函数中的权重，仅当include_returns=True时有效
    """
    
    def __init__(
        self,
        name: str = "最大分散化优化器",
        description: str = "基于分散化比率最大化的投资组合优化算法",
        weight_bounds: Tuple[float, float] = (0.0, 1.0),
        min_weight: Optional[float] = None,
        max_weight: Optional[float] = None,
        risk_aversion: float = 1.0,
        include_returns: bool = False,
        returns_weight: float = 0.5,
        **kwargs
    ):
        """初始化最大分散化优化器"""
        super().__init__(name=name, description=description, **kwargs)
        
        # 设置优化参数
        self._params = {
            "weight_bounds": weight_bounds,
            "min_weight": min_weight if min_weight is not None else weight_bounds[0],
            "max_weight": max_weight if max_weight is not None else weight_bounds[1],
            "risk_aversion": risk_aversion,
            "include_returns": include_returns,
            "returns_weight": returns_weight
        }
        
        # 初始化数据和结果存储
        self._covariance_matrix = None
        self._expected_returns = None
        self._asset_volatilities = None
        self._assets = None
        self._weights = None
        self._metrics = {}
        self._is_optimized = False
    
    def _validate_inputs(
        self, 
        expected_returns: Optional[pd.Series], 
        covariance_matrix: Optional[pd.DataFrame]
    ) -> None:
        """
        验证输入数据的有效性
        
        参数:
            expected_returns: 预期收益率序列，可以为None
            covariance_matrix: 协方差矩阵
            
        抛出:
            InvalidInputException: 如果输入数据无效
        """
        if covariance_matrix is None:
            raise InvalidInputException("必须提供协方差矩阵")
        
        if not isinstance(covariance_matrix, pd.DataFrame):
            raise InvalidInputException("协方差矩阵必须是pandas DataFrame类型")
        
        # 检查协方差矩阵是否为方阵
        if covariance_matrix.shape[0] != covariance_matrix.shape[1]:
            raise InvalidInputException("协方差矩阵必须是方阵")
        
        # 检查协方差矩阵是否半正定
        try:
            # 尝试计算Cholesky分解，如果失败则矩阵不是半正定的
            np.linalg.cholesky(covariance_matrix.values)
        except np.linalg.LinAlgError:
            # 尝试进行修正
            logger.warning("协方差矩阵不是半正定的，尝试进行调整")
            # 通过特征值分解进行修正
            eigen_vals, eigen_vecs = np.linalg.eigh(covariance_matrix.values)
            eigen_vals = np.maximum(eigen_vals, 1e-8)  # 将负或接近零的特征值调整为一个小的正数
            corrected_cov = eigen_vecs @ np.diag(eigen_vals) @ eigen_vecs.T
            covariance_matrix = pd.DataFrame(
                corrected_cov, 
                index=covariance_matrix.index, 
                columns=covariance_matrix.columns
            )
        
        # 如果include_returns为True，验证expected_returns
        if self._params["include_returns"] and expected_returns is None:
            raise InvalidInputException("启用了预期收益考虑，但没有提供预期收益率数据")
        
        if expected_returns is not None:
            if not isinstance(expected_returns, pd.Series):
                raise InvalidInputException("预期收益率必须是pandas Series类型")
            
            # 检查预期收益率与协方差矩阵的资产一致性
            if not set(expected_returns.index).issubset(set(covariance_matrix.index)):
                raise InvalidInputException("预期收益率中的资产必须是协方差矩阵资产的子集")
    
    def _prepare_data(
        self, 
        expected_returns: Optional[pd.Series], 
        covariance_matrix: pd.DataFrame
    ) -> None:
        """
        准备优化所需的数据
        
        参数:
            expected_returns: 预期收益率序列
            covariance_matrix: 协方差矩阵
        """
        # 保存输入数据
        self._covariance_matrix = covariance_matrix
        self._expected_returns = expected_returns
        
        # 提取资产列表和波动率
        self._assets = list(covariance_matrix.index)
        self._asset_volatilities = pd.Series(
            np.sqrt(np.diag(covariance_matrix.values)),
            index=self._assets
        )
    
    def _negative_diversification_ratio(self, weights: np.ndarray) -> float:
        """
        计算负的分散化比率（用于最小化）
        
        分散化比率 D(w) = (w^T * σ) / sqrt(w^T * Σ * w)
        
        参数:
            weights: 权重向量
            
        返回:
            负的分散化比率
        """
        # 计算权重波动率之和
        weighted_vol_sum = np.dot(weights, self._asset_volatilities)
        
        # 计算投资组合波动率
        port_variance = weights @ self._covariance_matrix.values @ weights
        port_volatility = np.sqrt(port_variance)
        
        # 计算分散化比率
        diversification_ratio = weighted_vol_sum / port_volatility
        
        # 如果需要考虑预期收益
        if self._params["include_returns"] and self._expected_returns is not None:
            expected_return = np.dot(weights, self._expected_returns)
            return_contribution = self._params["returns_weight"] * (-expected_return)
            div_ratio_contribution = (1 - self._params["returns_weight"]) * (-diversification_ratio)
            return return_contribution + div_ratio_contribution
        
        # 返回负值用于最小化
        return -diversification_ratio
    
    def optimize(
        self,
        expected_returns: pd.Series = None,
        covariance_matrix: pd.DataFrame = None,
        asset_data: pd.DataFrame = None,
        constraints: Dict[str, Any] = None,
        **kwargs
    ) -> pd.Series:
        """
        执行最大分散化优化
        
        参数:
            expected_returns: 预期收益率，如果不提供则使用之前设置的数据
            covariance_matrix: 协方差矩阵，如果不提供则使用之前设置的数据
            asset_data: 资产数据，包含收益率和协方差的替代输入方式
            constraints: 额外的优化约束条件
            **kwargs: 其他参数
            
        返回:
            最优权重序列
            
        抛出:
            PortfolioOptimizationException: 优化失败时抛出
        """
        # 处理asset_data输入
        if asset_data is not None and covariance_matrix is None:
            if hasattr(asset_data, 'cov'):
                covariance_matrix = asset_data.cov()
            else:
                raise InvalidInputException("提供的asset_data不包含可计算协方差的数据")
        
        # 验证输入
        self._validate_inputs(expected_returns, covariance_matrix)
        
        # 准备数据
        self._prepare_data(expected_returns, covariance_matrix)
        
        # 获取资产数量
        n_assets = len(self._assets)
        
        # 初始权重：等权重
        initial_weights = np.ones(n_assets) / n_assets
        
        # 设置权重约束
        bounds = [(self._params["min_weight"], self._params["max_weight"]) for _ in range(n_assets)]
        
        # 权重和为1的约束
        weight_constraint = {
            'type': 'eq',
            'fun': lambda weights: np.sum(weights) - 1.0
        }
        
        # 合并约束条件
        all_constraints = [weight_constraint]
        
        # 添加自定义约束
        if constraints:
            # 处理最大权重约束
            if 'max_weight' in constraints:
                self._params['max_weight'] = min(self._params['max_weight'], constraints['max_weight'])
                bounds = [(self._params["min_weight"], self._params["max_weight"]) for _ in range(n_assets)]
            
            # 处理行业约束
            if 'sector_constraints' in constraints and constraints['sector_constraints'] is not None:
                sector_data = constraints['sector_data']
                sector_limits = constraints['sector_constraints']
                
                for sector, limit in sector_limits.items():
                    # 创建行业暴露约束
                    sector_assets = [asset for asset in self._assets if sector_data.get(asset) == sector]
                    sector_indices = [self._assets.index(asset) for asset in sector_assets]
                    
                    if sector_indices:
                        sector_constraint = {
                            'type': 'ineq',
                            'fun': lambda w, indices=sector_indices, limit=limit: limit - sum(w[i] for i in indices)
                        }
                        all_constraints.append(sector_constraint)
        
        try:
            # 执行优化
            optimization_result = minimize(
                self._negative_diversification_ratio,
                initial_weights,
                method='SLSQP',
                bounds=bounds,
                constraints=all_constraints,
                options={'disp': False, 'maxiter': 1000}
            )
            
            if not optimization_result.success:
                raise OptimizationFailedException(
                    f"最大分散化优化失败: {optimization_result.message}"
                )
            
            # 提取最优权重
            optimal_weights = optimization_result.x
            
            # 确保权重和为1（处理数值误差）
            optimal_weights = optimal_weights / np.sum(optimal_weights)
            
            # 将权重转换为pandas Series
            self._weights = pd.Series(optimal_weights, index=self._assets)
            
            # 计算投资组合指标
            self._metrics = self.calculate_metrics()
            
            # 标记优化已完成
            self._is_optimized = True
            
            return self._weights
            
        except Exception as e:
            if isinstance(e, PortfolioOptimizationException):
                raise
            else:
                raise OptimizationFailedException(f"优化过程中发生错误: {str(e)}")
    
    def calculate_metrics(self) -> Dict[str, Any]:
        """
        计算投资组合的各项指标
        
        返回:
            包含各种指标的字典
        """
        if not self._is_optimized or self._weights is None:
            return {}
        
        metrics = {}
        
        # 获取权重向量
        weights = self._weights.values
        
        # 计算投资组合波动率
        portfolio_variance = weights @ self._covariance_matrix.values @ weights
        portfolio_volatility = np.sqrt(portfolio_variance)
        metrics['volatility'] = portfolio_volatility
        
        # 计算加权波动率之和
        weighted_volatility_sum = np.dot(weights, self._asset_volatilities)
        
        # 计算分散化比率
        diversification_ratio = weighted_volatility_sum / portfolio_volatility
        metrics['diversification_ratio'] = diversification_ratio
        
        # 如果有预期收益数据，计算预期收益率
        if self._expected_returns is not None:
            expected_return = np.dot(weights, self._expected_returns)
            metrics['expected_return'] = expected_return
            
            # 计算夏普比率（假设无风险利率为0）
            risk_free_rate = self._params.get('risk_free_rate', 0.0)
            sharpe_ratio = (expected_return - risk_free_rate) / portfolio_volatility
            metrics['sharpe_ratio'] = sharpe_ratio
        
        # 计算有效资产数量 (ENC)
        # ENC = 1 / sum(w_i^2)，表示投资组合中有效资产的数量
        effective_n = 1 / np.sum(np.square(weights))
        metrics['effective_assets'] = effective_n
        
        # 各资产的风险贡献
        marginal_risk_contribution = self._covariance_matrix.values @ weights
        risk_contribution = weights * marginal_risk_contribution / portfolio_volatility
        
        # 风险贡献百分比
        risk_contribution_pct = risk_contribution / np.sum(risk_contribution)
        metrics['risk_contribution'] = pd.Series(risk_contribution, index=self._assets)
        metrics['risk_contribution_pct'] = pd.Series(risk_contribution_pct, index=self._assets)
        
        # 计算集中度指标
        # 赫芬达尔-赫希曼指数 (HHI)，权重平方和
        hhi = np.sum(np.square(weights))
        metrics['hhi'] = hhi
        
        # 基尼系数，表示权重分布的不平等程度
        sorted_weights = np.sort(weights)
        cumsum_weights = np.cumsum(sorted_weights)
        n = len(weights)
        gini = 1 - (2 / n) * np.sum((n - np.arange(n)) * sorted_weights / np.sum(sorted_weights))
        metrics['gini'] = gini
        
        return metrics 