"""
权重可视化模块

该模块提供了用于可视化展示投资组合权重分布和特性的工具。
"""

from typing import Dict, List, Optional, Union, Tuple, Any
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap


def plot_weights(
    weights: Union[Dict[str, float], pd.Series],
    figsize: Tuple[int, int] = (10, 6),
    title: str = "投资组合权重分布",
    sort_values: bool = True,
    color: str = "steelblue",
    threshold: Optional[float] = None,
    show_other: bool = True,
    horizontal: bool = False,
    show_values: bool = True,
    show_grid: bool = True,
    text_color: str = "black",
    output_file: Optional[str] = None,
    **kwargs
) -> plt.Figure:
    """
    绘制投资组合权重柱状图

    参数:
        weights: 资产权重字典或Series
        figsize: 图表大小
        title: 图表标题
        sort_values: 是否按权重值排序
        color: 柱状图颜色
        threshold: 显示的权重阈值，低于此值的资产将被合并为"其他"
        show_other: 是否显示"其他"类别（当使用threshold时）
        horizontal: 是否使用水平柱状图
        show_values: 是否在柱子上显示权重值
        show_grid: 是否显示网格线
        text_color: 文本颜色
        output_file: 输出文件路径，如果不为None则保存图表
        **kwargs: 传递给plt.bar的其他参数

    返回:
        matplotlib Figure 对象
    """
    # 处理输入数据
    if isinstance(weights, dict):
        weights_series = pd.Series(weights)
    else:
        weights_series = weights.copy()
    
    # 过滤掉零权重或接近零的权重
    weights_series = weights_series[weights_series > 1e-10]
    
    # 应用阈值
    if threshold is not None and threshold > 0:
        other_weights = weights_series[weights_series < threshold]
        weights_series = weights_series[weights_series >= threshold]
        
        if not other_weights.empty and show_other:
            weights_series['其他'] = other_weights.sum()
    
    # 排序
    if sort_values:
        weights_series = weights_series.sort_values(ascending=False)
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 绘制柱状图
    if horizontal:
        bars = ax.barh(weights_series.index, weights_series.values, color=color, **kwargs)
        ax.set_xlabel('权重', fontsize=12)
        ax.set_ylabel('资产', fontsize=12)
    else:
        bars = ax.bar(weights_series.index, weights_series.values, color=color, **kwargs)
        ax.set_xlabel('资产', fontsize=12)
        ax.set_ylabel('权重', fontsize=12)
        # 旋转X轴标签
        plt.xticks(rotation=45, ha='right')
    
    # 添加值标签
    if show_values:
        for bar in bars:
            if horizontal:
                height = bar.get_width()
                ax.text(height + 0.01, bar.get_y() + bar.get_height()/2, 
                        f'{height:.2%}', ha='left', va='center', 
                        fontsize=8, color=text_color)
            else:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2, height + 0.01, 
                        f'{height:.2%}', ha='center', va='bottom', 
                        fontsize=8, color=text_color)
    
    # 设置标题
    ax.set_title(title, fontsize=14)
    
    # 添加网格线
    if show_grid:
        if horizontal:
            ax.grid(axis='x', linestyle='--', alpha=0.7)
        else:
            ax.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, bbox_inches='tight', dpi=300)
    
    return fig


def plot_weights_heatmap(
    weights: Union[Dict[str, float], pd.Series],
    asset_info: pd.DataFrame,
    x_field: str,
    y_field: Optional[str] = None,
    figsize: Tuple[int, int] = (12, 8),
    title: str = "投资组合权重热力图",
    colormap: str = "YlGnBu",
    show_values: bool = True,
    text_color: str = "black",
    normalize_rows: bool = False,
    output_file: Optional[str] = None
) -> plt.Figure:
    """
    绘制投资组合权重热力图

    参数:
        weights: 资产权重字典或Series
        asset_info: 包含资产信息的DataFrame
        x_field: 用于x轴的字段（例如'行业'）
        y_field: 用于y轴的字段，如果为None则只使用x_field
        figsize: 图表大小
        title: 图表标题
        colormap: 热力图色彩映射
        show_values: 是否在单元格中显示权重值
        text_color: 单元格中文本的颜色
        normalize_rows: 是否按行归一化权重
        output_file: 输出文件路径，如果不为None则保存图表

    返回:
        matplotlib Figure 对象
    """
    # 处理输入数据
    if isinstance(weights, dict):
        weights_series = pd.Series(weights)
    else:
        weights_series = weights.copy()
    
    # 检查是否所有权重资产都在asset_info中
    missing_assets = set(weights_series.index) - set(asset_info.index)
    if missing_assets:
        raise ValueError(f"以下资产在asset_info中不存在: {missing_assets}")
    
    # 过滤资产信息
    selected_assets = asset_info.loc[weights_series.index].copy()
    selected_assets['权重'] = weights_series
    
    if y_field is None:
        # 按x_field分组计算权重和
        grouped = selected_assets.groupby(x_field)['权重'].sum().reset_index()
        
        # 创建热力图数据
        heatmap_data = pd.pivot_table(data=grouped, 
                                      values='权重', 
                                      index=None, 
                                      columns=x_field)
        
        # 创建图表
        fig, ax = plt.subplots(figsize=figsize)
        
        # 绘制热力图
        sns.heatmap(heatmap_data, cmap=colormap, linewidths=.5, 
                   annot=show_values, fmt='.2%', cbar_kws={'label': '权重'}, 
                   annot_kws={'color': text_color}, ax=ax)
        
        ax.set_title(title, fontsize=14)
    else:
        # 按x_field和y_field分组计算权重和
        grouped = selected_assets.groupby([y_field, x_field])['权重'].sum().reset_index()
        
        # 创建热力图数据
        heatmap_data = pd.pivot_table(data=grouped, 
                                     values='权重', 
                                     index=y_field, 
                                     columns=x_field)
        
        # 按行归一化
        if normalize_rows:
            row_sums = heatmap_data.sum(axis=1)
            heatmap_data = heatmap_data.div(row_sums, axis=0)
        
        # 创建图表
        fig, ax = plt.subplots(figsize=figsize)
        
        # 绘制热力图
        sns.heatmap(heatmap_data, cmap=colormap, linewidths=.5, 
                   annot=show_values, fmt='.2%', cbar_kws={'label': '权重'}, 
                   annot_kws={'color': text_color}, ax=ax)
        
        ax.set_title(title, fontsize=14)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, bbox_inches='tight', dpi=300)
    
    return fig


def plot_weight_comparison(
    weight_dict: Dict[str, Union[Dict[str, float], pd.Series]],
    figsize: Tuple[int, int] = (12, 8),
    title: str = "投资组合权重比较",
    sort_by: Optional[str] = None,
    threshold: Optional[float] = 0.02,
    show_other: bool = True,
    horizontal: bool = False,
    color_palette: str = "tab10",
    show_grid: bool = True,
    output_file: Optional[str] = None
) -> plt.Figure:
    """
    比较不同投资组合权重分布

    参数:
        weight_dict: 包含多个权重分布的字典，格式为{名称: 权重}
        figsize: 图表大小
        title: 图表标题
        sort_by: 用于排序的权重分布名称
        threshold: 显示的权重阈值，低于此值的资产将被合并为"其他"
        show_other: 是否显示"其他"类别（当使用threshold时）
        horizontal: 是否使用水平柱状图
        color_palette: 色彩映射名称
        show_grid: 是否显示网格线
        output_file: 输出文件路径，如果不为None则保存图表

    返回:
        matplotlib Figure 对象
    """
    # 转换所有权重为Series
    weight_series = {}
    all_assets = set()
    
    for name, weights in weight_dict.items():
        if isinstance(weights, dict):
            weight_series[name] = pd.Series(weights)
        else:
            weight_series[name] = weights.copy()
        
        # 收集所有资产名称
        all_assets.update(weight_series[name].index)
    
    # 应用阈值
    if threshold is not None and threshold > 0:
        for name, weights in weight_series.items():
            other_weights = weights[weights < threshold]
            weight_series[name] = weights[weights >= threshold]
            
            if not other_weights.empty and show_other:
                weight_series[name]['其他'] = other_weights.sum()
    
    # 创建包含所有组合的DataFrame
    df = pd.DataFrame({k: pd.Series(v) for k, v in weight_series.items()})
    df = df.fillna(0)
    
    # 排序
    if sort_by is not None and sort_by in weight_series:
        df = df.reindex(df[sort_by].sort_values(ascending=False).index)
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 设置颜色
    colors = sns.color_palette(color_palette, n_colors=len(weight_series))
    
    # 绘制柱状图
    if horizontal:
        df.T.plot(kind='barh', ax=ax, color=colors)
        ax.set_xlabel('权重', fontsize=12)
        ax.set_ylabel('资产', fontsize=12)
    else:
        df.plot(kind='bar', ax=ax, color=colors)
        ax.set_xlabel('资产', fontsize=12)
        ax.set_ylabel('权重', fontsize=12)
        plt.xticks(rotation=45, ha='right')
    
    # 设置标题
    ax.set_title(title, fontsize=14)
    
    # 添加网格线
    if show_grid:
        ax.grid(linestyle='--', alpha=0.7)
    
    # 添加图例
    ax.legend(title='投资组合')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, bbox_inches='tight', dpi=300)
    
    return fig


def plot_weight_pie(
    weights: Union[Dict[str, float], pd.Series],
    figsize: Tuple[int, int] = (10, 10),
    title: str = "投资组合权重分布",
    threshold: Optional[float] = 0.03,
    show_other: bool = True,
    color_palette: str = "tab20c",
    explode_largest: bool = True,
    explode_all: bool = False,
    explode_value: float = 0.1,
    show_values: bool = True,
    show_labels: bool = True,
    label_fmt: str = "{0} ({1:.1%})",
    output_file: Optional[str] = None
) -> plt.Figure:
    """
    绘制投资组合权重饼图

    参数:
        weights: 资产权重字典或Series
        figsize: 图表大小
        title: 图表标题
        threshold: 显示的权重阈值，低于此值的资产将被合并为"其他"
        show_other: 是否显示"其他"类别（当使用threshold时）
        color_palette: 色彩映射名称
        explode_largest: 是否突出显示最大权重的资产
        explode_all: 是否突出显示所有资产
        explode_value: 突出显示的程度
        show_values: 是否显示百分比值
        show_labels: 是否显示资产标签
        label_fmt: 标签格式，{0}为资产名，{1}为权重百分比
        output_file: 输出文件路径，如果不为None则保存图表

    返回:
        matplotlib Figure 对象
    """
    # 处理输入数据
    if isinstance(weights, dict):
        weights_series = pd.Series(weights)
    else:
        weights_series = weights.copy()
    
    # 过滤掉零权重或接近零的权重
    weights_series = weights_series[weights_series > 1e-10]
    
    # 排序
    weights_series = weights_series.sort_values(ascending=False)
    
    # 应用阈值
    if threshold is not None and threshold > 0:
        other_weights = weights_series[weights_series < threshold]
        weights_series = weights_series[weights_series >= threshold]
        
        if not other_weights.empty and show_other:
            weights_series['其他'] = other_weights.sum()
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 设置颜色
    colors = sns.color_palette(color_palette, n_colors=len(weights_series))
    
    # 设置突出显示
    explode = None
    if explode_largest or explode_all:
        explode = np.zeros(len(weights_series))
        
        if explode_largest:
            explode[0] = explode_value
        
        if explode_all:
            explode = np.ones(len(weights_series)) * explode_value
    
    # 创建标签
    if show_labels:
        labels = [label_fmt.format(idx, val) for idx, val in weights_series.items()]
    else:
        labels = None
    
    # 绘制饼图
    wedges, texts, autotexts = ax.pie(
        weights_series.values,
        explode=explode,
        labels=labels,
        colors=colors,
        autopct='%1.1f%%' if show_values else None,
        shadow=False,
        startangle=90,
        wedgeprops={'linewidth': 1, 'edgecolor': 'white'}
    )
    
    # 设置文本属性
    if show_values:
        for autotext in autotexts:
            autotext.set_fontsize(9)
            autotext.set_color('white')
    
    if show_labels:
        for text in texts:
            text.set_fontsize(9)
    
    # 设置标题
    ax.set_title(title, fontsize=14)
    
    # 确保饼图是圆形的
    ax.axis('equal')
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, bbox_inches='tight', dpi=300)
    
    return fig 