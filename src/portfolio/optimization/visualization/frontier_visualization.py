"""
有效前沿可视化模块

该模块提供了用于可视化展示投资组合有效前沿和风险-收益空间的工具。
"""

from typing import Dict, List, Optional, Union, Tuple, Any, Callable
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap
from .. import MeanVarianceOptimizer


def plot_efficient_frontier(
    optimizer: MeanVarianceOptimizer,
    expected_returns: Union[pd.Series, np.ndarray],
    covariance_matrix: Union[pd.DataFrame, np.ndarray],
    risk_free_rate: float = 0.0,
    num_portfolios: int = 50,
    figsize: Tuple[int, int] = (10, 6),
    title: str = "有效前沿",
    show_assets: bool = True,
    show_optimal: bool = True,
    show_tangent: bool = True,
    show_cml: bool = True,
    show_min_vol: bool = True,
    assets_color: str = "orangered",
    frontier_color: str = "steelblue",
    optimal_color: str = "green",
    min_vol_color: str = "purple",
    tangent_color: str = "gold",
    asset_labels: bool = False,
    annotate_points: bool = True,
    risk_measure: str = "volatility",
    asset_names: Optional[List[str]] = None,
    output_file: Optional[str] = None,
    **kwargs
) -> plt.Figure:
    """
    绘制投资组合有效前沿

    参数:
        optimizer: 均值方差优化器实例
        expected_returns: 预期收益率向量
        covariance_matrix: 协方差矩阵
        risk_free_rate: 无风险利率
        num_portfolios: 有效前沿上的投资组合数量
        figsize: 图表大小
        title: 图表标题
        show_assets: 是否在图上显示单个资产
        show_optimal: 是否显示最优投资组合
        show_tangent: 是否显示切点投资组合
        show_cml: 是否显示资本市场线
        show_min_vol: 是否显示最小波动率投资组合
        assets_color: 单个资产点的颜色
        frontier_color: 有效前沿的颜色
        optimal_color: 最优投资组合点的颜色
        min_vol_color: 最小波动率投资组合点的颜色
        tangent_color: 切点投资组合点的颜色
        asset_labels: 是否显示资产标签
        annotate_points: 是否标注特殊点
        risk_measure: 风险度量，'volatility'或'variance'
        asset_names: 资产名称列表，用于显示资产标签
        output_file: 输出文件路径，如果不为None则保存图表
        **kwargs: 传递给plt.plot的其他参数

    返回:
        matplotlib Figure 对象
    """
    # 处理输入数据
    if isinstance(expected_returns, pd.Series):
        asset_names = expected_returns.index.tolist() if asset_names is None else asset_names
        expected_returns_array = expected_returns.values
    else:
        expected_returns_array = expected_returns
        if asset_names is None:
            asset_names = [f"资产{i+1}" for i in range(len(expected_returns))]
    
    if isinstance(covariance_matrix, pd.DataFrame):
        covariance_matrix_array = covariance_matrix.values
    else:
        covariance_matrix_array = covariance_matrix
    
    # 创建DataFrame和Series用于优化器
    expected_returns_series = pd.Series(expected_returns_array, index=asset_names)
    covariance_matrix_df = pd.DataFrame(covariance_matrix_array, index=asset_names, columns=asset_names)
    
    # 生成有效前沿上的投资组合
    frontier_returns = np.linspace(
        np.min(expected_returns_array),
        np.max(expected_returns_array),
        num_portfolios
    )
    
    # 计算有效前沿上的投资组合权重
    frontier_weights = []
    frontier_risks = []
    is_frontier_point = []
    
    # 对每个目标收益率优化组合
    for target_return in frontier_returns:
        try:
            weights = optimizer.optimize(
                expected_returns=expected_returns_series,
                covariance_matrix=covariance_matrix_df,
                optimization_target="min_risk",
                constraints={"target_return": target_return}
            )
            
            # 计算投资组合风险
            if isinstance(weights, pd.Series):
                weights_array = weights.values
            else:
                weights_array = np.array(weights)
            
            # 计算组合风险
            portfolio_variance = weights_array.T @ covariance_matrix_array @ weights_array
            
            if risk_measure.lower() == 'volatility':
                portfolio_risk = np.sqrt(portfolio_variance)
            else:
                portfolio_risk = portfolio_variance
            
            frontier_weights.append(weights_array)
            frontier_risks.append(portfolio_risk)
            is_frontier_point.append(True)
        except Exception as e:
            # 有些组合可能无法实现
            frontier_weights.append(None)
            frontier_risks.append(None)
            is_frontier_point.append(False)
    
    # 过滤掉无法实现的组合
    valid_indices = [i for i, valid in enumerate(is_frontier_point) if valid]
    valid_returns = [frontier_returns[i] for i in valid_indices]
    valid_risks = [frontier_risks[i] for i in valid_indices]
    
    # 计算单个资产的风险和收益
    asset_risks = []
    for i in range(len(expected_returns_array)):
        if risk_measure.lower() == 'volatility':
            asset_risks.append(np.sqrt(covariance_matrix_array[i, i]))
        else:
            asset_risks.append(covariance_matrix_array[i, i])
    
    # 计算最小波动率投资组合
    min_vol_weights = optimizer.optimize(
        expected_returns=expected_returns_series,
        covariance_matrix=covariance_matrix_df,
        optimization_target="min_risk"
    )
    
    if isinstance(min_vol_weights, pd.Series):
        min_vol_weights_array = min_vol_weights.values
    else:
        min_vol_weights_array = np.array(min_vol_weights)
    
    min_vol_variance = min_vol_weights_array.T @ covariance_matrix_array @ min_vol_weights_array
    if risk_measure.lower() == 'volatility':
        min_vol_risk = np.sqrt(min_vol_variance)
    else:
        min_vol_risk = min_vol_variance
    
    min_vol_return = np.sum(min_vol_weights_array * expected_returns_array)
    
    # 计算切点投资组合（最大夏普比率）
    if risk_free_rate is not None:
        tangent_weights = optimizer.optimize(
            expected_returns=expected_returns_series,
            covariance_matrix=covariance_matrix_df,
            optimization_target="sharpe",
            constraints={"risk_free_rate": risk_free_rate}
        )
        
        if isinstance(tangent_weights, pd.Series):
            tangent_weights_array = tangent_weights.values
        else:
            tangent_weights_array = np.array(tangent_weights)
        
        tangent_variance = tangent_weights_array.T @ covariance_matrix_array @ tangent_weights_array
        if risk_measure.lower() == 'volatility':
            tangent_risk = np.sqrt(tangent_variance)
        else:
            tangent_risk = tangent_variance
        
        tangent_return = np.sum(tangent_weights_array * expected_returns_array)
        tangent_sharpe = (tangent_return - risk_free_rate) / (np.sqrt(tangent_variance) if risk_measure.lower() == 'volatility' else tangent_variance)
    else:
        tangent_risk = None
        tangent_return = None
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 绘制有效前沿
    ax.plot(valid_risks, valid_returns, label='有效前沿', color=frontier_color, linestyle='-', **kwargs)
    
    # 绘制单个资产
    if show_assets:
        ax.scatter(asset_risks, expected_returns_array, color=assets_color, marker='o', s=50, label='单个资产')
        
        # 添加资产标签
        if asset_labels and asset_names:
            for i, txt in enumerate(asset_names):
                ax.annotate(txt, (asset_risks[i], expected_returns_array[i]), fontsize=8,
                           xytext=(5, 5), textcoords='offset points')
    
    # 绘制最小波动率投资组合
    if show_min_vol:
        ax.scatter(min_vol_risk, min_vol_return, color=min_vol_color, marker='*', s=100, label='最小波动率组合')
        
        if annotate_points:
            ax.annotate(f'最小波动率\n(风险: {min_vol_risk:.4f}, 收益: {min_vol_return:.4f})',
                       (min_vol_risk, min_vol_return), fontsize=8,
                       xytext=(15, -15), textcoords='offset points', 
                       arrowprops=dict(arrowstyle='->', lw=1.5))
    
    # 绘制切点投资组合
    if show_tangent and tangent_risk is not None:
        ax.scatter(tangent_risk, tangent_return, color=tangent_color, marker='*', s=100, label='切点组合')
        
        if annotate_points:
            ax.annotate(f'切点组合\n(风险: {tangent_risk:.4f}, 收益: {tangent_return:.4f}, 夏普: {tangent_sharpe:.4f})',
                       (tangent_risk, tangent_return), fontsize=8,
                       xytext=(15, 15), textcoords='offset points', 
                       arrowprops=dict(arrowstyle='->', lw=1.5))
    
    # 绘制资本市场线
    if show_cml and tangent_risk is not None and risk_free_rate is not None:
        max_risk = max(asset_risks + valid_risks) * 1.2
        cml_risks = np.linspace(0, max_risk, 100)
        cml_slope = (tangent_return - risk_free_rate) / tangent_risk
        cml_returns = risk_free_rate + cml_slope * cml_risks
        
        ax.plot(cml_risks, cml_returns, label='资本市场线', color='black', linestyle='--')
        
        # 绘制无风险资产点
        ax.scatter(0, risk_free_rate, color='black', marker='o', s=30)
        ax.annotate('无风险资产', (0, risk_free_rate), fontsize=8,
                   xytext=(5, 5), textcoords='offset points')
    
    # 设置标签和标题
    if risk_measure.lower() == 'volatility':
        ax.set_xlabel('波动率', fontsize=12)
    else:
        ax.set_xlabel('方差', fontsize=12)
    ax.set_ylabel('预期收益率', fontsize=12)
    ax.set_title(title, fontsize=14)
    
    # 添加图例
    ax.legend(loc='best')
    
    # 添加网格线
    ax.grid(True, linestyle='--', alpha=0.5)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, bbox_inches='tight', dpi=300)
    
    return fig


def plot_risk_return_space(
    optimizer: MeanVarianceOptimizer,
    expected_returns: Union[pd.Series, np.ndarray],
    covariance_matrix: Union[pd.DataFrame, np.ndarray],
    num_portfolios: int = 1000,
    figsize: Tuple[int, int] = (12, 8),
    title: str = "投资组合风险-收益空间",
    colormap: str = "viridis",
    show_assets: bool = True,
    show_sharpe: bool = True,
    show_min_vol: bool = True,
    show_efficient: bool = True,
    risk_free_rate: float = 0.0,
    asset_names: Optional[List[str]] = None,
    output_file: Optional[str] = None,
    random_weights_generator: Optional[Callable] = None,
    **kwargs
) -> plt.Figure:
    """
    绘制投资组合风险-收益空间

    参数:
        optimizer: 均值方差优化器实例
        expected_returns: 预期收益率向量
        covariance_matrix: 协方差矩阵
        num_portfolios: 随机投资组合数量
        figsize: 图表大小
        title: 图表标题
        colormap: 色彩映射名称
        show_assets: 是否在图上显示单个资产
        show_sharpe: 是否根据夏普比率着色
        show_min_vol: 是否显示最小波动率投资组合
        show_efficient: 是否显示有效前沿
        risk_free_rate: 无风险利率（用于计算夏普比率）
        asset_names: 资产名称列表，用于显示资产标签
        output_file: 输出文件路径，如果不为None则保存图表
        random_weights_generator: 自定义随机权重生成函数
        **kwargs: 传递给plt.scatter的其他参数

    返回:
        matplotlib Figure 对象
    """
    # 处理输入数据
    if isinstance(expected_returns, pd.Series):
        asset_names = expected_returns.index.tolist() if asset_names is None else asset_names
        expected_returns_array = expected_returns.values
    else:
        expected_returns_array = expected_returns
        if asset_names is None:
            asset_names = [f"资产{i+1}" for i in range(len(expected_returns))]
    
    if isinstance(covariance_matrix, pd.DataFrame):
        covariance_matrix_array = covariance_matrix.values
    else:
        covariance_matrix_array = covariance_matrix
    
    # 创建DataFrame和Series用于优化器
    expected_returns_series = pd.Series(expected_returns_array, index=asset_names)
    covariance_matrix_df = pd.DataFrame(covariance_matrix_array, index=asset_names, columns=asset_names)
    
    n_assets = len(expected_returns_array)
    
    # 定义默认的随机权重生成函数
    if random_weights_generator is None:
        def random_weights_generator(n):
            weights = np.random.random(n)
            return weights / np.sum(weights)
    
    # 生成随机投资组合
    portfolio_returns = []
    portfolio_volatilities = []
    portfolio_sharpe_ratios = []
    
    for _ in range(num_portfolios):
        # 生成随机权重
        weights = random_weights_generator(n_assets)
        
        # 计算组合收益率
        returns = np.sum(weights * expected_returns_array)
        
        # 计算组合波动率
        volatility = np.sqrt(weights.T @ covariance_matrix_array @ weights)
        
        # 计算夏普比率
        sharpe_ratio = (returns - risk_free_rate) / volatility if volatility > 0 else 0
        
        portfolio_returns.append(returns)
        portfolio_volatilities.append(volatility)
        portfolio_sharpe_ratios.append(sharpe_ratio)
    
    # 计算单个资产的风险和收益
    asset_volatilities = np.sqrt(np.diag(covariance_matrix_array))
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 为随机投资组合着色
    if show_sharpe:
        sc = ax.scatter(portfolio_volatilities, portfolio_returns, c=portfolio_sharpe_ratios, cmap=colormap,
                        edgecolors='none', alpha=0.7, **kwargs)
        cb = plt.colorbar(sc)
        cb.set_label('夏普比率')
    else:
        sc = ax.scatter(portfolio_volatilities, portfolio_returns, c='skyblue',
                        edgecolors='none', alpha=0.7, **kwargs)
    
    # 绘制单个资产
    if show_assets:
        ax.scatter(asset_volatilities, expected_returns_array, 
                   marker='o', s=100, c='red', edgecolors='black', 
                   label='单个资产')
        
        # 添加资产标签
        if asset_names:
            for i, txt in enumerate(asset_names):
                ax.annotate(txt, (asset_volatilities[i], expected_returns_array[i]), fontsize=8,
                           xytext=(5, 5), textcoords='offset points')
    
    # 计算并绘制最小波动率投资组合
    if show_min_vol:
        min_vol_weights = optimizer.optimize(
            expected_returns=expected_returns_series,
            covariance_matrix=covariance_matrix_df,
            optimization_target="min_risk"
        )
        
        if isinstance(min_vol_weights, pd.Series):
            min_vol_weights_array = min_vol_weights.values
        else:
            min_vol_weights_array = np.array(min_vol_weights)
        
        min_vol_return = np.sum(min_vol_weights_array * expected_returns_array)
        min_vol_volatility = np.sqrt(min_vol_weights_array.T @ covariance_matrix_array @ min_vol_weights_array)
        
        ax.scatter(min_vol_volatility, min_vol_return, 
                   marker='*', s=200, c='green', edgecolors='black', 
                   label='最小波动率组合')
        
        ax.annotate('最小波动率组合', (min_vol_volatility, min_vol_return), fontsize=8,
                   xytext=(10, 10), textcoords='offset points',
                   arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.2'))
    
    # 绘制有效前沿
    if show_efficient:
        target_returns = np.linspace(min(expected_returns_array), max(expected_returns_array), 50)
        efficient_volatilities = []
        efficient_returns = []
        
        for target_return in target_returns:
            try:
                weights = optimizer.optimize(
                    expected_returns=expected_returns_series,
                    covariance_matrix=covariance_matrix_df,
                    optimization_target="min_risk",
                    constraints={"target_return": target_return}
                )
                
                if isinstance(weights, pd.Series):
                    weights_array = weights.values
                else:
                    weights_array = np.array(weights)
                
                volatility = np.sqrt(weights_array.T @ covariance_matrix_array @ weights_array)
                
                efficient_volatilities.append(volatility)
                efficient_returns.append(target_return)
            except Exception:
                # 跳过无法实现的收益率目标
                pass
        
        ax.plot(efficient_volatilities, efficient_returns, 
                'b--', linewidth=2, label='有效前沿')
    
    # 添加标签和标题
    ax.set_xlabel('投资组合波动率', fontsize=12)
    ax.set_ylabel('投资组合预期收益率', fontsize=12)
    ax.set_title(title, fontsize=14)
    
    # 添加图例
    ax.legend(loc='best')
    
    # 添加网格线
    ax.grid(True, linestyle='--', alpha=0.5)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, bbox_inches='tight', dpi=300)
    
    return fig 