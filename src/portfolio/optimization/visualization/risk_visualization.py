"""
风险可视化模块

该模块提供了用于可视化展示投资组合风险分布和特征的工具。
"""

from typing import Dict, List, Optional, Union, Tuple, Any
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.cm as cm
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap
from ..analysis import RiskAnalysisResult


def plot_risk(
    weights: Union[Dict[str, float], RiskAnalysisResult],
    covariance_matrix: Optional[np.ndarray] = None,
    asset_names: Optional[List[str]] = None,
    figsize: Tuple[int, int] = (10, 8),
    title: str = "投资组合风险分解",
    color: str = "viridis",
    show_top_n: Optional[int] = None,
    threshold: Optional[float] = 0.02,
    show_percentage: bool = True,
    text_color: str = "black",
    output_file: Optional[str] = None,
    **kwargs
) -> plt.Figure:
    """
    绘制投资组合风险分解图

    参数:
        weights: 资产权重字典 {资产名称: 权重} 或 RiskAnalysisResult 实例
        covariance_matrix: 协方差矩阵（仅在传入纯权重字典时需要）
        asset_names: 资产名称列表（仅在传入纯权重字典时需要）
        figsize: 图表大小
        title: 图表标题
        color: 色彩映射名称
        show_top_n: 仅显示风险贡献最大的前N个资产
        threshold: 仅显示风险贡献百分比大于阈值的资产
        show_percentage: 是否在图表上显示百分比值
        text_color: 文本颜色
        output_file: 输出文件路径，如果不为None则保存图表
        **kwargs: 传递给plt.pie的其他参数

    返回:
        matplotlib Figure 对象
    """
    # 从分析结果对象中提取风险贡献
    if isinstance(weights, RiskAnalysisResult):
        risk_contrib_pct = weights.risk_contribution_percent
        total_risk = weights.portfolio_risk
    else:
        # 需要计算风险贡献
        if covariance_matrix is None or asset_names is None:
            raise ValueError("当传入纯权重字典时，必须提供协方差矩阵和资产名称列表。")
        
        # 转换权重字典为向量
        weight_vector = np.array([weights.get(asset, 0) for asset in asset_names])
        
        # 计算投资组合风险
        portfolio_variance = weight_vector.T @ covariance_matrix @ weight_vector
        portfolio_volatility = np.sqrt(portfolio_variance)
        
        # 计算边际风险贡献
        marginal_contrib = (covariance_matrix @ weight_vector) / portfolio_volatility
        
        # 计算风险贡献
        risk_contrib = weight_vector * marginal_contrib
        
        # 计算风险贡献百分比
        risk_contrib_pct = dict(zip(asset_names, risk_contrib / portfolio_volatility))
        total_risk = portfolio_volatility

    # 如果风险贡献为空，返回空图
    if not risk_contrib_pct:
        fig, ax = plt.subplots(figsize=figsize)
        ax.text(0.5, 0.5, "无风险贡献数据", horizontalalignment='center',
                verticalalignment='center', transform=ax.transAxes, fontsize=14)
        plt.tight_layout()
        if output_file:
            plt.savefig(output_file, bbox_inches='tight', dpi=300)
        return fig

    # 应用显示筛选条件
    filtered_contrib = risk_contrib_pct.copy()
    
    # 应用阈值过滤
    if threshold is not None:
        filtered_contrib = {k: v for k, v in filtered_contrib.items() if v >= threshold}
    
    # 转换为DataFrame便于操作
    df = pd.DataFrame(list(filtered_contrib.items()), columns=['Asset', 'Contribution'])
    df = df.sort_values('Contribution', ascending=False)
    
    # 如果需要，只保留前N个资产
    if show_top_n is not None and len(df) > show_top_n:
        top_assets = df.iloc[:show_top_n]
        other_contrib = df.iloc[show_top_n:]['Contribution'].sum()
        
        # 如果其他资产贡献不为0，则添加"其他"类别
        if other_contrib > 0:
            top_assets = pd.concat([
                top_assets, 
                pd.DataFrame([['其他', other_contrib]], columns=['Asset', 'Contribution'])
            ])
        df = top_assets

    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 设置颜色映射
    cmap = plt.cm.get_cmap(color)(np.linspace(0, 1, len(df)))
    
    # 设置饼图的文本格式
    if show_percentage:
        def autopct_format(pct):
            return f'{pct:.1f}%' if pct >= 1 else ''
        autopct = autopct_format
    else:
        autopct = None
    
    # 绘制饼图
    wedges, texts, autotexts = ax.pie(
        df['Contribution'], 
        labels=df['Asset'], 
        autopct=autopct,
        colors=cmap,
        shadow=False, 
        startangle=90,
        textprops={'color': text_color},
        **kwargs
    )
    
    # 保持饼图为圆形
    ax.axis('equal')
    
    # 设置标题，包括总风险值
    full_title = f"{title}\n(总风险: {total_risk:.6f})"
    ax.set_title(full_title, fontsize=14)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, bbox_inches='tight', dpi=300)
    
    return fig


def plot_risk_contribution(
    risk_data: Union[Dict[str, float], RiskAnalysisResult],
    figsize: Tuple[int, int] = (12, 6),
    title: str = "资产风险贡献",
    sort_values: bool = True,
    color: str = "steelblue",
    show_top_n: Optional[int] = None,
    show_grid: bool = True,
    output_file: Optional[str] = None,
    **kwargs
) -> plt.Figure:
    """
    绘制资产风险贡献条形图

    参数:
        risk_data: 风险贡献字典 {资产名称: 风险贡献} 或 RiskAnalysisResult 实例
        figsize: 图表大小
        title: 图表标题
        sort_values: 是否按贡献值排序
        color: 柱状图颜色
        show_top_n: 仅显示前N个贡献最大的资产
        show_grid: 显示网格线
        output_file: 输出文件路径，如果不为None则保存图表
        **kwargs: 传递给plt.bar的其他参数

    返回:
        matplotlib Figure 对象
    """
    # 从分析结果对象中提取风险贡献
    if isinstance(risk_data, RiskAnalysisResult):
        risk_contrib_dict = risk_data.risk_contribution_percent
    else:
        risk_contrib_dict = risk_data

    # 如果风险贡献为空，返回空图
    if not risk_contrib_dict:
        fig, ax = plt.subplots(figsize=figsize)
        ax.text(0.5, 0.5, "无风险贡献数据", horizontalalignment='center',
                verticalalignment='center', transform=ax.transAxes, fontsize=14)
        plt.tight_layout()
        if output_file:
            plt.savefig(output_file, bbox_inches='tight', dpi=300)
        return fig

    # 转换为DataFrame便于操作
    df = pd.DataFrame(list(risk_contrib_dict.items()), columns=['Asset', 'Contribution'])
    
    # 排序（如果需要）
    if sort_values:
        df = df.sort_values('Contribution', ascending=False)
    
    # 如果需要，只保留前N个资产
    if show_top_n is not None and len(df) > show_top_n:
        df = df.iloc[:show_top_n]

    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 绘制条形图
    bars = ax.bar(df['Asset'], df['Contribution'] * 100, color=color, **kwargs)
    
    # 在每个条形上添加数值标签
    for bar in bars:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height,
                f'{height:.1f}%', ha='center', va='bottom')
    
    # 添加标签和标题
    ax.set_xlabel('资产', fontsize=12)
    ax.set_ylabel('风险贡献 (%)', fontsize=12)
    ax.set_title(title, fontsize=14)
    
    # 旋转x轴标签以防止重叠
    plt.xticks(rotation=45, ha='right')
    
    # 添加网格线
    if show_grid:
        ax.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, bbox_inches='tight', dpi=300)
    
    return fig


def plot_risk_distribution(
    risk_data: RiskAnalysisResult,
    group_by: str,
    figsize: Tuple[int, int] = (10, 6),
    title: Optional[str] = None,
    chart_type: str = "bar",
    colormap: str = "viridis",
    sort_values: bool = True,
    show_values: bool = True,
    output_file: Optional[str] = None,
    **kwargs
) -> plt.Figure:
    """
    绘制按分组的风险分布图

    参数:
        risk_data: RiskAnalysisResult 实例
        group_by: 分组字段名称（如'industry', 'region'等）
        figsize: 图表大小
        title: 图表标题，如果为None则自动生成
        chart_type: 图表类型，'bar'为柱状图，'pie'为饼图
        colormap: 颜色映射
        sort_values: 是否按值排序
        show_values: 是否显示数值
        output_file: 输出文件路径，如果不为None则保存图表
        **kwargs: 传递给plt.bar或plt.pie的其他参数

    返回:
        matplotlib Figure 对象
    """
    # 确保提供的是RiskAnalysisResult对象
    if not isinstance(risk_data, RiskAnalysisResult):
        raise TypeError("必须提供RiskAnalysisResult实例")
    
    # 检查是否存在分组风险数据
    if not hasattr(risk_data, 'group_risk') or not risk_data.group_risk:
        raise ValueError("分析结果中没有分组风险数据")
    
    # 从分析结果对象中提取分组风险
    if group_by not in risk_data.group_risk:
        raise ValueError(f"分析结果中未找到分组 '{group_by}'，请确保提供的分组名称正确。")
    
    group_risk = risk_data.group_risk[group_by]
    
    # 如果分组风险为空，返回空图
    if not group_risk:
        fig, ax = plt.subplots(figsize=figsize)
        ax.text(0.5, 0.5, "无分组风险数据", horizontalalignment='center',
                verticalalignment='center', transform=ax.transAxes, fontsize=14)
        plt.tight_layout()
        if output_file:
            plt.savefig(output_file, bbox_inches='tight', dpi=300)
        return fig

    # 创建DataFrame并排序
    df = pd.DataFrame(list(group_risk.items()), columns=['Group', 'Risk'])
    if sort_values:
        df = df.sort_values('Risk', ascending=False)
    
    # 设置标题
    if title is None:
        title = f"按{group_by}分组的风险分布"
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 根据图表类型绘制
    if chart_type.lower() == 'bar':
        # 获取颜色映射
        cmap = plt.cm.get_cmap(colormap)
        colors = cmap(np.linspace(0, 1, len(df)))
        
        # 绘制柱状图
        bars = ax.bar(df['Group'], df['Risk'], color=colors, **kwargs)
        
        # 添加数值标签
        if show_values:
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                        f'{height:.4f}', ha='center', va='bottom')
        
        # 旋转标签
        plt.xticks(rotation=45, ha='right')
        
        # 添加轴标签
        ax.set_xlabel(group_by, fontsize=12)
        ax.set_ylabel('风险贡献', fontsize=12)
        
        # 添加网格线
        ax.grid(axis='y', linestyle='--', alpha=0.7)
    
    elif chart_type.lower() == 'pie':
        # 设置饼图的文本格式
        def autopct_format(pct):
            return f'{pct:.1f}%' if pct >= 1 else ''
        
        autopct = autopct_format if show_values else None
        
        # 绘制饼图
        ax.pie(df['Risk'], labels=df['Group'], autopct=autopct,
              colors=plt.cm.get_cmap(colormap)(np.linspace(0, 1, len(df))),
              startangle=90, **kwargs)
        ax.axis('equal')  # 保持饼图为圆形
    
    else:
        raise ValueError(f"不支持的图表类型: {chart_type}，请使用 'bar' 或 'pie'")
    
    # 设置标题
    ax.set_title(title, fontsize=14)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, bbox_inches='tight', dpi=300)
    
    return fig 