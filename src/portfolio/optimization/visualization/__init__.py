"""
优化结果可视化模块

该模块提供了一系列工具用于可视化展示投资组合优化的结果，包括：
1. 权重可视化 - 可视化投资组合权重分布和特征
2. 风险可视化 - 可视化投资组合的风险来源和分布
3. 有效前沿可视化 - 可视化投资组合有效前沿和风险-收益空间

用法示例：
```python
from src.portfolio.optimization.visualization import (
    plot_weights,
    plot_risk,
    plot_efficient_frontier,
    plot_risk_return_space
)

# 可视化投资组合权重
plot_weights(weights, title='投资组合权重分布')

# 可视化风险分布
plot_risk(weights, covariance_matrix, asset_names)

# 绘制有效前沿
plot_efficient_frontier(mean_variance_optimizer, expected_returns, covariance_matrix)

# 绘制风险-收益空间
plot_risk_return_space(mean_variance_optimizer, expected_returns, covariance_matrix)
```
"""

# 权重可视化模块
from .weight_visualization import (
    plot_weights,
    plot_weights_heatmap,
    plot_weight_comparison,
    plot_weight_pie
)

# 风险可视化模块
from .risk_visualization import (
    plot_risk,
    plot_risk_contribution,
    plot_risk_distribution
)

# 有效前沿可视化
from .frontier_visualization import (
    plot_efficient_frontier,
    plot_risk_return_space
)

# 导出主要功能
__all__ = [
    # 权重可视化
    'plot_weights',
    'plot_weights_heatmap',
    'plot_weight_comparison',
    'plot_weight_pie',
    
    # 风险可视化
    'plot_risk',
    'plot_risk_contribution',
    'plot_risk_distribution',
    
    # 有效前沿可视化
    'plot_efficient_frontier',
    'plot_risk_return_space',
] 