"""
权重分析模块

该模块提供了分析投资组合权重分布特征的工具，包括：
1. 集中度分析 - 分析权重的集中程度
2. 多样化分析 - 分析权重的分散程度
3. 换手率分析 - 分析相对于之前权重的变化
4. 分组权重分析 - 按行业/地区等分组分析权重
"""

from typing import Dict, List, Optional, Any, Tuple, Union
import numpy as np
import pandas as pd
import scipy.stats as stats
from ..analysis.interfaces import WeightAnalysisResult


def analyze_weights(
    weights: Dict[str, float],
    asset_info: Optional[pd.DataFrame] = None,
    prev_weights: Optional[Dict[str, float]] = None,
    group_by: Optional[List[str]] = None
) -> WeightAnalysisResult:
    """
    分析投资组合权重特征
    
    参数:
        weights: 资产权重字典 {资产代码: 权重}
        asset_info: 资产信息DataFrame，应包含资产代码作为索引，可能包含行业、地区等分组字段
        prev_weights: 先前的权重字典，用于计算换手率
        group_by: 分组字段列表，如['industry', 'region']等，需要与asset_info中的列名匹配
        
    返回:
        WeightAnalysisResult: 权重分析结果对象
    """
    # 检查权重总和是否接近1
    weight_sum = sum(weights.values())
    if not 0.99 <= weight_sum <= 1.01:
        # 如果不接近1，进行归一化处理
        weights = {k: v / weight_sum for k, v in weights.items()}
    
    # 计算基本权重统计
    weight_values = list(weights.values())
    weight_stats = {
        'max_weight': max(weight_values) if weight_values else 0,
        'min_weight': min(weight_values) if weight_values else 0,
        'mean_weight': np.mean(weight_values) if weight_values else 0,
        'median_weight': np.median(weight_values) if weight_values else 0,
        'std_weight': np.std(weight_values) if weight_values else 0,
        'num_assets': len(weights),
        'num_nonzero': sum(1 for w in weight_values if w > 1e-6),
        'weight_sum': sum(weight_values)
    }
    
    # 计算集中度指标
    concentration = calculate_concentration(weights)
    
    # 计算多样化指标
    diversification = calculate_diversification(weights)
    
    # 计算换手率（如果提供了先前权重）
    turnover = None
    turnover_details = {}
    if prev_weights:
        turnover, turnover_details = calculate_turnover(weights, prev_weights)
    
    # 计算分组权重（如果提供了资产信息和分组字段）
    group_weights = {}
    if asset_info is not None and group_by:
        group_weights = calculate_group_weights(weights, asset_info, group_by)
    
    # 创建并返回分析结果对象
    return WeightAnalysisResult(
        weights=weights,
        concentration=concentration,
        diversification=diversification,
        weight_stats=weight_stats,
        group_weights=group_weights,
        turnover=turnover,
        turnover_details=turnover_details
    )


def calculate_concentration(weights: Dict[str, float]) -> Dict[str, float]:
    """
    计算投资组合权重的集中度指标
    
    参数:
        weights: 资产权重字典
        
    返回:
        Dict[str, float]: 集中度指标字典
    """
    w_values = np.array(list(weights.values()))
    
    # 赫芬达尔-赫希曼指数 (HHI)
    hhi = np.sum(w_values ** 2)
    
    # 归一化HHI (0-1范围)
    n = len(w_values)
    norm_hhi = (hhi - 1/n) / (1 - 1/n) if n > 1 else 1.0
    
    # 基尼系数
    sorted_w = np.sort(w_values)
    cumsum = np.cumsum(sorted_w)
    gini = 1 - 2 * np.sum((cumsum - sorted_w / 2) * sorted_w) / np.sum(sorted_w)
    
    # 计算顶部集中度
    top1_weight = sorted_w[-1] if len(sorted_w) > 0 else 0
    top5_weight = np.sum(sorted_w[-5:]) if len(sorted_w) >= 5 else np.sum(sorted_w)
    top10_weight = np.sum(sorted_w[-10:]) if len(sorted_w) >= 10 else np.sum(sorted_w)
    
    # 计算占比50%的资产数量
    w_sum = np.sum(sorted_w)
    running_sum = 0
    for i, w in enumerate(reversed(sorted_w)):
        running_sum += w
        if running_sum >= w_sum * 0.5:
            half_concentration = (i + 1) / n
            break
    else:
        half_concentration = 1.0  # 如果所有资产都不能达到50%，则设为1.0
    
    # 计算有效资产数量 (Effective Number of Bets)
    enb = 1 / np.sum(w_values ** 2)
    
    # 计算有效资产比率
    enb_ratio = enb / n if n > 0 else 1.0
    
    return {
        'hhi': hhi,
        'normalized_hhi': norm_hhi,
        'gini': gini,
        'top1_pct': top1_weight,
        'top5_pct': top5_weight,
        'top10_pct': top10_weight,
        'half_concentration': half_concentration,
        'effective_n': enb,
        'effective_n_ratio': enb_ratio
    }


def calculate_diversification(weights: Dict[str, float]) -> Dict[str, float]:
    """
    计算投资组合权重的多样化指标
    
    参数:
        weights: 资产权重字典
        
    返回:
        Dict[str, float]: 多样化指标字典
    """
    w_values = np.array(list(weights.values()))
    n = len(w_values)
    
    # Shannon熵
    nonzero_w = w_values[w_values > 0]
    entropy = -np.sum(nonzero_w * np.log(nonzero_w))
    
    # 归一化熵 (0-1范围)
    max_entropy = np.log(n) if n > 0 else 0
    norm_entropy = entropy / max_entropy if max_entropy > 0 else 0
    
    # 计算分散度指标 (1 - HHI)
    diversification_ratio = 1 - np.sum(w_values ** 2)
    
    return {
        'entropy': entropy,
        'normalized_entropy': norm_entropy,
        'diversification_ratio': diversification_ratio
    }


def calculate_turnover(
    weights: Dict[str, float],
    prev_weights: Dict[str, float]
) -> Tuple[float, Dict[str, float]]:
    """
    计算投资组合的换手率
    
    参数:
        weights: 当前资产权重字典
        prev_weights: 先前的资产权重字典
        
    返回:
        Tuple[float, Dict[str, float]]: (总换手率, 详细换手信息)
    """
    # 获取所有资产的并集
    all_assets = set(weights.keys()) | set(prev_weights.keys())
    
    # 计算每个资产的权重变化
    weight_changes = {}
    for asset in all_assets:
        new_weight = weights.get(asset, 0)
        old_weight = prev_weights.get(asset, 0)
        weight_changes[asset] = abs(new_weight - old_weight)
    
    # 计算总换手率
    total_turnover = sum(weight_changes.values()) / 2  # 除以2是因为买入和卖出总和为200%
    
    # 计算详细指标
    turnover_details = {
        'total_turnover': total_turnover,
        'buy_turnover': sum(max(0, weights.get(a, 0) - prev_weights.get(a, 0)) for a in all_assets),
        'sell_turnover': sum(max(0, prev_weights.get(a, 0) - weights.get(a, 0)) for a in all_assets),
        'assets_added': sum(1 for a in all_assets if prev_weights.get(a, 0) == 0 and weights.get(a, 0) > 0),
        'assets_removed': sum(1 for a in all_assets if prev_weights.get(a, 0) > 0 and weights.get(a, 0) == 0)
    }
    
    return total_turnover, turnover_details


def calculate_group_weights(
    weights: Dict[str, float],
    asset_info: pd.DataFrame,
    group_by: List[str]
) -> Dict[str, Dict[str, float]]:
    """
    按指定分组计算权重分布
    
    参数:
        weights: 资产权重字典
        asset_info: 资产信息DataFrame，应包含资产代码作为索引，且包含分组字段
        group_by: 分组字段列表
        
    返回:
        Dict[str, Dict[str, float]]: 按分组的权重分布，格式如 {'industry': {'Tech': 0.3, 'Finance': 0.2, ...}}
    """
    # 检查asset_info是否包含所有分组字段
    missing_cols = [col for col in group_by if col not in asset_info.columns]
    if missing_cols:
        raise ValueError(f"资产信息中缺少以下分组字段: {missing_cols}")
    
    # 转换权重为DataFrame
    weights_df = pd.Series(weights).to_frame('weight')
    
    # 过滤有效资产（确保资产存在于asset_info中）
    valid_assets = set(weights.keys()) & set(asset_info.index)
    weights_df = weights_df.loc[list(valid_assets)]
    
    # 初始化结果字典
    group_weights = {}
    
    # 对每个分组字段进行计算
    for group_field in group_by:
        # 在索引上合并权重与资产信息
        merged = weights_df.join(asset_info[group_field])
        
        # 按分组计算总权重
        group_sum = merged.groupby(group_field)['weight'].sum()
        
        # 添加到结果字典
        group_weights[group_field] = group_sum.to_dict()
    
    return group_weights 