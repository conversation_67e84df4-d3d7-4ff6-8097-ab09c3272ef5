"""
风险因子敏感性分析模块

该模块提供了分析投资组合对关键风险因子敏感性的工具，包括：
1. 因子敏感性分析 - 评估投资组合对各种风险因子变化的敏感程度
2. 因子贡献分析 - 分析各风险因子对投资组合总风险的贡献
3. 敏感性可视化 - 提供可视化工具展示敏感性分析结果
"""

from typing import Dict, List, Optional, Union, Any, Tuple, Callable
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from dataclasses import dataclass, field
from datetime import datetime

from ..analysis.interfaces import AnalysisResult, SensitivityAnalysisResult


@dataclass
class FactorSensitivityResult(SensitivityAnalysisResult):
    """因子敏感性分析结果类"""
    
    # 敏感性分析类型和参数（从基类继承，需要放在前面）
    analysis_type: str  # 分析类型(收益率/波动率/相关系数等)
    parameter_name: str  # 扰动参数名称
    
    # 特有的数据
    portfolio_weights: Dict[str, float] = field(default_factory=dict)  # 投资组合权重
    factor_exposures: Dict[str, Dict[str, float]] = field(default_factory=dict)  # 各资产对各因子的暴露度
    
    # 敏感性结果
    factor_sensitivities: Dict[str, Dict[str, float]] = field(default_factory=dict)  # 各指标对各因子的敏感性
    factor_elasticities: Dict[str, Dict[str, float]] = field(default_factory=dict)  # 各指标对各因子的弹性
    
    # 因子贡献
    factor_contributions: Dict[str, Dict[str, float]] = field(default_factory=dict)  # 各因子对各风险指标的贡献
    percentage_contributions: Dict[str, Dict[str, float]] = field(default_factory=dict)  # 各因子的贡献百分比
    
    # 敏感性分析详情
    sensitivity_details: Dict[str, Any] = field(default_factory=dict)  # 敏感性分析的详细结果
    
    # 指标随扰动变化（继承自SensitivityAnalysisResult）
    metrics_by_perturbation: Dict[str, List[float]] = field(default_factory=dict)  # 指标随扰动变化的值
    
    # 权重随扰动变化（继承自SensitivityAnalysisResult）
    weights_by_perturbation: Dict[float, Dict[str, float]] = field(default_factory=dict)  # 权重随扰动变化
    
    # 敏感性指标（继承自SensitivityAnalysisResult）
    sensitivity_metrics: Dict[str, Dict[str, float]] = field(default_factory=dict)  # 各指标的敏感性度量
    
    # 额外的自定义指标（继承自SensitivityAnalysisResult）
    custom_metrics: Dict[str, Any] = field(default_factory=dict)  # 自定义指标
    
    def to_dict(self) -> Dict[str, Any]:
        """将分析结果转换为字典格式"""
        # 首先获取基类的字典
        result = super().to_dict()
        
        # 添加子类特有的字段
        result.update({
            'portfolio_weights': self.portfolio_weights,
            'factor_exposures': self.factor_exposures,
            'factor_sensitivities': self.factor_sensitivities,
            'factor_elasticities': self.factor_elasticities,
            'factor_contributions': self.factor_contributions,
            'percentage_contributions': self.percentage_contributions,
            'sensitivity_details': self.sensitivity_details
        })
        
        return result
    
    def get_top_factors(self, metric: str, n: int = 5) -> List[Tuple[str, float]]:
        """
        获取对指定指标影响最大的前n个因子
        
        参数:
            metric: 风险指标名称
            n: 返回的因子数量
            
        返回:
            List[Tuple[str, float]]: (因子名称, 敏感性值)的列表
        """
        if metric not in self.factor_sensitivities:
            raise ValueError(f"未找到指标 '{metric}' 的敏感性数据")
        
        # 按敏感性绝对值排序
        sorted_factors = sorted(
            self.factor_sensitivities[metric].items(),
            key=lambda x: abs(x[1]),
            reverse=True
        )
        
        return sorted_factors[:n]
    
    def to_dataframe(self, result_type: str = 'sensitivities') -> pd.DataFrame:
        """
        将分析结果转换为DataFrame格式
        
        参数:
            result_type: 结果类型，可选 'sensitivities', 'elasticities', 'contributions'
            
        返回:
            pd.DataFrame: 数据框形式的结果
        """
        if result_type == 'sensitivities':
            data = self.factor_sensitivities
            title = '因子敏感性'
        elif result_type == 'elasticities':
            data = self.factor_elasticities
            title = '因子弹性'
        elif result_type == 'contributions':
            data = self.factor_contributions
            title = '因子贡献'
        elif result_type == 'percentage_contributions':
            data = self.percentage_contributions
            title = '因子贡献百分比'
        else:
            raise ValueError(f"不支持的结果类型: {result_type}")
        
        if not data:
            return pd.DataFrame()
        
        # 创建DataFrame
        df = pd.DataFrame(data)
        df.index.name = '风险指标'
        df.columns.name = '风险因子'
        
        return df
    
    def summary(self) -> str:
        """生成分析结果的文本摘要"""
        lines = []
        lines.append("==== 风险因子敏感性分析摘要 ====")
        
        # 添加投资组合信息
        lines.append("\n投资组合信息:")
        lines.append(f"  资产数量: {len(self.portfolio_weights)}")
        
        # 添加因子暴露概览
        lines.append("\n因子暴露概览:")
        all_factors = set()
        for asset, exposures in self.factor_exposures.items():
            all_factors.update(exposures.keys())
        
        lines.append(f"  分析的因子数量: {len(all_factors)}")
        lines.append(f"  因子列表: {', '.join(sorted(all_factors))}")
        
        # 添加主要敏感性结果
        if self.factor_sensitivities:
            lines.append("\n主要敏感性结果:")
            
            # 针对每个指标，找出影响最大的因子
            for metric, sensitivities in self.factor_sensitivities.items():
                top_factors = sorted(sensitivities.items(), key=lambda x: abs(x[1]), reverse=True)[:3]
                
                lines.append(f"  {metric}:")
                for factor, sensitivity in top_factors:
                    lines.append(f"    {factor}: {sensitivity:.6f}")
        
        # 添加主要贡献结果
        if self.percentage_contributions:
            lines.append("\n主要风险贡献:")
            
            # 针对波动率指标，显示贡献排名
            if 'volatility' in self.percentage_contributions:
                vol_contributions = sorted(
                    self.percentage_contributions['volatility'].items(),
                    key=lambda x: abs(x[1]),
                    reverse=True
                )[:5]  # 贡献最大的5个因子
                
                lines.append("  波动率贡献:")
                for factor, contribution in vol_contributions:
                    lines.append(f"    {factor}: {contribution:.2%}")
        
        return "\n".join(lines)


def run_factor_sensitivity_analysis(
    weights: Dict[str, float],
    factor_exposures: Dict[str, Dict[str, float]],
    factor_covariance: Optional[pd.DataFrame] = None,
    factor_returns: Optional[pd.DataFrame] = None,
    perturbation_size: float = 0.01,
    risk_metrics: Optional[List[str]] = None,
    correlation_matrix: Optional[pd.DataFrame] = None
) -> FactorSensitivityResult:
    """
    执行投资组合的因子敏感性分析
    
    参数:
        weights: 投资组合权重字典
        factor_exposures: 各资产对各因子的暴露度, 格式: {资产名: {因子名: 暴露值}}
        factor_covariance: 因子协方差矩阵
        factor_returns: 因子收益率数据框
        perturbation_size: 扰动大小，用于数值微分
        risk_metrics: 要分析的风险指标列表，默认为['expected_return', 'volatility', 'sharpe_ratio', 'var']
        correlation_matrix: 资产相关性矩阵，用于非因子模型分析
        
    返回:
        FactorSensitivityResult: 因子敏感性分析结果
    """
    # 验证输入
    assets = list(weights.keys())
    
    # 确保所有资产都有因子暴露数据
    missing_assets = [asset for asset in assets if asset not in factor_exposures]
    if missing_assets:
        raise ValueError(f"缺少以下资产的因子暴露数据: {missing_assets}")
    
    # 获取所有因子
    all_factors = set()
    for asset, exposures in factor_exposures.items():
        all_factors.update(exposures.keys())
    
    factors = sorted(list(all_factors))
    
    # 设置默认风险指标
    if risk_metrics is None:
        risk_metrics = ['expected_return', 'volatility', 'sharpe_ratio', 'var']
    
    # 创建暴露矩阵 (资产 x 因子)
    exposure_matrix = pd.DataFrame(0.0, index=assets, columns=factors)
    for asset in assets:
        for factor, exposure in factor_exposures[asset].items():
            exposure_matrix.loc[asset, factor] = exposure
    
    # 计算组合的因子暴露
    portfolio_exposures = {}
    for factor in factors:
        portfolio_exposures[factor] = sum(weights[asset] * exposure_matrix.loc[asset, factor] 
                                        for asset in assets if asset in weights)
    
    # 使用因子模型计算风险和收益
    if factor_covariance is not None and isinstance(factor_covariance, pd.DataFrame):
        # 检查因子协方差矩阵维度
        if not all(factor in factor_covariance.index for factor in factors):
            raise ValueError("因子协方差矩阵中缺少部分因子")
        
        # 计算投资组合因子模型波动率
        portfolio_factor_var = 0
        for i, factor_i in enumerate(factors):
            for j, factor_j in enumerate(factors):
                portfolio_factor_var += (portfolio_exposures[factor_i] * 
                                      portfolio_exposures[factor_j] * 
                                      factor_covariance.loc[factor_i, factor_j])
        
        portfolio_factor_vol = np.sqrt(portfolio_factor_var)
        
        # 计算各因子对波动率的贡献
        factor_contributions = {}
        percentage_contributions = {}
        
        vol_contributions = {}
        for factor in factors:
            # 计算边际贡献
            marginal_contrib = sum(portfolio_exposures[f] * factor_covariance.loc[factor, f]
                                  for f in factors)
            
            # 计算总贡献
            contrib = portfolio_exposures[factor] * marginal_contrib
            vol_contributions[factor] = contrib
        
        # 计算贡献百分比
        total_vol = sum(vol_contributions.values())
        vol_percentage = {f: contrib / total_vol for f, contrib in vol_contributions.items()}
        
        factor_contributions['volatility'] = vol_contributions
        percentage_contributions['volatility'] = vol_percentage
    else:
        # 如果没有提供因子协方差矩阵，则无法计算贡献
        factor_contributions = {}
        percentage_contributions = {}
    
    # 敏感性分析 - 数值微分法
    factor_sensitivities = {metric: {} for metric in risk_metrics}
    factor_elasticities = {metric: {} for metric in risk_metrics}
    sensitivity_details = {'perturbation_points': {}}
    
    for factor in factors:
        # 基础暴露值
        base_exposure = portfolio_exposures[factor]
        
        # 创建扰动点
        perturb_up = base_exposure * (1 + perturbation_size)
        perturb_down = base_exposure * (1 - perturbation_size)
        
        sensitivity_details['perturbation_points'][factor] = {
            'base': base_exposure,
            'up': perturb_up,
            'down': perturb_down
        }
        
        # 分别计算扰动上下后的风险指标
        metrics_base = _calculate_risk_metrics(
            portfolio_exposures, factor_covariance, factor_returns)
        
        # 上扰动
        portfolio_exposures_up = portfolio_exposures.copy()
        portfolio_exposures_up[factor] = perturb_up
        metrics_up = _calculate_risk_metrics(
            portfolio_exposures_up, factor_covariance, factor_returns)
        
        # 下扰动
        portfolio_exposures_down = portfolio_exposures.copy()
        portfolio_exposures_down[factor] = perturb_down
        metrics_down = _calculate_risk_metrics(
            portfolio_exposures_down, factor_covariance, factor_returns)
        
        # 计算敏感性（中心差分法）
        for metric in risk_metrics:
            if metric in metrics_base and metric in metrics_up and metric in metrics_down:
                # 敏感性 = 指标变化 / 因子变化
                delta_metric = (metrics_up[metric] - metrics_down[metric])
                delta_factor = 2 * perturbation_size * base_exposure
                
                sensitivity = delta_metric / delta_factor if delta_factor != 0 else 0
                
                # 弹性 = 敏感性 * (因子/指标)
                elasticity = sensitivity * base_exposure / metrics_base[metric] if metrics_base[metric] != 0 else 0
                
                factor_sensitivities[metric][factor] = sensitivity
                factor_elasticities[metric][factor] = elasticity
    
    # 返回结果
    return FactorSensitivityResult(
        portfolio_weights=weights,
        factor_exposures=factor_exposures,
        analysis_type="factor_sensitivity",
        parameter_name="factor_exposures",
        perturbation_range=[1-perturbation_size, 1, 1+perturbation_size],
        factor_sensitivities=factor_sensitivities,
        factor_elasticities=factor_elasticities,
        factor_contributions=factor_contributions,
        percentage_contributions=percentage_contributions,
        sensitivity_details=sensitivity_details
    )


def _calculate_risk_metrics(
    portfolio_exposures: Dict[str, float],
    factor_covariance: Optional[pd.DataFrame] = None,
    factor_returns: Optional[pd.DataFrame] = None
) -> Dict[str, float]:
    """
    计算给定投资组合因子暴露下的风险指标
    
    参数:
        portfolio_exposures: 投资组合的因子暴露
        factor_covariance: 因子协方差矩阵
        factor_returns: 因子收益率数据框
        
    返回:
        Dict[str, float]: 风险指标字典
    """
    metrics = {}
    
    # 如果有因子协方差矩阵，计算波动率
    if factor_covariance is not None:
        factors = list(portfolio_exposures.keys())
        
        # 波动率计算
        portfolio_var = 0
        for i, factor_i in enumerate(factors):
            for j, factor_j in enumerate(factors):
                if factor_i in factor_covariance.index and factor_j in factor_covariance.columns:
                    portfolio_var += (portfolio_exposures[factor_i] * 
                                   portfolio_exposures[factor_j] * 
                                   factor_covariance.loc[factor_i, factor_j])
        
        metrics['volatility'] = np.sqrt(portfolio_var)
        
        # 如果有因子收益率，计算预期收益
        if factor_returns is not None:
            # 计算各因子的预期收益率
            factor_expected_returns = factor_returns.mean()
            
            # 计算投资组合预期收益率
            expected_return = sum(portfolio_exposures[f] * factor_expected_returns.get(f, 0)
                               for f in factors if f in factor_expected_returns)
            
            metrics['expected_return'] = expected_return
            
            # 计算夏普比率
            if metrics['volatility'] > 0:
                metrics['sharpe_ratio'] = expected_return / metrics['volatility']
            else:
                metrics['sharpe_ratio'] = 0
            
            # 计算VaR (假设正态分布，95%置信水平)
            z_score = 1.645  # 95%置信水平下的z值
            metrics['var'] = -expected_return + z_score * metrics['volatility']
    
    return metrics


def plot_factor_sensitivity(
    result: FactorSensitivityResult,
    metric: str,
    n_factors: int = 10,
    figsize: Tuple[int, int] = (10, 6),
    title: Optional[str] = None,
    color: str = 'steelblue',
    output_file: Optional[str] = None
) -> plt.Figure:
    """
    绘制因子敏感性条形图
    
    参数:
        result: 因子敏感性分析结果
        metric: 要显示的风险指标
        n_factors: 显示的因子数量（按敏感性绝对值排序）
        figsize: 图表大小
        title: 图表标题
        color: 图表颜色
        output_file: 输出文件路径
        
    返回:
        matplotlib图表对象
    """
    if metric not in result.factor_sensitivities:
        raise ValueError(f"未找到指标 '{metric}' 的敏感性数据")
    
    # 获取指定指标的敏感性数据
    sensitivities = result.factor_sensitivities[metric]
    
    # 按敏感性绝对值排序
    sorted_factors = sorted(
        sensitivities.items(),
        key=lambda x: abs(x[1]),
        reverse=True
    )[:n_factors]
    
    factors = [item[0] for item in sorted_factors]
    values = [item[1] for item in sorted_factors]
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 绘制水平条形图
    bars = ax.barh(factors, values, color=color)
    
    # 为条形图添加正负颜色
    for i, bar in enumerate(bars):
        if values[i] < 0:
            bar.set_color('firebrick')
    
    # 添加网格线
    ax.grid(axis='x', linestyle='--', alpha=0.7)
    
    # 设置标题和标签
    if title is None:
        title = f"{metric.capitalize()} 因子敏感性"
    
    ax.set_title(title)
    ax.set_xlabel(f"{metric.capitalize()} 敏感性")
    ax.set_ylabel("风险因子")
    
    # 在每个条形上显示数值
    for i, v in enumerate(values):
        ax.text(
            v + (0.01 if v >= 0 else -0.01),
            i,
            f"{v:.4f}",
            va='center',
            ha='left' if v >= 0 else 'right',
            fontsize=9
        )
    
    # 调整布局
    fig.tight_layout()
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
    
    return fig


def plot_factor_contribution(
    result: FactorSensitivityResult,
    metric: str = 'volatility',
    figsize: Tuple[int, int] = (10, 8),
    title: Optional[str] = None,
    min_contribution: float = 0.02,  # 最小贡献比例，低于此值的因子归入"其他"类别
    cmap: str = 'viridis',
    output_file: Optional[str] = None
) -> plt.Figure:
    """
    绘制因子贡献饼图
    
    参数:
        result: 因子敏感性分析结果
        metric: 要显示的风险指标，默认为'volatility'
        figsize: 图表大小
        title: 图表标题
        min_contribution: 最小贡献比例，低于此值的因子归入"其他"类别
        cmap: 颜色映射名称
        output_file: 输出文件路径
        
    返回:
        matplotlib图表对象
    """
    if metric not in result.percentage_contributions:
        raise ValueError(f"未找到指标 '{metric}' 的贡献数据")
    
    # 获取指定指标的贡献百分比数据
    contributions = result.percentage_contributions[metric]
    
    # 按贡献绝对值排序
    sorted_contributions = sorted(
        contributions.items(),
        key=lambda x: abs(x[1]),
        reverse=True
    )
    
    # 区分主要因子和次要因子
    main_factors = []
    main_values = []
    other_value = 0
    
    for factor, value in sorted_contributions:
        if abs(value) >= min_contribution:
            main_factors.append(factor)
            main_values.append(value)
        else:
            other_value += value
    
    # 如果有"其他"类别，添加到列表中
    if other_value > 0:
        main_factors.append('其他')
        main_values.append(other_value)
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 绘制饼图
    wedges, texts, autotexts = ax.pie(
        np.abs(main_values),  # 使用绝对值，因为负贡献也会占百分比
        labels=main_factors,
        autopct='%1.1f%%',
        startangle=90,
        pctdistance=0.85,
        colors=plt.cm.get_cmap(cmap)(np.linspace(0, 1, len(main_factors)))
    )
    
    # 设置标题
    if title is None:
        title = f"{metric.capitalize()} 因子贡献"
    
    ax.set_title(title)
    
    # 添加圆环中心的空白
    centre_circle = plt.Circle((0, 0), 0.70, fc='white')
    fig.gca().add_artist(centre_circle)
    
    # 设置文本属性
    plt.setp(autotexts, size=10, weight="bold")
    
    # 在中心显示指标名称
    ax.text(0, 0, metric.capitalize(), ha='center', va='center', fontsize=12)
    
    # 调整布局
    fig.tight_layout()
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
    
    return fig


def plot_factor_elasticity_matrix(
    result: FactorSensitivityResult,
    metrics: Optional[List[str]] = None,
    n_factors: int = 8,
    figsize: Tuple[int, int] = (12, 8),
    title: Optional[str] = None,
    cmap: str = 'RdBu_r',
    output_file: Optional[str] = None
) -> plt.Figure:
    """
    绘制因子弹性热力图
    
    参数:
        result: 因子敏感性分析结果
        metrics: 要显示的风险指标列表，默认为所有可用指标
        n_factors: 显示的因子数量（按弹性绝对值排序）
        figsize: 图表大小
        title: 图表标题
        cmap: 颜色映射名称
        output_file: 输出文件路径
        
    返回:
        matplotlib图表对象
    """
    if not result.factor_elasticities:
        raise ValueError("结果中没有因子弹性数据")
    
    # 如果未指定指标，则使用所有可用指标
    if metrics is None:
        metrics = list(result.factor_elasticities.keys())
    else:
        # 确保所有指定的指标都有数据
        for metric in metrics:
            if metric not in result.factor_elasticities:
                raise ValueError(f"未找到指标 '{metric}' 的弹性数据")
    
    # 获取所有因子的集合
    all_factors = set()
    for metric in metrics:
        all_factors.update(result.factor_elasticities[metric].keys())
    
    # 计算每个因子的平均弹性绝对值
    average_abs_elasticities = {}
    for factor in all_factors:
        elasticities = []
        for metric in metrics:
            if factor in result.factor_elasticities[metric]:
                elasticities.append(abs(result.factor_elasticities[metric][factor]))
        
        if elasticities:
            average_abs_elasticities[factor] = np.mean(elasticities)
    
    # 按平均弹性绝对值排序，选择前n_factors个因子
    top_factors = sorted(
        average_abs_elasticities.items(),
        key=lambda x: x[1],
        reverse=True
    )[:n_factors]
    
    selected_factors = [item[0] for item in top_factors]
    
    # 创建弹性矩阵
    elasticity_matrix = np.zeros((len(metrics), len(selected_factors)))
    
    for i, metric in enumerate(metrics):
        for j, factor in enumerate(selected_factors):
            if factor in result.factor_elasticities[metric]:
                elasticity_matrix[i, j] = result.factor_elasticities[metric][factor]
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 绘制热力图
    im = ax.imshow(elasticity_matrix, cmap=cmap)
    
    # 添加颜色条
    cbar = ax.figure.colorbar(im, ax=ax)
    cbar.ax.set_ylabel("因子弹性", rotation=-90, va="bottom")
    
    # 设置刻度和标签
    ax.set_xticks(np.arange(len(selected_factors)))
    ax.set_yticks(np.arange(len(metrics)))
    ax.set_xticklabels(selected_factors, rotation=45, ha="right")
    ax.set_yticklabels(metrics)
    
    # 设置标题
    if title is None:
        title = "因子弹性矩阵"
    
    ax.set_title(title)
    
    # 在每个单元格中显示弹性值
    for i in range(len(metrics)):
        for j in range(len(selected_factors)):
            text = ax.text(j, i, f"{elasticity_matrix[i, j]:.2f}",
                          ha="center", va="center", 
                          color="white" if abs(elasticity_matrix[i, j]) > 0.5 else "black")
    
    # 调整布局
    fig.tight_layout()
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
    
    return fig 