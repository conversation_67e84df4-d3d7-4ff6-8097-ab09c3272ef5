"""
极端情景分析模块

该模块提供了分析投资组合在极端市场情况下表现的工具，包括：
1. 历史极端事件分析 - 使用历史极端事件数据分析投资组合可能的表现
2. 模拟极端情景分析 - 使用蒙特卡洛模拟或压力测试分析投资组合在极端情况下的表现
3. 尾部风险分析 - 分析投资组合在极端市场条件下的尾部风险
"""

from typing import Dict, List, Optional, Union, Any, Tuple, Callable
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from dataclasses import dataclass, field
from datetime import datetime

from ..analysis.interfaces import AnalysisResult, SensitivityAnalysisResult


@dataclass
class ScenarioAnalysisResult(AnalysisResult):
    """情景分析结果类"""
    
    # 基本信息
    scenario_name: str  # 情景名称
    weights: Dict[str, float]  # 投资组合权重
    
    # 情景参数
    scenario_parameters: Dict[str, Any] = field(default_factory=dict)  # 情景定义参数
    
    # 情景影响
    base_metrics: Dict[str, float] = field(default_factory=dict)  # 基准情况下的指标
    scenario_metrics: Dict[str, float] = field(default_factory=dict)  # 情景下的指标
    
    # 变化量
    absolute_changes: Dict[str, float] = field(default_factory=dict)  # 绝对变化量
    percentage_changes: Dict[str, float] = field(default_factory=dict)  # 百分比变化
    
    # 详细资产层面影响
    asset_impacts: Dict[str, Dict[str, float]] = field(default_factory=dict)  # 各资产受影响情况
    
    # 额外分析结果
    custom_metrics: Dict[str, Any] = field(default_factory=dict)  # 自定义指标
    
    def to_dict(self) -> Dict[str, Any]:
        """将分析结果转换为字典格式"""
        return {
            'scenario_name': self.scenario_name,
            'weights': self.weights,
            'scenario_parameters': self.scenario_parameters,
            'base_metrics': self.base_metrics,
            'scenario_metrics': self.scenario_metrics,
            'absolute_changes': self.absolute_changes,
            'percentage_changes': self.percentage_changes,
            'asset_impacts': self.asset_impacts,
            'custom_metrics': self.custom_metrics
        }
    
    def to_dataframe(self) -> pd.DataFrame:
        """将分析结果转换为DataFrame格式"""
        # 创建基本指标比较DataFrame
        metrics = []
        for metric_name in set(self.base_metrics.keys()) | set(self.scenario_metrics.keys()):
            base_value = self.base_metrics.get(metric_name, None)
            scenario_value = self.scenario_metrics.get(metric_name, None)
            abs_change = self.absolute_changes.get(metric_name, None)
            pct_change = self.percentage_changes.get(metric_name, None)
            
            row = {
                'Metric': metric_name,
                'Base': base_value,
                'Scenario': scenario_value,
                'Absolute_Change': abs_change,
                'Percentage_Change': pct_change
            }
            metrics.append(row)
        
        return pd.DataFrame(metrics)
    
    def summary(self) -> str:
        """生成分析结果的文本摘要"""
        lines = []
        lines.append(f"==== {self.scenario_name} 情景分析摘要 ====")
        
        # 添加情景参数摘要
        if self.scenario_parameters:
            lines.append("\n情景参数:")
            for param, value in self.scenario_parameters.items():
                lines.append(f"  {param}: {value}")
        
        # 添加主要指标变化
        lines.append("\n主要指标变化:")
        metrics_to_show = sorted(self.percentage_changes.items(), 
                                key=lambda x: abs(x[1]), 
                                reverse=True)[:5]  # 变化最大的5个指标
        
        for metric, pct_change in metrics_to_show:
            base = self.base_metrics.get(metric, float('nan'))
            scenario = self.scenario_metrics.get(metric, float('nan'))
            abs_change = self.absolute_changes.get(metric, float('nan'))
            
            lines.append(f"  {metric}:")
            lines.append(f"    基准值: {base:.6f}")
            lines.append(f"    情景值: {scenario:.6f}")
            lines.append(f"    变化量: {abs_change:.6f} ({pct_change:.2%})")
        
        # 资产层面影响摘要
        if self.asset_impacts:
            lines.append("\n资产影响排名:")
            
            # 查找最显著的影响指标
            impact_metric = list(next(iter(self.asset_impacts.values())).keys())[0]
            
            # 获取受影响最大的资产
            if impact_metric in next(iter(self.asset_impacts.values())):
                top_impacts = sorted(
                    [(asset, impacts.get(impact_metric, 0)) 
                     for asset, impacts in self.asset_impacts.items()],
                    key=lambda x: abs(x[1]),
                    reverse=True
                )[:5]  # 取前5个
                
                for asset, impact in top_impacts:
                    lines.append(f"  {asset}: {impact:.2%}")
        
        return "\n".join(lines)


def run_historical_scenario_analysis(
    weights: Dict[str, float],
    returns: pd.DataFrame,
    scenario_period: Union[str, Tuple[datetime, datetime]],
    base_period: Optional[Union[str, Tuple[datetime, datetime]]] = None,
    metrics_calculator: Optional[Callable[[pd.Series], Dict[str, float]]] = None
) -> ScenarioAnalysisResult:
    """
    使用历史极端事件数据进行情景分析
    
    参数:
        weights: 投资组合权重字典
        returns: 资产收益率数据框，包含足够长的历史数据
        scenario_period: 情景时期，可以是预定义的字符串(如"2008金融危机")或日期范围
        base_period: 基准时期，默认为情景期外的所有数据
        metrics_calculator: 计算风险指标的函数，默认使用内部函数
        
    返回:
        ScenarioAnalysisResult: 情景分析结果
    """
    # 处理情景时期参数
    predefined_scenarios = {
        "2008金融危机": ("2008-09-01", "2009-03-31"),
        "2020新冠疫情": ("2020-02-20", "2020-04-30"),
        "2000互联网泡沫": ("2000-03-01", "2002-10-31"),
        "2015中国股灾": ("2015-06-15", "2015-08-31"),
        "2011欧债危机": ("2011-08-01", "2011-10-31")
    }
    
    if isinstance(scenario_period, str) and scenario_period in predefined_scenarios:
        start_date, end_date = predefined_scenarios[scenario_period]
        scenario_data = returns[(returns.index >= start_date) & (returns.index <= end_date)]
        scenario_name = scenario_period
    elif isinstance(scenario_period, tuple) and len(scenario_period) == 2:
        start_date, end_date = scenario_period
        scenario_data = returns[(returns.index >= start_date) & (returns.index <= end_date)]
        scenario_name = f"自定义情景 ({start_date} 至 {end_date})"
    else:
        raise ValueError("情景时期格式错误，必须是预定义情景名称或(开始日期,结束日期)元组")
    
    # 基准时期数据
    if base_period is None:
        # 默认使用情景期外的所有数据
        base_data = returns[~returns.index.isin(scenario_data.index)]
    elif isinstance(base_period, str) and base_period in predefined_scenarios:
        start_date, end_date = predefined_scenarios[base_period]
        base_data = returns[(returns.index >= start_date) & (returns.index <= end_date)]
    elif isinstance(base_period, tuple) and len(base_period) == 2:
        start_date, end_date = base_period
        base_data = returns[(returns.index >= start_date) & (returns.index <= end_date)]
    else:
        raise ValueError("基准时期格式错误")
    
    # 默认的指标计算函数
    if metrics_calculator is None:
        def default_metrics_calculator(ret_series):
            """计算基本风险收益指标"""
            portfolio_returns = pd.Series(0, index=ret_series.index)
            
            # 计算投资组合收益
            for asset, weight in weights.items():
                if asset in ret_series.columns:
                    portfolio_returns += weight * ret_series[asset]
            
            # 计算指标
            annualized_return = portfolio_returns.mean() * 252
            annualized_vol = portfolio_returns.std() * np.sqrt(252)
            sharpe_ratio = annualized_return / annualized_vol if annualized_vol != 0 else 0
            max_drawdown = ((1 + portfolio_returns).cumprod().cummax() / 
                           (1 + portfolio_returns).cumprod() - 1).max()
            
            return {
                'annualized_return': annualized_return,
                'annualized_volatility': annualized_vol,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'skewness': portfolio_returns.skew(),
                'kurtosis': portfolio_returns.kurtosis(),
                'var_95': portfolio_returns.quantile(0.05),
                'cvar_95': portfolio_returns[portfolio_returns <= portfolio_returns.quantile(0.05)].mean()
            }
        
        metrics_calculator = default_metrics_calculator
    
    # 计算基准情况下的指标
    base_metrics = metrics_calculator(base_data)
    
    # 计算情景下的指标
    scenario_metrics = metrics_calculator(scenario_data)
    
    # 计算变化量
    absolute_changes = {}
    percentage_changes = {}
    for metric in set(base_metrics.keys()) | set(scenario_metrics.keys()):
        base_value = base_metrics.get(metric, 0)
        scenario_value = scenario_metrics.get(metric, 0)
        
        abs_change = scenario_value - base_value
        pct_change = abs_change / base_value if base_value != 0 else float('inf')
        
        absolute_changes[metric] = abs_change
        percentage_changes[metric] = pct_change
    
    # 计算资产层面影响
    asset_impacts = {}
    for asset in weights:
        if asset in scenario_data.columns:
            # 计算此资产在情景期间的异常表现
            base_mean = base_data[asset].mean()
            scenario_mean = scenario_data[asset].mean()
            
            # 简单测量：收益率偏离
            deviation = scenario_mean - base_mean
            relative_impact = deviation / base_mean if base_mean != 0 else float('inf')
            
            asset_impacts[asset] = {
                'absolute_impact': deviation * 252,  # 年化
                'relative_impact': relative_impact,
                'contribution': weights[asset] * deviation * 252  # 贡献权重
            }
    
    # 创建情景参数
    scenario_parameters = {
        'period': (scenario_data.index[0], scenario_data.index[-1]),
        'duration_days': (scenario_data.index[-1] - scenario_data.index[0]).days,
        'sample_size': len(scenario_data)
    }
    
    # 返回结果
    return ScenarioAnalysisResult(
        scenario_name=scenario_name,
        weights=weights,
        scenario_parameters=scenario_parameters,
        base_metrics=base_metrics,
        scenario_metrics=scenario_metrics,
        absolute_changes=absolute_changes,
        percentage_changes=percentage_changes,
        asset_impacts=asset_impacts
    )


def run_monte_carlo_scenario_analysis(
    weights: Dict[str, float],
    expected_returns: np.ndarray,
    covariance_matrix: np.ndarray,
    asset_names: List[str],
    num_simulations: int = 1000,
    time_horizon: int = 252,  # 默认1年
    confidence_level: float = 0.95,
    stress_factors: Optional[Dict[str, float]] = None,
    distribution: str = 'normal',
    metrics_of_interest: Optional[List[str]] = None
) -> ScenarioAnalysisResult:
    """
    使用蒙特卡洛模拟进行极端情景分析
    
    参数:
        weights: 投资组合权重字典
        expected_returns: 预期收益率向量
        covariance_matrix: 协方差矩阵
        asset_names: 资产名称列表
        num_simulations: 模拟次数
        time_horizon: 模拟时间范围（天数）
        confidence_level: 置信水平
        stress_factors: 压力因子字典，格式为 {资产名: 压力倍数}
        distribution: 收益分布类型，'normal'或't'
        metrics_of_interest: 关注的风险指标列表
        
    返回:
        ScenarioAnalysisResult: 情景分析结果
    """
    # 参数验证
    if len(weights) != len(asset_names) or len(expected_returns) != len(asset_names):
        raise ValueError("权重、预期收益和资产名称数量不匹配")
    
    # 获取权重向量（保持顺序与asset_names一致）
    weight_vector = np.array([weights.get(asset, 0) for asset in asset_names])
    
    # 设置压力因子（默认为1，表示不加压力）
    if stress_factors is None:
        stress_factors = {asset: 1.0 for asset in asset_names}
    else:
        # 确保所有资产都有压力因子
        for asset in asset_names:
            if asset not in stress_factors:
                stress_factors[asset] = 1.0
    
    # 计算基准情况下的指标
    base_portfolio_return = np.dot(weight_vector, expected_returns)
    base_portfolio_vol = np.sqrt(np.dot(weight_vector, np.dot(covariance_matrix, weight_vector)))
    base_sharpe = base_portfolio_return / base_portfolio_vol if base_portfolio_vol != 0 else 0
    
    # 计算基准VaR
    if distribution == 'normal':
        z_score = -np.percentile(np.random.normal(0, 1, 10000), (1 - confidence_level) * 100)
    else:  # t分布，假设自由度为5
        z_score = -np.percentile(np.random.standard_t(5, 10000), (1 - confidence_level) * 100)
    
    base_var = -(base_portfolio_return - z_score * base_portfolio_vol) * np.sqrt(time_horizon)
    
    base_metrics = {
        'expected_return': base_portfolio_return * time_horizon,
        'volatility': base_portfolio_vol * np.sqrt(time_horizon),
        'sharpe_ratio': base_sharpe,
        'var': base_var
    }
    
    # 应用压力因子到协方差矩阵
    stress_cov = covariance_matrix.copy()
    stress_returns = expected_returns.copy()
    
    for i, asset_i in enumerate(asset_names):
        # 调整波动率（协方差矩阵对角线）
        stress_cov[i, i] *= stress_factors[asset_i]**2
        
        # 调整相关系数（协方差矩阵非对角线元素）
        for j, asset_j in enumerate(asset_names):
            if i != j:
                stress_cov[i, j] *= stress_factors[asset_i] * stress_factors[asset_j]
        
        # 调整预期收益（通常情况下，高风险压力情景会降低预期收益）
        # 如果压力因子>1，则降低预期收益
        if stress_factors[asset_i] > 1:
            stress_returns[i] *= (2 - stress_factors[asset_i])  # 简单规则：压力因子越大，收益降低越多
    
    # 计算压力情景下的指标
    stress_portfolio_return = np.dot(weight_vector, stress_returns)
    stress_portfolio_vol = np.sqrt(np.dot(weight_vector, np.dot(stress_cov, weight_vector)))
    stress_sharpe = stress_portfolio_return / stress_portfolio_vol if stress_portfolio_vol != 0 else 0
    
    # 计算压力情景下的VaR
    stress_var = -(stress_portfolio_return - z_score * stress_portfolio_vol) * np.sqrt(time_horizon)
    
    # 进行蒙特卡洛模拟
    np.random.seed(42)  # 设置随机种子以确保可重复性
    
    # 生成随机收益率
    if distribution == 'normal':
        random_returns = np.random.multivariate_normal(
            stress_returns, stress_cov, size=num_simulations
        )
    else:  # t分布模拟
        # 使用多元t分布（近似）
        df = 5  # 自由度
        normal_samples = np.random.multivariate_normal(
            np.zeros(len(stress_returns)), stress_cov, size=num_simulations
        )
        chi_samples = np.random.chisquare(df, size=num_simulations) / df
        random_returns = np.array([
            stress_returns + normal_samples[i] / np.sqrt(chi_samples[i])
            for i in range(num_simulations)
        ])
    
    # 计算投资组合收益
    portfolio_returns = np.dot(random_returns, weight_vector)
    
    # 计算情景下其他指标
    scenario_metrics = {
        'expected_return': stress_portfolio_return * time_horizon,
        'volatility': stress_portfolio_vol * np.sqrt(time_horizon),
        'sharpe_ratio': stress_sharpe,
        'var': stress_var,
        'cvar': -np.mean(portfolio_returns[portfolio_returns <= -stress_var/np.sqrt(time_horizon)]) * np.sqrt(time_horizon),
        'max_loss': -np.min(portfolio_returns) * np.sqrt(time_horizon),
        'probability_of_loss': np.mean(portfolio_returns < 0)
    }
    
    # 计算变化量
    absolute_changes = {}
    percentage_changes = {}
    for metric in set(base_metrics.keys()) | set(scenario_metrics.keys()):
        base_value = base_metrics.get(metric, 0)
        scenario_value = scenario_metrics.get(metric, 0)
        
        abs_change = scenario_value - base_value
        pct_change = abs_change / base_value if base_value != 0 else float('inf')
        
        absolute_changes[metric] = abs_change
        percentage_changes[metric] = pct_change
    
    # the asset_impacts
    asset_impacts = {}
    for i, asset in enumerate(asset_names):
        if asset in weights and weights[asset] > 0:
            # 计算此资产在压力情景下的风险贡献
            marginal_contrib = weight_vector[i] * np.dot(stress_cov[i, :], weight_vector) / stress_portfolio_vol
            
            asset_impacts[asset] = {
                'stress_factor': stress_factors[asset],
                'risk_contribution': marginal_contrib / stress_portfolio_vol,
                'return_impact': (stress_returns[i] - expected_returns[i]) * weight_vector[i] / base_portfolio_return
                if base_portfolio_return != 0 else 0
            }
    
    # 创建情景参数
    scenario_parameters = {
        'num_simulations': num_simulations,
        'time_horizon': time_horizon,
        'confidence_level': confidence_level,
        'distribution': distribution,
        'average_stress_factor': np.mean(list(stress_factors.values()))
    }
    
    # 返回结果
    return ScenarioAnalysisResult(
        scenario_name="蒙特卡洛压力情景分析",
        weights=weights,
        scenario_parameters=scenario_parameters,
        base_metrics=base_metrics,
        scenario_metrics=scenario_metrics,
        absolute_changes=absolute_changes,
        percentage_changes=percentage_changes,
        asset_impacts=asset_impacts
    )


def plot_scenario_analysis(
    result: ScenarioAnalysisResult,
    figsize: Tuple[int, int] = (12, 8),
    metrics_to_show: Optional[List[str]] = None,
    title: Optional[str] = None,
    chart_type: str = 'bar',
    color_scheme: Tuple[str, str] = ('steelblue', 'firebrick'),
    show_pct_change: bool = True,
    output_file: Optional[str] = None
) -> plt.Figure:
    """
    可视化情景分析结果
    
    参数:
        result: 情景分析结果
        figsize: 图表大小
        metrics_to_show: 要显示的指标列表
        title: 图表标题，默认使用情景名称
        chart_type: 图表类型，'bar'或'radar'
        color_scheme: 颜色方案，(基准颜色, 情景颜色)
        show_pct_change: 是否显示百分比变化
        output_file: 输出文件路径
        
    返回:
        matplotlib图表对象
    """
    # 设置默认指标
    if metrics_to_show is None:
        metrics_to_show = ['expected_return', 'volatility', 'sharpe_ratio', 'var', 
                          'cvar', 'max_drawdown', 'skewness', 'kurtosis']
    
    # 过滤有效指标（同时存在于基准和情景结果中）
    valid_metrics = [m for m in metrics_to_show 
                    if m in result.base_metrics and m in result.scenario_metrics]
    
    if not valid_metrics:
        raise ValueError("没有有效的指标可供显示")
    
    # 设置图表标题
    if title is None:
        title = f"{result.scenario_name} 分析结果"
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    if chart_type == 'bar':
        # 准备数据
        base_values = [result.base_metrics[m] for m in valid_metrics]
        scenario_values = [result.scenario_metrics[m] for m in valid_metrics]
        
        # 设置bar位置
        x = np.arange(len(valid_metrics))
        width = 0.35
        
        # 绘制条形图
        rects1 = ax.bar(x - width/2, base_values, width, label='基准情况', color=color_scheme[0])
        rects2 = ax.bar(x + width/2, scenario_values, width, label=result.scenario_name, color=color_scheme[1])
        
        # 添加标签和标题
        ax.set_xlabel('风险指标')
        ax.set_ylabel('指标值')
        ax.set_title(title)
        ax.set_xticks(x)
        ax.set_xticklabels(valid_metrics, rotation=45, ha='right')
        ax.legend()
        
        # 添加百分比变化标签
        if show_pct_change:
            for i, m in enumerate(valid_metrics):
                pct_change = result.percentage_changes.get(m, 0)
                height = scenario_values[i]
                ax.text(i + width/2, height, f"{pct_change:.1%}", 
                       ha='center', va='bottom', fontsize=8,
                       color='green' if pct_change >= 0 else 'red')
        
        fig.tight_layout()
    
    elif chart_type == 'radar':
        # 准备雷达图
        from matplotlib.path import Path
        from matplotlib.splines import Spline
        from matplotlib.patches import PathPatch
        
        # 确保有效指标至少有3个，否则雷达图无法正常显示
        if len(valid_metrics) < 3:
            raise ValueError("雷达图至少需要3个有效指标")
        
        # 准备数据
        base_values = np.array([result.base_metrics[m] for m in valid_metrics])
        scenario_values = np.array([result.scenario_metrics[m] for m in valid_metrics])
        
        # 标准化数据以便在雷达图上显示
        max_values = np.maximum(np.abs(base_values), np.abs(scenario_values))
        base_norm = base_values / max_values
        scenario_norm = scenario_values / max_values
        
        # 设置雷达图参数
        angles = np.linspace(0, 2*np.pi, len(valid_metrics), endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形
        
        base_norm = np.concatenate((base_norm, [base_norm[0]]))
        scenario_norm = np.concatenate((scenario_norm, [scenario_norm[0]]))
        
        # 绘制雷达图
        ax.set_theta_offset(np.pi / 2)
        ax.set_theta_direction(-1)
        
        # 绘制网格
        ax.set_thetagrids(np.degrees(angles[:-1]), valid_metrics)
        
        # 绘制基准和情景线
        ax.plot(angles, base_norm, 'o-', linewidth=2, label='基准情况', color=color_scheme[0])
        ax.fill(angles, base_norm, alpha=0.1, color=color_scheme[0])
        
        ax.plot(angles, scenario_norm, 'o-', linewidth=2, label=result.scenario_name, color=color_scheme[1])
        ax.fill(angles, scenario_norm, alpha=0.1, color=color_scheme[1])
        
        # 添加标题和图例
        ax.set_title(title)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.1))
        
        # 添加百分比变化标签
        if show_pct_change:
            for i, m in enumerate(valid_metrics):
                pct_change = result.percentage_changes.get(m, 0)
                angle = angles[i]
                radius = scenario_norm[i] * 1.1  # 稍微偏移一点
                ha = 'left' if angle >= np.pi else 'right'
                ax.text(angle, radius, f"{pct_change:.1%}", 
                       ha=ha, va='center', fontsize=8,
                       color='green' if pct_change >= 0 else 'red')
    
    else:
        raise ValueError(f"不支持的图表类型: {chart_type}, 请使用'bar'或'radar'")
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
    
    return fig


def plot_scenario_distribution(
    result: ScenarioAnalysisResult,
    return_samples: np.ndarray,
    figsize: Tuple[int, int] = (10, 8),
    title: Optional[str] = None,
    bins: int = 50,
    show_metrics: bool = True,
    color: str = 'steelblue',
    var_color: str = 'red',
    output_file: Optional[str] = None
) -> plt.Figure:
    """
    绘制蒙特卡洛模拟的收益分布图
    
    参数:
        result: 情景分析结果
        return_samples: 投资组合收益率样本
        figsize: 图表大小
        title: 图表标题
        bins: 直方图箱数
        show_metrics: 是否显示关键指标
        color: 直方图颜色
        var_color: VaR线颜色
        output_file: 输出文件路径
        
    返回:
        matplotlib图表对象
    """
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 设置图表标题
    if title is None:
        title = f"{result.scenario_name} - 收益分布"
    
    # 绘制直方图
    n, bins, patches = ax.hist(return_samples, bins=bins, density=True, 
                              alpha=0.7, color=color)
    
    # 添加核密度估计
    from scipy import stats
    kde = stats.gaussian_kde(return_samples)
    x = np.linspace(min(return_samples), max(return_samples), 1000)
    ax.plot(x, kde(x), 'k-', linewidth=2)
    
    # 标记VaR和CVaR
    if 'var' in result.scenario_metrics:
        var_value = -result.scenario_metrics['var'] / np.sqrt(result.scenario_parameters.get('time_horizon', 252))
        ax.axvline(var_value, color=var_color, linestyle='--', 
                  label=f"VaR ({result.scenario_parameters.get('confidence_level', 0.95):.0%})")
        
        # 着色VaR以下区域
        left_edge = min(bins)
        ax.fill_between(x, 0, kde(x), where=(x <= var_value), 
                       color=var_color, alpha=0.3)
    
    # 添加关键统计信息
    if show_metrics:
        stats_text = (
            f"均值: {np.mean(return_samples):.2%}\n"
            f"标准差: {np.std(return_samples):.2%}\n"
            f"偏度: {stats.skew(return_samples):.2f}\n"
            f"峰度: {stats.kurtosis(return_samples):.2f}\n"
            f"最小值: {np.min(return_samples):.2%}\n"
            f"最大值: {np.max(return_samples):.2%}\n"
        )
        
        if 'var' in result.scenario_metrics and 'cvar' in result.scenario_metrics:
            var = result.scenario_metrics['var'] / np.sqrt(result.scenario_parameters.get('time_horizon', 252))
            cvar = result.scenario_metrics['cvar'] / np.sqrt(result.scenario_parameters.get('time_horizon', 252))
            stats_text += f"VaR: {var:.2%}\nCVaR: {cvar:.2%}"
        
        # 在图表右上角添加文本框
        props = dict(boxstyle='round', facecolor='white', alpha=0.7)
        ax.text(0.95, 0.95, stats_text, transform=ax.transAxes,
               fontsize=10, verticalalignment='top', horizontalalignment='right',
               bbox=props)
    
    # 设置轴标签
    ax.set_xlabel('收益率')
    ax.set_ylabel('概率密度')
    ax.set_title(title)
    
    if 'var' in result.scenario_metrics:
        ax.legend()
    
    # 调整布局
    fig.tight_layout()
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
    
    return fig 