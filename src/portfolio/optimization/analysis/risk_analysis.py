"""
风险分析模块

该模块提供了分析投资组合风险分布和特征的工具，包括：
1. 风险分解 - 分析风险的来源和贡献
2. 风险贡献分析 - 计算各资产对总风险的贡献
3. 边际风险贡献 - 计算各资产的边际风险贡献
4. 风险平价分析 - 评估投资组合的风险平价程度
5. 分组风险分析 - 按行业/地区等分组分析风险分布
"""

from typing import Dict, List, Optional, Any, Tuple, Union
import numpy as np
import pandas as pd
from ..analysis.interfaces import RiskAnalysisResult


def analyze_risk(
    weights: Dict[str, float],
    covariance_matrix: np.ndarray,
    asset_names: Optional[List[str]] = None,
    asset_info: Optional[pd.DataFrame] = None,
    group_by: Optional[List[str]] = None
) -> RiskAnalysisResult:
    """
    分析投资组合风险特征和分布
    
    参数:
        weights: 资产权重字典 {资产代码: 权重}
        covariance_matrix: 协方差矩阵 (n x n numpy矩阵)
        asset_names: 资产名称列表，与协方差矩阵的行列顺序对应，若未提供则使用weights的键
        asset_info: 资产信息DataFrame，应包含资产代码作为索引，可能包含行业、地区等分组字段
        group_by: 分组字段列表，如['industry', 'region']等，需要与asset_info中的列名匹配
        
    返回:
        RiskAnalysisResult: 风险分析结果对象
    """
    # 如果未提供资产名称，则使用权重字典的键
    if asset_names is None:
        asset_names = list(weights.keys())
    
    # 确保协方差矩阵的大小与资产数量匹配
    if covariance_matrix.shape[0] != len(asset_names) or covariance_matrix.shape[1] != len(asset_names):
        raise ValueError(f"协方差矩阵大小({covariance_matrix.shape})与资产数量({len(asset_names)})不匹配")
    
    # 将权重字典转换为按照asset_names排序的权重向量
    w_vector = np.array([weights.get(asset, 0.0) for asset in asset_names])
    
    # 计算投资组合总风险(波动率)
    portfolio_variance = w_vector.T @ covariance_matrix @ w_vector
    portfolio_volatility = np.sqrt(portfolio_variance)
    
    # 计算风险贡献
    risk_contribution, risk_contribution_percent = calculate_risk_contribution(
        w_vector, covariance_matrix
    )
    
    # 将风险贡献转换为字典
    risk_contrib_dict = dict(zip(asset_names, risk_contribution))
    risk_contrib_pct_dict = dict(zip(asset_names, risk_contribution_percent))
    
    # 计算边际风险贡献
    marginal_contrib = calculate_marginal_risk_contribution(
        w_vector, covariance_matrix, portfolio_volatility
    )
    marginal_contrib_dict = dict(zip(asset_names, marginal_contrib))
    
    # 计算风险平价分数
    risk_parity_score = calculate_risk_parity_score(risk_contribution_percent)
    
    # 计算分组风险（如果提供了资产信息和分组字段）
    group_risk = {}
    if asset_info is not None and group_by:
        group_risk = calculate_group_risk(
            asset_names, risk_contrib_dict, asset_info, group_by
        )
    
    # 创建并返回分析结果对象
    return RiskAnalysisResult(
        weights=weights,
        portfolio_risk=portfolio_volatility,
        risk_contribution=risk_contrib_dict,
        risk_contribution_percent=risk_contrib_pct_dict,
        risk_parity_score=risk_parity_score,
        marginal_risk_contribution=marginal_contrib_dict,
        group_risk=group_risk
    )


def calculate_risk_contribution(
    weights: np.ndarray,
    covariance_matrix: np.ndarray
) -> Tuple[np.ndarray, np.ndarray]:
    """
    计算各资产对投资组合总风险的贡献
    
    参数:
        weights: 资产权重向量
        covariance_matrix: 协方差矩阵
        
    返回:
        Tuple[np.ndarray, np.ndarray]: (风险贡献值, 风险贡献百分比)
    """
    # 计算投资组合总风险(波动率)
    port_variance = weights.T @ covariance_matrix @ weights
    port_volatility = np.sqrt(port_variance)
    
    # 计算边际风险贡献 (dσ/dw)
    marginal_contrib = (covariance_matrix @ weights) / port_volatility
    
    # 计算风险贡献 (w * dσ/dw)
    risk_contrib = weights * marginal_contrib
    
    # 计算风险贡献百分比
    risk_contrib_percent = risk_contrib / port_volatility
    
    return risk_contrib, risk_contrib_percent


def calculate_marginal_risk_contribution(
    weights: np.ndarray,
    covariance_matrix: np.ndarray,
    portfolio_volatility: Optional[float] = None
) -> np.ndarray:
    """
    计算各资产的边际风险贡献 (投资组合风险对资产权重的偏导数)
    
    参数:
        weights: 资产权重向量
        covariance_matrix: 协方差矩阵
        portfolio_volatility: 投资组合总波动率，如果未提供则计算
        
    返回:
        np.ndarray: 边际风险贡献向量
    """
    # 如果未提供投资组合波动率，则计算
    if portfolio_volatility is None:
        port_variance = weights.T @ covariance_matrix @ weights
        portfolio_volatility = np.sqrt(port_variance)
    
    # 计算边际风险贡献
    marginal_contrib = (covariance_matrix @ weights) / portfolio_volatility
    
    return marginal_contrib


def calculate_risk_parity_score(risk_contribution_percent: np.ndarray) -> float:
    """
    计算投资组合的风险平价分数
    
    该分数衡量投资组合风险贡献分布的均匀程度，1为完全均匀（完美风险平价），
    0表示极度不均匀（所有风险集中在一个资产）
    
    参数:
        risk_contribution_percent: 风险贡献百分比数组或字典
        
    返回:
        float: 风险平价分数 (0-1 范围)
    """
    # 如果输入是字典，提取值列表
    if isinstance(risk_contribution_percent, dict):
        rc_values = np.array(list(risk_contribution_percent.values()))
    else:
        rc_values = np.array(risk_contribution_percent)
    
    # 只考虑有效资产（权重大于0）
    valid_rc = rc_values[rc_values > 0]
    
    if len(valid_rc) <= 1:
        return 1.0  # 只有一个资产，视为"平价"
    
    # 计算理想的平均风险贡献
    n = len(valid_rc)
    ideal_rc = 1.0 / n
    
    # 计算风险贡献偏离的绝对值总和
    deviation_sum = np.sum(np.abs(valid_rc - ideal_rc))
    
    # 计算最大可能偏离（最极端情况：一个资产占用全部风险）
    max_deviation = 2 * (1 - ideal_rc)  # 2 * (1 - 1/n)
    
    # 计算分数，将值映射到[0, 1]区间
    parity_score = 1 - (deviation_sum / max_deviation)
    
    return parity_score


def calculate_group_risk(
    asset_names: List[str],
    risk_contribution: Dict[str, float],
    asset_info: pd.DataFrame,
    group_by: List[str]
) -> Dict[str, Dict[str, float]]:
    """
    按指定分组计算风险贡献分布
    
    参数:
        asset_names: 资产名称列表
        risk_contribution: 风险贡献字典 {资产名称: 风险贡献值}
        asset_info: 资产信息DataFrame，应包含资产代码作为索引，且包含分组字段
        group_by: 分组字段列表
        
    返回:
        Dict[str, Dict[str, float]]: 按分组的风险分布，
                                    格式如 {'industry': {'Tech': 0.05, 'Finance': 0.03, ...}}
    """
    # 检查asset_info是否包含所有分组字段
    missing_cols = [col for col in group_by if col not in asset_info.columns]
    if missing_cols:
        raise ValueError(f"资产信息中缺少以下分组字段: {missing_cols}")
    
    # 转换风险贡献为DataFrame
    risk_df = pd.Series(risk_contribution).to_frame('risk_contribution')
    
    # 过滤有效资产（确保资产存在于asset_info中）
    valid_assets = set(risk_contribution.keys()) & set(asset_info.index)
    risk_df = risk_df.loc[list(valid_assets)]
    
    # 初始化结果字典
    group_risk_result = {}
    
    # 对每个分组字段进行计算
    for group_field in group_by:
        # 在索引上合并风险贡献与资产信息
        merged = risk_df.join(asset_info[group_field])
        
        # 按分组计算总风险贡献
        group_sum = merged.groupby(group_field)['risk_contribution'].sum()
        
        # 添加到结果字典
        group_risk_result[group_field] = group_sum.to_dict()
    
    return group_risk_result 