"""
优化结果分析模块

该模块提供了一系列工具用于分析投资组合优化的结果，包括：
1. 权重分析 - 分析投资组合权重的分布和特征
2. 风险分解 - 分析投资组合的风险来源和分布
3. 收益分解 - 分析投资组合的预期收益来源
4. 约束分析 - 分析优化约束的影响和效果
5. 敏感性分析 - 分析优化结果对输入参数的敏感性
6. 结果比较 - 比较不同优化方法或参数的结果
7. 优化器比较 - 比较不同优化方法在相同条件下的表现
8. 时间序列比较 - 分析优化结果在不同时间点的稳定性
9. 参数敏感性分析 - 评估优化结果对参数变化的敏感程度

用法示例：
```python
from src.portfolio.optimization.analysis import (
    analyze_weights,
    analyze_risk,
    analyze_return,
    analyze_constraints,
    compare_optimizers,
    compare_over_time,
    compare_parameters
)

# 分析投资组合权重
weight_analysis = analyze_weights(weights, asset_data)

# 分析风险分布
risk_analysis = analyze_risk(weights, covariance_matrix)

# 比较不同优化器的结果
comparison = compare_optimizers(optimizer_results, expected_returns, covariance_matrix)

# 分析优化结果随时间变化
time_analysis = compare_over_time(time_series_results, metrics_of_interest)

# 分析优化结果对参数变化的敏感性
param_analysis = compare_parameters(parameter_results, parameter_name)
```
"""

# 权重分析模块
from .weight_analysis import (
    analyze_weights,
    calculate_concentration, 
    calculate_diversification,
    calculate_turnover
)

# 风险分析模块
from .risk_analysis import (
    analyze_risk,
    calculate_risk_contribution,
    calculate_marginal_risk_contribution,
    calculate_risk_parity_score
)

# 约束分析模块
from .constraint_analysis import (
    analyze_constraints,
    identify_binding_constraints,
    calculate_constraint_impact
)

# 敏感性分析模块
from .sensitivity import (
    run_sensitivity_analysis,
    analyze_return_sensitivity
)

# 优化器比较模块
# {{ AURA-X: Add - 完善投资组合优化分析模块. Approval: 寸止(ID:投资组合分析). }}
from .comparison import (
    compare_optimizers,
    compare_over_time,
    compare_parameters
)

# 导入接口定义
from .interfaces import (
    AnalysisResult,
    WeightAnalysisResult,
    RiskAnalysisResult,
    ReturnAnalysisResult,
    ConstraintAnalysisResult,
    SensitivityAnalysisResult,
    OptimizerComparisonResult
)

# 导出主要功能
__all__ = [
    # 权重分析
    'analyze_weights',
    'calculate_concentration',
    'calculate_diversification',
    'calculate_turnover',
    
    # 风险分析
    'analyze_risk',
    'calculate_risk_contribution',
    'calculate_marginal_risk_contribution',
    'calculate_risk_parity_score',
    
    # 约束分析
    'analyze_constraints',
    'identify_binding_constraints',
    'calculate_constraint_impact',

    # 敏感性分析
    'run_sensitivity_analysis',
    'analyze_return_sensitivity',

    # 优化器比较
    'compare_optimizers',
    'compare_over_time',
    'compare_parameters',
    
    # 接口类
    'AnalysisResult',
    'WeightAnalysisResult',
    'RiskAnalysisResult',
    'ReturnAnalysisResult',
    'ConstraintAnalysisResult',
    'SensitivityAnalysisResult',
    'OptimizerComparisonResult'
] 