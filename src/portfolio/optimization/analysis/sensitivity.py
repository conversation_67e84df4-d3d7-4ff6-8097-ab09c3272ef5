"""
敏感性分析模块

该模块提供了分析投资组合优化结果对输入参数敏感性的工具，包括：
1. 收益率敏感性 - 分析优化结果对预期收益率估计的敏感性
2. 波动率敏感性 - 分析优化结果对波动率估计的敏感性
3. 相关系数敏感性 - 分析优化结果对相关系数估计的敏感性
4. 约束参数敏感性 - 分析优化结果对约束参数变化的敏感性
"""

from typing import Dict, List, Optional, Any, Tuple, Union, Callable
import numpy as np
import pandas as pd
from ..analysis.interfaces import SensitivityAnalysisResult


def run_sensitivity_analysis(
    optimizer_func: Callable,
    parameter_name: str,
    base_value: Union[float, np.ndarray],
    perturbation_range: List[float],
    fixed_params: Dict[str, Any]
) -> SensitivityAnalysisResult:
    """
    运行敏感性分析，测试参数变化对优化结果的影响
    
    参数:
        optimizer_func: 优化器函数，接受参数并返回权重和指标
        parameter_name: 扰动参数的名称
        base_value: 参数的基准值
        perturbation_range: 扰动范围列表（如[-0.2, -0.1, 0, 0.1, 0.2]）
        fixed_params: 固定参数字典，不随扰动变化的参数
        
    返回:
        SensitivityAnalysisResult: 敏感性分析结果对象
    """
    # 初始化结果容器
    metrics_by_perturbation = {}
    weights_by_perturbation = {}
    
    # 确定分析类型
    if 'return' in parameter_name.lower():
        analysis_type = 'Return'
    elif 'vol' in parameter_name.lower() or 'risk' in parameter_name.lower():
        analysis_type = 'Volatility'
    elif 'corr' in parameter_name.lower():
        analysis_type = 'Correlation'
    else:
        analysis_type = 'Parameter'
    
    # {{ AURA-X: Add - 完善投资组合优化分析模块. Approval: 寸止(ID:投资组合分析). }}
    # 实际敏感性分析实现

    # 对每个扰动值运行优化
    for perturb in perturbation_range:
        try:
            # 计算扰动后的参数值
            if isinstance(base_value, np.ndarray):
                perturbed_value = base_value * (1 + perturb)
            else:
                perturbed_value = base_value * (1 + perturb)

            # 创建扰动后的参数字典
            perturbed_params = fixed_params.copy()
            perturbed_params[parameter_name] = perturbed_value

            # 运行优化器
            result = optimizer_func(**perturbed_params)

            # 提取权重和指标
            if isinstance(result, dict):
                weights = result.get('weights', {})
                metrics = {
                    'risk': result.get('volatility', 0),
                    'return': result.get('expected_return', 0),
                    'sharpe': result.get('sharpe_ratio', 0)
                }
            else:
                # 如果返回的是权重向量，需要计算指标
                weights = {f'Asset_{i}': w for i, w in enumerate(result)}
                metrics = {'risk': 0, 'return': 0, 'sharpe': 0}  # 需要额外计算

            weights_by_perturbation[perturb] = weights

            # 存储指标
            for metric_name, value in metrics.items():
                if metric_name not in metrics_by_perturbation:
                    metrics_by_perturbation[metric_name] = []
                metrics_by_perturbation[metric_name].append(value)

        except Exception as e:
            # 如果优化失败，使用默认值
            weights_by_perturbation[perturb] = {}
            for metric_name in ['risk', 'return', 'sharpe']:
                if metric_name not in metrics_by_perturbation:
                    metrics_by_perturbation[metric_name] = []
                metrics_by_perturbation[metric_name].append(0)

    # 计算敏感性指标（线性回归斜率和R²）
    sensitivity_metrics = {}
    for metric_name, values in metrics_by_perturbation.items():
        if len(values) == len(perturbation_range):
            # 计算线性回归
            x = np.array(perturbation_range)
            y = np.array(values)

            # 计算斜率和截距
            if len(x) > 1 and np.std(x) > 0:
                slope, intercept = np.polyfit(x, y, 1)

                # 计算R²
                y_pred = slope * x + intercept
                ss_res = np.sum((y - y_pred) ** 2)
                ss_tot = np.sum((y - np.mean(y)) ** 2)
                r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0

                sensitivity_metrics[metric_name] = {
                    'slope': float(slope),
                    'intercept': float(intercept),
                    'r_squared': float(r_squared),
                    'sensitivity': abs(slope)  # 敏感性程度
                }
            else:
                sensitivity_metrics[metric_name] = {
                    'slope': 0.0,
                    'intercept': np.mean(y) if len(y) > 0 else 0.0,
                    'r_squared': 0.0,
                    'sensitivity': 0.0
                }
    
    # 创建并返回分析结果
    return SensitivityAnalysisResult(
        analysis_type=analysis_type,
        parameter_name=parameter_name,
        perturbation_range=perturbation_range,
        metrics_by_perturbation=metrics_by_perturbation,
        weights_by_perturbation=weights_by_perturbation,
        sensitivity_metrics=sensitivity_metrics
    )


def analyze_return_sensitivity(
    optimizer_func: Callable,
    expected_returns: np.ndarray,
    perturbation_scale: float = 0.2,
    num_points: int = 5,
    fixed_params: Optional[Dict[str, Any]] = None
) -> SensitivityAnalysisResult:
    """
    分析优化结果对预期收益率估计的敏感性
    
    参数:
        optimizer_func: 优化器函数
        expected_returns: 基准预期收益率向量
        perturbation_scale: 扰动比例（如0.2表示±20%）
        num_points: 扰动点数量
        fixed_params: 固定参数字典
        
    返回:
        SensitivityAnalysisResult: 敏感性分析结果
    """
    # 生成扰动范围
    perturbation_range = np.linspace(-perturbation_scale, perturbation_scale, num_points)
    
    # 使用通用敏感性分析函数
    return run_sensitivity_analysis(
        optimizer_func=optimizer_func,
        parameter_name='expected_returns',
        base_value=expected_returns,
        perturbation_range=perturbation_range.tolist(),
        fixed_params=fixed_params or {}
    )


def analyze_volatility_sensitivity(
    optimizer_func: Callable,
    volatilities: np.ndarray,
    perturbation_scale: float = 0.2,
    num_points: int = 5,
    fixed_params: Optional[Dict[str, Any]] = None
) -> SensitivityAnalysisResult:
    """
    分析优化结果对波动率估计的敏感性
    
    参数:
        optimizer_func: 优化器函数
        volatilities: 基准波动率向量
        perturbation_scale: 扰动比例（如0.2表示±20%）
        num_points: 扰动点数量
        fixed_params: 固定参数字典
        
    返回:
        SensitivityAnalysisResult: 敏感性分析结果
    """
    # 生成扰动范围
    perturbation_range = np.linspace(-perturbation_scale, perturbation_scale, num_points)
    
    # 使用通用敏感性分析函数
    return run_sensitivity_analysis(
        optimizer_func=optimizer_func,
        parameter_name='volatilities',
        base_value=volatilities,
        perturbation_range=perturbation_range.tolist(),
        fixed_params=fixed_params or {}
    )


def analyze_correlation_sensitivity(
    optimizer_func: Callable,
    correlation_matrix: np.ndarray,
    perturbation_scale: float = 0.2,
    num_points: int = 5,
    fixed_params: Optional[Dict[str, Any]] = None
) -> SensitivityAnalysisResult:
    """
    分析优化结果对相关系数估计的敏感性
    
    参数:
        optimizer_func: 优化器函数
        correlation_matrix: 基准相关系数矩阵
        perturbation_scale: 扰动比例（如0.2表示±20%）
        num_points: 扰动点数量
        fixed_params: 固定参数字典
        
    返回:
        SensitivityAnalysisResult: 敏感性分析结果
    """
    # 生成扰动范围
    perturbation_range = np.linspace(-perturbation_scale, perturbation_scale, num_points)
    
    # 使用通用敏感性分析函数
    return run_sensitivity_analysis(
        optimizer_func=optimizer_func,
        parameter_name='correlation',
        base_value=correlation_matrix,
        perturbation_range=perturbation_range.tolist(),
        fixed_params=fixed_params or {}
    ) 