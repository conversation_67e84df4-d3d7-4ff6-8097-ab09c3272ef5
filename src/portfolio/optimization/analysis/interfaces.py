"""
优化分析结果接口

定义了各种分析结果的数据结构和接口，包括：
- 基础分析结果接口
- 权重分析结果
- 风险分析结果
- 收益分析结果
- 约束分析结果
- 敏感性分析结果
- 优化器比较结果
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union, Tuple
import numpy as np
import pandas as pd
from datetime import datetime


class AnalysisResult(ABC):
    """分析结果基类"""
    
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """将分析结果转换为字典格式"""
        pass
    
    @abstractmethod
    def to_dataframe(self) -> pd.DataFrame:
        """将分析结果转换为DataFrame格式"""
        pass
    
    @abstractmethod
    def summary(self) -> str:
        """生成分析结果的文本摘要"""
        pass


@dataclass
class WeightAnalysisResult(AnalysisResult):
    """权重分析结果"""
    
    # 投资组合基本信息
    weights: Dict[str, float]  # 资产权重映射
    
    # 集中度指标
    concentration: Dict[str, float] = field(default_factory=dict)  # 各种集中度指标
    
    # 多样化指标
    diversification: Dict[str, float] = field(default_factory=dict)  # 各种多样化指标
    
    # 权重分布统计信息
    weight_stats: Dict[str, float] = field(default_factory=dict)  # 权重统计信息(最大/最小权重等)
    
    # 分组统计信息（如按行业、地区等）
    group_weights: Dict[str, Dict[str, float]] = field(default_factory=dict)  # 按分组的权重分布
    
    # 换手率相关指标（如果提供了先前权重）
    turnover: Optional[float] = None  # 总换手率
    turnover_details: Dict[str, float] = field(default_factory=dict)  # 详细的换手信息
    
    # 额外的自定义指标
    custom_metrics: Dict[str, Any] = field(default_factory=dict)  # 自定义指标
    
    def to_dict(self) -> Dict[str, Any]:
        """将分析结果转换为字典格式"""
        return {
            'weights': self.weights,
            'concentration': self.concentration,
            'diversification': self.diversification,
            'weight_stats': self.weight_stats,
            'group_weights': self.group_weights,
            'turnover': self.turnover,
            'turnover_details': self.turnover_details,
            'custom_metrics': self.custom_metrics
        }
    
    def to_dataframe(self) -> pd.DataFrame:
        """将分析结果转换为DataFrame格式"""
        # 创建权重DataFrame
        weight_df = pd.DataFrame(list(self.weights.items()), columns=['Asset', 'Weight'])
        
        # 添加分组信息（如果有）
        for group_name, group_data in self.group_weights.items():
            # 为每个资产找到对应的组
            group_map = {}
            for group, assets in group_data.items():
                for asset in assets:
                    group_map[asset] = group
            
            # 添加到DataFrame
            weight_df[f'Group_{group_name}'] = weight_df['Asset'].map(group_map)
        
        return weight_df
    
    def summary(self) -> str:
        """生成分析结果的文本摘要"""
        lines = []
        lines.append("==== 投资组合权重分析摘要 ====")
        
        # 资产数量
        lines.append(f"资产数量: {len(self.weights)}")
        
        # 权重范围
        if self.weight_stats:
            lines.append(f"最大权重: {self.weight_stats.get('max_weight', 'N/A'):.4f}")
            lines.append(f"最小权重: {self.weight_stats.get('min_weight', 'N/A'):.4f}")
            lines.append(f"平均权重: {self.weight_stats.get('mean_weight', 'N/A'):.4f}")
        
        # 集中度信息
        if self.concentration:
            lines.append("\n集中度指标:")
            for name, value in self.concentration.items():
                lines.append(f"  {name}: {value:.4f}")
        
        # 多样化信息
        if self.diversification:
            lines.append("\n多样化指标:")
            for name, value in self.diversification.items():
                lines.append(f"  {name}: {value:.4f}")
        
        # 分组信息
        if self.group_weights:
            lines.append("\n分组权重:")
            for group_name, group_data in self.group_weights.items():
                lines.append(f"  {group_name}:")
                for subgroup, weight in group_data.items():
                    lines.append(f"    {subgroup}: {weight:.4f}")
        
        # 换手率信息
        if self.turnover is not None:
            lines.append(f"\n总换手率: {self.turnover:.4f}")
        
        return "\n".join(lines)


@dataclass
class RiskAnalysisResult(AnalysisResult):
    """风险分析结果"""
    
    # 投资组合基本信息
    weights: Dict[str, float]  # 资产权重映射
    portfolio_risk: float  # 投资组合总风险
    
    # 风险分解
    risk_contribution: Dict[str, float] = field(default_factory=dict)  # 各资产风险贡献
    risk_contribution_percent: Dict[str, float] = field(default_factory=dict)  # 各资产风险贡献百分比
    
    # 风险平价指标
    risk_parity_score: Optional[float] = None  # 风险平价分数（0-1，1为完全平价）
    
    # 边际风险贡献
    marginal_risk_contribution: Dict[str, float] = field(default_factory=dict)  # 边际风险贡献
    
    # 分组风险信息（如按行业、地区等）
    group_risk: Dict[str, Dict[str, float]] = field(default_factory=dict)  # 按分组的风险分布
    
    # 额外的自定义指标
    custom_metrics: Dict[str, Any] = field(default_factory=dict)  # 自定义指标
    
    def to_dict(self) -> Dict[str, Any]:
        """将分析结果转换为字典格式"""
        return {
            'weights': self.weights,
            'portfolio_risk': self.portfolio_risk,
            'risk_contribution': self.risk_contribution,
            'risk_contribution_percent': self.risk_contribution_percent,
            'risk_parity_score': self.risk_parity_score,
            'marginal_risk_contribution': self.marginal_risk_contribution,
            'group_risk': self.group_risk,
            'custom_metrics': self.custom_metrics
        }
    
    def to_dataframe(self) -> pd.DataFrame:
        """将分析结果转换为DataFrame格式"""
        # 创建基础DataFrame
        assets = list(self.weights.keys())
        data = {
            'Weight': [self.weights.get(asset, 0) for asset in assets],
            'Risk_Contribution': [self.risk_contribution.get(asset, 0) for asset in assets],
            'Risk_Contribution_Percent': [self.risk_contribution_percent.get(asset, 0) for asset in assets],
            'Marginal_Risk_Contribution': [self.marginal_risk_contribution.get(asset, 0) for asset in assets]
        }
        
        # 添加分组信息（如果有）
        for group_name, group_data in self.group_risk.items():
            group_map = {}
            for group, risk in group_data.items():
                for asset in self.weights.keys():
                    if asset in self.group_risk.get(group_name, {}).get(group, []):
                        group_map[asset] = group
            
            # 添加到数据字典
            if group_map:
                data[f'Group_{group_name}'] = [group_map.get(asset, 'Unknown') for asset in assets]
        
        return pd.DataFrame(data, index=assets)
    
    def summary(self) -> str:
        """生成分析结果的文本摘要"""
        lines = []
        lines.append("==== 投资组合风险分析摘要 ====")
        
        # 总体风险
        lines.append(f"投资组合总风险: {self.portfolio_risk:.6f}")
        
        # 风险平价分数
        if self.risk_parity_score is not None:
            lines.append(f"风险平价分数: {self.risk_parity_score:.4f}")
        
        # 风险贡献前N资产
        if self.risk_contribution_percent:
            lines.append("\n风险贡献最高的资产:")
            sorted_risk = sorted(
                self.risk_contribution_percent.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5]  # 取前5个
            
            for asset, contrib in sorted_risk:
                lines.append(f"  {asset}: {contrib:.2%}")
        
        # 分组风险信息
        if self.group_risk:
            lines.append("\n分组风险分布:")
            for group_name, group_data in self.group_risk.items():
                lines.append(f"  {group_name}:")
                for subgroup, risk in sorted(
                    group_data.items(), 
                    key=lambda x: x[1], 
                    reverse=True
                ):
                    lines.append(f"    {subgroup}: {risk:.4f}")
        
        return "\n".join(lines)


@dataclass
class ReturnAnalysisResult(AnalysisResult):
    """收益分析结果"""
    
    # 投资组合基本信息
    weights: Dict[str, float]  # 资产权重映射
    expected_return: float  # 投资组合预期收益率
    
    # 收益分解
    return_contribution: Dict[str, float] = field(default_factory=dict)  # 各资产收益贡献
    return_contribution_percent: Dict[str, float] = field(default_factory=dict)  # 各资产收益贡献百分比
    
    # 分组收益信息（如按行业、地区等）
    group_return: Dict[str, Dict[str, float]] = field(default_factory=dict)  # 按分组的收益分布
    
    # 额外的自定义指标
    custom_metrics: Dict[str, Any] = field(default_factory=dict)  # 自定义指标
    
    def to_dict(self) -> Dict[str, Any]:
        """将分析结果转换为字典格式"""
        return {
            'weights': self.weights,
            'expected_return': self.expected_return,
            'return_contribution': self.return_contribution,
            'return_contribution_percent': self.return_contribution_percent,
            'group_return': self.group_return,
            'custom_metrics': self.custom_metrics
        }
    
    def to_dataframe(self) -> pd.DataFrame:
        """将分析结果转换为DataFrame格式"""
        # 创建基础DataFrame
        assets = list(self.weights.keys())
        data = {
            'Weight': [self.weights.get(asset, 0) for asset in assets],
            'Return_Contribution': [self.return_contribution.get(asset, 0) for asset in assets],
            'Return_Contribution_Percent': [self.return_contribution_percent.get(asset, 0) for asset in assets],
        }
        
        # 添加分组信息（如果有）
        for group_name in self.group_return:
            data[f'Group_{group_name}'] = [
                'Unknown' for _ in assets
            ]  # 占位，具体实现类似RiskAnalysisResult
        
        return pd.DataFrame(data, index=assets)
    
    def summary(self) -> str:
        """生成分析结果的文本摘要"""
        lines = []
        lines.append("==== 投资组合收益分析摘要 ====")
        
        # 总体收益
        lines.append(f"投资组合预期收益率: {self.expected_return:.6f}")
        
        # 收益贡献前N资产
        if self.return_contribution_percent:
            lines.append("\n收益贡献最高的资产:")
            sorted_return = sorted(
                self.return_contribution_percent.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5]  # 取前5个
            
            for asset, contrib in sorted_return:
                lines.append(f"  {asset}: {contrib:.2%}")
        
        # 分组收益信息
        if self.group_return:
            lines.append("\n分组收益分布:")
            for group_name, group_data in self.group_return.items():
                lines.append(f"  {group_name}:")
                for subgroup, ret in sorted(
                    group_data.items(), 
                    key=lambda x: x[1], 
                    reverse=True
                ):
                    lines.append(f"    {subgroup}: {ret:.4f}")
        
        return "\n".join(lines)


@dataclass
class ConstraintAnalysisResult(AnalysisResult):
    """约束分析结果"""
    
    # 约束信息
    constraint_info: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # 约束条件详情
    
    # 绑定约束（起作用的约束）
    binding_constraints: List[str] = field(default_factory=list)  # 绑定约束列表
    
    # 约束影响
    constraint_impact: Dict[str, float] = field(default_factory=dict)  # 各约束的影响度量
    
    # 无约束与有约束结果比较
    unconstrained_metrics: Dict[str, float] = field(default_factory=dict)  # 无约束情况的指标
    constrained_metrics: Dict[str, float] = field(default_factory=dict)  # 有约束情况的指标
    
    # 约束松弛值（如果适用）
    slack_values: Dict[str, float] = field(default_factory=dict)  # 各约束的松弛值
    
    # 约束冲突信息
    constraint_conflicts: Dict[str, List[str]] = field(default_factory=dict)  # 约束冲突信息
    
    # 额外的自定义指标
    custom_metrics: Dict[str, Any] = field(default_factory=dict)  # 自定义指标
    
    def to_dict(self) -> Dict[str, Any]:
        """将分析结果转换为字典格式"""
        return {
            'constraint_info': self.constraint_info,
            'binding_constraints': self.binding_constraints,
            'constraint_impact': self.constraint_impact,
            'unconstrained_metrics': self.unconstrained_metrics,
            'constrained_metrics': self.constrained_metrics,
            'slack_values': self.slack_values,
            'constraint_conflicts': self.constraint_conflicts,
            'custom_metrics': self.custom_metrics
        }
    
    def to_dataframe(self) -> pd.DataFrame:
        """将分析结果转换为DataFrame格式"""
        # 收集约束信息
        constraints = []
        for name, info in self.constraint_info.items():
            constraint_data = {
                'Name': name,
                'Type': info.get('type', 'Unknown'),
                'Binding': name in self.binding_constraints,
                'Impact': self.constraint_impact.get(name, 0),
                'Slack': self.slack_values.get(name, 0)
            }
            
            # 添加其他可能的信息
            for k, v in info.items():
                if k not in ['type']:  # 避免重复
                    constraint_data[k] = v
            
            constraints.append(constraint_data)
        
        return pd.DataFrame(constraints)
    
    def summary(self) -> str:
        """生成分析结果的文本摘要"""
        lines = []
        lines.append("==== 约束条件分析摘要 ====")
        
        # 约束总数
        lines.append(f"约束条件总数: {len(self.constraint_info)}")
        lines.append(f"绑定约束数量: {len(self.binding_constraints)}")
        
        # 绑定约束详情
        if self.binding_constraints:
            lines.append("\n绑定约束详情:")
            for constraint in self.binding_constraints:
                impact = self.constraint_impact.get(constraint, 'N/A')
                if isinstance(impact, (int, float)):
                    impact = f"{impact:.4f}"
                
                lines.append(f"  {constraint} (影响: {impact})")
        
        # 约束前后指标对比
        if self.unconstrained_metrics and self.constrained_metrics:
            common_keys = set(self.unconstrained_metrics.keys()) & set(self.constrained_metrics.keys())
            if common_keys:
                lines.append("\n约束前后指标对比:")
                for key in sorted(common_keys):
                    before = self.unconstrained_metrics[key]
                    after = self.constrained_metrics[key]
                    change = after - before
                    change_pct = change / before if before != 0 else float('inf')
                    
                    lines.append(f"  {key}:")
                    lines.append(f"    无约束: {before:.6f}")
                    lines.append(f"    有约束: {after:.6f}")
                    lines.append(f"    变化: {change:.6f} ({change_pct:.2%})")
        
        # 约束冲突信息
        if self.constraint_conflicts:
            lines.append("\n约束冲突信息:")
            for constraint, conflicts in self.constraint_conflicts.items():
                if conflicts:
                    lines.append(f"  {constraint} 与以下约束冲突:")
                    for conflict in conflicts:
                        lines.append(f"    - {conflict}")
        
        return "\n".join(lines)


@dataclass
class SensitivityAnalysisResult(AnalysisResult):
    """敏感性分析结果"""
    
    # 敏感性分析类型
    analysis_type: str  # 分析类型(收益率/波动率/相关系数等)
    parameter_name: str  # 扰动参数名称
    
    # 扰动范围
    perturbation_range: List[float] = field(default_factory=list)  # 扰动值列表
    
    # 结果指标随扰动变化
    metrics_by_perturbation: Dict[str, List[float]] = field(default_factory=dict)  # 指标随扰动变化的值
    
    # 权重随扰动变化（如适用）
    weights_by_perturbation: Dict[float, Dict[str, float]] = field(default_factory=dict)  # 权重随扰动变化
    
    # 敏感性指标
    sensitivity_metrics: Dict[str, Dict[str, float]] = field(default_factory=dict)  # 各指标的敏感性度量
    
    # 额外的自定义指标
    custom_metrics: Dict[str, Any] = field(default_factory=dict)  # 自定义指标
    
    def to_dict(self) -> Dict[str, Any]:
        """将分析结果转换为字典格式"""
        return {
            'analysis_type': self.analysis_type,
            'parameter_name': self.parameter_name,
            'perturbation_range': self.perturbation_range,
            'metrics_by_perturbation': self.metrics_by_perturbation,
            'weights_by_perturbation': self.weights_by_perturbation,
            'sensitivity_metrics': self.sensitivity_metrics,
            'custom_metrics': self.custom_metrics
        }
    
    def to_dataframe(self) -> pd.DataFrame:
        """将分析结果转换为DataFrame格式"""
        # 创建结果随扰动变化的DataFrame
        result_data = {'Perturbation': self.perturbation_range}
        
        # 添加各指标随扰动的变化
        for metric_name, values in self.metrics_by_perturbation.items():
            result_data[metric_name] = values
        
        return pd.DataFrame(result_data)
    
    def summary(self) -> str:
        """生成分析结果的文本摘要"""
        lines = []
        lines.append(f"==== {self.analysis_type}敏感性分析摘要 ====")
        lines.append(f"参数: {self.parameter_name}")
        
        # 扰动范围
        lines.append(f"扰动范围: {min(self.perturbation_range):.4f} 到 {max(self.perturbation_range):.4f}")
        
        # 敏感性指标
        if self.sensitivity_metrics:
            lines.append("\n敏感性指标:")
            for metric_name, sensitivity_data in self.sensitivity_metrics.items():
                lines.append(f"  {metric_name}:")
                for sensitivity_type, value in sensitivity_data.items():
                    lines.append(f"    {sensitivity_type}: {value:.6f}")
        
        # 权重变化（可选）
        if self.weights_by_perturbation:
            lines.append("\n权重变化摘要:")
            min_perturb = min(self.perturbation_range)
            max_perturb = max(self.perturbation_range)
            
            if min_perturb in self.weights_by_perturbation and max_perturb in self.weights_by_perturbation:
                min_weights = self.weights_by_perturbation[min_perturb]
                max_weights = self.weights_by_perturbation[max_perturb]
                
                # 找出变化最大的前几个资产
                weight_changes = {}
                for asset in set(min_weights.keys()) | set(max_weights.keys()):
                    min_w = min_weights.get(asset, 0)
                    max_w = max_weights.get(asset, 0)
                    weight_changes[asset] = abs(max_w - min_w)
                
                # 获取变化最大的N个资产
                top_changes = sorted(weight_changes.items(), key=lambda x: x[1], reverse=True)[:5]
                
                lines.append(f"  权重变化最大的资产:")
                for asset, change in top_changes:
                    min_w = min_weights.get(asset, 0)
                    max_w = max_weights.get(asset, 0)
                    lines.append(f"    {asset}: {min_w:.4f} → {max_w:.4f} (Δ = {change:.4f})")
        
        return "\n".join(lines)


@dataclass
class OptimizerComparisonResult(AnalysisResult):
    """优化器比较结果"""
    
    # 比较的优化器名称
    optimizer_names: List[str]  # 比较的优化器名称列表
    
    # 各优化器的权重
    weights_by_optimizer: Dict[str, Dict[str, float]] = field(default_factory=dict)  # 各优化器的权重
    
    # 各优化器的性能指标
    metrics_by_optimizer: Dict[str, Dict[str, float]] = field(default_factory=dict)  # 各优化器的指标
    
    # 各优化器的风险分解
    risk_by_optimizer: Dict[str, Dict[str, float]] = field(default_factory=dict)  # 各优化器的风险分解
    
    # 各优化器的收益分解
    return_by_optimizer: Dict[str, Dict[str, float]] = field(default_factory=dict)  # 各优化器的收益分解
    
    # 相似度指标
    similarity_matrix: Dict[str, Dict[str, float]] = field(default_factory=dict)  # 优化器间相似度矩阵
    
    # 差异资产信息
    differential_assets: Dict[str, List[Tuple[str, float]]] = field(default_factory=dict)  # 优化器间差异最大的资产
    
    # 额外的自定义指标
    custom_metrics: Dict[str, Any] = field(default_factory=dict)  # 自定义指标
    
    def to_dict(self) -> Dict[str, Any]:
        """将分析结果转换为字典格式"""
        return {
            'optimizer_names': self.optimizer_names,
            'weights_by_optimizer': self.weights_by_optimizer,
            'metrics_by_optimizer': self.metrics_by_optimizer,
            'risk_by_optimizer': self.risk_by_optimizer,
            'return_by_optimizer': self.return_by_optimizer,
            'similarity_matrix': self.similarity_matrix,
            'differential_assets': self.differential_assets,
            'custom_metrics': self.custom_metrics
        }
    
    def to_dataframe(self) -> pd.DataFrame:
        """将分析结果转换为DataFrame格式"""
        # 创建指标对比DataFrame
        metrics_data = []
        
        for optimizer_name in self.optimizer_names:
            row_data = {'Optimizer': optimizer_name}
            
            # 添加指标
            if optimizer_name in self.metrics_by_optimizer:
                for metric_name, value in self.metrics_by_optimizer[optimizer_name].items():
                    row_data[metric_name] = value
            
            metrics_data.append(row_data)
        
        return pd.DataFrame(metrics_data)
    
    def summary(self) -> str:
        """生成分析结果的文本摘要"""
        lines = []
        lines.append("==== 优化器比较摘要 ====")
        
        # 比较的优化器
        lines.append(f"比较的优化器: {', '.join(self.optimizer_names)}")
        
        # 关键指标对比
        if self.metrics_by_optimizer:
            lines.append("\n关键指标对比:")
            
            # 找出所有指标
            all_metrics = set()
            for metrics in self.metrics_by_optimizer.values():
                all_metrics.update(metrics.keys())
            
            # 生成对比表格
            for metric_name in sorted(all_metrics):
                lines.append(f"  {metric_name}:")
                for optimizer in self.optimizer_names:
                    if optimizer in self.metrics_by_optimizer and metric_name in self.metrics_by_optimizer[optimizer]:
                        value = self.metrics_by_optimizer[optimizer][metric_name]
                        lines.append(f"    {optimizer}: {value:.6f}")
        
        # 相似度矩阵（如果有）
        if self.similarity_matrix:
            lines.append("\n优化器相似度:")
            for opt1 in self.optimizer_names:
                for opt2 in self.optimizer_names:
                    if opt1 != opt2 and opt1 in self.similarity_matrix and opt2 in self.similarity_matrix[opt1]:
                        similarity = self.similarity_matrix[opt1][opt2]
                        lines.append(f"  {opt1} vs {opt2}: {similarity:.4f}")
        
        # 差异资产（如果有）
        if self.differential_assets:
            lines.append("\n差异最大的资产:")
            for pair, assets in self.differential_assets.items():
                if assets:
                    lines.append(f"  {pair}:")
                    for asset, diff in assets[:5]:  # 只显示前5个
                        lines.append(f"    {asset}: {diff:.4f}")
        
        return "\n".join(lines) 