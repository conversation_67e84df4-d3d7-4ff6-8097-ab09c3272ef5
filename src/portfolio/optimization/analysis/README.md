# 投资组合优化分析模块

本模块提供了一系列用于分析投资组合优化结果的高级工具，帮助投资者深入理解优化结果、进行敏感性分析、比较不同优化方法，以及评估投资组合在不同市场环境下的表现。

## 模块组成

优化分析模块主要包含以下几个部分：

### 1. 约束分析工具 (constraint_analysis.py)

- 用于分析投资组合优化约束条件的影响
- 识别约束条件对优化结果的边界作用和冲突情况
- 包含可视化工具，直观展示约束条件的影响

```python
from portfolio.optimization.analysis import analyze_constraints

# 分析约束条件
constraint_analysis = analyze_constraints(
    optimizer=my_optimizer,
    expected_returns=expected_returns,
    covariance_matrix=cov_matrix,
    constraints=constraints
)

# 获取分析结果
binding_constraints = constraint_analysis.get_binding_constraints()
constraint_impact = constraint_analysis.get_constraint_impact()

# 可视化约束影响
constraint_analysis.plot_constraint_impact()
```

### 2. 风险因子敏感性分析 (factor_sensitivity.py)

- 分析投资组合对不同风险因子变化的敏感性
- 量化各因子对总风险的贡献程度
- 支持单因子和多因子敏感性测试

```python
from portfolio.optimization.analysis import run_factor_sensitivity_analysis

# 运行因子敏感性分析
sensitivity_result = run_factor_sensitivity_analysis(
    portfolio_weights=weights,
    factor_exposures=exposures,
    factor_covariance=factor_cov,
    specific_risk=spec_risk
)

# 获取敏感性指标
factor_contributions = sensitivity_result.get_factor_contributions()
risk_decomposition = sensitivity_result.get_risk_decomposition()

# 可视化敏感性分析结果
sensitivity_result.plot_factor_contributions()
sensitivity_result.plot_sensitivity_heatmap()
```

### 3. 情景分析工具 (scenario_analysis.py)

- 支持历史情景和假设情景下的投资组合表现分析
- 提供蒙特卡洛模拟工具，评估不同市场环境下的表现
- 分析投资组合在极端市场条件下的尾部风险

```python
from portfolio.optimization.analysis import run_historical_scenario_analysis, run_stress_test

# 运行历史情景分析
historical_result = run_historical_scenario_analysis(
    portfolio_weights=weights,
    historical_data=historical_returns,
    scenarios=["2008金融危机", "2020新冠疫情"]
)

# 运行压力测试
stress_test_result = run_stress_test(
    portfolio_weights=weights,
    risk_factors=factors,
    stress_scenarios=custom_scenarios
)

# 可视化情景分析结果
historical_result.plot_scenario_comparison()
stress_test_result.plot_stress_impact()
```

### 4. 优化器比较工具 (optimizer_comparison.py)

- 比较不同优化方法在相同市场条件下的表现
- 分析优化结果在不同时间点的稳定性和一致性
- 评估优化结果对参数变化的敏感程度

```python
from portfolio.optimization.analysis import compare_optimizers, compare_over_time, compare_parameters

# 比较不同优化器
comparison_result = compare_optimizers(
    optimizer_results=results_dict,
    expected_returns=expected_returns,
    covariance_matrix=cov_matrix,
    asset_names=asset_names
)

# 分析时间序列稳定性
time_analysis = compare_over_time(
    time_series_results=time_results,
    metrics_of_interest=['sharpe_ratio', 'volatility', 'expected_return'],
    asset_names=asset_names
)

# 参数敏感性分析
parameter_analysis = compare_parameters(
    parameter_results=param_results,
    parameter_name="风险厌恶系数",
    metrics_of_interest=['sharpe_ratio', 'volatility', 'expected_return'],
    asset_names=asset_names
)
```

### 5. 结果验证工具 (validation.py)

- 验证优化结果是否满足约束条件
- 检查权重的合法性和数值精度
- 对比不同优化结果的差异和性能

```python
from portfolio.optimization.analysis import validate_optimization_result

# 验证优化结果
validation_result = validate_optimization_result(
    weights=optimized_weights,
    constraints=constraints,
    tolerance=1e-6
)

# 检查是否有任何违反约束的情况
if validation_result.is_valid():
    print("优化结果有效!")
else:
    print("优化结果存在问题:", validation_result.get_issues())
```

## 使用示例

以下示例展示了如何使用分析模块来全面评估投资组合优化结果：

```python
from portfolio.optimization.optimizer import MeanVarianceOptimizer
from portfolio.optimization.analysis import (
    analyze_constraints,
    compare_optimizers,
    run_factor_sensitivity_analysis,
    validate_optimization_result
)

# 创建并运行优化器
optimizer = MeanVarianceOptimizer(
    objective="sharpe_ratio",
    name="均值方差优化器"
)

weights = optimizer.optimize(
    expected_returns=returns,
    covariance_matrix=cov_matrix,
    constraints={"weight_bounds": (0, 0.2)}
)

# 分析约束条件影响
constraint_analysis = analyze_constraints(
    optimizer=optimizer,
    expected_returns=returns,
    covariance_matrix=cov_matrix,
    constraints={"weight_bounds": (0, 0.2)}
)

# 验证优化结果
validation = validate_optimization_result(
    weights=weights,
    constraints={"weight_bounds": (0, 0.2)},
    tolerance=1e-6
)

# 因子敏感性分析
sensitivity = run_factor_sensitivity_analysis(
    portfolio_weights=weights,
    factor_exposures=exposures,
    factor_covariance=factor_cov
)

# 创建报告
print("优化结果摘要:")
print(f"夏普比率: {optimizer.get_performance_metric('sharpe_ratio'):.4f}")
print(f"约束条件影响最大的资产: {constraint_analysis.get_most_constrained_assets()}")
print(f"结果是否有效: {validation.is_valid()}")
print(f"主要风险因子贡献: {sensitivity.get_top_factor_contributions(3)}")
```

## 如何扩展

如果您需要添加新的分析功能，可以按照以下步骤进行：

1. 在相应的模块文件中添加新的分析函数
2. 确保函数返回结构化的分析结果对象，包含必要的方法和属性
3. 为新功能添加适当的可视化方法
4. 更新相应的单元测试和示例脚本
5. 在模块的 `__init__.py` 文件中导出新函数

## 注意事项

- 大多数分析工具需要优化后的投资组合权重作为输入
- 某些高级分析功能可能需要额外的数据，如因子暴露、历史收益等
- 可视化工具返回 Matplotlib 图形对象，可以进一步自定义后保存或显示 