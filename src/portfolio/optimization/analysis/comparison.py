"""
优化器比较模块

该模块提供了比较不同优化方法结果的工具，包括：
1. 优化器间比较 - 比较不同优化算法的结果
2. 时间序列比较 - 比较不同时间点的优化结果
3. 参数比较 - 比较不同参数设置下的优化结果
"""

from typing import Dict, List, Optional, Any, Tuple, Union
import numpy as np
import pandas as pd
from ..analysis.interfaces import OptimizerComparisonResult


def compare_optimizers(
    optimizer_results: Dict[str, Dict[str, Any]],
    covariance_matrix: Optional[np.ndarray] = None,
    expected_returns: Optional[np.ndarray] = None,
    asset_names: Optional[List[str]] = None
) -> OptimizerComparisonResult:
    """
    比较不同优化器的结果
    
    参数:
        optimizer_results: 不同优化器的结果，格式为 {优化器名称: {结果指标}}
        covariance_matrix: 协方差矩阵，用于计算风险指标
        expected_returns: 预期收益率，用于计算收益指标
        asset_names: 资产名称列表，与协方差矩阵顺序对应
        
    返回:
        OptimizerComparisonResult: 优化器比较结果对象
    """
    # 提取优化器名称
    optimizer_names = list(optimizer_results.keys())
    
    # 提取各优化器的权重
    weights_by_optimizer = {}
    for name, result in optimizer_results.items():
        if 'weights' in result:
            weights_by_optimizer[name] = result['weights']
    
    # 提取各优化器的指标
    metrics_by_optimizer = {}
    for name, result in optimizer_results.items():
        if 'metrics' in result:
            metrics_by_optimizer[name] = result['metrics']
    
    # 计算风险分解和收益分解（如果提供了协方差矩阵和预期收益率）
    risk_by_optimizer = {}
    return_by_optimizer = {}
    
    if covariance_matrix is not None and asset_names:
        # 示例框架，实际实现会包含风险计算逻辑
        for name, weights in weights_by_optimizer.items():
            risk_by_optimizer[name] = {'volatility': 0.1}  # 示例数据
    
    if expected_returns is not None and asset_names:
        # 示例框架，实际实现会包含收益计算逻辑
        for name, weights in weights_by_optimizer.items():
            return_by_optimizer[name] = {'expected_return': 0.08}  # 示例数据
    
    # 计算相似度矩阵
    similarity_matrix = calculate_similarity_matrix(weights_by_optimizer)
    
    # 计算差异资产
    differential_assets = identify_differential_assets(weights_by_optimizer)
    
    # 创建并返回比较结果
    return OptimizerComparisonResult(
        optimizer_names=optimizer_names,
        weights_by_optimizer=weights_by_optimizer,
        metrics_by_optimizer=metrics_by_optimizer,
        risk_by_optimizer=risk_by_optimizer,
        return_by_optimizer=return_by_optimizer,
        similarity_matrix=similarity_matrix,
        differential_assets=differential_assets
    )


def compare_over_time(
    time_series_results: Dict[str, Dict[str, Any]],
    metrics_of_interest: Optional[List[str]] = None,
    asset_names: Optional[List[str]] = None,
    reference_time: Optional[str] = None
) -> Dict[str, pd.DataFrame]:
    """
    比较不同时间点的优化结果
    
    参数:
        time_series_results: 不同时间点的优化结果，格式为 {时间点: {结果指标}}
        metrics_of_interest: 感兴趣的指标列表
        asset_names: 资产名称列表，如果为None则从权重中推断
        reference_time: 参考时间点，用于计算相对变化，如果为None则使用第一个时间点
        
    返回:
        Dict[str, pd.DataFrame]: 不同指标随时间变化的DataFrame
    """
    # 验证输入
    if not time_series_results:
        raise ValueError("时间序列结果不能为空")
    
    # 获取时间点列表，并按时间排序
    time_points = sorted(time_series_results.keys())
    
    # 如果没有指定参考时间点，则使用第一个时间点
    if reference_time is None and time_points:
        reference_time = time_points[0]
    
    # 初始化结果容器
    time_series_metrics = {}
    weights_over_time = {}
    
    # 提取每个时间点的权重
    for time_point, result in time_series_results.items():
        if 'weights' in result:
            weights_over_time[time_point] = result['weights']
            
            # 如果asset_names未提供，则从权重中推断
            if asset_names is None:
                asset_names = list(result['weights'].keys())
    
    # 转换权重为时间序列DataFrame
    if weights_over_time:
        weights_data = []
        for time_point, weights in weights_over_time.items():
            row_data = {'time_point': time_point}
            row_data.update(weights)
            weights_data.append(row_data)
        
        weights_df = pd.DataFrame(weights_data)
        weights_df.set_index('time_point', inplace=True)
        time_series_metrics['weights'] = weights_df
    
    # 提取每个时间点的指标
    metrics_over_time = {}
    all_metrics = set()
    
    for time_point, result in time_series_results.items():
        if 'metrics' in result:
            metrics_over_time[time_point] = result['metrics']
            all_metrics.update(result['metrics'].keys())
    
    # 如果没有指定感兴趣的指标，则使用所有可用指标
    if metrics_of_interest is None:
        metrics_of_interest = sorted(all_metrics)
    
    # 转换指标为时间序列DataFrame
    if metrics_over_time:
        metrics_data = []
        for time_point, metrics in metrics_over_time.items():
            row_data = {'time_point': time_point}
            for metric in metrics_of_interest:
                if metric in metrics:
                    row_data[metric] = metrics[metric]
            metrics_data.append(row_data)
        
        metrics_df = pd.DataFrame(metrics_data)
        metrics_df.set_index('time_point', inplace=True)
        time_series_metrics['metrics'] = metrics_df
    
    # 计算权重变化（相对于参考时间点）
    if weights_over_time and reference_time in weights_over_time:
        weight_changes = {}
        reference_weights = weights_over_time[reference_time]
        
        for time_point, weights in weights_over_time.items():
            if time_point != reference_time:
                # 计算每个资产的权重变化
                changes = {}
                for asset in set(reference_weights.keys()) | set(weights.keys()):
                    ref_weight = reference_weights.get(asset, 0)
                    current_weight = weights.get(asset, 0)
                    changes[asset] = current_weight - ref_weight
                
                weight_changes[time_point] = changes
        
        # 转换权重变化为DataFrame
        if weight_changes:
            changes_data = []
            for time_point, changes in weight_changes.items():
                row_data = {'time_point': time_point}
                row_data.update(changes)
                changes_data.append(row_data)
            
            changes_df = pd.DataFrame(changes_data)
            changes_df.set_index('time_point', inplace=True)
            time_series_metrics['weight_changes'] = changes_df
    
    # 计算权重波动性
    if len(weights_over_time) > 1 and asset_names:
        # 创建权重时间序列矩阵
        weight_matrix = np.zeros((len(time_points), len(asset_names)))
        
        for i, time_point in enumerate(time_points):
            weights = weights_over_time.get(time_point, {})
            for j, asset in enumerate(asset_names):
                weight_matrix[i, j] = weights.get(asset, 0)
        
        # 计算每个资产权重的标准差，作为波动性度量
        weight_volatility = np.std(weight_matrix, axis=0)
        
        # 创建波动性DataFrame
        volatility_data = {'asset': asset_names, 'weight_volatility': weight_volatility}
        volatility_df = pd.DataFrame(volatility_data)
        volatility_df.set_index('asset', inplace=True)
        time_series_metrics['weight_volatility'] = volatility_df
    
    # 计算换手率时间序列
    if len(weights_over_time) > 1:
        turnover_data = []
        
        for i in range(1, len(time_points)):
            prev_time = time_points[i-1]
            curr_time = time_points[i]
            
            prev_weights = weights_over_time.get(prev_time, {})
            curr_weights = weights_over_time.get(curr_time, {})
            
            # 计算换手率（权重变化的绝对值之和的一半）
            turnover = 0
            for asset in set(prev_weights.keys()) | set(curr_weights.keys()):
                prev_weight = prev_weights.get(asset, 0)
                curr_weight = curr_weights.get(asset, 0)
                turnover += abs(curr_weight - prev_weight)
            
            turnover *= 0.5  # 总换手率是权重变化绝对值之和的一半
            
            turnover_data.append({'time_point': curr_time, 'turnover': turnover})
        
        # 创建换手率DataFrame
        turnover_df = pd.DataFrame(turnover_data)
        turnover_df.set_index('time_point', inplace=True)
        time_series_metrics['turnover'] = turnover_df
    
    return time_series_metrics


def compare_parameters(
    parameter_results: Dict[str, Dict[str, Any]],
    parameter_name: str,
    metrics_of_interest: Optional[List[str]] = None,
    asset_names: Optional[List[str]] = None,
    reference_parameter: Optional[str] = None,
    sort_parameters: bool = True
) -> Dict[str, pd.DataFrame]:
    """
    比较不同参数设置下的优化结果
    
    参数:
        parameter_results: 不同参数设置的优化结果，格式为 {参数值: {结果指标}}
        parameter_name: 参数名称
        metrics_of_interest: 感兴趣的指标列表
        asset_names: 资产名称列表，如果为None则从权重中推断
        reference_parameter: 参考参数值，用于计算相对变化，如果为None则使用第一个参数值
        sort_parameters: 是否对参数值进行排序，对于数值型参数有意义
        
    返回:
        Dict[str, pd.DataFrame]: 不同指标随参数变化的DataFrame
    """
    # 验证输入
    if not parameter_results:
        raise ValueError("参数结果不能为空")
    
    # 获取参数值列表
    parameter_values = list(parameter_results.keys())
    
    # 尝试将参数值转换为数值型，并排序
    if sort_parameters:
        try:
            # 尝试将参数值转换为浮点数
            numeric_params = [(float(p), p) for p in parameter_values]
            numeric_params.sort()  # 按数值大小排序
            parameter_values = [p[1] for p in numeric_params]  # 提取原始参数值
        except ValueError:
            # 如果转换失败，说明参数不是数值型，按字符串排序
            parameter_values.sort()
    
    # 如果没有指定参考参数值，则使用第一个参数值
    if reference_parameter is None and parameter_values:
        reference_parameter = parameter_values[0]
    
    # 初始化结果容器
    parameter_metrics = {}
    weights_by_parameter = {}
    
    # 提取每个参数设置的权重
    for param_value, result in parameter_results.items():
        if 'weights' in result:
            weights_by_parameter[param_value] = result['weights']
            
            # 如果asset_names未提供，则从权重中推断
            if asset_names is None:
                asset_names = list(result['weights'].keys())
    
    # 转换权重为参数变化DataFrame
    if weights_by_parameter:
        weights_data = []
        for param_value, weights in weights_by_parameter.items():
            row_data = {parameter_name: param_value}
            row_data.update(weights)
            weights_data.append(row_data)
        
        weights_df = pd.DataFrame(weights_data)
        weights_df.set_index(parameter_name, inplace=True)
        parameter_metrics['weights'] = weights_df
    
    # 提取每个参数设置的指标
    metrics_by_parameter = {}
    all_metrics = set()
    
    for param_value, result in parameter_results.items():
        if 'metrics' in result:
            metrics_by_parameter[param_value] = result['metrics']
            all_metrics.update(result['metrics'].keys())
    
    # 如果没有指定感兴趣的指标，则使用所有可用指标
    if metrics_of_interest is None:
        metrics_of_interest = sorted(all_metrics)
    
    # 转换指标为参数变化DataFrame
    if metrics_by_parameter:
        metrics_data = []
        for param_value, metrics in metrics_by_parameter.items():
            row_data = {parameter_name: param_value}
            for metric in metrics_of_interest:
                if metric in metrics:
                    row_data[metric] = metrics[metric]
            metrics_data.append(row_data)
        
        metrics_df = pd.DataFrame(metrics_data)
        metrics_df.set_index(parameter_name, inplace=True)
        
        # 尝试按照参数值的数值大小排序
        try:
            metrics_df = metrics_df.sort_index(key=lambda x: x.astype(float))
        except (TypeError, ValueError):
            # 如果转换失败，保持原始顺序
            pass
            
        parameter_metrics['metrics'] = metrics_df
    
    # 计算权重变化（相对于参考参数）
    if weights_by_parameter and reference_parameter in weights_by_parameter:
        weight_changes = {}
        reference_weights = weights_by_parameter[reference_parameter]
        
        for param_value, weights in weights_by_parameter.items():
            if param_value != reference_parameter:
                # 计算每个资产的权重变化
                changes = {}
                for asset in set(reference_weights.keys()) | set(weights.keys()):
                    ref_weight = reference_weights.get(asset, 0)
                    current_weight = weights.get(asset, 0)
                    changes[asset] = current_weight - ref_weight
                
                weight_changes[param_value] = changes
        
        # 转换权重变化为DataFrame
        if weight_changes:
            changes_data = []
            for param_value, changes in weight_changes.items():
                row_data = {parameter_name: param_value}
                row_data.update(changes)
                changes_data.append(row_data)
            
            changes_df = pd.DataFrame(changes_data)
            changes_df.set_index(parameter_name, inplace=True)
            
            # 尝试按照参数值的数值大小排序
            try:
                changes_df = changes_df.sort_index(key=lambda x: x.astype(float))
            except (TypeError, ValueError):
                # 如果转换失败，保持原始顺序
                pass
                
            parameter_metrics['weight_changes'] = changes_df
    
    # 计算权重敏感性
    if len(weights_by_parameter) > 1 and asset_names and all(isinstance(float(p), float) for p in parameter_values):
        # 将参数值转换为浮点数
        param_values_float = np.array([float(p) for p in parameter_values])
        
        # 创建权重对参数的敏感性矩阵
        sensitivity_data = {}
        
        for asset in asset_names:
            # 提取该资产在不同参数下的权重
            asset_weights = np.array([weights_by_parameter.get(p, {}).get(asset, 0) for p in parameter_values])
            
            # 如果权重有变化，计算敏感性（权重变化率 / 参数变化率）
            if len(param_values_float) > 1 and np.std(asset_weights) > 0:
                # 计算平均敏感性（使用线性回归斜率的简化方法）
                param_mean = np.mean(param_values_float)
                weight_mean = np.mean(asset_weights)
                
                numerator = np.sum((param_values_float - param_mean) * (asset_weights - weight_mean))
                denominator = np.sum((param_values_float - param_mean) ** 2)
                
                if denominator != 0:
                    sensitivity = numerator / denominator
                    sensitivity_data[asset] = sensitivity
        
        # 创建敏感性DataFrame
        if sensitivity_data:
            sensitivity_df = pd.DataFrame({
                'asset': list(sensitivity_data.keys()),
                'sensitivity': list(sensitivity_data.values())
            })
            sensitivity_df.set_index('asset', inplace=True)
            sensitivity_df = sensitivity_df.sort_values('sensitivity', ascending=False)
            parameter_metrics['weight_sensitivity'] = sensitivity_df
    
    # 计算指标敏感性
    if len(metrics_by_parameter) > 1 and all(isinstance(float(p), float) for p in parameter_values):
        # 将参数值转换为浮点数
        param_values_float = np.array([float(p) for p in parameter_values])
        
        # 创建指标对参数的敏感性矩阵
        metric_sensitivity_data = {}
        
        for metric in metrics_of_interest:
            # 提取该指标在不同参数下的值
            metric_values = np.array([
                metrics_by_parameter.get(p, {}).get(metric, np.nan) 
                for p in parameter_values
            ])
            
            # 移除NaN值
            valid_indices = ~np.isnan(metric_values)
            valid_params = param_values_float[valid_indices]
            valid_metrics = metric_values[valid_indices]
            
            # 如果有足够的有效数据点，计算敏感性
            if len(valid_params) > 1 and np.std(valid_metrics) > 0:
                # 计算平均敏感性（使用线性回归斜率的简化方法）
                param_mean = np.mean(valid_params)
                metric_mean = np.mean(valid_metrics)
                
                numerator = np.sum((valid_params - param_mean) * (valid_metrics - metric_mean))
                denominator = np.sum((valid_params - param_mean) ** 2)
                
                if denominator != 0:
                    sensitivity = numerator / denominator
                    
                    # 计算弹性（相对变化比）
                    elasticity = sensitivity * (param_mean / metric_mean) if metric_mean != 0 else np.nan
                    
                    metric_sensitivity_data[metric] = {
                        'sensitivity': sensitivity,
                        'elasticity': elasticity
                    }
        
        # 创建指标敏感性DataFrame
        if metric_sensitivity_data:
            sensitivity_data = []
            for metric, data in metric_sensitivity_data.items():
                sensitivity_data.append({
                    'metric': metric,
                    'sensitivity': data['sensitivity'],
                    'elasticity': data['elasticity']
                })
            
            sensitivity_df = pd.DataFrame(sensitivity_data)
            sensitivity_df.set_index('metric', inplace=True)
            sensitivity_df = sensitivity_df.sort_values('sensitivity', key=abs, ascending=False)
            parameter_metrics['metric_sensitivity'] = sensitivity_df
    
    return parameter_metrics


def calculate_similarity_matrix(
    weights_by_optimizer: Dict[str, Dict[str, float]]
) -> Dict[str, Dict[str, float]]:
    """
    计算不同优化器权重的相似度矩阵
    
    参数:
        weights_by_optimizer: 不同优化器的权重，格式为 {优化器名称: {资产: 权重}}
        
    返回:
        Dict[str, Dict[str, float]]: 相似度矩阵，格式为 {优化器1: {优化器2: 相似度}}
    """
    # 初始化相似度矩阵
    similarity_matrix = {name: {} for name in weights_by_optimizer}
    
    # 计算每对优化器之间的权重相似度
    optimizer_names = list(weights_by_optimizer.keys())
    for i, name1 in enumerate(optimizer_names):
        for name2 in optimizer_names[i:]:
            # 示例使用余弦相似度，实际实现可能包含更复杂的相似度计算
            similarity = 0.5  # 示例数据，实际会计算真实相似度
            
            # 将相似度添加到矩阵中（矩阵是对称的）
            similarity_matrix[name1][name2] = similarity
            if name1 != name2:
                similarity_matrix[name2][name1] = similarity
    
    return similarity_matrix


def identify_differential_assets(
    weights_by_optimizer: Dict[str, Dict[str, float]]
) -> Dict[str, List[Tuple[str, float]]]:
    """
    识别不同优化器间差异最大的资产
    
    参数:
        weights_by_optimizer: 不同优化器的权重，格式为 {优化器名称: {资产: 权重}}
        
    返回:
        Dict[str, List[Tuple[str, float]]]: 差异资产，格式为 {优化器对: [(资产, 差异)]}
    """
    # 初始化结果
    differential_assets = {}
    
    # 计算每对优化器之间的权重差异
    optimizer_names = list(weights_by_optimizer.keys())
    for i, name1 in enumerate(optimizer_names):
        for name2 in optimizer_names[i+1:]:
            # 获取两个优化器的权重
            weights1 = weights_by_optimizer[name1]
            weights2 = weights_by_optimizer[name2]
            
            # 计算所有资产的权重差异
            all_assets = set(weights1.keys()) | set(weights2.keys())
            weight_diffs = []
            
            for asset in all_assets:
                w1 = weights1.get(asset, 0)
                w2 = weights2.get(asset, 0)
                diff = abs(w1 - w2)
                weight_diffs.append((asset, diff))
            
            # 按差异降序排序
            weight_diffs.sort(key=lambda x: x[1], reverse=True)
            
            # 存储排序后的差异
            pair_key = f"{name1}_vs_{name2}"
            differential_assets[pair_key] = weight_diffs
    
    return differential_assets 