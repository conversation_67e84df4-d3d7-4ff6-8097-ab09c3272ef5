"""
约束分析模块

该模块提供了分析投资组合优化约束影响的工具，包括：
1. 绑定约束识别 - 找出实际影响优化结果的约束
2. 约束影响分析 - 评估约束对优化结果的影响程度
3. 约束敏感性 - 分析约束参数变化对优化结果的影响
"""

from typing import Dict, List, Optional, Any, Tuple, Union
import numpy as np
import pandas as pd
from ..analysis.interfaces import ConstraintAnalysisResult


def analyze_constraints(
    constraint_info: Dict[str, Dict[str, Any]],
    binding_constraints: Optional[List[str]] = None,
    unconstrained_result: Optional[Dict[str, Any]] = None,
    constrained_result: Optional[Dict[str, Any]] = None,
    slack_values: Optional[Dict[str, float]] = None
) -> ConstraintAnalysisResult:
    """
    分析约束条件对优化结果的影响
    
    参数:
        constraint_info: 约束信息字典，格式为 {约束名称: {约束属性字典}}
        binding_constraints: 绑定约束列表，即实际影响优化结果的约束
        unconstrained_result: 无约束情况下的优化结果
        constrained_result: 有约束情况下的优化结果
        slack_values: 约束松弛值字典，格式为 {约束名称: 松弛值}
        
    返回:
        ConstraintAnalysisResult: 约束分析结果对象
    """
    # 如果未提供绑定约束，则提供一个空列表
    if binding_constraints is None:
        binding_constraints = []
    
    # 如果未提供松弛值，则提供一个空字典
    if slack_values is None:
        slack_values = {}
    
    # 计算约束影响（如果提供了无约束和有约束结果）
    constraint_impact = {}
    if unconstrained_result and constrained_result:
        # {{ AURA-X: Add - 完善投资组合优化分析模块. Approval: 寸止(ID:投资组合分析). }}
        # 计算性能指标差异
        unconstrained_return = unconstrained_result.get('expected_return', 0)
        constrained_return = constrained_result.get('expected_return', 0)
        unconstrained_risk = unconstrained_result.get('volatility', 0)
        constrained_risk = constrained_result.get('volatility', 0)

        # 计算夏普比率差异
        unconstrained_sharpe = unconstrained_result.get('sharpe_ratio', 0)
        constrained_sharpe = constrained_result.get('sharpe_ratio', 0)

        # 计算权重差异
        unconstrained_weights = unconstrained_result.get('weights', {})
        constrained_weights = constrained_result.get('weights', {})

        # 计算整体影响度
        return_impact = abs(unconstrained_return - constrained_return)
        risk_impact = abs(unconstrained_risk - constrained_risk)
        sharpe_impact = abs(unconstrained_sharpe - constrained_sharpe)

        # 计算权重变化幅度
        all_assets = set(unconstrained_weights.keys()) | set(constrained_weights.keys())
        weight_changes = []
        for asset in all_assets:
            unc_w = unconstrained_weights.get(asset, 0)
            con_w = constrained_weights.get(asset, 0)
            weight_changes.append(abs(unc_w - con_w))

        total_weight_change = sum(weight_changes)

        # 为每个绑定约束分配影响度
        for constraint in binding_constraints:
            # 基于约束类型和影响程度计算影响度
            if constraint in constraint_info:
                constraint_type = constraint_info[constraint].get('type', 'unknown')

                if constraint_type == 'weight_bounds':
                    # 权重边界约束主要影响权重分布
                    constraint_impact[constraint] = total_weight_change / len(binding_constraints)
                elif constraint_type == 'risk_budget':
                    # 风险预算约束主要影响风险
                    constraint_impact[constraint] = risk_impact / len(binding_constraints)
                elif constraint_type == 'return_target':
                    # 收益目标约束主要影响收益
                    constraint_impact[constraint] = return_impact / len(binding_constraints)
                else:
                    # 其他约束使用综合影响度
                    constraint_impact[constraint] = (return_impact + risk_impact + sharpe_impact) / (3 * len(binding_constraints))
            else:
                # 默认影响度
                constraint_impact[constraint] = 0.1
    
    # 检测约束冲突
    constraint_conflicts = {}
    # {{ AURA-X: Add - 完善投资组合优化分析模块. Approval: 寸止(ID:投资组合分析). }}
    # 检测约束之间的潜在冲突
    constraint_names = list(constraint_info.keys())

    for i, constraint1 in enumerate(constraint_names):
        for j, constraint2 in enumerate(constraint_names[i+1:], i+1):
            conflict_score = _detect_constraint_conflict(
                constraint_info[constraint1],
                constraint_info[constraint2],
                constraint1,
                constraint2
            )

            if conflict_score > 0:
                conflict_key = f"{constraint1}_vs_{constraint2}"
                constraint_conflicts[conflict_key] = {
                    'constraint1': constraint1,
                    'constraint2': constraint2,
                    'conflict_score': conflict_score,
                    'description': _get_conflict_description(
                        constraint_info[constraint1],
                        constraint_info[constraint2]
                    )
                }
    
    # 创建约束分析结果
    return ConstraintAnalysisResult(
        constraint_info=constraint_info,
        binding_constraints=binding_constraints,
        constraint_impact=constraint_impact,
        unconstrained_metrics=unconstrained_result or {},
        constrained_metrics=constrained_result or {},
        slack_values=slack_values,
        constraint_conflicts=constraint_conflicts
    )


def identify_binding_constraints(
    constraints: Dict[str, Dict[str, Any]],
    optimal_solution: np.ndarray,
    tolerance: float = 1e-6
) -> List[str]:
    """
    识别绑定的约束条件
    
    参数:
        constraints: 约束条件字典
        optimal_solution: 优化结果向量
        tolerance: 判断约束是否绑定的容差
        
    返回:
        List[str]: 绑定约束的名称列表
    """
    # 这里为示例框架，实际实现会包含绑定约束识别逻辑
    binding = []
    for name, info in constraints.items():
        # 示例判断逻辑，实际会根据约束类型和具体情况进行判断
        if 'is_binding' in info and info['is_binding']:
            binding.append(name)
    
    return binding


def calculate_constraint_impact(
    unconstrained_weights: Dict[str, float],
    constrained_weights: Dict[str, float],
    constraints: Dict[str, Dict[str, Any]],
    covariance_matrix: Optional[np.ndarray] = None,
    expected_returns: Optional[np.ndarray] = None
) -> Dict[str, float]:
    """
    计算约束对优化结果的影响程度
    
    参数:
        unconstrained_weights: 无约束优化的权重
        constrained_weights: 有约束优化的权重
        constraints: 约束条件字典
        covariance_matrix: 协方差矩阵，用于计算风险影响
        expected_returns: 预期收益率，用于计算收益影响
        
    返回:
        Dict[str, float]: 约束影响字典，格式为 {约束名称: 影响度}
    """
    # 计算整体影响（权重变化的欧氏距离）
    assets = set(unconstrained_weights.keys()) | set(constrained_weights.keys())
    unconstrained_array = np.array([unconstrained_weights.get(a, 0) for a in assets])
    constrained_array = np.array([constrained_weights.get(a, 0) for a in assets])
    
    # 整体差异
    overall_diff = np.linalg.norm(unconstrained_array - constrained_array)
    
    # 这里为示例框架，实际实现会包含约束影响度计算逻辑
    # 分配一个默认值给每个约束
    impact = {name: 0.1 for name in constraints}
    
    return impact


def _detect_constraint_conflict(
    constraint1: Dict[str, Any],
    constraint2: Dict[str, Any],
    name1: str,
    name2: str
) -> float:
    """
    检测两个约束之间的冲突程度

    参数:
        constraint1: 第一个约束的信息
        constraint2: 第二个约束的信息
        name1: 第一个约束的名称
        name2: 第二个约束的名称

    返回:
        float: 冲突分数，0表示无冲突，1表示完全冲突
    """
    # {{ AURA-X: Add - 完善投资组合优化分析模块. Approval: 寸止(ID:投资组合分析). }}
    type1 = constraint1.get('type', 'unknown')
    type2 = constraint2.get('type', 'unknown')

    # 检测权重边界冲突
    if type1 == 'weight_bounds' and type2 == 'weight_bounds':
        bounds1 = constraint1.get('bounds', {})
        bounds2 = constraint2.get('bounds', {})

        # 检查是否有相同资产的冲突边界
        common_assets = set(bounds1.keys()) & set(bounds2.keys())
        conflict_score = 0.0

        for asset in common_assets:
            min1, max1 = bounds1[asset]
            min2, max2 = bounds2[asset]

            # 检查边界是否冲突（一个约束的最小值大于另一个的最大值）
            if min1 > max2 or min2 > max1:
                conflict_score = 1.0  # 完全冲突
                break
            elif min1 > min2 or max1 < max2:
                conflict_score = max(conflict_score, 0.5)  # 部分冲突

        return conflict_score

    # 检测风险预算与收益目标冲突
    elif (type1 == 'risk_budget' and type2 == 'return_target') or \
         (type1 == 'return_target' and type2 == 'risk_budget'):

        # 这里可以添加更复杂的冲突检测逻辑
        # 例如，检查风险预算是否与收益目标在理论上不兼容
        return 0.3  # 中等冲突可能性

    # 检测组合权重约束冲突
    elif type1 == 'group_weights' and type2 == 'group_weights':
        groups1 = constraint1.get('groups', {})
        groups2 = constraint2.get('groups', {})

        # 检查是否有重叠的资产组
        for group1_name, group1_assets in groups1.items():
            for group2_name, group2_assets in groups2.items():
                overlap = set(group1_assets) & set(group2_assets)
                if overlap:
                    # 有重叠资产的组约束可能冲突
                    return 0.4

        return 0.0

    # 其他类型的约束暂时认为无冲突
    return 0.0


def _get_conflict_description(constraint1: Dict[str, Any], constraint2: Dict[str, Any]) -> str:
    """
    生成约束冲突的描述

    参数:
        constraint1: 第一个约束的信息
        constraint2: 第二个约束的信息

    返回:
        str: 冲突描述
    """
    type1 = constraint1.get('type', 'unknown')
    type2 = constraint2.get('type', 'unknown')

    if type1 == 'weight_bounds' and type2 == 'weight_bounds':
        return "权重边界约束之间存在冲突，某些资产的边界设置不兼容"
    elif (type1 == 'risk_budget' and type2 == 'return_target') or \
         (type1 == 'return_target' and type2 == 'risk_budget'):
        return "风险预算约束与收益目标约束可能存在冲突"
    elif type1 == 'group_weights' and type2 == 'group_weights':
        return "资产组权重约束之间存在重叠，可能导致冲突"
    else:
        return f"{type1}约束与{type2}约束之间可能存在冲突"