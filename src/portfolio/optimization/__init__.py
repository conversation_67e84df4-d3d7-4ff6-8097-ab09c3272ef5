"""
组合优化子模块
提供各种投资组合优化算法的实现
"""

from src.portfolio.optimization.interfaces import (
    PortfolioOptimizerInterface,
    PortfolioOptimizationException
)

from src.portfolio.optimization.optimizer_factory import (
    OptimizerFactory,
    create_optimizer
)

# 导入实现的优化器
from src.portfolio.optimization.mean_variance import MeanVarianceOptimizer
from src.portfolio.optimization.risk_parity import RiskParityOptimizer
from src.portfolio.optimization.max_diversification import MaxDiversificationOptimizer
from src.portfolio.optimization.hierarchical_risk_parity import HierarchicalRiskParityOptimizer

# 导入约束模块
from src.portfolio.optimization.constraints import (
    ConstraintInterface,
    BaseConstraint,
    OptimizationConstraintException,
    WeightSumConstraint,
    WeightBoundsConstraint,
    GroupWeightConstraint,
    MaximumVolatilityConstraint,
    TargetVolatilityConstraint,
    FactorExposureConstraint,
    MinimumReturnConstraint,
    ConstraintManager
)

__all__ = [
    # 接口和异常
    "PortfolioOptimizerInterface",
    "PortfolioOptimizationException",
    
    # 工厂类
    "OptimizerFactory",
    "create_optimizer",
    
    # 优化器
    "MeanVarianceOptimizer",
    "RiskParityOptimizer",
    "MaxDiversificationOptimizer",
    "HierarchicalRiskParityOptimizer",
    
    # 约束接口和基类
    "ConstraintInterface",
    "BaseConstraint",
    "OptimizationConstraintException",
    
    # 基本约束
    "WeightSumConstraint",
    "WeightBoundsConstraint",
    "GroupWeightConstraint",
    
    # 高级约束
    "MaximumVolatilityConstraint",
    "TargetVolatilityConstraint",
    "FactorExposureConstraint",
    "MinimumReturnConstraint",
    
    # 约束管理器
    "ConstraintManager"
] 