"""
均值方差优化模块

实现经典的Markowitz均值方差优化算法，用于构建最优投资组合。
"""
from typing import Dict, Any, Optional, Union, List, Tuple
import numpy as np
import pandas as pd
import scipy.optimize as sco
import logging

from src.portfolio.optimization.interfaces import (
    PortfolioOptimizerInterface,
    OptimizationFailedException,
    InvalidInputException
)

logger = logging.getLogger(__name__)


class MeanVarianceOptimizer(PortfolioOptimizerInterface):
    """
    均值方差优化器
    
    实现经典的Markowitz均值方差优化算法，可以构建:
    1. 最小方差投资组合
    2. 最大夏普比率投资组合
    3. 目标收益率下的最小风险投资组合
    4. 目标风险下的最大收益率投资组合
    """
    
    def __init__(
        self,
        name: str = "均值方差优化器",
        description: str = "基于Markowitz理论的均值方差优化算法",
        optimization_target: str = "sharpe",  # 'min_variance', 'sharpe', 'target_return', 'target_risk'
        risk_free_rate: float = 0.0,
        target_return: Optional[float] = None,
        target_risk: Optional[float] = None,
        weight_bounds: Tuple[float, float] = (0.0, 1.0),
        **kwargs
    ):
        """
        初始化均值方差优化器
        
        参数:
            name: 优化器名称
            description: 优化器描述
            optimization_target: 优化目标，可选值:
                - 'min_variance': 最小方差
                - 'sharpe': 最大夏普比率
                - 'target_return': 目标收益率下的最小风险
                - 'target_risk': 目标风险下的最大收益率
            risk_free_rate: 无风险利率，用于计算夏普比率
            target_return: 目标收益率，仅在optimization_target为'target_return'时有效
            target_risk: 目标风险（标准差），仅在optimization_target为'target_risk'时有效
            weight_bounds: 权重约束范围，默认为(0, 1)，代表禁止做空，权重上限为100%
            **kwargs: 其他参数
        """
        super().__init__(name=name, description=description, **kwargs)
        
        self.set_param("optimization_target", optimization_target)
        self.set_param("risk_free_rate", risk_free_rate)
        self.set_param("target_return", target_return)
        self.set_param("target_risk", target_risk)
        self.set_param("weight_bounds", weight_bounds)
        
        self._expected_returns = None
        self._covariance_matrix = None
        self._assets = None
        self._n_assets = 0
        
    def _validate_inputs(
        self, 
        expected_returns: Optional[pd.Series], 
        covariance_matrix: Optional[pd.DataFrame]
    ) -> None:
        """
        验证输入数据的有效性
        
        参数:
            expected_returns: 期望收益率
            covariance_matrix: 协方差矩阵
            
        抛出:
            InvalidInputException: 如果输入数据无效
        """
        # 检查协方差矩阵
        if covariance_matrix is None:
            raise InvalidInputException("必须提供协方差矩阵")
        
        if not isinstance(covariance_matrix, pd.DataFrame):
            raise InvalidInputException("协方差矩阵必须是pandas DataFrame")
        
        if covariance_matrix.shape[0] != covariance_matrix.shape[1]:
            raise InvalidInputException("协方差矩阵必须是方阵")
        
        # 检查收益率（对于非最小方差目标是必需的）
        optimization_target = self.get_param("optimization_target")
        if optimization_target != "min_variance" and expected_returns is None:
            raise InvalidInputException(f"optimization_target为'{optimization_target}'时，必须提供期望收益率")
        
        if expected_returns is not None and not isinstance(expected_returns, pd.Series):
            raise InvalidInputException("期望收益率必须是pandas Series")
        
        # 检查资产匹配
        if expected_returns is not None:
            if not all(asset in covariance_matrix.index for asset in expected_returns.index):
                raise InvalidInputException("期望收益率中的资产必须包含在协方差矩阵中")
            
            if not all(asset in covariance_matrix.columns for asset in expected_returns.index):
                raise InvalidInputException("期望收益率中的资产必须包含在协方差矩阵中")
    
    def _prepare_data(
        self, 
        expected_returns: Optional[pd.Series], 
        covariance_matrix: pd.DataFrame
    ) -> None:
        """
        准备优化数据
        
        参数:
            expected_returns: 期望收益率
            covariance_matrix: 协方差矩阵
        """
        # 如果只提供了协方差矩阵，则使用协方差矩阵的资产列表
        if expected_returns is None:
            self._assets = covariance_matrix.index.tolist()
            # 对于最小方差，可以假设所有资产的期望收益率相同
            self._expected_returns = pd.Series(np.zeros(len(self._assets)), index=self._assets)
        else:
            self._assets = expected_returns.index.tolist()
            self._expected_returns = expected_returns
        
        # 确保协方差矩阵只包含相关资产
        self._covariance_matrix = covariance_matrix.loc[self._assets, self._assets]
        self._n_assets = len(self._assets)
    
    def _min_variance_objective(self, weights: np.ndarray) -> float:
        """
        最小方差优化目标函数
        
        参数:
            weights: 权重数组
            
        返回:
            投资组合方差
        """
        # 返回投资组合方差
        portfolio_variance = np.dot(weights.T, np.dot(self._covariance_matrix.values, weights))
        return portfolio_variance
    
    def _negative_sharpe_ratio(self, weights: np.ndarray) -> float:
        """
        负夏普比率（最大化夏普比率等价于最小化负夏普比率）
        
        参数:
            weights: 权重数组
            
        返回:
            负夏普比率
        """
        portfolio_return = np.sum(self._expected_returns.values * weights)
        portfolio_volatility = np.sqrt(self._min_variance_objective(weights))
        
        # 避免被零除
        if portfolio_volatility == 0:
            return -float("inf")
        
        risk_free_rate = self.get_param("risk_free_rate", 0.0)
        sharpe_ratio = (portfolio_return - risk_free_rate) / portfolio_volatility
        
        # 返回负夏普比率（因为我们要最小化）
        return -sharpe_ratio
    
    def _target_return_objective(self, weights: np.ndarray) -> float:
        """
        目标收益率下最小化风险的目标函数
        
        参数:
            weights: 权重数组
            
        返回:
            投资组合标准差
        """
        return np.sqrt(self._min_variance_objective(weights))
    
    def _target_return_constraint(self, weights: np.ndarray) -> float:
        """
        目标收益率约束
        
        参数:
            weights: 权重数组
            
        返回:
            实际收益率与目标收益率的差值
        """
        target_return = self.get_param("target_return")
        portfolio_return = np.sum(self._expected_returns.values * weights)
        return portfolio_return - target_return
    
    def _target_risk_objective(self, weights: np.ndarray) -> float:
        """
        目标风险下最大化收益率的目标函数
        
        参数:
            weights: 权重数组
            
        返回:
            负投资组合收益率（因为我们要最大化收益率，但scipy只能最小化）
        """
        portfolio_return = np.sum(self._expected_returns.values * weights)
        return -portfolio_return
    
    def _target_risk_constraint(self, weights: np.ndarray) -> float:
        """
        目标风险约束
        
        参数:
            weights: 权重数组
            
        返回:
            目标风险与实际风险的差值
        """
        target_risk = self.get_param("target_risk")
        portfolio_risk = np.sqrt(self._min_variance_objective(weights))
        return target_risk - portfolio_risk
    
    def optimize(
        self,
        expected_returns: pd.Series = None,
        covariance_matrix: pd.DataFrame = None,
        asset_data: pd.DataFrame = None,
        constraints: Dict[str, Any] = None,
        **kwargs
    ) -> pd.Series:
        """
        执行均值方差优化
        
        参数:
            expected_returns: 期望收益率（通常是Series，指数为资产代码）
            covariance_matrix: 协方差矩阵（通常是DataFrame，行列索引为资产代码）
            asset_data: 资产数据（均值方差优化不使用这个参数）
            constraints: 约束条件字典，可以覆盖初始化时的参数，包括:
                - optimization_target: 优化目标
                - risk_free_rate: 无风险利率
                - target_return: 目标收益率
                - target_risk: 目标风险
                - weight_bounds: 权重约束
            **kwargs: 其他参数
            
        返回:
            优化后的资产权重（Series，指数为资产代码）
            
        抛出:
            OptimizationFailedException: 优化失败
            InvalidInputException: 输入数据无效
        """
        # 更新参数（如果在constraints中提供）
        if constraints:
            for key, value in constraints.items():
                self.set_param(key, value)
        
        # 验证输入
        self._validate_inputs(expected_returns, covariance_matrix)
        
        # 准备数据
        self._prepare_data(expected_returns, covariance_matrix)
        
        # 设置初始权重（均等分配）
        initial_weights = np.ones(self._n_assets) / self._n_assets
        
        # 设置权重约束
        weight_bounds = self.get_param("weight_bounds", (0.0, 1.0))
        bounds = tuple(weight_bounds for _ in range(self._n_assets))
        
        # 权重和为1的约束
        weight_sum_constraint = {
            'type': 'eq',
            'fun': lambda weights: np.sum(weights) - 1
        }
        
        try:
            optimization_target = self.get_param("optimization_target")
            
            if optimization_target == "min_variance":
                # 最小方差优化
                result = sco.minimize(
                    self._min_variance_objective,
                    initial_weights,
                    method='SLSQP',
                    bounds=bounds,
                    constraints=[weight_sum_constraint],
                    options={'disp': False}
                )
            
            elif optimization_target == "sharpe":
                # 最大夏普比率优化
                result = sco.minimize(
                    self._negative_sharpe_ratio,
                    initial_weights,
                    method='SLSQP',
                    bounds=bounds,
                    constraints=[weight_sum_constraint],
                    options={'disp': False}
                )
            
            elif optimization_target == "target_return":
                # 检查目标收益率是否设置
                target_return = self.get_param("target_return")
                if target_return is None:
                    raise InvalidInputException("optimization_target为'target_return'时，必须设置target_return参数")
                
                # 目标收益率约束
                target_return_constraint = {
                    'type': 'eq',
                    'fun': self._target_return_constraint
                }
                
                # 目标收益率下的最小风险优化
                result = sco.minimize(
                    self._target_return_objective,
                    initial_weights,
                    method='SLSQP',
                    bounds=bounds,
                    constraints=[weight_sum_constraint, target_return_constraint],
                    options={'disp': False}
                )
            
            elif optimization_target == "target_risk":
                # 检查目标风险是否设置
                target_risk = self.get_param("target_risk")
                if target_risk is None:
                    raise InvalidInputException("optimization_target为'target_risk'时，必须设置target_risk参数")
                
                # 目标风险约束
                target_risk_constraint = {
                    'type': 'eq',
                    'fun': self._target_risk_constraint
                }
                
                # 目标风险下的最大收益率优化
                result = sco.minimize(
                    self._target_risk_objective,
                    initial_weights,
                    method='SLSQP',
                    bounds=bounds,
                    constraints=[weight_sum_constraint, target_risk_constraint],
                    options={'disp': False}
                )
            
            else:
                raise InvalidInputException(f"无效的optimization_target: {optimization_target}")
            
            # 检查优化是否成功
            if not result.success:
                raise OptimizationFailedException(f"优化失败: {result.message}")
            
            # 处理结果
            optimal_weights = result.x
            
            # 四舍五入并处理极小值
            optimal_weights = np.round(optimal_weights, 8)
            optimal_weights[optimal_weights < 1e-8] = 0
            
            # 确保权重和为1
            if optimal_weights.sum() > 0:
                optimal_weights = optimal_weights / optimal_weights.sum()
            else:
                raise OptimizationFailedException("优化结果中所有权重均为0")
            
            # 保存结果
            self._weights = pd.Series(optimal_weights, index=self._assets)
            self._is_optimized = True
            
            # 计算指标
            self.calculate_metrics()
            
            return self._weights
            
        except Exception as e:
            if not isinstance(e, (OptimizationFailedException, InvalidInputException)):
                logger.exception("优化过程中发生错误")
                raise OptimizationFailedException(f"优化失败: {str(e)}")
            raise
    
    def calculate_metrics(self) -> Dict[str, Any]:
        """
        计算优化后的投资组合指标
        
        返回:
            指标字典，包含:
            - portfolio_return: 投资组合预期收益率
            - portfolio_volatility: 投资组合波动率（标准差）
            - portfolio_variance: 投资组合方差
            - sharpe_ratio: 夏普比率
            - diversification_ratio: 多样化比率
            - weights: 权重分布
        """
        if not self._is_optimized or self._weights is None:
            raise ValueError("请先调用optimize()方法")
        
        weights_array = self._weights.values
        
        # 计算投资组合收益率
        portfolio_return = np.sum(self._expected_returns.values * weights_array)
        
        # 计算投资组合风险
        portfolio_variance = self._min_variance_objective(weights_array)
        portfolio_volatility = np.sqrt(portfolio_variance)
        
        # 计算夏普比率
        risk_free_rate = self.get_param("risk_free_rate", 0.0)
        sharpe_ratio = (portfolio_return - risk_free_rate) / portfolio_volatility if portfolio_volatility > 0 else 0
        
        # 计算多样化比率（权重加权的单资产风险之和与投资组合风险的比率）
        asset_volatilities = np.sqrt(np.diag(self._covariance_matrix.values))
        weighted_volatility_sum = np.sum(weights_array * asset_volatilities)
        diversification_ratio = weighted_volatility_sum / portfolio_volatility if portfolio_volatility > 0 else 0
        
        # 计算权重分布统计
        weight_stats = {
            "max_weight": float(weights_array.max()),
            "min_weight": float(weights_array[weights_array > 0].min()) if np.any(weights_array > 0) else 0,
            "non_zero_weights": int(np.sum(weights_array > 0)),
            "concentration": float(np.sum(weights_array ** 2))  # 赫芬达尔指数
        }
        
        metrics = {
            "portfolio_return": float(portfolio_return),
            "portfolio_volatility": float(portfolio_volatility),
            "portfolio_variance": float(portfolio_variance),
            "sharpe_ratio": float(sharpe_ratio),
            "diversification_ratio": float(diversification_ratio),
            "weight_stats": weight_stats
        }
        
        self._metrics = metrics
        return metrics
    
    def efficient_frontier(
        self, 
        return_points: int = 50
    ) -> pd.DataFrame:
        """
        计算有效前沿
        
        参数:
            return_points: 收益率点数
            
        返回:
            有效前沿数据，包含每个收益率点对应的风险和权重
        """
        if self._expected_returns is None or self._covariance_matrix is None:
            raise ValueError("请先调用optimize()方法")
        
        # 寻找最小方差组合
        min_var_optimizer = MeanVarianceOptimizer(
            optimization_target="min_variance",
            weight_bounds=self.get_param("weight_bounds", (0.0, 1.0))
        )
        min_var_weights = min_var_optimizer.optimize(
            covariance_matrix=self._covariance_matrix
        )
        min_var_return = np.sum(min_var_weights.values * self._expected_returns.values)
        
        # 寻找最大收益率组合（简单地选择收益率最高的资产）
        max_return_idx = np.argmax(self._expected_returns.values)
        max_return = self._expected_returns.values[max_return_idx]
        
        # 在最小方差组合和最大收益率之间创建一系列目标收益率
        target_returns = np.linspace(min_var_return, max_return, return_points)
        
        results = []
        for target_return in target_returns:
            try:
                target_return_optimizer = MeanVarianceOptimizer(
                    optimization_target="target_return",
                    target_return=target_return,
                    weight_bounds=self.get_param("weight_bounds", (0.0, 1.0))
                )
                weights = target_return_optimizer.optimize(
                    expected_returns=self._expected_returns,
                    covariance_matrix=self._covariance_matrix
                )
                metrics = target_return_optimizer.metrics
                
                result = {
                    "return": target_return,
                    "volatility": metrics["portfolio_volatility"],
                    "sharpe_ratio": metrics["sharpe_ratio"],
                    "weights": weights
                }
                results.append(result)
            except Exception as e:
                logger.warning(f"计算目标收益率 {target_return} 的有效前沿点时出错: {e}")
        
        # 将结果转换为DataFrame
        frontier_data = pd.DataFrame([
            {
                "return": res["return"],
                "volatility": res["volatility"],
                "sharpe_ratio": res["sharpe_ratio"]
            }
            for res in results
        ])
        
        return frontier_data 