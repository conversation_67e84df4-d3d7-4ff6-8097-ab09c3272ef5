"""
风险平价（Risk Parity）投资组合优化模块

风险平价是一种投资组合构建方法，其目标是在各个资产的风险贡献保持相等的情况下分配权重。
这种方法不依赖于预期收益率的估计，只关注资产之间的风险关系，因此可能比传统的均值方差法更加稳健。

特点:
1. 不依赖收益预测，只需要协方差矩阵
2. 寻求风险贡献平衡，而非简单多元化
3. 通常产生比最小方差组合更分散的投资组合
4. 在多数市场环境下表现稳健
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from scipy.optimize import minimize
import logging

from src.portfolio.optimization.interfaces import (
    PortfolioOptimizerInterface,
    OptimizationFailedException,
    InvalidInputException
)

logger = logging.getLogger(__name__)

class RiskParityOptimizer(PortfolioOptimizerInterface):
    """
    风险平价（Risk Parity）优化器

    实现风险平价投资组合构建方法，确保每个资产对整体投资组合风险的贡献相等。
    
    算法特点:
    - 不依赖预期收益率估计，只需要协方差矩阵
    - 追求风险贡献的平衡，而非权重的平衡
    - 通常比最小方差组合产生更分散的资产配置
    
    参数:
        risk_target: 目标投资组合风险（波动率）
        weight_bounds: 权重限制，格式为 (min_weight, max_weight)
        risk_budget: 风险预算向量，默认为等风险（None）
        tolerance: 优化收敛容差
        max_iterations: 最大迭代次数
    """
    
    def __init__(
        self,
        name: str = "风险平价优化器",
        description: str = "基于风险贡献平衡的投资组合优化算法",
        risk_target: Optional[float] = None,
        weight_bounds: Tuple[float, float] = (0.0, 1.0),
        risk_budget: Optional[np.ndarray] = None,
        tolerance: float = 1e-8,
        max_iterations: int = 1000,
        **kwargs
    ):
        """
        初始化风险平价优化器
        
        参数:
            name: 优化器名称
            description: 优化器描述
            risk_target: 目标投资组合风险（波动率），None表示不强制目标风险
            weight_bounds: 权重限制，(min_weight, max_weight)
            risk_budget: 自定义风险预算，None表示等风险配置
            tolerance: 优化收敛容差
            max_iterations: 最大迭代次数
            **kwargs: 其他参数
        """
        super().__init__(name=name, description=description, **kwargs)
        
        self._params = {
            "risk_target": risk_target,
            "weight_bounds": weight_bounds,
            "risk_budget": risk_budget,
            "tolerance": tolerance,
            "max_iterations": max_iterations
        }
        
        self._covariance_matrix = None
        self._weights = None
        self._is_optimized = False
        self._metrics = {}
    
    def _validate_inputs(self, covariance_matrix: Optional[pd.DataFrame]) -> None:
        """
        验证输入数据的有效性
        
        参数:
            covariance_matrix: 协方差矩阵
            
        异常:
            InvalidInputException: 输入无效时抛出
        """
        if covariance_matrix is None:
            raise InvalidInputException("协方差矩阵不能为空")
            
        if not isinstance(covariance_matrix, pd.DataFrame):
            raise InvalidInputException("协方差矩阵必须是DataFrame类型")
            
        # 检查是否为方阵
        if covariance_matrix.shape[0] != covariance_matrix.shape[1]:
            raise InvalidInputException("协方差矩阵必须是方阵")
            
        # 检查是否为正定矩阵
        try:
            # 尝试计算Cholesky分解，这只对正定矩阵有效
            np.linalg.cholesky(covariance_matrix.values)
        except np.linalg.LinAlgError:
            logger.warning("协方差矩阵不是正定矩阵，可能导致优化问题")
            
        # 检查自定义风险预算（如果有）
        risk_budget = self._params.get("risk_budget")
        if risk_budget is not None:
            if len(risk_budget) != covariance_matrix.shape[0]:
                raise InvalidInputException("风险预算向量长度必须等于资产数量")
            
            if not np.all(risk_budget > 0):
                raise InvalidInputException("风险预算必须都为正值")
                
            if abs(np.sum(risk_budget) - 1.0) > 1e-10:
                raise InvalidInputException("风险预算总和必须为1")
    
    def _prepare_data(self, covariance_matrix: pd.DataFrame) -> None:
        """
        准备优化所需的数据
        
        参数:
            covariance_matrix: 协方差矩阵
        """
        self._covariance_matrix = covariance_matrix.copy()
        
        # 如果没有提供风险预算，则创建等风险预算
        if self._params.get("risk_budget") is None:
            n_assets = self._covariance_matrix.shape[0]
            self._params["risk_budget"] = np.ones(n_assets) / n_assets
        
    def _risk_contribution_objective(self, weights: np.ndarray) -> float:
        """
        风险贡献目标函数
        
        最小化资产风险贡献与目标风险预算的偏差平方和
        
        参数:
            weights: 投资组合权重
            
        返回:
            目标函数值
        """
        # 确保权重为正值并归一化
        weights = np.maximum(weights, 1e-8)
        weights = weights / np.sum(weights)
        
        # 计算投资组合方差和标准差
        portfolio_variance = weights.T @ self._covariance_matrix.values @ weights
        portfolio_volatility = np.sqrt(portfolio_variance)
        
        # 计算每个资产的边际风险贡献
        marginal_risk_contribution = self._covariance_matrix.values @ weights
        
        # 计算每个资产的风险贡献
        risk_contribution = weights * marginal_risk_contribution / portfolio_volatility
        
        # 风险预算
        risk_budget = self._params.get("risk_budget")
        
        # 计算风险贡献与目标风险预算的偏差平方和
        objective = np.sum((risk_contribution - risk_budget * portfolio_volatility) ** 2)
        
        return objective
    
    def _risk_target_constraint(self, weights: np.ndarray) -> float:
        """
        目标风险约束函数
        
        参数:
            weights: 投资组合权重
            
        返回:
            实际风险与目标风险的差值
        """
        weights = np.maximum(weights, 1e-8)
        weights = weights / np.sum(weights)
        
        portfolio_variance = weights.T @ self._covariance_matrix.values @ weights
        portfolio_volatility = np.sqrt(portfolio_variance)
        
        risk_target = self._params.get("risk_target")
        
        # 返回实际风险与目标风险的偏差
        return portfolio_volatility - risk_target
    
    def optimize(
        self,
        expected_returns: pd.Series = None,
        covariance_matrix: pd.DataFrame = None,
        asset_data: pd.DataFrame = None,
        constraints: Dict[str, Any] = None,
        **kwargs
    ) -> pd.Series:
        """
        优化投资组合权重
        
        参数:
            expected_returns: 预期收益率（风险平价算法不需要，但为保持接口一致而保留）
            covariance_matrix: 协方差矩阵
            asset_data: 其他资产数据（可选）
            constraints: 额外的约束条件（可选）
            **kwargs: 其他参数
            
        返回:
            pd.Series: 优化后的投资组合权重
            
        异常:
            InvalidInputException: 输入数据无效
            OptimizationFailedException: 优化失败
        """
        try:
            # 验证输入
            self._validate_inputs(covariance_matrix)
            
            # 准备数据
            self._prepare_data(covariance_matrix)
            
            # 获取参数
            weight_bounds = self._params.get("weight_bounds", (0.0, 1.0))
            tolerance = self._params.get("tolerance", 1e-8)
            max_iterations = self._params.get("max_iterations", 1000)
            risk_target = self._params.get("risk_target", None)
            
            # 初始权重（等权重起始）
            n_assets = self._covariance_matrix.shape[0]
            initial_weights = np.ones(n_assets) / n_assets
            
            # 创建权重约束
            bounds = [weight_bounds] * n_assets
            
            # 创建权重和为1的约束
            weight_sum_constraint = {
                'type': 'eq',
                'fun': lambda x: np.sum(x) - 1.0
            }
            
            # 构建约束列表
            constraints_list = [weight_sum_constraint]
            
            # 添加目标风险约束（如果指定）
            if risk_target is not None:
                risk_constraint = {
                    'type': 'eq',
                    'fun': self._risk_target_constraint
                }
                constraints_list.append(risk_constraint)
            
            # 执行优化
            opt_result = minimize(
                self._risk_contribution_objective,
                initial_weights,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints_list,
                tol=tolerance,
                options={'maxiter': max_iterations}
            )
            
            if not opt_result.success:
                raise OptimizationFailedException(f"优化失败: {opt_result.message}")
            
            # 获取并归一化权重
            optimal_weights = opt_result.x
            optimal_weights = np.maximum(optimal_weights, 1e-8)  # 防止负权重
            optimal_weights = optimal_weights / np.sum(optimal_weights)  # 归一化
            
            # 将结果转换为Series
            weights_series = pd.Series(
                optimal_weights,
                index=self._covariance_matrix.index
            )
            
            # 存储结果
            self._weights = weights_series
            self._is_optimized = True
            
            # 计算指标
            self.calculate_metrics()
            
            return weights_series
            
        except (InvalidInputException, OptimizationFailedException) as e:
            logger.error(f"风险平价优化失败: {str(e)}")
            raise
        
        except Exception as e:
            logger.exception("优化过程中发生错误")
            raise OptimizationFailedException(f"优化失败: {str(e)}")
    
    def calculate_metrics(self) -> Dict[str, Any]:
        """
        计算优化后的投资组合指标
        
        返回:
            指标字典，包含:
            - portfolio_volatility: 投资组合波动率
            - risk_contribution: 各资产风险贡献
            - diversification_ratio: 多样化比率
            - weights: 权重分布
        """
        if not self._is_optimized or self._weights is None:
            raise ValueError("请先调用optimize()方法")
        
        weights_array = self._weights.values
        
        # 计算投资组合风险
        portfolio_variance = weights_array.T @ self._covariance_matrix.values @ weights_array
        portfolio_volatility = np.sqrt(portfolio_variance)
        
        # 计算边际风险贡献
        marginal_risk_contribution = self._covariance_matrix.values @ weights_array
        
        # 计算风险贡献
        risk_contribution = weights_array * marginal_risk_contribution / portfolio_volatility
        
        # 将风险贡献转换为Series
        risk_contrib_series = pd.Series(
            risk_contribution,
            index=self._covariance_matrix.index
        )
        
        # 计算多样化比率（权重加权的单资产风险之和与投资组合风险的比率）
        asset_volatilities = np.sqrt(np.diag(self._covariance_matrix.values))
        weighted_volatility_sum = np.sum(weights_array * asset_volatilities)
        diversification_ratio = weighted_volatility_sum / portfolio_volatility if portfolio_volatility > 0 else 0
        
        # 计算权重分布统计
        weight_stats = {
            "max_weight": float(weights_array.max()),
            "min_weight": float(weights_array[weights_array > 0].min()) if np.any(weights_array > 0) else 0,
            "non_zero_weights": int(np.sum(weights_array > 0)),
            "concentration": float(np.sum(weights_array ** 2))  # 赫芬达尔指数
        }
        
        # 汇总指标
        metrics = {
            "portfolio_volatility": float(portfolio_volatility),
            "risk_contribution": risk_contrib_series,
            "risk_contribution_percent": risk_contrib_series / portfolio_volatility,
            "diversification_ratio": float(diversification_ratio),
            "weight_stats": weight_stats
        }
        
        self._metrics = metrics
        return metrics 