"""
组合优化接口模块

定义组合优化所需的接口和异常类。
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union, Any, Tuple
import numpy as np
import pandas as pd


class PortfolioOptimizationException(Exception):
    """组合优化异常基类"""
    pass


class OptimizationFailedException(PortfolioOptimizationException):
    """优化过程失败异常"""
    pass


class InvalidInputException(PortfolioOptimizationException):
    """输入数据无效异常"""
    pass


class PortfolioOptimizerInterface(ABC):
    """
    投资组合优化器接口类
    
    所有组合优化算法必须实现此接口，提供统一的优化方法。
    """
    
    def __init__(
        self, 
        name: str = "",
        description: str = "",
        **kwargs
    ):
        """
        初始化优化器
        
        参数:
            name: 优化器名称
            description: 优化器描述
            **kwargs: 其他参数
        """
        self._name = name or self.__class__.__name__
        self._description = description
        self._params = kwargs
        self._is_optimized = False
        self._weights = None
        self._metrics = {}
    
    @property
    def name(self) -> str:
        """获取优化器名称"""
        return self._name
    
    @property
    def description(self) -> str:
        """获取优化器描述"""
        return self._description
    
    @property
    def params(self) -> Dict[str, Any]:
        """获取优化器参数"""
        return self._params.copy()
    
    @property
    def is_optimized(self) -> bool:
        """是否已完成优化"""
        return self._is_optimized
    
    @property
    def weights(self) -> Optional[pd.Series]:
        """获取优化后的权重"""
        return self._weights
    
    @property
    def metrics(self) -> Dict[str, Any]:
        """获取优化指标"""
        return self._metrics.copy()
    
    def set_param(self, param_name: str, param_value: Any) -> None:
        """
        设置优化器参数
        
        参数:
            param_name: 参数名
            param_value: 参数值
        """
        self._params[param_name] = param_value
        
    def get_param(self, param_name: str, default: Any = None) -> Any:
        """
        获取优化器参数
        
        参数:
            param_name: 参数名
            default: 默认值（如果参数不存在）
            
        返回:
            参数值或默认值
        """
        return self._params.get(param_name, default)
    
    @abstractmethod
    def optimize(
        self,
        expected_returns: pd.Series = None,
        covariance_matrix: pd.DataFrame = None,
        asset_data: pd.DataFrame = None,
        constraints: Dict[str, Any] = None,
        **kwargs
    ) -> pd.Series:
        """
        执行投资组合优化
        
        参数:
            expected_returns: 期望收益率（通常是Series，指数为资产代码）
            covariance_matrix: 协方差矩阵（通常是DataFrame，行列索引为资产代码）
            asset_data: 资产数据（通常是DataFrame，用于计算其他指标）
            constraints: 约束条件字典
            **kwargs: 其他参数
            
        返回:
            优化后的资产权重（Series，指数为资产代码）
            
        抛出:
            OptimizationFailedException: 优化失败
            InvalidInputException: 输入数据无效
        """
        pass
    
    @abstractmethod
    def calculate_metrics(self) -> Dict[str, Any]:
        """
        计算优化相关指标
        
        返回:
            指标字典
        """
        pass
    
    def __str__(self) -> str:
        """返回优化器的字符串表示"""
        return f"{self._name}: {self._description}"
    
    def __repr__(self) -> str:
        """返回优化器的详细表示"""
        return f"{self.__class__.__name__}(name='{self._name}', description='{self._description}', params={self._params})" 