#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
投资组合分析工具模块

该模块提供了投资组合优化结果的分析和可视化工具
"""

from .portfolio_analyzer import PortfolioAnalyzer
from .visualization import (
    plot_efficient_frontier,
    plot_portfolio_weights,
    plot_risk_contribution,
    plot_performance_metrics,
    plot_correlation_matrix,
    plot_portfolio_comparison,
    plot_risk_return_scatter
)

__all__ = [
    'PortfolioAnalyzer',
    'plot_efficient_frontier',
    'plot_portfolio_weights',
    'plot_risk_contribution',
    'plot_performance_metrics',
    'plot_correlation_matrix',
    'plot_portfolio_comparison',
    'plot_risk_return_scatter'
] 