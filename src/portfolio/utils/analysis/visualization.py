#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
投资组合优化可视化工具模块

提供用于可视化投资组合优化结果的函数，包括权重分布、风险贡献、有效前沿、
绩效指标比较等可视化功能。
"""

from typing import Dict, List, Optional, Union, Any, Tuple
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import os

# 设置中文字体支持
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像是负号'-'显示为方块的问题
except:
    print("警告: 未能设置中文字体，图表中文可能无法正确显示")


def plot_efficient_frontier(
    efficient_frontier: pd.DataFrame,
    portfolios: Dict[str, Dict[str, float]] = None,
    risk_free_rate: float = 0.0,
    title: str = "投资组合有效前沿",
    x_label: str = "波动率",
    y_label: str = "预期收益率",
    figsize: Tuple[int, int] = (10, 6),
    save_path: str = None,
    show_plot: bool = True
) -> plt.Figure:
    """
    绘制投资组合有效前沿
    
    参数:
        efficient_frontier: 有效前沿数据，必须包含'volatility'和'return'列
        portfolios: 要在图上标记的投资组合，格式为 {名称: {'return': 值, 'volatility': 值}}
        risk_free_rate: 无风险利率，用于绘制资本市场线
        title: 图表标题
        x_label: x轴标签
        y_label: y轴标签
        figsize: 图表大小
        save_path: 保存路径，None表示不保存
        show_plot: 是否显示图表
        
    返回:
        matplotlib图表对象
    """
    fig, ax = plt.subplots(figsize=figsize)
    
    # 确定x轴和y轴的列名
    x_column = 'volatility'
    y_column = 'return'
    
    # 检查列名是否存在，如果不存在则尝试替代名称
    if x_column not in efficient_frontier.columns and 'portfolio_volatility' in efficient_frontier.columns:
        x_column = 'portfolio_volatility'
    if y_column not in efficient_frontier.columns and 'portfolio_return' in efficient_frontier.columns:
        y_column = 'portfolio_return'
    if y_column not in efficient_frontier.columns and 'expected_return' in efficient_frontier.columns:
        y_column = 'expected_return'
        
    # 绘制有效前沿
    ax.plot(efficient_frontier[x_column], efficient_frontier[y_column], 'b--', linewidth=2, label='有效前沿')
    
    # 找出最大夏普比率点（如果有夏普比率列）
    if 'sharpe_ratio' in efficient_frontier.columns:
        max_sharpe_idx = efficient_frontier['sharpe_ratio'].idxmax()
        max_sharpe_point = efficient_frontier.loc[max_sharpe_idx]
        ax.scatter(max_sharpe_point[x_column], max_sharpe_point[y_column], 
                  s=100, c='g', marker='*', label='最大夏普比率组合')
    
    # 找出最小方差点
    min_vol_idx = efficient_frontier[x_column].idxmin()
    min_vol_point = efficient_frontier.loc[min_vol_idx]
    ax.scatter(min_vol_point[x_column], min_vol_point[y_column], 
              s=100, c='r', marker='o', label='最小方差组合')
    
    # 绘制资本市场线（如果提供了无风险利率）
    if risk_free_rate is not None and 'sharpe_ratio' in efficient_frontier.columns:
        max_sharpe_point = efficient_frontier.loc[efficient_frontier['sharpe_ratio'].idxmax()]
        max_return = efficient_frontier[y_column].max()
        x_max = max_return / max_sharpe_point['sharpe_ratio'] if max_sharpe_point['sharpe_ratio'] > 0 else 0
        x_vals = np.linspace(0, x_max * 1.2, 100)
        y_vals = risk_free_rate + max_sharpe_point['sharpe_ratio'] * x_vals
        ax.plot(x_vals, y_vals, 'g-', label='资本市场线')
    
    # 绘制特定投资组合
    if portfolios:
        markers = ['o', 's', '^', 'D', 'v', '<', '>', 'p', 'h']
        colors = ['red', 'green', 'blue', 'purple', 'orange', 'brown', 'black', 'gray', 'cyan']
        
        for i, (name, data) in enumerate(portfolios.items()):
            marker = markers[i % len(markers)]
            color = colors[i % len(colors)]
            
            # 获取投资组合的波动率和收益率
            volatility = data.get('volatility') or data.get('portfolio_volatility', 0)
            returns = data.get('return') or data.get('portfolio_return') or data.get('expected_return', 0)
            
            ax.scatter(volatility, returns, marker=marker, color=color, s=100, label=name)
    
    # 设置标题和标签
    ax.set_title(title, fontsize=15)
    ax.set_xlabel(x_label, fontsize=12)
    ax.set_ylabel(y_label, fontsize=12)
    ax.grid(True, alpha=0.3)
    ax.legend(loc='best')
    
    plt.tight_layout()
    
    # 保存图表
    if save_path:
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(save_path)), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    # 显示图表
    if show_plot:
        plt.show()
    
    return fig


def plot_portfolio_weights(
    weights: Union[pd.Series, Dict[str, pd.Series]],
    title: str = "投资组合权重分布",
    figsize: Tuple[int, int] = (12, 6),
    kind: str = "bar",  # 'bar', 'pie', 'treemap'
    save_path: str = None,
    show_plot: bool = True,
    **kwargs
) -> plt.Figure:
    """
    绘制投资组合权重分布
    
    参数:
        weights: 单个投资组合的权重Series或多个投资组合的权重字典
        title: 图表标题
        figsize: 图表大小
        kind: 图表类型，'bar'为条形图，'pie'为饼图，'treemap'为树图
        save_path: 保存路径，None表示不保存
        show_plot: 是否显示图表
        **kwargs: 其他参数传递给绘图函数
        
    返回:
        matplotlib图表对象
    """
    fig = None
    
    # 处理单个投资组合
    if isinstance(weights, pd.Series):
        weights = {"Portfolio": weights}
    
    # 根据不同类型绘制图表
    if kind.lower() == 'bar':
        fig = _plot_weights_bar(weights, title, figsize, **kwargs)
    elif kind.lower() == 'pie':
        fig = _plot_weights_pie(weights, title, figsize, **kwargs)
    else:  # 默认为条形图
        fig = _plot_weights_bar(weights, title, figsize, **kwargs)
    
    # 保存图表
    if save_path:
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(save_path)), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    # 显示图表
    if show_plot:
        plt.show()
    
    return fig


def _plot_weights_bar(
    weights: Dict[str, pd.Series],
    title: str,
    figsize: Tuple[int, int],
    **kwargs
) -> plt.Figure:
    """
    使用条形图绘制权重分布
    
    参数:
        weights: 投资组合权重字典
        title: 图表标题
        figsize: 图表大小
        **kwargs: 其他参数
        
    返回:
        matplotlib图表对象
    """
    n_portfolios = len(weights)
    
    if n_portfolios == 1:
        # 单个投资组合
        portfolio_name = list(weights.keys())[0]
        portfolio_weights = weights[portfolio_name]
        
        # 只保留非零权重
        portfolio_weights = portfolio_weights[portfolio_weights > 0]
        
        # 排序
        portfolio_weights = portfolio_weights.sort_values(ascending=False)
        
        fig, ax = plt.subplots(figsize=figsize)
        bars = ax.bar(portfolio_weights.index, portfolio_weights.values, 
                     color=kwargs.get('color', 'skyblue'))
        
        # 在条形上方显示数值
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{height:.2%}',
                   ha='center', va='bottom')
        
        ax.set_title(f"{title} - {portfolio_name}", fontsize=15)
        ax.set_ylabel('权重', fontsize=12)
        ax.set_ylim(0, portfolio_weights.max() * 1.1)  # 留出一些空间显示标签
        plt.xticks(rotation=45, ha='right')
        ax.grid(True, alpha=0.3, axis='y')
        
    else:
        # 多个投资组合
        fig, ax = plt.subplots(figsize=figsize)
        
        # 获取所有资产
        all_assets = set()
        for w in weights.values():
            all_assets.update(w.index)
        
        # 创建DataFrame进行比较
        df = pd.DataFrame(index=all_assets, columns=weights.keys())
        for name, w in weights.items():
            df[name] = w
        
        # 填充NaN
        df = df.fillna(0)
        
        # 按第一个投资组合的权重排序
        first_portfolio = list(weights.keys())[0]
        df = df.sort_values(by=first_portfolio, ascending=False)
        
        # 绘制柱状图
        df.plot(kind='bar', ax=ax)
        
        ax.set_title(title, fontsize=15)
        ax.set_ylabel('权重', fontsize=12)
        ax.set_ylim(0, df.max().max() * 1.1)  # 留出一些空间显示标签
        plt.xticks(rotation=45, ha='right')
        ax.grid(True, alpha=0.3, axis='y')
        ax.legend(title='投资组合')
    
    plt.tight_layout()
    return fig


def _plot_weights_pie(
    weights: Dict[str, pd.Series],
    title: str,
    figsize: Tuple[int, int],
    **kwargs
) -> plt.Figure:
    """
    使用饼图绘制权重分布
    
    参数:
        weights: 投资组合权重字典
        title: 图表标题
        figsize: 图表大小
        **kwargs: 其他参数
        
    返回:
        matplotlib图表对象
    """
    n_portfolios = len(weights)
    
    if n_portfolios == 1:
        # 单个投资组合
        portfolio_name = list(weights.keys())[0]
        portfolio_weights = weights[portfolio_name]
        
        # 只保留非零权重
        portfolio_weights = portfolio_weights[portfolio_weights > 0]
        
        # 设置小权重的处理方式
        min_weight = kwargs.get('min_weight', 0.01)
        if min_weight > 0:
            # 将小于阈值的权重合并为"其他"
            other_weights = portfolio_weights[portfolio_weights < min_weight].sum()
            main_weights = portfolio_weights[portfolio_weights >= min_weight]
            
            if other_weights > 0:
                pie_weights = pd.concat([main_weights, pd.Series([other_weights], index=['其他'])])
            else:
                pie_weights = main_weights
        else:
            pie_weights = portfolio_weights
        
        # 排序
        pie_weights = pie_weights.sort_values(ascending=False)
        
        fig, ax = plt.subplots(figsize=figsize)
        
        # 绘制饼图
        wedges, texts, autotexts = ax.pie(
            pie_weights,
            labels=pie_weights.index,
            autopct='%1.1f%%',
            startangle=90,
            **kwargs
        )
        
        # 自定义文本样式
        plt.setp(autotexts, size=10, weight="bold")
        
        ax.set_title(f"{title} - {portfolio_name}", fontsize=15)
        ax.axis('equal')  # 确保饼图是圆形
        
    else:
        # 多个投资组合，创建子图
        n_cols = min(3, n_portfolios)
        n_rows = (n_portfolios + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(figsize[0] * n_cols / 2, figsize[1] * n_rows / 2))
        
        if n_rows == 1 and n_cols == 1:
            axes = np.array([axes])
        if n_rows == 1 or n_cols == 1:
            axes = axes.flatten()
        
        for i, (name, w) in enumerate(weights.items()):
            if i < len(axes):
                ax = axes[i]
                
                # 只保留非零权重
                w = w[w > 0]
                
                # 设置小权重的处理方式
                min_weight = kwargs.get('min_weight', 0.01)
                if min_weight > 0:
                    # 将小于阈值的权重合并为"其他"
                    other_weights = w[w < min_weight].sum()
                    main_weights = w[w >= min_weight]
                    
                    if other_weights > 0:
                        pie_weights = pd.concat([main_weights, pd.Series([other_weights], index=['其他'])])
                    else:
                        pie_weights = main_weights
                else:
                    pie_weights = w
                
                # 排序
                pie_weights = pie_weights.sort_values(ascending=False)
                
                # 绘制饼图
                wedges, texts, autotexts = ax.pie(
                    pie_weights,
                    labels=pie_weights.index,
                    autopct='%1.1f%%',
                    startangle=90,
                    **kwargs
                )
                
                # 自定义文本样式
                plt.setp(autotexts, size=8, weight="bold")
                
                ax.set_title(name, fontsize=12)
                ax.axis('equal')  # 确保饼图是圆形
        
        # 隐藏空白子图
        for i in range(len(weights), len(axes)):
            axes[i].axis('off')
        
        fig.suptitle(title, fontsize=15)
    
    plt.tight_layout()
    return fig 


def plot_risk_contribution(
    risk_contributions: Union[pd.Series, Dict[str, pd.Series]],
    title: str = "投资组合风险贡献分布",
    figsize: Tuple[int, int] = (12, 6),
    kind: str = "bar",  # 'bar', 'pie'
    save_path: str = None,
    show_plot: bool = True,
    **kwargs
) -> plt.Figure:
    """
    绘制投资组合风险贡献分布
    
    参数:
        risk_contributions: 单个投资组合的风险贡献Series或多个投资组合的风险贡献字典
        title: 图表标题
        figsize: 图表大小
        kind: 图表类型，'bar'为条形图，'pie'为饼图
        save_path: 保存路径，None表示不保存
        show_plot: 是否显示图表
        **kwargs: 其他参数传递给绘图函数
        
    返回:
        matplotlib图表对象
    """
    # 将风险贡献转为百分比格式
    def to_percentage(risk_contrib):
        if isinstance(risk_contrib, pd.Series):
            return risk_contrib / risk_contrib.sum()
        return risk_contrib
    
    # 处理单个投资组合
    if isinstance(risk_contributions, pd.Series):
        risk_contributions = {"Portfolio": to_percentage(risk_contributions)}
    else:
        # 处理多个投资组合
        risk_contributions = {k: to_percentage(v) for k, v in risk_contributions.items()}
    
    # 重用权重绘图函数
    if kind.lower() == 'bar':
        return _plot_weights_bar(risk_contributions, title, figsize, **kwargs)
    elif kind.lower() == 'pie':
        return _plot_weights_pie(risk_contributions, title, figsize, **kwargs)
    else:
        return _plot_weights_bar(risk_contributions, title, figsize, **kwargs)


def plot_performance_metrics(
    metrics: Union[pd.DataFrame, Dict[str, Dict[str, float]]],
    title: str = "投资组合绩效指标比较",
    figsize: Tuple[int, int] = (14, 8),
    save_path: str = None,
    show_plot: bool = True,
    **kwargs
) -> plt.Figure:
    """
    绘制投资组合绩效指标比较图
    
    参数:
        metrics: 投资组合绩效指标DataFrame或字典
        title: 图表标题
        figsize: 图表大小
        save_path: 保存路径，None表示不保存
        show_plot: 是否显示图表
        **kwargs: 其他参数
        
    返回:
        matplotlib图表对象
    """
    # 转换字典为DataFrame
    if isinstance(metrics, dict):
        metrics_df = pd.DataFrame(metrics).T
    else:
        metrics_df = metrics
    
    # 绘制条形图
    fig, ax = plt.subplots(figsize=figsize)
    
    # 转置DataFrame，使指标成为列，投资组合成为行
    if kwargs.get('transpose', False):
        metrics_df = metrics_df.T
    
    # 绘制条形图
    metrics_df.plot(kind='bar', ax=ax)
    
    # 设置标题和标签
    ax.set_title(title, fontsize=15)
    ax.set_xlabel('投资组合', fontsize=12) if not kwargs.get('transpose', False) else ax.set_xlabel('指标', fontsize=12)
    ax.set_ylabel('值', fontsize=12)
    ax.grid(True, alpha=0.3, axis='y')
    
    # 在条形上方显示数值
    for container in ax.containers:
        ax.bar_label(container, fmt='%.3f', padding=3)
    
    # 设置图例
    ax.legend(title=kwargs.get('legend_title', '指标') if not kwargs.get('transpose', False) else '投资组合')
    
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    
    # 保存图表
    if save_path:
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(save_path)), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    # 显示图表
    if show_plot:
        plt.show()
    
    return fig


def plot_correlation_matrix(
    correlation_matrix: pd.DataFrame,
    title: str = "资产相关性矩阵",
    figsize: Tuple[int, int] = (10, 8),
    cmap: str = "coolwarm",
    save_path: str = None,
    show_plot: bool = True,
    **kwargs
) -> plt.Figure:
    """
    绘制资产相关性矩阵热图
    
    参数:
        correlation_matrix: 相关性矩阵DataFrame
        title: 图表标题
        figsize: 图表大小
        cmap: 颜色映射
        save_path: 保存路径，None表示不保存
        show_plot: 是否显示图表
        **kwargs: 其他参数
        
    返回:
        matplotlib图表对象
    """
    fig, ax = plt.subplots(figsize=figsize)
    
    # 使用Seaborn绘制热图
    sns.heatmap(
        correlation_matrix,
        annot=kwargs.get('annot', True),
        cmap=cmap,
        vmin=-1,
        vmax=1,
        center=0,
        linewidths=.5,
        fmt=".2f",
        ax=ax
    )
    
    # 设置标题
    ax.set_title(title, fontsize=15)
    
    plt.tight_layout()
    
    # 保存图表
    if save_path:
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(save_path)), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    # 显示图表
    if show_plot:
        plt.show()
    
    return fig


def plot_portfolio_comparison(
    portfolios: Dict[str, Dict[str, float]],
    metrics: List[str] = None,
    title: str = "投资组合比较",
    figsize: Tuple[int, int] = (14, 8),
    normalize: bool = True,
    show_table: bool = True,
    save_path: str = None,
    show_plot: bool = True,
    **kwargs
) -> plt.Figure:
    """
    绘制多个投资组合的雷达图比较
    
    参数:
        portfolios: 投资组合指标字典，格式为 {投资组合名称: {指标名称: 值}}
        metrics: 要比较的指标列表，None表示使用所有指标
        title: 图表标题
        figsize: 图表大小
        normalize: 是否对指标进行归一化处理
        show_table: 是否显示指标表格
        save_path: 保存路径，None表示不保存
        show_plot: 是否显示图表
        **kwargs: 其他参数
        
    返回:
        matplotlib图表对象
    """
    # 提取所有指标
    all_metrics = set()
    for p_metrics in portfolios.values():
        all_metrics.update(p_metrics.keys())
    
    # 如果未指定指标，则使用所有指标
    if metrics is None:
        metrics = list(all_metrics)
    else:
        # 确保指定的所有指标都存在
        for metric in metrics:
            if metric not in all_metrics:
                print(f"警告: 指标 '{metric}' 不存在于任何投资组合中")
    
    # 创建指标DataFrame
    metrics_data = {}
    for p_name, p_metrics in portfolios.items():
        metrics_data[p_name] = [p_metrics.get(metric, np.nan) for metric in metrics]
    
    df = pd.DataFrame(metrics_data, index=metrics)
    
    # 数据归一化处理
    if normalize:
        # 对每个指标进行Min-Max归一化
        for metric in df.index:
            values = df.loc[metric]
            if not values.isna().all():  # 确保不是所有值都是NaN
                min_val = values.min()
                max_val = values.max()
                if max_val > min_val:  # 避免除以零
                    # 对于下降型指标（如波动率），反转方向
                    if metric.lower() in ['volatility', 'var', 'cvar', 'weight_concentration', 'manhattan_distance', 'euclidean_distance']:
                        df.loc[metric] = 1 - (values - min_val) / (max_val - min_val)
                    else:
                        df.loc[metric] = (values - min_val) / (max_val - min_val)
    
    # 设置图表
    fig = plt.figure(figsize=figsize)
    
    # 创建雷达图和表格的布局
    if show_table:
        gs = fig.add_gridspec(1, 2, width_ratios=[3, 1])
        ax_radar = fig.add_subplot(gs[0], polar=True)
        ax_table = fig.add_subplot(gs[1])
        ax_table.axis('off')  # 隐藏表格轴
    else:
        ax_radar = fig.add_subplot(111, polar=True)
    
    # 计算雷达图的角度
    angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
    angles += angles[:1]  # 闭合雷达图
    
    # 颜色和标记
    colors = plt.cm.tab10.colors
    markers = ['o', 's', '^', 'D', 'v', '<', '>', 'p', 'h']
    
    # 绘制雷达图
    for i, (p_name, values) in enumerate(df.T.iterrows()):
        values_list = values.tolist()
        values_list += values_list[:1]  # 闭合雷达图
        
        color = colors[i % len(colors)]
        marker = markers[i % len(markers)]
        
        ax_radar.plot(angles, values_list, color=color, marker=marker, label=p_name, linewidth=2)
        ax_radar.fill(angles, values_list, color=color, alpha=0.1)
    
    # 设置雷达图标签
    ax_radar.set_xticks(angles[:-1])
    ax_radar.set_xticklabels(metrics, fontsize=10)
    
    # 设置雷达图范围
    ax_radar.set_ylim(0, 1.1)
    ax_radar.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax_radar.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'] if normalize else ["20%", "40%", "60%", "80%", "100%"])
    
    # 设置网格
    ax_radar.grid(True, linestyle='-', alpha=0.7)
    
    # 添加图例
    ax_radar.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    
    # 添加标题
    plt.suptitle(title, fontsize=15)
    
    # 添加指标表格
    if show_table:
        # 创建原始值表格
        orig_df = pd.DataFrame(metrics_data, index=metrics)
        
        # 格式化表格数据
        cell_text = []
        for metric in metrics:
            row = [f"{orig_df.loc[metric, col]:.4f}" if not pd.isna(orig_df.loc[metric, col]) else "N/A" 
                   for col in orig_df.columns]
            cell_text.append(row)
        
        # 创建表格
        table = ax_table.table(
            cellText=cell_text,
            rowLabels=metrics,
            colLabels=orig_df.columns,
            cellLoc='center',
            loc='center'
        )
        
        # 调整表格样式
        table.auto_set_font_size(False)
        table.set_fontsize(9)
        table.scale(1, 1.5)
    
    plt.tight_layout()
    
    # 保存图表
    if save_path:
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(save_path)), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    # 显示图表
    if show_plot:
        plt.show()
    
    return fig


def plot_risk_return_scatter(
    portfolios: Dict[str, Dict[str, float]],
    title: str = "风险收益散点图",
    x_label: str = "波动率",
    y_label: str = "预期收益率",
    add_sharpe_lines: bool = True,
    risk_free_rate: float = 0.0,
    figsize: Tuple[int, int] = (10, 6),
    save_path: str = None,
    show_plot: bool = True,
    **kwargs
) -> plt.Figure:
    """
    绘制投资组合风险收益散点图
    
    参数:
        portfolios: 投资组合指标字典，格式为 {投资组合名称: {'volatility': 值, 'expected_return': 值}}
        title: 图表标题
        x_label: x轴标签
        y_label: y轴标签
        add_sharpe_lines: 是否添加等夏普比率线
        risk_free_rate: 无风险利率
        figsize: 图表大小
        save_path: 保存路径，None表示不保存
        show_plot: 是否显示图表
        **kwargs: 其他参数
        
    返回:
        matplotlib图表对象
    """
    fig, ax = plt.subplots(figsize=figsize)
    
    # 提取风险和收益数据
    risk_data = []
    return_data = []
    names = []
    
    for name, metrics in portfolios.items():
        # 提取波动率（风险）数据
        volatility = metrics.get('volatility') or metrics.get('portfolio_volatility') or metrics.get('risk', 0)
        
        # 提取收益率数据
        expected_return = metrics.get('expected_return') or metrics.get('portfolio_return') or metrics.get('return', 0)
        
        risk_data.append(volatility)
        return_data.append(expected_return)
        names.append(name)
    
    # 设置散点图
    markers = ['o', 's', '^', 'D', 'v', '<', '>', 'p', 'h']
    colors = plt.cm.tab10.colors
    
    for i, (name, risk, ret) in enumerate(zip(names, risk_data, return_data)):
        marker = markers[i % len(markers)]
        color = colors[i % len(colors)]
        
        ax.scatter(risk, ret, s=100, marker=marker, color=color, label=name)
        
        # 添加文本标签
        if kwargs.get('add_labels', True):
            ax.annotate(
                name,
                xy=(risk, ret),
                xytext=(5, 5),
                textcoords='offset points',
                fontsize=9
            )
    
    # 添加等夏普比率线
    if add_sharpe_lines:
        # 计算一系列夏普比率
        sharpe_values = [0.5, 1.0, 1.5, 2.0, 2.5, 3.0]
        
        # 找出数据范围
        max_risk = max(risk_data) * 1.2
        
        for sharpe in sharpe_values:
            # 计算线段的起点和终点
            x_vals = np.linspace(0, max_risk, 100)
            y_vals = risk_free_rate + sharpe * x_vals
            
            # 绘制等夏普比率线
            ax.plot(x_vals, y_vals, 'k--', alpha=0.3)
            
            # 添加标签（在一定范围内）
            x_label_pos = max_risk * 0.7
            y_label_pos = risk_free_rate + sharpe * x_label_pos
            
            ax.text(
                x_label_pos, y_label_pos,
                f"SR={sharpe:.1f}",
                fontsize=8,
                alpha=0.7,
                ha='center',
                va='center',
                bbox=dict(facecolor='white', alpha=0.7, edgecolor='none')
            )
    
    # 设置标题和标签
    ax.set_title(title, fontsize=15)
    ax.set_xlabel(x_label, fontsize=12)
    ax.set_ylabel(y_label, fontsize=12)
    
    # 设置图例
    if kwargs.get('show_legend', True):
        ax.legend()
    
    # 设置网格
    ax.grid(True, alpha=0.3)
    
    # 设置坐标轴范围
    if len(risk_data) > 0:
        ax.set_xlim(0, max(risk_data) * 1.2)
        ax.set_ylim(min(min(return_data) * 0.8, 0), max(return_data) * 1.2)
    
    plt.tight_layout()
    
    # 保存图表
    if save_path:
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(save_path)), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    # 显示图表
    if show_plot:
        plt.show()
    
    return fig 