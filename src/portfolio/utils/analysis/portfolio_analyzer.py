#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
投资组合分析器模块

提供用于分析投资组合优化结果的工具类和函数，
包括性能评估、风险分析、分散化分析、权重分析等功能。
"""

from typing import Dict, List, Optional, Union, Any, Tuple, Callable
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from collections import OrderedDict
import logging

# 配置日志
logger = logging.getLogger(__name__)


class PortfolioAnalyzer:
    """
    投资组合分析器类
    
    用于分析和比较不同投资组合优化结果的综合工具。
    """
    
    def __init__(
        self,
        returns: Optional[pd.Series] = None,
        covariance_matrix: Optional[pd.DataFrame] = None,
        asset_data: Optional[pd.DataFrame] = None,
        risk_free_rate: float = 0.0,
        time_period: int = 252  # 默认为年化（252个交易日）
    ):
        """
        初始化投资组合分析器
        
        参数:
            returns: 资产预期收益率，Index为资产名称
            covariance_matrix: 资产协方差矩阵，Index和columns为资产名称
            asset_data: 资产历史价格数据，Index为日期，columns为资产名称
            risk_free_rate: 无风险利率，用于计算夏普比率等指标
            time_period: 时间周期，用于年化计算，252为交易日数，12为月数，4为季度数
        """
        self.returns = returns
        self.covariance_matrix = covariance_matrix
        self.asset_data = asset_data
        self.risk_free_rate = risk_free_rate
        self.time_period = time_period
        
        self.asset_names = None
        if returns is not None:
            self.asset_names = returns.index.tolist()
        elif covariance_matrix is not None:
            self.asset_names = covariance_matrix.index.tolist()
        elif asset_data is not None:
            self.asset_data = asset_data
            self.asset_names = asset_data.columns.tolist()
            
            # 计算收益率和协方差矩阵
            if returns is None:
                self.returns = self._calculate_returns()
            if covariance_matrix is None:
                self.covariance_matrix = self._calculate_covariance()
    
    def _calculate_returns(self) -> pd.Series:
        """
        根据历史数据计算预期收益率
        
        返回:
            预期收益率Series
        """
        if self.asset_data is None:
            raise ValueError("未提供资产历史数据")
        
        # 计算收益率序列
        returns = self.asset_data.pct_change().dropna()
        
        # 计算年化预期收益率
        annualized_returns = (1 + returns.mean()) ** self.time_period - 1
        
        return annualized_returns
    
    def _calculate_covariance(self) -> pd.DataFrame:
        """
        根据历史数据计算协方差矩阵
        
        返回:
            协方差矩阵DataFrame
        """
        if self.asset_data is None:
            raise ValueError("未提供资产历史数据")
            
        # 计算收益率序列
        returns = self.asset_data.pct_change().dropna()
        
        # 计算年化协方差矩阵
        cov_matrix = returns.cov() * self.time_period
        
        return cov_matrix
    
    def analyze_portfolio(
        self,
        weights: pd.Series,
        name: str = "Portfolio"
    ) -> Dict[str, Any]:
        """
        分析单个投资组合的各项指标
        
        参数:
            weights: 投资组合权重，Index为资产名称
            name: 投资组合名称
            
        返回:
            包含各项指标的字典
        """
        if not isinstance(weights, pd.Series):
            if isinstance(weights, dict):
                weights = pd.Series(weights)
            elif isinstance(weights, (list, np.ndarray)):
                if self.asset_names is not None and len(weights) == len(self.asset_names):
                    weights = pd.Series(weights, index=self.asset_names)
                else:
                    raise ValueError("权重列表长度必须与资产数量相同")
            else:
                raise ValueError("权重必须是pd.Series、dict、list或np.ndarray类型")
                
        # 验证权重
        if not all(item in self.asset_names for item in weights.index):
            raise ValueError("权重中的资产名称必须与分析器中的资产名称一致")
            
        # 计算投资组合收益率
        portfolio_return = 0
        if self.returns is not None:
            # 确保只使用相同资产
            common_assets = [asset for asset in weights.index if asset in self.returns.index]
            portfolio_return = np.sum(weights[common_assets] * self.returns[common_assets])
        
        # 计算投资组合波动率
        portfolio_volatility = 0
        if self.covariance_matrix is not None:
            # 确保只使用相同资产
            common_assets = [asset for asset in weights.index if asset in self.covariance_matrix.index]
            weights_subset = weights[common_assets]
            cov_subset = self.covariance_matrix.loc[common_assets, common_assets]
            portfolio_volatility = np.sqrt(weights_subset.T @ cov_subset @ weights_subset)
        
        # 计算夏普比率
        sharpe_ratio = 0
        if portfolio_volatility > 0:
            sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_volatility
        
        # 计算多样化比率
        diversification_ratio = 0
        if self.covariance_matrix is not None and portfolio_volatility > 0:
            common_assets = [asset for asset in weights.index if asset in self.covariance_matrix.index]
            weights_subset = weights[common_assets]
            asset_volatilities = np.sqrt(np.diag(self.covariance_matrix.loc[common_assets, common_assets]))
            weighted_volatility_sum = np.sum(weights_subset * asset_volatilities)
            diversification_ratio = weighted_volatility_sum / portfolio_volatility
        
        # 计算有效资产数量
        non_zero_weights = len(weights[weights > 0])
        weight_concentration = np.sum(weights ** 2)  # 赫芬达尔指数
        effective_assets = 1 / weight_concentration if weight_concentration > 0 else 0
        
        # 计算风险贡献
        risk_contribution = None
        risk_contribution_pct = None
        if self.covariance_matrix is not None:
            common_assets = [asset for asset in weights.index if asset in self.covariance_matrix.index]
            weights_subset = weights[common_assets]
            cov_subset = self.covariance_matrix.loc[common_assets, common_assets]
            
            # 计算边际风险贡献
            marginal_risk_contribution = cov_subset @ weights_subset
            
            # 计算风险贡献
            risk_contribution = weights_subset * marginal_risk_contribution / portfolio_volatility if portfolio_volatility > 0 else 0
            
            # 风险贡献百分比
            risk_contribution_pct = risk_contribution / np.sum(risk_contribution) if np.sum(risk_contribution) > 0 else 0
        
        # 汇总结果
        result = {
            "name": name,
            "expected_return": portfolio_return,
            "volatility": portfolio_volatility,
            "sharpe_ratio": sharpe_ratio,
            "diversification_ratio": diversification_ratio,
            "effective_assets": effective_assets,
            "non_zero_weights": non_zero_weights,
            "weight_concentration": weight_concentration,
            "weights": weights,
            "risk_contribution": risk_contribution,
            "risk_contribution_pct": risk_contribution_pct
        }
        
        return result
    
    def compare_portfolios(
        self,
        portfolios: Dict[str, pd.Series]
    ) -> pd.DataFrame:
        """
        比较多个投资组合的各项指标
        
        参数:
            portfolios: 投资组合权重字典，键为投资组合名称，值为权重Series
            
        返回:
            比较结果DataFrame
        """
        # 分析每个投资组合
        results = {}
        for name, weights in portfolios.items():
            try:
                results[name] = self.analyze_portfolio(weights, name)
            except Exception as e:
                logger.warning(f"分析投资组合 '{name}' 时出错: {str(e)}")
        
        # 提取要比较的指标
        metrics = [
            "expected_return",
            "volatility",
            "sharpe_ratio",
            "diversification_ratio",
            "effective_assets",
            "non_zero_weights",
            "weight_concentration"
        ]
        
        # 创建比较表格
        comparison = {}
        for name, result in results.items():
            comparison[name] = {metric: result.get(metric, np.nan) for metric in metrics}
        
        comparison_df = pd.DataFrame(comparison).T
        
        # 重命名列以便于理解
        column_mapping = {
            "expected_return": "预期收益率",
            "volatility": "波动率",
            "sharpe_ratio": "夏普比率",
            "diversification_ratio": "分散化比率",
            "effective_assets": "有效资产数",
            "non_zero_weights": "非零权重数",
            "weight_concentration": "权重集中度"
        }
        comparison_df = comparison_df.rename(columns=column_mapping)
        
        return comparison_df
    
    def calculate_risk_metrics(
        self,
        weights: pd.Series,
        confidence_level: float = 0.95
    ) -> Dict[str, float]:
        """
        计算投资组合的风险指标
        
        参数:
            weights: 投资组合权重
            confidence_level: 计算VaR和CVaR的置信水平
            
        返回:
            风险指标字典
        """
        if self.covariance_matrix is None:
            raise ValueError("未提供协方差矩阵，无法计算风险指标")
            
        if self.asset_data is None:
            raise ValueError("未提供历史数据，无法计算VaR和CVaR")
            
        # 确保我们有共同的资产
        common_assets = [asset for asset in weights.index if asset in self.covariance_matrix.index]
        weights_subset = weights[common_assets]
        cov_subset = self.covariance_matrix.loc[common_assets, common_assets]
        
        # 计算波动率
        portfolio_volatility = np.sqrt(weights_subset.T @ cov_subset @ weights_subset)
        
        # 计算基于正态分布的VaR
        z_score = stats.norm.ppf(confidence_level)
        var_normal = -z_score * portfolio_volatility
        
        # 计算基于正态分布的CVaR (Expected Shortfall)
        pdf_value = stats.norm.pdf(-z_score)
        cvar_normal = pdf_value / (1 - confidence_level) * portfolio_volatility
        
        # 计算历史模拟VaR和CVaR
        if self.asset_data is not None:
            # 确保我们有共同的资产
            common_assets = [asset for asset in weights.index if asset in self.asset_data.columns]
            weights_subset = weights[common_assets]
            
            # 计算收益率序列
            returns = self.asset_data[common_assets].pct_change().dropna()
            
            # 计算投资组合收益率序列
            portfolio_returns = returns @ weights_subset
            
            # 计算历史VaR
            var_historical = -np.percentile(portfolio_returns, (1 - confidence_level) * 100)
            
            # 计算历史CVaR
            cvar_historical = -portfolio_returns[portfolio_returns <= -var_historical].mean()
        else:
            var_historical = np.nan
            cvar_historical = np.nan
        
        # 汇总结果
        result = {
            "volatility": portfolio_volatility,
            "var_normal": var_normal,
            "cvar_normal": cvar_normal,
            "var_historical": var_historical,
            "cvar_historical": cvar_historical
        }
        
        return result
    
    def evaluate_portfolio_stability(
        self,
        weights: pd.Series,
        perturbation: float = 0.1,
        n_samples: int = 100,
        seed: int = 42
    ) -> Dict[str, Any]:
        """
        评估投资组合在输入数据扰动下的稳定性
        
        参数:
            weights: 投资组合权重
            perturbation: 扰动幅度，表示原始值的百分比
            n_samples: 扰动模拟次数
            seed: 随机种子
            
        返回:
            稳定性评估结果
        """
        if self.returns is None or self.covariance_matrix is None:
            raise ValueError("未提供收益率或协方差矩阵，无法评估稳定性")
            
        np.random.seed(seed)
        
        # 确保我们有共同的资产
        common_assets = [asset for asset in weights.index if asset in self.returns.index and asset in self.covariance_matrix.index]
        weights_subset = weights[common_assets]
        
        # 原始指标
        original_metrics = self.analyze_portfolio(weights_subset)
        
        # 保存模拟结果
        return_samples = []
        volatility_samples = []
        sharpe_ratio_samples = []
        
        for _ in range(n_samples):
            # 扰动收益率
            perturbed_returns = self.returns.copy()
            perturbed_returns[common_assets] *= (1 + np.random.uniform(-perturbation, perturbation, len(common_assets)))
            
            # 扰动协方差矩阵（保持正定性）
            perturb_factor = 1 + np.random.uniform(-perturbation, perturbation)
            perturbed_cov = self.covariance_matrix.copy() * perturb_factor
            
            # 临时替换数据
            temp_analyzer = PortfolioAnalyzer(
                returns=perturbed_returns,
                covariance_matrix=perturbed_cov,
                risk_free_rate=self.risk_free_rate,
                time_period=self.time_period
            )
            
            # 计算扰动后的指标
            perturbed_metrics = temp_analyzer.analyze_portfolio(weights_subset)
            
            # 保存结果
            return_samples.append(perturbed_metrics["expected_return"])
            volatility_samples.append(perturbed_metrics["volatility"])
            sharpe_ratio_samples.append(perturbed_metrics["sharpe_ratio"])
        
        # 计算统计量
        result = {
            "original": {
                "expected_return": original_metrics["expected_return"],
                "volatility": original_metrics["volatility"],
                "sharpe_ratio": original_metrics["sharpe_ratio"]
            },
            "perturbed": {
                "expected_return": {
                    "mean": np.mean(return_samples),
                    "std": np.std(return_samples),
                    "min": np.min(return_samples),
                    "max": np.max(return_samples),
                    "samples": return_samples
                },
                "volatility": {
                    "mean": np.mean(volatility_samples),
                    "std": np.std(volatility_samples),
                    "min": np.min(volatility_samples),
                    "max": np.max(volatility_samples),
                    "samples": volatility_samples
                },
                "sharpe_ratio": {
                    "mean": np.mean(sharpe_ratio_samples),
                    "std": np.std(sharpe_ratio_samples),
                    "min": np.min(sharpe_ratio_samples),
                    "max": np.max(sharpe_ratio_samples),
                    "samples": sharpe_ratio_samples
                }
            }
        }
        
        return result


def compare_portfolios(
    portfolios: Dict[str, pd.Series],
    returns: pd.Series = None,
    covariance_matrix: pd.DataFrame = None,
    asset_data: pd.DataFrame = None,
    risk_free_rate: float = 0.0,
    time_period: int = 252
) -> pd.DataFrame:
    """
    比较多个投资组合的各项指标（函数版本）
    
    参数:
        portfolios: 投资组合权重字典，键为投资组合名称，值为权重Series
        returns: 资产预期收益率
        covariance_matrix: 资产协方差矩阵
        asset_data: 资产历史价格数据
        risk_free_rate: 无风险利率
        time_period: 时间周期
        
    返回:
        比较结果DataFrame
    """
    analyzer = PortfolioAnalyzer(
        returns=returns,
        covariance_matrix=covariance_matrix,
        asset_data=asset_data,
        risk_free_rate=risk_free_rate,
        time_period=time_period
    )
    
    return analyzer.compare_portfolios(portfolios)


def evaluate_portfolio_stability(
    weights: pd.Series,
    returns: pd.Series = None,
    covariance_matrix: pd.DataFrame = None,
    perturbation: float = 0.1,
    n_samples: int = 100,
    seed: int = 42,
    risk_free_rate: float = 0.0,
    time_period: int = 252
) -> Dict[str, Any]:
    """
    评估投资组合在输入数据扰动下的稳定性（函数版本）
    
    参数:
        weights: 投资组合权重
        returns: 资产预期收益率
        covariance_matrix: 资产协方差矩阵
        perturbation: 扰动幅度
        n_samples: 扰动模拟次数
        seed: 随机种子
        risk_free_rate: 无风险利率
        time_period: 时间周期
        
    返回:
        稳定性评估结果
    """
    analyzer = PortfolioAnalyzer(
        returns=returns,
        covariance_matrix=covariance_matrix,
        risk_free_rate=risk_free_rate,
        time_period=time_period
    )
    
    return analyzer.evaluate_portfolio_stability(
        weights=weights,
        perturbation=perturbation,
        n_samples=n_samples,
        seed=seed
    )


def analyze_portfolio_concentration(
    weights: pd.Series
) -> Dict[str, float]:
    """
    分析投资组合的集中度
    
    参数:
        weights: 投资组合权重
        
    返回:
        集中度指标字典
    """
    # 计算非零权重数量
    non_zero_weights = len(weights[weights > 0])
    
    # 计算赫芬达尔指数 (HHI)
    hhi = np.sum(weights ** 2)
    
    # 计算基尼系数
    sorted_weights = np.sort(weights[weights > 0])
    n = len(sorted_weights)
    if n > 0:
        cumsum = np.cumsum(sorted_weights)
        gini = 1 - 2 * np.sum((cumsum - sorted_weights / 2) / cumsum[-1]) / n
    else:
        gini = 0
    
    # 计算有效资产数量
    effective_n = 1 / hhi if hhi > 0 else 0
    
    # 计算熵
    entropy = -np.sum(weights[weights > 0] * np.log(weights[weights > 0]))
    
    # 计算最大权重
    max_weight = np.max(weights) if len(weights) > 0 else 0
    
    # 计算前N个资产的权重总和
    top_5_weight_sum = np.sum(np.sort(weights)[::-1][:5]) if len(weights) >= 5 else np.sum(weights)
    top_10_weight_sum = np.sum(np.sort(weights)[::-1][:10]) if len(weights) >= 10 else np.sum(weights)
    
    return {
        "non_zero_weights": non_zero_weights,
        "herfindahl_index": hhi,
        "gini_coefficient": gini,
        "effective_assets": effective_n,
        "entropy": entropy,
        "max_weight": max_weight,
        "top_5_weight_sum": top_5_weight_sum,
        "top_10_weight_sum": top_10_weight_sum
    }


def calculate_portfolio_similarity(
    portfolio1: pd.Series,
    portfolio2: pd.Series
) -> Dict[str, float]:
    """
    计算两个投资组合之间的相似度
    
    参数:
        portfolio1: 第一个投资组合的权重
        portfolio2: 第二个投资组合的权重
        
    返回:
        相似度指标字典
    """
    # 确保两个投资组合具有相同的资产
    all_assets = set(portfolio1.index) | set(portfolio2.index)
    
    # 扩展投资组合以包含所有资产，缺少的资产权重设为0
    p1 = pd.Series(0, index=all_assets)
    p2 = pd.Series(0, index=all_assets)
    
    for asset in portfolio1.index:
        p1[asset] = portfolio1[asset]
    
    for asset in portfolio2.index:
        p2[asset] = portfolio2[asset]
    
    # 计算欧氏距离
    euclidean_distance = np.sqrt(np.sum((p1 - p2) ** 2))
    
    # 计算曼哈顿距离
    manhattan_distance = np.sum(np.abs(p1 - p2))
    
    # 计算余弦相似度
    dot_product = np.sum(p1 * p2)
    norm_p1 = np.sqrt(np.sum(p1 ** 2))
    norm_p2 = np.sqrt(np.sum(p2 ** 2))
    cosine_similarity = dot_product / (norm_p1 * norm_p2) if norm_p1 > 0 and norm_p2 > 0 else 0
    
    # 计算权重重叠百分比
    overlap = np.sum(np.minimum(p1, p2))
    
    # 计算相同非零资产的比例
    p1_non_zero = set(p1[p1 > 0].index)
    p2_non_zero = set(p2[p2 > 0].index)
    common_assets = len(p1_non_zero & p2_non_zero)
    total_assets = len(p1_non_zero | p2_non_zero)
    jaccard_similarity = common_assets / total_assets if total_assets > 0 else 0
    
    return {
        "euclidean_distance": euclidean_distance,
        "manhattan_distance": manhattan_distance,
        "cosine_similarity": cosine_similarity,
        "weight_overlap": overlap,
        "jaccard_similarity": jaccard_similarity
    } 