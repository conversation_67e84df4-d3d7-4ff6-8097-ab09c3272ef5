#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据生成工具模块

提供用于生成测试数据的函数，包括随机收益率、协方差矩阵等。
"""

import numpy as np
import pandas as pd
from typing import Tuple, Optional, Union, List
import logging

logger = logging.getLogger(__name__)


def generate_test_data(
    n_assets: int = 8,
    n_periods: int = 252,
    seed: Optional[int] = None,
    mean_annual_return: float = 0.08,
    mean_annual_vol: float = 0.2,
    corr_strength: float = 0.5,
    skewness: float = 0.0
) -> Tuple[pd.Series, pd.DataFrame]:
    """
    生成测试数据：预期收益率和协方差矩阵
    
    通过随机方式生成具有一定相关性的资产收益率预期和协方差矩阵，
    适用于投资组合优化算法测试。
    
    参数:
        n_assets: 资产数量
        n_periods: 模拟的时间周期数量（默认为252个交易日，约一年）
        seed: 随机种子，用于结果复现
        mean_annual_return: 平均年化收益率
        mean_annual_vol: 平均年化波动率
        corr_strength: 相关性强度 (0-1之间，0表示无相关，1表示高相关)
        skewness: 收益率分布偏度
    
    返回:
        预期年化收益率Series, 样本协方差矩阵DataFrame
    """
    if seed is not None:
        np.random.seed(seed)
    
    # 生成随机年化收益率 (均值为mean_annual_return，标准差为mean_annual_return/2)
    expected_returns = np.random.normal(
        loc=mean_annual_return, 
        scale=mean_annual_return/2, 
        size=n_assets
    )
    
    # 添加一些偏度，使分布更接近真实市场
    if skewness != 0:
        skew_factor = np.random.exponential(scale=skewness, size=n_assets)
        expected_returns += skew_factor * 0.02
    
    # 确保至少有一些资产有较高和较低的回报率
    if n_assets > 3:
        expected_returns[0] = max(0.12, expected_returns[0])  # 高回报
        expected_returns[1] = min(0.04, expected_returns[1])  # 低回报
    
    # 生成随机波动率 (均值为mean_annual_vol，标准差为mean_annual_vol/3)
    vols = np.random.normal(
        loc=mean_annual_vol, 
        scale=mean_annual_vol/3, 
        size=n_assets
    )
    vols = np.abs(vols)  # 确保波动率为正
    
    # 生成基础相关矩阵
    corr_matrix = np.eye(n_assets)  # 初始化为单位矩阵
    
    # 生成非对角元素的相关性
    for i in range(n_assets):
        for j in range(i+1, n_assets):
            # 生成 -0.2 到 0.8 范围内的相关性，并根据corr_strength调整
            corr = corr_strength * (np.random.random() - 0.2)
            corr_matrix[i, j] = corr
            corr_matrix[j, i] = corr  # 确保矩阵对称
    
    # 计算协方差矩阵
    vols_matrix = np.diag(vols)
    cov_matrix = vols_matrix @ corr_matrix @ vols_matrix
    
    # 确保协方差矩阵半正定
    min_eig = np.min(np.linalg.eigvals(cov_matrix))
    if min_eig < 0:
        cov_matrix = cov_matrix - 1.1*min_eig * np.eye(n_assets)
    
    # 转换为pandas数据结构
    expected_returns_series = pd.Series(expected_returns)
    cov_matrix_df = pd.DataFrame(cov_matrix)
    
    return expected_returns_series, cov_matrix_df


def generate_price_history(
    expected_returns: pd.Series,
    covariance_matrix: pd.DataFrame,
    n_periods: int = 252,
    initial_prices: Optional[Union[float, List[float]]] = 100.0,
    frequency: str = 'D',
    seed: Optional[int] = None,
    include_dividends: bool = False,
    dividend_yield: float = 0.02
) -> pd.DataFrame:
    """
    根据预期收益率和协方差矩阵生成价格历史数据
    
    使用几何布朗运动模型模拟资产价格路径
    
    参数:
        expected_returns: 年化预期收益率Series
        covariance_matrix: 年化协方差矩阵DataFrame
        n_periods: 生成的时间周期数
        initial_prices: 初始价格，可以是单个值或与资产数量相等的列表
        frequency: 数据频率 ('D'=日频, 'W'=周频, 'M'=月频)
        seed: 随机种子
        include_dividends: 是否包含股息数据
        dividend_yield: 平均年股息收益率
        
    返回:
        包含价格历史的DataFrame，索引为日期，列为资产
    """
    if seed is not None:
        np.random.seed(seed)
    
    n_assets = len(expected_returns)
    
    # 检查协方差矩阵维度
    if covariance_matrix.shape != (n_assets, n_assets):
        raise ValueError(
            f"协方差矩阵维度 {covariance_matrix.shape} "
            f"与资产数量 {n_assets} 不匹配"
        )
    
    # 设置频率乘数
    freq_multiplier = {
        'D': 252,  # 交易日
        'W': 52,   # 周
        'M': 12    # 月
    }.get(frequency, 252)
    
    # 调整收益率和协方差为相应频率
    dt = 1.0 / freq_multiplier
    mu = expected_returns.values * dt
    sigma = covariance_matrix.values * dt
    
    # 生成协方差矩阵的Cholesky分解
    try:
        L = np.linalg.cholesky(sigma)
    except np.linalg.LinAlgError:
        # 如果分解失败，尝试修正协方差矩阵
        min_eig = np.min(np.linalg.eigvals(sigma))
        if min_eig < 0:
            logger.warning(
                "协方差矩阵不是半正定的，添加小扰动使其半正定"
            )
            sigma = sigma - 1.1*min_eig * np.eye(n_assets)
            L = np.linalg.cholesky(sigma)
        else:
            raise
    
    # 初始化价格数组
    if isinstance(initial_prices, (int, float)):
        prices = np.ones((n_periods + 1, n_assets)) * initial_prices
    else:
        if len(initial_prices) != n_assets:
            raise ValueError(
                f"初始价格列表长度 {len(initial_prices)} "
                f"与资产数量 {n_assets} 不匹配"
            )
        prices = np.ones((n_periods + 1, n_assets))
        prices[0, :] = initial_prices
    
    # 生成随机价格路径
    for t in range(1, n_periods + 1):
        # 生成相关的随机数
        epsilon = np.random.normal(size=n_assets)
        correlated_returns = L @ epsilon
        
        # 计算对数收益率
        log_returns = mu - 0.5 * np.diag(sigma) + correlated_returns
        
        # 更新价格
        prices[t, :] = prices[t-1, :] * np.exp(log_returns)
    
    # 创建日期索引
    if frequency == 'D':
        dates = pd.date_range(
            start=pd.Timestamp.today() - pd.Timedelta(days=n_periods),
            periods=n_periods + 1,
            freq='B'  # 工作日
        )
    elif frequency == 'W':
        dates = pd.date_range(
            start=pd.Timestamp.today() - pd.Timedelta(weeks=n_periods),
            periods=n_periods + 1,
            freq='W'
        )
    elif frequency == 'M':
        dates = pd.date_range(
            start=pd.Timestamp.today() - pd.Timedelta(days=n_periods*30),
            periods=n_periods + 1,
            freq='M'
        )
    else:
        dates = pd.date_range(
            start=pd.Timestamp.today() - pd.Timedelta(days=n_periods),
            periods=n_periods + 1
        )
    
    # 创建价格DataFrame
    price_df = pd.DataFrame(
        prices, 
        index=dates,
        columns=expected_returns.index if hasattr(expected_returns, 'index') else None
    )
    
    # 添加股息数据（如果需要）
    if include_dividends:
        # 简单模型：按季度支付股息
        quarterly_dates = pd.date_range(
            start=dates[0],
            end=dates[-1],
            freq='Q'
        )
        
        dividend_df = pd.DataFrame(
            0.0, 
            index=dates, 
            columns=price_df.columns
        )
        
        for q_date in quarterly_dates:
            if q_date in dividend_df.index:
                # 生成每个资产不同的股息率（围绕平均股息率）
                asset_div_yields = np.random.normal(
                    loc=dividend_yield/4,  # 季度股息
                    scale=dividend_yield/16,
                    size=n_assets
                )
                asset_div_yields = np.maximum(asset_div_yields, 0)  # 确保非负
                
                dividend_df.loc[q_date] = price_df.loc[q_date].values * asset_div_yields
        
        return price_df, dividend_df
    
    return price_df


def generate_returns_from_prices(
    prices: pd.DataFrame,
    method: str = 'log'
) -> pd.DataFrame:
    """
    从价格数据计算收益率
    
    参数:
        prices: 价格数据DataFrame
        method: 计算方法，'simple'=简单收益率，'log'=对数收益率
    
    返回:
        收益率DataFrame
    """
    if method.lower() == 'simple':
        returns = prices.pct_change().dropna()
    elif method.lower() == 'log':
        returns = np.log(prices / prices.shift(1)).dropna()
    else:
        raise ValueError(
            f"不支持的计算方法: {method}，"
            f"有效选项: 'simple' 或 'log'"
        )
    
    return returns 