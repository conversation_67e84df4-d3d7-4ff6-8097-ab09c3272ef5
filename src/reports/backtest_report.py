#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一回测报告类
整合原有的PerformanceReport功能，消除重复代码
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Any, List, Optional, Union, Tuple
from .base_report import BaseReport, ReportType
# {{ AURA-X: Modify - 修复导入路径，避免循环依赖. Approval: 寸止(ID:PerformanceReport整合). }}
try:
    from src.backtest.performance.metrics import (
        calculate_returns_stats,
        calculate_risk_metrics,
        calculate_drawdown_stats,
        calculate_performance_metrics
    )
except ImportError:
    # 如果导入失败，使用备用的简化计算函数
    def calculate_performance_metrics(returns, benchmark_returns=None, risk_free_rate=0.0):
        """备用的性能指标计算函数"""
        total_return = (1 + returns).prod() - 1
        annualized_return = (1 + total_return) ** (252 / len(returns)) - 1
        volatility = returns.std() * np.sqrt(252)
        sharpe_ratio = (annualized_return - risk_free_rate) / volatility if volatility > 0 else 0

        # 计算最大回撤
        equity_curve = (1 + returns).cumprod()
        peak = equity_curve.cummax()
        drawdown = (peak - equity_curve) / peak
        max_drawdown = drawdown.max()

        return {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'annualized_volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown
        }
from src.utils.logging.logger_factory import get_logger

logger = get_logger(__name__)


class UnifiedBacktestReport(BaseReport):
    """
    统一回测报告类
    
    整合策略回测性能分析功能，消除重复代码
    """
    
    def __init__(self, 
                 returns: pd.Series,
                 benchmark_returns: Optional[pd.Series] = None,
                 positions: Optional[pd.DataFrame] = None,
                 trades: Optional[pd.DataFrame] = None,
                 risk_free_rate: float = 0.0,
                 strategy_name: str = "策略回测"):
        """
        初始化统一回测报告
        
        参数:
            returns: 策略日收益率序列
            benchmark_returns: 基准日收益率序列
            positions: 持仓数据
            trades: 交易记录
            risk_free_rate: 无风险利率(日频率)
            strategy_name: 策略名称
        """
        # 准备报告数据
        data = {
            'returns': returns,
            'benchmark_returns': benchmark_returns,
            'positions': positions,
            'trades': trades,
            'risk_free_rate': risk_free_rate
        }
        
        super().__init__(
            title=f"{strategy_name} - 回测性能报告",
            report_type=ReportType.BACKTEST,
            data=data
        )
        
        self.strategy_name = strategy_name
        self.returns = returns
        self.benchmark_returns = benchmark_returns
        self.positions = positions
        self.trades = trades
        self.risk_free_rate = risk_free_rate
        
        # 计算累积收益曲线
        self.equity_curve = (1 + self.returns).cumprod()
        if self.benchmark_returns is not None:
            self.benchmark_curve = (1 + self.benchmark_returns).cumprod()
        else:
            self.benchmark_curve = None
    
    def calculate_metrics(self) -> Dict[str, Any]:
        """计算回测性能指标"""
        try:
            # 使用现有的性能指标计算函数
            metrics = calculate_performance_metrics(
                returns=self.returns,
                benchmark_returns=self.benchmark_returns,
                risk_free_rate=self.risk_free_rate
            )
            
            # 添加额外的策略特定指标
            if self.trades is not None and not self.trades.empty:
                metrics.update(self._calculate_trading_metrics())
            
            if self.positions is not None and not self.positions.empty:
                metrics.update(self._calculate_position_metrics())
            
            return metrics
            
        except Exception as e:
            logger.error(f"计算回测指标失败: {e}")
            return self._get_fallback_metrics()
    
    def _calculate_trading_metrics(self) -> Dict[str, Any]:
        """计算交易相关指标"""
        if self.trades is None or self.trades.empty:
            return {}
        
        try:
            # 计算交易统计
            total_trades = len(self.trades)
            
            # 假设trades包含profit_loss列
            if 'profit_loss' in self.trades.columns:
                winning_trades = (self.trades['profit_loss'] > 0).sum()
                losing_trades = (self.trades['profit_loss'] < 0).sum()
                win_rate = winning_trades / total_trades if total_trades > 0 else 0
                
                avg_win = self.trades[self.trades['profit_loss'] > 0]['profit_loss'].mean()
                avg_loss = abs(self.trades[self.trades['profit_loss'] < 0]['profit_loss'].mean())
                profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0
            else:
                win_rate = 0.5  # 默认值
                profit_loss_ratio = 1.0  # 默认值
            
            return {
                'total_trades': total_trades,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio
            }
            
        except Exception as e:
            logger.warning(f"计算交易指标失败: {e}")
            return {}
    
    def _calculate_position_metrics(self) -> Dict[str, Any]:
        """计算持仓相关指标"""
        if self.positions is None or self.positions.empty:
            return {}
        
        try:
            # 计算持仓统计
            avg_positions = self.positions.mean().mean() if hasattr(self.positions, 'mean') else 0
            max_positions = self.positions.max().max() if hasattr(self.positions, 'max') else 0
            
            return {
                'avg_position_size': avg_positions,
                'max_position_size': max_positions
            }
            
        except Exception as e:
            logger.warning(f"计算持仓指标失败: {e}")
            return {}
    
    def _get_fallback_metrics(self) -> Dict[str, Any]:
        """获取备用指标（当计算失败时）"""
        try:
            total_return = self.equity_curve.iloc[-1] - 1
            annualized_return = (1 + total_return) ** (252 / len(self.returns)) - 1
            volatility = self.returns.std() * np.sqrt(252)
            sharpe_ratio = (annualized_return - self.risk_free_rate) / volatility if volatility > 0 else 0
            
            # 计算最大回撤
            peak = self.equity_curve.cummax()
            drawdown = (peak - self.equity_curve) / peak
            max_drawdown = drawdown.max()
            
            return {
                'total_return': total_return,
                'annualized_return': annualized_return,
                'annualized_volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown
            }
            
        except Exception as e:
            logger.error(f"计算备用指标失败: {e}")
            return {}
    
    def generate_charts(self) -> Dict[str, plt.Figure]:
        """生成回测报告图表"""
        charts = {}
        
        try:
            # 1. 净值曲线图
            charts['equity_curve'] = self._create_equity_curve_chart()
            
            # 2. 回撤图
            charts['drawdown'] = self._create_drawdown_chart()
            
            # 3. 月度收益热力图
            charts['monthly_returns'] = self._create_monthly_returns_chart()
            
            # 4. 滚动指标图
            charts['rolling_metrics'] = self._create_rolling_metrics_chart()
            
        except Exception as e:
            logger.error(f"生成图表失败: {e}")
        
        return charts
    
    def _create_equity_curve_chart(self) -> plt.Figure:
        """创建净值曲线图"""
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # 绘制策略净值曲线
        ax.plot(self.equity_curve.index, self.equity_curve.values, 
                label=self.strategy_name, linewidth=2, color='blue')
        
        # 如果有基准，绘制基准净值曲线
        if self.benchmark_curve is not None:
            ax.plot(self.benchmark_curve.index, self.benchmark_curve.values,
                   label='基准', linewidth=2, color='red', alpha=0.7)
        
        ax.set_title('净值曲线', fontsize=15)
        ax.set_ylabel('净值')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加收益率标注
        final_return = self.equity_curve.iloc[-1] - 1
        ax.annotate(f'总收益率: {final_return:.2%}', 
                   xy=(0.02, 0.95), xycoords='axes fraction', fontsize=12)
        
        plt.tight_layout()
        return fig
    
    def _create_drawdown_chart(self) -> plt.Figure:
        """创建回撤图"""
        fig, ax = plt.subplots(figsize=(12, 4))
        
        # 计算回撤
        peak = self.equity_curve.cummax()
        drawdown = (peak - self.equity_curve) / peak
        
        ax.fill_between(drawdown.index, drawdown.values, 0, 
                       color='red', alpha=0.3, label='回撤')
        ax.plot(drawdown.index, drawdown.values, color='red', linewidth=1)
        
        ax.set_title('回撤分析', fontsize=15)
        ax.set_ylabel('回撤比例')
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))
        ax.grid(True, alpha=0.3)
        
        # 标注最大回撤
        max_dd = drawdown.max()
        max_dd_date = drawdown.idxmax()
        ax.annotate(f'最大回撤: {max_dd:.2%}', 
                   xy=(max_dd_date, max_dd), xytext=(10, 10),
                   textcoords='offset points', fontsize=10,
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
        
        plt.tight_layout()
        return fig
    
    def _create_monthly_returns_chart(self) -> plt.Figure:
        """创建月度收益热力图"""
        fig, ax = plt.subplots(figsize=(12, 8))
        
        try:
            # 计算月度收益
            monthly_returns = self.returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
            
            # 创建年月矩阵
            monthly_data = monthly_returns.to_frame('returns')
            monthly_data['year'] = monthly_data.index.year
            monthly_data['month'] = monthly_data.index.month
            
            pivot_table = monthly_data.pivot(index='year', columns='month', values='returns')
            
            # 绘制热力图
            sns.heatmap(pivot_table, annot=True, fmt='.2%', cmap='RdYlGn', 
                       center=0, ax=ax, cbar_kws={'label': '月度收益率'})
            
            ax.set_title('月度收益热力图', fontsize=15)
            ax.set_xlabel('月份')
            ax.set_ylabel('年份')
            
        except Exception as e:
            logger.warning(f"创建月度收益图失败: {e}")
            ax.text(0.5, 0.5, '数据不足，无法生成月度收益图', 
                   ha='center', va='center', transform=ax.transAxes)
        
        plt.tight_layout()
        return fig
    
    def _create_rolling_metrics_chart(self) -> plt.Figure:
        """创建滚动指标图"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True)
        
        try:
            # 计算滚动夏普比率
            window = min(60, len(self.returns) // 4)  # 动态窗口大小
            rolling_sharpe = self.returns.rolling(window).apply(
                lambda x: (x.mean() - self.risk_free_rate) / x.std() * np.sqrt(252) 
                if x.std() > 0 else 0
            )
            
            # 计算滚动波动率
            rolling_vol = self.returns.rolling(window).std() * np.sqrt(252)
            
            # 绘制滚动夏普比率
            ax1.plot(rolling_sharpe.index, rolling_sharpe.values, color='blue', linewidth=2)
            ax1.axhline(y=self.metrics.get('sharpe_ratio', 0), color='red', linestyle='--', 
                       label=f'总体夏普: {self.metrics.get("sharpe_ratio", 0):.2f}')
            ax1.set_title('滚动夏普比率', fontsize=14)
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 绘制滚动波动率
            ax2.plot(rolling_vol.index, rolling_vol.values, color='green', linewidth=2)
            ax2.axhline(y=self.metrics.get('annualized_volatility', 0), color='red', linestyle='--',
                       label=f'总体波动率: {self.metrics.get("annualized_volatility", 0):.2%}')
            ax2.set_title('滚动波动率', fontsize=14)
            ax2.set_ylabel('年化波动率')
            ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
        except Exception as e:
            logger.warning(f"创建滚动指标图失败: {e}")
            ax1.text(0.5, 0.5, '数据不足，无法生成滚动指标图', 
                    ha='center', va='center', transform=ax1.transAxes)
        
        plt.tight_layout()
        return fig
    
    def create_tables(self) -> Dict[str, pd.DataFrame]:
        """创建回测报告表格"""
        tables = {}
        
        try:
            # 1. 性能指标表
            tables['performance_metrics'] = self._create_performance_table()
            
            # 2. 交易统计表
            if self.trades is not None and not self.trades.empty:
                tables['trading_stats'] = self._create_trading_table()
            
            # 3. 月度收益表
            tables['monthly_returns'] = self._create_monthly_returns_table()
            
        except Exception as e:
            logger.error(f"创建表格失败: {e}")
        
        return tables
    
    def _create_performance_table(self) -> pd.DataFrame:
        """创建性能指标表"""
        if not self.metrics:
            return pd.DataFrame()
        
        # 基础指标
        basic_metrics = [
            ('总收益率', 'total_return', 'percent'),
            ('年化收益率', 'annualized_return', 'percent'),
            ('年化波动率', 'annualized_volatility', 'percent'),
            ('夏普比率', 'sharpe_ratio', 'float'),
            ('最大回撤', 'max_drawdown', 'percent')
        ]
        
        data = []
        for name, key, format_type in basic_metrics:
            value = self.metrics.get(key, 0)
            formatted_value = self.format_number(value, format_type)
            data.append({'指标': name, '值': formatted_value})
        
        return pd.DataFrame(data)
    
    def _create_trading_table(self) -> pd.DataFrame:
        """创建交易统计表"""
        if self.trades is None or self.trades.empty:
            return pd.DataFrame()
        
        # 简化的交易统计
        data = [
            {'指标': '总交易次数', '值': len(self.trades)},
            {'指标': '胜率', '值': f"{self.metrics.get('win_rate', 0):.2%}"},
            {'指标': '盈亏比', '值': f"{self.metrics.get('profit_loss_ratio', 0):.2f}"}
        ]
        
        return pd.DataFrame(data)
    
    def _create_monthly_returns_table(self) -> pd.DataFrame:
        """创建月度收益表"""
        try:
            monthly_returns = self.returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
            
            data = []
            for date, ret in monthly_returns.items():
                data.append({
                    '年月': date.strftime('%Y-%m'),
                    '月度收益率': f"{ret:.2%}"
                })
            
            return pd.DataFrame(data)
            
        except Exception as e:
            logger.warning(f"创建月度收益表失败: {e}")
            return pd.DataFrame()
