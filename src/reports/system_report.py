#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一系统报告类
整合数据获取性能监控和Web性能监控功能，消除重复代码
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from .base_report import BaseReport, ReportType
from src.utils.logging.logger_factory import get_logger

logger = get_logger(__name__)


class UnifiedSystemReport(BaseReport):
    """
    统一系统报告类
    
    整合数据获取性能监控、Web性能监控等系统级报告功能
    """
    
    def __init__(self,
                 performance_data: Dict[str, Any],
                 report_subtype: str = "system",
                 time_range: Optional[tuple] = None):
        """
        初始化统一系统报告

        参数:
            performance_data: 性能数据字典
            report_subtype: 报告子类型 ('data_fetch', 'web_performance', 'system')
            time_range: 时间范围 (start_time, end_time)
        """
        # 确定具体的报告类型
        if report_subtype == "data_fetch":
            title = "数据获取性能报告"
            rtype = ReportType.DATA_FETCH
        elif report_subtype == "web_performance":
            title = "Web性能监控报告"
            rtype = ReportType.WEB_PERFORMANCE
        else:
            title = "系统性能报告"
            rtype = ReportType.SYSTEM

        super().__init__(
            title=title,
            report_type=rtype,
            data=performance_data
        )

        self.performance_data = performance_data
        self.time_range = time_range
        self.report_subtype = report_subtype
    
    def calculate_metrics(self) -> Dict[str, Any]:
        """计算系统性能指标"""
        if self.report_subtype == "data_fetch":
            return self._calculate_data_fetch_metrics()
        elif self.report_subtype == "web_performance":
            return self._calculate_web_performance_metrics()
        else:
            return self._calculate_general_system_metrics()
    
    def _calculate_data_fetch_metrics(self) -> Dict[str, Any]:
        """计算数据获取性能指标"""
        try:
            data = self.performance_data
            
            # 基础统计
            total_requests = data.get('total_requests', 0)
            successful_requests = data.get('successful_requests', 0)
            failed_requests = data.get('failed_requests', 0)
            
            # 成功率
            success_rate = successful_requests / total_requests if total_requests > 0 else 0
            
            # 响应时间统计
            response_times = data.get('response_times', [])
            if response_times:
                avg_response_time = np.mean(response_times)
                median_response_time = np.median(response_times)
                p95_response_time = np.percentile(response_times, 95)
                max_response_time = np.max(response_times)
            else:
                avg_response_time = median_response_time = p95_response_time = max_response_time = 0
            
            # 吞吐量
            duration = data.get('duration_seconds', 1)
            throughput = successful_requests / duration if duration > 0 else 0
            
            # 限流统计
            rate_limited_requests = data.get('rate_limited_requests', 0)
            rate_limit_rate = rate_limited_requests / total_requests if total_requests > 0 else 0
            
            return {
                'total_requests': total_requests,
                'successful_requests': successful_requests,
                'failed_requests': failed_requests,
                'success_rate': success_rate,
                'avg_response_time': avg_response_time,
                'median_response_time': median_response_time,
                'p95_response_time': p95_response_time,
                'max_response_time': max_response_time,
                'throughput': throughput,
                'rate_limited_requests': rate_limited_requests,
                'rate_limit_rate': rate_limit_rate
            }
            
        except Exception as e:
            logger.error(f"计算数据获取指标失败: {e}")
            return {}
    
    def _calculate_web_performance_metrics(self) -> Dict[str, Any]:
        """计算Web性能指标"""
        try:
            data = self.performance_data
            
            # API请求统计
            total_api_calls = data.get('total_api_calls', 0)
            avg_api_response_time = data.get('avg_api_response_time', 0)
            
            # 错误统计
            error_count = data.get('error_count', 0)
            error_rate = error_count / total_api_calls if total_api_calls > 0 else 0
            
            # 并发统计
            max_concurrent_requests = data.get('max_concurrent_requests', 0)
            avg_concurrent_requests = data.get('avg_concurrent_requests', 0)
            
            # 资源使用
            cpu_usage = data.get('cpu_usage', 0)
            memory_usage = data.get('memory_usage', 0)
            
            return {
                'total_api_calls': total_api_calls,
                'avg_api_response_time': avg_api_response_time,
                'error_count': error_count,
                'error_rate': error_rate,
                'max_concurrent_requests': max_concurrent_requests,
                'avg_concurrent_requests': avg_concurrent_requests,
                'cpu_usage': cpu_usage,
                'memory_usage': memory_usage
            }
            
        except Exception as e:
            logger.error(f"计算Web性能指标失败: {e}")
            return {}
    
    def _calculate_general_system_metrics(self) -> Dict[str, Any]:
        """计算通用系统指标"""
        try:
            data = self.performance_data
            
            # 合并数据获取和Web性能指标
            metrics = {}
            metrics.update(self._calculate_data_fetch_metrics())
            metrics.update(self._calculate_web_performance_metrics())
            
            return metrics
            
        except Exception as e:
            logger.error(f"计算系统指标失败: {e}")
            return {}
    
    def generate_charts(self) -> Dict[str, plt.Figure]:
        """生成系统报告图表"""
        charts = {}
        
        try:
            if self.report_subtype == "data_fetch":
                charts.update(self._create_data_fetch_charts())
            elif self.report_subtype == "web_performance":
                charts.update(self._create_web_performance_charts())
            else:
                charts.update(self._create_general_system_charts())
                
        except Exception as e:
            logger.error(f"生成系统图表失败: {e}")
        
        return charts
    
    def _create_data_fetch_charts(self) -> Dict[str, plt.Figure]:
        """创建数据获取性能图表"""
        charts = {}
        
        # 1. 成功率饼图
        charts['success_rate_pie'] = self._create_success_rate_pie()
        
        # 2. 响应时间分布图
        charts['response_time_dist'] = self._create_response_time_distribution()
        
        # 3. 吞吐量时间序列图
        charts['throughput_timeline'] = self._create_throughput_timeline()
        
        return charts
    
    def _create_success_rate_pie(self) -> plt.Figure:
        """创建成功率饼图"""
        fig, ax = plt.subplots(figsize=(8, 6))
        
        successful = self.metrics.get('successful_requests', 0)
        failed = self.metrics.get('failed_requests', 0)
        rate_limited = self.metrics.get('rate_limited_requests', 0)
        
        if successful + failed + rate_limited > 0:
            labels = ['成功', '失败', '限流']
            sizes = [successful, failed, rate_limited]
            colors = ['#2ecc71', '#e74c3c', '#f39c12']
            
            # 过滤掉为0的部分
            filtered_data = [(label, size, color) for label, size, color in zip(labels, sizes, colors) if size > 0]
            if filtered_data:
                labels, sizes, colors = zip(*filtered_data)
                
                wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
                
                # 美化文本
                for autotext in autotexts:
                    autotext.set_color('white')
                    autotext.set_fontweight('bold')
        else:
            ax.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax.transAxes, fontsize=16)
        
        ax.set_title('请求成功率分布', fontsize=15)
        plt.tight_layout()
        return fig
    
    def _create_response_time_distribution(self) -> plt.Figure:
        """创建响应时间分布图"""
        fig, ax = plt.subplots(figsize=(10, 6))
        
        response_times = self.performance_data.get('response_times', [])
        
        if response_times:
            ax.hist(response_times, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
            ax.axvline(np.mean(response_times), color='red', linestyle='--', 
                      label=f'平均值: {np.mean(response_times):.2f}s')
            ax.axvline(np.median(response_times), color='green', linestyle='--',
                      label=f'中位数: {np.median(response_times):.2f}s')
            ax.legend()
        else:
            ax.text(0.5, 0.5, '暂无响应时间数据', ha='center', va='center', transform=ax.transAxes, fontsize=16)
        
        ax.set_title('响应时间分布', fontsize=15)
        ax.set_xlabel('响应时间 (秒)')
        ax.set_ylabel('频次')
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig
    
    def _create_throughput_timeline(self) -> plt.Figure:
        """创建吞吐量时间序列图"""
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # 模拟时间序列数据（实际应用中应该从performance_data中获取）
        timeline_data = self.performance_data.get('timeline_data', [])
        
        if timeline_data:
            times = [item['timestamp'] for item in timeline_data]
            throughputs = [item['throughput'] for item in timeline_data]
            
            ax.plot(times, throughputs, marker='o', linewidth=2, markersize=4)
            ax.set_title('吞吐量时间序列', fontsize=15)
            ax.set_ylabel('吞吐量 (请求/秒)')
            ax.grid(True, alpha=0.3)
            
            # 格式化x轴时间显示
            fig.autofmt_xdate()
        else:
            # 显示当前吞吐量
            current_throughput = self.metrics.get('throughput', 0)
            ax.bar(['当前吞吐量'], [current_throughput], color='lightblue')
            ax.set_title('系统吞吐量', fontsize=15)
            ax.set_ylabel('吞吐量 (请求/秒)')
            
            # 添加数值标签
            ax.text(0, current_throughput + current_throughput * 0.05, 
                   f'{current_throughput:.2f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        return fig
    
    def _create_web_performance_charts(self) -> Dict[str, plt.Figure]:
        """创建Web性能图表"""
        charts = {}
        
        # 1. API响应时间图
        charts['api_response_time'] = self._create_api_response_time_chart()
        
        # 2. 错误率图
        charts['error_rate'] = self._create_error_rate_chart()
        
        return charts
    
    def _create_api_response_time_chart(self) -> plt.Figure:
        """创建API响应时间图"""
        fig, ax = plt.subplots(figsize=(10, 6))
        
        avg_response_time = self.metrics.get('avg_api_response_time', 0)
        
        # 简单的条形图显示平均响应时间
        ax.bar(['平均API响应时间'], [avg_response_time], color='lightcoral')
        ax.set_title('API性能指标', fontsize=15)
        ax.set_ylabel('响应时间 (毫秒)')
        
        # 添加数值标签
        ax.text(0, avg_response_time + avg_response_time * 0.05, 
               f'{avg_response_time:.2f}ms', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        return fig
    
    def _create_error_rate_chart(self) -> plt.Figure:
        """创建错误率图"""
        fig, ax = plt.subplots(figsize=(8, 6))
        
        error_rate = self.metrics.get('error_rate', 0)
        success_rate = 1 - error_rate
        
        labels = ['成功', '错误']
        sizes = [success_rate, error_rate]
        colors = ['#2ecc71', '#e74c3c']
        
        wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, 
                                         autopct='%1.2f%%', startangle=90)
        
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
        
        ax.set_title('API请求成功率', fontsize=15)
        plt.tight_layout()
        return fig
    
    def _create_general_system_charts(self) -> Dict[str, plt.Figure]:
        """创建通用系统图表"""
        charts = {}
        
        # 合并数据获取和Web性能图表
        charts.update(self._create_data_fetch_charts())
        charts.update(self._create_web_performance_charts())
        
        return charts
    
    def create_tables(self) -> Dict[str, pd.DataFrame]:
        """创建系统报告表格"""
        tables = {}
        
        try:
            # 1. 性能指标表
            tables['performance_metrics'] = self._create_performance_metrics_table()
            
            # 2. 详细统计表
            if self.report_subtype == "data_fetch":
                tables['detailed_stats'] = self._create_data_fetch_stats_table()
            elif self.report_subtype == "web_performance":
                tables['detailed_stats'] = self._create_web_performance_stats_table()
            
        except Exception as e:
            logger.error(f"创建系统表格失败: {e}")
        
        return tables
    
    def _create_performance_metrics_table(self) -> pd.DataFrame:
        """创建性能指标表"""
        if not self.metrics:
            return pd.DataFrame()
        
        data = []
        for key, value in self.metrics.items():
            if isinstance(value, (int, float)):
                # 根据指标类型选择格式
                if 'rate' in key or 'ratio' in key:
                    formatted_value = self.format_number(value, 'percent')
                elif 'time' in key:
                    formatted_value = f"{value:.3f}s"
                elif 'usage' in key:
                    formatted_value = self.format_number(value, 'percent')
                else:
                    formatted_value = self.format_number(value)
                
                data.append({
                    '指标': key.replace('_', ' ').title(),
                    '值': formatted_value
                })
        
        return pd.DataFrame(data)
    
    def _create_data_fetch_stats_table(self) -> pd.DataFrame:
        """创建数据获取统计表"""
        data = [
            {'统计项': '总请求数', '值': self.metrics.get('total_requests', 0)},
            {'统计项': '成功请求数', '值': self.metrics.get('successful_requests', 0)},
            {'统计项': '失败请求数', '值': self.metrics.get('failed_requests', 0)},
            {'统计项': '限流请求数', '值': self.metrics.get('rate_limited_requests', 0)},
            {'统计项': '平均响应时间', '值': f"{self.metrics.get('avg_response_time', 0):.3f}s"},
            {'统计项': '系统吞吐量', '值': f"{self.metrics.get('throughput', 0):.2f} 请求/秒"}
        ]
        
        return pd.DataFrame(data)
    
    def _create_web_performance_stats_table(self) -> pd.DataFrame:
        """创建Web性能统计表"""
        data = [
            {'统计项': 'API调用总数', '值': self.metrics.get('total_api_calls', 0)},
            {'统计项': '平均API响应时间', '值': f"{self.metrics.get('avg_api_response_time', 0):.2f}ms"},
            {'统计项': '错误数量', '值': self.metrics.get('error_count', 0)},
            {'统计项': '最大并发请求', '值': self.metrics.get('max_concurrent_requests', 0)},
            {'统计项': 'CPU使用率', '值': f"{self.metrics.get('cpu_usage', 0):.1%}"},
            {'统计项': '内存使用率', '值': f"{self.metrics.get('memory_usage', 0):.1%}"}
        ]
        
        return pd.DataFrame(data)
