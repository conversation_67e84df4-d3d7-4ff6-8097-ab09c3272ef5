"""
Matplotlib适配器

提供matplotlib的配置和样式适配，解决字体问题。
"""

import os
import platform
import warnings
import matplotlib as mpl
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
import matplotlib

# 屏蔽特定警告
warnings.filterwarnings("ignore", category=UserWarning, message=".*SimHei.*")
warnings.filterwarnings("ignore", message=".*missing from current font.*")

matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False

class MatplotlibAdapter:
    """Matplotlib适配器类"""
    
    _initialized = False
    
    @staticmethod
    def init(use_chinese=False):
        """
        初始化Matplotlib配置
        
        参数：
            use_chinese: 是否启用中文支持
        """
        if MatplotlibAdapter._initialized:
            return
            
        # 设置样式
        plt.style.use('seaborn-v0_8-whitegrid')
        
        # 设置基本配置
        mpl.rcParams['figure.figsize'] = (10, 6)
        mpl.rcParams['axes.grid'] = True
        mpl.rcParams['grid.linestyle'] = '--'
        mpl.rcParams['grid.alpha'] = 0.7
        
        # 处理中文显示问题
        if use_chinese:
            MatplotlibAdapter.configure_chinese_fonts()
        else:
            # 使用英文字体
            mpl.rcParams['font.family'] = 'sans-serif'
            mpl.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Bitstream Vera Sans', 'Lucida Grande', 'Verdana', 'Geneva', 'Lucid', 'Helvetica']
        
        MatplotlibAdapter._initialized = True
    
    @staticmethod
    def configure_chinese_fonts():
        """配置中文字体支持"""
        system = platform.system()
        
        # 设置默认使用的字体
        if system == 'Windows':
            # Windows系统
            font_list = ['Microsoft YaHei', 'SimHei', 'SimSun', 'NSimSun', 'FangSong', 'KaiTi']
        elif system == 'Darwin':
            # macOS系统
            font_list = ['PingFang SC', 'Heiti SC', 'STHeiti', 'Hiragino Sans GB', 'Apple LiGothic Medium']
        else:  # Linux或其他
            font_list = ['WenQuanYi Micro Hei', 'Droid Sans Fallback', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC']
        
        # 设置字体
        mpl.rcParams['font.family'] = 'sans-serif'
        mpl.rcParams['font.sans-serif'] = font_list
        mpl.rcParams['axes.unicode_minus'] = False  # 正确显示负号
    
    @staticmethod
    def get_font_property(font_size=12):
        """
        获取字体属性
        
        参数：
            font_size: 字体大小
        
        返回：
            FontProperties对象
        """
        system = platform.system()
        
        if system == 'Windows':
            font_name = 'Microsoft YaHei'
        elif system == 'Darwin':
            font_name = 'PingFang SC'
        else:
            font_name = 'WenQuanYi Micro Hei'
        
        return FontProperties(family=font_name, size=font_size)
    
    @staticmethod
    def set_chart_style(ax, title=None, xlabel=None, ylabel=None, legend=True, 
                        grid=True, fontsize=12, use_chinese=False):
        """
        设置图表样式
        
        参数：
            ax: 图表轴对象
            title: 标题
            xlabel: x轴标签
            ylabel: y轴标签
            legend: 是否显示图例
            grid: 是否显示网格
            fontsize: 字体大小
            use_chinese: 是否使用中文
        """
        if use_chinese:
            font_prop = MatplotlibAdapter.get_font_property(fontsize)
            
            if title:
                ax.set_title(title, fontproperties=font_prop, fontsize=fontsize+2)
            if xlabel:
                ax.set_xlabel(xlabel, fontproperties=font_prop, fontsize=fontsize)
            if ylabel:
                ax.set_ylabel(ylabel, fontproperties=font_prop, fontsize=fontsize)
            
            if legend:
                ax.legend(prop=font_prop)
        else:
            if title:
                ax.set_title(title, fontsize=fontsize+2)
            if xlabel:
                ax.set_xlabel(xlabel, fontsize=fontsize)
            if ylabel:
                ax.set_ylabel(ylabel, fontsize=fontsize)
            
            if legend:
                ax.legend(fontsize=fontsize-2)
        
        if grid:
            ax.grid(linestyle='--', alpha=0.7)
            
        # 设置轴刻度标签大小
        ax.tick_params(axis='both', labelsize=fontsize-2)
        
        # 添加一些边距
        plt.tight_layout()


# 在模块导入时自动初始化
MatplotlibAdapter.init()

