#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告工厂
提供统一的报告创建接口
"""

from typing import Dict, Type, Any, Optional, Union
import pandas as pd
from .base_report import BaseReport, ReportType
from .backtest_report import UnifiedBacktestReport
from .system_report import UnifiedSystemReport
from src.utils.logging.logger_factory import get_logger

logger = get_logger(__name__)


class ReportFactory:
    """
    报告工厂类
    
    负责创建和管理不同类型的报告实例
    """
    
    # 存储已注册的报告类型
    _reports: Dict[str, Type[BaseReport]] = {}
    
    @classmethod
    def register_report(cls, report_type: str, report_class: Type[BaseReport]) -> None:
        """
        注册报告类型
        
        参数:
            report_type: 报告类型名称
            report_class: 报告类
        """
        if report_type in cls._reports:
            logger.warning(f"报告类型 '{report_type}' 已存在，将被覆盖")
            
        cls._reports[report_type] = report_class
        logger.info(f"注册报告类型: {report_type}")
    
    @classmethod
    def create_report(cls, report_type: str, **kwargs) -> Optional[BaseReport]:
        """
        创建报告实例
        
        参数:
            report_type: 报告类型名称
            **kwargs: 传递给报告构造函数的参数
            
        返回:
            报告实例，如果类型不存在则返回None
        """
        if report_type not in cls._reports:
            logger.error(f"未知的报告类型: {report_type}")
            raise ValueError(f"未知的报告类型: {report_type}")
            
        report_class = cls._reports[report_type]
        
        try:
            report = report_class(**kwargs)
            logger.info(f"创建报告: {report_type}")
            return report
        except Exception as e:
            logger.error(f"创建报告 {report_type} 失败: {e}")
            raise
    
    @classmethod
    def get_available_reports(cls) -> Dict[str, Type[BaseReport]]:
        """
        获取所有可用的报告类型
        
        返回:
            Dict: 报告类型名称到报告类的映射
        """
        return cls._reports.copy()
    
    @classmethod
    def create_backtest_report(cls, 
                             returns: pd.Series,
                             benchmark_returns: Optional[pd.Series] = None,
                             positions: Optional[pd.DataFrame] = None,
                             trades: Optional[pd.DataFrame] = None,
                             risk_free_rate: float = 0.0,
                             strategy_name: str = "策略回测") -> UnifiedBacktestReport:
        """
        创建回测报告的便捷方法
        
        参数:
            returns: 策略日收益率序列
            benchmark_returns: 基准日收益率序列
            positions: 持仓数据
            trades: 交易记录
            risk_free_rate: 无风险利率
            strategy_name: 策略名称
            
        返回:
            UnifiedBacktestReport: 回测报告实例
        """
        return cls.create_report('backtest',
                               returns=returns,
                               benchmark_returns=benchmark_returns,
                               positions=positions,
                               trades=trades,
                               risk_free_rate=risk_free_rate,
                               strategy_name=strategy_name)
    
    @classmethod
    def create_system_report(cls,
                           performance_data: Dict[str, Any],
                           report_subtype: str = "system",
                           time_range: Optional[tuple] = None) -> UnifiedSystemReport:
        """
        创建系统报告的便捷方法

        参数:
            performance_data: 性能数据字典
            report_subtype: 报告子类型
            time_range: 时间范围

        返回:
            UnifiedSystemReport: 系统报告实例
        """
        return cls.create_report('system',
                               performance_data=performance_data,
                               report_subtype=report_subtype,
                               time_range=time_range)


# 注册默认的报告类型
ReportFactory.register_report('backtest', UnifiedBacktestReport)
ReportFactory.register_report('system', UnifiedSystemReport)


def create_backtest_report(returns: pd.Series,
                         benchmark_returns: Optional[pd.Series] = None,
                         positions: Optional[pd.DataFrame] = None,
                         trades: Optional[pd.DataFrame] = None,
                         risk_free_rate: float = 0.0,
                         strategy_name: str = "策略回测") -> UnifiedBacktestReport:
    """
    创建回测报告的便捷函数
    
    参数:
        returns: 策略日收益率序列
        benchmark_returns: 基准日收益率序列
        positions: 持仓数据
        trades: 交易记录
        risk_free_rate: 无风险利率
        strategy_name: 策略名称
        
    返回:
        UnifiedBacktestReport: 回测报告实例
    """
    return ReportFactory.create_backtest_report(
        returns=returns,
        benchmark_returns=benchmark_returns,
        positions=positions,
        trades=trades,
        risk_free_rate=risk_free_rate,
        strategy_name=strategy_name
    )


def create_system_report(performance_data: Dict[str, Any],
                       report_type: str = "system",
                       time_range: Optional[tuple] = None) -> UnifiedSystemReport:
    """
    创建系统报告的便捷函数

    参数:
        performance_data: 性能数据字典
        report_type: 报告子类型 ('data_fetch', 'web_performance', 'system')
        time_range: 时间范围

    返回:
        UnifiedSystemReport: 系统报告实例
    """
    return ReportFactory.create_system_report(
        performance_data=performance_data,
        report_subtype=report_type,
        time_range=time_range
    )


def create_data_fetch_report(performance_data: Dict[str, Any]) -> UnifiedSystemReport:
    """
    创建数据获取性能报告的便捷函数
    
    参数:
        performance_data: 数据获取性能数据
        
    返回:
        UnifiedSystemReport: 数据获取报告实例
    """
    return create_system_report(performance_data, report_type="data_fetch")


def create_web_performance_report(performance_data: Dict[str, Any]) -> UnifiedSystemReport:
    """
    创建Web性能报告的便捷函数
    
    参数:
        performance_data: Web性能数据
        
    返回:
        UnifiedSystemReport: Web性能报告实例
    """
    return create_system_report(performance_data, report_type="web_performance")
