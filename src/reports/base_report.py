#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一报告基类
提供所有报告类型的通用功能和接口
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, Tuple
from enum import Enum
import os
import datetime
import json
import io
import base64
from dataclasses import dataclass
from src.utils.logging.logger_factory import get_logger

logger = get_logger(__name__)


class ReportType(Enum):
    """报告类型枚举"""
    BACKTEST = "backtest"
    SYSTEM = "system"
    DATA_FETCH = "data_fetch"
    WEB_PERFORMANCE = "web_performance"
    CUSTOM = "custom"


class ReportFormat(Enum):
    """报告格式枚举"""
    HTML = "html"
    PDF = "pdf"
    JSON = "json"
    TEXT = "text"
    EXCEL = "excel"


@dataclass
class ReportMetadata:
    """报告元数据"""
    title: str
    report_type: ReportType
    generated_at: datetime.datetime
    author: str = "量化投资平台"
    version: str = "1.0"
    description: str = ""
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []


class BaseReport(ABC):
    """
    统一报告基类
    
    提供所有报告类型的通用功能：
    1. 数据处理和验证
    2. 指标计算和统计
    3. 图表生成和可视化
    4. 报告格式化和导出
    5. 模板管理和渲染
    """
    
    def __init__(self, 
                 title: str,
                 report_type: ReportType,
                 data: Optional[Dict[str, Any]] = None,
                 config: Optional[Dict[str, Any]] = None):
        """
        初始化报告基类
        
        参数:
            title: 报告标题
            report_type: 报告类型
            data: 报告数据
            config: 报告配置
        """
        self.metadata = ReportMetadata(
            title=title,
            report_type=report_type,
            generated_at=datetime.datetime.now()
        )
        
        self.data = data or {}
        self.config = config or {}
        self.metrics = {}
        self.charts = {}
        self.tables = {}
        
        # 设置matplotlib中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
        plt.rcParams['axes.unicode_minus'] = False
        
        logger.info(f"初始化报告: {title}, 类型: {report_type.value}")
    
    @abstractmethod
    def calculate_metrics(self) -> Dict[str, Any]:
        """
        计算报告指标
        
        返回:
            指标字典
        """
        pass
    
    @abstractmethod
    def generate_charts(self) -> Dict[str, plt.Figure]:
        """
        生成报告图表
        
        返回:
            图表字典
        """
        pass
    
    @abstractmethod
    def create_tables(self) -> Dict[str, pd.DataFrame]:
        """
        创建报告表格
        
        返回:
            表格字典
        """
        pass
    
    def validate_data(self) -> bool:
        """
        验证报告数据
        
        返回:
            是否有效
        """
        if not self.data:
            logger.warning("报告数据为空")
            return False
        
        return True
    
    def process_data(self) -> None:
        """处理报告数据"""
        if not self.validate_data():
            raise ValueError("报告数据验证失败")
        
        # 计算指标
        self.metrics = self.calculate_metrics()
        
        # 生成图表
        self.charts = self.generate_charts()
        
        # 创建表格
        self.tables = self.create_tables()
        
        logger.info(f"报告数据处理完成: {len(self.metrics)}个指标, {len(self.charts)}个图表, {len(self.tables)}个表格")
    
    def figure_to_base64(self, fig: plt.Figure) -> str:
        """
        将matplotlib图表转换为base64字符串
        
        参数:
            fig: matplotlib图表对象
            
        返回:
            base64编码的图片字符串
        """
        buffer = io.BytesIO()
        fig.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        buffer.close()
        plt.close(fig)
        return image_base64
    
    def format_number(self, value: Union[int, float], format_type: str = 'auto') -> str:
        """
        格式化数字显示
        
        参数:
            value: 数值
            format_type: 格式类型 ('auto', 'percent', 'currency', 'integer', 'float')
            
        返回:
            格式化后的字符串
        """
        if pd.isna(value) or value is None:
            return "N/A"
        
        if format_type == 'percent':
            return f"{value:.2%}"
        elif format_type == 'currency':
            return f"¥{value:,.2f}"
        elif format_type == 'integer':
            return f"{int(value):,}"
        elif format_type == 'float':
            return f"{value:.4f}"
        else:  # auto
            if isinstance(value, int) or (isinstance(value, float) and value.is_integer()):
                return f"{int(value):,}"
            else:
                return f"{value:.4f}"
    
    def create_summary_table(self) -> pd.DataFrame:
        """
        创建摘要表格
        
        返回:
            摘要表格
        """
        if not self.metrics:
            return pd.DataFrame()
        
        summary_data = []
        for key, value in self.metrics.items():
            if isinstance(value, (int, float)):
                summary_data.append({
                    '指标': key,
                    '值': self.format_number(value),
                    '类型': type(value).__name__
                })
        
        return pd.DataFrame(summary_data)
    
    def generate_html_report(self) -> str:
        """
        生成HTML格式报告
        
        返回:
            HTML字符串
        """
        # 确保数据已处理
        if not self.metrics:
            self.process_data()
        
        html_parts = []
        
        # HTML头部
        html_parts.append(self._get_html_header())
        
        # 报告标题和元数据
        html_parts.append(self._get_html_title_section())
        
        # 摘要部分
        html_parts.append(self._get_html_summary_section())
        
        # 图表部分
        html_parts.append(self._get_html_charts_section())
        
        # 表格部分
        html_parts.append(self._get_html_tables_section())
        
        # HTML尾部
        html_parts.append(self._get_html_footer())
        
        return '\n'.join(html_parts)
    
    def _get_html_header(self) -> str:
        """获取HTML头部"""
        return f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{self.metadata.title}</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }}
        .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }}
        .metric {{ display: inline-block; margin: 10px; padding: 10px; background: #f8f9fa; border-radius: 5px; }}
        .chart {{ text-align: center; margin: 20px 0; }}
        table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .footer {{ text-align: center; color: #666; margin-top: 30px; }}
    </style>
</head>
<body>
"""
    
    def _get_html_title_section(self) -> str:
        """获取标题部分HTML"""
        return f"""
<div class="header">
    <h1>{self.metadata.title}</h1>
    <p>报告类型: {self.metadata.report_type.value} | 生成时间: {self.metadata.generated_at.strftime('%Y-%m-%d %H:%M:%S')}</p>
    <p>{self.metadata.description}</p>
</div>
"""
    
    def _get_html_summary_section(self) -> str:
        """获取摘要部分HTML"""
        if not self.metrics:
            return ""
        
        metrics_html = []
        for key, value in self.metrics.items():
            if isinstance(value, (int, float)):
                formatted_value = self.format_number(value)
                metrics_html.append(f'<div class="metric"><strong>{key}:</strong> {formatted_value}</div>')
        
        return f"""
<div class="section">
    <h2>📊 关键指标</h2>
    {''.join(metrics_html)}
</div>
"""
    
    def _get_html_charts_section(self) -> str:
        """获取图表部分HTML"""
        if not self.charts:
            return ""
        
        charts_html = []
        for chart_name, fig in self.charts.items():
            image_base64 = self.figure_to_base64(fig)
            charts_html.append(f"""
<div class="chart">
    <h3>{chart_name}</h3>
    <img src="data:image/png;base64,{image_base64}" alt="{chart_name}" style="max-width: 100%; height: auto;">
</div>
""")
        
        return f"""
<div class="section">
    <h2>📈 图表分析</h2>
    {''.join(charts_html)}
</div>
"""
    
    def _get_html_tables_section(self) -> str:
        """获取表格部分HTML"""
        if not self.tables:
            return ""
        
        tables_html = []
        for table_name, df in self.tables.items():
            if not df.empty:
                table_html = df.to_html(classes='table', escape=False, index=False)
                tables_html.append(f"""
<div>
    <h3>{table_name}</h3>
    {table_html}
</div>
""")
        
        return f"""
<div class="section">
    <h2>📋 详细数据</h2>
    {''.join(tables_html)}
</div>
"""
    
    def _get_html_footer(self) -> str:
        """获取HTML尾部"""
        return f"""
<div class="footer">
    <p>报告由 {self.metadata.author} 生成 | 版本: {self.metadata.version}</p>
</div>
</body>
</html>
"""
    
    def export_report(self, 
                     output_path: str,
                     format: ReportFormat = ReportFormat.HTML) -> str:
        """
        导出报告
        
        参数:
            output_path: 输出路径
            format: 报告格式
            
        返回:
            实际输出文件路径
        """
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        if format == ReportFormat.HTML:
            html_content = self.generate_html_report()
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
        
        elif format == ReportFormat.JSON:
            report_data = {
                'metadata': {
                    'title': self.metadata.title,
                    'report_type': self.metadata.report_type.value,
                    'generated_at': self.metadata.generated_at.isoformat(),
                    'author': self.metadata.author,
                    'version': self.metadata.version
                },
                'metrics': self.metrics,
                'tables': {name: df.to_dict('records') for name, df in self.tables.items()}
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
        
        else:
            raise ValueError(f"不支持的报告格式: {format}")
        
        logger.info(f"报告已导出: {output_path}")
        return output_path
    
    def get_report_summary(self) -> Dict[str, Any]:
        """
        获取报告摘要
        
        返回:
            报告摘要字典
        """
        return {
            'title': self.metadata.title,
            'type': self.metadata.report_type.value,
            'generated_at': self.metadata.generated_at,
            'metrics_count': len(self.metrics),
            'charts_count': len(self.charts),
            'tables_count': len(self.tables),
            'data_points': sum(len(df) for df in self.tables.values()) if self.tables else 0
        }
