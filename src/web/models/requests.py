#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API请求模型定义
- 定义所有API请求的数据结构
- 使用Pydantic进行数据验证
- 提供清晰的API文档
"""

from typing import Dict, List, Any, Optional, Union
from datetime import datetime, date
from enum import Enum

from pydantic import BaseModel, Field, validator

# ==================== 枚举定义 ====================

class DataType(str, Enum):
    """数据类型枚举"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    FUNDAMENTAL = "fundamental"
    MARKET_CAP = "market_cap"
    CASH_FLOW = "cash_flow"

class StrategyType(str, Enum):
    """策略类型枚举"""
    MOVING_AVERAGE = "moving_average"
    DUAL_MA = "dual_ma"
    VALUE_INVESTMENT = "value_investment"
    MULTI_FACTOR = "multi_factor"
    HIGH_FREQUENCY = "high_frequency"
    ML_STRATEGY = "ml_strategy"

class MarketType(str, Enum):
    """市场类型枚举"""
    MAIN = "主板"
    SME = "中小板"
    CHINEXT = "创业板"
    STAR = "科创板"
    BSE = "北交所"

class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

# ==================== 基础请求模型 ====================

class BaseRequest(BaseModel):
    """基础请求模型"""
    request_id: Optional[str] = Field(None, description="请求ID")
    timestamp: Optional[datetime] = Field(default_factory=datetime.now, description="请求时间戳")

# ==================== 数据获取请求模型 ====================

class DataFetchRequest(BaseRequest):
    """数据获取请求模型"""
    stock_codes: Optional[List[str]] = Field(
        None, 
        description="股票代码列表，如['000001.SZ', '000002.SZ']"
    )
    start_date: Optional[str] = Field(
        None,
        description="开始日期，格式：YYYYMMDD",
        pattern=r'^\d{8}$'
    )
    end_date: Optional[str] = Field(
        None,
        description="结束日期，格式：YYYYMMDD",
        pattern=r'^\d{8}$'
    )
    data_types: List[DataType] = Field(
        [DataType.DAILY], 
        description="数据类型列表"
    )
    limit: Optional[int] = Field(
        1000, 
        description="股票数量限制",
        ge=1,
        le=10000
    )
    market: Optional[MarketType] = Field(None, description="市场类型筛选")
    industry: Optional[str] = Field(None, description="行业筛选")
    use_cache: bool = Field(True, description="是否使用缓存")
    force_update: bool = Field(False, description="是否强制更新")

    @validator('end_date')
    def validate_date_range(cls, v, values):
        """验证日期范围"""
        if v and 'start_date' in values and values['start_date']:
            if v < values['start_date']:
                raise ValueError('结束日期不能早于开始日期')
        return v

class StockQueryRequest(BaseRequest):
    """股票查询请求模型"""
    stock_code: Optional[str] = Field(None, description="股票代码")
    stock_name: Optional[str] = Field(None, description="股票名称")
    market: Optional[MarketType] = Field(None, description="市场类型")
    industry: Optional[str] = Field(None, description="行业")
    limit: int = Field(100, description="返回数量限制", ge=1, le=5000)
    offset: int = Field(0, description="偏移量", ge=0)

# ==================== 回测请求模型 ====================

class BacktestRequest(BaseRequest):
    """回测请求模型"""
    strategy: StrategyType = Field(..., description="策略类型")
    start_date: Optional[str] = Field(
        None,
        description="回测开始日期，格式：YYYYMMDD",
        pattern=r'^\d{8}$'
    )
    end_date: Optional[str] = Field(
        None,
        description="回测结束日期，格式：YYYYMMDD",
        pattern=r'^\d{8}$'
    )
    initial_capital: float = Field(
        1000000, 
        description="初始资金",
        gt=0,
        le=100000000
    )
    stock_codes: Optional[List[str]] = Field(
        None, 
        description="股票代码列表，为空则使用全市场"
    )
    benchmark: str = Field(
        "000300.SH", 
        description="基准指数代码"
    )
    parameters: Optional[Dict[str, Any]] = Field(
        None, 
        description="策略参数字典"
    )
    risk_params: Optional[Dict[str, float]] = Field(
        None,
        description="风险控制参数"
    )
    generate_plot: bool = Field(True, description="是否生成图表")
    save_results: bool = Field(True, description="是否保存结果")

    @validator('parameters')
    def validate_parameters(cls, v, values):
        """验证策略参数"""
        if v is None:
            return v
        
        strategy = values.get('strategy')
        if strategy == StrategyType.MOVING_AVERAGE:
            # 移动平均策略参数验证
            if 'window' in v and (v['window'] < 1 or v['window'] > 250):
                raise ValueError('移动平均窗口期必须在1-250之间')
        elif strategy == StrategyType.DUAL_MA:
            # 双均线策略参数验证
            if 'short_window' in v and 'long_window' in v:
                if v['short_window'] >= v['long_window']:
                    raise ValueError('短期均线窗口必须小于长期均线窗口')
        
        return v

class StrategyOptimizationRequest(BaseRequest):
    """策略优化请求模型"""
    strategy: StrategyType = Field(..., description="策略类型")
    optimization_target: str = Field(
        "sharpe_ratio", 
        description="优化目标：sharpe_ratio, return, max_drawdown"
    )
    parameter_ranges: Dict[str, List[Union[int, float]]] = Field(
        ..., 
        description="参数优化范围"
    )
    optimization_method: str = Field(
        "grid_search", 
        description="优化方法：grid_search, random_search, bayesian"
    )
    max_iterations: int = Field(100, description="最大迭代次数", ge=1, le=1000)

# ==================== 监控请求模型 ====================

class MonitorRequest(BaseRequest):
    """监控请求模型"""
    metric_types: List[str] = Field(
        ["performance", "cache", "system"], 
        description="监控指标类型"
    )
    time_range: str = Field(
        "1h", 
        description="时间范围：1h, 6h, 1d, 7d, 30d"
    )
    granularity: str = Field(
        "1m", 
        description="数据粒度：1m, 5m, 15m, 1h"
    )

class AlertRequest(BaseRequest):
    """告警请求模型"""
    alert_type: str = Field(..., description="告警类型")
    threshold: float = Field(..., description="告警阈值")
    comparison: str = Field("gt", description="比较方式：gt, lt, eq")
    enabled: bool = Field(True, description="是否启用")

# ==================== 工具请求模型 ====================

class ExportRequest(BaseRequest):
    """数据导出请求模型"""
    data_type: str = Field(..., description="数据类型")
    format: str = Field("json", description="导出格式：json, csv, excel")
    start_date: Optional[str] = Field(None, description="开始日期")
    end_date: Optional[str] = Field(None, description="结束日期")
    filters: Optional[Dict[str, Any]] = Field(None, description="过滤条件")
    include_metadata: bool = Field(True, description="是否包含元数据")

class CacheRequest(BaseRequest):
    """缓存操作请求模型"""
    operation: str = Field(..., description="操作类型：clear, refresh, stats")
    cache_type: Optional[str] = Field(None, description="缓存类型")
    keys: Optional[List[str]] = Field(None, description="缓存键列表")

# ==================== 批量操作请求模型 ====================

class BatchRequest(BaseRequest):
    """批量操作请求模型"""
    operations: List[Dict[str, Any]] = Field(..., description="操作列表")
    parallel: bool = Field(True, description="是否并行执行")
    max_workers: int = Field(4, description="最大工作线程数", ge=1, le=16)
    timeout: int = Field(300, description="超时时间（秒）", ge=1, le=3600)

class TaskRequest(BaseRequest):
    """任务请求模型"""
    task_type: str = Field(..., description="任务类型")
    task_params: Dict[str, Any] = Field(..., description="任务参数")
    priority: int = Field(0, description="任务优先级", ge=0, le=10)
    retry_count: int = Field(3, description="重试次数", ge=0, le=10)
    timeout: int = Field(300, description="任务超时时间（秒）")

# ==================== 配置请求模型 ====================

class ConfigRequest(BaseRequest):
    """配置请求模型"""
    config_type: str = Field(..., description="配置类型")
    config_data: Dict[str, Any] = Field(..., description="配置数据")
    validate_only: bool = Field(False, description="仅验证不保存")

class UserPreferenceRequest(BaseRequest):
    """用户偏好请求模型"""
    preferences: Dict[str, Any] = Field(..., description="用户偏好设置")
    merge_with_existing: bool = Field(True, description="是否与现有设置合并")
