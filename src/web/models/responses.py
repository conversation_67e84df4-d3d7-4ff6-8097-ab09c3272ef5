#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API响应模型定义
- 定义所有API响应的数据结构
- 统一响应格式
- 提供清晰的数据结构
"""

from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field

# ==================== 基础响应模型 ====================

class ApiResponse(BaseModel):
    """统一API响应模型"""
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    error_code: Optional[str] = Field(None, description="错误代码")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")
    request_id: Optional[str] = Field(None, description="请求ID")

class PaginatedResponse(BaseModel):
    """分页响应模型"""
    items: List[Any] = Field(..., description="数据项列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    total_pages: int = Field(..., description="总页数")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")

# ==================== 数据响应模型 ====================

class StockInfo(BaseModel):
    """股票信息模型"""
    ts_code: str = Field(..., description="股票代码")
    symbol: str = Field(..., description="股票简称")
    name: str = Field(..., description="股票名称")
    area: Optional[str] = Field(None, description="地区")
    industry: Optional[str] = Field(None, description="行业")
    market: Optional[str] = Field(None, description="市场")
    list_date: Optional[str] = Field(None, description="上市日期")
    is_hs: Optional[str] = Field(None, description="是否沪深港通标的")

class DailyData(BaseModel):
    """日线数据模型"""
    ts_code: str = Field(..., description="股票代码")
    trade_date: str = Field(..., description="交易日期")
    open: Optional[float] = Field(None, description="开盘价")
    high: Optional[float] = Field(None, description="最高价")
    low: Optional[float] = Field(None, description="最低价")
    close: Optional[float] = Field(None, description="收盘价")
    pre_close: Optional[float] = Field(None, description="昨收价")
    change: Optional[float] = Field(None, description="涨跌额")
    pct_chg: Optional[float] = Field(None, description="涨跌幅")
    vol: Optional[float] = Field(None, description="成交量")
    amount: Optional[float] = Field(None, description="成交额")

class FundamentalData(BaseModel):
    """基本面数据模型"""
    ts_code: str = Field(..., description="股票代码")
    ann_date: Optional[str] = Field(None, description="公告日期")
    end_date: Optional[str] = Field(None, description="报告期")
    report_type: Optional[str] = Field(None, description="报告类型")
    # 财务指标字段（根据具体需求添加）
    total_revenue: Optional[float] = Field(None, description="营业总收入")
    net_profit: Optional[float] = Field(None, description="净利润")
    total_assets: Optional[float] = Field(None, description="总资产")
    total_liab: Optional[float] = Field(None, description="总负债")

class DataFetchResult(BaseModel):
    """数据获取结果模型"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    total_stocks: int = Field(..., description="总股票数")
    completed_stocks: int = Field(..., description="已完成股票数")
    success_count: int = Field(..., description="成功数量")
    error_count: int = Field(..., description="错误数量")
    start_time: datetime = Field(..., description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    duration: Optional[float] = Field(None, description="耗时（秒）")
    data_summary: Optional[Dict[str, Any]] = Field(None, description="数据摘要")

# ==================== 回测响应模型 ====================

class StrategyInfo(BaseModel):
    """策略信息模型"""
    name: str = Field(..., description="策略名称")
    description: str = Field(..., description="策略描述")
    parameters: Dict[str, Any] = Field(..., description="策略参数")
    risk_level: str = Field(..., description="风险等级")
    suitable_market: List[str] = Field(..., description="适用市场")

class BacktestMetrics(BaseModel):
    """回测指标模型"""
    total_return: float = Field(..., description="总收益率")
    annualized_return: float = Field(..., description="年化收益率")
    annualized_volatility: float = Field(..., description="年化波动率")
    sharpe_ratio: float = Field(..., description="夏普比率")
    sortino_ratio: float = Field(..., description="索提诺比率")
    max_drawdown: float = Field(..., description="最大回撤")
    max_drawdown_duration: int = Field(..., description="最大回撤持续期")
    calmar_ratio: float = Field(..., description="卡玛比率")
    win_rate: float = Field(..., description="胜率")
    profit_loss_ratio: float = Field(..., description="盈亏比")

class BacktestResult(BaseModel):
    """回测结果模型"""
    task_id: str = Field(..., description="任务ID")
    strategy_name: str = Field(..., description="策略名称")
    start_date: str = Field(..., description="回测开始日期")
    end_date: str = Field(..., description="回测结束日期")
    initial_capital: float = Field(..., description="初始资金")
    final_capital: float = Field(..., description="最终资金")
    metrics: BacktestMetrics = Field(..., description="回测指标")
    equity_curve: List[Dict[str, Any]] = Field(..., description="权益曲线")
    trades: List[Dict[str, Any]] = Field(..., description="交易记录")
    positions: List[Dict[str, Any]] = Field(..., description="持仓记录")
    benchmark_metrics: Optional[BacktestMetrics] = Field(None, description="基准指标")

# ==================== 监控响应模型 ====================

class SystemStats(BaseModel):
    """系统统计模型"""
    cpu_usage: float = Field(..., description="CPU使用率")
    memory_usage: float = Field(..., description="内存使用率")
    disk_usage: float = Field(..., description="磁盘使用率")
    network_io: Dict[str, float] = Field(..., description="网络IO")
    active_connections: int = Field(..., description="活跃连接数")
    uptime: float = Field(..., description="运行时间（秒）")

class PerformanceMetrics(BaseModel):
    """性能指标模型"""
    api_response_time: float = Field(..., description="API平均响应时间")
    data_fetch_speed: float = Field(..., description="数据获取速度（行/秒）")
    cache_hit_rate: float = Field(..., description="缓存命中率")
    database_query_time: float = Field(..., description="数据库查询时间")
    concurrent_users: int = Field(..., description="并发用户数")
    throughput: float = Field(..., description="吞吐量（请求/秒）")

class CacheMetrics(BaseModel):
    """缓存指标模型"""
    l1_cache_stats: Dict[str, Any] = Field(..., description="L1缓存统计")
    l2_cache_stats: Dict[str, Any] = Field(..., description="L2缓存统计")
    l3_cache_stats: Dict[str, Any] = Field(..., description="L3缓存统计")
    overall_hit_rate: float = Field(..., description="总体命中率")
    cache_size: Dict[str, int] = Field(..., description="缓存大小")
    eviction_count: Dict[str, int] = Field(..., description="淘汰次数")

class AlertInfo(BaseModel):
    """告警信息模型"""
    alert_id: str = Field(..., description="告警ID")
    alert_type: str = Field(..., description="告警类型")
    level: str = Field(..., description="告警级别")
    message: str = Field(..., description="告警消息")
    timestamp: datetime = Field(..., description="告警时间")
    status: str = Field(..., description="告警状态")
    source: str = Field(..., description="告警来源")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")

# ==================== 任务响应模型 ====================

class TaskStatus(BaseModel):
    """任务状态模型"""
    task_id: str = Field(..., description="任务ID")
    task_type: str = Field(..., description="任务类型")
    status: str = Field(..., description="任务状态")
    progress: float = Field(..., description="进度百分比")
    start_time: datetime = Field(..., description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    duration: Optional[float] = Field(None, description="耗时（秒）")
    result: Optional[Any] = Field(None, description="任务结果")
    error_message: Optional[str] = Field(None, description="错误信息")

class BatchResult(BaseModel):
    """批量操作结果模型"""
    batch_id: str = Field(..., description="批次ID")
    total_operations: int = Field(..., description="总操作数")
    successful_operations: int = Field(..., description="成功操作数")
    failed_operations: int = Field(..., description="失败操作数")
    results: List[Dict[str, Any]] = Field(..., description="操作结果列表")
    summary: Dict[str, Any] = Field(..., description="结果摘要")

# ==================== 配置响应模型 ====================

class ConfigInfo(BaseModel):
    """配置信息模型"""
    config_type: str = Field(..., description="配置类型")
    config_data: Dict[str, Any] = Field(..., description="配置数据")
    last_updated: datetime = Field(..., description="最后更新时间")
    version: str = Field(..., description="配置版本")
    is_valid: bool = Field(..., description="配置是否有效")

class ExportResult(BaseModel):
    """导出结果模型"""
    export_id: str = Field(..., description="导出ID")
    file_path: str = Field(..., description="文件路径")
    file_size: int = Field(..., description="文件大小（字节）")
    record_count: int = Field(..., description="记录数量")
    format: str = Field(..., description="文件格式")
    created_time: datetime = Field(..., description="创建时间")
    download_url: Optional[str] = Field(None, description="下载链接")

# ==================== WebSocket响应模型 ====================

class WebSocketMessage(BaseModel):
    """WebSocket消息模型"""
    type: str = Field(..., description="消息类型")
    data: Any = Field(..., description="消息数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    source: str = Field(..., description="消息来源")

class RealTimeData(BaseModel):
    """实时数据模型"""
    data_type: str = Field(..., description="数据类型")
    symbol: Optional[str] = Field(None, description="股票代码")
    values: Dict[str, Any] = Field(..., description="数据值")
    timestamp: datetime = Field(..., description="数据时间戳")
    sequence: int = Field(..., description="序列号")

# ==================== 错误响应模型 ====================

class ErrorDetail(BaseModel):
    """错误详情模型"""
    error_code: str = Field(..., description="错误代码")
    error_type: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    stack_trace: Optional[str] = Field(None, description="堆栈跟踪")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间")

class ValidationError(BaseModel):
    """验证错误模型"""
    field: str = Field(..., description="字段名")
    message: str = Field(..., description="错误消息")
    invalid_value: Any = Field(..., description="无效值")
    constraint: Optional[str] = Field(None, description="约束条件")
