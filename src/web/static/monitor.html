<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化投资平台 - 系统监控</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        
        .status-healthy { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-critical { background-color: #e74c3c; }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .metric:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .metric-label {
            font-weight: 500;
            color: #34495e;
        }
        
        .metric-value {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .progress-low { background-color: #27ae60; }
        .progress-medium { background-color: #f39c12; }
        .progress-high { background-color: #e74c3c; }
        
        .refresh-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px auto;
            display: block;
        }
        
        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .auto-refresh {
            text-align: center;
            color: white;
            margin-top: 20px;
            opacity: 0.8;
        }
        
        .loading {
            text-align: center;
            color: white;
            font-size: 1.1rem;
            margin: 50px 0;
        }
        
        .error {
            background-color: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .dashboard {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 量化投资平台监控中心</h1>
            <p>实时系统性能监控 & 健康状态分析</p>
        </div>
        
        <div id="loading" class="loading">
            📊 正在加载监控数据...
        </div>
        
        <div id="error" class="error" style="display: none;">
            ❌ 数据加载失败，请检查系统状态
        </div>
        
        <div id="dashboard" class="dashboard" style="display: none;">
            <!-- 系统概览 -->
            <div class="card">
                <div class="card-title">
                    <span class="status-indicator" id="overall-status"></span>
                    系统概览
                </div>
                <div class="metric">
                    <span class="metric-label">整体状态</span>
                    <span class="metric-value" id="overall-status-text">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">监控状态</span>
                    <span class="metric-value" id="monitoring-active">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">数据点数</span>
                    <span class="metric-value" id="data-points">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">告警数量</span>
                    <span class="metric-value" id="alert-count">-</span>
                </div>
            </div>
            
            <!-- CPU & 内存 -->
            <div class="card">
                <div class="card-title">💻 CPU & 内存</div>
                <div class="metric">
                    <span class="metric-label">CPU 使用率</span>
                    <span class="metric-value" id="cpu-usage">-</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="cpu-progress"></div>
                </div>
                <div class="metric">
                    <span class="metric-label">内存使用率</span>
                    <span class="metric-value" id="memory-usage">-</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="memory-progress"></div>
                </div>
                <div class="metric">
                    <span class="metric-label">可用内存</span>
                    <span class="metric-value" id="memory-available">-</span>
                </div>
            </div>
            
            <!-- 磁盘 & 网络 -->
            <div class="card">
                <div class="card-title">💾 磁盘 & 网络</div>
                <div class="metric">
                    <span class="metric-label">磁盘使用率</span>
                    <span class="metric-value" id="disk-usage">-</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="disk-progress"></div>
                </div>
                <div class="metric">
                    <span class="metric-label">可用空间</span>
                    <span class="metric-value" id="disk-free">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">进程数量</span>
                    <span class="metric-value" id="process-count">-</span>
                </div>
            </div>
            
            <!-- 数据库状态 -->
            <div class="card">
                <div class="card-title">
                    <span class="status-indicator" id="db-status"></span>
                    🗄️ 数据库状态
                </div>
                <div class="metric">
                    <span class="metric-label">连接状态</span>
                    <span class="metric-value" id="db-status-text">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">活跃连接</span>
                    <span class="metric-value" id="db-connections">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">股票数量</span>
                    <span class="metric-value" id="db-stock-count">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">数据库大小</span>
                    <span class="metric-value" id="db-size">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">查询时间</span>
                    <span class="metric-value" id="db-query-time">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">健康分数</span>
                    <span class="metric-value" id="db-health-score">-</span>
                </div>
            </div>
        </div>
        
        <button class="refresh-btn" onclick="loadMonitorData()">🔄 刷新数据</button>
        
        <div class="auto-refresh">
            ⏱️ 自动刷新：每30秒更新一次
        </div>
    </div>

    <script>
        let autoRefreshInterval;
        
        async function loadMonitorData() {
            try {
                document.getElementById('loading').style.display = 'block';
                document.getElementById('error').style.display = 'none';
                
                const response = await fetch('/api/system/monitor');
                const data = await response.json();
                
                if (data.success) {
                    updateDashboard(data.data);
                    document.getElementById('dashboard').style.display = 'grid';
                } else {
                    throw new Error(data.message || '数据加载失败');
                }
            } catch (error) {
                console.error('加载监控数据失败:', error);
                document.getElementById('error').style.display = 'block';
                document.getElementById('error').textContent = `❌ ${error.message}`;
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }
        
        function updateDashboard(data) {
            const currentStatus = data.current_status;
            const system = currentStatus.system;
            const database = currentStatus.database;
            
            // 更新系统概览
            updateElement('overall-status-text', currentStatus.overall_status || '未知');
            updateStatusIndicator('overall-status', currentStatus.overall_status);
            updateElement('monitoring-active', data.monitoring_active ? '✅ 运行中' : '❌ 已停止');
            updateElement('data-points', data.data_points);
            updateElement('alert-count', data.alert_count);
            
            if (system) {
                // 更新CPU和内存
                updateElement('cpu-usage', `${system.cpu_usage}%`);
                updateProgressBar('cpu-progress', system.cpu_usage);
                updateElement('memory-usage', `${system.memory_usage}%`);
                updateProgressBar('memory-progress', system.memory_usage);
                updateElement('memory-available', `${system.memory_available_gb} GB`);
                
                // 更新磁盘和网络
                updateElement('disk-usage', `${system.disk_usage}%`);
                updateProgressBar('disk-progress', system.disk_usage);
                updateElement('disk-free', `${system.disk_free_gb} GB`);
                updateElement('process-count', system.process_count);
            }
            
            if (database) {
                // 更新数据库状态
                updateElement('db-status-text', database.status);
                updateStatusIndicator('db-status', database.status);
                updateElement('db-connections', database.connections);
                updateElement('db-stock-count', database.stock_count.toLocaleString());
                updateElement('db-size', `${database.data_size_mb} MB`);
                updateElement('db-query-time', `${database.query_time_ms} ms`);
                updateElement('db-health-score', `${database.health_score}/100`);
            }
        }
        
        function updateElement(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        }
        
        function updateStatusIndicator(id, status) {
            const element = document.getElementById(id);
            if (element) {
                element.className = 'status-indicator';
                if (status === 'healthy') {
                    element.classList.add('status-healthy');
                } else if (status === 'warning') {
                    element.classList.add('status-warning');
                } else {
                    element.classList.add('status-critical');
                }
            }
        }
        
        function updateProgressBar(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.style.width = `${Math.min(value, 100)}%`;
                element.className = 'progress-fill';
                if (value < 60) {
                    element.classList.add('progress-low');
                } else if (value < 80) {
                    element.classList.add('progress-medium');
                } else {
                    element.classList.add('progress-high');
                }
            }
        }
        
        function startAutoRefresh() {
            autoRefreshInterval = setInterval(loadMonitorData, 30000); // 30秒刷新
        }
        
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        }
        
        // 页面加载时启动
        document.addEventListener('DOMContentLoaded', function() {
            loadMonitorData();
            startAutoRefresh();
        });
        
        // 页面隐藏时停止自动刷新，显示时恢复
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                stopAutoRefresh();
            } else {
                startAutoRefresh();
            }
        });
    </script>
</body>
</html>
