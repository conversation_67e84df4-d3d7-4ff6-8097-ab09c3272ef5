<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化投资平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            width: 90%;
            text-align: center;
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 40px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .feature h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .feature p {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .links {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .link {
            display: inline-block;
            padding: 12px 24px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .link:hover {
            background: #5a67d8;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .link.secondary {
            background: #48bb78;
        }
        
        .link.secondary:hover {
            background: #38a169;
            box-shadow: 0 5px 15px rgba(72, 187, 120, 0.4);
        }
        
        .status {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: #d4edda;
            color: #155724;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-bottom: 20px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            background: #28a745;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        @media (max-width: 600px) {
            .container {
                padding: 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀</div>
        <h1>量化投资平台</h1>
        <p class="subtitle">世界级量化投资平台Web界面</p>
        
        <div class="status">
            <div class="status-dot"></div>
            FastAPI后端服务运行中
        </div>
        
        <div class="stats">
            <div class="stat">
                <div class="stat-number">280万+</div>
                <div class="stat-label">数据记录</div>
            </div>
            <div class="stat">
                <div class="stat-number">5421</div>
                <div class="stat-label">股票覆盖</div>
            </div>
            <div class="stat">
                <div class="stat-number">99.99%</div>
                <div class="stat-label">数据质量</div>
            </div>
            <div class="stat">
                <div class="stat-number">6+</div>
                <div class="stat-label">策略类型</div>
            </div>
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>📊 数据获取</h3>
                <p>高性能数据获取系统，支持多级缓存和异步处理</p>
            </div>
            <div class="feature">
                <h3>🎯 策略回测</h3>
                <p>多种量化策略回测，包含技术分析和机器学习</p>
            </div>
            <div class="feature">
                <h3>📈 性能监控</h3>
                <p>实时性能监控和智能告警系统</p>
            </div>
            <div class="feature">
                <h3>🔄 实时数据</h3>
                <p>WebSocket实时数据推送和交互式图表</p>
            </div>
        </div>
        
        <div class="links">
            <a href="/docs" class="link">📚 API文档</a>
            <a href="/redoc" class="link secondary">📖 ReDoc文档</a>
            <a href="/api/v1/system/info" class="link">ℹ️ 系统信息</a>
            <a href="/distributed-dashboard.html" class="link" style="background: #e53e3e;">🗄️ 分布式数据库监控</a>
            <a href="/data-explorer.html" class="link" style="background: #6f42c1;">🔍 数据查询器</a>
        </div>
    </div>
    
    <script>
        // 简单的状态检查
        fetch('/api/health')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('✅ API服务正常运行');
                }
            })
            .catch(error => {
                console.error('❌ API服务连接失败:', error);
                document.querySelector('.status').innerHTML = 
                    '<div style="width: 8px; height: 8px; background: #dc3545; border-radius: 50%;"></div>API服务连接失败';
                document.querySelector('.status').style.background = '#f8d7da';
                document.querySelector('.status').style.color = '#721c24';
            });
    </script>
</body>
</html>
