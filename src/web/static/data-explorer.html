<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据查询器 - 量化投资平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .query-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .result-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-height: 600px;
            overflow-y: auto;
        }
        .data-table {
            font-size: 0.9rem;
        }
        .data-table th {
            background-color: #f8f9fa;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 40px;
        }
        .query-stats {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 0.9rem;
        }
        .nav-pills .nav-link.active {
            background-color: #007bff;
        }
        .code-editor {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <h1 class="h3 mb-0">
                    <i class="fas fa-search text-primary"></i>
                    分布式数据库查询器
                </h1>
                <p class="text-muted">查询和浏览分布式数据库中的数据</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="/distributed-dashboard.html" class="btn btn-outline-secondary">
                    <i class="fas fa-chart-line"></i> 返回监控仪表板
                </a>
                <a href="/" class="btn btn-outline-primary">
                    <i class="fas fa-home"></i> 首页
                </a>
            </div>
        </div>

        <!-- 查询面板 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="query-panel">
                    <ul class="nav nav-pills mb-3" id="queryTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="stocks-tab" data-bs-toggle="pill" data-bs-target="#stocks" type="button" role="tab">
                                <i class="fas fa-building"></i> 股票数据
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="daily-tab" data-bs-toggle="pill" data-bs-target="#daily" type="button" role="tab">
                                <i class="fas fa-chart-bar"></i> 日线数据
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="custom-tab" data-bs-toggle="pill" data-bs-target="#custom" type="button" role="tab">
                                <i class="fas fa-code"></i> 自定义查询
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" id="queryTabsContent">
                        <!-- 股票数据查询 -->
                        <div class="tab-pane fade show active" id="stocks" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">查询数量</label>
                                    <select id="stockLimit" class="form-select">
                                        <option value="10">10条</option>
                                        <option value="50" selected>50条</option>
                                        <option value="100">100条</option>
                                        <option value="500">500条</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">操作</label>
                                    <div>
                                        <button class="btn btn-primary" onclick="queryStocks()">
                                            <i class="fas fa-search"></i> 查询股票列表
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 日线数据查询 -->
                        <div class="tab-pane fade" id="daily" role="tabpanel">
                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">股票代码（可选）</label>
                                    <input type="text" id="stockCode" class="form-control" placeholder="如：000001.SZ">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">查询数量</label>
                                    <select id="dailyLimit" class="form-select">
                                        <option value="10">10条</option>
                                        <option value="50" selected>50条</option>
                                        <option value="100">100条</option>
                                        <option value="500">500条</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">天数范围</label>
                                    <select id="dayRange" class="form-select">
                                        <option value="7">7天</option>
                                        <option value="30" selected>30天</option>
                                        <option value="90">90天</option>
                                        <option value="365">365天</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">操作</label>
                                    <div>
                                        <button class="btn btn-primary" onclick="queryDaily()">
                                            <i class="fas fa-search"></i> 查询
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 自定义查询 -->
                        <div class="tab-pane fade" id="custom" role="tabpanel">
                            <div class="row">
                                <div class="col-md-8">
                                    <label class="form-label">表名</label>
                                    <select id="tableName" class="form-select mb-3">
                                        <option value="stock_basic">stock_basic - 股票基本信息</option>
                                        <option value="daily">daily - 日线数据</option>
                                        <option value="daily_basic">daily_basic - 每日基本面数据</option>
                                        <option value="weekly">weekly - 周线数据</option>
                                        <option value="monthly">monthly - 月线数据</option>
                                    </select>
                                    <label class="form-label">查询条件（JSON格式，可选）</label>
                                    <textarea id="queryConditions" class="form-control code-editor" rows="3" placeholder='{"ts_code": "000001.SZ"}'></textarea>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">查询列（可选）</label>
                                    <input type="text" id="queryColumns" class="form-control mb-3" placeholder="用逗号分隔，如：ts_code,name">
                                    <label class="form-label">限制条数</label>
                                    <input type="number" id="queryLimit" class="form-control mb-3" value="50" min="1" max="1000">
                                    <button class="btn btn-primary w-100" onclick="queryCustom()">
                                        <i class="fas fa-search"></i> 执行查询
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 结果面板 -->
        <div class="row">
            <div class="col-12">
                <div class="result-panel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="fas fa-table"></i>
                            查询结果
                        </h5>
                        <div>
                            <button id="exportBtn" class="btn btn-outline-secondary btn-sm" onclick="exportResults()" style="display: none;">
                                <i class="fas fa-download"></i> 导出CSV
                            </button>
                        </div>
                    </div>

                    <div id="queryStats" class="query-stats" style="display: none;">
                        <!-- 查询统计信息 -->
                    </div>

                    <div id="loadingSpinner" class="loading-spinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">正在查询数据...</p>
                    </div>

                    <div id="resultsContainer">
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-search fa-3x mb-3"></i>
                            <p>请选择查询条件并点击查询按钮</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentResults = null;

        // 查询股票列表
        async function queryStocks() {
            const limit = document.getElementById('stockLimit').value;
            
            showLoading();
            
            try {
                const response = await fetch(`/api/v2/data/stocks?limit=${limit}`);
                const data = await response.json();
                
                if (data.success) {
                    displayResults(data.data, 'stocks');
                    showStats(`查询成功，共 ${data.data.length} 条股票记录`);
                } else {
                    showError('查询失败：' + data.message);
                }
            } catch (error) {
                showError('网络错误：' + error.message);
            } finally {
                hideLoading();
            }
        }

        // 查询日线数据
        async function queryDaily() {
            const stockCode = document.getElementById('stockCode').value.trim();
            const limit = document.getElementById('dailyLimit').value;
            const days = document.getElementById('dayRange').value;
            
            showLoading();
            
            try {
                let url = `/api/v2/data/daily?limit=${limit}&days=${days}`;
                if (stockCode) {
                    url += `&ts_code=${encodeURIComponent(stockCode)}`;
                }
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.success) {
                    displayResults(data.data, 'daily');
                    showStats(`查询成功，共 ${data.data.length} 条日线记录`);
                } else {
                    showError('查询失败：' + data.message);
                }
            } catch (error) {
                showError('网络错误：' + error.message);
            } finally {
                hideLoading();
            }
        }

        // 自定义查询
        async function queryCustom() {
            const tableName = document.getElementById('tableName').value;
            const conditionsText = document.getElementById('queryConditions').value.trim();
            const columnsText = document.getElementById('queryColumns').value.trim();
            const limit = document.getElementById('queryLimit').value;
            
            showLoading();
            
            try {
                const requestBody = {
                    table_name: tableName,
                    limit: parseInt(limit)
                };
                
                if (conditionsText) {
                    try {
                        requestBody.conditions = JSON.parse(conditionsText);
                    } catch (e) {
                        showError('查询条件JSON格式错误');
                        hideLoading();
                        return;
                    }
                }
                
                if (columnsText) {
                    requestBody.columns = columnsText.split(',').map(col => col.trim());
                }
                
                const response = await fetch('/api/v2/data/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    displayResults(data.data, 'custom');
                    showStats(`查询成功，表：${tableName}，共 ${data.data.length} 条记录`);
                } else {
                    showError('查询失败：' + data.message);
                }
            } catch (error) {
                showError('网络错误：' + error.message);
            } finally {
                hideLoading();
            }
        }

        // 显示结果
        function displayResults(data, type) {
            currentResults = data;
            const container = document.getElementById('resultsContainer');
            
            if (!data || data.length === 0) {
                container.innerHTML = '<div class="text-center text-muted py-5"><p>没有找到匹配的数据</p></div>';
                document.getElementById('exportBtn').style.display = 'none';
                return;
            }
            
            // 创建表格
            const table = document.createElement('table');
            table.className = 'table table-striped table-hover data-table';
            
            // 创建表头
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');
            
            const columns = Object.keys(data[0]);
            columns.forEach(col => {
                const th = document.createElement('th');
                th.textContent = col;
                headerRow.appendChild(th);
            });
            
            thead.appendChild(headerRow);
            table.appendChild(thead);
            
            // 创建表体
            const tbody = document.createElement('tbody');
            data.forEach(row => {
                const tr = document.createElement('tr');
                columns.forEach(col => {
                    const td = document.createElement('td');
                    td.textContent = row[col] || '';
                    tr.appendChild(td);
                });
                tbody.appendChild(tr);
            });
            
            table.appendChild(tbody);
            container.innerHTML = '';
            container.appendChild(table);
            
            document.getElementById('exportBtn').style.display = 'inline-block';
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('resultsContainer').style.display = 'none';
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';
        }

        // 显示统计信息
        function showStats(message) {
            const statsDiv = document.getElementById('queryStats');
            statsDiv.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;
            statsDiv.style.display = 'block';
        }

        // 显示错误信息
        function showError(message) {
            const container = document.getElementById('resultsContainer');
            container.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    ${message}
                </div>
            `;
            document.getElementById('exportBtn').style.display = 'none';
        }

        // 导出结果
        function exportResults() {
            if (!currentResults || currentResults.length === 0) {
                alert('没有数据可导出');
                return;
            }
            
            const columns = Object.keys(currentResults[0]);
            let csvContent = columns.join(',') + '\n';
            
            currentResults.forEach(row => {
                const values = columns.map(col => {
                    const value = row[col] || '';
                    return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
                });
                csvContent += values.join(',') + '\n';
            });
            
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `query-results-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>
