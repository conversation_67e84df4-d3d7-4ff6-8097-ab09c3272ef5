#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回测服务模块
- 封装策略回测逻辑
- 提供异步回测操作接口
- 集成现有回测功能
"""

import asyncio
import uuid
import logging
import argparse
import sys
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
import json
import pandas as pd
import numpy as np
import hashlib
import pickle

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../.."))

from src.utils.logging.logger_factory import get_logger
from src.commands.backtest_command import run_backtest_command
from src.web.models.requests import BacktestRequest
from src.web.models.responses import StrategyInfo, BacktestResult, BacktestMetrics

# 导入现有策略
from src.strategies.moving_average_strategy import MovingAverageStrategy
from src.strategies.momentum_strategy import MomentumStrategy
from src.strategies.value_investment_strategy import ValueInvestmentStrategy
from src.strategies.enhanced_multi_factor_strategy import EnhancedMultiFactorStrategy
from src.data.fetcher.hybrid_data_fetcher import HybridDataFetcher

# 导入缓存
from src.data.storage.cache.memory_cache import MemoryCache

logger = get_logger(__name__)

class BacktestService:
    """回测服务类"""

    # 类级别的数据获取器缓存
    _shared_data_fetcher = None
    _fetcher_lock = asyncio.Lock()
    _cache_instance = None

    def __init__(self):
        """初始化回测服务"""
        self.active_tasks: Dict[str, Dict[str, Any]] = {}

        # 初始化缓存
        if BacktestService._cache_instance is None:
            BacktestService._cache_instance = MemoryCache(
                max_size=1000,  # 最大缓存1000个条目
                default_expires=3600  # 默认1小时过期
            )
        self.cache = BacktestService._cache_instance
        self.available_strategies = {
            "moving_average": {
                "name": "移动平均线策略",
                "description": "基于移动平均线的趋势跟踪策略",
                "parameters": {
                    "short_window": {"type": "int", "default": 5, "range": [3, 50]},
                    "long_window": {"type": "int", "default": 20, "range": [10, 250]}
                },
                "risk_level": "中等",
                "suitable_market": ["牛市", "震荡市"],
                "class": MovingAverageStrategy
            },
            "momentum": {
                "name": "动量策略",
                "description": "基于价格动量的趋势跟踪策略",
                "parameters": {
                    "lookback_period": {"type": "int", "default": 20, "range": [5, 60]},
                    "holding_period": {"type": "int", "default": 5, "range": [1, 20]},
                    "top_n": {"type": "int", "default": 10, "range": [5, 50]}
                },
                "risk_level": "高",
                "suitable_market": ["趋势市"],
                "class": MomentumStrategy
            },
            "value_investment": {
                "name": "价值投资策略",
                "description": "基于财务指标筛选低估值、高质量的股票",
                "parameters": {
                    "pe_max": {"type": "float", "default": 15, "range": [5, 30]},
                    "pb_max": {"type": "float", "default": 2, "range": [0.5, 5]},
                    "roe_min": {"type": "float", "default": 0.15, "range": [0.05, 0.3]},
                    "stock_num": {"type": "int", "default": 20, "range": [10, 50]}
                },
                "risk_level": "低",
                "suitable_market": ["熊市", "震荡市"],
                "class": ValueInvestmentStrategy
            },
            "multi_factor": {
                "name": "多因子策略",
                "description": "基于多个因子的综合选股策略",
                "parameters": {
                    "value_weight": {"type": "float", "default": 0.3, "range": [0, 1]},
                    "quality_weight": {"type": "float", "default": 0.25, "range": [0, 1]},
                    "growth_weight": {"type": "float", "default": 0.25, "range": [0, 1]},
                    "momentum_weight": {"type": "float", "default": 0.2, "range": [0, 1]},
                    "stock_num": {"type": "int", "default": 30, "range": [10, 100]}
                },
                "risk_level": "中等",
                "suitable_market": ["全市场"],
                "class": EnhancedMultiFactorStrategy
            }
        }

        # 使用共享的数据获取器实例，提高性能
        self.data_fetcher = self._get_shared_data_fetcher()

    @classmethod
    def _get_shared_data_fetcher(cls):
        """
        获取共享的数据获取器实例（单例模式）

        返回:
            HybridDataFetcher: 数据获取器实例
        """
        if cls._shared_data_fetcher is None:
            try:
                # {{ AURA-X: Modify - 使用DataFetcherManager统一管理，消除直接实例化. Approval: 寸止(ID:架构一致性修复). }}
                from src.data.fetcher.data_fetcher_manager import get_data_fetcher

                # 使用DataFetcherManager获取实例，确保配置统一
                cls._shared_data_fetcher = get_data_fetcher({
                    'use_cache': True,
                    'auto_async': True
                })
                logger.info(f"共享数据获取器初始化成功（使用DataFetcherManager）")
            except Exception as e:
                logger.warning(f"共享数据获取器初始化失败: {e}")
                # 备用初始化方式
                from src.data.fetcher.data_fetcher_manager import get_data_fetcher
                cls._shared_data_fetcher = get_data_fetcher()

        return cls._shared_data_fetcher

    def _generate_cache_key(self, prefix: str, **kwargs) -> str:
        """
        生成缓存键

        参数:
            prefix: 缓存键前缀
            **kwargs: 用于生成键的参数

        返回:
            str: 缓存键
        """
        # 将参数转换为字符串并排序，确保一致性
        params_str = "&".join(f"{k}={v}" for k, v in sorted(kwargs.items()))

        # 生成MD5哈希
        hash_obj = hashlib.md5(params_str.encode('utf-8'))
        hash_str = hash_obj.hexdigest()[:16]  # 取前16位

        return f"{prefix}:{hash_str}"
        logger.info("✅ 回测服务初始化完成")
    
    async def get_available_strategies(self) -> List[Dict[str, Any]]:
        """
        获取可用策略列表
        
        返回:
            List[Dict]: 策略信息列表
        """
        try:
            strategies = []
            for strategy_key, strategy_info in self.available_strategies.items():
                strategy_data = {
                    "key": strategy_key,
                    "name": strategy_info["name"],
                    "description": strategy_info["description"],
                    "parameters": strategy_info["parameters"],
                    "risk_level": strategy_info["risk_level"],
                    "suitable_market": strategy_info["suitable_market"]
                }
                strategies.append(strategy_data)
            
            logger.info(f"成功获取{len(strategies)}个可用策略")
            return strategies
            
        except Exception as e:
            logger.error(f"获取策略列表失败: {e}")
            raise

    async def start_backtest_task(self, request: BacktestRequest) -> str:
        """
        启动回测任务
        
        参数:
            request: 回测请求
            
        返回:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())
        
        # 创建任务记录
        task_info = {
            "task_id": task_id,
            "status": "pending",
            "request": request.dict(),
            "start_time": datetime.now(),
            "progress": 0.0,
            "stage": "初始化",
            "result": None,
            "error_message": None
        }
        
        self.active_tasks[task_id] = task_info
        
        # 启动后台任务
        asyncio.create_task(self._execute_backtest_task(task_id, request))
        
        logger.info(f"回测任务已启动: {task_id}, 策略: {request.strategy}")
        return task_id

    async def _execute_backtest_task(self, task_id: str, request: BacktestRequest):
        """
        执行回测任务

        参数:
            task_id: 任务ID
            request: 回测请求
        """
        task_info = self.active_tasks[task_id]

        try:
            # 更新任务状态
            task_info["status"] = "running"
            task_info["start_time"] = datetime.now()
            task_info["stage"] = "准备回测参数"
            task_info["progress"] = 10.0

            # 获取策略配置
            strategy_key = request.strategy.value if hasattr(request.strategy, 'value') else request.strategy
            if strategy_key not in self.available_strategies:
                raise ValueError(f"不支持的策略: {strategy_key}")

            strategy_config = self.available_strategies[strategy_key]

            # 更新进度
            task_info["stage"] = "获取数据"
            task_info["progress"] = 20.0

            # 获取回测数据
            data = await self._get_backtest_data(request)

            if data.empty:
                raise ValueError("未获取到有效的回测数据")

            # 更新进度
            task_info["stage"] = "初始化策略"
            task_info["progress"] = 30.0

            # 创建策略实例
            strategy = await self._create_strategy_instance(strategy_config, request.parameters)

            # 更新进度
            task_info["stage"] = "执行回测"
            task_info["progress"] = 40.0

            # 执行回测
            backtest_result = await self._run_strategy_backtest(strategy, data, request)

            # 更新进度
            task_info["stage"] = "处理回测结果"
            task_info["progress"] = 80.0

            # 格式化回测结果
            formatted_result = await self._format_backtest_result(task_id, backtest_result, request)

            # 更新任务完成状态
            task_info["status"] = "completed"
            task_info["end_time"] = datetime.now()
            task_info["duration"] = (task_info["end_time"] - task_info["start_time"]).total_seconds()
            task_info["result"] = formatted_result
            task_info["progress"] = 100.0
            task_info["stage"] = "完成"

            logger.info(f"回测任务完成: {task_id}")

        except Exception as e:
            # 更新任务失败状态
            task_info["status"] = "failed"
            task_info["end_time"] = datetime.now()
            task_info["error_message"] = str(e)
            task_info["progress"] = 0.0
            task_info["stage"] = "失败"

            logger.error(f"回测任务失败: {task_id}, 错误: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")

    async def _load_backtest_result(self, task_id: str) -> Dict[str, Any]:
        """
        加载回测结果

        参数:
            task_id: 任务ID

        返回:
            Dict: 回测结果
        """
        try:
            # 从输出文件读取回测结果
            import os
            from src.utils.temporary.path_utils import get_output_path
            result_file = get_output_path('backtest_result.json')

            if not os.path.exists(result_file):
                raise FileNotFoundError("回测结果文件不存在")

            with open(result_file, 'r', encoding='utf-8') as f:
                result_data = json.load(f)

            # 转换为标准格式
            backtest_result = {
                "task_id": task_id,
                "strategy_name": result_data.get("strategy_name", "未知策略"),
                "start_date": result_data.get("start_date", ""),
                "end_date": result_data.get("end_date", ""),
                "initial_capital": result_data.get("initial_capital", 0),
                "final_capital": result_data.get("final_capital", 0),
                "metrics": self._extract_metrics(result_data),
                "equity_curve": result_data.get("equity_curve", []),
                "trades": result_data.get("trades", []),
                "positions": result_data.get("positions", []),
                "benchmark_metrics": result_data.get("benchmark_metrics")
            }

            return backtest_result

        except Exception as e:
            logger.error(f"加载回测结果失败: {e}")
            # 返回默认结果
            return {
                "task_id": task_id,
                "strategy_name": "未知策略",
                "error": str(e)
            }

    def _extract_metrics(self, result_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取回测指标
        
        参数:
            result_data: 原始回测数据
            
        返回:
            Dict: 标准化的回测指标
        """
        metrics = result_data.get("metrics", {})
        
        return {
            "total_return": metrics.get("total_return", 0.0),
            "annualized_return": metrics.get("annualized_return", 0.0),
            "annualized_volatility": metrics.get("annualized_volatility", 0.0),
            "sharpe_ratio": metrics.get("sharpe_ratio", 0.0),
            "sortino_ratio": metrics.get("sortino_ratio", 0.0),
            "max_drawdown": metrics.get("max_drawdown", 0.0),
            "max_drawdown_duration": metrics.get("max_drawdown_duration", 0),
            "calmar_ratio": metrics.get("calmar_ratio", 0.0),
            "win_rate": metrics.get("win_rate", 0.0),
            "profit_loss_ratio": metrics.get("profit_loss_ratio", 0.0)
        }

    async def get_backtest_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取回测任务状态
        
        参数:
            task_id: 任务ID
            
        返回:
            Dict: 任务状态信息
        """
        if task_id not in self.active_tasks:
            raise ValueError(f"任务不存在: {task_id}")
        
        task_info = self.active_tasks[task_id]
        
        # 构建状态响应
        status_info = {
            "task_id": task_id,
            "status": task_info["status"],
            "progress": task_info["progress"],
            "stage": task_info["stage"],
            "start_time": task_info["start_time"].isoformat(),
            "end_time": task_info.get("end_time").isoformat() if task_info.get("end_time") else None,
            "duration": task_info.get("duration"),
            "error_message": task_info.get("error_message")
        }
        
        return status_info

    async def get_backtest_results(self, task_id: str) -> Dict[str, Any]:
        """
        获取回测结果
        
        参数:
            task_id: 任务ID
            
        返回:
            Dict: 回测结果
        """
        if task_id not in self.active_tasks:
            raise ValueError(f"任务不存在: {task_id}")
        
        task_info = self.active_tasks[task_id]
        
        if task_info["status"] != "completed":
            raise ValueError(f"任务尚未完成: {task_info['status']}")
        
        return task_info["result"]

    async def monitor_backtest_task(self, task_id: str):
        """
        监控回测任务
        
        参数:
            task_id: 任务ID
        """
        logger.info(f"开始监控回测任务: {task_id}")
        
        while task_id in self.active_tasks:
            task_info = self.active_tasks[task_id]
            
            if task_info["status"] in ["completed", "failed"]:
                break
            
            # 每10秒检查一次任务状态
            await asyncio.sleep(10)
            
            # 可以在这里发送进度更新通知
            logger.debug(f"回测任务{task_id}进度: {task_info['progress']:.1f}%, 阶段: {task_info['stage']}")
        
        logger.info(f"回测任务监控结束: {task_id}")

    async def cancel_backtest_task(self, task_id: str) -> bool:
        """
        取消回测任务
        
        参数:
            task_id: 任务ID
            
        返回:
            bool: 是否成功取消
        """
        if task_id not in self.active_tasks:
            return False
        
        task_info = self.active_tasks[task_id]
        
        if task_info["status"] in ["completed", "failed"]:
            return False
        
        # 更新任务状态为取消
        task_info["status"] = "cancelled"
        task_info["end_time"] = datetime.now()
        task_info["stage"] = "已取消"
        
        logger.info(f"回测任务已取消: {task_id}")
        return True

    async def _get_backtest_data(self, request: BacktestRequest) -> pd.DataFrame:
        """
        获取回测数据（支持真实数据和模拟数据，支持缓存）

        参数:
            request: 回测请求

        返回:
            pd.DataFrame: 回测数据

        异常:
            ValueError: 数据获取失败时抛出详细错误信息
        """
        # 生成缓存键
        cache_key = self._generate_cache_key(
            "backtest_data",
            stock_codes=request.stock_codes,
            start_date=request.start_date,
            end_date=request.end_date
        )

        # 尝试从缓存获取
        cached_data = self.cache.get(cache_key)
        if cached_data is not None:
            logger.info(f"从缓存获取回测数据: {len(cached_data)} 条记录")
            return pd.DataFrame(cached_data)

        try:
            if request.stock_codes:
                # 使用批量并发获取提高性能
                try:
                    # 批量获取所有股票数据
                    stock_data = await asyncio.to_thread(
                        self.data_fetcher.fetch_market_data,
                        symbols=request.stock_codes,  # 传入整个列表
                        data_type='daily',
                        start_date=request.start_date,
                        end_date=request.end_date
                    )

                    if stock_data is not None and not stock_data.empty:
                        # 转换为字典列表格式
                        all_data = stock_data.to_dict('records')
                        failed_stocks = []
                    else:
                        all_data = []
                        failed_stocks = request.stock_codes

                except Exception as batch_error:
                    logger.warning(f"批量获取失败，尝试逐个获取: {batch_error}")
                    # 批量获取失败时，回退到逐个获取
                    all_data = []
                    failed_stocks = []

                    for stock_code in request.stock_codes:
                        try:
                            stock_data = await asyncio.to_thread(
                                self.data_fetcher.fetch_market_data,
                                symbols=stock_code,
                                data_type='daily',
                                start_date=request.start_date,
                                end_date=request.end_date
                            )
                            if stock_data is not None and not stock_data.empty:
                                data_records = stock_data.to_dict('records')
                                all_data.extend(data_records)
                            else:
                                failed_stocks.append(stock_code)
                        except Exception as stock_error:
                            logger.error(f"获取股票 {stock_code} 数据失败: {stock_error}")
                            failed_stocks.append(stock_code)

                if not all_data:
                    if failed_stocks:
                        raise ValueError(f"无法获取指定股票数据: {', '.join(failed_stocks)}。请检查：1) Tushare token是否正确配置 2) 股票代码是否有效 3) 网络连接是否正常")
                    else:
                        raise ValueError("未获取到任何股票数据。请检查日期范围和股票代码是否正确。")

                if failed_stocks:
                    logger.warning(f"部分股票数据获取失败: {', '.join(failed_stocks)}")

                # 缓存成功获取的数据
                if all_data:
                    self.cache.set(cache_key, all_data, expires=1800)  # 缓存30分钟
                    logger.debug(f"数据已缓存: {cache_key}")

                return pd.DataFrame(all_data)
            else:
                # 获取全市场数据（使用默认股票列表）
                default_stocks = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ']

                all_data = []
                failed_stocks = []

                for stock_code in default_stocks:
                    try:
                        stock_data = await asyncio.to_thread(
                            self.data_fetcher.fetch_market_data,
                            symbols=stock_code,
                            data_type='daily',
                            start_date=request.start_date,
                            end_date=request.end_date
                        )
                        if stock_data is not None and not stock_data.empty:
                            data_records = stock_data.to_dict('records')
                            all_data.extend(data_records)
                        else:
                            failed_stocks.append(stock_code)
                    except Exception as stock_error:
                        logger.error(f"获取股票 {stock_code} 数据失败: {stock_error}")
                        failed_stocks.append(stock_code)

                if not all_data:
                    raise ValueError("无法获取市场数据。请检查：1) Tushare token是否正确配置 2) 日期范围是否有效 3) 数据源服务是否可用")

                if failed_stocks:
                    logger.warning(f"部分默认股票数据获取失败: {', '.join(failed_stocks)}")

                return pd.DataFrame(all_data)

        except ValueError:
            # 重新抛出业务逻辑错误
            raise
        except Exception as e:
            logger.error(f"数据获取过程中发生未知错误: {e}")
            # 根据错误类型提供具体的解决建议
            if "token" in str(e).lower():
                raise ValueError("Tushare token未配置或无效。请在系统配置中设置正确的Tushare token。")
            elif "network" in str(e).lower() or "connection" in str(e).lower():
                raise ValueError("网络连接失败。请检查网络连接并重试。")
            else:
                raise ValueError(f"数据获取失败: {str(e)}。请联系系统管理员或稍后重试。")



    async def _create_strategy_instance(self, strategy_config: Dict[str, Any], parameters: Dict[str, Any]):
        """
        创建策略实例

        参数:
            strategy_config: 策略配置
            parameters: 策略参数

        返回:
            策略实例
        """
        try:
            strategy_class = strategy_config["class"]

            # 合并默认参数和用户参数
            final_params = {}
            for param_name, param_config in strategy_config["parameters"].items():
                if param_name in parameters:
                    final_params[param_name] = parameters[param_name]
                else:
                    final_params[param_name] = param_config["default"]

            # 创建策略实例
            strategy = strategy_class(**final_params)

            logger.info(f"策略实例创建成功: {strategy_config['name']}")
            return strategy

        except Exception as e:
            logger.error(f"创建策略实例失败: {e}")
            raise

    async def _run_strategy_backtest(self, strategy, data: pd.DataFrame, request: BacktestRequest) -> Dict[str, Any]:
        """
        运行策略回测

        参数:
            strategy: 策略实例
            data: 回测数据
            request: 回测请求

        返回:
            Dict: 回测结果
        """
        try:
            # 获取基准指数代码
            benchmark = getattr(request, 'benchmark', '000300.SH')

            # 异步执行回测
            result = await asyncio.to_thread(
                strategy.backtest,
                data=data,
                start_date=request.start_date,
                end_date=request.end_date,
                initial_capital=request.initial_capital,
                benchmark=benchmark
            )

            logger.info("策略回测执行成功")
            return result

        except Exception as e:
            logger.error(f"策略回测执行失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            raise



    async def _format_backtest_result(self, task_id: str, result: Dict[str, Any], request: BacktestRequest) -> Dict[str, Any]:
        """
        格式化回测结果

        参数:
            task_id: 任务ID
            result: 原始回测结果
            request: 回测请求

        返回:
            Dict: 格式化的回测结果
        """
        try:
            # 获取策略名称
            strategy_key = request.strategy.value if hasattr(request.strategy, 'value') else request.strategy
            strategy_name = self.available_strategies[strategy_key]["name"]

            # 格式化结果
            formatted_result = {
                "task_id": task_id,
                "strategy_name": strategy_name,
                "start_date": request.start_date,
                "end_date": request.end_date,
                "initial_capital": request.initial_capital,
                "final_capital": result.get("final_capital", request.initial_capital),
                "metrics": {
                    "total_return": result.get("total_return", 0.0),
                    "annualized_return": result.get("annual_return", 0.0),
                    "annualized_volatility": result.get("volatility", 0.0),
                    "sharpe_ratio": result.get("sharpe_ratio", 0.0),
                    "sortino_ratio": result.get("sortino_ratio", 0.0),
                    "max_drawdown": result.get("max_drawdown", 0.0),
                    "max_drawdown_duration": result.get("max_drawdown_duration", 0),
                    "calmar_ratio": result.get("calmar_ratio", 0.0),
                    "win_rate": result.get("win_rate", 0.0),
                    "profit_loss_ratio": result.get("profit_loss_ratio", 0.0)
                },
                "equity_curve": self._convert_to_list(result.get("equity_curve", [])),
                "trades": self._convert_to_list(result.get("trades", [])),
                "positions": self._convert_to_list(result.get("positions", [])),
                "benchmark_metrics": result.get("benchmark_metrics")
            }

            return formatted_result

        except Exception as e:
            logger.error(f"格式化回测结果失败: {e}")
            # 返回基本结果
            return {
                "task_id": task_id,
                "strategy_name": "未知策略",
                "error": str(e)
            }

    def _convert_to_list(self, data):
        """
        将数据转换为可序列化的列表格式

        参数:
            data: 待转换的数据

        返回:
            list: 转换后的列表
        """
        try:
            if isinstance(data, pd.DataFrame):
                return data.to_dict('records')
            elif isinstance(data, pd.Series):
                return data.tolist()
            elif isinstance(data, list):
                return data
            else:
                return []
        except Exception as e:
            logger.warning(f"数据转换失败: {e}")
            return []
