#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据服务模块
- 封装数据获取和处理逻辑
- 提供异步数据操作接口
- 集成现有数据获取器
"""

import asyncio
import uuid
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

from src.utils.logging.logger_factory import get_logger
from src.data.fetcher.data_fetcher_manager import get_data_fetcher
from src.web.models.requests import DataFetchRequest, StockQueryRequest
from src.web.models.responses import StockInfo, DailyData, DataFetchResult

logger = get_logger(__name__)

class DataService:
    """数据服务类"""
    
    def __init__(self):
        """初始化数据服务"""
        # {{ AURA-X: Modify - 使用DataFetcherManager统一管理，消除直接实例化. Approval: 寸止(ID:架构一致性修复). }}
        self.data_fetcher = get_data_fetcher()
        self.active_tasks: Dict[str, Dict[str, Any]] = {}
        logger.info("✅ 数据服务初始化完成")
    
    async def get_stock_list(
        self, 
        limit: int = 100,
        market: Optional[str] = None,
        industry: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        获取股票列表
        
        参数:
            limit: 返回数量限制
            market: 市场筛选
            industry: 行业筛选
            
        返回:
            List[Dict]: 股票信息列表
        """
        try:
            # 异步调用现有数据获取器
            stocks_data = await asyncio.to_thread(
                self.data_fetcher.get_stock_list,
                limit=limit
            )
            
            # 数据格式转换
            result = []
            for stock in stocks_data:
                stock_info = {
                    "ts_code": stock.get("ts_code", ""),
                    "symbol": stock.get("symbol", ""),
                    "name": stock.get("name", ""),
                    "area": stock.get("area", ""),
                    "industry": stock.get("industry", ""),
                    "market": stock.get("market", ""),
                    "list_date": stock.get("list_date", ""),
                    "is_hs": stock.get("is_hs", "")
                }
                
                # 应用筛选条件
                if market and stock_info.get("market") != market:
                    continue
                if industry and stock_info.get("industry") != industry:
                    continue
                    
                result.append(stock_info)
            
            logger.info(f"成功获取{len(result)}只股票信息")
            return result
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            raise

    async def get_daily_data(
        self,
        stock_code: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        获取股票日线数据
        
        参数:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            limit: 返回条数限制
            
        返回:
            List[Dict]: 日线数据列表
        """
        try:
            # 异步调用现有数据获取器
            daily_data = await asyncio.to_thread(
                self.data_fetcher.get_daily_data,
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date,
                limit=limit
            )
            
            # 数据格式转换
            result = []
            for record in daily_data:
                daily_info = {
                    "ts_code": record.get("ts_code", ""),
                    "trade_date": record.get("trade_date", ""),
                    "open": record.get("open"),
                    "high": record.get("high"),
                    "low": record.get("low"),
                    "close": record.get("close"),
                    "pre_close": record.get("pre_close"),
                    "change": record.get("change"),
                    "pct_chg": record.get("pct_chg"),
                    "vol": record.get("vol"),
                    "amount": record.get("amount")
                }
                result.append(daily_info)
            
            logger.info(f"成功获取{stock_code}的{len(result)}条日线数据")
            return result
            
        except Exception as e:
            logger.error(f"获取日线数据失败: {e}")
            raise

    async def start_data_fetch_task(self, request: DataFetchRequest) -> str:
        """
        启动数据获取任务
        
        参数:
            request: 数据获取请求
            
        返回:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())
        
        # 创建任务记录
        task_info = {
            "task_id": task_id,
            "status": "pending",
            "request": request.dict(),
            "start_time": datetime.now(),
            "progress": 0.0,
            "total_stocks": 0,
            "completed_stocks": 0,
            "success_count": 0,
            "error_count": 0,
            "errors": []
        }
        
        self.active_tasks[task_id] = task_info
        
        # 启动后台任务
        asyncio.create_task(self._execute_data_fetch_task(task_id, request))
        
        logger.info(f"数据获取任务已启动: {task_id}")
        return task_id

    async def _execute_data_fetch_task(self, task_id: str, request: DataFetchRequest):
        """
        执行数据获取任务
        
        参数:
            task_id: 任务ID
            request: 数据获取请求
        """
        task_info = self.active_tasks[task_id]
        
        try:
            # 更新任务状态
            task_info["status"] = "running"
            task_info["start_time"] = datetime.now()
            
            # 准备获取参数
            fetch_params = {
                "stock_codes": request.stock_codes,
                "start_date": request.start_date,
                "end_date": request.end_date,
                "data_types": [dt.value for dt in request.data_types],
                "limit": request.limit,
                "use_cache": request.use_cache,
                "force_update": request.force_update
            }
            
            # 如果没有指定股票代码，获取股票列表
            if not request.stock_codes:
                stocks = await self.get_stock_list(limit=request.limit)
                fetch_params["stock_codes"] = [stock["ts_code"] for stock in stocks]
            
            task_info["total_stocks"] = len(fetch_params["stock_codes"])
            
            # 执行数据获取
            result = await asyncio.to_thread(
                self._fetch_data_with_progress,
                task_id,
                fetch_params
            )
            
            # 更新任务完成状态
            task_info["status"] = "completed"
            task_info["end_time"] = datetime.now()
            task_info["duration"] = (task_info["end_time"] - task_info["start_time"]).total_seconds()
            task_info["result"] = result
            task_info["progress"] = 100.0
            
            logger.info(f"数据获取任务完成: {task_id}")
            
        except Exception as e:
            # 更新任务失败状态
            task_info["status"] = "failed"
            task_info["end_time"] = datetime.now()
            task_info["error_message"] = str(e)
            task_info["progress"] = 0.0
            
            logger.error(f"数据获取任务失败: {task_id}, 错误: {e}")

    def _fetch_data_with_progress(self, task_id: str, fetch_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        带进度更新的数据获取
        
        参数:
            task_id: 任务ID
            fetch_params: 获取参数
            
        返回:
            Dict: 获取结果
        """
        task_info = self.active_tasks[task_id]
        stock_codes = fetch_params["stock_codes"]
        total_stocks = len(stock_codes)
        
        # 批量获取数据
        success_count = 0
        error_count = 0
        all_data = []
        
        for i, stock_code in enumerate(stock_codes):
            try:
                # 获取单只股票数据
                stock_data = self.data_fetcher.get_stock_data(
                    stock_code=stock_code,
                    start_date=fetch_params.get("start_date"),
                    end_date=fetch_params.get("end_date"),
                    data_types=fetch_params.get("data_types", ["daily"])
                )
                
                if stock_data:
                    all_data.extend(stock_data)
                    success_count += 1
                else:
                    error_count += 1
                
                # 更新进度
                task_info["completed_stocks"] = i + 1
                task_info["success_count"] = success_count
                task_info["error_count"] = error_count
                task_info["progress"] = (i + 1) / total_stocks * 100
                
            except Exception as e:
                error_count += 1
                task_info["errors"].append({
                    "stock_code": stock_code,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
                logger.warning(f"获取股票{stock_code}数据失败: {e}")
        
        return {
            "total_records": len(all_data),
            "success_count": success_count,
            "error_count": error_count,
            "data_summary": {
                "total_stocks": total_stocks,
                "successful_stocks": success_count,
                "failed_stocks": error_count,
                "data_types": fetch_params.get("data_types", []),
                "date_range": {
                    "start_date": fetch_params.get("start_date"),
                    "end_date": fetch_params.get("end_date")
                }
            }
        }

    async def get_fetch_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取数据获取任务状态
        
        参数:
            task_id: 任务ID
            
        返回:
            Dict: 任务状态信息
        """
        if task_id not in self.active_tasks:
            raise ValueError(f"任务不存在: {task_id}")
        
        task_info = self.active_tasks[task_id]
        
        # 构建状态响应
        status_info = {
            "task_id": task_id,
            "status": task_info["status"],
            "progress": task_info["progress"],
            "total_stocks": task_info["total_stocks"],
            "completed_stocks": task_info["completed_stocks"],
            "success_count": task_info["success_count"],
            "error_count": task_info["error_count"],
            "start_time": task_info["start_time"].isoformat(),
            "end_time": task_info.get("end_time").isoformat() if task_info.get("end_time") else None,
            "duration": task_info.get("duration"),
            "error_message": task_info.get("error_message")
        }
        
        return status_info

    async def monitor_fetch_task(self, task_id: str):
        """
        监控数据获取任务
        
        参数:
            task_id: 任务ID
        """
        logger.info(f"开始监控数据获取任务: {task_id}")
        
        # 这里可以添加任务监控逻辑
        # 例如：发送WebSocket通知、记录日志、性能统计等
        
        while task_id in self.active_tasks:
            task_info = self.active_tasks[task_id]
            
            if task_info["status"] in ["completed", "failed"]:
                break
            
            # 每5秒检查一次任务状态
            await asyncio.sleep(5)
            
            # 可以在这里发送进度更新通知
            logger.debug(f"任务{task_id}进度: {task_info['progress']:.1f}%")
        
        logger.info(f"任务监控结束: {task_id}")

    async def export_data(
        self,
        data_type: str,
        format: str = "json",
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        数据导出功能

        参数:
            data_type: 数据类型
            format: 导出格式
            start_date: 开始日期
            end_date: 结束日期

        返回:
            Dict: 导出结果
        """
        try:
            export_id = str(uuid.uuid4())

            # 根据数据类型获取数据
            if data_type == "stocks":
                data = await self.get_stock_list(limit=10000)
            elif data_type == "daily":
                # 获取所有股票的日线数据（示例）
                data = []
                stocks = await self.get_stock_list(limit=100)
                for stock in stocks[:10]:  # 限制数量避免过大
                    daily_data = await self.get_daily_data(
                        stock_code=stock["ts_code"],
                        start_date=start_date,
                        end_date=end_date,
                        limit=100
                    )
                    data.extend(daily_data)
            else:
                raise ValueError(f"不支持的数据类型: {data_type}")

            # 生成导出文件
            from src.utils.temporary.path_utils import get_output_path

            if format == "json":
                file_path = get_output_path(f"export_{export_id}.json")
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            elif format == "csv":
                import pandas as pd
                file_path = get_output_path(f"export_{export_id}.csv")
                df = pd.DataFrame(data)
                df.to_csv(file_path, index=False, encoding='utf-8')
            else:
                raise ValueError(f"不支持的导出格式: {format}")

            # 获取文件信息
            import os
            file_size = os.path.getsize(file_path)

            result = {
                "export_id": export_id,
                "file_path": file_path,
                "file_size": file_size,
                "record_count": len(data),
                "format": format,
                "created_time": datetime.now().isoformat(),
                "download_url": f"/api/v1/download/{export_id}"
            }

            logger.info(f"数据导出完成: {export_id}, 记录数: {len(data)}")
            return result

        except Exception as e:
            logger.error(f"数据导出失败: {e}")
            raise

    def get_stock_data(self, stock_code: str, start_date: str = None, end_date: str = None, data_types: List[str] = None) -> List[Dict[str, Any]]:
        """
        获取单只股票数据（同步方法，供内部使用）

        参数:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            data_types: 数据类型列表

        返回:
            List[Dict]: 股票数据列表
        """
        try:
            # 调用现有数据获取器
            if "daily" in (data_types or ["daily"]):
                daily_data = self.data_fetcher.get_daily_data(
                    stock_code=stock_code,
                    start_date=start_date,
                    end_date=end_date
                )
                return daily_data if daily_data else []

            return []

        except Exception as e:
            logger.warning(f"获取股票{stock_code}数据失败: {e}")
            return []

    async def get_stock_list(self, limit: int = 10000) -> List[Dict[str, Any]]:
        """
        获取股票列表（异步方法）

        参数:
            limit: 限制返回数量

        返回:
            List[Dict]: 股票列表
        """
        try:
            # {{ AURA-X: Add - 添加股票列表获取方法，支持服务层抽象. Approval: 寸止(ID:架构一致性修复). }}
            # 异步调用现有数据获取器
            stock_list = await asyncio.to_thread(
                self.data_fetcher.fetch_reference_data,
                data_type='stock_list'
            )

            # 转换为字典列表并限制数量
            if hasattr(stock_list, 'to_dict'):
                stocks_data = stock_list.to_dict('records')[:limit]
            elif isinstance(stock_list, list):
                stocks_data = stock_list[:limit]
            else:
                stocks_data = []

            logger.info(f"成功获取{len(stocks_data)}只股票信息")
            return stocks_data

        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            raise

    async def fetch_market_data(self, stock_codes: List[str], data_types: List[str]) -> Dict[str, Any]:
        """
        获取市场数据（异步方法）

        参数:
            stock_codes: 股票代码列表
            data_types: 数据类型列表

        返回:
            Dict: 数据获取结果
        """
        try:
            # {{ AURA-X: Add - 添加市场数据获取方法，支持服务层抽象. Approval: 寸止(ID:深度架构复查修复). }}
            total_records = 0
            success_count = 0

            # 处理数据类型
            for data_type in data_types:
                if data_type == "daily":
                    # 获取日线数据
                    for stock_code in stock_codes:
                        daily_data = await self.get_daily_data(stock_code, limit=100)
                        if daily_data:
                            total_records += len(daily_data)
                            success_count += 1
                elif data_type == "fundamental":
                    # 获取基本面数据
                    stocks_data = await self.get_stock_list(len(stock_codes))
                    if stocks_data:
                        total_records += len(stocks_data)
                        success_count += 1
                else:
                    logger.warning(f"不支持的数据类型: {data_type}")

            return {
                "success": success_count > 0,
                "total_records": total_records,
                "message": f"数据获取完成，共获取{total_records}条记录"
            }

        except Exception as e:
            logger.error(f"获取市场数据失败: {e}")
            return {
                "success": False,
                "total_records": 0,
                "message": f"数据获取失败: {e}"
            }
