<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分布式数据库监控仪表板 - 量化投资平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .status-card {
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .status-healthy { border-left-color: #28a745; }
        .status-warning { border-left-color: #ffc107; }
        .status-critical { border-left-color: #dc3545; }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .node-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        .node-online { border-left: 4px solid #28a745; }
        .node-offline { border-left: 4px solid #dc3545; }
        .progress-sm {
            height: 8px;
        }
        .alert-item {
            border-left: 4px solid #ffc107;
            padding: 10px;
            margin-bottom: 10px;
            background: #fff3cd;
            border-radius: 4px;
        }
        .alert-critical { border-left-color: #dc3545; background: #f8d7da; }
        .alert-warning { border-left-color: #ffc107; background: #fff3cd; }
        .alert-info { border-left-color: #17a2b8; background: #d1ecf1; }
        .refresh-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 刷新指示器 -->
    <div id="refreshIndicator" class="refresh-indicator">
        <div class="spinner-border text-primary" role="status" style="display: none;">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3 mb-0">
                    <i class="fas fa-database text-primary"></i>
                    分布式数据库监控仪表板
                </h1>
                <p class="text-muted">实时监控分布式数据库集群状态和性能</p>
            </div>
        </div>

        <!-- 系统概览 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card status-card h-100" id="systemStatusCard">
                    <div class="card-body text-center">
                        <i class="fas fa-server fa-2x text-primary mb-3"></i>
                        <div class="metric-value" id="systemStatus">检查中...</div>
                        <div class="metric-label">系统状态</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-heartbeat fa-2x text-success mb-3"></i>
                        <div class="metric-value" id="clusterHealth">--</div>
                        <div class="metric-label">集群健康分数</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-network-wired fa-2x text-info mb-3"></i>
                        <div class="metric-value" id="onlineNodes">--</div>
                        <div class="metric-label">在线节点</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                        <div class="metric-value" id="activeAlerts">--</div>
                        <div class="metric-label">活跃告警</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 集群节点状态 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-sitemap"></i>
                            集群节点状态
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="nodesContainer" class="row">
                            <!-- 节点信息将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 性能监控图表 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line"></i>
                            TPS趋势
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="tpsChart" height="200"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clock"></i>
                            响应时间趋势
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="responseTimeChart" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 运维告警 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bell"></i>
                            运维告警
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="alertsContainer">
                            <!-- 告警信息将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-pie"></i>
                            容量使用情况
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="capacityChart" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 自动化运维状态 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-robot"></i>
                            自动化运维状态
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="automationContainer">
                            <!-- 自动化运维信息将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let tpsChart, responseTimeChart, capacityChart;
        let refreshInterval;

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            loadDashboardData();
            
            // 设置自动刷新
            refreshInterval = setInterval(loadDashboardData, 30000); // 30秒刷新一次
        });

        // 初始化图表
        function initCharts() {
            // TPS趋势图
            const tpsCtx = document.getElementById('tpsChart').getContext('2d');
            tpsChart = new Chart(tpsCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'TPS',
                        data: [],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 响应时间趋势图
            const responseCtx = document.getElementById('responseTimeChart').getContext('2d');
            responseTimeChart = new Chart(responseCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '响应时间 (ms)',
                        data: [],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 容量使用图
            const capacityCtx = document.getElementById('capacityChart').getContext('2d');
            capacityChart = new Chart(capacityCtx, {
                type: 'doughnut',
                data: {
                    labels: ['已使用', '可用'],
                    datasets: [{
                        data: [0, 100],
                        backgroundColor: ['#dc3545', '#28a745']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        // 加载仪表板数据
        async function loadDashboardData() {
            showRefreshIndicator();
            
            try {
                // 并行加载多个数据源
                const [systemInfo, clusterStatus, opsData] = await Promise.all([
                    fetch('/api/v2/system/info').then(r => r.json()),
                    fetch('/api/v2/cluster/status').then(r => r.json()),
                    fetch('/api/v2/ops/dashboard').then(r => r.json())
                ]);

                updateSystemOverview(systemInfo.data, clusterStatus.data);
                updateNodesStatus(clusterStatus.data);
                updateCharts(opsData.data);
                updateAlerts(clusterStatus.data);
                updateAutomationStatus(opsData.data);

            } catch (error) {
                console.error('加载仪表板数据失败:', error);
                showError('数据加载失败，请检查网络连接');
            } finally {
                hideRefreshIndicator();
            }
        }

        // 更新系统概览
        function updateSystemOverview(systemInfo, clusterData) {
            const cluster = systemInfo.cluster || {};
            const clusterMetrics = clusterData.cluster_metrics || {};
            const alertSummary = clusterData.alert_summary || {};

            // 系统状态
            const systemStatus = cluster.health_score >= 90 ? '健康' : 
                                cluster.health_score >= 70 ? '警告' : '异常';
            document.getElementById('systemStatus').textContent = systemStatus;
            
            const statusCard = document.getElementById('systemStatusCard');
            statusCard.className = 'card status-card h-100 ' + 
                (cluster.health_score >= 90 ? 'status-healthy' : 
                 cluster.health_score >= 70 ? 'status-warning' : 'status-critical');

            // 集群健康分数
            document.getElementById('clusterHealth').textContent = 
                (cluster.health_score || 0).toFixed(1);

            // 在线节点
            document.getElementById('onlineNodes').textContent = 
                `${cluster.online_nodes || 0}/${cluster.total_nodes || 0}`;

            // 活跃告警
            document.getElementById('activeAlerts').textContent = 
                alertSummary.active_alerts_count || 0;
        }

        // 更新节点状态
        function updateNodesStatus(clusterData) {
            const nodeMetrics = clusterData.node_metrics || {};
            const container = document.getElementById('nodesContainer');
            
            container.innerHTML = '';
            
            Object.entries(nodeMetrics).forEach(([nodeId, metrics]) => {
                const nodeCard = createNodeCard(nodeId, metrics);
                container.appendChild(nodeCard);
            });
        }

        // 创建节点卡片
        function createNodeCard(nodeId, metrics) {
            const div = document.createElement('div');
            div.className = 'col-md-6 col-lg-3';
            
            const isOnline = metrics.status === 'online';
            const statusClass = isOnline ? 'node-online' : 'node-offline';
            const statusIcon = isOnline ? 'fa-check-circle text-success' : 'fa-times-circle text-danger';
            
            div.innerHTML = `
                <div class="node-card ${statusClass}">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">${nodeId}</h6>
                        <i class="fas ${statusIcon}"></i>
                    </div>
                    <div class="small text-muted mb-2">角色: ${metrics.role || 'unknown'}</div>
                    <div class="mb-2">
                        <div class="d-flex justify-content-between">
                            <span class="small">CPU</span>
                            <span class="small">${(metrics.cpu_usage || 0).toFixed(1)}%</span>
                        </div>
                        <div class="progress progress-sm">
                            <div class="progress-bar" style="width: ${metrics.cpu_usage || 0}%"></div>
                        </div>
                    </div>
                    <div class="mb-2">
                        <div class="d-flex justify-content-between">
                            <span class="small">内存</span>
                            <span class="small">${(metrics.memory_usage || 0).toFixed(1)}%</span>
                        </div>
                        <div class="progress progress-sm">
                            <div class="progress-bar bg-info" style="width: ${metrics.memory_usage || 0}%"></div>
                        </div>
                    </div>
                    <div class="small text-muted">连接数: ${metrics.connection_count || 0}</div>
                </div>
            `;
            
            return div;
        }

        // 更新图表
        function updateCharts(opsData) {
            const dashboardData = opsData.dashboard_data || {};
            const trends = dashboardData.performance_trends || {};
            const capacityReport = opsData.capacity_report || {};
            
            // 更新TPS图表
            if (trends.tps && trends.timestamps) {
                const labels = trends.timestamps.map(ts => new Date(ts * 1000).toLocaleTimeString());
                tpsChart.data.labels = labels.slice(-20); // 最近20个数据点
                tpsChart.data.datasets[0].data = trends.tps.slice(-20);
                tpsChart.update();
            }
            
            // 更新响应时间图表
            if (trends.response_times && trends.timestamps) {
                const labels = trends.timestamps.map(ts => new Date(ts * 1000).toLocaleTimeString());
                responseTimeChart.data.labels = labels.slice(-20);
                responseTimeChart.data.datasets[0].data = trends.response_times.slice(-20);
                responseTimeChart.update();
            }
            
            // 更新容量图表
            if (capacityReport.current_utilization !== undefined) {
                const utilization = capacityReport.current_utilization || 0;
                capacityChart.data.datasets[0].data = [utilization, 100 - utilization];
                capacityChart.update();
            }
        }

        // 更新告警信息
        function updateAlerts(clusterData) {
            const alertSummary = clusterData.alert_summary || {};
            const recentAlerts = alertSummary.recent_alerts || [];
            const container = document.getElementById('alertsContainer');
            
            if (recentAlerts.length === 0) {
                container.innerHTML = '<div class="text-muted text-center">暂无活跃告警</div>';
                return;
            }
            
            container.innerHTML = recentAlerts.slice(0, 5).map(alert => `
                <div class="alert-item alert-${alert.level}">
                    <div class="d-flex justify-content-between">
                        <strong>${alert.title}</strong>
                        <small>${new Date(alert.timestamp * 1000).toLocaleString()}</small>
                    </div>
                    <div class="small">${alert.message}</div>
                </div>
            `).join('');
        }

        // 更新自动化运维状态
        function updateAutomationStatus(opsData) {
            const automationStatus = opsData.automation_status || {};
            const container = document.getElementById('automationContainer');
            
            const isActive = automationStatus.automation_active;
            const statusBadge = isActive ? 
                '<span class="badge bg-success">运行中</span>' : 
                '<span class="badge bg-secondary">已停止</span>';
            
            container.innerHTML = `
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4">${statusBadge}</div>
                            <div class="text-muted">自动化状态</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4">${automationStatus.pending_actions || 0}</div>
                            <div class="text-muted">待处理动作</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4">${(automationStatus.automation_rules || []).length}</div>
                            <div class="text-muted">自动化规则</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4">${(automationStatus.maintenance_tasks || []).length}</div>
                            <div class="text-muted">维护任务</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 显示刷新指示器
        function showRefreshIndicator() {
            document.querySelector('#refreshIndicator .spinner-border').style.display = 'block';
        }

        // 隐藏刷新指示器
        function hideRefreshIndicator() {
            document.querySelector('#refreshIndicator .spinner-border').style.display = 'none';
        }

        // 显示错误信息
        function showError(message) {
            // 可以实现一个toast通知
            console.error(message);
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
