"""
运维监控API路由

提供运维自动化管理、智能监控和系统状态的API接口
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel

from src.monitoring import (
    OpsAutomationManager, 
    IntelligentOpsEngine, 
    SystemMonitor,
    Alert,
    AutomationRule,
    AutomationTask,
    AlertLevel,
    AutomationAction
)

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/ops", tags=["运维监控"])

# 全局运维组件实例
ops_manager: Optional[OpsAutomationManager] = None
intelligent_engine: Optional[IntelligentOpsEngine] = None
system_monitor: Optional[SystemMonitor] = None

# 数据模型
class SystemMetricsResponse(BaseModel):
    """系统指标响应模型"""
    cpu_usage: float
    memory_usage: float
    memory_available_gb: float
    disk_usage: float
    disk_free_gb: float
    network_sent_mb: float
    network_recv_mb: float
    process_count: int
    load_average: List[float]
    timestamp: str

class AlertResponse(BaseModel):
    """告警响应模型"""
    id: str
    level: str
    title: str
    message: str
    timestamp: str
    source: str
    resolved: bool

class AutomationTaskResponse(BaseModel):
    """自动化任务响应模型"""
    id: str
    rule_id: str
    action: str
    status: str
    created_at: str
    started_at: Optional[str]
    completed_at: Optional[str]
    result: Optional[Dict[str, Any]]
    error: Optional[str]

class AutomationRuleResponse(BaseModel):
    """自动化规则响应模型"""
    id: str
    name: str
    description: str
    condition: str
    action: str
    enabled: bool
    cooldown_minutes: int
    last_executed: Optional[str]

class PerformanceInsightResponse(BaseModel):
    """性能洞察响应模型"""
    category: str
    issue_type: str
    severity: str
    description: str
    impact: str
    recommendation: str
    estimated_improvement: str

class CapacityPredictionResponse(BaseModel):
    """容量预测响应模型"""
    metric_name: str
    current_value: float
    predicted_value: float
    prediction_horizon_hours: int
    confidence: float
    trend: str
    recommendation: str

def get_ops_manager() -> OpsAutomationManager:
    """获取运维自动化管理器实例"""
    global ops_manager
    if ops_manager is None:
        ops_manager = OpsAutomationManager()
        ops_manager.start()
    return ops_manager

def get_intelligent_engine() -> IntelligentOpsEngine:
    """获取智能运维引擎实例"""
    global intelligent_engine
    if intelligent_engine is None:
        intelligent_engine = IntelligentOpsEngine()
    return intelligent_engine

def get_system_monitor() -> SystemMonitor:
    """获取系统监控实例"""
    global system_monitor
    if system_monitor is None:
        system_monitor = SystemMonitor()
        system_monitor.start_monitoring()
    return system_monitor

@router.get("/status", summary="获取运维系统状态")
async def get_ops_status():
    """获取运维系统状态"""
    try:
        manager = get_ops_manager()
        engine = get_intelligent_engine()
        
        ops_stats = manager.get_automation_stats()
        engine_stats = engine.get_engine_stats()
        
        return {
            "status": "running",
            "automation": ops_stats,
            "intelligence": engine_stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取运维系统状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/metrics", response_model=SystemMetricsResponse, summary="获取系统指标")
async def get_system_metrics():
    """获取当前系统指标"""
    try:
        monitor = get_system_monitor()
        metrics = monitor.get_current_metrics()
        
        if not metrics:
            raise HTTPException(status_code=500, detail="无法获取系统指标")
        
        return SystemMetricsResponse(
            cpu_usage=metrics.get('cpu_usage', 0),
            memory_usage=metrics.get('memory_usage', 0),
            memory_available_gb=metrics.get('memory_available_gb', 0),
            disk_usage=metrics.get('disk_usage', 0),
            disk_free_gb=metrics.get('disk_free_gb', 0),
            network_sent_mb=metrics.get('network_sent_mb', 0),
            network_recv_mb=metrics.get('network_recv_mb', 0),
            process_count=metrics.get('process_count', 0),
            load_average=metrics.get('load_average', [0, 0, 0]),
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"获取系统指标失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/alerts", response_model=List[AlertResponse], summary="获取告警列表")
async def get_alerts(limit: int = 50):
    """获取告警列表"""
    try:
        manager = get_ops_manager()
        alerts = []
        
        for alert in list(manager.alerts.values())[-limit:]:
            alerts.append(AlertResponse(
                id=alert.id,
                level=alert.level.value,
                title=alert.title,
                message=alert.message,
                timestamp=alert.timestamp.isoformat(),
                source=alert.source,
                resolved=alert.resolved
            ))
        
        # 按时间倒序排列
        alerts.sort(key=lambda x: x.timestamp, reverse=True)
        return alerts
        
    except Exception as e:
        logger.error(f"获取告警列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tasks", response_model=List[AutomationTaskResponse], summary="获取自动化任务")
async def get_automation_tasks(limit: int = 100):
    """获取自动化任务列表"""
    try:
        manager = get_ops_manager()
        tasks = []
        
        for task in list(manager.automation_tasks.values())[-limit:]:
            tasks.append(AutomationTaskResponse(
                id=task.id,
                rule_id=task.rule_id,
                action=task.action.value,
                status=task.status,
                created_at=task.created_at.isoformat(),
                started_at=task.started_at.isoformat() if task.started_at else None,
                completed_at=task.completed_at.isoformat() if task.completed_at else None,
                result=task.result,
                error=task.error
            ))
        
        # 按创建时间倒序排列
        tasks.sort(key=lambda x: x.created_at, reverse=True)
        return tasks
        
    except Exception as e:
        logger.error(f"获取自动化任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/rules", response_model=List[AutomationRuleResponse], summary="获取自动化规则")
async def get_automation_rules():
    """获取自动化规则列表"""
    try:
        manager = get_ops_manager()
        rules = []
        
        for rule in manager.automation_rules.values():
            rules.append(AutomationRuleResponse(
                id=rule.id,
                name=rule.name,
                description=rule.description,
                condition=rule.condition,
                action=rule.action.value,
                enabled=rule.enabled,
                cooldown_minutes=rule.cooldown_minutes,
                last_executed=rule.last_executed.isoformat() if rule.last_executed else None
            ))
        
        return rules
        
    except Exception as e:
        logger.error(f"获取自动化规则失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/rules/{rule_id}/toggle", summary="切换自动化规则状态")
async def toggle_automation_rule(rule_id: str):
    """启用/禁用自动化规则"""
    try:
        manager = get_ops_manager()
        
        if rule_id not in manager.automation_rules:
            raise HTTPException(status_code=404, detail="规则不存在")
        
        rule = manager.automation_rules[rule_id]
        rule.enabled = not rule.enabled
        
        return {
            "success": True,
            "message": f"规则 {rule.name} 已{'启用' if rule.enabled else '禁用'}",
            "enabled": rule.enabled
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"切换自动化规则状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/insights", response_model=List[PerformanceInsightResponse], summary="获取性能洞察")
async def get_performance_insights():
    """获取性能洞察"""
    try:
        engine = get_intelligent_engine()
        monitor = get_system_monitor()
        
        # 获取当前系统指标
        current_metrics = monitor.get_current_metrics()
        if not current_metrics:
            raise HTTPException(status_code=500, detail="无法获取当前系统指标")
        
        # 模拟SystemMetrics对象
        from src.monitoring.system_monitor import SystemMetrics
        system_metrics = SystemMetrics(
            timestamp=datetime.now().isoformat(),
            cpu_usage=current_metrics.get('cpu_usage', 0),
            memory_usage=current_metrics.get('memory_usage', 0),
            memory_available_gb=current_metrics.get('memory_available_gb', 0),
            disk_usage=current_metrics.get('disk_usage', 0),
            disk_free_gb=current_metrics.get('disk_free_gb', 0),
            network_sent_mb=current_metrics.get('network_sent_mb', 0),
            network_recv_mb=current_metrics.get('network_recv_mb', 0),
            process_count=current_metrics.get('process_count', 0),
            load_average=current_metrics.get('load_average', [0, 0, 0])
        )
        
        insights = engine.generate_performance_insights(system_metrics)
        
        return [
            PerformanceInsightResponse(
                category=insight.category,
                issue_type=insight.issue_type,
                severity=insight.severity,
                description=insight.description,
                impact=insight.impact,
                recommendation=insight.recommendation,
                estimated_improvement=insight.estimated_improvement
            )
            for insight in insights
        ]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取性能洞察失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/predictions", response_model=List[CapacityPredictionResponse], summary="获取容量预测")
async def get_capacity_predictions():
    """获取容量预测"""
    try:
        engine = get_intelligent_engine()
        predictions = []
        
        metrics_to_predict = ['cpu_usage', 'memory_usage', 'disk_usage']
        
        for metric in metrics_to_predict:
            prediction = engine.predict_capacity(metric, 24)
            if prediction:
                predictions.append(CapacityPredictionResponse(
                    metric_name=prediction.metric_name,
                    current_value=prediction.current_value,
                    predicted_value=prediction.predicted_value,
                    prediction_horizon_hours=prediction.prediction_horizon_hours,
                    confidence=prediction.confidence,
                    trend=prediction.trend,
                    recommendation=prediction.recommendation
                ))
        
        return predictions
        
    except Exception as e:
        logger.error(f"获取容量预测失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats", summary="获取运维统计")
async def get_ops_stats():
    """获取运维统计信息"""
    try:
        manager = get_ops_manager()
        engine = get_intelligent_engine()
        
        return {
            "automation": manager.get_automation_stats(),
            "intelligence": engine.get_engine_stats(),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取运维统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
