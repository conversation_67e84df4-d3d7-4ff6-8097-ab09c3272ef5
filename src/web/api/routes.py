#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API路由模块
- 定义所有API端点
- 集成现有业务逻辑
- 提供数据获取、回测、监控等功能
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, date

from fastapi import APIRouter, HTTPException, BackgroundTasks, Query
from pydantic import BaseModel, Field

from src.utils.logging.logger_factory import get_logger
from src.web.models.requests import *
from src.web.models.responses import *
from src.web.services.data_service import DataService
from src.web.services.backtest_service import BacktestService
from src.web.services.monitor_service import MonitorService

logger = get_logger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v1", tags=["量化投资平台API"])

# 服务实例（将在应用启动时初始化）
data_service: Optional[DataService] = None
backtest_service: Optional[BacktestService] = None
monitor_service: Optional[MonitorService] = None

def init_services():
    """初始化服务实例"""
    global data_service, backtest_service, monitor_service
    data_service = DataService()
    backtest_service = BacktestService()
    monitor_service = MonitorService()
    logger.info("✅ API服务实例初始化完成")

# ==================== 系统信息接口 ====================

@router.get("/system/info", response_model=ApiResponse)
async def get_system_info():
    """获取系统信息"""
    try:
        system_info = {
            "platform_name": "量化投资平台",
            "version": "1.0.0",
            "api_version": "v1",
            "build_date": "2025-01-25",
            "author": "泽强Felix",
            "features": [
                "数据获取与处理",
                "策略回测分析", 
                "性能监控告警",
                "多级缓存优化",
                "实时数据推送"
            ],
            "status": "运行中",
            "uptime": datetime.now().isoformat()
        }
        
        return ApiResponse(
            success=True,
            message="系统信息获取成功",
            data=system_info
        )
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/system/stats", response_model=ApiResponse)
async def get_system_stats():
    """获取系统统计信息"""
    try:
        stats = await monitor_service.get_system_stats()
        
        return ApiResponse(
            success=True,
            message="系统统计信息获取成功",
            data=stats
        )
    except Exception as e:
        logger.error(f"获取系统统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== 数据管理接口 ====================

@router.get("/data/stocks", response_model=ApiResponse)
async def get_stock_list(
    limit: int = Query(100, description="返回数量限制"),
    market: Optional[str] = Query(None, description="市场筛选"),
    industry: Optional[str] = Query(None, description="行业筛选")
):
    """获取股票列表"""
    try:
        stocks = await data_service.get_stock_list(
            limit=limit,
            market=market,
            industry=industry
        )
        
        return ApiResponse(
            success=True,
            message=f"成功获取{len(stocks)}只股票信息",
            data=stocks
        )
    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/data/fetch", response_model=ApiResponse)
async def fetch_market_data(
    request: DataFetchRequest,
    background_tasks: BackgroundTasks
):
    """获取市场数据"""
    try:
        # 启动后台任务执行数据获取
        task_id = await data_service.start_data_fetch_task(request)
        
        # 添加后台任务监控
        background_tasks.add_task(
            data_service.monitor_fetch_task,
            task_id
        )
        
        return ApiResponse(
            success=True,
            message="数据获取任务已启动",
            data={"task_id": task_id, "status": "started"}
        )
    except Exception as e:
        logger.error(f"启动数据获取任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/data/fetch/status/{task_id}", response_model=ApiResponse)
async def get_fetch_status(task_id: str):
    """获取数据获取任务状态"""
    try:
        status = await data_service.get_fetch_task_status(task_id)
        
        return ApiResponse(
            success=True,
            message="任务状态获取成功",
            data=status
        )
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/data/daily/{stock_code}", response_model=ApiResponse)
async def get_daily_data(
    stock_code: str,
    start_date: Optional[str] = Query(None, description="开始日期 YYYYMMDD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYYMMDD"),
    limit: int = Query(100, description="返回条数限制")
):
    """获取股票日线数据"""
    try:
        daily_data = await data_service.get_daily_data(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )
        
        return ApiResponse(
            success=True,
            message=f"成功获取{stock_code}的{len(daily_data)}条日线数据",
            data=daily_data
        )
    except Exception as e:
        logger.error(f"获取日线数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== 策略回测接口 ====================

@router.get("/strategies", response_model=ApiResponse)
async def get_available_strategies():
    """获取可用策略列表"""
    try:
        strategies = await backtest_service.get_available_strategies()
        
        return ApiResponse(
            success=True,
            message="策略列表获取成功",
            data=strategies
        )
    except Exception as e:
        logger.error(f"获取策略列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/backtest/run", response_model=ApiResponse)
async def run_strategy_backtest(
    request: BacktestRequest,
    background_tasks: BackgroundTasks
):
    """执行策略回测"""
    try:
        # 启动后台回测任务
        task_id = await backtest_service.start_backtest_task(request)
        
        # 添加后台任务监控
        background_tasks.add_task(
            backtest_service.monitor_backtest_task,
            task_id
        )
        
        return ApiResponse(
            success=True,
            message="回测任务已启动",
            data={"task_id": task_id, "status": "started"}
        )
    except Exception as e:
        logger.error(f"启动回测任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/backtest/status/{task_id}", response_model=ApiResponse)
async def get_backtest_status(task_id: str):
    """获取回测任务状态"""
    try:
        status = await backtest_service.get_backtest_task_status(task_id)
        
        return ApiResponse(
            success=True,
            message="回测状态获取成功",
            data=status
        )
    except Exception as e:
        logger.error(f"获取回测状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/backtest/results/{task_id}", response_model=ApiResponse)
async def get_backtest_results(task_id: str):
    """获取回测结果"""
    try:
        results = await backtest_service.get_backtest_results(task_id)
        
        return ApiResponse(
            success=True,
            message="回测结果获取成功",
            data=results
        )
    except Exception as e:
        logger.error(f"获取回测结果失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== 性能监控接口 ====================

@router.get("/monitor/performance", response_model=ApiResponse)
async def get_performance_metrics():
    """获取性能监控指标"""
    try:
        metrics = await monitor_service.get_performance_metrics()
        
        return ApiResponse(
            success=True,
            message="性能指标获取成功",
            data=metrics
        )
    except Exception as e:
        logger.error(f"获取性能指标失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/monitor/cache", response_model=ApiResponse)
async def get_cache_metrics():
    """获取缓存监控指标"""
    try:
        cache_metrics = await monitor_service.get_cache_metrics()
        
        return ApiResponse(
            success=True,
            message="缓存指标获取成功",
            data=cache_metrics
        )
    except Exception as e:
        logger.error(f"获取缓存指标失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/monitor/alerts", response_model=ApiResponse)
async def get_system_alerts():
    """获取系统告警信息"""
    try:
        alerts = await monitor_service.get_system_alerts()
        
        return ApiResponse(
            success=True,
            message="告警信息获取成功",
            data=alerts
        )
    except Exception as e:
        logger.error(f"获取告警信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== 实用工具接口 ====================

@router.post("/utils/cache/clear", response_model=ApiResponse)
async def clear_cache():
    """清理系统缓存"""
    try:
        result = await monitor_service.clear_system_cache()
        
        return ApiResponse(
            success=True,
            message="缓存清理完成",
            data=result
        )
    except Exception as e:
        logger.error(f"清理缓存失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/utils/export/data", response_model=ApiResponse)
async def export_data(
    data_type: str = Query(..., description="数据类型"),
    format: str = Query("json", description="导出格式"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期")
):
    """数据导出功能"""
    try:
        export_result = await data_service.export_data(
            data_type=data_type,
            format=format,
            start_date=start_date,
            end_date=end_date
        )
        
        return ApiResponse(
            success=True,
            message="数据导出完成",
            data=export_result
        )
    except Exception as e:
        logger.error(f"数据导出失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
