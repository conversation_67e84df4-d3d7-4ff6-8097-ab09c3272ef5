"""
Web应用数据集成模块
基于现有数据获取器的Web接口适配层
"""

import pandas as pd
import sqlite3
import os
import yaml
from datetime import datetime
from typing import List, Dict, Optional, Any
from src.utils.logging.logger_factory import get_logger
# {{ AURA-X: Modify - 移除分离式数据获取器导入，改用DataFetcherManager. Approval: 寸止(ID:阶段1.3). }}
from src.data.storage.storage_factory import StorageFactory
from src.data.storage.cache.cache_factory import CacheFactory
from src.utils.config.config_factory import config_factory

logger = get_logger(__name__)

class WebDataIntegration:
    """Web应用数据集成类 - 基于现有数据获取器的适配层"""

    def __init__(self):
        self.db_manager = None
        self.market_data = None
        self.financial_data = None
        self.cache_manager = None
        self._init_database()
        self._init_cache()
        self._init_data_fetchers()

    def _init_database(self):
        """初始化数据库连接"""
        try:
            # 使用存储工厂创建MySQL适配器
            mysql_config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': '',
                'database': 'quantification',
                'charset': 'utf8mb4'
            }
            self.db_manager = StorageFactory.create('mysql', mysql_config)
            logger.info("MySQL数据库连接初始化成功")

        except Exception as e:
            logger.error(f"MySQL数据库连接初始化失败: {e}")
            self.db_manager = None

    def _init_cache(self):
        """初始化多级缓存系统"""
        try:
            # 获取缓存配置
            app_config = config_factory.load_config('app', 'config')
            cache_config = app_config.get('cache', {})

            if cache_config.get('strategy') == 'multi':
                # 创建多级缓存
                self.cache_manager = CacheFactory.create_cache(
                    cache_type='multi',
                    config=cache_config
                )
                logger.info("多级缓存系统初始化成功")
            else:
                # 创建单级内存缓存
                self.cache_manager = CacheFactory.create_cache(
                    cache_type='memory',
                    max_size=1000,
                    default_expires=3600
                )
                logger.info("单级内存缓存初始化成功")

        except Exception as e:
            logger.error(f"缓存系统初始化失败: {e}")
            # 创建简单的内存缓存作为后备
            try:
                from src.data.storage.cache.memory_cache import MemoryCache
                self.cache_manager = MemoryCache(max_size=500, default_expires=1800)
                logger.info("后备内存缓存初始化成功")
            except Exception as fallback_e:
                logger.error(f"后备缓存初始化也失败: {fallback_e}")
                self.cache_manager = None

    def _init_data_fetchers(self):
        """初始化数据获取器"""
        try:
            # {{ AURA-X: Modify - 完全使用DataFetcherManager，移除不必要的适配器. Approval: 寸止(ID:阶段1.3). }}
            from src.data.fetcher.data_fetcher_manager import get_cached_data_fetcher

            # 使用统一的HybridDataFetcher实例，启用缓存
            self.data_fetcher = get_cached_data_fetcher(
                cache_type='memory',
                cache_config={'ttl': 3600}  # 1小时TTL
            )

            logger.info("数据获取器初始化成功（使用DataFetcherManager）")

        except Exception as e:
            logger.error(f"数据获取器初始化失败: {e}")
            # 降级到模拟数据模式
            self.data_fetcher = None

    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息（带缓存优化）"""
        try:
            # 尝试从缓存获取系统信息
            cache_key = "system_info"
            if self.cache_manager:
                cached_info = self.cache_manager.get(cache_key)
                if cached_info is not None:
                    logger.debug("从缓存获取系统信息")
                    return cached_info

            # 获取数据库统计信息
            stock_count = 0
            data_count = 0
            db_size = 0
            db_healthy = False
            active_connections = 0

            # 使用MySQL适配器获取统计信息
            if self.db_manager:
                try:
                    if not self.db_manager.connect():
                        raise Exception("数据库连接失败")

                    # 获取股票数量
                    result = self.db_manager.execute_query("SELECT COUNT(*) as count FROM stock_basic")
                    stock_count = int(result.iloc[0]['count']) if not result.empty else 0

                    # 获取日线数据数量
                    result = self.db_manager.execute_query("SELECT COUNT(*) as count FROM daily_price")
                    data_count = int(result.iloc[0]['count']) if not result.empty else 0

                    # 获取数据库大小（MySQL）
                    result = self.db_manager.execute_query("""
                        SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS db_size_mb
                        FROM information_schema.tables
                        WHERE table_schema = DATABASE()
                    """)
                    db_size_mb = float(result.iloc[0]['db_size_mb']) if not result.empty else 0.0
                    db_size = int((db_size_mb or 0) * 1024 * 1024)  # 转换为字节

                    # 添加数据库健康检查
                    health_result = self.db_manager.execute_query("SELECT 1 as health_check")
                    db_healthy = not health_result.empty and health_result.iloc[0]['health_check'] == 1

                    # 获取数据库连接信息
                    try:
                        conn_result = self.db_manager.execute_query("SHOW STATUS LIKE 'Threads_connected'")
                        if not conn_result.empty and len(conn_result.columns) >= 2:
                            # SHOW STATUS返回两列：Variable_name和Value
                            active_connections = int(conn_result.iloc[0].iloc[1])  # 使用位置索引获取Value列
                        else:
                            active_connections = 0
                    except Exception as conn_e:
                        logger.warning(f"获取连接数失败: {conn_e}")
                        active_connections = 0

                    self.db_manager.disconnect()
                    logger.info(f"✅ MySQL统计获取成功: 股票{stock_count}条, 日线{data_count}条, 大小{db_size_mb}MB, 连接数{active_connections}, 健康状态{'正常' if db_healthy else '异常'}")

                except Exception as e:
                    logger.error(f"获取数据库统计失败，使用默认值: {e}")
                    logger.error(f"数据库连接状态: {self.db_manager is not None}")
                    if self.db_manager:
                        logger.error(f"数据库连接详情: host={getattr(self.db_manager, 'host', 'unknown')}, database={getattr(self.db_manager, 'database', 'unknown')}")
                    stock_count = 0
                    data_count = 0
                    db_size = 0
                    db_healthy = False
                    active_connections = 0
            else:
                logger.warning("数据库管理器未初始化，使用默认值")
                stock_count = 0
                data_count = 0
                db_size = 0

            # 构建系统信息
            system_info = {
                "platform_name": "量化投资平台",
                "version": "1.0.0",
                "api_version": "v1",
                "build_date": "2025-01-25",
                "author": "泽强Felix",
                "features": [
                    "数据获取与处理",
                    "策略回测分析",
                    "性能监控告警",
                    "多级缓存优化",
                    "实时数据推送"
                ],
                "status": "运行中",
                "uptime": datetime.now().isoformat(),
                "database": {
                    "type": "MySQL",
                    "stock_count": stock_count,
                    "data_count": data_count,
                    "database_size": db_size,
                    "health_status": "healthy" if db_healthy else "unhealthy",
                    "active_connections": active_connections
                }
            }

            # 缓存系统信息（30秒TTL）
            if self.cache_manager:
                try:
                    self.cache_manager.set(cache_key, system_info, expires=30)
                    logger.debug("系统信息已缓存")
                except Exception as cache_e:
                    logger.warning(f"缓存系统信息失败: {cache_e}")

            return system_info

        except Exception as e:
            logger.error(f"获取系统信息失败: {e}")
            return {
                "platform_name": "量化投资平台",
                "version": "1.0.0",
                "status": "运行中",
                "database": {"type": "MySQL"},
                "error": str(e)
            }

    def get_stock_list(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取股票列表 - 使用现有的财务数据获取器（带缓存优化）"""
        try:
            # 尝试从缓存获取股票列表
            cache_key = f"stock_list:{limit}"
            if self.cache_manager:
                cached_stocks = self.cache_manager.get(cache_key)
                if cached_stocks is not None:
                    logger.debug(f"从缓存获取股票列表 (limit={limit})")
                    return cached_stocks

            # {{ AURA-X: Modify - 直接使用DataFetcherManager，移除适配器调用. Approval: 寸止(ID:阶段1.3). }}
            # 优先使用现有的数据获取器
            if self.data_fetcher:
                try:
                    df = self.data_fetcher.fetch_reference_data(
                        data_type='stock_list',
                        exchange='',
                        list_status='L',
                        fields=['ts_code', 'symbol', 'name', 'area', 'industry', 'market', 'list_date'],
                        save=False,
                        use_cache=True
                    )

                    if not df.empty:
                        # 更新本地数据库
                        self._update_stock_basic_to_db(df)

                        # 返回限制数量的结果（如果limit大于等于总数，返回全部）
                        if limit >= len(df):
                            results = df.to_dict('records')
                        else:
                            results = df.head(limit).to_dict('records')
                        logger.info(f"通过数据获取器获取股票列表成功，共 {len(results)} 条记录")

                        # 缓存结果（5分钟TTL）
                        if self.cache_manager:
                            try:
                                self.cache_manager.set(cache_key, results, expires=300)
                                logger.debug("股票列表已缓存")
                            except Exception as cache_e:
                                logger.warning(f"缓存股票列表失败: {cache_e}")

                        return results

                except Exception as e:
                    logger.warning(f"数据获取器调用失败，使用本地数据: {e}")

            # 从MySQL数据库获取
            if self.db_manager:
                try:
                    if not self.db_manager.connect():
                        raise Exception("数据库连接失败")

                    # 使用存储适配器执行查询
                    df = self.db_manager.execute_query(f"""
                        SELECT ts_code, symbol, name, area, industry, market
                        FROM stock_basic
                        LIMIT {limit}
                    """)

                    if not df.empty:
                        results = df.to_dict('records')
                        logger.info(f"从MySQL数据库获取股票列表成功，共 {len(results)} 条记录")

                        # 缓存结果（5分钟TTL）
                        if self.cache_manager:
                            try:
                                self.cache_manager.set(cache_key, results, expires=300)
                                logger.debug("股票列表已缓存")
                            except Exception as cache_e:
                                logger.warning(f"缓存股票列表失败: {cache_e}")

                        self.db_manager.disconnect()
                        return results

                    self.db_manager.disconnect()

                except Exception as e:
                    logger.warning(f"MySQL查询失败: {e}")
                    try:
                        self.db_manager.disconnect()
                    except:
                        pass

            # 如果MySQL失败，返回空列表
            logger.warning("数据库管理器未初始化或查询失败，返回空列表")
            return []

        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []

    def get_daily_data(self, ts_code: str = None, limit: int = 100, days: int = 30) -> List[Dict[str, Any]]:
        """获取日线数据（带缓存优化）"""
        try:
            # 构建缓存键
            cache_key = f"daily_data:{ts_code or 'all'}:{limit}:{days}"

            # 尝试从缓存获取
            if self.cache_manager:
                cached_data = self.cache_manager.get(cache_key)
                if cached_data is not None:
                    logger.debug(f"从缓存获取日线数据 (ts_code={ts_code}, limit={limit})")
                    return cached_data

            # 从MySQL数据库获取日线数据
            if self.db_manager:
                try:
                    if not self.db_manager.connect():
                        raise Exception("数据库连接失败")

                    # 构建查询SQL
                    if ts_code:
                        # 获取特定股票的日线数据
                        sql = f"""
                            SELECT ts_code, trade_date, open, high, low, close,
                                   pre_close, change_amount, pct_chg, vol, amount
                            FROM daily_price
                            WHERE ts_code = '{ts_code}'
                            ORDER BY trade_date DESC
                            LIMIT {limit}
                        """
                    else:
                        # 获取最近的日线数据
                        sql = f"""
                            SELECT ts_code, trade_date, open, high, low, close,
                                   pre_close, change_amount, pct_chg, vol, amount
                            FROM daily_price
                            WHERE trade_date >= DATE_SUB(CURDATE(), INTERVAL {days} DAY)
                            ORDER BY trade_date DESC, ts_code
                            LIMIT {limit}
                        """

                    df = self.db_manager.execute_query(sql)

                    if not df.empty:
                        # 转换日期格式
                        df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d')
                        results = df.to_dict('records')
                        logger.info(f"从MySQL获取日线数据成功，共 {len(results)} 条记录")

                        # 缓存结果（2分钟TTL，日线数据更新频率较低）
                        if self.cache_manager:
                            try:
                                self.cache_manager.set(cache_key, results, expires=120)
                                logger.debug("日线数据已缓存")
                            except Exception as cache_e:
                                logger.warning(f"缓存日线数据失败: {cache_e}")

                        self.db_manager.disconnect()
                        return results

                    self.db_manager.disconnect()

                except Exception as e:
                    logger.warning(f"MySQL查询日线数据失败: {e}")
                    try:
                        self.db_manager.disconnect()
                    except:
                        pass

            logger.warning("数据库管理器未初始化或查询失败，返回空列表")
            return []

        except Exception as e:
            logger.error(f"获取日线数据失败: {e}")
            return []

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            if self.cache_manager and hasattr(self.cache_manager, 'get_stats'):
                stats = self.cache_manager.get_stats()
                logger.debug("获取缓存统计信息成功")
                return stats
            else:
                return {"status": "cache_not_available"}
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {"error": str(e)}

    def _update_stock_basic_to_db(self, df: pd.DataFrame):
        """更新股票基本信息到MySQL数据库"""
        try:
            if not self.db_manager:
                logger.warning("数据库管理器未初始化，跳过数据更新")
                return

            if not self.db_manager.connect():
                raise Exception("数据库连接失败")

            # 清空现有数据
            self.db_manager.query("DELETE FROM stock_basic")

            # 使用存储适配器的批量插入功能
            self.db_manager.insert_dataframe('stock_basic', df)

            self.db_manager.disconnect()
            logger.info(f"更新股票基本信息到MySQL数据库成功，共 {len(df)} 条记录")

        except Exception as e:
            logger.error(f"更新股票基本信息到数据库失败: {e}")
    
    def get_stock_data(self, ts_code: str, start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """获取股票数据"""
        try:
            if not self.db_manager:
                return []

            if not self.db_manager.connect():
                raise Exception("数据库连接失败")

            query = "SELECT * FROM daily_price WHERE ts_code = %s"
            params = [ts_code]

            if start_date:
                query += " AND trade_date >= %s"
                params.append(start_date)

            if end_date:
                query += " AND trade_date <= %s"
                params.append(end_date)

            query += " ORDER BY trade_date DESC LIMIT 100"

            df = self.db_manager.query(query, params)
            results = df.to_dict('records') if not df.empty else []

            self.db_manager.disconnect()
            return results

        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            return []
    
    def fetch_data_from_tushare(self, stock_codes: List[str] = None, data_types: List[str] = None) -> Dict[str, Any]:
        """使用现有数据获取器获取数据"""
        try:
            if not self.market_data and not self.financial_data:
                return self._get_mock_fetch_result(stock_codes, data_types)

            total_records = 0
            success_count = 0
            error_count = 0

            # 处理数据类型
            data_types = data_types or ["daily"]

            for data_type in data_types:
                if data_type == "daily":
                    records = self._fetch_daily_data_with_fetcher(stock_codes)
                    total_records += records
                    if records > 0:
                        success_count += 1
                elif data_type == "fundamental":
                    records = self._fetch_fundamental_data_with_fetcher(stock_codes)
                    total_records += records
                    if records > 0:
                        success_count += 1
                else:
                    logger.warning(f"不支持的数据类型: {data_type}")
                    error_count += 1

            result = {
                "success": success_count > 0,
                "total_records": total_records,
                "data_types": data_types,
                "stock_codes": stock_codes or [],
                "success_count": success_count,
                "error_count": error_count,
                "message": f"数据获取完成，成功 {success_count} 个类型，失败 {error_count} 个类型"
            }

            logger.info(f"数据获取完成: {total_records} 条记录")
            return result

        except Exception as e:
            logger.error(f"数据获取失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "total_records": 0
            }

    def _get_mock_fetch_result(self, stock_codes: List[str] = None, data_types: List[str] = None) -> Dict[str, Any]:
        """获取模拟数据结果"""
        total_records = 0
        if stock_codes:
            total_records = len(stock_codes) * 100  # 假设每只股票100条记录
        else:
            total_records = 1000  # 默认1000条记录

        return {
            "success": True,
            "total_records": total_records,
            "data_types": data_types or ["daily"],
            "stock_codes": stock_codes or [],
            "message": "模拟数据获取完成"
        }

    def _fetch_daily_data_with_fetcher(self, stock_codes: List[str] = None) -> int:
        """使用现有市场数据获取器获取日线数据"""
        try:
            if not self.market_data:
                return 0

            if not stock_codes:
                # 获取默认股票列表的最近数据
                stock_codes = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']

            # {{ AURA-X: Modify - 直接使用DataFetcherManager获取市场数据. Approval: 寸止(ID:阶段1.3). }}
            # 使用现有的数据获取器获取真实数据
            df = self.data_fetcher.fetch_market_data(
                symbols=stock_codes[:50],  # 增加数量但避免API限制
                data_type='daily',
                start_date='20240101',  # 获取2024年全年数据
                end_date='20241231',
                save=True,  # 确保保存到数据库
                use_cache=True
            )

            if not df.empty:
                # 保存到数据库
                self._save_daily_data_to_db(df)
                logger.info(f"通过市场数据获取器获取日线数据成功: {len(df)} 条记录")
                return len(df)
            else:
                logger.warning("未获取到日线数据")
                return 0

        except Exception as e:
            logger.error(f"获取日线数据失败: {e}")
            return 0

    def _fetch_fundamental_data_with_fetcher(self, stock_codes: List[str] = None) -> int:
        """使用现有财务数据获取器获取基本面数据"""
        try:
            if not self.financial_data:
                return 0

            # {{ AURA-X: Modify - 直接使用DataFetcherManager获取股票基本信息. Approval: 寸止(ID:阶段1.3). }}
            # 使用现有的数据获取器
            df = self.data_fetcher.fetch_reference_data(
                data_type='stock_list',
                exchange='',
                list_status='L',
                fields=['ts_code', 'symbol', 'name', 'area', 'industry', 'market', 'list_date'],
                save=False,
                use_cache=True
            )

            if not df.empty:
                self._update_stock_basic_to_db(df)
                logger.info(f"通过财务数据获取器获取基本面数据成功: {len(df)} 条记录")
                return len(df)
            else:
                logger.warning("未获取到基本面数据")
                return 0

        except Exception as e:
            logger.error(f"获取基本面数据失败: {e}")
            return 0

    def _save_daily_data_to_db(self, df: pd.DataFrame):
        """保存日线数据到数据库"""
        try:
            if not self.db_manager:
                raise Exception("数据库管理器未初始化")

            if not self.db_manager.connect():
                raise Exception("数据库连接失败")

            self.db_manager.insert_dataframe('daily_price', df)
            self.db_manager.disconnect()
            logger.info(f"保存日线数据到MySQL数据库成功: {len(df)} 条记录")

        except Exception as e:
            logger.error(f"保存日线数据到数据库失败: {e}")
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        try:
            if not self.db_manager:
                raise Exception("数据库管理器未初始化")

            if not self.db_manager.connect():
                raise Exception("数据库连接失败")

            # 获取股票数量
            result = self.db_manager.query("SELECT COUNT(*) as count FROM stock_basic")
            stock_count = result.iloc[0]['count'] if not result.empty else 0

            # 获取数据记录数量
            result = self.db_manager.query("SELECT COUNT(*) as count FROM daily_price")
            data_count = result.iloc[0]['count'] if not result.empty else 0

            # 获取数据库大小
            result = self.db_manager.query("""
                SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS db_size_mb
                FROM information_schema.tables
                WHERE table_schema = DATABASE()
            """)
            db_size_mb = result.iloc[0]['db_size_mb'] if not result.empty else 0
            db_size = int((db_size_mb or 0) * 1024 * 1024)  # 转换为字节

            self.db_manager.disconnect()

            return {
                "stock_count": stock_count,
                "data_count": data_count,
                "database_size": db_size
            }

        except Exception as e:
            logger.error(f"获取系统统计失败: {e}")
            return {
                "stock_count": 0,
                "data_count": 0,
                "database_size": 0
            }

# 全局实例
web_data_integration = WebDataIntegration()
