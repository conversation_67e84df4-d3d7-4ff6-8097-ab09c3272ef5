"""
风险优化模块

该模块整合了风险管理和组合优化功能，提供基于风险的投资组合优化工具。
核心功能包括：
- 风险预算优化
- 尾部风险优化
- 风险情景分析
- 多目标优化
"""

from src.risk.optimization.interfaces import (
    RiskOptimizerInterface,
    RiskOptimizationException,
    RiskBudgetingMethod,
    OptimizationObjective
)
from src.risk.optimization.risk_optimizer import (
    RiskOptimizer,
    TailRiskOptimizer,
    RiskBudgetOptimizer,
    MultiObjectiveRiskOptimizer
)
from src.risk.optimization.risk_optimizer_factory import RiskOptimizerFactory

__all__ = [
    "RiskOptimizerInterface",
    "RiskOptimizationException",
    "RiskBudgetingMethod",
    "OptimizationObjective",
    "RiskOptimizer",
    "TailRiskOptimizer",
    "RiskBudgetOptimizer",
    "MultiObjectiveRiskOptimizer",
    "RiskOptimizerFactory"
]
