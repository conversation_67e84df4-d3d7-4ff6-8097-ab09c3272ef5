"""
风险优化器实现模块

提供风险优化器的具体实现，包括：
- 基础风险优化器
- 风险预算优化器
- 尾部风险优化器
- 多目标风险优化器
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
from scipy import optimize
import cvxpy as cp

from risk.optimization.interfaces import (
    RiskOptimizerInterface, 
    RiskBudgetingMethod, 
    OptimizationObjective,
    RiskOptimizationException
)
from src.portfolio.optimization.constraints.constraint_factory import ConstraintFactory
from risk.monitoring.var_monitor import VaRMonitor
from risk.monitoring.drawdown_monitor import DrawdownMonitor


class RiskOptimizer(RiskOptimizerInterface):
    """
    基础风险优化器
    
    实现了风险优化器接口的基础功能，包括风险指标计算和压力测试。
    这是一个通用的风险优化器，可以使用不同的风险度量和优化目标。
    """
    
    def __init__(
        self,
        name: str = "基础风险优化器",
        description: str = "结合风险管理和组合优化的基础优化器",
        risk_measure: str = "volatility",  # 'volatility', 'var', 'cvar', 'drawdown'
        objective: OptimizationObjective = OptimizationObjective.MINIMIZE_RISK,
        risk_aversion: float = 2.0,
        target_return: Optional[float] = None,
        max_drawdown_limit: Optional[float] = None,
        var_limit: Optional[float] = None,
        var_confidence: float = 0.95,
        **kwargs
    ):
        """
        初始化风险优化器
        
        参数:
            name: 优化器名称
            description: 优化器描述
            risk_measure: 风险度量方式，可选'volatility', 'var', 'cvar', 'drawdown'
            objective: 优化目标，默认为最小化风险
            risk_aversion: 风险厌恶系数
            target_return: 目标收益率
            max_drawdown_limit: 最大回撤限制
            var_limit: VaR限制
            var_confidence: VaR置信水平
            **kwargs: 其他参数
        """
        super().__init__(
            name=name,
            description=description,
            risk_aversion=risk_aversion,
            target_return=target_return,
            max_drawdown_limit=max_drawdown_limit,
            var_limit=var_limit,
            var_confidence=var_confidence,
            **kwargs
        )
        self._risk_measure = risk_measure
        self._objective = objective
        self._constraint_factory = ConstraintFactory()
        
    @property
    def risk_measure(self) -> str:
        """获取风险度量方式"""
        return self._risk_measure
    
    @risk_measure.setter
    def risk_measure(self, value: str) -> None:
        """设置风险度量方式"""
        valid_measures = ['volatility', 'var', 'cvar', 'drawdown']
        if value not in valid_measures:
            raise ValueError(f"风险度量方式必须是以下之一: {valid_measures}")
        self._risk_measure = value
        
    @property
    def objective(self) -> OptimizationObjective:
        """获取优化目标"""
        return self._objective
    
    @objective.setter
    def objective(self, value: OptimizationObjective) -> None:
        """设置优化目标"""
        self._objective = value
    
    def optimize(
        self,
        expected_returns: pd.Series = None,
        covariance_matrix: pd.DataFrame = None,
        asset_data: pd.DataFrame = None,
        constraints: Dict[str, Any] = None,
        **kwargs
    ) -> pd.Series:
        """
        执行投资组合优化
        
        参数:
            expected_returns: 期望收益率
            covariance_matrix: 协方差矩阵
            asset_data: 资产数据
            constraints: 约束条件
            **kwargs: 其他参数
            
        返回:
            优化后的资产权重
        """
        # 提取历史收益率
        historical_returns = None
        if asset_data is not None and 'returns' in kwargs:
            historical_returns = kwargs.get('returns')
        elif asset_data is not None and isinstance(asset_data, pd.DataFrame):
            if 'returns' in asset_data.columns:
                historical_returns = asset_data['returns']
                
        # 调用风险约束优化
        return self.optimize_with_risk_constraints(
            expected_returns=expected_returns,
            covariance_matrix=covariance_matrix,
            historical_returns=historical_returns,
            constraints=constraints,
            **kwargs
        )
    
    def optimize_with_risk_constraints(
        self,
        expected_returns: pd.Series = None,
        covariance_matrix: pd.DataFrame = None,
        historical_returns: pd.DataFrame = None,
        risk_targets: Dict[str, float] = None,
        risk_budgets: pd.Series = None,
        constraints: Dict[str, Any] = None,
        **kwargs
    ) -> pd.Series:
        """
        带风险约束的投资组合优化
        
        参数:
            expected_returns: 期望收益率
            covariance_matrix: 协方差矩阵
            historical_returns: 历史收益率
            risk_targets: 风险目标
            risk_budgets: 风险预算
            constraints: 其他约束条件
            **kwargs: 其他参数
            
        返回:
            优化后的资产权重
        """
        if covariance_matrix is None:
            raise ValueError("必须提供协方差矩阵")
            
        if self._objective == OptimizationObjective.MAXIMIZE_RETURN and expected_returns is None:
            raise ValueError("最大化收益目标下必须提供期望收益率")
            
        n_assets = len(covariance_matrix)
        
        # 初始化变量
        weights = cp.Variable(n_assets)
        risk = None
        objective = None
        
        # 根据风险度量设置风险表达式
        if self._risk_measure == 'volatility':
            risk = cp.quad_form(weights, covariance_matrix.values)
        elif self._risk_measure in ['var', 'cvar'] and historical_returns is not None:
            # 对于VaR和CVaR，我们需要线性化表示或使用场景近似
            # 这里简化处理，使用历史模拟法
            if historical_returns.shape[1] != n_assets:
                raise ValueError("历史收益率数据与资产数量不匹配")
                
            # 创建场景变量
            n_scenarios = len(historical_returns)
            scenario_returns = historical_returns.values @ weights
            var_aux = cp.Variable(1)
            
            if self._risk_measure == 'var':
                # VaR约束 - 简化实现
                var_constraints = []
                for i in range(n_scenarios):
                    var_constraints.append(-scenario_returns[i] <= var_aux)
                risk = var_aux
            else:  # CVaR
                # CVaR约束
                cvar_aux = cp.Variable(n_scenarios)
                alpha = 1 - self._var_confidence
                cvar_constraints = []
                for i in range(n_scenarios):
                    cvar_constraints.append(cvar_aux[i] >= -scenario_returns[i] - var_aux)
                    cvar_constraints.append(cvar_aux[i] >= 0)
                risk = var_aux + (1/alpha) * cp.sum(cvar_aux) / n_scenarios
        elif self._risk_measure == 'drawdown' and historical_returns is not None:
            # 对于回撤约束，也需要线性化表示
            # 这里使用简化的近似方法
            if historical_returns.shape[1] != n_assets:
                raise ValueError("历史收益率数据与资产数量不匹配")
                
            # 累积收益
            cum_returns = np.cumprod(1 + historical_returns.values, axis=0) - 1
            n_periods = len(cum_returns)
            
            # 创建变量表示高水位线和回撤
            hwm = cp.Variable(n_periods)
            drawdown = cp.Variable(n_periods)
            
            # 约束
            drawdown_constraints = []
            port_cum_returns = cum_returns @ weights
            
            for t in range(n_periods):
                if t == 0:
                    drawdown_constraints.append(hwm[t] == port_cum_returns[t])
                else:
                    drawdown_constraints.append(hwm[t] >= hwm[t-1])
                    drawdown_constraints.append(hwm[t] >= port_cum_returns[t])
                drawdown_constraints.append(drawdown[t] >= hwm[t] - port_cum_returns[t])
                
            risk = cp.max(drawdown)
        else:
            raise ValueError(f"无法使用所选风险度量: {self._risk_measure}，或缺少必要的历史收益率数据")
        
        # 设定目标函数
        if self._objective == OptimizationObjective.MINIMIZE_RISK:
            objective = risk
        elif self._objective == OptimizationObjective.MAXIMIZE_RETURN:
            if expected_returns is None:
                raise ValueError("最大化收益目标下必须提供期望收益率")
            objective = -weights @ expected_returns.values
        elif self._objective == OptimizationObjective.MAXIMIZE_SHARPE:
            if expected_returns is None:
                raise ValueError("最大化夏普比率目标下必须提供期望收益率")
            portfolio_return = weights @ expected_returns.values
            portfolio_risk = cp.sqrt(risk) if self._risk_measure == 'volatility' else risk
            # 注意：这不是严格的夏普比率，而是其近似值，真正的夏普最大化需要特殊处理
            objective = -portfolio_return / portfolio_risk
        elif self._objective == OptimizationObjective.MAXIMIZE_UTILITY:
            if expected_returns is None:
                raise ValueError("最大化效用函数目标下必须提供期望收益率")
            portfolio_return = weights @ expected_returns.values
            portfolio_risk = risk
            objective = -portfolio_return + self._risk_aversion * portfolio_risk
        else:
            raise ValueError(f"不支持的优化目标: {self._objective}")
        
        # 构建约束条件
        all_constraints = [cp.sum(weights) == 1, weights >= 0]  # 基本约束：权重和为1，非负
        
        # 添加用户定义的约束
        if constraints:
            user_constraints = self._constraint_factory.create_constraints(weights, constraints)
            all_constraints.extend(user_constraints)
            
        # 添加风险目标约束
        if risk_targets:
            if 'volatility' in risk_targets and self._risk_measure == 'volatility':
                vol_limit = risk_targets['volatility']
                all_constraints.append(cp.sqrt(risk) <= vol_limit)
            if 'var' in risk_targets and historical_returns is not None:
                var_limit = risk_targets['var']
                var_monitor = VaRMonitor(confidence=self._var_confidence)
                var_estimate = var_monitor.calculate_var(
                    historical_returns, 
                    method='historical'
                )
                var_scaling = var_limit / var_estimate
                all_constraints.append(risk <= var_scaling * var_estimate)
            if 'max_drawdown' in risk_targets and historical_returns is not None:
                dd_limit = risk_targets['max_drawdown']
                if self._risk_measure == 'drawdown':
                    all_constraints.append(risk <= dd_limit)
                else:
                    # 如果风险度量不是回撤，添加单独的回撤约束
                    pass  # 复杂的实现，这里简化处理
        
        # 目标收益率约束
        if self._target_return is not None and expected_returns is not None:
            all_constraints.append(weights @ expected_returns.values >= self._target_return)
            
        # 解决优化问题
        prob = cp.Problem(cp.Minimize(objective), all_constraints)
        try:
            prob.solve()
            
            if prob.status not in ["optimal", "optimal_inaccurate"]:
                raise RiskOptimizationException(f"优化未成功收敛，状态: {prob.status}")
                
            # 提取权重
            self._weights = pd.Series(weights.value, index=covariance_matrix.index)
            self._is_optimized = True
            
            # 计算指标
            self.calculate_metrics()
            self.calculate_risk_metrics(weights=self._weights, historical_returns=historical_returns)
            
            return self._weights
            
        except Exception as e:
            raise RiskOptimizationException(f"优化过程中出错: {str(e)}")
    
    def calculate_metrics(self) -> Dict[str, Any]:
        """
        计算优化相关指标
        
        返回:
            指标字典
        """
        if not self._is_optimized or self._weights is None:
            raise RiskOptimizationException("请先执行优化")
            
        # 为简化起见，此处仅计算基本指标
        self._metrics = {
            "num_assets": len(self._weights),
            "nonzero_weights": np.sum(self._weights > 1e-5),
            "max_weight": self._weights.max(),
            "min_weight": self._weights[self._weights > 1e-5].min() if np.any(self._weights > 1e-5) else 0
        }
        
        return self._metrics
    
    def calculate_risk_metrics(
        self,
        weights: pd.Series = None,
        historical_returns: pd.DataFrame = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        计算投资组合风险指标
        
        参数:
            weights: 资产权重
            historical_returns: 历史收益率
            **kwargs: 其他参数
            
        返回:
            风险指标字典
        """
        if weights is None:
            if not self._is_optimized or self._weights is None:
                raise RiskOptimizationException("请先执行优化或提供权重")
            weights = self._weights
            
        risk_metrics = {}
        
        # 如果有协方差矩阵，计算波动率
        covariance_matrix = kwargs.get('covariance_matrix')
        if covariance_matrix is not None:
            portfolio_variance = weights @ covariance_matrix @ weights
            risk_metrics['volatility'] = np.sqrt(portfolio_variance)
            
        # 如果有历史收益率，计算其他风险指标
        if historical_returns is not None:
            if not isinstance(historical_returns, pd.DataFrame):
                raise ValueError("历史收益率必须是DataFrame")
                
            # 确保权重的索引与历史收益率的列匹配
            common_assets = [asset for asset in weights.index if asset in historical_returns.columns]
            if not common_assets:
                raise ValueError("权重和历史收益率没有共同的资产")
                
            # 计算投资组合历史收益率
            portfolio_returns = (weights[common_assets] * historical_returns[common_assets]).sum(axis=1)
            
            # 计算VaR
            var_monitor = VaRMonitor(confidence=self._var_confidence)
            var_result = var_monitor.calculate_var(portfolio_returns, method='historical')
            risk_metrics['var'] = var_result
            
            # 计算CVaR（条件VaR）
            cvar_result = var_monitor.calculate_cvar(portfolio_returns, method='historical')
            risk_metrics['cvar'] = cvar_result
            
            # 计算最大回撤
            dd_monitor = DrawdownMonitor()
            max_dd, avg_dd, dd_duration = dd_monitor.calculate_drawdown_stats(portfolio_returns)
            risk_metrics['max_drawdown'] = max_dd
            risk_metrics['avg_drawdown'] = avg_dd
            risk_metrics['avg_drawdown_duration'] = dd_duration
            
            # 计算偏度和峰度
            risk_metrics['skewness'] = portfolio_returns.skew()
            risk_metrics['kurtosis'] = portfolio_returns.kurt()
            
        self._risk_metrics = risk_metrics
        return risk_metrics
    
    def run_stress_test(
        self,
        weights: pd.Series = None,
        scenarios: Dict[str, pd.DataFrame] = None,
        **kwargs
    ) -> Dict[str, pd.Series]:
        """
        执行压力测试
        
        参数:
            weights: 资产权重
            scenarios: 情景字典，键为情景名称，值为情景下的资产收益率
            **kwargs: 其他参数
            
        返回:
            压力测试结果，键为情景名称，值为情景下的投资组合表现
        """
        if weights is None:
            if not self._is_optimized or self._weights is None:
                raise RiskOptimizationException("请先执行优化或提供权重")
            weights = self._weights
            
        if scenarios is None or not scenarios:
            raise ValueError("必须提供情景数据")
            
        results = {}
        
        for scenario_name, scenario_returns in scenarios.items():
            if not isinstance(scenario_returns, pd.DataFrame):
                raise ValueError(f"情景 '{scenario_name}' 的收益率必须是DataFrame")
                
            # 确保权重的索引与情景收益率的列匹配
            common_assets = [asset for asset in weights.index if asset in scenario_returns.columns]
            if not common_assets:
                raise ValueError(f"权重和情景 '{scenario_name}' 收益率没有共同的资产")
                
            # 计算投资组合在该情景下的收益率
            portfolio_returns = (weights[common_assets] * scenario_returns[common_assets]).sum(axis=1)
            
            # 计算情景下的关键指标
            scenario_result = {}
            scenario_result['return'] = portfolio_returns
            scenario_result['cumulative_return'] = (1 + portfolio_returns).cumprod() - 1
            scenario_result['volatility'] = portfolio_returns.std()
            scenario_result['max_drawdown'] = (scenario_result['cumulative_return'].cummax() - scenario_result['cumulative_return']).max()
            
            results[scenario_name] = scenario_result
            
        return results


class RiskBudgetOptimizer(RiskOptimizer):
    """
    风险预算优化器
    
    基于风险贡献的投资组合优化。
    """
    
    def __init__(
        self,
        name: str = "风险预算优化器",
        description: str = "基于风险预算的投资组合优化",
        risk_measure: str = "volatility",
        budgeting_method: RiskBudgetingMethod = RiskBudgetingMethod.EQUAL_RISK,
        **kwargs
    ):
        """
        初始化风险预算优化器
        
        参数:
            name: 优化器名称
            description: 优化器描述
            risk_measure: 风险度量方式
            budgeting_method: 风险预算方法
            **kwargs: 其他参数
        """
        super().__init__(
            name=name,
            description=description,
            risk_measure=risk_measure,
            objective=OptimizationObjective.MINIMIZE_RISK,  # 风险预算优化默认为最小化风险
            **kwargs
        )
        self._budgeting_method = budgeting_method
        
    @property
    def budgeting_method(self) -> RiskBudgetingMethod:
        """获取风险预算方法"""
        return self._budgeting_method
    
    @budgeting_method.setter
    def budgeting_method(self, value: RiskBudgetingMethod) -> None:
        """设置风险预算方法"""
        self._budgeting_method = value
        
    def optimize_with_risk_constraints(
        self,
        expected_returns: pd.Series = None,
        covariance_matrix: pd.DataFrame = None,
        historical_returns: pd.DataFrame = None,
        risk_targets: Dict[str, float] = None,
        risk_budgets: pd.Series = None,
        constraints: Dict[str, Any] = None,
        **kwargs
    ) -> pd.Series:
        """
        带风险约束的投资组合优化
        
        参数:
            expected_returns: 期望收益率
            covariance_matrix: 协方差矩阵
            historical_returns: 历史收益率
            risk_targets: 风险目标
            risk_budgets: 风险预算
            constraints: 其他约束条件
            **kwargs: 其他参数
            
        返回:
            优化后的资产权重
        """
        if covariance_matrix is None:
            raise ValueError("必须提供协方差矩阵")
            
        n_assets = len(covariance_matrix)
        
        # 确定风险预算
        if self._budgeting_method == RiskBudgetingMethod.EQUAL_RISK:
            # 等风险贡献
            budgets = pd.Series(1.0 / n_assets, index=covariance_matrix.index)
        elif self._budgeting_method == RiskBudgetingMethod.PROPORTIONAL and expected_returns is not None:
            # 按收益率比例分配风险
            total_return = expected_returns.sum()
            if abs(total_return) < 1e-8:
                budgets = pd.Series(1.0 / n_assets, index=covariance_matrix.index)
            else:
                budgets = expected_returns / total_return
        elif self._budgeting_method == RiskBudgetingMethod.CUSTOM and risk_budgets is not None:
            # 自定义风险预算
            if len(risk_budgets) != n_assets:
                raise ValueError("风险预算数量必须与资产数量相同")
            budgets = risk_budgets
        else:
            # 默认为等风险贡献
            budgets = pd.Series(1.0 / n_assets, index=covariance_matrix.index)
            
        # 风险平价优化
        def risk_budget_objective(weights, cov_matrix, budgets):
            # 计算投资组合总风险
            portfolio_vol = np.sqrt(np.dot(weights, np.dot(cov_matrix, weights)))
            
            # 计算边际风险贡献
            marginal_risk_contribution = np.dot(cov_matrix, weights) / portfolio_vol
            
            # 计算风险贡献
            risk_contribution = weights * marginal_risk_contribution
            
            # 目标是让风险贡献与预算成正比
            target_ratio = risk_contribution / budgets
            return ((target_ratio - target_ratio.mean()) ** 2).sum()
            
        # 初始权重为等权
        initial_weights = np.ones(n_assets) / n_assets
        
        # 优化约束
        bounds = [(0.0, 1.0) for _ in range(n_assets)]
        weight_constraint = {'type': 'eq', 'fun': lambda w: np.sum(w) - 1.0}
        
        # 执行优化
        result = optimize.minimize(
            risk_budget_objective,
            initial_weights,
            args=(covariance_matrix.values, budgets.values),
            bounds=bounds,
            constraints=[weight_constraint],
            method='SLSQP'
        )
        
        if not result.success:
            raise RiskOptimizationException(f"风险预算优化失败: {result.message}")
            
        # 提取权重
        self._weights = pd.Series(result.x, index=covariance_matrix.index)
        self._is_optimized = True
        
        # 计算指标
        self.calculate_metrics()
        self.calculate_risk_metrics(weights=self._weights, historical_returns=historical_returns, covariance_matrix=covariance_matrix)
        
        return self._weights


class TailRiskOptimizer(RiskOptimizer):
    """
    尾部风险优化器
    
    专注于最小化尾部风险（VaR或CVaR）的优化器。
    """
    
    def __init__(
        self,
        name: str = "尾部风险优化器",
        description: str = "专注于最小化尾部风险的优化器",
        risk_measure: str = "cvar",  # 'var', 'cvar'
        var_confidence: float = 0.95,
        **kwargs
    ):
        """
        初始化尾部风险优化器
        
        参数:
            name: 优化器名称
            description: 优化器描述
            risk_measure: 风险度量方式，推荐'cvar'
            var_confidence: VaR/CVaR置信水平
            **kwargs: 其他参数
        """
        # 确保风险度量是var或cvar
        if risk_measure not in ['var', 'cvar']:
            raise ValueError("尾部风险优化器只支持'var'和'cvar'风险度量")
            
        super().__init__(
            name=name,
            description=description,
            risk_measure=risk_measure,
            objective=OptimizationObjective.MINIMIZE_RISK,  # 尾部风险优化默认为最小化风险
            var_confidence=var_confidence,
            **kwargs
        )


class MultiObjectiveRiskOptimizer(RiskOptimizer):
    """
    多目标风险优化器
    
    结合多个优化目标的投资组合优化。
    """
    
    def __init__(
        self,
        name: str = "多目标风险优化器",
        description: str = "结合多个优化目标的投资组合优化",
        objectives: Dict[OptimizationObjective, float] = None,
        risk_measures: Dict[str, float] = None,
        **kwargs
    ):
        """
        初始化多目标风险优化器
        
        参数:
            name: 优化器名称
            description: 优化器描述
            objectives: 优化目标及其权重字典
            risk_measures: 风险度量及其权重字典
            **kwargs: 其他参数
        """
        # 如果未指定优化目标，默认为最小化风险和最大化收益的等权组合
        if objectives is None:
            objectives = {
                OptimizationObjective.MINIMIZE_RISK: 0.5,
                OptimizationObjective.MAXIMIZE_RETURN: 0.5
            }
            
        # 如果未指定风险度量，默认为波动率
        if risk_measures is None:
            risk_measures = {'volatility': 1.0}
            
        super().__init__(
            name=name,
            description=description,
            # 使用值最大的风险度量作为主要度量
            risk_measure=max(risk_measures.items(), key=lambda x: x[1])[0],
            # 使用复合目标
            objective=OptimizationObjective.CUSTOM,
            **kwargs
        )
        self._objectives = objectives
        self._risk_measures = risk_measures
        
    @property
    def objectives(self) -> Dict[OptimizationObjective, float]:
        """获取优化目标及其权重"""
        return self._objectives.copy()
    
    @property
    def risk_measures(self) -> Dict[str, float]:
        """获取风险度量及其权重"""
        return self._risk_measures.copy()
    
    def optimize_with_risk_constraints(
        self,
        expected_returns: pd.Series = None,
        covariance_matrix: pd.DataFrame = None,
        historical_returns: pd.DataFrame = None,
        risk_targets: Dict[str, float] = None,
        risk_budgets: pd.Series = None,
        constraints: Dict[str, Any] = None,
        **kwargs
    ) -> pd.Series:
        """
        带风险约束的投资组合优化
        
        参数:
            expected_returns: 期望收益率
            covariance_matrix: 协方差矩阵
            historical_returns: 历史收益率
            risk_targets: 风险目标
            risk_budgets: 风险预算
            constraints: 其他约束条件
            **kwargs: 其他参数
            
        返回:
            优化后的资产权重
        """
        # 多目标优化需要更复杂的实现，这里简化为线性加权组合
        # 在实际应用中，可能需要Pareto最优解或其他多目标优化技术
        
        if covariance_matrix is None:
            raise ValueError("必须提供协方差矩阵")
            
        if OptimizationObjective.MAXIMIZE_RETURN in self._objectives and expected_returns is None:
            raise ValueError("最大化收益目标下必须提供期望收益率")
            
        n_assets = len(covariance_matrix)
        
        # 初始化变量
        weights = cp.Variable(n_assets)
        
        # 构建复合目标函数
        objectives = []
        
        for obj, weight in self._objectives.items():
            if obj == OptimizationObjective.MINIMIZE_RISK:
                # 对于每个风险度量，添加其加权目标
                for risk_name, risk_weight in self._risk_measures.items():
                    if risk_name == 'volatility':
                        risk = cp.quad_form(weights, covariance_matrix.values)
                        objectives.append(weight * risk_weight * cp.sqrt(risk))
                    elif risk_name in ['var', 'cvar'] and historical_returns is not None:
                        # 对于VaR和CVaR，使用简化实现
                        # 真实实现需要更复杂的建模
                        pass
            elif obj == OptimizationObjective.MAXIMIZE_RETURN:
                if expected_returns is not None:
                    portfolio_return = weights @ expected_returns.values
                    objectives.append(-weight * portfolio_return)  # 负号是因为我们最小化
            elif obj == OptimizationObjective.MAXIMIZE_SHARPE:
                if expected_returns is not None:
                    portfolio_return = weights @ expected_returns.values
                    portfolio_risk = cp.sqrt(cp.quad_form(weights, covariance_matrix.values))
                    # 近似夏普比率最大化
                    objectives.append(-weight * portfolio_return / portfolio_risk)
        
        # 合并目标
        if not objectives:
            raise ValueError("未找到有效的优化目标")
            
        objective = sum(objectives)
        
        # 构建约束条件
        all_constraints = [cp.sum(weights) == 1, weights >= 0]  # 基本约束：权重和为1，非负
        
        # 添加用户定义的约束
        if constraints:
            user_constraints = self._constraint_factory.create_constraints(weights, constraints)
            all_constraints.extend(user_constraints)
            
        # 添加风险目标约束
        if risk_targets:
            for risk_name, limit in risk_targets.items():
                if risk_name == 'volatility':
                    portfolio_risk = cp.sqrt(cp.quad_form(weights, covariance_matrix.values))
                    all_constraints.append(portfolio_risk <= limit)
                # 其他风险约束类似实现
                
        # 目标收益率约束
        if self._target_return is not None and expected_returns is not None:
            all_constraints.append(weights @ expected_returns.values >= self._target_return)
            
        # 解决优化问题
        prob = cp.Problem(cp.Minimize(objective), all_constraints)
        try:
            prob.solve()
            
            if prob.status not in ["optimal", "optimal_inaccurate"]:
                raise RiskOptimizationException(f"优化未成功收敛，状态: {prob.status}")
                
            # 提取权重
            self._weights = pd.Series(weights.value, index=covariance_matrix.index)
            self._is_optimized = True
            
            # 计算指标
            self.calculate_metrics()
            self.calculate_risk_metrics(weights=self._weights, historical_returns=historical_returns, covariance_matrix=covariance_matrix)
            
            return self._weights
            
        except Exception as e:
            raise RiskOptimizationException(f"优化过程中出错: {str(e)}")
