"""
风险优化器工厂模块

提供创建各种风险优化器实例的工厂类。
"""

from typing import Dict, Optional, Any, Union

from risk.optimization.interfaces import (
    RiskOptimizerInterface,
    OptimizationObjective,
    RiskBudgetingMethod
)
from risk.optimization.risk_optimizer import (
    RiskOptimizer,
    RiskBudgetOptimizer,
    TailRiskOptimizer,
    MultiObjectiveRiskOptimizer
)


class RiskOptimizerFactory:
    """
    风险优化器工厂类
    
    用于创建各种类型的风险优化器实例。
    """
    
    @staticmethod
    def create_optimizer(
        optimizer_type: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        risk_measure: str = "volatility",
        objective: OptimizationObjective = OptimizationObjective.MINIMIZE_RISK,
        risk_aversion: float = 2.0,
        var_confidence: float = 0.95,
        budgeting_method: RiskBudgetingMethod = RiskBudgetingMethod.EQUAL_RISK,
        objectives: Optional[Dict[OptimizationObjective, float]] = None,
        risk_measures: Optional[Dict[str, float]] = None,
        **kwargs
    ) -> RiskOptimizerInterface:
        """
        创建风险优化器
        
        参数:
            optimizer_type: 优化器类型，可选'basic', 'risk_budget', 'tail_risk', 'multi_objective'
            name: 优化器名称
            description: 优化器描述
            risk_measure: 风险度量方式
            objective: 优化目标
            risk_aversion: 风险厌恶系数
            var_confidence: VaR置信水平
            budgeting_method: 风险预算方法（适用于风险预算优化器）
            objectives: 多目标优化的目标权重（适用于多目标优化器）
            risk_measures: 多风险度量的权重（适用于多目标优化器）
            **kwargs: 其他参数
            
        返回:
            风险优化器实例
            
        抛出:
            ValueError: 无效的优化器类型
        """
        if optimizer_type == 'basic':
            return RiskOptimizer(
                name=name or "基础风险优化器",
                description=description or "基础风险优化实现",
                risk_measure=risk_measure,
                objective=objective,
                risk_aversion=risk_aversion,
                var_confidence=var_confidence,
                **kwargs
            )
        
        elif optimizer_type == 'risk_budget':
            return RiskBudgetOptimizer(
                name=name or "风险预算优化器",
                description=description or "基于风险预算的投资组合优化",
                risk_measure=risk_measure,
                budgeting_method=budgeting_method,
                var_confidence=var_confidence,
                **kwargs
            )
        
        elif optimizer_type == 'tail_risk':
            # 尾部风险默认使用CVaR作为风险度量
            tail_risk_measure = risk_measure if risk_measure in ['var', 'cvar'] else 'cvar'
            return TailRiskOptimizer(
                name=name or "尾部风险优化器",
                description=description or "专注于最小化尾部风险的优化器",
                risk_measure=tail_risk_measure,
                var_confidence=var_confidence,
                **kwargs
            )
        
        elif optimizer_type == 'multi_objective':
            return MultiObjectiveRiskOptimizer(
                name=name or "多目标风险优化器",
                description=description or "结合多个优化目标的投资组合优化",
                objectives=objectives,
                risk_measures=risk_measures,
                risk_aversion=risk_aversion,
                var_confidence=var_confidence,
                **kwargs
            )
        
        else:
            raise ValueError(f"无效的优化器类型: {optimizer_type}, 可选类型: 'basic', 'risk_budget', 'tail_risk', 'multi_objective'")
    
    @staticmethod
    def create_risk_optimizer(**kwargs) -> RiskOptimizer:
        """创建基础风险优化器"""
        return RiskOptimizerFactory.create_optimizer('basic', **kwargs)
    
    @staticmethod
    def create_risk_budget_optimizer(**kwargs) -> RiskBudgetOptimizer:
        """创建风险预算优化器"""
        return RiskOptimizerFactory.create_optimizer('risk_budget', **kwargs)
    
    @staticmethod
    def create_tail_risk_optimizer(**kwargs) -> TailRiskOptimizer:
        """创建尾部风险优化器"""
        return RiskOptimizerFactory.create_optimizer('tail_risk', **kwargs)
    
    @staticmethod
    def create_multi_objective_optimizer(**kwargs) -> MultiObjectiveRiskOptimizer:
        """创建多目标风险优化器"""
        return RiskOptimizerFactory.create_optimizer('multi_objective', **kwargs)
