"""
风险优化接口模块

定义风险优化相关的接口和异常类。
"""
from abc import ABC, abstractmethod
from enum import Enum, auto
from typing import Dict, List, Optional, Union, Any, Tuple
import numpy as np
import pandas as pd

from src.portfolio.optimization.interfaces import PortfolioOptimizerInterface, PortfolioOptimizationException


class RiskOptimizationException(PortfolioOptimizationException):
    """风险优化异常基类"""
    pass


class RiskBudgetingMethod(Enum):
    """风险预算方法枚举"""
    EQUAL_RISK = auto()  # 等风险贡献
    PROPORTIONAL = auto()  # 按比例风险贡献
    MINIMUM_RISK = auto()  # 最小风险
    MAXIMUM_DIVERSIFICATION = auto()  # 最大分散化
    CUSTOM = auto()  # 自定义风险预算


class OptimizationObjective(Enum):
    """优化目标枚举"""
    MINIMIZE_RISK = auto()  # 最小化风险
    MAXIMIZE_RETURN = auto()  # 最大化收益
    MAXIMIZE_SHARPE = auto()  # 最大化夏普比率
    MINIMIZE_CVAR = auto()  # 最小化条件风险价值
    MINIMIZE_VAR = auto()  # 最小化风险价值
    MAXIMIZE_UTILITY = auto()  # 最大化效用函数
    CUSTOM = auto()  # 自定义目标


class RiskOptimizerInterface(PortfolioOptimizerInterface, ABC):
    """
    风险优化器接口类
    
    继承自投资组合优化器接口，添加风险管理相关功能。
    所有风险优化算法必须实现此接口。
    """
    
    def __init__(
        self, 
        name: str = "",
        description: str = "",
        risk_aversion: float = 2.0,
        target_return: Optional[float] = None,
        max_drawdown_limit: Optional[float] = None,
        var_limit: Optional[float] = None,
        var_confidence: float = 0.95,
        **kwargs
    ):
        """
        初始化风险优化器
        
        参数:
            name: 优化器名称
            description: 优化器描述
            risk_aversion: 风险厌恶系数
            target_return: 目标收益率
            max_drawdown_limit: 最大回撤限制
            var_limit: VaR限制
            var_confidence: VaR置信水平
            **kwargs: 其他参数
        """
        super().__init__(name=name, description=description, **kwargs)
        self._risk_aversion = risk_aversion
        self._target_return = target_return
        self._max_drawdown_limit = max_drawdown_limit
        self._var_limit = var_limit
        self._var_confidence = var_confidence
        self._risk_metrics = {}
        
    @property
    def risk_aversion(self) -> float:
        """获取风险厌恶系数"""
        return self._risk_aversion
    
    @risk_aversion.setter
    def risk_aversion(self, value: float) -> None:
        """设置风险厌恶系数"""
        if value <= 0:
            raise ValueError("风险厌恶系数必须为正数")
        self._risk_aversion = value
        
    @property
    def target_return(self) -> Optional[float]:
        """获取目标收益率"""
        return self._target_return
    
    @target_return.setter
    def target_return(self, value: Optional[float]) -> None:
        """设置目标收益率"""
        self._target_return = value
        
    @property
    def max_drawdown_limit(self) -> Optional[float]:
        """获取最大回撤限制"""
        return self._max_drawdown_limit
    
    @max_drawdown_limit.setter
    def max_drawdown_limit(self, value: Optional[float]) -> None:
        """设置最大回撤限制"""
        if value is not None and (value <= 0 or value >= 1):
            raise ValueError("最大回撤限制必须在(0,1)范围内")
        self._max_drawdown_limit = value
        
    @property
    def var_limit(self) -> Optional[float]:
        """获取VaR限制"""
        return self._var_limit
    
    @var_limit.setter
    def var_limit(self, value: Optional[float]) -> None:
        """设置VaR限制"""
        if value is not None and value <= 0:
            raise ValueError("VaR限制必须为正数")
        self._var_limit = value
        
    @property
    def var_confidence(self) -> float:
        """获取VaR置信水平"""
        return self._var_confidence
    
    @var_confidence.setter
    def var_confidence(self, value: float) -> None:
        """设置VaR置信水平"""
        if value <= 0 or value >= 1:
            raise ValueError("VaR置信水平必须在(0,1)范围内")
        self._var_confidence = value
        
    @property
    def risk_metrics(self) -> Dict[str, Any]:
        """获取风险指标"""
        return self._risk_metrics.copy()
    
    @abstractmethod
    def optimize_with_risk_constraints(
        self,
        expected_returns: pd.Series = None,
        covariance_matrix: pd.DataFrame = None,
        historical_returns: pd.DataFrame = None,
        risk_targets: Dict[str, float] = None,
        risk_budgets: pd.Series = None,
        constraints: Dict[str, Any] = None,
        **kwargs
    ) -> pd.Series:
        """
        带风险约束的投资组合优化
        
        参数:
            expected_returns: 期望收益率
            covariance_matrix: 协方差矩阵
            historical_returns: 历史收益率
            risk_targets: 风险目标（如VaR限制、波动率限制等）
            risk_budgets: 风险预算
            constraints: 其他约束条件
            **kwargs: 其他参数
            
        返回:
            优化后的资产权重
        """
        pass
    
    @abstractmethod
    def calculate_risk_metrics(
        self,
        weights: pd.Series = None,
        historical_returns: pd.DataFrame = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        计算投资组合风险指标
        
        参数:
            weights: 资产权重
            historical_returns: 历史收益率
            **kwargs: 其他参数
            
        返回:
            风险指标字典
        """
        pass
    
    @abstractmethod
    def run_stress_test(
        self,
        weights: pd.Series = None,
        scenarios: Dict[str, pd.DataFrame] = None,
        **kwargs
    ) -> Dict[str, pd.Series]:
        """
        执行压力测试
        
        参数:
            weights: 资产权重
            scenarios: 情景字典，键为情景名称，值为情景下的收益率
            **kwargs: 其他参数
            
        返回:
            压力测试结果
        """
        pass
