"""
风险监控接口模块
- 定义风险监控的标准接口
- 为各种风险监控器提供统一的接口规范
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
import pandas as pd
import numpy as np

class RiskMonitorException(Exception):
    """风险监控异常的基类"""
    pass

class RiskMonitorInterface(ABC):
    """
    风险监控接口
    所有风险监控器必须实现此接口
    """
    
    @abstractmethod
    def calculate(self, returns: pd.Series, **kwargs) -> Dict[str, Any]:
        """
        计算风险指标
        
        参数:
            returns: 收益率序列
            **kwargs: 其他参数
            
        返回:
            Dict[str, Any]: 包含风险指标的字典
        """
        pass
    
    @abstractmethod
    def is_risk_exceeded(self, current_value: float, threshold: float) -> bool:
        """
        判断风险是否超过阈值
        
        参数:
            current_value: 当前风险指标值
            threshold: 风险阈值
            
        返回:
            bool: 是否超过阈值
        """
        pass
    
    @abstractmethod
    def get_risk_level(self, current_value: float) -> str:
        """
        获取风险等级
        
        参数:
            current_value: 当前风险指标值
            
        返回:
            str: 风险等级，例如 "低", "中", "高"
        """
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """
        获取风险监控器的描述信息
        
        返回:
            str: 描述信息
        """
        pass
    
    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取风险监控器的参数
        
        返回:
            Dict[str, Any]: 参数字典
        """
        pass
    
    @abstractmethod
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """
        设置风险监控器的参数
        
        参数:
            parameters: 参数字典
        """
        pass

