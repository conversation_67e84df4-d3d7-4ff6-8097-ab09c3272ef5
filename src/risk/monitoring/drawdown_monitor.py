"""
回撤风险监控器
- 监控投资组合回撤风险
- 检测是否超过回撤阈值
"""

from typing import Dict, List, Any, Optional, Union
import pandas as pd
import numpy as np

from src.risk.monitoring.monitor_interface import RiskMonitorInterface


class DrawdownMonitor(RiskMonitorInterface):
    """回撤风险监控类，用于监控策略/投资组合的回撤风险"""
    
    def __init__(self, name: str = "DrawdownMonitor", 
                 window: int = None, 
                 risk_levels: Optional[Dict[str, float]] = None):
        """
        初始化回撤监控器
        
        参数:
            name: 监控器名称
            window: 计算滚动回撤的窗口大小，None表示使用全部历史数据
            risk_levels: 风险等级阈值，格式为 {等级: 阈值}
                        例如 {"低": -0.05, "中": -0.10, "高": -0.15}
        """
        self.name = name
        self.window = window
        
        # 默认风险等级阈值
        self.risk_levels = risk_levels or {
            "低": -0.05,  # 5%回撤
            "中": -0.10,  # 10%回撤
            "高": -0.15   # 15%回撤
        }
    
    def calculate(self, returns: pd.Series, **kwargs) -> Dict[str, Any]:
        """
        计算回撤风险指标
        
        参数:
            returns: 收益率序列
            **kwargs: 其他参数
            
        返回:
            Dict[str, Any]: 包含回撤风险指标的字典
        """
        # 计算累积收益
        cumulative_returns = (1 + returns).cumprod()
        
        # 计算峰值
        if self.window:
            # 使用滚动窗口计算峰值
            peak = cumulative_returns.rolling(window=self.window, min_periods=1).max()
        else:
            # 使用全部历史数据计算峰值
            peak = cumulative_returns.cummax()
        
        # 计算回撤
        drawdown = (cumulative_returns / peak) - 1
        
        # 计算最大回撤
        max_drawdown = drawdown.min()
        
        # 当前回撤
        current_drawdown = drawdown.iloc[-1]
        
        # 回撤持续时间
        in_drawdown = drawdown < 0
        # 将布尔序列转换为整数，避免pandas的填充警告
        shifted_in_drawdown = in_drawdown.astype(int).shift(1).fillna(0).astype(bool)
        is_peak = ~in_drawdown & shifted_in_drawdown
        drawdown_start = pd.Series(returns.index, index=returns.index)[is_peak].shift(-1)
        
        current_drawdown_start = None
        drawdown_duration = 0
        
        if current_drawdown < 0:
            # 当前处于回撤状态，计算持续时间
            last_peak_idx = drawdown_start.last_valid_index()
            if last_peak_idx is not None:
                current_drawdown_start = drawdown_start[last_peak_idx]
                if current_drawdown_start is not None:
                    drawdown_duration = (returns.index[-1] - current_drawdown_start).days
        
        # 计算其他指标
        underwater_periods = (drawdown < 0).sum()
        pct_underwater = underwater_periods / len(returns) if len(returns) > 0 else 0
        
        # 恢复期：从最大回撤到恢复所需的时间
        if max_drawdown < 0:
            max_dd_idx = drawdown.idxmin()
            if max_dd_idx is not None and max_dd_idx < returns.index[-1]:
                # 检查是否已恢复
                recovered_idx = None
                peak_at_max_dd = peak.loc[max_dd_idx]  # 获取最大回撤点的峰值
                
                for i in range(returns.index.get_loc(max_dd_idx) + 1, len(returns)):
                    if cumulative_returns.iloc[i] >= peak_at_max_dd:
                        recovered_idx = returns.index[i]
                        break
                
                recovery_days = (recovered_idx - max_dd_idx).days if recovered_idx else None
            else:
                recovery_days = None
        else:
            recovery_days = 0
        
        return {
            "max_drawdown": max_drawdown,
            "current_drawdown": current_drawdown,
            "underwater_periods": underwater_periods,
            "pct_underwater": pct_underwater,
            "drawdown_duration": drawdown_duration,
            "recovery_days": recovery_days,
            "drawdown_series": drawdown
        }
    
    def is_risk_exceeded(self, current_value: float, threshold: float) -> bool:
        """
        判断回撤风险是否超过阈值
        
        参数:
            current_value: 当前回撤值（应为负数或零）
            threshold: 回撤阈值（应为负数或零）
            
        返回:
            bool: 是否超过阈值
        """
        # 回撤为负值，所以当 current_value < threshold 时表示风险超过阈值
        return current_value < threshold
    
    def get_risk_level(self, current_value: float) -> str:
        """
        获取风险等级
        
        参数:
            current_value: 当前回撤值
            
        返回:
            str: 风险等级，例如 "低", "中", "高"
        """
        for level, threshold in sorted(self.risk_levels.items(), key=lambda x: x[1], reverse=True):
            if current_value <= threshold:
                return level
        
        return "安全"  # 如果没有超过任何阈值
    
    def get_description(self) -> str:
        """
        获取风险监控器的描述信息
        
        返回:
            str: 描述信息
        """
        window_str = f"{self.window}日" if self.window else "全历史"
        thresholds = ", ".join([f"{level}风险: {threshold:.1%}" for level, threshold in self.risk_levels.items()])
        return f"回撤监控器 ({window_str}): {thresholds}"
    
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取风险监控器的参数
        
        返回:
            Dict[str, Any]: 参数字典
        """
        return {
            "name": self.name,
            "window": self.window,
            "risk_levels": self.risk_levels.copy()
        }
    
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """
        设置风险监控器的参数
        
        参数:
            parameters: 参数字典
        """
        if "name" in parameters:
            self.name = parameters["name"]
        
        if "window" in parameters:
            self.window = parameters["window"]
        
        if "risk_levels" in parameters:
            self.risk_levels = parameters["risk_levels"].copy()

