"""
VaR风险监控器
- 计算和监控投资组合的风险价值(VaR)指标
- 支持多种VaR计算方法
"""

from typing import Dict, List, Any, Optional, Union, Literal, Tuple
import pandas as pd
import numpy as np
import scipy.stats as stats

from src.risk.monitoring.monitor_interface import RiskMonitorInterface


class VaRMonitor(RiskMonitorInterface):
    """VaR（风险价值）监控类，计算和监控风险价值指标"""
    
    def __init__(self, 
                 name: str = "VaRMonitor", 
                 method: Literal["historical", "parametric", "monte_carlo"] = "historical",
                 confidence_level: float = 0.95,
                 time_horizon: int = 1,
                 rolling_window: int = 252,
                 risk_levels: Optional[Dict[str, float]] = None):
        """
        初始化VaR监控器
        
        参数:
            name: 监控器名称
            method: VaR计算方法 ('historical', 'parametric', 'monte_carlo')
            confidence_level: 置信水平，默认0.95 (95%)
            time_horizon: 时间周期，默认1（单位与收益率相同）
            rolling_window: 滚动窗口大小，默认252（约一年交易日）
            risk_levels: 风险等级阈值，格式为 {等级: 阈值}
                        例如 {"低": -0.01, "中": -0.02, "高": -0.03}
        """
        self.name = name
        self.method = method
        self.confidence_level = confidence_level
        self.time_horizon = time_horizon
        self.rolling_window = rolling_window
        
        # 默认风险等级阈值（VaR为负值）
        self.risk_levels = risk_levels or {
            "低": -0.01,  # 1%日VaR
            "中": -0.02,  # 2%日VaR
            "高": -0.03   # 3%日VaR
        }
        
        # 蒙特卡洛模拟参数
        self.monte_carlo_simulations = 10000
    
    def calculate(self, returns: pd.Series, **kwargs) -> Dict[str, Any]:
        """
        计算VaR指标
        
        参数:
            returns: 收益率序列
            **kwargs: 其他参数，可包括:
                - portfolio_value: 投资组合当前价值
                - distributions: 分布假设 ('normal', 't', 'empirical')
                - monte_carlo_sims: 蒙特卡洛模拟次数
            
        返回:
            Dict[str, Any]: 包含VaR指标的字典
        """
        if len(returns) < 3:
            return {
                "var": 0.0,
                "cvar": 0.0,
                "var_pct": 0.0,
                "cvar_pct": 0.0
            }
        
        # 获取参数
        portfolio_value = kwargs.get("portfolio_value", 1.0)
        dist_type = kwargs.get("distribution", "empirical")
        mc_sims = kwargs.get("monte_carlo_sims", self.monte_carlo_simulations)
        
        # 应用滚动窗口
        window_size = min(self.rolling_window, len(returns))
        returns_window = returns.iloc[-window_size:]
        
        # 根据方法计算VaR
        if self.method == "historical":
            var_result = self._calculate_historical_var(returns_window)
        elif self.method == "parametric":
            var_result = self._calculate_parametric_var(returns_window, dist_type)
        else:  # monte_carlo
            var_result = self._calculate_monte_carlo_var(returns_window, mc_sims)
        
        var_pct, cvar_pct = var_result
        
        # 转换为货币价值（如果提供了投资组合价值）
        var_value = var_pct * portfolio_value
        cvar_value = cvar_pct * portfolio_value
        
        # 计算滚动VaR
        rolling_var = self._calculate_rolling_var(returns, window_size)
        
        # 计算VaR的波动性
        var_volatility = rolling_var.std() if len(rolling_var) > 1 else 0.0
        
        # 计算VaR趋势（当前vs历史平均）
        if len(rolling_var) > 1:
            historical_avg_var = rolling_var.iloc[:-1].mean()
            var_trend = (rolling_var.iloc[-1] / historical_avg_var) - 1 if historical_avg_var != 0 else 0.0
        else:
            var_trend = 0.0
        
        # VaR突破检测（计算过去突破VaR的频率）
        actual_breaches = (returns < rolling_var).sum()
        expected_breaches = int(len(returns) * (1 - self.confidence_level))
        breach_ratio = actual_breaches / expected_breaches if expected_breaches > 0 else 0.0
        
        return {
            "var": var_value,  # 货币价值
            "cvar": cvar_value,  # 货币价值
            "var_pct": var_pct,  # 百分比
            "cvar_pct": cvar_pct,  # 百分比
            "rolling_var": rolling_var,  # 滚动VaR序列
            "var_volatility": var_volatility,  # VaR的波动性
            "var_trend": var_trend,  # VaR趋势
            "actual_breaches": actual_breaches,  # 实际突破次数
            "expected_breaches": expected_breaches,  # 预期突破次数
            "breach_ratio": breach_ratio,  # 突破比率
            "method": self.method,
            "confidence_level": self.confidence_level,
            "time_horizon": self.time_horizon
        }
    
    def _calculate_historical_var(self, returns: pd.Series) -> Tuple[float, float]:
        """
        计算历史模拟法VaR和CVaR
        
        参数:
            returns: 收益率序列
            
        返回:
            Tuple[float, float]: (VaR, CVaR)，均为百分比
        """
        # 按收益率排序
        sorted_returns = np.sort(returns.values)
        
        # 计算VaR
        var_percentile = 1 - self.confidence_level
        var_idx = int(np.floor(var_percentile * len(sorted_returns)))
        var = sorted_returns[var_idx]
        
        # 计算CVaR (Conditional VaR，又称Expected Shortfall)
        cvar = sorted_returns[:var_idx+1].mean()
        
        # 根据时间尺度调整
        var = var * np.sqrt(self.time_horizon)
        cvar = cvar * np.sqrt(self.time_horizon)
        
        return var, cvar
    
    def _calculate_parametric_var(self, returns: pd.Series, dist_type: str = "normal") -> Tuple[float, float]:
        """
        计算参数法VaR和CVaR
        
        参数:
            returns: 收益率序列
            dist_type: 分布类型 ('normal', 't')
            
        返回:
            Tuple[float, float]: (VaR, CVaR)，均为百分比
        """
        mu = returns.mean()
        sigma = returns.std()
        
        if dist_type == "t":
            # 估计t分布的自由度
            df = 6  # 假设自由度为6（可以通过极大似然法估计，但这里简化处理）
            t_value = stats.t.ppf(1 - self.confidence_level, df)
            var = mu + sigma * t_value
            
            # t分布的CVaR
            t_density = stats.t.pdf(t_value, df)
            t_cdf = 1 - self.confidence_level
            cvar = mu + sigma * (t_density / t_cdf) * (df + t_value**2) / (df - 1)
        else:  # normal
            # 正态分布的VaR
            z_value = stats.norm.ppf(1 - self.confidence_level)
            var = mu + sigma * z_value
            
            # 正态分布的CVaR
            z_density = stats.norm.pdf(z_value)
            z_cdf = 1 - self.confidence_level
            cvar = mu + sigma * (z_density / z_cdf)
        
        # 根据时间尺度调整
        var = var * np.sqrt(self.time_horizon)
        cvar = cvar * np.sqrt(self.time_horizon)
        
        return var, cvar
    
    def _calculate_monte_carlo_var(self, returns: pd.Series, simulations: int = 10000) -> Tuple[float, float]:
        """
        使用蒙特卡洛模拟计算VaR和CVaR
        
        参数:
            returns: 收益率序列
            simulations: 模拟次数
            
        返回:
            Tuple[float, float]: (VaR, CVaR)，均为百分比
        """
        mu = returns.mean()
        sigma = returns.std()
        
        # 生成正态分布的随机收益率
        simulated_returns = np.random.normal(mu, sigma, simulations)
        
        # 计算VaR
        var_percentile = 1 - self.confidence_level
        var = np.percentile(simulated_returns, var_percentile * 100)
        
        # 计算CVaR
        cvar = simulated_returns[simulated_returns <= var].mean()
        
        # 根据时间尺度调整
        var = var * np.sqrt(self.time_horizon)
        cvar = cvar * np.sqrt(self.time_horizon)
        
        return var, cvar
    
    def _calculate_rolling_var(self, returns: pd.Series, window_size: int) -> pd.Series:
        """
        计算滚动VaR
        
        参数:
            returns: 收益率序列
            window_size: 窗口大小
            
        返回:
            pd.Series: 滚动VaR序列
        """
        if len(returns) < window_size:
            return pd.Series(index=returns.index)
        
        # 计算滚动窗口VaR
        rolling_var = []
        
        for i in range(len(returns) - window_size + 1):
            window = returns.iloc[i:i+window_size]
            
            if self.method == "historical":
                var, _ = self._calculate_historical_var(window)
            elif self.method == "parametric":
                var, _ = self._calculate_parametric_var(window)
            else:  # monte_carlo，简化为参数法以提高性能
                var, _ = self._calculate_parametric_var(window)
            
            rolling_var.append(var)
        
        return pd.Series(rolling_var, index=returns.index[window_size-1:])
    
    def is_risk_exceeded(self, current_value: float, threshold: float) -> bool:
        """
        判断VaR风险是否超过阈值
        
        参数:
            current_value: 当前VaR值（负值）
            threshold: VaR阈值（负值）
            
        返回:
            bool: 是否超过阈值
        """
        # 对于VaR，值越小（负值越大）表示风险越高
        return current_value < threshold
    
    def get_risk_level(self, current_value: float) -> str:
        """
        获取风险等级
        
        参数:
            current_value: 当前VaR值
            
        返回:
            str: 风险等级，例如 "低", "中", "高"
        """
        for level, threshold in sorted(self.risk_levels.items(), key=lambda x: x[1], reverse=True):
            if current_value <= threshold:
                return level
        
        return "安全"  # 如果没有超过任何阈值
    
    def get_description(self) -> str:
        """
        获取风险监控器的描述信息
        
        返回:
            str: 描述信息
        """
        method_name = {
            "historical": "历史模拟法",
            "parametric": "参数法",
            "monte_carlo": "蒙特卡洛法"
        }.get(self.method, self.method)
        
        confidence_str = f"{self.confidence_level*100:.0f}%"
        
        thresholds = ", ".join([f"{level}风险: {threshold:.1%}" for level, threshold in self.risk_levels.items()])
        
        return f"VaR监控器 ({method_name}, {confidence_str}置信水平): {thresholds}"
    
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取风险监控器的参数
        
        返回:
            Dict[str, Any]: 参数字典
        """
        return {
            "name": self.name,
            "method": self.method,
            "confidence_level": self.confidence_level,
            "time_horizon": self.time_horizon,
            "rolling_window": self.rolling_window,
            "risk_levels": self.risk_levels.copy(),
            "monte_carlo_simulations": self.monte_carlo_simulations
        }
    
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """
        设置风险监控器的参数
        
        参数:
            parameters: 参数字典
        """
        if "name" in parameters:
            self.name = parameters["name"]
        
        if "method" in parameters:
            method = parameters["method"]
            if method in ["historical", "parametric", "monte_carlo"]:
                self.method = method
        
        if "confidence_level" in parameters:
            confidence_level = parameters["confidence_level"]
            if 0 < confidence_level < 1:
                self.confidence_level = confidence_level
        
        if "time_horizon" in parameters:
            self.time_horizon = parameters["time_horizon"]
        
        if "rolling_window" in parameters:
            self.rolling_window = parameters["rolling_window"]
        
        if "risk_levels" in parameters:
            self.risk_levels = parameters["risk_levels"].copy()
        
        if "monte_carlo_simulations" in parameters:
            self.monte_carlo_simulations = parameters["monte_carlo_simulations"] 