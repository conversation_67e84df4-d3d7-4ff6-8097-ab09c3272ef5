"""
集中度风险监控器
- 监控投资组合的集中度风险
- 检测是否存在过度集中的情况
- 支持多种集中度计算方法
"""

from typing import Dict, List, Any, Optional, Union
import pandas as pd
import numpy as np

from src.risk.monitoring.monitor_interface import RiskMonitorInterface


class ConcentrationMonitor(RiskMonitorInterface):
    """集中度风险监控类，用于监控策略/投资组合的集中度风险"""
    
    def __init__(self, name: str = "ConcentrationMonitor", 
                 concentration_methods: Optional[List[str]] = None,
                 risk_levels: Optional[Dict[str, float]] = None):
        """
        初始化集中度监控器
        
        参数:
            name: 监控器名称
            concentration_methods: 集中度计算方法列表
            risk_levels: 风险等级阈值，格式为 {等级: 阈值}
        """
        self.name = name
        self.concentration_methods = concentration_methods or [
            'herfindahl', 'top_n', 'effective_positions', 'entropy'
        ]
        
        # 默认风险等级阈值（基于赫芬达尔指数）
        self.risk_levels = risk_levels or {
            "低": 0.1,    # HHI < 0.1
            "中": 0.25,   # 0.1 <= HHI < 0.25
            "高": 0.5     # HHI >= 0.25
        }
    
    def calculate(self, weights: pd.Series, **kwargs) -> Dict[str, Any]:
        """
        计算集中度指标
        
        参数:
            weights: 权重数据
            **kwargs: 其他参数，可包括:
                - sector_info: 行业信息字典
                - market_cap_info: 市值信息
                - top_n: 计算前N大持仓集中度，默认为5
                
        返回:
            Dict[str, Any]: 包含集中度指标的字典
        """
        if len(weights) == 0 or weights.abs().sum() == 0:
            return self._empty_result()
        
        # 标准化权重（使用绝对值）
        abs_weights = weights.abs()
        normalized_weights = abs_weights / abs_weights.sum()
        
        # 计算各种集中度指标
        concentration_metrics = {}
        
        # 1. 赫芬达尔-赫希曼指数 (HHI)
        if 'herfindahl' in self.concentration_methods:
            concentration_metrics['herfindahl_index'] = self._calculate_hhi(normalized_weights)
        
        # 2. 前N大持仓集中度
        if 'top_n' in self.concentration_methods:
            top_n = kwargs.get('top_n', 5)
            concentration_metrics.update(self._calculate_top_n_concentration(normalized_weights, top_n))
        
        # 3. 有效持仓数量
        if 'effective_positions' in self.concentration_methods:
            concentration_metrics['effective_positions'] = self._calculate_effective_positions(normalized_weights)
        
        # 4. 信息熵
        if 'entropy' in self.concentration_methods:
            concentration_metrics['entropy'] = self._calculate_entropy(normalized_weights)
        
        # 5. 行业集中度（如果提供行业信息）
        sector_info = kwargs.get('sector_info', None)
        if sector_info:
            sector_concentration = self._calculate_sector_concentration(normalized_weights, sector_info)
            concentration_metrics.update(sector_concentration)
        
        # 6. 市值集中度（如果提供市值信息）
        market_cap_info = kwargs.get('market_cap_info', None)
        if market_cap_info:
            cap_concentration = self._calculate_market_cap_concentration(normalized_weights, market_cap_info)
            concentration_metrics.update(cap_concentration)
        
        # 风险评估
        risk_assessment = self._assess_concentration_risk(concentration_metrics)
        
        return {
            "concentration_metrics": concentration_metrics,
            "risk_assessment": risk_assessment,
            "overall_concentration": concentration_metrics.get('herfindahl_index', 0),
            "effective_positions": concentration_metrics.get('effective_positions', len(weights))
        }
    
    def _calculate_hhi(self, weights: pd.Series) -> float:
        """计算赫芬达尔-赫希曼指数"""
        return (weights ** 2).sum()
    
    def _calculate_top_n_concentration(self, weights: pd.Series, top_n: int) -> Dict[str, float]:
        """计算前N大持仓集中度"""
        sorted_weights = weights.sort_values(ascending=False)
        
        result = {}
        for n in [1, 3, 5, 10]:
            if n <= len(sorted_weights) and n <= top_n:
                concentration = sorted_weights.head(n).sum()
                result[f'top_{n}_concentration'] = concentration
        
        return result
    
    def _calculate_effective_positions(self, weights: pd.Series) -> float:
        """计算有效持仓数量"""
        hhi = self._calculate_hhi(weights)
        return 1.0 / hhi if hhi > 0 else len(weights)
    
    def _calculate_entropy(self, weights: pd.Series) -> float:
        """计算信息熵"""
        # 过滤掉零权重
        non_zero_weights = weights[weights > 0]
        if len(non_zero_weights) == 0:
            return 0
        
        # 计算信息熵
        entropy = -(non_zero_weights * np.log(non_zero_weights)).sum()
        return entropy
    
    def _calculate_sector_concentration(self, weights: pd.Series, 
                                     sector_info: Dict[str, str]) -> Dict[str, Any]:
        """计算行业集中度"""
        # 按行业汇总权重
        sector_weights = {}
        for symbol, weight in weights.items():
            sector = sector_info.get(symbol, "其他")
            if sector not in sector_weights:
                sector_weights[sector] = 0
            sector_weights[sector] += weight
        
        if not sector_weights:
            return {}
        
        sector_series = pd.Series(sector_weights)
        
        result = {
            'sector_hhi': self._calculate_hhi(sector_series),
            'sector_effective_count': self._calculate_effective_positions(sector_series),
            'sector_entropy': self._calculate_entropy(sector_series)
        }
        
        # 最大行业权重
        result['max_sector_weight'] = sector_series.max()
        
        # 前3大行业集中度
        top_sectors = sector_series.sort_values(ascending=False).head(3)
        result['top_3_sectors_concentration'] = top_sectors.sum()
        
        return result
    
    def _calculate_market_cap_concentration(self, weights: pd.Series, 
                                         market_cap_info: Dict[str, float]) -> Dict[str, Any]:
        """计算市值集中度"""
        # 按市值分组
        large_cap_weight = 0
        mid_cap_weight = 0
        small_cap_weight = 0
        
        for symbol, weight in weights.items():
            market_cap = market_cap_info.get(symbol, 0)
            
            if market_cap > 100e8:  # 大盘股：市值>100亿
                large_cap_weight += weight
            elif market_cap > 20e8:  # 中盘股：市值20-100亿
                mid_cap_weight += weight
            else:  # 小盘股：市值<20亿
                small_cap_weight += weight
        
        cap_weights = pd.Series({
            'large_cap': large_cap_weight,
            'mid_cap': mid_cap_weight,
            'small_cap': small_cap_weight
        })
        
        return {
            'market_cap_hhi': self._calculate_hhi(cap_weights),
            'large_cap_concentration': large_cap_weight,
            'mid_cap_concentration': mid_cap_weight,
            'small_cap_concentration': small_cap_weight
        }
    
    def _assess_concentration_risk(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """评估集中度风险"""
        risk_assessment = {
            "overall_risk_level": "低",
            "risk_factors": [],
            "warnings": []
        }
        
        hhi = metrics.get('herfindahl_index', 0)
        effective_positions = metrics.get('effective_positions', float('inf'))
        top_5_concentration = metrics.get('top_5_concentration', 0)
        
        # 基于HHI评估风险
        if hhi > self.risk_levels["高"]:
            risk_assessment["overall_risk_level"] = "高"
            risk_assessment["risk_factors"].append("整体集中度过高")
            risk_assessment["warnings"].append(f"赫芬达尔指数{hhi:.3f}超过高风险阈值")
        elif hhi > self.risk_levels["中"]:
            risk_assessment["overall_risk_level"] = "中"
            risk_assessment["risk_factors"].append("整体集中度偏高")
        
        # 基于有效持仓数量评估
        if effective_positions < 5:
            risk_assessment["risk_factors"].append("有效持仓数量过少")
            risk_assessment["warnings"].append(f"有效持仓数量{effective_positions:.1f}过少")
        
        # 基于前5大持仓集中度评估
        if top_5_concentration > 0.8:
            risk_assessment["risk_factors"].append("前5大持仓过于集中")
            risk_assessment["warnings"].append(f"前5大持仓占比{top_5_concentration:.1%}过高")
        
        # 行业集中度风险
        sector_hhi = metrics.get('sector_hhi', 0)
        if sector_hhi > 0.5:
            risk_assessment["risk_factors"].append("行业集中度过高")
            risk_assessment["warnings"].append(f"行业集中度指数{sector_hhi:.3f}过高")
        
        # 市值集中度风险
        large_cap_concentration = metrics.get('large_cap_concentration', 0)
        if large_cap_concentration > 0.9:
            risk_assessment["risk_factors"].append("大盘股集中度过高")
        elif large_cap_concentration < 0.1:
            risk_assessment["risk_factors"].append("小盘股集中度过高")
        
        return risk_assessment
    
    def _empty_result(self) -> Dict[str, Any]:
        """返回空结果"""
        return {
            "concentration_metrics": {
                "herfindahl_index": 0,
                "effective_positions": 0,
                "entropy": 0
            },
            "risk_assessment": {
                "overall_risk_level": "低",
                "risk_factors": [],
                "warnings": []
            },
            "overall_concentration": 0,
            "effective_positions": 0
        }
    
    def is_risk_exceeded(self, current_value: float, threshold: float) -> bool:
        """
        判断集中度风险是否超过阈值
        
        参数:
            current_value: 当前集中度值（HHI）
            threshold: 集中度阈值
            
        返回:
            bool: 是否超过阈值
        """
        return current_value > threshold
    
    def get_risk_level(self, concentration_value: float) -> str:
        """
        获取集中度对应的风险等级
        
        参数:
            concentration_value: 集中度值（HHI）
            
        返回:
            str: 风险等级
        """
        if concentration_value > self.risk_levels["高"]:
            return "高"
        elif concentration_value > self.risk_levels["中"]:
            return "中"
        else:
            return "低"
