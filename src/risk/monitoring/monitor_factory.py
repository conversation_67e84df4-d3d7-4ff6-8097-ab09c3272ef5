"""
风险监控器工厂模块
- 提供创建不同类型风险监控器的工厂方法
- 管理已注册的监控器类型
"""

import logging
from typing import Dict, Type, Any, Optional, List

from src.risk.monitoring.monitor_interface import RiskMonitorInterface, RiskMonitorException
from src.risk.monitoring.var_monitor import VaRMonitor
from src.risk.monitoring.volatility_monitor import VolatilityMonitor
from src.risk.monitoring.drawdown_monitor import DrawdownMonitor

# 配置日志
logger = logging.getLogger(__name__)

class MonitorFactory:
    """
    风险监控器工厂类
    
    负责创建和管理不同类型的风险监控器实例
    """
    
    # 存储已注册的监控器类型
    _monitors: Dict[str, Type[RiskMonitorInterface]] = {}
    
    @classmethod
    def register_monitor(cls, monitor_type: str, monitor_class: Type[RiskMonitorInterface]) -> None:
        """
        注册风险监控器类型
        
        参数:
            monitor_type: 监控器类型名称
            monitor_class: 监控器类
            
        异常:
            RiskMonitorException: 注册失败时抛出
        """
        if not issubclass(monitor_class, RiskMonitorInterface):
            raise RiskMonitorException(f"监控器类必须实现RiskMonitorInterface接口: {monitor_class.__name__}")
            
        if monitor_type in cls._monitors:
            logger.warning(f"监控器类型 '{monitor_type}' 已存在，将被覆盖")
        
        cls._monitors[monitor_type] = monitor_class
        logger.info(f"注册风险监控器: {monitor_type}")
    
    @classmethod
    def create_monitor(cls, monitor_type: str, **kwargs) -> RiskMonitorInterface:
        """
        创建风险监控器实例
        
        参数:
            monitor_type: 监控器类型名称
            **kwargs: 传递给监控器构造函数的参数
            
        返回:
            RiskMonitorInterface: 风险监控器实例
            
        异常:
            RiskMonitorException: 创建失败时抛出
        """
        if monitor_type not in cls._monitors:
            raise RiskMonitorException(f"未知的监控器类型: {monitor_type}")
        
        monitor_class = cls._monitors[monitor_type]
        
        try:
            monitor = monitor_class(**kwargs)
            logger.info(f"创建风险监控器: {monitor_type}")
            return monitor
        except Exception as e:
            error_msg = f"创建监控器 {monitor_type} 失败: {str(e)}"
            logger.error(error_msg)
            raise RiskMonitorException(error_msg) from e
    
    @classmethod
    def get_available_monitors(cls) -> Dict[str, Type[RiskMonitorInterface]]:
        """
        获取所有可用的监控器类型
        
        返回:
            Dict[str, Type[RiskMonitorInterface]]: 监控器类型名称到监控器类的映射
        """
        return cls._monitors.copy()
    
    @classmethod
    def create_combined_monitor(cls, monitor_configs: List[Dict[str, Any]]) -> Dict[str, RiskMonitorInterface]:
        """
        创建多个监控器的组合
        
        参数:
            monitor_configs: 监控器配置列表，每个配置包含 'type' 和 'params' 字段
            
        返回:
            Dict[str, RiskMonitorInterface]: 名称到监控器实例的映射
            
        异常:
            RiskMonitorException: 创建失败时抛出
        """
        monitors = {}
        
        for config in monitor_configs:
            if 'type' not in config:
                raise RiskMonitorException("监控器配置必须包含 'type' 字段")
            
            monitor_type = config['type']
            monitor_name = config.get('name', monitor_type)
            monitor_params = config.get('params', {})
            
            try:
                monitor = cls.create_monitor(monitor_type, **monitor_params)
                monitors[monitor_name] = monitor
            except Exception as e:
                logger.error(f"创建监控器 {monitor_type} 失败: {str(e)}")
                raise
        
        return monitors

# 注册默认的监控器类型
MonitorFactory.register_monitor('var', VaRMonitor)
MonitorFactory.register_monitor('volatility', VolatilityMonitor)
MonitorFactory.register_monitor('drawdown', DrawdownMonitor)

def create_risk_monitor(monitor_type: str = 'var', **kwargs) -> RiskMonitorInterface:
    """
    创建风险监控器的便捷函数
    
    参数:
        monitor_type: 监控器类型，默认为'var'
        **kwargs: 监控器参数
        
    返回:
        RiskMonitorInterface: 风险监控器实例
    """
    return MonitorFactory.create_monitor(monitor_type, **kwargs)

