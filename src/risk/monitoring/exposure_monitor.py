"""
敞口风险监控器
- 监控投资组合的敞口风险
- 检测是否超过敞口阈值
- 支持多种敞口计算方法
"""

from typing import Dict, List, Any, Optional, Union
import pandas as pd
import numpy as np

from src.risk.monitoring.monitor_interface import RiskMonitorInterface


class ExposureMonitor(RiskMonitorInterface):
    """敞口风险监控类，用于监控策略/投资组合的敞口风险"""
    
    def __init__(self, name: str = "ExposureMonitor", 
                 exposure_types: Optional[List[str]] = None,
                 risk_levels: Optional[Dict[str, float]] = None):
        """
        初始化敞口监控器
        
        参数:
            name: 监控器名称
            exposure_types: 敞口类型列表，如['gross', 'net', 'long', 'short']
            risk_levels: 风险等级阈值，格式为 {等级: 阈值}
        """
        self.name = name
        self.exposure_types = exposure_types or ['gross', 'net', 'long', 'short']
        
        # 默认风险等级阈值
        self.risk_levels = risk_levels or {
            "低": 0.8,    # 80%敞口
            "中": 1.2,    # 120%敞口
            "高": 1.5     # 150%敞口
        }
    
    def calculate(self, positions: Union[pd.Series, pd.DataFrame], 
                 prices: Optional[pd.Series] = None, **kwargs) -> Dict[str, Any]:
        """
        计算敞口指标
        
        参数:
            positions: 持仓数据，可以是权重或数量
            prices: 价格数据（如果positions是数量）
            **kwargs: 其他参数，可包括:
                - portfolio_value: 投资组合总价值
                - benchmark_weights: 基准权重
                
        返回:
            Dict[str, Any]: 包含敞口指标的字典
        """
        if len(positions) == 0:
            return self._empty_result()
        
        # 获取参数
        portfolio_value = kwargs.get("portfolio_value", 1.0)
        benchmark_weights = kwargs.get("benchmark_weights", None)
        
        # 如果positions是数量且提供了价格，转换为权重
        if prices is not None and isinstance(positions, pd.Series):
            market_values = positions * prices
            total_value = market_values.abs().sum()
            if total_value > 0:
                weights = market_values / total_value
            else:
                weights = pd.Series(0, index=positions.index)
        else:
            weights = positions
        
        # 计算各种敞口
        exposures = self._calculate_exposures(weights)
        
        # 计算相对基准的敞口（如果提供基准）
        if benchmark_weights is not None:
            relative_exposures = self._calculate_relative_exposures(weights, benchmark_weights)
            exposures.update(relative_exposures)
        
        # 计算行业/板块敞口（如果有相关信息）
        sector_exposures = self._calculate_sector_exposures(weights, kwargs)
        if sector_exposures:
            exposures.update(sector_exposures)
        
        # 风险评估
        risk_assessment = self._assess_exposure_risk(exposures)
        
        return {
            "exposures": exposures,
            "risk_assessment": risk_assessment,
            "total_exposure": exposures.get("gross_exposure", 0),
            "net_exposure": exposures.get("net_exposure", 0),
            "long_exposure": exposures.get("long_exposure", 0),
            "short_exposure": exposures.get("short_exposure", 0)
        }
    
    def _calculate_exposures(self, weights: pd.Series) -> Dict[str, float]:
        """计算基本敞口指标"""
        exposures = {}
        
        # 总敞口（绝对值之和）
        exposures["gross_exposure"] = weights.abs().sum()
        
        # 净敞口（代数和）
        exposures["net_exposure"] = weights.sum()
        
        # 多头敞口
        long_weights = weights[weights > 0]
        exposures["long_exposure"] = long_weights.sum() if len(long_weights) > 0 else 0
        
        # 空头敞口
        short_weights = weights[weights < 0]
        exposures["short_exposure"] = abs(short_weights.sum()) if len(short_weights) > 0 else 0
        
        # 多空比例
        if exposures["short_exposure"] > 0:
            exposures["long_short_ratio"] = exposures["long_exposure"] / exposures["short_exposure"]
        else:
            exposures["long_short_ratio"] = float('inf') if exposures["long_exposure"] > 0 else 0
        
        # 杠杆比率
        exposures["leverage_ratio"] = exposures["gross_exposure"] / max(abs(exposures["net_exposure"]), 0.01)
        
        return exposures
    
    def _calculate_relative_exposures(self, weights: pd.Series, 
                                    benchmark_weights: pd.Series) -> Dict[str, float]:
        """计算相对基准的敞口"""
        # 确保索引对齐
        aligned_weights = weights.reindex(benchmark_weights.index, fill_value=0)
        
        # 相对权重
        relative_weights = aligned_weights - benchmark_weights
        
        # 相对敞口指标
        relative_exposures = {
            "relative_gross_exposure": relative_weights.abs().sum(),
            "relative_net_exposure": relative_weights.sum(),
            "tracking_error_contribution": np.sqrt((relative_weights ** 2).sum())
        }
        
        return relative_exposures
    
    def _calculate_sector_exposures(self, weights: pd.Series, 
                                  kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """计算行业/板块敞口"""
        sector_info = kwargs.get("sector_info", None)
        if sector_info is None:
            return {}
        
        sector_exposures = {}
        
        # 按行业汇总敞口
        if isinstance(sector_info, dict):
            sectors = {}
            for symbol, weight in weights.items():
                sector = sector_info.get(symbol, "其他")
                if sector not in sectors:
                    sectors[sector] = 0
                sectors[sector] += weight
            
            sector_exposures["sector_exposures"] = sectors
            
            # 最大行业敞口
            if sectors:
                max_sector_exposure = max(abs(exp) for exp in sectors.values())
                sector_exposures["max_sector_exposure"] = max_sector_exposure
                
                # 行业集中度（前5大行业敞口占比）
                sorted_sectors = sorted(sectors.values(), key=abs, reverse=True)
                top5_exposure = sum(abs(exp) for exp in sorted_sectors[:5])
                total_exposure = sum(abs(exp) for exp in sectors.values())
                if total_exposure > 0:
                    sector_exposures["sector_concentration"] = top5_exposure / total_exposure
        
        return sector_exposures
    
    def _assess_exposure_risk(self, exposures: Dict[str, float]) -> Dict[str, Any]:
        """评估敞口风险"""
        risk_assessment = {
            "overall_risk_level": "低",
            "risk_factors": [],
            "warnings": []
        }
        
        gross_exposure = exposures.get("gross_exposure", 0)
        net_exposure = abs(exposures.get("net_exposure", 0))
        leverage_ratio = exposures.get("leverage_ratio", 1)
        
        # 评估总敞口风险
        if gross_exposure > self.risk_levels["高"]:
            risk_assessment["overall_risk_level"] = "高"
            risk_assessment["risk_factors"].append("总敞口过高")
            risk_assessment["warnings"].append(f"总敞口{gross_exposure:.1%}超过高风险阈值")
        elif gross_exposure > self.risk_levels["中"]:
            risk_assessment["overall_risk_level"] = "中"
            risk_assessment["risk_factors"].append("总敞口偏高")
        
        # 评估杠杆风险
        if leverage_ratio > 3:
            risk_assessment["risk_factors"].append("杠杆比率过高")
            risk_assessment["warnings"].append(f"杠杆比率{leverage_ratio:.1f}过高")
        
        # 评估净敞口风险
        if net_exposure > 0.9:
            risk_assessment["risk_factors"].append("净敞口过高")
            risk_assessment["warnings"].append(f"净敞口{net_exposure:.1%}接近满仓")
        
        # 评估行业集中度风险
        sector_concentration = exposures.get("sector_concentration", 0)
        if sector_concentration > 0.8:
            risk_assessment["risk_factors"].append("行业集中度过高")
            risk_assessment["warnings"].append(f"前5大行业集中度{sector_concentration:.1%}过高")
        
        return risk_assessment
    
    def _empty_result(self) -> Dict[str, Any]:
        """返回空结果"""
        return {
            "exposures": {
                "gross_exposure": 0,
                "net_exposure": 0,
                "long_exposure": 0,
                "short_exposure": 0
            },
            "risk_assessment": {
                "overall_risk_level": "低",
                "risk_factors": [],
                "warnings": []
            },
            "total_exposure": 0,
            "net_exposure": 0,
            "long_exposure": 0,
            "short_exposure": 0
        }
    
    def is_risk_exceeded(self, current_value: float, threshold: float) -> bool:
        """
        判断敞口风险是否超过阈值
        
        参数:
            current_value: 当前敞口值
            threshold: 敞口阈值
            
        返回:
            bool: 是否超过阈值
        """
        return current_value > threshold
    
    def get_risk_level(self, exposure_value: float) -> str:
        """
        获取敞口对应的风险等级
        
        参数:
            exposure_value: 敞口值
            
        返回:
            str: 风险等级
        """
        if exposure_value > self.risk_levels["高"]:
            return "高"
        elif exposure_value > self.risk_levels["中"]:
            return "中"
        else:
            return "低"
