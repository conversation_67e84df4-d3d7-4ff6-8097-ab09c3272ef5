"""
波动率风险监控器
- 监控投资组合波动率风险
- 分析波动率趋势和异常
"""

from typing import Dict, List, Any, Optional, Union
import pandas as pd
import numpy as np

from src.risk.monitoring.monitor_interface import RiskMonitorInterface


class VolatilityMonitor(RiskMonitorInterface):
    """波动率风险监控类，用于监控策略/投资组合的波动率风险"""
    
    def __init__(self, name: str = "VolatilityMonitor", 
                 window: int = 20, 
                 annualization_factor: int = 252,
                 risk_levels: Optional[Dict[str, float]] = None):
        """
        初始化波动率监控器
        
        参数:
            name: 监控器名称
            window: 计算波动率的窗口大小，默认为20个交易日
            annualization_factor: 年化因子，默认为252（交易日）
            risk_levels: 风险等级阈值，格式为 {等级: 阈值}
                        例如 {"低": 0.10, "中": 0.20, "高": 0.30}
        """
        self.name = name
        self.window = window
        self.annualization_factor = annualization_factor
        
        # 默认风险等级阈值
        self.risk_levels = risk_levels or {
            "低": 0.10,  # 10%年化波动率
            "中": 0.20,  # 20%年化波动率
            "高": 0.30   # 30%年化波动率
        }
    
    def calculate(self, returns: pd.Series, **kwargs) -> Dict[str, Any]:
        """
        计算波动率风险指标
        
        参数:
            returns: 收益率序列
            **kwargs: 其他参数
            
        返回:
            Dict[str, Any]: 包含波动率风险指标的字典
        """
        if len(returns) < 2:
            return {
                "volatility": 0.0,
                "annualized_volatility": 0.0,
                "rolling_volatility": pd.Series(dtype=float),
                "relative_volatility": 0.0,
                "volatility_trend": 0.0
            }
        
        # 计算全样本波动率
        volatility = returns.std()
        annualized_volatility = volatility * np.sqrt(self.annualization_factor)
        
        # 计算滚动窗口波动率
        min_periods = min(2, len(returns))
        rolling_window = min(self.window, len(returns))
        rolling_vol = returns.rolling(window=rolling_window, min_periods=min_periods).std()
        rolling_vol_annualized = rolling_vol * np.sqrt(self.annualization_factor)
        
        # 当前滚动波动率
        current_rolling_vol = rolling_vol_annualized.iloc[-1] if not rolling_vol_annualized.empty else annualized_volatility
        
        # 计算波动率趋势（当前vs历史平均）
        if len(rolling_vol_annualized) > 1:
            historical_avg_vol = rolling_vol_annualized.iloc[:-1].mean()
            volatility_trend = (current_rolling_vol / historical_avg_vol) - 1 if historical_avg_vol > 0 else 0.0
        else:
            volatility_trend = 0.0
        
        # 计算波动率的波动率（二阶波动率）
        vol_of_vol = rolling_vol_annualized.rolling(window=rolling_window, min_periods=min_periods).std()
        current_vol_of_vol = vol_of_vol.iloc[-1] if not vol_of_vol.empty and len(vol_of_vol) > 1 else 0.0
        
        # 相对波动率（与基准比较）
        benchmark_returns = kwargs.get("benchmark_returns", None)
        relative_volatility = 0.0
        
        if benchmark_returns is not None and len(benchmark_returns) >= 2:
            benchmark_vol = benchmark_returns.std() * np.sqrt(self.annualization_factor)
            relative_volatility = annualized_volatility / benchmark_vol if benchmark_vol > 0 else float('inf')
        
        # 计算上行和下行波动率
        positive_returns = returns[returns > 0]
        negative_returns = returns[returns < 0]
        
        upside_volatility = positive_returns.std() * np.sqrt(self.annualization_factor) if len(positive_returns) > 1 else 0.0
        downside_volatility = negative_returns.std() * np.sqrt(self.annualization_factor) if len(negative_returns) > 1 else 0.0
        
        # 计算偏度和峰度
        skewness = returns.skew() if len(returns) > 2 else 0.0
        kurtosis = returns.kurtosis() if len(returns) > 3 else 0.0
        
        return {
            "volatility": volatility,
            "annualized_volatility": annualized_volatility,
            "rolling_volatility": rolling_vol_annualized,
            "current_rolling_volatility": current_rolling_vol,
            "volatility_trend": volatility_trend,
            "vol_of_vol": current_vol_of_vol,
            "relative_volatility": relative_volatility,
            "upside_volatility": upside_volatility,
            "downside_volatility": downside_volatility,
            "skewness": skewness,
            "kurtosis": kurtosis
        }
    
    def is_risk_exceeded(self, current_value: float, threshold: float) -> bool:
        """
        判断波动率风险是否超过阈值
        
        参数:
            current_value: 当前波动率值
            threshold: 波动率阈值
            
        返回:
            bool: 是否超过阈值
        """
        return current_value > threshold
    
    def get_risk_level(self, current_value: float) -> str:
        """
        获取风险等级
        
        参数:
            current_value: 当前波动率值
            
        返回:
            str: 风险等级，例如 "低", "中", "高"
        """
        for level, threshold in sorted(self.risk_levels.items(), key=lambda x: x[1]):
            if current_value <= threshold:
                return level
        
        return "高"  # 如果超过所有阈值，返回最高风险等级
    
    def get_description(self) -> str:
        """
        获取风险监控器的描述信息
        
        返回:
            str: 描述信息
        """
        thresholds = ", ".join([f"{level}风险: {threshold:.1%}" for level, threshold in self.risk_levels.items()])
        return f"波动率监控器 ({self.window}日): {thresholds}"
    
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取风险监控器的参数
        
        返回:
            Dict[str, Any]: 参数字典
        """
        return {
            "name": self.name,
            "window": self.window,
            "annualization_factor": self.annualization_factor,
            "risk_levels": self.risk_levels.copy()
        }
    
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """
        设置风险监控器的参数
        
        参数:
            parameters: 参数字典
        """
        if "name" in parameters:
            self.name = parameters["name"]
        
        if "window" in parameters:
            self.window = parameters["window"]
        
        if "annualization_factor" in parameters:
            self.annualization_factor = parameters["annualization_factor"]
        
        if "risk_levels" in parameters:
            self.risk_levels = parameters["risk_levels"].copy()

