"""
敞口风险控制器

监控投资组合的总敞口和单一资产敞口，防止过度集中风险。
"""

import logging
from typing import Dict, Any, List, Optional
import datetime as dt
import numpy as np

from src.risk.control.risk_controller import RiskController


class ExposureController(RiskController):
    """
    敞口风险控制器
    
    监控投资组合的总敞口和单一资产敞口，当敞口超过设定阈值时触发预警。
    """
    
    def __init__(self, 
                max_total_exposure: float = 1.0,
                max_single_exposure: float = 0.3, 
                alert_threshold: float = 0.8):
        """
        初始化敞口风险控制器
        
        参数:
            max_total_exposure: 最大总敞口比例（相对于总资产）
            max_single_exposure: 单一资产最大敞口比例（相对于总资产）
            alert_threshold: 风险预警阈值（0-1之间）
        """
        super().__init__(name="敞口控制器", alert_threshold=alert_threshold)
        self.max_total_exposure = max_total_exposure
        self.max_single_exposure = max_single_exposure
        
        self.logger.info(
            f"初始化敞口控制器: 最大总敞口={max_total_exposure:.2f}, "
            f"最大单一敞口={max_single_exposure:.2f}, 预警阈值={alert_threshold:.2f}"
        )
    
    def _evaluate_risk(self, market_data: Dict[str, Any], positions: Dict[str, Any], 
                      account_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估敞口风险
        
        参数:
            market_data: 市场数据
            positions: 持仓数据
            account_data: 账户数据
            
        返回:
            包含风险评估结果的字典
        """
        if not market_data or not positions or not account_data:
            return {
                'risk_score': 0.0,
                'message': "没有足够数据进行敞口评估",
                'details': {}
            }
        
        # 获取总资产
        total_assets = account_data.get('total_assets', 0.0)
        if total_assets <= 0:
            return {
                'risk_score': 0.0,
                'message': "总资产为零或负值，无法计算敞口",
                'details': {}
            }
        
        # 计算各资产敞口
        exposure_details = {}
        total_exposure = 0.0
        max_single_exposure_ratio = 0.0
        max_exposed_symbol = ""
        
        for symbol, position in positions.items():
            # 跳过零持仓
            position_size = position.get('size', 0)
            if position_size == 0:
                continue
            
            # 获取当前价格
            symbol_data = market_data.get(symbol, {})
            if not symbol_data or 'close' not in symbol_data:
                continue
                
            current_price = symbol_data['close']
            
            # 计算持仓价值和敞口比例
            position_value = abs(position_size) * current_price
            exposure_ratio = position_value / total_assets
            
            # 累加总敞口
            total_exposure += exposure_ratio
            
            # 记录最大单一敞口
            if exposure_ratio > max_single_exposure_ratio:
                max_single_exposure_ratio = exposure_ratio
                max_exposed_symbol = symbol
            
            # 添加到详情
            exposure_details[symbol] = {
                'position_size': position_size,
                'current_price': current_price,
                'position_value': position_value,
                'exposure_ratio': exposure_ratio,
                'is_long': position_size > 0,
                'exceeds_limit': exposure_ratio > self.max_single_exposure
            }
        
        # 计算风险得分
        total_exposure_score = min(1.0, total_exposure / self.max_total_exposure)
        single_exposure_score = min(1.0, max_single_exposure_ratio / self.max_single_exposure) if self.max_single_exposure > 0 else 0.0
        
        # 取两者的最大值作为最终风险得分
        risk_score = max(total_exposure_score, single_exposure_score)
        
        # 确定风险消息
        risk_message = []
        if total_exposure > self.max_total_exposure:
            risk_message.append(f"总敞口 ({total_exposure:.2%}) 超过限制 ({self.max_total_exposure:.2%})")
        
        if max_single_exposure_ratio > self.max_single_exposure:
            risk_message.append(
                f"资产 {max_exposed_symbol} 敞口 ({max_single_exposure_ratio:.2%}) "
                f"超过单一敞口限制 ({self.max_single_exposure:.2%})"
            )
        
        if not risk_message:
            message = f"敞口风险正常，总敞口: {total_exposure:.2%}, 最大单一敞口: {max_single_exposure_ratio:.2%}"
        else:
            message = "; ".join(risk_message)
        
        return {
            'risk_score': risk_score,
            'total_exposure': total_exposure,
            'max_single_exposure_ratio': max_single_exposure_ratio,
            'max_exposed_symbol': max_exposed_symbol,
            'details': exposure_details,
            'message': message
        }
    
    def get_description(self) -> str:
        """获取风险控制器描述"""
        return (
            f"敞口控制器 [最大总敞口: {self.max_total_exposure:.2f}, "
            f"最大单一敞口: {self.max_single_exposure:.2f}, 预警阈值: {self.alert_threshold:.2f}]"
        ) 