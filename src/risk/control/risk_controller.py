"""
风险控制器模块

提供风险评估和控制功能的基类和实现。
风险控制器负责评估特定类型的风险并计算风险得分。
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import datetime as dt


class RiskController(ABC):
    """
    风险控制器基类
    
    所有风险控制器必须继承此类并实现评估方法。
    """
    
    def __init__(self, name: str, alert_threshold: float = 0.8):
        """
        初始化风险控制器
        
        参数:
            name: 控制器名称
            alert_threshold: 风险预警阈值（0-1之间），超过此阈值将触发预警
        """
        self.name = name
        self.alert_threshold = min(max(alert_threshold, 0.0), 1.0)  # 确保在0-1之间
        self.logger = logging.getLogger(f"risk.control.{name}")
        
        # 初始化风险得分和上次评估时间
        self._risk_score = 0.0
        self._last_evaluation_time = None
        self._evaluation_history = []
        
    @property
    def risk_score(self) -> float:
        """获取当前风险得分"""
        return self._risk_score
    
    @property
    def last_evaluation_time(self) -> Optional[dt.datetime]:
        """获取上次评估时间"""
        return self._last_evaluation_time
    
    @property
    def evaluation_history(self) -> List[Dict[str, Any]]:
        """获取评估历史"""
        return self._evaluation_history.copy()
    
    def should_alert(self) -> bool:
        """判断是否应该触发预警"""
        return self._risk_score >= self.alert_threshold
    
    def evaluate(self, market_data: Dict[str, Any], positions: Dict[str, Any], 
                account_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估风险并返回评估结果
        
        参数:
            market_data: 市场数据，包含价格、成交量等信息
            positions: 持仓数据，包含各资产的持仓信息
            account_data: 账户数据，包含可用资金、总资产等信息
            
        返回:
            包含风险评估结果的字典
        """
        # 记录评估时间
        evaluation_time = dt.datetime.now()
        self._last_evaluation_time = evaluation_time
        
        # 调用子类实现的具体评估方法
        result = self._evaluate_risk(market_data, positions, account_data)
        
        # 更新风险得分
        self._risk_score = result.get('risk_score', 0.0)
        
        # 添加评估记录
        evaluation_record = {
            'time': evaluation_time,
            'risk_score': self._risk_score,
            'details': result
        }
        self._evaluation_history.append(evaluation_record)
        
        # 如果历史记录太长，保留最近的记录
        if len(self._evaluation_history) > 100:
            self._evaluation_history = self._evaluation_history[-100:]
        
        return result
    
    @abstractmethod
    def _evaluate_risk(self, market_data: Dict[str, Any], positions: Dict[str, Any], 
                      account_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行具体的风险评估逻辑
        
        这是一个抽象方法，必须由子类实现。
        
        参数:
            market_data: 市场数据
            positions: 持仓数据
            account_data: 账户数据
            
        返回:
            包含风险评估结果的字典，至少包含'risk_score'键
        """
        pass
    
    def get_description(self) -> str:
        """获取风险控制器描述"""
        return f"风险控制器: {self.name}, 预警阈值: {self.alert_threshold:.2f}"
    
    def __str__(self) -> str:
        """字符串表示"""
        status = "预警" if self.should_alert() else "正常"
        return f"{self.name} [风险分数: {self._risk_score:.4f}, 状态: {status}]" 