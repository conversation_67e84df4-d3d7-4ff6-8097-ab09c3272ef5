"""
回撤风险控制器

监控投资组合或单个资产的回撤风险，当回撤超过特定阈值时触发预警。
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional
import datetime as dt

from src.risk.control.risk_controller import RiskController


class DrawdownController(RiskController):
    """
    回撤风险控制器
    
    监控资产或投资组合的回撤风险，根据当前价格与历史最高价格的差距计算风险得分。
    """
    
    def __init__(self, 
                max_drawdown_threshold: float = 0.1, 
                alert_threshold: float = 0.8,
                lookback_period: int = 100,
                price_key: str = 'close'):
        """
        初始化回撤风险控制器
        
        参数:
            max_drawdown_threshold: 最大允许回撤比例，例如0.1表示允许10%的回撤
            alert_threshold: 风险预警阈值（0-1之间）
            lookback_period: 计算回撤的历史数据长度
            price_key: 价格数据在市场数据中的键名
        """
        super().__init__(name="回撤控制器", alert_threshold=alert_threshold)
        self.max_drawdown_threshold = max_drawdown_threshold
        self.lookback_period = lookback_period
        self.price_key = price_key
        
        # 记录资产历史价格
        self._price_history = {}  # symbol -> list of prices
        self._peak_prices = {}    # symbol -> peak price
        
        self.logger.info(
            f"初始化回撤控制器: 最大回撤阈值={max_drawdown_threshold:.2%}, "
            f"预警阈值={alert_threshold:.2f}, 观察周期={lookback_period}"
        )
    
    def _calculate_drawdown(self, symbol: str, current_price: float) -> float:
        """
        计算特定资产的回撤
        
        参数:
            symbol: 资产代码
            current_price: 当前价格
            
        返回:
            回撤比例（0-1之间）
        """
        # 更新价格历史
        if symbol not in self._price_history:
            self._price_history[symbol] = []
            self._peak_prices[symbol] = current_price
        
        self._price_history[symbol].append(current_price)
        
        # 保持历史数据长度不超过lookback_period
        if len(self._price_history[symbol]) > self.lookback_period:
            self._price_history[symbol] = self._price_history[symbol][-self.lookback_period:]
        
        # 更新峰值价格
        self._peak_prices[symbol] = max(self._peak_prices[symbol], current_price)
        
        # 计算回撤
        peak_price = self._peak_prices[symbol]
        if peak_price == 0:
            return 0.0
            
        drawdown = (peak_price - current_price) / peak_price
        return max(0.0, drawdown)  # 确保回撤非负
    
    def _evaluate_risk(self, market_data: Dict[str, Any], positions: Dict[str, Any], 
                      account_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估回撤风险
        
        参数:
            market_data: 市场数据
            positions: 持仓数据
            account_data: 账户数据
            
        返回:
            包含风险评估结果的字典
        """
        if not market_data or not positions:
            return {
                'risk_score': 0.0, 
                'max_drawdown': 0.0,
                'details': {},
                'message': "没有足够数据进行回撤评估"
            }
        
        # 计算各资产回撤
        drawdown_details = {}
        weighted_drawdowns = []
        total_position_value = 0.0
        
        for symbol, position in positions.items():
            # 跳过空头或零持仓
            position_size = position.get('size', 0)
            if position_size <= 0:
                continue
            
            # 获取当前价格
            symbol_data = market_data.get(symbol, {})
            if not symbol_data or self.price_key not in symbol_data:
                continue
                
            current_price = symbol_data[self.price_key]
            position_value = position_size * current_price
            
            # 计算回撤
            drawdown = self._calculate_drawdown(symbol, current_price)
            
            # 计算权重（基于持仓价值）
            total_position_value += position_value
            weighted_drawdowns.append((drawdown, position_value))
            
            # 记录详情
            drawdown_details[symbol] = {
                'current_price': current_price,
                'peak_price': self._peak_prices[symbol],
                'drawdown': drawdown,
                'position_size': position_size,
                'position_value': position_value
            }
        
        # 计算加权平均回撤
        if total_position_value > 0:
            portfolio_drawdown = sum(dd * val / total_position_value for dd, val in weighted_drawdowns)
        else:
            portfolio_drawdown = 0.0
        
        # 计算风险得分
        # 如果回撤超过最大允许回撤，风险分数为1.0
        # 否则，根据回撤与最大允许回撤的比例计算风险分数
        if self.max_drawdown_threshold <= 0:
            risk_score = 0.0
        else:
            risk_score = min(1.0, portfolio_drawdown / self.max_drawdown_threshold)
        
        return {
            'risk_score': risk_score,
            'portfolio_drawdown': portfolio_drawdown,
            'max_drawdown_threshold': self.max_drawdown_threshold,
            'details': drawdown_details,
            'message': f"投资组合回撤: {portfolio_drawdown:.2%}, 风险得分: {risk_score:.4f}"
        }
    
    def get_description(self) -> str:
        """获取风险控制器描述"""
        return (
            f"回撤控制器 [最大允许回撤: {self.max_drawdown_threshold:.2%}, "
            f"预警阈值: {self.alert_threshold:.2f}, 观察周期: {self.lookback_period}]"
        ) 