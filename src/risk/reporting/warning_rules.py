"""
风险警告规则模块
- 定义常用的风险预警规则
- 提供预警规则的工厂方法
- 支持自定义预警规则创建
"""

from typing import List, Dict, Any, Optional, Union, Callable
import pandas as pd
import numpy as np

from src.risk.monitoring import (
    RiskMonitorInterface, VaRMonitor, VolatilityMonitor,
    DrawdownMonitor, ExposureMonitor, ConcentrationMonitor
)
from src.risk.reporting.warning_system import RiskWarningRule, RiskWarningLevel


class RiskWarningRuleFactory:
    """风险警告规则工厂类"""
    
    @staticmethod
    def create_var_warning_rule(
            var_monitor: Optional[VaRMonitor] = None,
            confidence: float = 0.95,
            threshold: float = 0.05,  # 默认VaR阈值5%
            warning_level: RiskWarningLevel = RiskWarningLevel.MEDIUM,
            name: Optional[str] = None,
            description: Optional[str] = None) -> RiskWarningRule:
        """
        创建风险价值(VaR)预警规则
        
        参数:
            var_monitor: VaR监控器，如果为None则创建新监控器
            confidence: 置信度
            threshold: 阈值
            warning_level: 警告级别
            name: 规则名称
            description: 规则描述
            
        返回:
            RiskWarningRule: VaR预警规则
        """
        # 如果没有提供监控器，创建新的
        if var_monitor is None:
            var_monitor = VaRMonitor(confidence=confidence)
        
        # 生成默认名称和描述
        name = name or f"VaR_{int(confidence*100)}预警"
        description = description or f"风险价值(VaR)超过{threshold*100:.2f}%的预警"
        
        # 创建规则
        return RiskWarningRule(
            name=name,
            monitor=var_monitor,
            threshold=threshold,
            warning_level=warning_level,
            description=description
        )
    
    @staticmethod
    def create_volatility_warning_rule(
            vol_monitor: Optional[VolatilityMonitor] = None,
            window: int = 20,
            threshold: float = 0.20,  # 默认年化波动率阈值20%
            warning_level: RiskWarningLevel = RiskWarningLevel.MEDIUM,
            name: Optional[str] = None,
            description: Optional[str] = None) -> RiskWarningRule:
        """
        创建波动率预警规则
        
        参数:
            vol_monitor: 波动率监控器，如果为None则创建新监控器
            window: 窗口期
            threshold: 阈值
            warning_level: 警告级别
            name: 规则名称
            description: 规则描述
            
        返回:
            RiskWarningRule: 波动率预警规则
        """
        # 如果没有提供监控器，创建新的
        if vol_monitor is None:
            vol_monitor = VolatilityMonitor(window=window, annualize=True)
        
        # 生成默认名称和描述
        name = name or f"波动率_{window}日预警"
        description = description or f"{window}日滚动波动率超过{threshold*100:.2f}%的预警"
        
        # 创建规则
        return RiskWarningRule(
            name=name,
            monitor=vol_monitor,
            threshold=threshold,
            warning_level=warning_level,
            description=description
        )
    
    @staticmethod
    def create_drawdown_warning_rule(
            dd_monitor: Optional[DrawdownMonitor] = None,
            threshold: float = 0.10,  # 默认回撤阈值10%
            warning_level: RiskWarningLevel = RiskWarningLevel.HIGH,
            name: Optional[str] = None,
            description: Optional[str] = None) -> RiskWarningRule:
        """
        创建回撤预警规则
        
        参数:
            dd_monitor: 回撤监控器，如果为None则创建新监控器
            threshold: 阈值
            warning_level: 警告级别
            name: 规则名称
            description: 规则描述
            
        返回:
            RiskWarningRule: 回撤预警规则
        """
        # 如果没有提供监控器，创建新的
        if dd_monitor is None:
            dd_monitor = DrawdownMonitor()
        
        # 生成默认名称和描述
        name = name or f"回撤预警_{threshold*100:.0f}%"
        description = description or f"最大回撤超过{threshold*100:.2f}%的预警"
        
        # 创建规则
        return RiskWarningRule(
            name=name,
            monitor=dd_monitor,
            threshold=threshold,
            warning_level=warning_level,
            description=description
        )
    
    @staticmethod
    def create_exposure_warning_rule(
            exposure_monitor: Optional[ExposureMonitor] = None,
            max_exposure: float = 1.5,  # 默认最大敞口150%
            warning_level: RiskWarningLevel = RiskWarningLevel.MEDIUM,
            name: Optional[str] = None,
            description: Optional[str] = None) -> RiskWarningRule:
        """
        创建敞口预警规则

        参数:
            exposure_monitor: 敞口监控器，如果为None则创建新监控器
            max_exposure: 最大敞口阈值
            warning_level: 警告级别
            name: 规则名称
            description: 规则描述

        返回:
            RiskWarningRule: 敞口预警规则
        """
        # 如果没有提供监控器，创建新的
        if exposure_monitor is None:
            exposure_monitor = ExposureMonitor()
        
        # 生成默认名称和描述
        name = name or f"敞口预警_{max_exposure*100:.0f}%"
        description = description or f"总敞口超过{max_exposure*100:.0f}%的预警"
        
        # 创建规则，自定义检查函数
        def check_exposure(value, threshold):
            return value > threshold
        
        return RiskWarningRule(
            name=name,
            monitor=exposure_monitor,
            threshold=max_exposure,
            warning_level=warning_level,
            description=description,
            check_function=check_exposure
        )
    
    @staticmethod
    def create_concentration_warning_rule(
            concentration_monitor: Optional[ConcentrationMonitor] = None,
            max_concentration: float = 0.30,  # 默认最大集中度30%
            warning_level: RiskWarningLevel = RiskWarningLevel.MEDIUM,
            name: Optional[str] = None,
            description: Optional[str] = None) -> RiskWarningRule:
        """
        创建集中度预警规则
        
        参数:
            concentration_monitor: 集中度监控器，如果为None则创建新监控器
            max_concentration: 最大集中度阈值
            warning_level: 警告级别
            name: 规则名称
            description: 规则描述
            
        返回:
            RiskWarningRule: 集中度预警规则
        """
        # 如果没有提供监控器，创建新的
        if concentration_monitor is None:
            concentration_monitor = ConcentrationMonitor()
        
        # 生成默认名称和描述
        name = name or f"集中度预警_{max_concentration*100:.0f}%"
        description = description or f"最大持仓集中度超过{max_concentration*100:.0f}%的预警"
        
        # 创建规则
        return RiskWarningRule(
            name=name,
            monitor=concentration_monitor,
            threshold=max_concentration,
            warning_level=warning_level,
            description=description
        )
    
    @staticmethod
    def create_consecutive_loss_rule(
            days: int = 3,
            threshold: float = 0.01,  # 默认连续亏损阈值1%
            warning_level: RiskWarningLevel = RiskWarningLevel.MEDIUM,
            name: Optional[str] = None,
            description: Optional[str] = None) -> RiskWarningRule:
        """
        创建连续亏损预警规则
        
        参数:
            days: 连续亏损天数
            threshold: 单日亏损阈值
            warning_level: 警告级别
            name: 规则名称
            description: 规则描述
            
        返回:
            RiskWarningRule: 连续亏损预警规则
        """
        # 创建自定义监控器
        class ConsecutiveLossMonitor(RiskMonitorInterface):
            def __init__(self, days=days, threshold=threshold):
                self.days = days
                self.loss_threshold = threshold
                self.name = f"连续亏损_{days}天"
            
            def calculate(self, returns):
                if len(returns) < self.days:
                    return 0
                
                # 计算连续亏损天数
                consecutive_losses = 0
                max_consecutive_losses = 0
                
                for ret in returns.iloc[-self.days*2:]:
                    if ret < -self.loss_threshold:
                        consecutive_losses += 1
                        max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)
                    else:
                        consecutive_losses = 0
                
                return max_consecutive_losses
            
            def is_risk_exceeded(self, value, threshold):
                return value >= threshold
        
        # 创建监控器
        monitor = ConsecutiveLossMonitor(days=days, threshold=threshold)
        
        # 生成默认名称和描述
        name = name or f"连续亏损预警_{days}天"
        description = description or f"连续{days}天每日亏损超过{threshold*100:.2f}%的预警"
        
        # 创建规则
        return RiskWarningRule(
            name=name,
            monitor=monitor,
            threshold=days,  # 阈值就是连续天数
            warning_level=warning_level,
            description=description
        )
    
    @staticmethod
    def create_sharp_decline_rule(
            decline_percent: float = 0.03,  # 3%的单日下跌
            warning_level: RiskWarningLevel = RiskWarningLevel.HIGH,
            name: Optional[str] = None,
            description: Optional[str] = None) -> RiskWarningRule:
        """
        创建单日大跌预警规则
        
        参数:
            decline_percent: 下跌百分比阈值
            warning_level: 警告级别
            name: 规则名称
            description: 规则描述
            
        返回:
            RiskWarningRule: 单日大跌预警规则
        """
        # 创建自定义监控器
        class SharpDeclineMonitor(RiskMonitorInterface):
            def __init__(self, threshold=decline_percent):
                self.threshold = threshold
                self.name = f"单日大跌_{threshold*100:.0f}%"
            
            def calculate(self, returns):
                if len(returns) == 0:
                    return 0
                
                # 获取最近的收益率
                latest_return = returns.iloc[-1]
                return abs(min(latest_return, 0))
            
            def is_risk_exceeded(self, value, threshold):
                return value >= threshold
        
        # 创建监控器
        monitor = SharpDeclineMonitor(threshold=decline_percent)
        
        # 生成默认名称和描述
        name = name or f"单日大跌预警_{decline_percent*100:.0f}%"
        description = description or f"单日下跌超过{decline_percent*100:.1f}%的预警"
        
        # 创建规则
        return RiskWarningRule(
            name=name,
            monitor=monitor,
            threshold=decline_percent,
            warning_level=warning_level,
            description=description
        )
    
    @staticmethod
    def create_rate_of_change_rule(
            window: int = 5,
            threshold: float = 0.10,  # 10%的变化率
            warning_level: RiskWarningLevel = RiskWarningLevel.MEDIUM,
            name: Optional[str] = None,
            description: Optional[str] = None) -> RiskWarningRule:
        """
        创建变化率预警规则
        
        参数:
            window: 窗口期
            threshold: 变化率阈值
            warning_level: 警告级别
            name: 规则名称
            description: 规则描述
            
        返回:
            RiskWarningRule: 变化率预警规则
        """
        # 创建自定义监控器
        class RateOfChangeMonitor(RiskMonitorInterface):
            def __init__(self, window=window):
                self.window = window
                self.name = f"变化率_{window}日"
            
            def calculate(self, returns):
                if len(returns) < self.window:
                    return 0
                
                # 计算累积收益率
                prices = (1 + returns).cumprod()
                
                # 计算变化率
                if len(prices) <= self.window:
                    return 0
                
                roc = abs((prices.iloc[-1] / prices.iloc[-(self.window+1)] - 1))
                return roc
            
            def is_risk_exceeded(self, value, threshold):
                return value >= threshold
        
        # 创建监控器
        monitor = RateOfChangeMonitor(window=window)
        
        # 生成默认名称和描述
        name = name or f"变化率预警_{window}日"
        description = description or f"{window}日内收益变化率超过{threshold*100:.0f}%的预警"
        
        # 创建规则
        return RiskWarningRule(
            name=name,
            monitor=monitor,
            threshold=threshold,
            warning_level=warning_level,
            description=description
        )
    
    @staticmethod
    def create_custom_rule(
            name: str,
            monitor: RiskMonitorInterface,
            threshold: float,
            warning_level: RiskWarningLevel,
            description: Optional[str] = None,
            check_function: Optional[Callable] = None) -> RiskWarningRule:
        """
        创建自定义预警规则
        
        参数:
            name: 规则名称
            monitor: 风险监控器
            threshold: 阈值
            warning_level: 警告级别
            description: 规则描述
            check_function: 自定义检查函数
            
        返回:
            RiskWarningRule: 自定义预警规则
        """
        return RiskWarningRule(
            name=name,
            monitor=monitor,
            threshold=threshold,
            warning_level=warning_level,
            description=description,
            check_function=check_function
        )


def create_default_warning_rules() -> List[RiskWarningRule]:
    """
    创建默认的预警规则集合
    
    返回:
        List[RiskWarningRule]: 默认预警规则列表
    """
    factory = RiskWarningRuleFactory()
    
    rules = [
        # VaR规则 (95%置信度，超过5%为中等风险)
        factory.create_var_warning_rule(
            confidence=0.95,
            threshold=0.05,
            warning_level=RiskWarningLevel.MEDIUM
        ),
        
        # VaR规则 (99%置信度，超过10%为高风险)
        factory.create_var_warning_rule(
            confidence=0.99,
            threshold=0.10,
            warning_level=RiskWarningLevel.HIGH
        ),
        
        # 波动率规则 (20日波动率超过20%为中等风险)
        factory.create_volatility_warning_rule(
            window=20,
            threshold=0.20,
            warning_level=RiskWarningLevel.MEDIUM
        ),
        
        # 波动率规则 (10日波动率超过30%为高风险)
        factory.create_volatility_warning_rule(
            window=10,
            threshold=0.30,
            warning_level=RiskWarningLevel.HIGH
        ),
        
        # 回撤规则 (回撤超过10%为中等风险)
        factory.create_drawdown_warning_rule(
            threshold=0.10,
            warning_level=RiskWarningLevel.MEDIUM
        ),
        
        # 回撤规则 (回撤超过20%为高风险)
        factory.create_drawdown_warning_rule(
            threshold=0.20,
            warning_level=RiskWarningLevel.HIGH
        ),
        
        # 回撤规则 (回撤超过30%为紧急风险)
        factory.create_drawdown_warning_rule(
            threshold=0.30,
            warning_level=RiskWarningLevel.CRITICAL
        ),
        
        # 敞口规则 (敞口超过150%为中等风险)
        factory.create_exposure_warning_rule(
            max_exposure=1.5,
            warning_level=RiskWarningLevel.MEDIUM
        ),
        
        # 敞口规则 (敞口超过200%为高风险)
        factory.create_exposure_warning_rule(
            max_exposure=2.0,
            warning_level=RiskWarningLevel.HIGH
        ),
        
        # 集中度规则 (单一资产集中度超过30%为中等风险)
        factory.create_concentration_warning_rule(
            max_concentration=0.30,
            warning_level=RiskWarningLevel.MEDIUM
        ),
        
        # 集中度规则 (单一资产集中度超过50%为高风险)
        factory.create_concentration_warning_rule(
            max_concentration=0.50,
            warning_level=RiskWarningLevel.HIGH
        ),
        
        # 连续亏损规则 (连续3天每天亏损超过1%为中等风险)
        factory.create_consecutive_loss_rule(
            days=3,
            threshold=0.01,
            warning_level=RiskWarningLevel.MEDIUM
        ),
        
        # 连续亏损规则 (连续5天每天亏损超过1%为高风险)
        factory.create_consecutive_loss_rule(
            days=5,
            threshold=0.01,
            warning_level=RiskWarningLevel.HIGH
        ),
        
        # 单日大跌规则 (单日下跌超过3%为中等风险)
        factory.create_sharp_decline_rule(
            decline_percent=0.03,
            warning_level=RiskWarningLevel.MEDIUM
        ),
        
        # 单日大跌规则 (单日下跌超过5%为高风险)
        factory.create_sharp_decline_rule(
            decline_percent=0.05,
            warning_level=RiskWarningLevel.HIGH
        ),
        
        # 单日大跌规则 (单日下跌超过8%为紧急风险)
        factory.create_sharp_decline_rule(
            decline_percent=0.08,
            warning_level=RiskWarningLevel.CRITICAL
        ),
        
        # 变化率规则 (5日内收益变化率超过10%为中等风险)
        factory.create_rate_of_change_rule(
            window=5,
            threshold=0.10,
            warning_level=RiskWarningLevel.MEDIUM
        ),
    ]
    
    return rules 