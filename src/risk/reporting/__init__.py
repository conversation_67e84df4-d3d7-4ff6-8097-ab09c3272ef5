"""
风险报告模块

该模块提供风险报告和预警系统的功能:
- 生成风险分析报告，包括风险指标、图表和分析
- 支持定期风险监控报告
- 实现风险预警系统，自动识别和通知风险事件
- 提供各种风险可视化工具
"""

# 导入报告生成器相关类和函数
from src.risk.reporting.report_generator import RiskReportGenerator, generate_risk_report

# 导入风险预警系统相关类和函数
from src.risk.reporting.warning_system import RiskWarningSystem, RiskWarningLevel, RiskWarningRule
from src.risk.reporting.warning_rules import RiskWarningRuleFactory, create_default_warning_rules

__all__ = [
    # 报告生成器
    'RiskReportGenerator',
    'generate_risk_report',
    
    # 风险预警系统
    'RiskWarningSystem',
    'RiskWarningLevel',
    'RiskWarningRule',
    'RiskWarningRuleFactory',
    'create_default_warning_rules',
] 