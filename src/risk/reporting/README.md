# 风险报告与预警系统

本模块提供完整的风险报告生成和风险预警功能，支持量化交易和投资组合管理过程中的风险管理需求。

## 功能特点

### 风险报告生成器
- 支持生成多种格式的风险报告（HTML、PDF、JSON）
- 自动生成风险仪表板，可视化展示关键风险指标
- 提供风险摘要表，突出显示重要指标
- 支持自定义风险监控器和报告模板
- 可配置的报告组件，包括图表、表格和摘要

### 风险预警系统
- 支持多级别风险预警（信息、低风险、中等风险、高风险、紧急风险）
- 可配置的预警规则和阈值
- 提供丰富的预警规则工厂方法
- 支持多种通知方式（控制台、邮件、Webhook、短信等）
- 预警历史记录和加载功能
- 支持自定义风险监控器和检查函数

## 主要组件

### 报告生成
- `RiskReportGenerator`: 风险报告生成器类
- `generate_risk_report`: 便捷的报告生成函数

### 预警系统
- `RiskWarningSystem`: 风险预警系统类
- `RiskWarningLevel`: 风险预警级别枚举
- `RiskWarningRule`: 风险预警规则类
- `RiskWarningRuleFactory`: 风险预警规则工厂类
- `create_default_warning_rules`: 创建默认预警规则集合

## 使用示例

### 生成风险报告

```python
from risk.reporting import generate_risk_report

# 使用便捷函数生成报告
report_path = generate_risk_report(
    returns=returns_series,
    positions=positions_dict,
    title="投资组合风险报告",
    report_format="html",
    output_dir="reports",
    include_summary=True,
    include_charts=True
)
```

### 设置风险预警系统

```python
from risk.reporting import (
    RiskWarningSystem, RiskWarningLevel,
    RiskWarningRuleFactory, create_default_warning_rules
)

# 创建预警系统
warning_system = RiskWarningSystem(
    rules=create_default_warning_rules(),  # 使用默认规则集
    warning_log_dir="logs/warnings"
)

# 注册通知处理器
warning_system.create_default_email_handler(
    smtp_server="smtp.example.com",
    smtp_port=587,
    sender_email="<EMAIL>",
    sender_password="password",
    recipient_emails=["<EMAIL>"]
)

# 检查风险指标
triggered_warnings = warning_system.check_warnings(risk_metrics)
```

## 完整示例

请参考 `examples/risk_reporting_example.py` 文件，其中包含风险报告生成和预警系统的完整使用示例。

## 单元测试

模块包含全面的单元测试：
- `tests/test_risk_report_generator.py`: 测试风险报告生成器
- `tests/test_risk_warning_system.py`: 测试风险预警系统 