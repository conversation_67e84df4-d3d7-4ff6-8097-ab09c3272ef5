"""
风险报告生成器模块
- 基于风险监控结果生成报告
- 支持多种报告格式和模板
- 可集成各类风险指标和可视化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any, List, Optional, Union, Tuple
import os
import datetime
import io
import base64
import json
from pathlib import Path
import matplotlib

from src.risk.monitoring import (
    VaRMonitor,
    VolatilityMonitor,
    DrawdownMonitor
)
from src.reports.visualization.matplotlib_adapter import MatplotlibAdapter

matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False

class RiskReportGenerator:
    """风险报告生成器类"""
    
    def __init__(self, 
                 returns: pd.Series,
                 positions: Optional[pd.DataFrame] = None,
                 risk_monitors: Optional[List[Any]] = None,
                 title: str = "风险分析报告",
                 report_date: Optional[datetime.datetime] = None,
                 use_chinese: bool = True):
        """
        初始化风险报告生成器
        
        参数:
            returns: 收益率序列
            positions: 持仓数据
            risk_monitors: 风险监控器列表
            title: 报告标题
            report_date: 报告日期，默认为当前日期
            use_chinese: 是否使用中文
        """
        self.returns = returns
        self.positions = positions
        self.title = title
        self.report_date = report_date or datetime.datetime.now()
        self.use_chinese = use_chinese
        
        # 初始化风险监控器
        if risk_monitors is None:
            # 创建默认风险监控器
            self.risk_monitors = self._create_default_monitors()
        else:
            self.risk_monitors = risk_monitors
        
        # 执行风险计算
        self.risk_metrics = self._calculate_risk_metrics()
        
        # 初始化matplotlib适配器
        MatplotlibAdapter.init(use_chinese=use_chinese)
        
        # 设置绘图风格
        sns.set_style('whitegrid')
        plt.rcParams['figure.figsize'] = (12, 8)
    
    def _create_default_monitors(self) -> List[Any]:
        """
        创建默认风险监控器
        
        返回:
            List[Any]: 风险监控器列表
        """
        monitors = [
            VaRMonitor(method="historical", confidence_level=0.95),
            VaRMonitor(method="parametric", confidence_level=0.99),
            VolatilityMonitor(window=20),
            DrawdownMonitor()
        ]
        return monitors
    
    def _calculate_risk_metrics(self) -> Dict[str, Any]:
        """
        计算所有风险指标
        
        返回:
            Dict[str, Any]: 风险指标结果
        """
        metrics = {}
        
        # 使用每个监控器计算指标
        for monitor in self.risk_monitors:
            monitor_metrics = monitor.calculate(self.returns)
            monitor_name = monitor.name
            metrics[monitor_name] = monitor_metrics
        
        return metrics
    
    def plot_risk_dashboard(self, figsize: Tuple[int, int] = (14, 10)) -> plt.Figure:
        """
        绘制风险仪表盘
        
        参数:
            figsize: 图表尺寸
            
        返回:
            matplotlib Figure对象
        """
        fig, axes = plt.subplots(2, 2, figsize=figsize)
        axes = axes.flatten()
        
        # 绘制净值曲线和回撤
        ax1 = axes[0]
        self._plot_equity_and_drawdown(ax1)
        
        # 绘制滚动波动率
        ax2 = axes[1]
        self._plot_rolling_volatility(ax2)
        
        # 绘制历史VaR
        ax3 = axes[2]
        self._plot_var_analysis(ax3)
        
        # 绘制风险分布
        ax4 = axes[3]
        self._plot_risk_distribution(ax4)
        
        # 设置图表标题和布局
        fig.suptitle(f"{self.title}", fontsize=16, y=0.98)
        fig.text(0.5, 0.01, f"报告生成日期: {self.report_date.strftime('%Y-%m-%d')}", 
                 ha='center', fontsize=10)
        
        plt.tight_layout(rect=[0, 0.03, 1, 0.95])
        return fig
    
    def _plot_equity_and_drawdown(self, ax) -> None:
        """
        绘制净值曲线和回撤
        
        参数:
            ax: matplotlib轴对象
        """
        # 计算净值和回撤
        equity_curve = (1 + self.returns).cumprod()
        rolling_max = equity_curve.cummax()
        drawdown = (equity_curve - rolling_max) / rolling_max
        
        # 创建双Y轴
        ax_drawdown = ax.twinx()
        
        # 绘制净值曲线
        ax.plot(equity_curve.index, equity_curve, 'b-', label='净值曲线')
        
        # 绘制回撤曲线
        ax_drawdown.fill_between(
            drawdown.index, 
            0, 
            drawdown, 
            color='r', 
            alpha=0.3, 
            label='回撤'
        )
        
        # 设置轴标签和图例
        ax.set_ylabel('净值', color='b')
        ax_drawdown.set_ylabel('回撤', color='r')
        
        # 设置Y轴范围
        ax.set_ylim(bottom=max(0, equity_curve.min() * 0.9))
        ax_drawdown.set_ylim(top=0, bottom=min(-0.01, drawdown.min() * 1.1))
        
        # 为双轴添加图例
        lines1, labels1 = ax.get_legend_handles_labels()
        lines2, labels2 = ax_drawdown.get_legend_handles_labels()
        ax.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
        
        # 设置图表样式
        MatplotlibAdapter.set_chart_style(
            ax, 
            title='净值曲线与回撤', 
            fontsize=12, 
            use_chinese=self.use_chinese
        )
    
    def _plot_rolling_volatility(self, ax) -> None:
        """
        绘制滚动波动率
        
        参数:
            ax: matplotlib轴对象
        """
        # 计算多个窗口的滚动波动率
        windows = [20, 60, 120]
        colors = ['blue', 'green', 'red']
        
        for window, color in zip(windows, colors):
            rolling_vol = self.returns.rolling(window=window).std() * np.sqrt(252)
            ax.plot(
                rolling_vol.index, 
                rolling_vol, 
                color=color, 
                label=f'{window}日波动率'
            )
        
        # 添加平均波动率线
        avg_vol = self.returns.std() * np.sqrt(252)
        ax.axhline(
            y=avg_vol, 
            color='black', 
            linestyle='--', 
            label=f'平均波动率: {avg_vol:.2%}'
        )
        
        # 设置图表样式
        MatplotlibAdapter.set_chart_style(
            ax, 
            title='滚动波动率', 
            ylabel='年化波动率', 
            fontsize=12, 
            use_chinese=self.use_chinese
        )
    
    def _plot_var_analysis(self, ax) -> None:
        """
        绘制风险价值分析
        
        参数:
            ax: matplotlib轴对象
        """
        # 提取VaR数据
        var_data = {}
        for monitor_name, metrics in self.risk_metrics.items():
            if 'VaR' in monitor_name:
                for metric_name, value in metrics.items():
                    if 'VaR' in metric_name:
                        var_data[f"{monitor_name}: {metric_name}"] = value
        
        if not var_data:
            ax.text(0.5, 0.5, 'VaR数据不可用', ha='center', va='center')
            return
        
        # 绘制柱状图
        var_series = pd.Series(var_data)
        var_series = var_series.sort_values(ascending=True)
        
        bars = ax.barh(
            range(len(var_series)), 
            var_series.abs().values,
            color=['r' if x < 0 else 'g' for x in var_series.values],
            alpha=0.7
        )
        
        # 在柱状图上添加数值标签
        for i, v in enumerate(var_series.values):
            ax.text(
                max(0.001, abs(v) * 1.05),
                i,
                f'{v:.2%}',
                va='center'
            )
        
        # 设置Y轴标签
        ax.set_yticks(range(len(var_series)))
        ax.set_yticklabels(var_series.index)
        
        # 设置图表样式
        MatplotlibAdapter.set_chart_style(
            ax, 
            title='风险价值分析 (VaR)', 
            xlabel='风险价值', 
            fontsize=12, 
            use_chinese=self.use_chinese
        )
    
    def _plot_risk_distribution(self, ax) -> None:
        """
        绘制风险分布
        
        参数:
            ax: matplotlib轴对象
        """
        # 绘制收益率分布直方图
        sns.histplot(
            self.returns, 
            kde=True, 
            stat="density", 
            color='skyblue',
            ax=ax
        )
        
        # 绘制正态分布参考线
        from scipy import stats
        x = np.linspace(self.returns.min(), self.returns.max(), 100)
        mu, sigma = self.returns.mean(), self.returns.std()
        normal_dist = stats.norm.pdf(x, mu, sigma)
        ax.plot(x, normal_dist, 'r--', label='正态分布')
        
        # 添加均值和标准差的垂直线
        ax.axvline(x=mu, color='g', linestyle='-', label=f'均值: {mu:.2%}')
        ax.axvline(x=mu + sigma, color='y', linestyle=':', 
                   label=f'+1σ: {(mu + sigma):.2%}')
        ax.axvline(x=mu - sigma, color='y', linestyle=':', 
                   label=f'-1σ: {(mu - sigma):.2%}')
        
        # 设置图表样式
        MatplotlibAdapter.set_chart_style(
            ax, 
            title='收益率分布', 
            xlabel='日收益率', 
            ylabel='密度',
            fontsize=12, 
            use_chinese=self.use_chinese
        )
    
    def generate_report(self, output_dir: Optional[str] = None, 
                       filename: Optional[str] = None,
                       format: str = 'html') -> Optional[str]:
        """
        生成完整风险报告
        
        参数:
            output_dir: 输出目录
            filename: 输出文件名
            format: 输出格式 ('html', 'pdf', 'json')
            
        返回:
            str: 输出文件路径或报告内容
        """
        if format not in ['html', 'pdf', 'json']:
            raise ValueError("输出格式必须为 'html', 'pdf' 或 'json'")
        
        # 创建风险仪表盘
        risk_dashboard = self.plot_risk_dashboard()
        
        # 准备风险指标概要
        risk_summary = self._create_risk_summary()
        
        # 根据格式生成报告
        if format == 'json':
            # 生成JSON报告
            return self._generate_json_report(output_dir, filename)
        elif format in ['html', 'pdf']:
            # 生成HTML或PDF报告
            return self._generate_html_report(risk_dashboard, risk_summary, output_dir, filename, format)
    
    def _create_risk_summary(self) -> pd.DataFrame:
        """
        创建风险指标概要表格
        
        返回:
            pd.DataFrame: 风险指标概要表格
        """
        summary_data = {
            '指标类别': [],
            '指标名称': [],
            '当前值': [],
            '风险等级': [],
            '30天趋势': []
        }
        
        # 填充风险指标数据
        for monitor_name, metrics in self.risk_metrics.items():
            monitor = next((m for m in self.risk_monitors if m.name == monitor_name), None)
            if not monitor:
                continue
                
            for metric_name, value in metrics.items():
                # 跳过复杂类型
                if isinstance(value, (dict, list, pd.Series, pd.DataFrame)):
                    continue
                    
                # 处理数值类型指标
                if isinstance(value, (int, float)):
                    risk_level = monitor.get_risk_level(value) if hasattr(monitor, 'get_risk_level') else "未知"
                    
                    # 计算趋势（示例，实际应根据历史数据计算）
                    trend = "→"  # 默认为稳定
                    
                    summary_data['指标类别'].append(monitor_name)
                    summary_data['指标名称'].append(metric_name)
                    summary_data['当前值'].append(value)
                    summary_data['风险等级'].append(risk_level)
                    summary_data['30天趋势'].append(trend)
        
        return pd.DataFrame(summary_data)
    
    def _generate_json_report(self, output_dir: Optional[str], filename: Optional[str]) -> str:
        """
        生成JSON格式的风险报告
        
        参数:
            output_dir: 输出目录
            filename: 文件名
            
        返回:
            str: JSON文本或文件路径
        """
        # 准备JSON数据
        report_data = {
            'title': self.title,
            'report_date': self.report_date.strftime('%Y-%m-%d'),
            'risk_metrics': {}
        }
        
        # 转换风险指标为JSON格式
        for monitor_name, metrics in self.risk_metrics.items():
            serializable_metrics = {}
            for k, v in metrics.items():
                # 处理不可序列化的对象
                if isinstance(v, (pd.Series, pd.DataFrame)):
                    continue
                elif isinstance(v, np.ndarray):
                    serializable_metrics[k] = v.tolist()
                elif isinstance(v, (datetime.date, datetime.datetime)):
                    serializable_metrics[k] = v.isoformat()
                else:
                    try:
                        # 尝试序列化
                        json.dumps({k: v})
                        serializable_metrics[k] = v
                    except (TypeError, OverflowError):
                        # 忽略不可序列化的值
                        pass
            
            report_data['risk_metrics'][monitor_name] = serializable_metrics
        
        # 将report_info添加到JSON数据中，解决测试失败
        report_data['report_info'] = {
            'title': self.title,
            'date': self.report_date.strftime('%Y-%m-%d'),
            'generated_at': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 转换为JSON字符串
        json_report = json.dumps(report_data, indent=4, ensure_ascii=False)
        
        # 如果未指定输出目录，则直接返回JSON内容
        if output_dir is None:
            return json_report
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 默认文件名
        if filename is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"risk_report_{timestamp}.json"
        elif not filename.lower().endswith('.json'):
            filename = f"{filename}.json"
        
        # 写入JSON文件
        json_path = os.path.join(output_dir, filename)
        with open(json_path, 'w', encoding='utf-8') as f:
            f.write(json_report)
        
        return json_path
    
    def _generate_html_report(self, risk_dashboard, risk_summary, 
                             output_dir: Optional[str], 
                             filename: Optional[str],
                             format: str) -> str:
        """
        生成HTML或PDF格式的风险报告
        
        参数:
            risk_dashboard: 风险仪表盘图表
            risk_summary: 风险概要表格
            output_dir: 输出目录
            filename: 文件名
            format: 输出格式 ('html', 'pdf')
            
        返回:
            str: HTML内容或文件路径
        """
        # 将图表转换为base64编码
        def fig_to_base64(fig):
            buf = io.BytesIO()
            fig.savefig(buf, format='png', dpi=120)
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)  # 释放内存
            return img_str
        
        # 转换风险仪表盘
        dashboard_base64 = fig_to_base64(risk_dashboard)
        
        # 创建HTML表格
        risk_table_html = risk_summary.to_html(
            index=False, 
            classes='table table-striped table-hover',
            border=0
        )
        
        # 创建HTML报告
        html_template = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{self.title}</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 1px solid #ddd;
                    padding-bottom: 10px;
                }}
                .section {{
                    margin-bottom: 40px;
                }}
                h1 {{
                    color: #2c3e50;
                }}
                h2 {{
                    color: #3498db;
                    border-bottom: 1px solid #eee;
                    padding-bottom: 5px;
                }}
                h3 {{
                    color: #2980b9;
                }}
                .chart {{
                    margin: 20px 0;
                    text-align: center;
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                }}
                th, td {{
                    padding: 12px 15px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }}
                th {{
                    background-color: #f8f9fa;
                    font-weight: bold;
                }}
                tr:hover {{
                    background-color: #f5f5f5;
                }}
                .footer {{
                    margin-top: 50px;
                    text-align: center;
                    font-size: 0.8em;
                    color: #7f8c8d;
                    border-top: 1px solid #ddd;
                    padding-top: 20px;
                }}
                .table-striped tbody tr:nth-of-type(odd) {{
                    background-color: rgba(0, 0, 0, 0.05);
                }}
                .table-hover tbody tr:hover {{
                    background-color: rgba(0, 0, 0, 0.075);
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{self.title}</h1>
                <p>报告日期: {self.report_date.strftime('%Y-%m-%d')}</p>
            </div>
            
            <div class="section">
                <h2>风险摘要</h2>
                {risk_table_html}
            </div>
            
            <div class="section">
                <h2>风险监控仪表盘</h2>
                <div class="chart">
                    <img src="data:image/png;base64,{dashboard_base64}" width="100%">
                </div>
            </div>
            
            <div class="section">
                <h2>风险详情</h2>
                <h3>风险值 (VaR)</h3>
                <p>
                    风险值(Value at Risk)表示在给定置信水平下，投资组合在特定时间段内可能的最大损失。
                    以下是不同计算方法和置信水平下的VaR值。
                </p>
                
                <h3>波动率分析</h3>
                <p>
                    波动率是衡量投资组合价格变动幅度的指标。较高的波动率通常意味着较高的风险。
                    以下是不同时间窗口下的滚动波动率分析。
                </p>
                
                <h3>回撤分析</h3>
                <p>
                    回撤是指投资组合从高点到低点的最大跌幅。最大回撤是衡量下行风险的重要指标。
                    较低的回撤表明投资组合在不利市场环境中的表现较为稳健。
                </p>
            </div>
            
            <div class="footer">
                <p>报告生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>量化交易策略风险管理系统</p>
            </div>
        </body>
        </html>
        """
        
        # 如果未指定输出目录，则直接返回HTML内容
        if output_dir is None:
            return html_template
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 默认文件名
        if filename is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"risk_report_{timestamp}"
        
        # 添加正确的扩展名（确保不重复添加）
        if not filename.lower().endswith('.html'):
            html_filename = f"{filename}.html"
        else:
            html_filename = filename
        
        # 写入HTML文件
        html_path = os.path.join(output_dir, html_filename)
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_template)
        
        # 如果需要PDF格式
        if format == 'pdf':
            try:
                from weasyprint import HTML
                
                # 确保PDF文件名不重复添加扩展名
                if filename.lower().endswith('.html'):
                    pdf_filename = filename[:-5] + '.pdf'
                elif not filename.lower().endswith('.pdf'):
                    pdf_filename = f"{filename}.pdf"
                else:
                    pdf_filename = filename
                
                pdf_path = os.path.join(output_dir, pdf_filename)
                HTML(string=html_template).write_pdf(pdf_path)
                return pdf_path
            except ImportError:
                print("警告: 未安装WeasyPrint库，无法生成PDF报告。请使用 pip install weasyprint 安装。")
                return html_path
        
        return html_path


def generate_risk_report(returns: pd.Series,
                        positions: Optional[pd.DataFrame] = None,
                        risk_monitors: Optional[List[Any]] = None,
                        title: str = "风险分析报告",
                        report_date: Optional[datetime.datetime] = None,
                        output_dir: Optional[str] = None,
                        filename: Optional[str] = None,
                        format: str = 'html',
                        use_chinese: bool = True) -> str:
    """
    生成风险报告的快捷函数
    
    参数:
        returns: 收益率序列
        positions: 持仓数据
        risk_monitors: 风险监控器列表
        title: 报告标题
        report_date: 报告日期
        output_dir: 输出目录
        filename: 输出文件名
        format: 输出格式，可选 'html', 'json', 'pdf'
        use_chinese: 是否使用中文
        
    返回:
        str: 报告文件路径
    """
    report_generator = RiskReportGenerator(
        returns=returns,
        positions=positions,
        risk_monitors=risk_monitors,
        title=title,
        report_date=report_date,
        use_chinese=use_chinese
    )
    
    return report_generator.generate_report(
        output_dir=output_dir,
        filename=filename,
        format=format
    ) 