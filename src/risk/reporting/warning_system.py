"""
风险预警系统模块
- 提供风险监控和预警机制
- 支持多级别风险预警
- 可配置的预警规则和通知方式
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Union, Tuple, Callable
import datetime
import json
import os
import logging
import requests
from enum import Enum
from pathlib import Path
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

from src.risk.monitoring import RiskMonitorInterface


class RiskWarningLevel(Enum):
    """风险预警级别枚举"""
    INFO = 0      # 信息级别，一般提示
    LOW = 1       # 低风险警告
    MEDIUM = 2    # 中等风险警告
    HIGH = 3      # 高风险警告
    CRITICAL = 4  # 紧急风险警告


class RiskWarningRule:
    """风险预警规则类"""
    
    def __init__(self, 
                 name: str,
                 monitor: RiskMonitorInterface,
                 threshold: float,
                 warning_level: RiskWarningLevel,
                 description: str = "",
                 check_function: Optional[Callable] = None):
        """
        初始化风险预警规则
        
        参数:
            name: 规则名称
            monitor: 关联的风险监控器
            threshold: 风险阈值
            warning_level: 风险级别
            description: 规则描述
            check_function: 自定义检查函数，如果为None则使用monitor的is_risk_exceeded方法
        """
        self.name = name
        self.monitor = monitor
        self.threshold = threshold
        self.warning_level = warning_level
        self.description = description or f"{monitor.name}风险预警规则"
        self.check_function = check_function
    
    def check(self, value: float) -> bool:
        """
        检查风险值是否超过阈值
        
        参数:
            value: 当前风险值
            
        返回:
            bool: 是否触发预警
        """
        if self.check_function is not None:
            return self.check_function(value, self.threshold)
        else:
            return self.monitor.is_risk_exceeded(value, self.threshold)
    
    def get_message(self, value: float) -> str:
        """
        获取预警消息
        
        参数:
            value: 当前风险值
            
        返回:
            str: 预警消息
        """
        return f"{self.description}: 当前值 {value:.4f} 超过阈值 {self.threshold:.4f}"
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将规则转换为字典
        
        返回:
            Dict[str, Any]: 规则字典
        """
        return {
            "name": self.name,
            "monitor": self.monitor.name,
            "threshold": self.threshold,
            "warning_level": self.warning_level.name,
            "description": self.description,
        }


class RiskWarningSystem:
    """风险预警系统类"""
    
    def __init__(self, 
                 rules: Optional[List[RiskWarningRule]] = None,
                 warning_log_dir: Optional[str] = None,
                 notification_handlers: Optional[Dict[str, Callable]] = None):
        """
        初始化风险预警系统
        
        参数:
            rules: 预警规则列表
            warning_log_dir: 预警日志目录
            notification_handlers: 通知处理器字典，格式为 {类型: 处理函数}
        """
        self.rules = rules or []
        self.warning_log_dir = warning_log_dir
        
        # 初始化通知处理器
        self.notification_handlers = notification_handlers or {}
        
        # 初始化日志
        self.logger = self._setup_logging()
        
        # 预警历史记录
        self.warning_history = []
    
    def _setup_logging(self) -> logging.Logger:
        """
        设置日志记录器
        
        返回:
            logging.Logger: 配置好的日志记录器
        """
        logger = logging.getLogger("risk_warning_system")
        logger.setLevel(logging.INFO)
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 如果指定了日志目录，添加文件处理器
        if self.warning_log_dir:
            os.makedirs(self.warning_log_dir, exist_ok=True)
            log_file = os.path.join(self.warning_log_dir, "risk_warnings.log")
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.INFO)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def add_rule(self, rule: RiskWarningRule) -> None:
        """
        添加预警规则
        
        参数:
            rule: 预警规则
        """
        self.rules.append(rule)
        self.logger.info(f"添加预警规则: {rule.name}, 监控器: {rule.monitor.name}, 级别: {rule.warning_level.name}")
    
    def remove_rule(self, rule_name: str) -> bool:
        """
        移除预警规则
        
        参数:
            rule_name: 规则名称
            
        返回:
            bool: 是否成功移除
        """
        for i, rule in enumerate(self.rules):
            if rule.name == rule_name:
                self.rules.pop(i)
                self.logger.info(f"移除预警规则: {rule_name}")
                return True
        
        self.logger.warning(f"未找到预警规则: {rule_name}")
        return False
    
    def register_notification_handler(self, notification_type: str, handler: Callable) -> None:
        """
        注册通知处理器
        
        参数:
            notification_type: 通知类型（如email, sms, webhook等）
            handler: 处理函数，接收预警信息字典作为参数
        """
        self.notification_handlers[notification_type] = handler
        self.logger.info(f"注册通知处理器: {notification_type}")
    
    def check_warnings(self, 
                       risk_metrics: Dict[str, Any],
                       send_notifications: bool = True) -> List[Dict[str, Any]]:
        """
        检查所有风险预警规则
        
        参数:
            risk_metrics: 风险指标字典，格式为 {监控器名称: {指标名称: 值}}
            send_notifications: 是否发送通知
            
        返回:
            List[Dict[str, Any]]: 触发的预警信息列表
        """
        triggered_warnings = []
        
        # 检查每个规则
        for rule in self.rules:
            monitor_name = rule.monitor.name
            
            # 检查监控器数据是否存在
            if monitor_name not in risk_metrics:
                self.logger.warning(f"未找到监控器数据: {monitor_name}")
                continue
            
            monitor_metrics = risk_metrics[monitor_name]
            
            # 对每个指标进行检查
            for metric_name, value in monitor_metrics.items():
                # 跳过非数值类型
                if not isinstance(value, (int, float)):
                    continue
                
                # 检查是否触发预警
                if rule.check(value):
                    # 创建预警信息
                    warning_info = {
                        "timestamp": datetime.datetime.now().isoformat(),
                        "rule_name": rule.name,
                        "monitor_name": monitor_name,
                        "metric_name": metric_name,
                        "current_value": value,
                        "threshold": rule.threshold,
                        "warning_level": rule.warning_level.name,
                        "message": rule.get_message(value)
                    }
                    
                    # 记录预警信息
                    self._log_warning(warning_info)
                    
                    # 添加到历史记录
                    self.warning_history.append(warning_info)
                    
                    # 添加到触发列表
                    triggered_warnings.append(warning_info)
                    
                    # 发送通知
                    if send_notifications:
                        self._send_notifications(warning_info)
        
        return triggered_warnings
    
    def _log_warning(self, warning_info: Dict[str, Any]) -> None:
        """
        记录预警信息
        
        参数:
            warning_info: 预警信息字典
        """
        level_name = warning_info["warning_level"]
        message = warning_info["message"]
        
        # 根据级别选择日志级别
        log_level = getattr(logging, level_name if level_name != "INFO" else "INFO")
        
        # 记录日志
        self.logger.log(log_level, f"[{level_name}] {message}")
    
    def _send_notifications(self, warning_info: Dict[str, Any]) -> None:
        """
        发送预警通知
        
        参数:
            warning_info: 预警信息字典
        """
        # 只有达到一定级别的预警才发送通知
        warning_level = RiskWarningLevel[warning_info["warning_level"]]
        
        # 遍历所有通知处理器
        for notification_type, handler in self.notification_handlers.items():
            try:
                # 调用处理器
                handler(warning_info)
                self.logger.info(f"已发送 {notification_type} 通知, 级别: {warning_level.name}")
            except Exception as e:
                self.logger.error(f"发送 {notification_type} 通知失败: {str(e)}")
    
    def get_warning_history(self, 
                           start_time: Optional[datetime.datetime] = None,
                           end_time: Optional[datetime.datetime] = None,
                           min_level: Optional[RiskWarningLevel] = None) -> List[Dict[str, Any]]:
        """
        获取预警历史记录
        
        参数:
            start_time: 开始时间
            end_time: 结束时间
            min_level: 最低级别
            
        返回:
            List[Dict[str, Any]]: 预警历史记录列表
        """
        filtered_history = self.warning_history
        
        # 过滤时间范围
        if start_time or end_time:
            filtered_history = []
            for warning in self.warning_history:
                warning_time = datetime.datetime.fromisoformat(warning["timestamp"])
                
                if start_time and warning_time < start_time:
                    continue
                
                if end_time and warning_time > end_time:
                    continue
                
                filtered_history.append(warning)
        
        # 过滤级别
        if min_level:
            filtered_history = [
                warning for warning in filtered_history
                if RiskWarningLevel[warning["warning_level"]].value >= min_level.value
            ]
        
        return filtered_history
    
    def save_warning_history(self, filepath: str) -> None:
        """
        保存预警历史记录到文件
        
        参数:
            filepath: 文件路径
        """
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)
        
        # 保存为JSON格式
        with open(filepath, 'w', encoding='utf-8') as f:
            # 处理日期时间对象
            serializable_history = []
            for warning in self.warning_history:
                serializable_warning = warning.copy()
                # 处理日期时间格式
                if isinstance(serializable_warning.get("timestamp"), datetime.datetime):
                    serializable_warning["timestamp"] = serializable_warning["timestamp"].isoformat()
                serializable_history.append(serializable_warning)
            
            json.dump(serializable_history, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"预警历史记录已保存至: {filepath}")
    
    def load_warning_history(self, filepath: str) -> None:
        """
        从文件加载预警历史记录
        
        参数:
            filepath: 文件路径
        """
        if not os.path.exists(filepath):
            self.logger.warning(f"历史记录文件不存在: {filepath}")
            return
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                self.warning_history = json.load(f)
            
            self.logger.info(f"已加载 {len(self.warning_history)} 条预警历史记录")
        except Exception as e:
            self.logger.error(f"加载历史记录失败: {str(e)}")
    
    def create_default_email_handler(self, 
                                   smtp_server: str,
                                   smtp_port: int,
                                   sender_email: str,
                                   sender_password: str,
                                   recipient_emails: List[str]) -> Callable:
        """
        创建默认的邮件通知处理器
        
        参数:
            smtp_server: SMTP服务器地址
            smtp_port: SMTP服务器端口
            sender_email: 发件人邮箱
            sender_password: 发件人密码
            recipient_emails: 收件人邮箱列表
            
        返回:
            Callable: 邮件通知处理函数
        """
        def email_handler(warning_info: Dict[str, Any]) -> None:
            """
            发送邮件通知
            
            参数:
                warning_info: 预警信息字典
            """
            # 创建邮件主题
            level = warning_info["warning_level"]
            subject = f"[{level}] 风险预警通知: {warning_info['rule_name']}"
            
            # 创建邮件正文
            body = f"""
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; }}
                    .header {{ background-color: #f8f9fa; padding: 10px; }}
                    .content {{ padding: 15px; }}
                    .footer {{ font-size: 0.8em; color: #6c757d; padding: 10px; }}
                    .warning-high {{ color: #dc3545; }}
                    .warning-medium {{ color: #ffc107; }}
                    .warning-low {{ color: #28a745; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h2>风险预警通知</h2>
                    <p>时间: {warning_info["timestamp"]}</p>
                </div>
                <div class="content">
                    <h3 class="warning-{level.lower() if level != 'CRITICAL' else 'high'}">
                        预警级别: {level}
                    </h3>
                    <p><strong>规则名称:</strong> {warning_info["rule_name"]}</p>
                    <p><strong>监控指标:</strong> {warning_info["monitor_name"]} - {warning_info["metric_name"]}</p>
                    <p><strong>当前值:</strong> {warning_info["current_value"]:.4f}</p>
                    <p><strong>阈值:</strong> {warning_info["threshold"]:.4f}</p>
                    <p><strong>消息:</strong> {warning_info["message"]}</p>
                </div>
                <div class="footer">
                    <p>此邮件由风险预警系统自动发送，请勿直接回复。</p>
                </div>
            </body>
            </html>
            """
            
            # 创建邮件
            msg = MIMEMultipart()
            msg['Subject'] = subject
            msg['From'] = sender_email
            msg['To'] = ", ".join(recipient_emails)
            
            # 添加HTML内容
            msg.attach(MIMEText(body, 'html'))
            
            # 发送邮件
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls()
                server.login(sender_email, sender_password)
                server.send_message(msg)
        
        # 注册处理器
        self.register_notification_handler('email', email_handler)
        
        return email_handler
    
    def create_webhook_handler(self, webhook_url: str, custom_formatter: Optional[Callable] = None) -> Callable:
        """
        创建Webhook通知处理器
        
        参数:
            webhook_url: Webhook URL
            custom_formatter: 自定义格式化函数，用于转换预警信息为Webhook可接受的格式
            
        返回:
            Callable: Webhook通知处理函数
        """
        def default_formatter(warning_info: Dict[str, Any]) -> Dict[str, Any]:
            """默认格式化函数"""
            return {
                "type": "risk_warning",
                "level": warning_info["warning_level"],
                "title": f"风险预警: {warning_info['rule_name']}",
                "message": warning_info["message"],
                "details": warning_info,
                "timestamp": warning_info["timestamp"]
            }
        
        formatter = custom_formatter or default_formatter
        
        def webhook_handler(warning_info: Dict[str, Any]) -> None:
            """
            发送Webhook通知
            
            参数:
                warning_info: 预警信息字典
            """
            # 格式化数据
            payload = formatter(warning_info)
            
            # 发送请求
            response = requests.post(
                webhook_url,
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            # 检查响应
            response.raise_for_status()
        
        # 注册处理器
        self.register_notification_handler('webhook', webhook_handler)
        
        return webhook_handler
    
    def create_sms_handler(self, 
                          sms_service_provider: str,
                          api_key: str,
                          phone_numbers: List[str],
                          **service_params) -> Callable:
        """
        创建短信通知处理器
        
        参数:
            sms_service_provider: 短信服务提供商
            api_key: API密钥
            phone_numbers: 手机号码列表
            service_params: 服务特定参数
            
        返回:
            Callable: 短信通知处理函数
        """
        def sms_handler(warning_info: Dict[str, Any]) -> None:
            """
            发送短信通知
            
            参数:
                warning_info: 预警信息字典
            """
            # 根据级别决定是否发送短信（通常只有高级别预警才通过短信发送）
            level = RiskWarningLevel[warning_info["warning_level"]]
            if level.value < RiskWarningLevel.HIGH.value:
                return
            
            # 创建短信内容
            message = f"风险预警[{level.name}]: {warning_info['message']}"
            
            # 这里应该实现与特定短信服务提供商的集成
            # 由于短信服务提供商API各不相同，此处仅提供示例框架
            self.logger.info(f"短信通知处理器: 尚未实现与{sms_service_provider}的集成")
            self.logger.info(f"将向{phone_numbers}发送短信: {message}")
        
        # 注册处理器
        self.register_notification_handler('sms', sms_handler)
        
        return sms_handler 