"""
风险管理模块
- 提供风险计算和管理功能
- 支持风险监控、预警和报告
"""

# 从风险控制模块导入组件
from src.risk.controls import (
    RiskControlInterface,
    RiskControlException,
    StopLossControl,
    ExposureControl,
    RiskControlFactory
)

# 从风险监控模块导入组件
from src.risk.monitoring import (
    RiskMonitorInterface,
    RiskMonitorException,
    VaRMonitor,
    VolatilityMonitor,
    DrawdownMonitor
)

# 导入子模块
from src.risk import monitoring
from src.risk import controls
from src.risk import reporting

__all__ = [
    # 风险控制组件
    "RiskControlInterface",
    "RiskControlException",
    "StopLossControl",
    "ExposureControl",
    "RiskControlFactory",
    
    # 风险监控组件
    "RiskMonitorInterface",
    "RiskMonitorException",
    "VaRMonitor",
    "VolatilityMonitor",
    "DrawdownMonitor",

    # 导出模块名称
    'monitoring',
    'controls',
    'reporting'
] 