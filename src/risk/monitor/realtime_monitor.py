"""
实时风险监控系统

提供基于事件驱动的实时风险监控功能，可以订阅市场数据和账户更新事件，
实时评估风险状态并触发预警。
"""

import logging
import threading
import time
import queue
from typing import Dict, List, Any, Callable, Optional, Set, Union
import datetime as dt

from src.risk.monitor.risk_monitor import RiskMonitor, RiskAlert


class RealTimeRiskMonitor(RiskMonitor):
    """
    实时风险监控系统
    
    基于事件驱动的实时风险监控，订阅市场数据和账户更新事件，实时评估风险状态。
    """
    
    def __init__(self, name: str = "实时风险监控系统", 
                evaluation_interval: float = 1.0,
                max_queue_size: int = 1000):
        """
        初始化实时风险监控系统
        
        参数:
            name: 监控系统名称
            evaluation_interval: 风险评估间隔时间（秒）
            max_queue_size: 事件队列最大容量
        """
        super().__init__(name=name)
        self.evaluation_interval = evaluation_interval
        
        # 事件队列
        self.event_queue = queue.Queue(maxsize=max_queue_size)
        
        # 存储最新的数据
        self._latest_market_data: Dict[str, Any] = {}
        self._latest_positions: Dict[str, Any] = {}
        self._latest_account_data: Dict[str, Any] = {}
        
        # 运行状态
        self._running = False
        self._monitor_thread = None
        self._lock = threading.RLock()
        
        # 统计信息
        self._stats = {
            'processed_events': 0,
            'evaluations_count': 0,
            'last_evaluation_time': None,
            'start_time': None,
            'alerts_generated': 0
        }
        
        self.logger.info(
            f"初始化实时风险监控系统: {name}, "
            f"评估间隔: {evaluation_interval}秒, "
            f"队列容量: {max_queue_size}"
        )
    
    def start(self) -> None:
        """启动实时风险监控"""
        with self._lock:
            if self._running:
                self.logger.warning("实时风险监控系统已经在运行")
                return
            
            self._running = True
            self._stats['start_time'] = dt.datetime.now()
            self._monitor_thread = threading.Thread(
                target=self._monitor_loop,
                name=f"{self.name}-Thread",
                daemon=True
            )
            self._monitor_thread.start()
            self.logger.info(f"启动实时风险监控系统: {self.name}")
    
    def stop(self) -> None:
        """停止实时风险监控"""
        with self._lock:
            if not self._running:
                self.logger.warning("实时风险监控系统未运行")
                return
            
            self._running = False
            if self._monitor_thread:
                # 等待线程结束，但不超过5秒
                self._monitor_thread.join(timeout=5.0)
                self._monitor_thread = None
            
            self.logger.info(f"停止实时风险监控系统: {self.name}")
    
    def is_running(self) -> bool:
        """检查监控系统是否正在运行"""
        with self._lock:
            return self._running
    
    def _monitor_loop(self) -> None:
        """监控线程主循环"""
        self.logger.info(f"实时风险监控线程启动: {threading.current_thread().name}")
        last_evaluation_time = 0.0
        
        try:
            while self._running:
                current_time = time.time()
                
                # 处理队列中的事件
                self._process_events()
                
                # 检查是否需要执行风险评估
                if current_time - last_evaluation_time >= self.evaluation_interval:
                    self._evaluate_current_risk()
                    last_evaluation_time = current_time
                
                # 休眠一小段时间，避免CPU占用过高
                time.sleep(0.01)
        except Exception as e:
            self.logger.error(f"监控线程异常: {e}", exc_info=True)
            with self._lock:
                self._running = False
        finally:
            self.logger.info("监控线程结束")
    
    def _process_events(self) -> None:
        """处理事件队列中的事件"""
        # 处理所有可用的事件，但最多处理100个，避免阻塞太久
        processed = 0
        while not self.event_queue.empty() and processed < 100:
            try:
                event = self.event_queue.get_nowait()
                self._handle_event(event)
                self.event_queue.task_done()
                processed += 1
                self._stats['processed_events'] += 1
            except queue.Empty:
                break
            except Exception as e:
                self.logger.error(f"处理事件异常: {e}", exc_info=True)
    
    def _handle_event(self, event: Dict[str, Any]) -> None:
        """
        处理单个事件
        
        参数:
            event: 事件数据
        """
        event_type = event.get('type')
        
        if event_type == 'market_data':
            self._update_market_data(event.get('data', {}))
        elif event_type == 'position_update':
            self._update_positions(event.get('data', {}))
        elif event_type == 'account_update':
            self._update_account_data(event.get('data', {}))
        elif event_type == 'force_evaluation':
            # 强制评估事件
            self._evaluate_current_risk()
        else:
            self.logger.warning(f"未知事件类型: {event_type}")
    
    def _update_market_data(self, data: Dict[str, Any]) -> None:
        """更新市场数据"""
        if not data:
            return
            
        # 更新特定符号的数据
        if 'symbol' in data and 'data' in data:
            symbol = data['symbol']
            self._latest_market_data[symbol] = data['data']
        # 批量更新多个符号的数据
        elif all(isinstance(k, str) for k in data.keys()):
            self._latest_market_data.update(data)
    
    def _update_positions(self, data: Dict[str, Any]) -> None:
        """更新持仓数据"""
        if not data:
            return
            
        # 更新特定符号的持仓
        if 'symbol' in data and 'position' in data:
            symbol = data['symbol']
            self._latest_positions[symbol] = data['position']
        # 批量更新
        elif isinstance(data, dict):
            self._latest_positions.update(data)
    
    def _update_account_data(self, data: Dict[str, Any]) -> None:
        """更新账户数据"""
        if isinstance(data, dict):
            self._latest_account_data.update(data)
    
    def _evaluate_current_risk(self) -> Dict[str, Dict[str, Any]]:
        """
        评估当前风险状态
        
        返回:
            风险评估结果
        """
        with self._lock:
            # 如果没有足够的数据，跳过评估
            if not self._latest_market_data or not self._latest_positions:
                self.logger.debug("没有足够的数据进行风险评估")
                return {}
            
            results = self.evaluate_risk(
                self._latest_market_data,
                self._latest_positions,
                self._latest_account_data
            )
            
            # 更新统计信息
            self._stats['evaluations_count'] += 1
            self._stats['last_evaluation_time'] = dt.datetime.now()
            self._stats['alerts_generated'] = len(self._alert_history)
            
            return results
    
    def on_market_data(self, symbol: str, data: Dict[str, Any]) -> None:
        """
        处理市场数据更新
        
        参数:
            symbol: 资产代码
            data: 市场数据
        """
        try:
            event = {
                'type': 'market_data',
                'data': {
                    'symbol': symbol,
                    'data': data
                },
                'timestamp': dt.datetime.now()
            }
            self.event_queue.put(event, block=False)
        except queue.Full:
            self.logger.warning("事件队列已满，丢弃市场数据更新")
    
    def on_position_update(self, symbol: str, position: Dict[str, Any]) -> None:
        """
        处理持仓更新
        
        参数:
            symbol: 资产代码
            position: 持仓数据
        """
        try:
            event = {
                'type': 'position_update',
                'data': {
                    'symbol': symbol,
                    'position': position
                },
                'timestamp': dt.datetime.now()
            }
            self.event_queue.put(event, block=False)
        except queue.Full:
            self.logger.warning("事件队列已满，丢弃持仓更新")
    
    def on_account_update(self, account_data: Dict[str, Any]) -> None:
        """
        处理账户更新
        
        参数:
            account_data: 账户数据
        """
        try:
            event = {
                'type': 'account_update',
                'data': account_data,
                'timestamp': dt.datetime.now()
            }
            self.event_queue.put(event, block=False)
        except queue.Full:
            self.logger.warning("事件队列已满，丢弃账户更新")
    
    def force_evaluation(self) -> None:
        """强制执行一次风险评估"""
        try:
            event = {
                'type': 'force_evaluation',
                'timestamp': dt.datetime.now()
            }
            self.event_queue.put(event, block=False)
        except queue.Full:
            self.logger.warning("事件队列已满，丢弃强制评估请求")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取监控系统统计信息"""
        with self._lock:
            stats = self._stats.copy()
            
            # 添加运行时间
            if stats['start_time']:
                run_time = dt.datetime.now() - stats['start_time']
                stats['run_time_seconds'] = run_time.total_seconds()
            
            # 添加队列信息
            stats['queue_size'] = self.event_queue.qsize()
            stats['queue_full_percent'] = self.event_queue.qsize() / self.event_queue._maxsize if self.event_queue._maxsize > 0 else 0
            
            # 添加评估频率
            if stats.get('run_time_seconds', 0) > 0:
                stats['evaluations_per_second'] = stats['evaluations_count'] / stats['run_time_seconds']
                stats['events_per_second'] = stats['processed_events'] / stats['run_time_seconds']
            
            return stats
    
    def clear_data(self) -> None:
        """清除所有存储的数据"""
        with self._lock:
            self._latest_market_data.clear()
            self._latest_positions.clear()
            self._latest_account_data.clear()
            self.logger.info("清除所有存储的数据")
            
    def __str__(self) -> str:
        """字符串表示"""
        status = self.get_status_summary()
        stats = self.get_stats()
        run_status = "运行中" if self._running else "已停止"
        
        return (
            f"{self.name} [{run_status}] [控制器: {status['controller_count']}, "
            f"预警: {status['alert_controller_count']}/{stats.get('alerts_generated', 0)}, "
            f"评估次数: {stats.get('evaluations_count', 0)}, "
            f"处理事件: {stats.get('processed_events', 0)}]"
        ) 