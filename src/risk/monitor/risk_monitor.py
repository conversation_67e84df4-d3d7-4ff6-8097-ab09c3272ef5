"""
风险监控系统

集成多个风险控制器，对投资组合进行实时监控，产生风险预警事件。
"""

import logging
import time
import threading
from typing import Dict, List, Any, Callable, Optional, Set
import datetime as dt

from src.risk.control import RiskController


class RiskAlert:
    """风险预警类，表示一个风险预警事件"""
    
    def __init__(self, 
                controller_name: str, 
                risk_score: float, 
                message: str, 
                details: Dict[str, Any],
                timestamp: Optional[dt.datetime] = None):
        """
        初始化风险预警
        
        参数:
            controller_name: 产生预警的控制器名称
            risk_score: 风险得分
            message: 预警消息
            details: 详细信息
            timestamp: 预警时间戳，默认为当前时间
        """
        self.controller_name = controller_name
        self.risk_score = risk_score
        self.message = message
        self.details = details
        self.timestamp = timestamp or dt.datetime.now()
        self.alert_id = f"{controller_name}_{self.timestamp.strftime('%Y%m%d%H%M%S%f')}"
        
    def __str__(self) -> str:
        """字符串表示"""
        return f"[{self.timestamp.strftime('%Y-%m-%d %H:%M:%S')}] {self.controller_name}: {self.message} (风险分数: {self.risk_score:.4f})"


class RiskMonitor:
    """
    风险监控系统基类
    
    集成多个风险控制器，对投资组合进行监控，产生风险预警事件。
    """
    
    def __init__(self, name: str = "风险监控系统"):
        """
        初始化风险监控系统
        
        参数:
            name: 监控系统名称
        """
        self.name = name
        self.controllers: Dict[str, RiskController] = {}
        self.alert_handlers: List[Callable[[RiskAlert], None]] = []
        self.logger = logging.getLogger(f"risk.monitor.{name}")
        
        # 预警历史
        self._alert_history: List[RiskAlert] = []
        self._alert_ids: Set[str] = set()
        
        self.logger.info(f"初始化风险监控系统: {name}")
    
    def add_controller(self, controller: RiskController) -> None:
        """
        添加风险控制器
        
        参数:
            controller: 要添加的风险控制器
        """
        if controller.name in self.controllers:
            self.logger.warning(f"风险控制器 '{controller.name}' 已存在，将被替换")
        
        self.controllers[controller.name] = controller
        self.logger.info(f"添加风险控制器: {controller.get_description()}")
    
    def remove_controller(self, controller_name: str) -> bool:
        """
        移除风险控制器
        
        参数:
            controller_name: 要移除的控制器名称
            
        返回:
            是否成功移除
        """
        if controller_name in self.controllers:
            del self.controllers[controller_name]
            self.logger.info(f"移除风险控制器: {controller_name}")
            return True
        
        self.logger.warning(f"尝试移除不存在的风险控制器: {controller_name}")
        return False
    
    def add_alert_handler(self, handler: Callable[[RiskAlert], None]) -> None:
        """
        添加预警处理器
        
        参数:
            handler: 预警处理函数，接收一个RiskAlert参数
        """
        self.alert_handlers.append(handler)
        self.logger.info(f"添加预警处理器，当前处理器数量: {len(self.alert_handlers)}")
    
    def evaluate_risk(self, market_data: Dict[str, Any], positions: Dict[str, Any], 
                     account_data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """
        评估所有风险控制器
        
        参数:
            market_data: 市场数据
            positions: 持仓数据
            account_data: 账户数据
            
        返回:
            各控制器的评估结果
        """
        results = {}
        alerts = []
        
        for name, controller in self.controllers.items():
            # 执行风险评估
            result = controller.evaluate(market_data, positions, account_data)
            results[name] = result
            
            # 检查是否需要产生预警
            if controller.should_alert():
                alert = RiskAlert(
                    controller_name=name,
                    risk_score=controller.risk_score,
                    message=result.get('message', f"{name} 风险预警"),
                    details=result
                )
                alerts.append(alert)
                
                # 添加到历史记录
                if alert.alert_id not in self._alert_ids:
                    self._alert_history.append(alert)
                    self._alert_ids.add(alert.alert_id)
                    
                    # 保持历史记录不过长
                    if len(self._alert_history) > 1000:
                        removed_alert = self._alert_history.pop(0)
                        self._alert_ids.remove(removed_alert.alert_id)
        
        # 处理所有产生的预警
        for alert in alerts:
            self._handle_alert(alert)
        
        return results
    
    def _handle_alert(self, alert: RiskAlert) -> None:
        """
        处理风险预警
        
        参数:
            alert: 风险预警对象
        """
        self.logger.warning(f"风险预警: {alert}")
        
        # 调用所有注册的预警处理器
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                self.logger.error(f"预警处理器异常: {e}", exc_info=True)
    
    @property
    def alert_history(self) -> List[RiskAlert]:
        """获取预警历史"""
        return self._alert_history.copy()
    
    def get_latest_alerts(self, count: int = 10) -> List[RiskAlert]:
        """
        获取最新的预警
        
        参数:
            count: 要返回的预警数量
            
        返回:
            最新的预警列表
        """
        return self._alert_history[-count:] if self._alert_history else []
    
    def get_controller_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有控制器的状态
        
        返回:
            控制器状态字典
        """
        status = {}
        for name, controller in self.controllers.items():
            status[name] = {
                'risk_score': controller.risk_score,
                'alert_threshold': controller.alert_threshold,
                'should_alert': controller.should_alert(),
                'last_evaluation_time': controller.last_evaluation_time
            }
        return status
    
    def get_status_summary(self) -> Dict[str, Any]:
        """
        获取风险监控系统状态摘要
        
        返回:
            状态摘要字典
        """
        controller_status = self.get_controller_status()
        max_risk_score = 0.0
        max_risk_controller = ""
        alert_count = 0
        
        for name, status in controller_status.items():
            risk_score = status['risk_score']
            if risk_score > max_risk_score:
                max_risk_score = risk_score
                max_risk_controller = name
            
            if status['should_alert']:
                alert_count += 1
        
        return {
            'controller_count': len(self.controllers),
            'alert_controller_count': alert_count,
            'max_risk_score': max_risk_score,
            'max_risk_controller': max_risk_controller,
            'alert_count_24h': sum(1 for alert in self._alert_history 
                                   if (dt.datetime.now() - alert.timestamp).total_seconds() < 86400),
            'alert_handlers_count': len(self.alert_handlers)
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        status = self.get_status_summary()
        return (
            f"{self.name} [控制器: {status['controller_count']}, "
            f"预警控制器: {status['alert_controller_count']}, "
            f"最高风险分数: {status['max_risk_score']:.4f} ({status['max_risk_controller']}), "
            f"24小时预警数: {status['alert_count_24h']}]"
        ) 