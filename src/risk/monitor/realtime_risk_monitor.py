"""
实时风险监控模块

提供实时风险监控和预警功能，基于事件驱动架构实现对交易风险的实时监控和管理。
"""

import logging
import datetime as dt
from typing import Dict, List, Any, Optional, Set, Union, Callable
import threading
import time
import pandas as pd
import numpy as np
from enum import Enum, auto

from src.trading.core.event_bus import (
    Event, EventHandler, EventType, EventBus, OrderEvent, TradeEvent
)
from src.trading.data.realtime_processor import (
    DataEvent, DataEventType, DataConsumer
)
from src.risk.control.risk_controller import RiskController
from src.risk.monitor.risk_monitor import RiskMonitor

# 设置日志
logger = logging.getLogger(__name__)


class RiskAlertLevel(Enum):
    """风险预警级别"""
    INFO = auto()       # 信息
    WARNING = auto()    # 警告
    ALERT = auto()      # 警报
    CRITICAL = auto()   # 严重
    EMERGENCY = auto()  # 紧急


class RiskAlert:
    """风险预警信息"""
    
    def __init__(
        self,
        alert_id: str,
        level: RiskAlertLevel,
        message: str,
        source: str,
        timestamp: dt.datetime = None,
        data: Dict[str, Any] = None
    ):
        """
        初始化风险预警
        
        参数:
            alert_id: 预警ID
            level: 预警级别
            message: 预警消息
            source: 预警来源
            timestamp: 预警时间戳
            data: 附加数据
        """
        self.alert_id = alert_id
        self.level = level
        self.message = message
        self.source = source
        self.timestamp = timestamp or dt.datetime.now()
        self.data = data or {}
        self.acknowledged = False
        self.resolved = False
        self.resolution_time = None
        self.resolution_note = ""
    
    def acknowledge(self) -> None:
        """确认预警"""
        self.acknowledged = True
    
    def resolve(self, note: str = "") -> None:
        """解决预警"""
        self.resolved = True
        self.resolution_time = dt.datetime.now()
        self.resolution_note = note
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "alert_id": self.alert_id,
            "level": self.level.name,
            "message": self.message,
            "source": self.source,
            "timestamp": self.timestamp.isoformat(),
            "data": self.data,
            "acknowledged": self.acknowledged,
            "resolved": self.resolved,
            "resolution_time": self.resolution_time.isoformat() if self.resolution_time else None,
            "resolution_note": self.resolution_note
        }
    
    def __str__(self) -> str:
        status = "已解决" if self.resolved else ("已确认" if self.acknowledged else "未处理")
        return f"风险预警[{self.level.name}]: {self.message} ({status})"


class RiskAlertHandler(EventHandler):
    """风险预警处理器"""
    
    def __init__(
        self,
        name: str = "风险预警处理器",
        callback: Optional[Callable[[RiskAlert], None]] = None
    ):
        """
        初始化风险预警处理器
        
        参数:
            name: 处理器名称
            callback: 预警回调函数
        """
        super().__init__(name)
        self.callback = callback
        self.alerts: List[RiskAlert] = []
        self.max_alerts = 1000
        self.lock = threading.RLock()
    
    def handle_event(self, event: Event) -> None:
        """处理风险事件"""
        if event.event_type != EventType.RISK:
            return
            
        # 从事件数据创建风险预警
        try:
            alert_data = event.data
            alert = RiskAlert(
                alert_id=alert_data.get("alert_id", event.event_id),
                level=RiskAlertLevel[alert_data.get("level", "WARNING")],
                message=alert_data.get("message", "未知风险"),
                source=alert_data.get("source", event.sender),
                timestamp=event.timestamp,
                data=alert_data.get("data", {})
            )
            
            self._add_alert(alert)
            
            # 调用回调函数
            if self.callback:
                try:
                    self.callback(alert)
                except Exception as e:
                    logger.error(f"调用风险预警回调函数时出错: {e}", exc_info=True)
                    
        except Exception as e:
            logger.error(f"处理风险事件时出错: {e}", exc_info=True)
    
    def _add_alert(self, alert: RiskAlert) -> None:
        """添加预警到列表"""
        with self.lock:
            self.alerts.append(alert)
            
            # 限制预警列表大小
            if len(self.alerts) > self.max_alerts:
                self.alerts = self.alerts[-self.max_alerts:]
    
    def get_alerts(
        self, 
        level: Optional[RiskAlertLevel] = None,
        resolved: Optional[bool] = None,
        limit: int = 100
    ) -> List[RiskAlert]:
        """获取预警列表"""
        with self.lock:
            result = self.alerts.copy()
            
            if level is not None:
                result = [a for a in result if a.level == level]
                
            if resolved is not None:
                result = [a for a in result if a.resolved == resolved]
                
            return result[-limit:] if limit > 0 else result
    
    def clear_resolved_alerts(self) -> int:
        """清除已解决的预警"""
        with self.lock:
            before_count = len(self.alerts)
            self.alerts = [a for a in self.alerts if not a.resolved]
            return before_count - len(self.alerts)


class RealTimeRiskMonitor(RiskMonitor, DataConsumer, EventHandler):
    """实时风险监控器"""
    
    def __init__(
        self,
        name: str = "实时风险监控器",
        description: str = "基于事件总线的实时风险监控",
        risk_controllers: List[RiskController] = None,
        event_bus: Optional[EventBus] = None,
        check_interval: float = 1.0
    ):
        """
        初始化实时风险监控器
        
        参数:
            name: 监控器名称
            description: 描述
            risk_controllers: 风险控制器列表
            event_bus: 事件总线实例
            check_interval: 风险检查间隔(秒)
        """
        RiskMonitor.__init__(self, name, description, risk_controllers)
        DataConsumer.__init__(self, name)
        EventHandler.__init__(self, name)
        
        self.event_bus = event_bus
        self.check_interval = check_interval
        
        # 市场数据缓存
        self.market_data: Dict[str, Dict[str, Any]] = {}
        
        # 最新持仓和账户信息
        self.positions: Dict[str, Dict[str, Any]] = {}
        self.account_info: Dict[str, Any] = {}
        
        # 风险计算结果缓存
        self.risk_metrics: Dict[str, Any] = {}
        self.last_check_time = None
        
        # 监控状态
        self.running = False
        self.monitor_thread = None
        self.lock = threading.RLock()
        
        # 订阅事件类型
        self.subscribe(EventType.ORDER)
        self.subscribe(EventType.TRADE)
        self.subscribe(EventType.POSITION)
        self.subscribe(EventType.ACCOUNT)
    
    def start_monitoring(self) -> bool:
        """启动风险监控"""
        with self.lock:
            if self.running:
                logger.warning("风险监控器已经在运行")
                return False
                
            self.running = True
            
            # 启动监控线程
            self.monitor_thread = threading.Thread(
                target=self._monitoring_loop,
                name=f"{self.name}_monitor",
                daemon=True
            )
            self.monitor_thread.start()
            
            logger.info(f"启动实时风险监控器: {self.name}")
            return True
    
    def stop_monitoring(self) -> bool:
        """停止风险监控"""
        with self.lock:
            if not self.running:
                logger.warning("风险监控器未运行")
                return False
                
            self.running = False
            
            # 等待监控线程结束
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5.0)
                
            logger.info(f"停止实时风险监控器: {self.name}")
            return True
    
    def on_data(self, event: DataEvent) -> None:
        """处理实时市场数据"""
        symbol = event.symbol
        
        # 更新市场数据缓存
        if symbol not in self.market_data:
            self.market_data[symbol] = {}
            
        # 根据事件类型更新不同的数据
        if event.event_type == DataEventType.TICK:
            self.market_data[symbol]['tick'] = event.data
            self.market_data[symbol]['last_price'] = event.data.get('price')
            self.market_data[symbol]['last_update'] = event.timestamp
            
        elif event.event_type == DataEventType.QUOTE:
            self.market_data[symbol]['quote'] = event.data
            # 使用中间价作为最新价格
            bid = event.data.get('bid', 0)
            ask = event.data.get('ask', 0)
            if bid > 0 and ask > 0:
                self.market_data[symbol]['last_price'] = (bid + ask) / 2
            self.market_data[symbol]['last_update'] = event.timestamp
            
        elif event.event_type == DataEventType.BAR:
            self.market_data[symbol]['bar'] = event.data
            self.market_data[symbol]['last_price'] = event.data.get('close')
            self.market_data[symbol]['last_update'] = event.timestamp
            
        # 其他事件类型...
    
    def handle_event(self, event: Event) -> None:
        """处理交易相关事件"""
        
        if event.event_type == EventType.POSITION:
            # 更新持仓信息
            position_data = event.data
            symbol = position_data.get('symbol')
            if symbol:
                self.positions[symbol] = position_data
                
        elif event.event_type == EventType.ACCOUNT:
            # 更新账户信息
            self.account_info.update(event.data)
            
        elif event.event_type == EventType.ORDER:
            # 处理订单事件，可能需要更新内部状态
            order_data = event.data
            # 特定处理逻辑...
            
        elif event.event_type == EventType.TRADE:
            # 处理成交事件，可能需要更新内部状态
            trade_data = event.data
            # 特定处理逻辑...
    
    def get_market_data(self, symbol: str) -> Dict[str, Any]:
        """获取特定证券的市场数据"""
        with self.lock:
            return self.market_data.get(symbol, {})
    
    def get_market_price(self, symbol: str) -> Optional[float]:
        """获取特定证券的最新价格"""
        with self.lock:
            market_data = self.market_data.get(symbol, {})
            return market_data.get('last_price')
    
    def get_position(self, symbol: str) -> Dict[str, Any]:
        """获取特定证券的持仓信息"""
        with self.lock:
            return self.positions.get(symbol, {})
    
    def get_account_value(self) -> float:
        """获取账户总价值"""
        with self.lock:
            return self.account_info.get('total_value', 0.0)
    
    def check_risk(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """
        执行风险检查
        
        参数:
            symbol: 特定证券代码，如果为None则检查所有持仓
            
        返回:
            风险检查结果
        """
        with self.lock:
            # 记录检查时间
            check_time = dt.datetime.now()
            
            # 准备市场数据和持仓数据
            market_data = {}
            position_data = {}
            
            if symbol:
                # 检查特定证券
                market_data[symbol] = self.get_market_data(symbol)
                position = self.get_position(symbol)
                if position:
                    position_data[symbol] = position
            else:
                # 检查所有持仓
                for sym in self.positions:
                    market_data[sym] = self.get_market_data(sym)
                    position_data[sym] = self.positions[sym]
            
            # 创建市场数据和持仓数据的DataFrame
            market_df = pd.DataFrame([
                {
                    'symbol': sym,
                    'price': data.get('last_price', 0),
                    'timestamp': data.get('last_update', check_time)
                }
                for sym, data in market_data.items()
                if data.get('last_price') is not None
            ])
            
            position_df = pd.DataFrame([
                {
                    'symbol': sym,
                    'quantity': data.get('quantity', 0),
                    'cost_basis': data.get('cost_basis', 0),
                    'market_value': data.get('market_value', 0)
                }
                for sym, data in position_data.items()
            ])
            
            # 准备账户数据
            account_data = {
                'total_value': self.get_account_value(),
                'cash': self.account_info.get('cash', 0.0),
                'margin': self.account_info.get('margin', 0.0)
            }
            
            # 执行风险检查
            results = {}
            
            for controller in self.risk_controllers:
                if not controller.enabled:
                    continue
                    
                try:
                    # 检查风险
                    check_result = controller.check_risk(
                        market_data=market_df,
                        position_data=position_df,
                        account_data=account_data
                    )
                    
                    results[controller.name] = check_result
                    
                    # 如果风险超过阈值，生成风险预警
                    if check_result.get('risk_level', 0) > controller.alert_threshold:
                        self._generate_risk_alert(controller, check_result)
                        
                except Exception as e:
                    logger.error(f"风险控制器 {controller.name} 检查时出错: {e}", exc_info=True)
            
            # 更新风险指标和最后检查时间
            self.risk_metrics = self._aggregate_risk_metrics(results)
            self.last_check_time = check_time
            
            return self.risk_metrics
    
    def _generate_risk_alert(self, controller: RiskController, check_result: Dict[str, Any]) -> None:
        """生成风险预警并发布到事件总线"""
        if not self.event_bus:
            return
            
        risk_level = check_result.get('risk_level', 0)
        
        # 根据风险等级确定预警级别
        if risk_level > 0.8:
            alert_level = RiskAlertLevel.EMERGENCY
        elif risk_level > 0.6:
            alert_level = RiskAlertLevel.CRITICAL
        elif risk_level > 0.4:
            alert_level = RiskAlertLevel.ALERT
        elif risk_level > 0.2:
            alert_level = RiskAlertLevel.WARNING
        else:
            alert_level = RiskAlertLevel.INFO
        
        # 创建预警消息
        message = check_result.get('message', f"风险控制器 {controller.name} 检测到风险")
        details = check_result.get('details', {})
        
        # 创建风险事件数据
        event_data = {
            "alert_id": f"risk_{dt.datetime.now().strftime('%Y%m%d%H%M%S')}_{id(controller)}",
            "level": alert_level.name,
            "message": message,
            "source": controller.name,
            "data": {
                "risk_level": risk_level,
                "details": details,
                "controller_type": controller.__class__.__name__
            }
        }
        
        # 创建并发布风险事件
        try:
            risk_event = Event(
                event_type=EventType.RISK,
                data=event_data,
                sender=self.name,
                timestamp=dt.datetime.now()
            )
            self.event_bus.publish(risk_event)
            logger.info(f"发布风险预警: {message} (级别: {alert_level.name})")
        except Exception as e:
            logger.error(f"发布风险预警事件时出错: {e}", exc_info=True)
    
    def _monitoring_loop(self) -> None:
        """风险监控循环"""
        logger.info(f"启动风险监控循环: {self.name}")
        
        while self.running:
            try:
                # 执行风险检查
                self.check_risk()
                
                # 等待下一个检查间隔
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"风险监控循环出错: {e}", exc_info=True)
                time.sleep(1.0)  # 出错后短暂休息
                
        logger.info(f"停止风险监控循环: {self.name}")
    
    def _aggregate_risk_metrics(self, results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """聚合各个风险控制器的结果"""
        if not results:
            return {}
            
        # 提取所有风险等级
        risk_levels = [
            result.get('risk_level', 0)
            for result in results.values()
        ]
        
        # 计算整体风险等级
        overall_risk = max(risk_levels) if risk_levels else 0
        
        # 聚合详细信息
        aggregated = {
            'overall_risk_level': overall_risk,
            'max_controller': "",
            'controller_results': results,
            'timestamp': dt.datetime.now().isoformat()
        }
        
        # 找出风险等级最高的控制器
        if risk_levels:
            max_risk = max(risk_levels)
            for controller_name, result in results.items():
                if result.get('risk_level', 0) == max_risk:
                    aggregated['max_controller'] = controller_name
                    break
        
        return aggregated 