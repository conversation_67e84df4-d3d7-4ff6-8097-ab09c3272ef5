"""
风险情景分析模块

本模块提供风险情景分析工具，用于评估投资组合在各种市场情景下的表现，包括：

1. 情景分析：
   - 历史情景模拟 - 基于过去市场事件重现情景
   - 假设情景分析 - 基于用户定义的市场变动创建情景
   - 蒙特卡洛模拟 - 通过随机模拟生成大量可能的市场情景

2. 压力测试：
   - 极端事件压力测试 - 模拟极端市场事件对投资组合的影响
   - 因子压力测试 - 分析特定风险因子变动对投资组合的影响
   - 相关性压力测试 - 评估资产相关性结构变化的影响
   - 自定义压力测试 - 支持用户自定义的压力测试场景

3. 结果分析：
   - 情景比较 - 对比不同情景下的投资组合表现
   - 风险指标计算 - 计算各情景下的VaR、CVaR、最大回撤等风险指标
   - 敏感性分析 - 识别投资组合对特定风险因素的敏感度
"""

from src.risk.scenarios.interfaces import (
    ScenarioAnalysisException,
    ScenarioType,
    ScenarioSeverity,
    DistributionType,
    ScenarioResult,
    ScenarioAnalyzerInterface,
    StressTestGeneratorInterface
)

from src.risk.scenarios.scenario_analyzer import ScenarioAnalyzer
from src.risk.scenarios.stress_test_generator import StressTestGenerator

__all__ = [
    # 异常类
    'ScenarioAnalysisException',
    
    # 枚举类型
    'ScenarioType',
    'ScenarioSeverity',
    'DistributionType',
    
    # 结果类
    'ScenarioResult',
    
    # 接口类
    'ScenarioAnalyzerInterface',
    'StressTestGeneratorInterface',
    
    # 实现类
    'ScenarioAnalyzer',
    'StressTestGenerator'
] 