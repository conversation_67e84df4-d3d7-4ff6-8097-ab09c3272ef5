"""
压力测试生成器实现模块

提供压力测试场景生成的具体实现，包括：
- 极端事件压力测试
- 因子压力测试
- 相关性压力测试
- 自定义压力测试
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple, Callable
import datetime as dt

from src.risk.scenarios.interfaces import (
    StressTestGeneratorInterface,
    ScenarioType,
    ScenarioSeverity,
    ScenarioAnalysisException
)


class StressTestGenerator(StressTestGeneratorInterface):
    """
    压力测试生成器类
    
    实现了压力测试生成器接口的基础功能，包括极端事件压力测试、因子压力测试和相关性压力测试。
    """
    
    # 预定义的历史极端事件，键为事件名称，值为(开始日期, 结束日期, 描述)
    HISTORICAL_EVENTS = {
        "2008金融危机": ("2008-09-15", "2009-03-09", "2008年金融危机，从雷曼兄弟破产到市场触底"),
        "2020新冠疫情": ("2020-02-20", "2020-03-23", "2020年新冠疫情初期市场急剧下跌"),
        "2015中国股灾": ("2015-06-15", "2015-08-26", "2015年中国股市崩盘"),
        "2000科技泡沫破灭": ("2000-03-10", "2002-10-09", "2000年科技泡沫破灭"),
        "1997亚洲金融危机": ("1997-07-02", "1998-01-12", "1997年亚洲金融危机"),
        "2011欧债危机": ("2011-07-22", "2011-09-22", "2011年欧洲主权债务危机恶化"),
        "2018年底美股下跌": ("2018-10-03", "2018-12-24", "2018年底美联储加息和贸易冲突导致的市场下跌"),
        "2013年缩减恐慌": ("2013-05-22", "2013-06-24", "2013年美联储暗示缩减QE引发的市场恐慌"),
        "2010闪崩": ("2010-05-06", "2010-05-06", "2010年5月6日美股市场闪崩"),
        "2016英国脱欧": ("2016-06-23", "2016-06-27", "2016年英国脱欧公投后的市场动荡")
    }
    
    # 预定义的压力测试情景，键为情景名称，值为资产价格变动的字典
    PREDEFINED_SCENARIOS = {
        "股市崩盘": {
            "股票": -0.30,  # 股票下跌30%
            "债券": 0.05,   # 债券上涨5%
            "黄金": 0.10,   # 黄金上涨10%
            "现金": 0.0     # 现金不变
        },
        "利率急升": {
            "股票": -0.15,  # 股票下跌15%
            "债券": -0.10,  # 债券下跌10%
            "黄金": -0.05,  # 黄金下跌5%
            "现金": 0.01    # 现金略有增长
        },
        "通胀冲击": {
            "股票": -0.10,  # 股票下跌10%
            "债券": -0.15,  # 债券下跌15%
            "黄金": 0.20,   # 黄金上涨20%
            "现金": -0.05   # 现金实际购买力下降
        },
        "经济衰退": {
            "股票": -0.25,  # 股票下跌25%
            "债券": 0.05,   # 债券上涨5%
            "黄金": 0.05,   # 黄金上涨5%
            "现金": 0.01    # 现金略有增长
        },
        "地缘政治危机": {
            "股票": -0.15,  # 股票下跌15%
            "债券": 0.03,   # 债券上涨3%
            "黄金": 0.15,   # 黄金上涨15%
            "现金": 0.0     # 现金不变
        }
    }
    
    # 预定义的相关性变化情景，键为情景名称，值为相关性结构的变化
    CORRELATION_SCENARIOS = {
        "危机相关性上升": {
            "描述": "市场危机期间，资产间相关性趋于1",
            "方法": "shift_towards_one",
            "参数": {
                "intensity": 0.5  # 相关性向1移动的强度(0-1)
            }
        },
        "相关性结构解体": {
            "描述": "市场混乱期间，历史相关性不再适用",
            "方法": "random_correlations",
            "参数": {
                "min_correlation": -0.3,  # 最小相关性
                "max_correlation": 0.7    # 最大相关性
            }
        },
        "分化加强": {
            "描述": "不同类型资产之间的相关性减弱",
            "方法": "amplify_differences",
            "参数": {
                "amplification_factor": 1.5  # 差异放大因子
            }
        }
    }
    
    def __init__(
        self,
        name: str = "压力测试生成器",
        description: str = "提供极端事件、因子和相关性压力测试",
        historical_data: Optional[pd.DataFrame] = None,
        **kwargs
    ):
        """
        初始化压力测试生成器
        
        参数:
            name: 生成器名称
            description: 生成器描述
            historical_data: 历史数据，用于创建历史事件压力测试
            **kwargs: 其他参数
        """
        super().__init__(name=name, description=description)
        self._stress_tests = {}
        self._historical_data = historical_data
        self._custom_events = {}  # 自定义极端事件字典
    
    def create_extreme_event_stress_test(
        self,
        stress_test_name: str,
        event_name: str,
        severity: ScenarioSeverity = ScenarioSeverity.HIGH,
        historical_data: Optional[pd.DataFrame] = None,
        use_predefined_scenario: bool = False,
        apply_severity_multiplier: bool = True,
        description: str = "",
        **kwargs
    ) -> pd.DataFrame:
        """
        创建极端事件压力测试
        
        参数:
            stress_test_name: 压力测试名称
            event_name: 事件名称
            severity: 严重程度
            historical_data: 历史数据，如果为None则使用初始化时提供的数据
            use_predefined_scenario: 是否使用预定义情景
            apply_severity_multiplier: 是否应用严重程度乘数
            description: 压力测试描述
            **kwargs: 其他参数
            
        返回:
            生成的压力测试收益率
        """
        if stress_test_name in self._stress_tests:
            raise ScenarioAnalysisException(f"压力测试 '{stress_test_name}' 已存在")
            
        # 确定要使用的历史数据
        data = historical_data if historical_data is not None else self._historical_data
        
        if use_predefined_scenario:
            # 使用预定义的情景
            if event_name not in self.PREDEFINED_SCENARIOS:
                raise ValueError(f"预定义情景 '{event_name}' 不存在")
                
            # 获取预定义情景
            asset_moves = self.PREDEFINED_SCENARIOS[event_name]
            
            # 如果需要，根据严重程度调整
            if apply_severity_multiplier:
                severity_multiplier = self._get_severity_multiplier(severity)
                asset_moves = {k: v * severity_multiplier for k, v in asset_moves.items()}
                
            # 创建单一时间点的压力测试数据
            date_index = pd.date_range(start=dt.datetime.now(), periods=1, freq='D')
            stress_data = pd.DataFrame([asset_moves], index=date_index)
            
            # 默认描述
            if not description:
                description = f"基于预定义情景 '{event_name}' 的压力测试, 严重程度: {severity.name}"
        else:
            # 使用历史数据重现极端事件
            if data is None:
                raise ValueError("创建历史事件压力测试需要提供历史数据")
                
            # 获取事件日期
            if event_name in self.HISTORICAL_EVENTS:
                start_date, end_date, event_desc = self.HISTORICAL_EVENTS[event_name]
            elif event_name in self._custom_events:
                start_date, end_date, event_desc = self._custom_events[event_name]
            else:
                raise ValueError(f"事件 '{event_name}' 不存在于预定义事件或自定义事件中")
                
            # 确保数据索引是日期
            if not isinstance(data.index, pd.DatetimeIndex):
                try:
                    data.index = pd.to_datetime(data.index)
                except Exception:
                    raise ValueError("历史数据的索引必须是日期格式或可转换为日期的格式")
                    
            # 过滤事件期间的数据
            event_data = data.loc[start_date:end_date]
            if event_data.empty:
                raise ScenarioAnalysisException(f"事件 '{event_name}' 期间 ({start_date} 到 {end_date}) 没有数据")
                
            # 计算收益率
            returns = (event_data / event_data.shift(1) - 1).dropna()
            
            # 如果需要，根据严重程度调整
            if apply_severity_multiplier:
                severity_multiplier = self._get_severity_multiplier(severity)
                returns = returns * severity_multiplier
                
            stress_data = returns
            
            # 默认描述
            if not description:
                description = f"基于历史事件 '{event_name}' ({start_date} 到 {end_date}) 的压力测试, 严重程度: {severity.name}"
                
        # 保存压力测试
        self._stress_tests[stress_test_name] = {
            "data": stress_data,
            "type": ScenarioType.STRESS_TEST,
            "description": description
        }
        
        return stress_data
        
    def create_factor_stress_test(
        self,
        stress_test_name: str,
        factor_shocks: Dict[str, float],
        assets_factor_exposure: pd.DataFrame,
        time_periods: int = 1,
        description: str = "",
        **kwargs
    ) -> pd.DataFrame:
        """
        创建因子压力测试
        
        参数:
            stress_test_name: 压力测试名称
            factor_shocks: 因子冲击字典，键为因子名称，值为冲击幅度
            assets_factor_exposure: 资产对因子的暴露度，索引为资产，列为因子
            time_periods: 时间周期数
            description: 压力测试描述
            **kwargs: 其他参数
            
        返回:
            生成的压力测试收益率
        """
        if stress_test_name in self._stress_tests:
            raise ScenarioAnalysisException(f"压力测试 '{stress_test_name}' 已存在")
            
        # 确保assets_factor_exposure是DataFrame
        if not isinstance(assets_factor_exposure, pd.DataFrame):
            raise ValueError("assets_factor_exposure必须是DataFrame")
            
        # 提取因子列表
        factors = list(factor_shocks.keys())
        
        # 确保所有的因子都在暴露度矩阵中
        missing_factors = set(factors) - set(assets_factor_exposure.columns)
        if missing_factors:
            raise ValueError(f"以下因子在暴露度矩阵中不存在: {missing_factors}")
            
        # 提取资产列表
        assets = assets_factor_exposure.index.tolist()
        
        # 创建因子冲击向量
        shock_vector = pd.Series(factor_shocks)
        
        # 计算资产收益率
        asset_returns = assets_factor_exposure[factors].dot(shock_vector)
        
        # 创建时间序列数据
        date_index = pd.date_range(start=dt.datetime.now(), periods=time_periods, freq='D')
        
        # 创建收益率DataFrame
        if time_periods > 1:
            # 假设多个周期内冲击平均分布
            per_period_returns = asset_returns / time_periods
            returns_list = [per_period_returns] * time_periods
            returns_df = pd.DataFrame(returns_list, index=date_index, columns=assets)
        else:
            # 单周期情况
            returns_df = pd.DataFrame([asset_returns], index=date_index, columns=assets)
            
        # 保存压力测试
        self._stress_tests[stress_test_name] = {
            "data": returns_df,
            "type": ScenarioType.STRESS_TEST,
            "description": description if description else f"因子压力测试 - 因子冲击: {dict(shock_vector)}"
        }
        
        return returns_df
    
    def create_correlation_stress_test(
        self,
        stress_test_name: str,
        base_correlation_matrix: pd.DataFrame,
        correlation_changes: Dict[Tuple[str, str], float] = None,
        correlation_scenario: str = None,
        expected_returns: pd.Series = None,
        volatilities: pd.Series = None,
        time_periods: int = 1,
        description: str = "",
        **kwargs
    ) -> pd.DataFrame:
        """
        创建相关性压力测试
        
        参数:
            stress_test_name: 压力测试名称
            base_correlation_matrix: 基础相关性矩阵
            correlation_changes: 相关性变化字典，键为资产对，值为相关性变化
            correlation_scenario: 预定义相关性情景
            expected_returns: 期望收益率
            volatilities: 波动率
            time_periods: 时间周期数
            description: 压力测试描述
            **kwargs: 其他参数
            
        返回:
            生成的压力测试收益率
        """
        if stress_test_name in self._stress_tests:
            raise ScenarioAnalysisException(f"压力测试 '{stress_test_name}' 已存在")
            
        # 确保基础相关性矩阵是DataFrame
        if not isinstance(base_correlation_matrix, pd.DataFrame):
            raise ValueError("base_correlation_matrix必须是DataFrame")
            
        # 确保相关性矩阵是方形的
        if base_correlation_matrix.shape[0] != base_correlation_matrix.shape[1]:
            raise ValueError("相关性矩阵必须是方形的")
            
        # 提取资产列表
        assets = base_correlation_matrix.index.tolist()
        
        # 复制基础相关性矩阵
        stressed_corr_matrix = base_correlation_matrix.copy()
        
        # 应用相关性变化
        if correlation_changes:
            for (asset1, asset2), change in correlation_changes.items():
                if asset1 in assets and asset2 in assets:
                    # 计算新的相关性
                    new_corr = base_correlation_matrix.loc[asset1, asset2] + change
                    # 限制在-1到1之间
                    new_corr = max(-1.0, min(1.0, new_corr))
                    # 更新相关性矩阵
                    stressed_corr_matrix.loc[asset1, asset2] = new_corr
                    stressed_corr_matrix.loc[asset2, asset1] = new_corr
                    
        # 或者应用预定义的相关性情景
        elif correlation_scenario and correlation_scenario in self.CORRELATION_SCENARIOS:
            scenario = self.CORRELATION_SCENARIOS[correlation_scenario]
            method = scenario["方法"]
            params = scenario["参数"]
            
            if method == "shift_towards_one":
                # 相关性向1移动
                intensity = params["intensity"]
                for i, asset1 in enumerate(assets):
                    for j, asset2 in enumerate(assets):
                        if i != j:  # 不修改对角线
                            current_corr = stressed_corr_matrix.loc[asset1, asset2]
                            stressed_corr_matrix.loc[asset1, asset2] = current_corr + (1 - current_corr) * intensity
                            stressed_corr_matrix.loc[asset2, asset1] = stressed_corr_matrix.loc[asset1, asset2]
                            
            elif method == "random_correlations":
                # 随机相关性
                min_corr = params["min_correlation"]
                max_corr = params["max_correlation"]
                for i, asset1 in enumerate(assets):
                    for j, asset2 in enumerate(assets):
                        if i < j:  # 只处理上三角
                            stressed_corr_matrix.loc[asset1, asset2] = np.random.uniform(min_corr, max_corr)
                            stressed_corr_matrix.loc[asset2, asset1] = stressed_corr_matrix.loc[asset1, asset2]
                            
            elif method == "amplify_differences":
                # 放大差异
                factor = params["amplification_factor"]
                for i, asset1 in enumerate(assets):
                    for j, asset2 in enumerate(assets):
                        if i < j:  # 只处理上三角
                            current_corr = stressed_corr_matrix.loc[asset1, asset2]
                            # 向0方向放大
                            if current_corr > 0:
                                stressed_corr_matrix.loc[asset1, asset2] = min(1.0, current_corr / factor)
                            else:
                                stressed_corr_matrix.loc[asset1, asset2] = max(-1.0, current_corr * factor)
                            stressed_corr_matrix.loc[asset2, asset1] = stressed_corr_matrix.loc[asset1, asset2]
        
        # 确保相关性矩阵是正定的
        try:
            np.linalg.cholesky(stressed_corr_matrix)
        except np.linalg.LinAlgError:
            # 如果不是正定的，进行修正
            stressed_corr_matrix = self._fix_correlation_matrix(stressed_corr_matrix)
            
        # 如果提供了期望收益率和波动率，生成随机收益率
        if expected_returns is not None and volatilities is not None:
            # 创建协方差矩阵
            cov_matrix = stressed_corr_matrix.copy()
            for i, asset1 in enumerate(assets):
                for j, asset2 in enumerate(assets):
                    cov_matrix.loc[asset1, asset2] = (
                        stressed_corr_matrix.loc[asset1, asset2] * 
                        volatilities[asset1] * 
                        volatilities[asset2]
                    )
                    
            # 生成随机收益率
            random_returns = np.random.multivariate_normal(
                mean=expected_returns.values,
                cov=cov_matrix.values,
                size=time_periods
            )
            
            # 创建时间序列数据
            date_index = pd.date_range(start=dt.datetime.now(), periods=time_periods, freq='D')
            returns_df = pd.DataFrame(random_returns, index=date_index, columns=assets)
        else:
            # 如果没有提供期望收益率和波动率，只返回相关性矩阵
            returns_df = None
            
        # 保存压力测试
        self._stress_tests[stress_test_name] = {
            "data": returns_df if returns_df is not None else stressed_corr_matrix,
            "correlation_matrix": stressed_corr_matrix,
            "type": ScenarioType.STRESS_TEST,
            "description": description if description else f"相关性压力测试 - {'应用相关性变化' if correlation_changes else f'情景: {correlation_scenario}'}"
        }
        
        return returns_df if returns_df is not None else stressed_corr_matrix
    
    def create_custom_stress_test(
        self,
        stress_test_name: str,
        scenario_generator: Callable,
        assets: List[str] = None,
        time_periods: int = 1,
        description: str = "",
        **kwargs
    ) -> pd.DataFrame:
        """
        创建自定义压力测试
        
        参数:
            stress_test_name: 压力测试名称
            scenario_generator: 情景生成函数，应返回一个资产收益率的DataFrame
            assets: 资产列表
            time_periods: 时间周期数
            description: 压力测试描述
            **kwargs: 其他参数
            
        返回:
            生成的压力测试收益率
        """
        if stress_test_name in self._stress_tests:
            raise ScenarioAnalysisException(f"压力测试 '{stress_test_name}' 已存在")
            
        # 调用用户提供的情景生成函数
        try:
            stress_data = scenario_generator(
                assets=assets,
                time_periods=time_periods,
                **kwargs
            )
        except Exception as e:
            raise ScenarioAnalysisException(f"运行场景生成函数时出错: {str(e)}")
            
        # 确保返回值是DataFrame
        if not isinstance(stress_data, pd.DataFrame):
            raise ValueError("情景生成函数必须返回一个DataFrame")
            
        # 如果数据没有日期索引，创建一个
        if not isinstance(stress_data.index, pd.DatetimeIndex):
            stress_data.index = pd.date_range(start=dt.datetime.now(), periods=len(stress_data), freq='D')
            
        # 保存压力测试
        self._stress_tests[stress_test_name] = {
            "data": stress_data,
            "type": ScenarioType.STRESS_TEST,
            "description": description if description else "自定义压力测试"
        }
        
        return stress_data
        
    def register_custom_event(
        self,
        event_name: str,
        start_date: str,
        end_date: str,
        description: str
    ) -> None:
        """
        注册自定义极端事件
        
        参数:
            event_name: 事件名称
            start_date: 开始日期
            end_date: 结束日期
            description: 事件描述
        """
        if event_name in self.HISTORICAL_EVENTS:
            raise ValueError(f"事件名称 '{event_name}' 已存在于预定义事件中")
            
        if event_name in self._custom_events:
            raise ValueError(f"事件名称 '{event_name}' 已存在于自定义事件中")
            
        # 注册事件
        self._custom_events[event_name] = (start_date, end_date, description)
        
    def get_stress_test(self, stress_test_name: str) -> pd.DataFrame:
        """
        获取压力测试数据
        
        参数:
            stress_test_name: 压力测试名称
            
        返回:
            压力测试数据
        """
        if stress_test_name not in self._stress_tests:
            raise ScenarioAnalysisException(f"压力测试 '{stress_test_name}' 不存在")
            
        return self._stress_tests[stress_test_name]["data"]
        
    def get_stress_test_description(self, stress_test_name: str) -> str:
        """
        获取压力测试描述
        
        参数:
            stress_test_name: 压力测试名称
            
        返回:
            压力测试描述
        """
        if stress_test_name not in self._stress_tests:
            raise ScenarioAnalysisException(f"压力测试 '{stress_test_name}' 不存在")
            
        return self._stress_tests[stress_test_name]["description"]
        
    def list_historical_events(self) -> Dict[str, Tuple[str, str, str]]:
        """
        列出所有历史极端事件
        
        返回:
            历史事件字典
        """
        # 合并预定义事件和自定义事件
        all_events = {}
        all_events.update(self.HISTORICAL_EVENTS)
        all_events.update(self._custom_events)
        return all_events
        
    def list_predefined_scenarios(self) -> Dict[str, Dict[str, float]]:
        """
        列出所有预定义情景
        
        返回:
            预定义情景字典
        """
        return self.PREDEFINED_SCENARIOS
        
    def list_correlation_scenarios(self) -> Dict[str, Dict[str, Any]]:
        """
        列出所有相关性情景
        
        返回:
            相关性情景字典
        """
        return self.CORRELATION_SCENARIOS

    def _get_severity_multiplier(self, severity: ScenarioSeverity) -> float:
        """
        获取严重程度乘数
        
        参数:
            severity: 严重程度
            
        返回:
            乘数
        """
        if severity == ScenarioSeverity.LOW:
            return 0.5
        elif severity == ScenarioSeverity.MODERATE:
            return 1.0
        elif severity == ScenarioSeverity.HIGH:
            return 1.5
        elif severity == ScenarioSeverity.EXTREME:
            return 2.0
        else:
            return 1.0
            
    def _fix_correlation_matrix(self, matrix: pd.DataFrame) -> pd.DataFrame:
        """
        修复非正定相关性矩阵
        
        参数:
            matrix: 相关性矩阵
            
        返回:
            修复后的正定相关性矩阵
        """
        # 计算特征值和特征向量
        eigenvalues, eigenvectors = np.linalg.eigh(matrix)
        
        # 将负特征值设为小的正数
        eigenvalues[eigenvalues < 1e-8] = 1e-8
        
        # 重建矩阵
        fixed_matrix = eigenvectors @ np.diag(eigenvalues) @ eigenvectors.T
        
        # 确保对角线为1，且相关性在[-1,1]范围内
        for i in range(len(matrix)):
            fixed_matrix[i, i] = 1.0
            for j in range(len(matrix)):
                if i != j:
                    if fixed_matrix[i, j] < -1:
                        fixed_matrix[i, j] = -1.0
                    elif fixed_matrix[i, j] > 1:
                        fixed_matrix[i, j] = 1.0
                        
        # 转换回DataFrame
        fixed_df = pd.DataFrame(fixed_matrix, index=matrix.index, columns=matrix.columns)
        
        return fixed_df 