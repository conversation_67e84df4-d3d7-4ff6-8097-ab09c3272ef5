"""
风险情景分析器实现模块

提供风险情景分析的具体实现，包括：
- 情景分析器
- 历史情景生成
- 假设情景生成
- 蒙特卡洛模拟
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
from scipy import stats
import datetime as dt

from src.risk.scenarios.interfaces import (
    ScenarioAnalyzerInterface,
    ScenarioType,
    ScenarioResult,
    DistributionType,
    ScenarioAnalysisException
)


class ScenarioAnalyzer(ScenarioAnalyzerInterface):
    """
    情景分析器类
    
    实现了情景分析器接口的基础功能，包括历史情景、假设情景和蒙特卡洛模拟。
    """
    
    def __init__(
        self,
        name: str = "情景分析器",
        description: str = "提供历史、假设和蒙特卡洛情景分析",
        **kwargs
    ):
        """
        初始化情景分析器
        
        参数:
            name: 分析器名称
            description: 分析器描述
            **kwargs: 其他参数
        """
        super().__init__(name=name, description=description)
        self._scenarios = {}
        self._scenario_types = {}
        self._scenario_descriptions = {}
        
    def create_historical_scenario(
        self,
        scenario_name: str,
        historical_period: Tuple[str, str],
        asset_data: pd.DataFrame,
        use_log_returns: bool = False,
        description: str = "",
        **kwargs
    ) -> pd.DataFrame:
        """
        创建历史情景
        
        参数:
            scenario_name: 情景名称
            historical_period: 历史时期(开始日期, 结束日期)
            asset_data: 资产数据，索引为日期，列为资产
            use_log_returns: 是否使用对数收益率
            description: 情景描述
            **kwargs: 其他参数
            
        返回:
            生成的情景收益率
        """
        if scenario_name in self._scenarios:
            raise ScenarioAnalysisException(f"情景 '{scenario_name}' 已存在")
            
        # 确保asset_data是DataFrame
        if not isinstance(asset_data, pd.DataFrame):
            raise ValueError("asset_data必须是DataFrame")
            
        # 确保索引是日期类型
        if not isinstance(asset_data.index, pd.DatetimeIndex):
            try:
                asset_data.index = pd.to_datetime(asset_data.index)
            except Exception:
                raise ValueError("asset_data的索引必须是日期格式或可转换为日期的格式")
                
        # 解析历史时期
        start_date, end_date = pd.to_datetime(historical_period[0]), pd.to_datetime(historical_period[1])
        
        # 过滤数据
        filtered_data = asset_data.loc[start_date:end_date]
        
        if filtered_data.empty:
            raise ScenarioAnalysisException(f"历史时期 {start_date} 到 {end_date} 内没有数据")
            
        # 计算资产收益率
        if use_log_returns:
            returns = np.log(filtered_data / filtered_data.shift(1)).dropna()
        else:
            returns = (filtered_data / filtered_data.shift(1) - 1).dropna()
            
        # 保存情景
        self._scenarios[scenario_name] = returns
        self._scenario_types[scenario_name] = ScenarioType.HISTORICAL
        
        # 如果没有提供描述，则生成默认描述
        if not description:
            description = f"历史情景 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}"
        self._scenario_descriptions[scenario_name] = description
        
        return returns
    
    def create_hypothetical_scenario(
        self,
        scenario_name: str,
        asset_moves: Dict[str, float],
        correlation_matrix: pd.DataFrame = None,
        time_periods: int = 1,
        time_steps_per_period: int = 1,
        distribution: DistributionType = DistributionType.NORMAL,
        description: str = "",
        **kwargs
    ) -> pd.DataFrame:
        """
        创建假设情景
        
        参数:
            scenario_name: 情景名称
            asset_moves: 资产涨跌幅字典，键为资产名，值为预期变动率
            correlation_matrix: 相关性矩阵，如果为None则假设资产之间不相关
            time_periods: 时间周期数
            time_steps_per_period: 每个周期的时间步数
            distribution: 收益率分布类型
            description: 情景描述
            **kwargs: 其他参数
            
        返回:
            生成的情景收益率
        """
        if scenario_name in self._scenarios:
            raise ScenarioAnalysisException(f"情景 '{scenario_name}' 已存在")
            
        # 提取资产列表
        assets = list(asset_moves.keys())
        n_assets = len(assets)
        
        # 创建目标收益率向量
        target_returns = pd.Series(asset_moves)
        
        # 如果未提供相关性矩阵，则假设不相关
        if correlation_matrix is None:
            correlation_matrix = pd.DataFrame(np.identity(n_assets), index=assets, columns=assets)
        else:
            # 确保相关性矩阵包含所有资产
            missing_assets = set(assets) - set(correlation_matrix.index)
            if missing_assets:
                raise ValueError(f"相关性矩阵缺少以下资产: {missing_assets}")
            
            # 确保相关性矩阵是方形的
            if correlation_matrix.shape[0] != correlation_matrix.shape[1]:
                raise ValueError("相关性矩阵必须是方形的")
                
            # 确保相关性矩阵是正定的
            try:
                np.linalg.cholesky(correlation_matrix)
            except np.linalg.LinAlgError:
                # 如果不是正定的，进行修正
                correlation_matrix = self._fix_correlation_matrix(correlation_matrix)
        
        # 计算每期的收益率
        period_returns = target_returns / time_periods
        
        # 计算每步的标准差
        std_devs = kwargs.get('volatilities')
        if std_devs is None:
            # 如果未提供波动率，使用预期收益的一半作为估计
            std_devs = period_returns.abs() * 0.5 / np.sqrt(time_steps_per_period)
        else:
            # 转换为每步波动率
            std_devs = std_devs / np.sqrt(time_steps_per_period)
        
        # 创建协方差矩阵
        cov_matrix = correlation_matrix.copy()
        for i, asset1 in enumerate(assets):
            for j, asset2 in enumerate(assets):
                cov_matrix.loc[asset1, asset2] = (
                    correlation_matrix.loc[asset1, asset2] * std_devs[asset1] * std_devs[asset2]
                )
        
        # 生成时间序列数据
        total_steps = time_periods * time_steps_per_period
        date_index = pd.date_range(start=dt.datetime.now(), periods=total_steps, freq='D')
        
        # 生成随机收益率
        returns = self._generate_returns(
            mean_returns=period_returns,
            cov_matrix=cov_matrix,
            time_steps=total_steps,
            distribution=distribution,
            **kwargs
        )
        
        # 设置索引和列名
        returns.index = date_index
        
        # 保存情景
        self._scenarios[scenario_name] = returns
        self._scenario_types[scenario_name] = ScenarioType.HYPOTHETICAL
        
        # 如果没有提供描述，则生成默认描述
        if not description:
            moves_str = ", ".join([f"{a}: {m:.1%}" for a, m in asset_moves.items()])
            description = f"假设情景 - 资产变动: {moves_str}, 周期: {time_periods}"
        self._scenario_descriptions[scenario_name] = description
        
        return returns
        
    def create_monte_carlo_scenario(
        self,
        scenario_name: str,
        expected_returns: pd.Series,
        covariance_matrix: pd.DataFrame,
        time_periods: int = 252,
        num_simulations: int = 1000,
        distribution: DistributionType = DistributionType.NORMAL,
        seed: int = None,
        description: str = "",
        **kwargs
    ) -> Dict[int, pd.DataFrame]:
        """
        创建蒙特卡洛情景
        
        参数:
            scenario_name: 情景名称
            expected_returns: 期望收益率
            covariance_matrix: 协方差矩阵
            time_periods: 时间周期数
            num_simulations: 模拟次数
            distribution: 分布类型
            seed: 随机种子
            description: 情景描述
            **kwargs: 其他参数
            
        返回:
            生成的情景收益率字典，键为模拟ID，值为收益率DataFrame
        """
        if scenario_name in self._scenarios:
            raise ScenarioAnalysisException(f"情景 '{scenario_name}' 已存在")
            
        # 设置随机种子
        if seed is not None:
            np.random.seed(seed)
            
        # 提取资产列表
        assets = expected_returns.index.tolist()
        
        # 确保协方差矩阵是正定的
        try:
            np.linalg.cholesky(covariance_matrix)
        except np.linalg.LinAlgError:
            # 如果不是正定的，进行修正
            covariance_matrix = self._fix_correlation_matrix(covariance_matrix)
            
        # 创建存储所有模拟的字典
        simulations = {}
        
        for sim_id in range(num_simulations):
            # 生成随机收益率
            returns = self._generate_returns(
                mean_returns=expected_returns,
                cov_matrix=covariance_matrix,
                time_steps=time_periods,
                distribution=distribution,
                **kwargs
            )
            
            # 设置索引
            date_index = pd.date_range(start=dt.datetime.now(), periods=time_periods, freq='D')
            returns.index = date_index
            
            # 保存这次模拟
            simulations[sim_id] = returns
            
        # 保存情景
        self._scenarios[scenario_name] = simulations
        self._scenario_types[scenario_name] = ScenarioType.MONTE_CARLO
        
        # 如果没有提供描述，则生成默认描述
        if not description:
            description = f"蒙特卡洛情景 - {num_simulations}次模拟, {time_periods}个周期"
        self._scenario_descriptions[scenario_name] = description
        
        return simulations
    
    def run_scenario_analysis(
        self,
        scenario_name: str,
        weights: pd.Series,
        simulation_id: int = None,
        **kwargs
    ) -> ScenarioResult:
        """
        运行情景分析
        
        参数:
            scenario_name: 情景名称
            weights: 资产权重
            simulation_id: 模拟ID，仅用于蒙特卡洛情景
            **kwargs: 其他参数
            
        返回:
            情景分析结果
        """
        if scenario_name not in self._scenarios:
            raise ScenarioAnalysisException(f"情景 '{scenario_name}' 不存在")
            
        scenario_type = self._scenario_types[scenario_name]
        scenario_data = self._scenarios[scenario_name]
        description = self._scenario_descriptions.get(scenario_name, "")
        
        # 针对不同类型的情景进行处理
        if scenario_type == ScenarioType.MONTE_CARLO:
            if simulation_id is None:
                raise ValueError("对于蒙特卡洛情景，必须提供simulation_id")
                
            if simulation_id not in scenario_data:
                raise ValueError(f"模拟ID {simulation_id} 不存在")
                
            asset_returns = scenario_data[simulation_id]
            result_name = f"{scenario_name} (Sim {simulation_id})"
        else:
            asset_returns = scenario_data
            result_name = scenario_name
            
        # 计算投资组合收益率
        # 确保资产名称匹配
        common_assets = [asset for asset in weights.index if asset in asset_returns.columns]
        if not common_assets:
            raise ValueError("权重和资产收益率没有共同的资产")
            
        portfolio_returns = (weights[common_assets] * asset_returns[common_assets]).sum(axis=1)
        
        # 创建结果对象
        result = ScenarioResult(
            scenario_name=result_name,
            scenario_type=scenario_type,
            portfolio_returns=portfolio_returns,
            asset_returns=asset_returns,
            description=description
        )
        
        # 计算指标
        result.calculate_metrics()
        
        return result
    
    def compare_scenarios(
        self,
        scenario_names: List[str],
        weights: pd.Series,
        simulation_ids: Dict[str, int] = None,
        **kwargs
    ) -> Dict[str, ScenarioResult]:
        """
        比较多个情景
        
        参数:
            scenario_names: 情景名称列表
            weights: 资产权重
            simulation_ids: 模拟ID字典，键为情景名称，值为模拟ID
            **kwargs: 其他参数
            
        返回:
            情景分析结果字典
        """
        if simulation_ids is None:
            simulation_ids = {}
            
        results = {}
        
        for scenario_name in scenario_names:
            if scenario_name not in self._scenarios:
                raise ScenarioAnalysisException(f"情景 '{scenario_name}' 不存在")
                
            # 获取情景类型
            scenario_type = self._scenario_types[scenario_name]
            
            # 对于蒙特卡洛情景，需要模拟ID
            sim_id = None
            if scenario_type == ScenarioType.MONTE_CARLO:
                sim_id = simulation_ids.get(scenario_name)
                if sim_id is None:
                    # 默认使用第一个模拟
                    sim_id = 0
                    
            # 运行情景分析
            result = self.run_scenario_analysis(
                scenario_name=scenario_name,
                weights=weights,
                simulation_id=sim_id,
                **kwargs
            )
            
            results[scenario_name] = result
            
        return results
    
    def _generate_returns(
        self,
        mean_returns: pd.Series,
        cov_matrix: pd.DataFrame,
        time_steps: int,
        distribution: DistributionType = DistributionType.NORMAL,
        df: int = 5,  # T分布自由度
        skewness: float = 0,  # 偏斜度
        kurtosis: float = 3,  # 峰度
        **kwargs
    ) -> pd.DataFrame:
        """
        生成随机收益率
        
        参数:
            mean_returns: 期望收益率
            cov_matrix: 协方差矩阵
            time_steps: 时间步数
            distribution: 分布类型
            df: T分布自由度
            skewness: 偏斜度
            kurtosis: 峰度
            **kwargs: 其他参数
            
        返回:
            生成的随机收益率
        """
        assets = mean_returns.index.tolist()
        n_assets = len(assets)
        
        # 根据分布类型生成随机收益率
        if distribution == DistributionType.NORMAL:
            # 正态分布
            random_returns = np.random.multivariate_normal(
                mean=mean_returns.values,
                cov=cov_matrix.values,
                size=time_steps
            )
        elif distribution == DistributionType.STUDENT_T:
            # T分布
            random_standard = np.random.standard_t(df, size=(time_steps, n_assets))
            # 转换为多元T分布
            chol = np.linalg.cholesky(cov_matrix.values)
            random_returns = mean_returns.values + np.dot(random_standard, chol.T)
        elif distribution == DistributionType.SKEWED_T:
            # 偏T分布 - 使用乘以偏斜因子的方法简化
            random_standard = np.random.standard_t(df, size=(time_steps, n_assets))
            # 添加偏斜
            skew_factor = np.random.random(size=(time_steps, n_assets))
            skew_factor = np.where(skew_factor > 0.5, 1 + skewness, 1 - skewness)
            random_standard *= skew_factor
            # 转换为多元偏T分布
            chol = np.linalg.cholesky(cov_matrix.values)
            random_returns = mean_returns.values + np.dot(random_standard, chol.T)
        elif distribution == DistributionType.HISTORICAL:
            # 历史经验分布 - 需要提供历史收益率
            historical_returns = kwargs.get('historical_returns')
            if historical_returns is None:
                raise ValueError("对于历史经验分布，必须提供historical_returns参数")
                
            # 重采样历史收益率
            indices = np.random.choice(len(historical_returns), size=time_steps, replace=True)
            random_returns = historical_returns.iloc[indices].values
        else:
            raise ValueError(f"不支持的分布类型: {distribution}")
            
        # 转换为DataFrame
        returns_df = pd.DataFrame(random_returns, columns=assets)
        
        return returns_df
    
    def _fix_correlation_matrix(self, matrix: pd.DataFrame) -> pd.DataFrame:
        """
        修复非正定相关性矩阵
        
        参数:
            matrix: 相关性矩阵
            
        返回:
            修复后的正定相关性矩阵
        """
        # 计算特征值和特征向量
        eigenvalues, eigenvectors = np.linalg.eigh(matrix)
        
        # 将负特征值设为小的正数
        eigenvalues[eigenvalues < 1e-8] = 1e-8
        
        # 重建矩阵
        fixed_matrix = eigenvectors @ np.diag(eigenvalues) @ eigenvectors.T
        
        # 确保对角线为1
        for i in range(len(matrix)):
            fixed_matrix[i, i] = 1.0
            
        # 转换回DataFrame
        fixed_df = pd.DataFrame(fixed_matrix, index=matrix.index, columns=matrix.columns)
        
        return fixed_df 