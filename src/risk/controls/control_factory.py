"""
风险控制工厂模块
- 提供创建风险控制器的工厂方法
- 管理已注册的风险控制器类型
- 支持风险控制流水线创建
"""

import importlib
import logging
from typing import Dict, Any, Type, Optional, List, Union

from src.risk.controls.control_interface import RiskControlInterface, RiskControlException
from src.risk.controls.stop_loss import StopLossControl
from src.risk.controls.exposure_limit import ExposureControl
from src.utils.logging.logger_factory import get_logger

logger = get_logger(__name__)

class RiskControlFactory:
    """
    增强的风险控制工厂
    用于创建和管理不同类型的风险控制器，支持：
    1. 类对象注册
    2. 字符串路径注册（动态导入）
    3. 配置文件批量注册
    4. 风险控制流水线创建
    """

    # 存储已注册的风险控制器类型：{name: (type, reference)}
    _control_types: Dict[str, Union[Type[RiskControlInterface], str]] = {}
    _control_metadata: Dict[str, Dict[str, Any]] = {}

    @classmethod
    def register(cls, name: str, control_class: Type[RiskControlInterface],
                 metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        注册风险控制器类型

        参数：
            name: 控制器类型名称
            control_class: 控制器类
            metadata: 控制器元数据
        """
        # {{ AURA-X: Modify - 增强注册功能，支持元数据和覆盖警告. Approval: 寸止(ID:架构合规性修正). }}
        if name in cls._control_types:
            logger.warning(f"风险控制器类型 '{name}' 已存在，将被覆盖")

        cls._control_types[name] = control_class
        cls._control_metadata[name] = metadata or {}
        logger.info(f"注册风险控制器: {name}")

    @classmethod
    def register_from_path(cls, name: str, class_path: str,
                          metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        从类路径注册风险控制器

        参数：
            name: 控制器类型名称
            class_path: 类路径，格式为'module.Class'
            metadata: 控制器元数据
        """
        if name in cls._control_types:
            logger.warning(f"风险控制器类型 '{name}' 已存在，将被覆盖")

        cls._control_types[name] = class_path
        cls._control_metadata[name] = metadata or {}
        logger.info(f"注册风险控制器路径: {name} -> {class_path}")
    
    @classmethod
    def register_from_config(cls, config_path: str) -> None:
        """
        从配置文件批量注册风险控制器

        参数：
            config_path: 配置文件路径
        """
        # {{ AURA-X: Add - 添加配置驱动的批量注册功能. Approval: 寸止(ID:架构合规性修正). }}
        try:
            from src.config.config_factory import ConfigFactory
            config = ConfigFactory.load_config(config_path)

            controls_config = config.get('risk_controls', {})
            for control_name, control_config in controls_config.items():
                if 'class_path' in control_config:
                    cls.register_from_path(
                        control_name,
                        control_config['class_path'],
                        control_config.get('metadata', {})
                    )
                elif 'module' in control_config and 'class' in control_config:
                    class_path = f"{control_config['module']}.{control_config['class']}"
                    cls.register_from_path(
                        control_name,
                        class_path,
                        control_config.get('metadata', {})
                    )

            logger.info(f"从配置文件 {config_path} 注册了 {len(controls_config)} 个风险控制器")

        except Exception as e:
            logger.error(f"从配置文件注册风险控制器失败: {e}")
            raise

    @classmethod
    def _import_control_class(cls, class_path: str) -> Type[RiskControlInterface]:
        """
        动态导入风险控制器类

        参数：
            class_path: 类路径

        返回：
            风险控制器类
        """
        try:
            module_name, class_name = class_path.rsplit('.', 1)
            module = importlib.import_module(module_name)
            control_class = getattr(module, class_name)

            # 验证是否实现了正确的接口
            if not issubclass(control_class, RiskControlInterface):
                raise ValueError(f"控制器类 {class_name} 必须继承 RiskControlInterface")

            return control_class

        except (ImportError, AttributeError) as e:
            raise ImportError(f"无法导入风险控制器 {class_path}: {e}")
        except ValueError as e:
            raise

    @classmethod
    def get_control_types(cls) -> Dict[str, str]:
        """
        获取所有已注册的风险控制器类型

        返回：
            Dict[str, str]: 控制器类型到描述的映射
        """
        result = {}
        for name, control_ref in cls._control_types.items():
            if isinstance(control_ref, str):
                description = f"模块路径: {control_ref}"
            else:
                description = f"类对象: {control_ref.__name__}"
            result[name] = description

        return result
    
    @classmethod
    def create(cls, control_type: str, **kwargs) -> RiskControlInterface:
        """
        创建风险控制器实例

        参数：
            control_type: 控制器类型名称
            **kwargs: 控制器初始化参数

        返回：
            RiskControlInterface: 风险控制器实例

        异常：
            RiskControlException: 如果控制器类型不存在
        """
        # {{ AURA-X: Modify - 增强创建方法，支持动态导入. Approval: 寸止(ID:架构合规性修正). }}
        if control_type not in cls._control_types:
            available = ', '.join(cls._control_types.keys())
            raise RiskControlException(f"未知的风险控制器类型: '{control_type}'。可用类型: {available}")

        control_ref = cls._control_types[control_type]

        # 获取控制器类
        if isinstance(control_ref, str):
            control_class = cls._import_control_class(control_ref)
        else:
            control_class = control_ref

        try:
            instance = control_class(**kwargs)
            logger.debug(f"创建风险控制器: {control_type}")
            return instance
        except Exception as e:
            logger.error(f"创建风险控制器 '{control_type}' 失败: {e}")
            raise RiskControlException(f"创建风险控制器 '{control_type}' 失败: {str(e)}")

    @classmethod
    def get_available_controls(cls) -> Dict[str, str]:
        """
        获取所有可用的风险控制器

        返回：
            Dict[str, str]: 控制器名称到描述的映射
        """
        available = {}
        for name, control_ref in cls._control_types.items():
            try:
                if isinstance(control_ref, str):
                    # 尝试导入以验证可用性
                    cls._import_control_class(control_ref)
                    available[name] = f"模块路径: {control_ref}"
                else:
                    available[name] = f"类对象: {control_ref.__name__}"
            except Exception:
                # 跳过不可用的控制器
                continue

        return available
    
    @classmethod
    def create_composite(cls, control_configs: List[Dict[str, Any]]) -> List[RiskControlInterface]:
        """
        创建多个风险控制器

        参数：
            control_configs: 控制器配置列表

        返回：
            List[RiskControlInterface]: 风险控制器列表
        """
        controllers = []

        for config in control_configs:
            config_copy = config.copy()
            control_type = config_copy.pop("type", None)
            if not control_type:
                raise RiskControlException("控制器配置中缺少 'type' 字段")

            controller = cls.create(control_type, **config_copy)
            controllers.append(controller)

        return controllers

    @classmethod
    def create_risk_pipeline(cls, pipeline_config: List[Dict[str, Any]]) -> 'RiskPipeline':
        """
        创建风险控制流水线

        参数：
            pipeline_config: 流水线配置列表

        返回：
            RiskPipeline: 风险控制流水线
        """
        # {{ AURA-X: Add - 添加风险控制流水线创建功能. Approval: 寸止(ID:架构合规性修正). }}
        controls = []
        for config in pipeline_config:
            config_copy = config.copy()
            control_type = config_copy.pop('type', None)
            if not control_type:
                raise RiskControlException("流水线配置中缺少 'type' 字段")

            # 支持控制器优先级和条件
            priority = config_copy.pop('priority', 0)
            condition = config_copy.pop('condition', None)

            control = cls.create(control_type, **config_copy)
            controls.append({
                'control': control,
                'priority': priority,
                'condition': condition
            })

        return RiskPipeline(controls)


class RiskPipeline:
    """
    风险控制流水线

    按优先级顺序执行多个风险控制器，支持条件执行
    """

    def __init__(self, controls: List[Dict[str, Any]]):
        """
        初始化风险控制流水线

        参数：
            controls: 控制器配置列表，每个包含control、priority、condition
        """
        # 按优先级排序
        self.controls = sorted(controls, key=lambda x: x.get('priority', 0), reverse=True)
        self.execution_stats = {
            'total_checks': 0,
            'passed_checks': 0,
            'failed_checks': 0,
            'control_stats': {}
        }

    def check_signal(self, signal: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        通过流水线检查信号

        参数：
            signal: 交易信号
            context: 上下文信息（如当前持仓、市场状态等）

        返回：
            检查结果字典
        """
        self.execution_stats['total_checks'] += 1

        result = {
            'approved': True,
            'adjusted_signal': signal,
            'reasons': [],
            'control_results': []
        }

        for control_config in self.controls:
            control = control_config['control']
            condition = control_config.get('condition')

            # 检查执行条件
            if condition and not self._evaluate_condition(condition, context):
                continue

            # 执行风险检查
            try:
                control_result = control.check_risk(signal, context)
                result['control_results'].append({
                    'control': control.__class__.__name__,
                    'result': control_result
                })

                # 更新统计
                control_name = control.__class__.__name__
                if control_name not in self.execution_stats['control_stats']:
                    self.execution_stats['control_stats'][control_name] = {
                        'total': 0, 'passed': 0, 'failed': 0
                    }

                self.execution_stats['control_stats'][control_name]['total'] += 1

                if not control_result.get('approved', True):
                    result['approved'] = False
                    result['reasons'].append(control_result.get('reason', '风险检查失败'))
                    self.execution_stats['control_stats'][control_name]['failed'] += 1

                    # 如果是严重风险，立即停止检查
                    if control_result.get('severity', 'medium') == 'critical':
                        break
                else:
                    self.execution_stats['control_stats'][control_name]['passed'] += 1

                    # 应用信号调整
                    if 'adjusted_signal' in control_result:
                        result['adjusted_signal'] = control_result['adjusted_signal']

            except Exception as e:
                logger.error(f"风险控制器 {control.__class__.__name__} 执行失败: {e}")
                result['approved'] = False
                result['reasons'].append(f"风险控制器执行错误: {str(e)}")

        # 更新总体统计
        if result['approved']:
            self.execution_stats['passed_checks'] += 1
        else:
            self.execution_stats['failed_checks'] += 1

        return result

    def _evaluate_condition(self, condition: str, context: Dict[str, Any]) -> bool:
        """
        评估执行条件

        参数：
            condition: 条件表达式
            context: 上下文

        返回：
            是否满足条件
        """
        try:
            # 简单的条件评估（可以扩展为更复杂的表达式解析）
            return eval(condition, {"__builtins__": {}}, context)
        except Exception as e:
            logger.warning(f"条件评估失败: {condition}, 错误: {e}")
            return True  # 默认执行

    def get_stats(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        return self.execution_stats.copy()

    def reset_stats(self) -> None:
        """重置统计信息"""
        self.execution_stats = {
            'total_checks': 0,
            'passed_checks': 0,
            'failed_checks': 0,
            'control_stats': {}
        }

# {{ AURA-X: Modify - 增强内置控制器注册，添加元数据. Approval: 寸止(ID:架构合规性修正). }}
# 注册内置风险控制器
RiskControlFactory.register("stop_loss", StopLossControl, {
    'description': '止损控制器',
    'category': 'position_risk',
    'severity': 'high'
})

RiskControlFactory.register("exposure_limit", ExposureControl, {
    'description': '仓位暴露限制控制器',
    'category': 'portfolio_risk',
    'severity': 'medium'
})

# 便捷函数
def create_risk_control(control_type: str, **kwargs) -> RiskControlInterface:
    """创建风险控制器的便捷函数"""
    return RiskControlFactory.create(control_type, **kwargs)

def create_risk_pipeline(config: List[Dict[str, Any]]) -> RiskPipeline:
    """创建风险控制流水线的便捷函数"""
    return RiskControlFactory.create_risk_pipeline(config)

