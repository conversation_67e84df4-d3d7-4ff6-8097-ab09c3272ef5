"""
敞口限制控制模块
- 实现各种敞口限制策略
- 包括单一持仓限额、行业敞口限制、市值敞口限制等
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union

from src.risk.controls.control_interface import RiskControlInterface, RiskControlException

class ExposureControl(RiskControlInterface):
    """
    敞口限制控制器
    
    控制投资组合的风险敞口，防止过度集中
    
    支持的敞口限制类型：
    - 单一持仓限额：限制单一股票的最大持仓比例
    - 行业敞口限制：限制单一行业的最大持仓比例
    - 市值敞口限制：限制不同市值区间的最大持仓比例
    
    参数：
    - limit_type: 限制类型，支持 "single"（单一持仓）, "industry"（行业）, "market_cap"（市值）
    - threshold: 阈值，表示最大允许的持仓比例
    - exposure_target: 敞口目标，指定限制的具体目标，如特定行业或市值区间
    """
    
    # 支持的限制类型
    VALID_LIMIT_TYPES = {"single", "industry", "market_cap", "overall"}
    
    def __init__(self, 
                 limit_type: str = "single",
                 threshold: float = 0.1,
                 exposure_target: Any = None,
                 name: str = None,
                 description: str = ""):
        """
        初始化敞口限制控制器
        
        参数：
            limit_type: 限制类型，支持 "single", "industry", "market_cap", "overall"
            threshold: 阈值，表示最大允许的持仓比例
            exposure_target: 敞口目标，指定限制的具体目标，如特定行业或市值区间
            name: 控制器名称
            description: 控制器描述
        """
        if limit_type not in self.VALID_LIMIT_TYPES:
            raise RiskControlException(f"不支持的敞口限制类型: {limit_type}，有效类型为: {self.VALID_LIMIT_TYPES}")
        
        if not (0 < threshold <= 1):
            raise RiskControlException(f"敞口限制阈值必须在0和1之间，当前值: {threshold}")
        
        self.limit_type = limit_type
        self.threshold = threshold
        self.exposure_target = exposure_target
        self.name = name or f"敞口限制({limit_type})"
        self.description = description or f"限制{limit_type}类型的最大敞口为{threshold*100:.1f}%"
        
        # 状态跟踪
        self.violations = {}  # 记录违反敞口限制的持仓
        self.current_exposures = {}  # 记录当前各敞口
        self.is_active = True  # 控制器是否激活
    
    def check_risk(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        检查是否存在敞口超限
        
        参数:
            context: 上下文信息，包括当前持仓、市场数据等
            
        返回:
            Dict[str, Any]: 风险检查结果，包括超限持仓等
        """
        if not self.is_active:
            return {"violated": False, "violations": {}}
        
        # 提取持仓信息
        positions = context.get("positions", {})
        portfolio_value = context.get("portfolio_value", 0)
        
        if not positions or portfolio_value <= 0:
            return {"violated": False, "violations": {}}
        
        # 获取行业和市值信息
        stock_info = context.get("stock_info", {})
        industry_map = {s: info.get("industry", "unknown") for s, info in stock_info.items()}
        market_cap_map = {s: info.get("market_cap", 0) for s, info in stock_info.items()}
        
        # 计算敞口
        violations = {}
        
        if self.limit_type == "single":
            # 检查单一持仓限制
            for symbol, position in positions.items():
                position_value = position.get("value", 0)
                exposure = position_value / portfolio_value
                
                # 记录当前敞口
                self.current_exposures[symbol] = exposure
                
                # 检查是否超过阈值
                if exposure > self.threshold:
                    violations[symbol] = {
                        "exposure": exposure,
                        "threshold": self.threshold,
                        "overage": exposure - self.threshold,
                        "position_value": position_value
                    }
        
        elif self.limit_type == "industry":
            # 计算行业敞口
            industry_exposures = {}
            
            for symbol, position in positions.items():
                position_value = position.get("value", 0)
                industry = industry_map.get(symbol, "unknown")
                
                industry_exposures[industry] = industry_exposures.get(industry, 0) + position_value
            
            # 计算行业敞口比例
            for industry, value in industry_exposures.items():
                exposure = value / portfolio_value
                
                # 记录当前敞口
                self.current_exposures[industry] = exposure
                
                # 检查特定行业或所有行业
                if self.exposure_target is None or industry == self.exposure_target:
                    if exposure > self.threshold:
                        violations[industry] = {
                            "exposure": exposure,
                            "threshold": self.threshold,
                            "overage": exposure - self.threshold,
                            "industry_value": value
                        }
        
        elif self.limit_type == "market_cap":
            # 定义市值区间
            market_cap_ranges = {
                "large": (10e9, float('inf')),
                "mid": (2e9, 10e9),
                "small": (300e6, 2e9),
                "micro": (0, 300e6)
            }
            
            # 计算市值区间敞口
            market_cap_exposures = {"large": 0, "mid": 0, "small": 0, "micro": 0}
            
            for symbol, position in positions.items():
                position_value = position.get("value", 0)
                market_cap = market_cap_map.get(symbol, 0)
                
                # 确定市值区间
                cap_range = "unknown"
                for range_name, (min_val, max_val) in market_cap_ranges.items():
                    if min_val <= market_cap < max_val:
                        cap_range = range_name
                        break
                
                market_cap_exposures[cap_range] = market_cap_exposures.get(cap_range, 0) + position_value
            
            # 计算市值区间敞口比例
            for cap_range, value in market_cap_exposures.items():
                exposure = value / portfolio_value
                
                # 记录当前敞口
                self.current_exposures[cap_range] = exposure
                
                # 检查特定市值区间或所有区间
                if self.exposure_target is None or cap_range == self.exposure_target:
                    if exposure > self.threshold:
                        violations[cap_range] = {
                            "exposure": exposure,
                            "threshold": self.threshold,
                            "overage": exposure - self.threshold,
                            "market_cap_value": value
                        }
        
        elif self.limit_type == "overall":
            # 计算总体敞口（例如股票比例）
            total_stock_value = sum(position.get("value", 0) for position in positions.values())
            overall_exposure = total_stock_value / portfolio_value
            
            # 记录当前敞口
            self.current_exposures["overall"] = overall_exposure
            
            # 检查是否超过阈值
            if overall_exposure > self.threshold:
                violations["overall"] = {
                    "exposure": overall_exposure,
                    "threshold": self.threshold,
                    "overage": overall_exposure - self.threshold,
                    "total_value": total_stock_value
                }
        
        # 更新违规记录
        self.violations = violations
        
        return {
            "violated": len(violations) > 0,
            "violations": violations,
            "current_exposures": self.current_exposures,
            "limit_type": self.limit_type,
            "threshold": self.threshold
        }
    
    def apply_control(self, context: Dict[str, Any], orders: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        应用敞口限制控制，修改订单列表
        
        参数:
            context: 上下文信息
            orders: 原始订单列表
            
        返回:
            List[Dict[str, Any]]: 修改后的订单列表
        """
        if not self.is_active:
            return orders
        
        # 检查是否有敞口超限
        check_result = self.check_risk(context)
        violations = check_result.get("violations", {})
        
        if not violations:
            return orders
        
        # 获取持仓信息
        positions = context.get("positions", {})
        portfolio_value = context.get("portfolio_value", 0)
        
        # 创建新的订单列表
        new_orders = []
        
        # 根据限制类型调整订单
        if self.limit_type == "single":
            # 单一持仓限制
            for order in orders:
                symbol = order.get("symbol", "")
                direction = order.get("direction", "")
                
                # 如果是买入超限的股票，跳过或调整订单
                if symbol in violations and direction == "buy":
                    # 可以选择跳过订单或者调整数量
                    # 这里选择跳过
                    continue
                
                # 其他订单不变
                new_orders.append(order)
        
        elif self.limit_type == "industry":
            # 行业敞口限制
            # 获取行业信息
            stock_info = context.get("stock_info", {})
            industry_map = {s: info.get("industry", "unknown") for s, info in stock_info.items()}
            
            for order in orders:
                symbol = order.get("symbol", "")
                direction = order.get("direction", "")
                industry = industry_map.get(symbol, "unknown")
                
                # 如果是买入超限行业的股票，跳过或调整订单
                if industry in violations and direction == "buy":
                    continue
                
                # 其他订单不变
                new_orders.append(order)
        
        elif self.limit_type == "market_cap":
            # 市值敞口限制
            # 获取市值信息
            stock_info = context.get("stock_info", {})
            market_cap_map = {s: info.get("market_cap", 0) for s, info in stock_info.items()}
            
            # 定义市值区间
            market_cap_ranges = {
                "large": (10e9, float('inf')),
                "mid": (2e9, 10e9),
                "small": (300e6, 2e9),
                "micro": (0, 300e6)
            }
            
            for order in orders:
                symbol = order.get("symbol", "")
                direction = order.get("direction", "")
                market_cap = market_cap_map.get(symbol, 0)
                
                # 确定市值区间
                cap_range = "unknown"
                for range_name, (min_val, max_val) in market_cap_ranges.items():
                    if min_val <= market_cap < max_val:
                        cap_range = range_name
                        break
                
                # 如果是买入超限市值区间的股票，跳过或调整订单
                if cap_range in violations and direction == "buy":
                    continue
                
                # 其他订单不变
                new_orders.append(order)
        
        elif self.limit_type == "overall":
            # 总体敞口限制
            # 如果总体敞口超限，跳过所有买入订单
            if "overall" in violations:
                for order in orders:
                    direction = order.get("direction", "")
                    if direction != "buy":
                        new_orders.append(order)
            else:
                new_orders = orders
        
        return new_orders
    
    def get_control_status(self) -> Dict[str, Any]:
        """
        获取控制器状态
        
        返回:
            Dict[str, Any]: 控制器状态信息
        """
        return {
            "is_active": self.is_active,
            "limit_type": self.limit_type,
            "threshold": self.threshold,
            "exposure_target": self.exposure_target,
            "violations": self.violations.copy(),
            "current_exposures": self.current_exposures.copy()
        }
    
    def reset(self) -> None:
        """
        重置控制器状态
        """
        self.violations = {}
        self.current_exposures = {}
        self.is_active = True
    
    def get_description(self) -> str:
        """
        获取控制器的描述信息
        
        返回:
            str: 描述信息
        """
        return self.description
    
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取控制器的参数
        
        返回:
            Dict[str, Any]: 参数字典
        """
        return {
            "limit_type": self.limit_type,
            "threshold": self.threshold,
            "exposure_target": self.exposure_target,
            "name": self.name,
            "description": self.description
        }
    
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """
        设置控制器的参数
        
        参数:
            parameters: 参数字典
        """
        if "limit_type" in parameters:
            limit_type = parameters["limit_type"]
            if limit_type not in self.VALID_LIMIT_TYPES:
                raise RiskControlException(f"不支持的敞口限制类型: {limit_type}")
            self.limit_type = limit_type
            
        if "threshold" in parameters:
            threshold = parameters["threshold"]
            if not (0 < threshold <= 1):
                raise RiskControlException(f"敞口限制阈值必须在0和1之间: {threshold}")
            self.threshold = threshold
            
        if "exposure_target" in parameters:
            self.exposure_target = parameters["exposure_target"]
            
        if "name" in parameters:
            self.name = parameters["name"]
            
        if "description" in parameters:
            self.description = parameters["description"]
    
    def set_active(self, is_active: bool) -> None:
        """
        设置控制器是否激活
        
        参数:
            is_active: 是否激活
        """
        self.is_active = is_active

