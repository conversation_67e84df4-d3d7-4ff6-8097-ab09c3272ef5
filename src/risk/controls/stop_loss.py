"""
止损控制模块
- 实现各种止损策略
- 包括固定百分比止损、移动止损、时间止损等
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta

from src.risk.controls.control_interface import RiskControlInterface, RiskControlException

class StopLossControl(RiskControlInterface):
    """
    止损控制器
    
    实现多种止损策略，当达到止损条件时触发平仓操作
    
    支持的止损类型：
    - 固定百分比止损：当亏损达到一定百分比时触发
    - 跟踪止损（移动止损）：当价格从最高点回落一定比例时触发
    - 时间止损：持有超过特定时间且亏损时触发
    - 金额止损：当亏损金额达到特定值时触发
    
    参数：
    - stop_type: 止损类型，支持 "fixed"（固定百分比）, "trailing"（跟踪止损）, "time"（时间止损）, "amount"（金额止损）
    - threshold: 止损阈值，对于固定百分比和跟踪止损为比例值（如0.05表示5%），对于金额止损为具体金额
    - max_hold_days: 最大持有天数，用于时间止损
    """
    
    # 支持的止损类型
    VALID_STOP_TYPES = {"fixed", "trailing", "time", "amount"}
    
    def __init__(self, 
                 stop_type: str = "fixed",
                 threshold: float = 0.05,
                 max_hold_days: int = None,
                 apply_to: str = "all",
                 name: str = None,
                 description: str = ""):
        """
        初始化止损控制器
        
        参数：
            stop_type: 止损类型，支持 "fixed", "trailing", "time", "amount"
            threshold: 止损阈值，对于固定百分比和跟踪止损为比例值，对于金额止损为具体金额
            max_hold_days: 最大持有天数，用于时间止损
            apply_to: 应用范围，"all"表示所有持仓，"position"表示单个持仓，"portfolio"表示整个组合
            name: 控制器名称
            description: 控制器描述
        """
        if stop_type not in self.VALID_STOP_TYPES:
            raise RiskControlException(f"不支持的止损类型: {stop_type}，有效类型为: {self.VALID_STOP_TYPES}")
        
        if threshold <= 0:
            raise RiskControlException(f"止损阈值必须为正数，当前值: {threshold}")
        
        if stop_type == "time" and (max_hold_days is None or max_hold_days <= 0):
            raise RiskControlException(f"时间止损必须指定有效的最大持有天数，当前值: {max_hold_days}")
        
        self.stop_type = stop_type
        self.threshold = threshold
        self.max_hold_days = max_hold_days
        self.apply_to = apply_to
        self.name = name or f"止损控制器({stop_type})"
        self.description = description or f"使用{stop_type}策略实现止损控制"
        
        # 状态跟踪
        self.triggered_positions = {}  # 记录触发止损的持仓
        self.position_peaks = {}       # 记录每个持仓的峰值（用于跟踪止损）
        self.position_entry = {}       # 记录每个持仓的买入时间和价格
        self.is_active = True          # 控制器是否激活
    
    def check_risk(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        检查是否需要触发止损
        
        参数:
            context: 上下文信息，包括当前日期、持仓、价格等
            
        返回:
            Dict[str, Any]: 风险检查结果，包括触发止损的持仓列表等
        """
        if not self.is_active:
            return {"triggered": False, "positions": []}
        
        # 提取当前日期和持仓信息
        current_date = context.get("current_date")
        positions = context.get("positions", {})
        prices = context.get("prices", {})
        
        if not positions or not prices:
            return {"triggered": False, "positions": []}
        
        # 初始化结果
        triggered_positions = []
        
        # 根据应用范围检查止损
        if self.apply_to == "portfolio":
            # 对整个组合进行止损检查
            portfolio_value = context.get("portfolio_value", 0)
            initial_capital = context.get("initial_capital", 0)
            
            if portfolio_value > 0 and initial_capital > 0:
                portfolio_return = (portfolio_value - initial_capital) / initial_capital
                
                if self._check_stop_condition(portfolio_return, "portfolio", current_date, context):
                    # 触发整个组合止损，清空所有持仓
                    triggered_positions = list(positions.keys())
                    self.triggered_positions["portfolio"] = {
                        "trigger_date": current_date,
                        "trigger_value": portfolio_value,
                        "trigger_return": portfolio_return
                    }
        else:
            # 对单个持仓进行止损检查
            for symbol, position in positions.items():
                current_price = prices.get(symbol, None)
                if current_price is None:
                    continue
                
                # 计算持仓收益率
                cost_price = position.get("cost_price", current_price)
                position_return = (current_price - cost_price) / cost_price
                
                # 更新峰值价格（用于跟踪止损）
                if symbol not in self.position_peaks:
                    self.position_peaks[symbol] = current_price
                elif current_price > self.position_peaks[symbol]:
                    self.position_peaks[symbol] = current_price
                
                # 记录买入信息（如果是新持仓）
                if symbol not in self.position_entry:
                    self.position_entry[symbol] = {
                        "entry_date": current_date,
                        "entry_price": cost_price
                    }
                
                # 检查止损条件
                if self._check_stop_condition(position_return, symbol, current_date, context):
                    triggered_positions.append(symbol)
                    self.triggered_positions[symbol] = {
                        "trigger_date": current_date,
                        "trigger_price": current_price,
                        "cost_price": cost_price,
                        "position_return": position_return
                    }
        
        return {
            "triggered": len(triggered_positions) > 0,
            "positions": triggered_positions,
            "stop_type": self.stop_type,
            "threshold": self.threshold
        }
    
    def _check_stop_condition(self, return_value: float, symbol: str, current_date, context: Dict[str, Any]) -> bool:
        """
        检查止损条件
        
        参数:
            return_value:
            symbol: 证券代码
            current_date: 当前日期
            context: 上下文信息
            
        返回:
            bool: 是否触发止损
        """
        # 如果已经触发过止损，不再重复触发
        if symbol in self.triggered_positions:
            return False
        
        # 根据止损类型检查条件
        if self.stop_type == "fixed":
            # 固定百分比止损：亏损超过阈值
            return return_value < -self.threshold
            
        elif self.stop_type == "trailing":
            # 跟踪止损：从峰值回落超过阈值
            peak_price = self.position_peaks.get(symbol, 0)
            current_price = context.get("prices", {}).get(symbol, 0)
            
            if peak_price <= 0 or current_price <= 0:
                return False
                
            drawdown = (peak_price - current_price) / peak_price
            return drawdown > self.threshold
            
        elif self.stop_type == "time":
            # 时间止损：持有时间超过阈值且亏损
            entry_info = self.position_entry.get(symbol, None)
            if not entry_info or "entry_date" not in entry_info:
                return False
                
            entry_date = entry_info["entry_date"]
            
            # 计算持有天数
            if isinstance(current_date, datetime) and isinstance(entry_date, datetime):
                hold_days = (current_date - entry_date).days
            else:
                # 尝试转换为日期进行计算
                try:
                    curr_date = pd.to_datetime(current_date)
                    ent_date = pd.to_datetime(entry_date)
                    hold_days = (curr_date - ent_date).days
                except:
                    return False
            
            # 检查是否超过最大持有天数且亏损
            return hold_days >= self.max_hold_days and return_value < 0
            
        elif self.stop_type == "amount":
            # 金额止损：亏损金额超过阈值
            position = context.get("positions", {}).get(symbol, {})
            current_price = context.get("prices", {}).get(symbol, 0)
            
            if not position:
                return False
                
            quantity = position.get("quantity", 0)
            cost_price = position.get("cost_price", 0)
            
            if quantity <= 0 or cost_price <= 0 or current_price <= 0:
                return False
                
            loss_amount = (current_price - cost_price) * quantity
            return loss_amount < -self.threshold
        
        return False
    
    def apply_control(self, context: Dict[str, Any], orders: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        应用止损控制，修改订单列表
        
        参数:
            context: 上下文信息
            orders: 原始订单列表
            
        返回:
            List[Dict[str, Any]]: 修改后的订单列表
        """
        if not self.is_active:
            return orders
        
        # 检查是否有持仓触发止损
        check_result = self.check_risk(context)
        triggered_positions = check_result.get("positions", [])
        
        if not triggered_positions:
            return orders
        
        # 获取当前持仓
        positions = context.get("positions", {})
        
        # 创建新的订单列表
        new_orders = []
        
        # 添加原始订单（过滤掉触发止损的持仓）
        for order in orders:
            symbol = order.get("symbol", "")
            if symbol not in triggered_positions:
                new_orders.append(order)
        
        # 为触发止损的持仓添加平仓订单
        for symbol in triggered_positions:
            if symbol in positions:
                position = positions[symbol]
                quantity = position.get("quantity", 0)
                
                if quantity > 0:
                    # 创建卖出订单
                    stop_loss_order = {
                        "symbol": symbol,
                        "direction": "sell",
                        "quantity": quantity,
                        "order_type": "market",
                        "reason": f"止损:{self.stop_type}"
                    }
                    new_orders.append(stop_loss_order)
        
        return new_orders
    
    def get_control_status(self) -> Dict[str, Any]:
        """
        获取控制器状态
        
        返回:
            Dict[str, Any]: 控制器状态信息
        """
        return {
            "is_active": self.is_active,
            "stop_type": self.stop_type,
            "threshold": self.threshold,
            "triggered_positions": self.triggered_positions.copy(),
            "position_peaks": self.position_peaks.copy() if self.stop_type == "trailing" else None,
            "position_entry": self.position_entry.copy() if self.stop_type == "time" else None
        }
    
    def reset(self) -> None:
        """
        重置控制器状态
        """
        self.triggered_positions = {}
        self.position_peaks = {}
        self.position_entry = {}
        self.is_active = True
    
    def get_description(self) -> str:
        """
        获取控制器的描述信息
        
        返回:
            str: 描述信息
        """
        return self.description
    
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取控制器的参数
        
        返回:
            Dict[str, Any]: 参数字典
        """
        return {
            "stop_type": self.stop_type,
            "threshold": self.threshold,
            "max_hold_days": self.max_hold_days,
            "apply_to": self.apply_to,
            "name": self.name,
            "description": self.description
        }
    
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """
        设置控制器的参数
        
        参数:
            parameters: 参数字典
        """
        if "stop_type" in parameters:
            stop_type = parameters["stop_type"]
            if stop_type not in self.VALID_STOP_TYPES:
                raise RiskControlException(f"不支持的止损类型: {stop_type}")
            self.stop_type = stop_type
            
        if "threshold" in parameters:
            threshold = parameters["threshold"]
            if threshold <= 0:
                raise RiskControlException(f"止损阈值必须为正数: {threshold}")
            self.threshold = threshold
            
        if "max_hold_days" in parameters:
            self.max_hold_days = parameters["max_hold_days"]
            
        if "apply_to" in parameters:
            self.apply_to = parameters["apply_to"]
            
        if "name" in parameters:
            self.name = parameters["name"]
            
        if "description" in parameters:
            self.description = parameters["description"]
    
    def set_active(self, is_active: bool) -> None:
        """
        设置控制器是否激活
        
        参数:
            is_active: 是否激活
        """
        self.is_active = is_active

