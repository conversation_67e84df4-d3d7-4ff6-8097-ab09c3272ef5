"""
风险控制接口模块
- 定义风险控制规则的标准接口
- 为各种风险控制器提供统一的接口规范
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
import pandas as pd

class RiskControlException(Exception):
    """风险控制异常的基类"""
    pass

class RiskControlInterface(ABC):
    """
    风险控制接口
    所有风险控制器必须实现此接口
    """
    
    @abstractmethod
    def check_risk(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        检查风险
        
        参数:
            context: 上下文信息，包括当前持仓、市场数据等
            
        返回:
            Dict[str, Any]: 风险检查结果，包括是否触发控制、风险等级等
        """
        pass
    
    @abstractmethod
    def apply_control(self, context: Dict[str, Any], orders: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        应用风险控制规则
        
        参数:
            context: 上下文信息
            orders: 原始订单列表
            
        返回:
            List[Dict[str, Any]]: 调整后的订单列表
        """
        pass
    
    @abstractmethod
    def get_control_status(self) -> Dict[str, Any]:
        """
        获取控制器状态
        
        返回:
            Dict[str, Any]: 控制器状态信息
        """
        pass
    
    @abstractmethod
    def reset(self) -> None:
        """
        重置控制器状态
        """
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """
        获取控制器的描述信息
        
        返回:
            str: 描述信息
        """
        pass
    
    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取控制器的参数
        
        返回:
            Dict[str, Any]: 参数字典
        """
        pass
    
    @abstractmethod
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """
        设置控制器的参数
        
        参数:
            parameters: 参数字典
        """
        pass

