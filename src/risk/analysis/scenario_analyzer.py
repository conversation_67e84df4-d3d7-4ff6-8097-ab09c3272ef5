"""
情景分析器实现模块

提供完整的情景分析功能，包括：
- 多种情景生成方法
- 风险指标计算
- 压力测试
- 情景分析报告生成
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Union, Tuple
import logging
from datetime import datetime, timedelta

from src.risk.monitoring.var_monitor import VaRMonitor
from src.risk.monitoring.drawdown_monitor import DrawdownMonitor
from src.utils.logging.logger_factory import get_logger

logger = get_logger(__name__)


class ScenarioAnalyzer:
    """
    情景分析器 - 完整实现
    
    支持多种情景分析方法：
    1. 历史情景重现
    2. 蒙特卡洛模拟
    3. 压力测试情景
    4. 自定义情景
    """
    
    def __init__(self, scenarios_config: Dict[str, Any]):
        """
        初始化情景分析器
        
        参数:
            scenarios_config: 情景配置字典
        """
        # {{ AURA-X: Add - 实现完整的情景分析器. Approval: 寸止(ID:架构合规性修正). }}
        self.scenarios = scenarios_config
        self.var_monitor = VaRMonitor()
        self.drawdown_monitor = DrawdownMonitor()
        
        # 默认情景配置
        self.default_scenarios = {
            'market_crash': {
                'type': 'stress_test',
                'market_shock': -0.20,  # 20%市场下跌
                'volatility_multiplier': 2.0,
                'correlation_increase': 0.3,
                'duration_days': 30
            },
            'high_volatility': {
                'type': 'volatility_shock',
                'volatility_multiplier': 3.0,
                'duration_days': 60
            },
            'liquidity_crisis': {
                'type': 'liquidity_shock',
                'liquidity_reduction': 0.5,
                'bid_ask_spread_increase': 3.0,
                'duration_days': 45
            },
            'interest_rate_shock': {
                'type': 'rate_shock',
                'rate_change': 0.02,  # 200bp利率变化
                'duration_days': 90
            }
        }
        
        # 合并默认情景和用户配置
        self.scenarios.update({k: v for k, v in self.default_scenarios.items() 
                              if k not in self.scenarios})
    
    def run_scenario_analysis(self, portfolio_returns: pd.Series, 
                            portfolio_weights: Optional[pd.Series] = None,
                            market_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """
        运行完整的情景分析
        
        参数:
            portfolio_returns: 投资组合收益率序列
            portfolio_weights: 投资组合权重（可选）
            market_data: 市场数据（可选，用于更精确的情景生成）
            
        返回:
            情景分析结果字典
        """
        logger.info("开始情景分析")
        results = {}
        
        for scenario_name, scenario_params in self.scenarios.items():
            logger.info(f"分析情景: {scenario_name}")
            
            try:
                # 生成情景数据
                scenario_returns = self._generate_scenario_returns(
                    portfolio_returns, scenario_params, market_data
                )
                
                if scenario_returns is None or len(scenario_returns) == 0:
                    logger.warning(f"情景 {scenario_name} 生成失败，跳过")
                    continue
                
                # 计算风险指标
                var_result = self.var_monitor.calculate(scenario_returns)
                drawdown_result = self.drawdown_monitor.calculate(scenario_returns)
                
                # 运行压力测试
                stress_test_result = self._run_stress_test(scenario_returns, scenario_params)
                
                # 生成情景摘要
                scenario_summary = self._generate_scenario_summary(
                    var_result, drawdown_result, stress_test_result, scenario_params
                )
                
                results[scenario_name] = {
                    'scenario_params': scenario_params,
                    'returns': scenario_returns,
                    'var_analysis': var_result,
                    'drawdown_analysis': drawdown_result,
                    'stress_test': stress_test_result,
                    'summary': scenario_summary,
                    'risk_metrics': self._calculate_additional_risk_metrics(scenario_returns)
                }
                
            except Exception as e:
                logger.error(f"情景 {scenario_name} 分析失败: {e}")
                results[scenario_name] = {
                    'error': str(e),
                    'scenario_params': scenario_params
                }
        
        # 生成综合分析
        results['综合分析'] = self._generate_comprehensive_analysis(results)
        
        logger.info("情景分析完成")
        return results
    
    def _generate_scenario_returns(self, base_returns: pd.Series, 
                                 scenario_params: Dict[str, Any],
                                 market_data: Optional[pd.DataFrame] = None) -> pd.Series:
        """
        根据情景参数生成情景收益率
        
        参数:
            base_returns: 基础收益率序列
            scenario_params: 情景参数
            market_data: 市场数据
            
        返回:
            情景收益率序列
        """
        scenario_type = scenario_params.get('type', 'monte_carlo')
        
        if scenario_type == 'historical':
            return self._generate_historical_scenario(base_returns, scenario_params)
        elif scenario_type == 'monte_carlo':
            return self._generate_monte_carlo_scenario(base_returns, scenario_params)
        elif scenario_type == 'stress_test':
            return self._generate_stress_test_scenario(base_returns, scenario_params)
        elif scenario_type == 'volatility_shock':
            return self._generate_volatility_shock_scenario(base_returns, scenario_params)
        elif scenario_type == 'liquidity_shock':
            return self._generate_liquidity_shock_scenario(base_returns, scenario_params)
        elif scenario_type == 'rate_shock':
            return self._generate_rate_shock_scenario(base_returns, scenario_params)
        else:
            logger.warning(f"未知的情景类型: {scenario_type}，使用蒙特卡洛方法")
            return self._generate_monte_carlo_scenario(base_returns, scenario_params)
    
    def _generate_historical_scenario(self, base_returns: pd.Series, 
                                    scenario_params: Dict[str, Any]) -> pd.Series:
        """生成历史情景重现"""
        # 选择历史上的特定时期
        start_date = scenario_params.get('start_date')
        end_date = scenario_params.get('end_date')
        
        if start_date and end_date:
            mask = (base_returns.index >= start_date) & (base_returns.index <= end_date)
            historical_period = base_returns[mask]
            
            # 如果需要，可以重复或调整历史数据
            repeat_times = scenario_params.get('repeat_times', 1)
            if repeat_times > 1:
                historical_period = pd.concat([historical_period] * repeat_times)
            
            return historical_period
        else:
            # 随机选择历史时期
            window_size = scenario_params.get('window_size', 252)  # 默认一年
            if len(base_returns) < window_size:
                return base_returns
            
            start_idx = np.random.randint(0, len(base_returns) - window_size)
            return base_returns.iloc[start_idx:start_idx + window_size]
    
    def _generate_monte_carlo_scenario(self, base_returns: pd.Series, 
                                     scenario_params: Dict[str, Any]) -> pd.Series:
        """生成蒙特卡洛模拟情景"""
        # 估计参数
        mu = base_returns.mean()
        sigma = base_returns.std()
        
        # 调整参数
        mu_adjustment = scenario_params.get('mean_adjustment', 0)
        sigma_multiplier = scenario_params.get('volatility_multiplier', 1.0)
        
        adjusted_mu = mu + mu_adjustment
        adjusted_sigma = sigma * sigma_multiplier
        
        # 生成模拟数据
        simulation_length = scenario_params.get('simulation_length', len(base_returns))
        simulated_returns = np.random.normal(adjusted_mu, adjusted_sigma, simulation_length)
        
        # 创建时间索引
        start_date = base_returns.index[-1] + timedelta(days=1)
        date_range = pd.date_range(start=start_date, periods=simulation_length, freq='D')
        
        return pd.Series(simulated_returns, index=date_range)
    
    def _generate_stress_test_scenario(self, base_returns: pd.Series, 
                                     scenario_params: Dict[str, Any]) -> pd.Series:
        """生成压力测试情景"""
        market_shock = scenario_params.get('market_shock', -0.10)
        volatility_multiplier = scenario_params.get('volatility_multiplier', 1.5)
        duration_days = scenario_params.get('duration_days', 30)
        
        # 基础统计
        mu = base_returns.mean()
        sigma = base_returns.std()
        
        # 压力期间的参数
        stress_mu = mu + market_shock / duration_days  # 将冲击分散到整个期间
        stress_sigma = sigma * volatility_multiplier
        
        # 生成压力期间的收益率
        stress_returns = np.random.normal(stress_mu, stress_sigma, duration_days)
        
        # 恢复期间的参数（逐渐回归正常）
        recovery_days = scenario_params.get('recovery_days', duration_days // 2)
        if recovery_days > 0:
            recovery_mu = np.linspace(stress_mu, mu, recovery_days)
            recovery_sigma = np.linspace(stress_sigma, sigma, recovery_days)
            recovery_returns = [np.random.normal(m, s) for m, s in zip(recovery_mu, recovery_sigma)]
            
            all_returns = np.concatenate([stress_returns, recovery_returns])
        else:
            all_returns = stress_returns
        
        # 创建时间索引
        start_date = base_returns.index[-1] + timedelta(days=1)
        date_range = pd.date_range(start=start_date, periods=len(all_returns), freq='D')
        
        return pd.Series(all_returns, index=date_range)

    def _generate_volatility_shock_scenario(self, base_returns: pd.Series,
                                          scenario_params: Dict[str, Any]) -> pd.Series:
        """生成波动率冲击情景"""
        volatility_multiplier = scenario_params.get('volatility_multiplier', 2.0)
        duration_days = scenario_params.get('duration_days', 60)

        mu = base_returns.mean()
        sigma = base_returns.std() * volatility_multiplier

        shock_returns = np.random.normal(mu, sigma, duration_days)

        start_date = base_returns.index[-1] + timedelta(days=1)
        date_range = pd.date_range(start=start_date, periods=duration_days, freq='D')

        return pd.Series(shock_returns, index=date_range)

    def _generate_liquidity_shock_scenario(self, base_returns: pd.Series,
                                         scenario_params: Dict[str, Any]) -> pd.Series:
        """生成流动性冲击情景"""
        liquidity_reduction = scenario_params.get('liquidity_reduction', 0.5)
        bid_ask_spread_increase = scenario_params.get('bid_ask_spread_increase', 2.0)
        duration_days = scenario_params.get('duration_days', 45)

        # 流动性冲击主要影响交易成本和价格冲击
        mu = base_returns.mean()
        sigma = base_returns.std()

        # 增加交易成本的影响
        transaction_cost_impact = -0.001 * bid_ask_spread_increase  # 每日额外成本

        # 增加价格冲击的随机性
        price_impact_volatility = sigma * (1 + liquidity_reduction)

        adjusted_mu = mu + transaction_cost_impact
        shock_returns = np.random.normal(adjusted_mu, price_impact_volatility, duration_days)

        start_date = base_returns.index[-1] + timedelta(days=1)
        date_range = pd.date_range(start=start_date, periods=duration_days, freq='D')

        return pd.Series(shock_returns, index=date_range)

    def _generate_rate_shock_scenario(self, base_returns: pd.Series,
                                    scenario_params: Dict[str, Any]) -> pd.Series:
        """生成利率冲击情景"""
        rate_change = scenario_params.get('rate_change', 0.02)  # 200bp
        duration_days = scenario_params.get('duration_days', 90)

        # 利率变化对不同资产的影响不同，这里简化处理
        # 假设利率上升对股票有负面影响
        rate_impact = -rate_change * 0.5  # 简化的利率敏感性

        mu = base_returns.mean() + rate_impact / duration_days
        sigma = base_returns.std()

        shock_returns = np.random.normal(mu, sigma, duration_days)

        start_date = base_returns.index[-1] + timedelta(days=1)
        date_range = pd.date_range(start=start_date, periods=duration_days, freq='D')

        return pd.Series(shock_returns, index=date_range)

    def _run_stress_test(self, scenario_returns: pd.Series,
                        scenario_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        运行压力测试

        参数:
            scenario_returns: 情景收益率
            scenario_params: 情景参数

        返回:
            压力测试结果
        """
        # 计算压力测试指标
        total_return = (1 + scenario_returns).prod() - 1
        annualized_return = (1 + total_return) ** (252 / len(scenario_returns)) - 1
        annualized_volatility = scenario_returns.std() * np.sqrt(252)

        # 计算最大连续亏损天数
        cumulative_returns = (1 + scenario_returns).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max

        max_consecutive_loss_days = 0
        current_loss_days = 0
        for ret in scenario_returns:
            if ret < 0:
                current_loss_days += 1
                max_consecutive_loss_days = max(max_consecutive_loss_days, current_loss_days)
            else:
                current_loss_days = 0

        # 计算尾部风险指标
        sorted_returns = scenario_returns.sort_values()
        var_95 = sorted_returns.quantile(0.05)
        var_99 = sorted_returns.quantile(0.01)
        cvar_95 = sorted_returns[sorted_returns <= var_95].mean()
        cvar_99 = sorted_returns[sorted_returns <= var_99].mean()

        return {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'annualized_volatility': annualized_volatility,
            'sharpe_ratio': annualized_return / annualized_volatility if annualized_volatility > 0 else 0,
            'max_drawdown': drawdown.min(),
            'max_consecutive_loss_days': max_consecutive_loss_days,
            'var_95': var_95,
            'var_99': var_99,
            'cvar_95': cvar_95,
            'cvar_99': cvar_99,
            'negative_return_ratio': (scenario_returns < 0).sum() / len(scenario_returns),
            'extreme_loss_days': (scenario_returns < -0.05).sum()  # 单日亏损超过5%的天数
        }

    def _calculate_additional_risk_metrics(self, returns: pd.Series) -> Dict[str, Any]:
        """计算额外的风险指标"""
        # 偏度和峰度
        skewness = returns.skew()
        kurtosis = returns.kurtosis()

        # 下行风险指标
        negative_returns = returns[returns < 0]
        downside_deviation = negative_returns.std() if len(negative_returns) > 0 else 0

        # 最大回撤恢复时间
        cumulative_returns = (1 + returns).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max

        # 找到最大回撤点
        max_dd_idx = drawdown.idxmin()
        max_dd_value = drawdown.min()

        # 计算恢复时间
        recovery_time = None
        if max_dd_idx in drawdown.index:
            recovery_series = drawdown[max_dd_idx:]
            recovery_idx = recovery_series[recovery_series >= -0.001].index  # 接近恢复
            if len(recovery_idx) > 0:
                recovery_time = (recovery_idx[0] - max_dd_idx).days

        return {
            'skewness': skewness,
            'kurtosis': kurtosis,
            'downside_deviation': downside_deviation,
            'max_drawdown_recovery_days': recovery_time,
            'calmar_ratio': returns.mean() * 252 / abs(max_dd_value) if max_dd_value != 0 else 0
        }

    def _generate_scenario_summary(self, var_result: Dict[str, Any],
                                 drawdown_result: Dict[str, Any],
                                 stress_test_result: Dict[str, Any],
                                 scenario_params: Dict[str, Any]) -> Dict[str, Any]:
        """生成情景分析摘要"""
        # 风险等级评估
        risk_level = self._assess_risk_level(var_result, drawdown_result, stress_test_result)

        # 关键风险指标
        key_metrics = {
            'var_95': var_result.get('var_pct', 0),
            'max_drawdown': drawdown_result.get('max_drawdown', 0),
            'total_return': stress_test_result.get('total_return', 0),
            'volatility': stress_test_result.get('annualized_volatility', 0)
        }

        # 风险建议
        recommendations = self._generate_risk_recommendations(
            risk_level, key_metrics, scenario_params
        )

        return {
            'risk_level': risk_level,
            'key_metrics': key_metrics,
            'recommendations': recommendations,
            'scenario_type': scenario_params.get('type', 'unknown'),
            'duration': scenario_params.get('duration_days', 0)
        }

    def _assess_risk_level(self, var_result: Dict[str, Any],
                          drawdown_result: Dict[str, Any],
                          stress_test_result: Dict[str, Any]) -> str:
        """评估风险等级"""
        var_95 = abs(var_result.get('var_pct', 0))
        max_drawdown = abs(drawdown_result.get('max_drawdown', 0))
        volatility = stress_test_result.get('annualized_volatility', 0)

        # 风险评分
        risk_score = 0

        # VaR评分
        if var_95 > 0.05:
            risk_score += 3
        elif var_95 > 0.03:
            risk_score += 2
        elif var_95 > 0.01:
            risk_score += 1

        # 回撤评分
        if max_drawdown > 0.20:
            risk_score += 3
        elif max_drawdown > 0.10:
            risk_score += 2
        elif max_drawdown > 0.05:
            risk_score += 1

        # 波动率评分
        if volatility > 0.30:
            risk_score += 3
        elif volatility > 0.20:
            risk_score += 2
        elif volatility > 0.15:
            risk_score += 1

        # 风险等级判定
        if risk_score >= 7:
            return '极高'
        elif risk_score >= 5:
            return '高'
        elif risk_score >= 3:
            return '中'
        else:
            return '低'

    def _generate_risk_recommendations(self, risk_level: str,
                                     key_metrics: Dict[str, Any],
                                     scenario_params: Dict[str, Any]) -> List[str]:
        """生成风险建议"""
        recommendations = []

        if risk_level in ['极高', '高']:
            recommendations.append('建议降低仓位或增加对冲')
            recommendations.append('考虑增加现金或低风险资产配置')

        if abs(key_metrics.get('max_drawdown', 0)) > 0.15:
            recommendations.append('建议设置更严格的止损策略')

        if key_metrics.get('volatility', 0) > 0.25:
            recommendations.append('考虑分散投资以降低波动率')

        scenario_type = scenario_params.get('type', '')
        if scenario_type == 'liquidity_shock':
            recommendations.append('建议增加流动性较好的资产配置')
        elif scenario_type == 'rate_shock':
            recommendations.append('考虑利率敏感性对冲策略')

        if not recommendations:
            recommendations.append('当前风险水平可接受，建议继续监控')

        return recommendations

    def _generate_comprehensive_analysis(self, scenario_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成综合分析结果"""
        # 排除错误和综合分析本身
        valid_scenarios = {k: v for k, v in scenario_results.items()
                          if k != '综合分析' and 'error' not in v}

        if not valid_scenarios:
            return {'error': '没有有效的情景分析结果'}

        # 汇总风险指标
        all_vars = [result['var_analysis'].get('var_pct', 0)
                   for result in valid_scenarios.values()]
        all_drawdowns = [result['drawdown_analysis'].get('max_drawdown', 0)
                        for result in valid_scenarios.values()]
        all_returns = [result['stress_test'].get('total_return', 0)
                      for result in valid_scenarios.values()]

        # 最坏情景识别
        worst_var_scenario = min(valid_scenarios.items(),
                               key=lambda x: x[1]['var_analysis'].get('var_pct', 0))
        worst_drawdown_scenario = min(valid_scenarios.items(),
                                    key=lambda x: x[1]['drawdown_analysis'].get('max_drawdown', 0))

        # 综合风险评估
        avg_var = np.mean([abs(v) for v in all_vars])
        avg_drawdown = np.mean([abs(d) for d in all_drawdowns])
        avg_return = np.mean(all_returns)

        overall_risk_level = self._assess_overall_risk_level(avg_var, avg_drawdown)

        return {
            'scenario_count': len(valid_scenarios),
            'average_var_95': avg_var,
            'average_max_drawdown': avg_drawdown,
            'average_return': avg_return,
            'worst_var_scenario': worst_var_scenario[0],
            'worst_drawdown_scenario': worst_drawdown_scenario[0],
            'overall_risk_level': overall_risk_level,
            'risk_distribution': self._analyze_risk_distribution(valid_scenarios),
            'correlation_analysis': self._analyze_scenario_correlations(valid_scenarios)
        }

    def _assess_overall_risk_level(self, avg_var: float, avg_drawdown: float) -> str:
        """评估整体风险等级"""
        if avg_var > 0.04 or avg_drawdown > 0.15:
            return '高'
        elif avg_var > 0.02 or avg_drawdown > 0.08:
            return '中'
        else:
            return '低'

    def _analyze_risk_distribution(self, scenarios: Dict[str, Any]) -> Dict[str, Any]:
        """分析风险分布"""
        risk_levels = [result['summary']['risk_level'] for result in scenarios.values()]
        risk_counts = {}
        for level in ['低', '中', '高', '极高']:
            risk_counts[level] = risk_levels.count(level)

        return {
            'risk_level_distribution': risk_counts,
            'high_risk_scenarios': [name for name, result in scenarios.items()
                                  if result['summary']['risk_level'] in ['高', '极高']]
        }

    def _analyze_scenario_correlations(self, scenarios: Dict[str, Any]) -> Dict[str, Any]:
        """分析情景间相关性"""
        # 简化的相关性分析
        scenario_names = list(scenarios.keys())
        if len(scenario_names) < 2:
            return {'correlation_matrix': {}, 'note': '情景数量不足，无法计算相关性'}

        # 提取关键指标进行相关性分析
        metrics_data = {}
        for name, result in scenarios.items():
            metrics_data[name] = {
                'var': result['var_analysis'].get('var_pct', 0),
                'drawdown': result['drawdown_analysis'].get('max_drawdown', 0),
                'return': result['stress_test'].get('total_return', 0)
            }

        return {
            'correlation_matrix': metrics_data,
            'note': '相关性分析基于VaR、最大回撤和总收益'
        }

    def generate_scenario_report(self, analysis_results: Dict[str, Any],
                               output_format: str = 'html') -> str:
        """
        生成情景分析报告

        参数:
            analysis_results: 分析结果
            output_format: 输出格式 ('html', 'markdown', 'text')

        返回:
            报告内容字符串
        """
        if output_format == 'html':
            return self._generate_html_report(analysis_results)
        elif output_format == 'markdown':
            return self._generate_markdown_report(analysis_results)
        else:
            return self._generate_text_report(analysis_results)

    def _generate_html_report(self, results: Dict[str, Any]) -> str:
        """生成HTML格式报告"""
        html = """
        <html>
        <head>
            <title>情景分析报告</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }
                .scenario { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                .risk-high { background-color: #ffebee; }
                .risk-medium { background-color: #fff3e0; }
                .risk-low { background-color: #e8f5e8; }
                table { border-collapse: collapse; width: 100%; margin: 10px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
        """

        html += f"<div class='header'><h1>情景分析报告</h1>"
        html += f"<p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p></div>"

        # 综合分析
        if '综合分析' in results:
            comprehensive = results['综合分析']
            html += "<h2>综合分析</h2>"
            html += f"<p>分析情景数量: {comprehensive.get('scenario_count', 0)}</p>"
            html += f"<p>整体风险等级: <strong>{comprehensive.get('overall_risk_level', '未知')}</strong></p>"
            html += f"<p>平均VaR(95%): {comprehensive.get('average_var_95', 0):.2%}</p>"
            html += f"<p>平均最大回撤: {comprehensive.get('average_max_drawdown', 0):.2%}</p>"

        # 各情景详情
        html += "<h2>情景分析详情</h2>"
        for scenario_name, result in results.items():
            if scenario_name == '综合分析' or 'error' in result:
                continue

            risk_level = result.get('summary', {}).get('risk_level', '未知')
            css_class = {
                '极高': 'risk-high', '高': 'risk-high',
                '中': 'risk-medium', '低': 'risk-low'
            }.get(risk_level, '')

            html += f"<div class='scenario {css_class}'>"
            html += f"<h3>{scenario_name}</h3>"
            html += f"<p>风险等级: <strong>{risk_level}</strong></p>"

            # 关键指标表格
            html += "<table>"
            html += "<tr><th>指标</th><th>数值</th></tr>"

            var_result = result.get('var_analysis', {})
            html += f"<tr><td>VaR(95%)</td><td>{var_result.get('var_pct', 0):.2%}</td></tr>"

            drawdown_result = result.get('drawdown_analysis', {})
            html += f"<tr><td>最大回撤</td><td>{drawdown_result.get('max_drawdown', 0):.2%}</td></tr>"

            stress_result = result.get('stress_test', {})
            html += f"<tr><td>总收益</td><td>{stress_result.get('total_return', 0):.2%}</td></tr>"
            html += f"<tr><td>年化波动率</td><td>{stress_result.get('annualized_volatility', 0):.2%}</td></tr>"

            html += "</table>"

            # 建议
            recommendations = result.get('summary', {}).get('recommendations', [])
            if recommendations:
                html += "<h4>风险建议:</h4><ul>"
                for rec in recommendations:
                    html += f"<li>{rec}</li>"
                html += "</ul>"

            html += "</div>"

        html += "</body></html>"
        return html

    def _generate_markdown_report(self, results: Dict[str, Any]) -> str:
        """生成Markdown格式报告"""
        md = f"# 情景分析报告\n\n"
        md += f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        # 综合分析
        if '综合分析' in results:
            comprehensive = results['综合分析']
            md += "## 综合分析\n\n"
            md += f"- 分析情景数量: {comprehensive.get('scenario_count', 0)}\n"
            md += f"- 整体风险等级: **{comprehensive.get('overall_risk_level', '未知')}**\n"
            md += f"- 平均VaR(95%): {comprehensive.get('average_var_95', 0):.2%}\n"
            md += f"- 平均最大回撤: {comprehensive.get('average_max_drawdown', 0):.2%}\n\n"

        # 各情景详情
        md += "## 情景分析详情\n\n"
        for scenario_name, result in results.items():
            if scenario_name == '综合分析' or 'error' in result:
                continue

            md += f"### {scenario_name}\n\n"

            risk_level = result.get('summary', {}).get('risk_level', '未知')
            md += f"**风险等级**: {risk_level}\n\n"

            # 关键指标
            md += "| 指标 | 数值 |\n|------|------|\n"

            var_result = result.get('var_analysis', {})
            md += f"| VaR(95%) | {var_result.get('var_pct', 0):.2%} |\n"

            drawdown_result = result.get('drawdown_analysis', {})
            md += f"| 最大回撤 | {drawdown_result.get('max_drawdown', 0):.2%} |\n"

            stress_result = result.get('stress_test', {})
            md += f"| 总收益 | {stress_result.get('total_return', 0):.2%} |\n"
            md += f"| 年化波动率 | {stress_result.get('annualized_volatility', 0):.2%} |\n\n"

            # 建议
            recommendations = result.get('summary', {}).get('recommendations', [])
            if recommendations:
                md += "**风险建议**:\n"
                for rec in recommendations:
                    md += f"- {rec}\n"
                md += "\n"

        return md

    def _generate_text_report(self, results: Dict[str, Any]) -> str:
        """生成纯文本格式报告"""
        text = "=" * 50 + "\n"
        text += "情景分析报告\n"
        text += "=" * 50 + "\n"
        text += f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        # 综合分析
        if '综合分析' in results:
            comprehensive = results['综合分析']
            text += "综合分析\n" + "-" * 20 + "\n"
            text += f"分析情景数量: {comprehensive.get('scenario_count', 0)}\n"
            text += f"整体风险等级: {comprehensive.get('overall_risk_level', '未知')}\n"
            text += f"平均VaR(95%): {comprehensive.get('average_var_95', 0):.2%}\n"
            text += f"平均最大回撤: {comprehensive.get('average_max_drawdown', 0):.2%}\n\n"

        # 各情景详情
        text += "情景分析详情\n" + "-" * 20 + "\n"
        for scenario_name, result in results.items():
            if scenario_name == '综合分析' or 'error' in result:
                continue

            text += f"\n{scenario_name}\n" + "." * len(scenario_name) + "\n"

            risk_level = result.get('summary', {}).get('risk_level', '未知')
            text += f"风险等级: {risk_level}\n"

            # 关键指标
            var_result = result.get('var_analysis', {})
            text += f"VaR(95%): {var_result.get('var_pct', 0):.2%}\n"

            drawdown_result = result.get('drawdown_analysis', {})
            text += f"最大回撤: {drawdown_result.get('max_drawdown', 0):.2%}\n"

            stress_result = result.get('stress_test', {})
            text += f"总收益: {stress_result.get('total_return', 0):.2%}\n"
            text += f"年化波动率: {stress_result.get('annualized_volatility', 0):.2%}\n"

            # 建议
            recommendations = result.get('summary', {}).get('recommendations', [])
            if recommendations:
                text += "风险建议:\n"
                for i, rec in enumerate(recommendations, 1):
                    text += f"  {i}. {rec}\n"

        return text


# 便捷函数
def create_scenario_analyzer(scenarios_config: Dict[str, Any]) -> ScenarioAnalyzer:
    """创建情景分析器的便捷函数"""
    return ScenarioAnalyzer(scenarios_config)

def run_quick_scenario_analysis(portfolio_returns: pd.Series) -> Dict[str, Any]:
    """快速情景分析的便捷函数"""
    default_scenarios = {
        'market_crash': {'type': 'stress_test', 'market_shock': -0.20, 'duration_days': 30},
        'high_volatility': {'type': 'volatility_shock', 'volatility_multiplier': 2.0, 'duration_days': 60}
    }

    analyzer = ScenarioAnalyzer(default_scenarios)
    return analyzer.run_scenario_analysis(portfolio_returns)
