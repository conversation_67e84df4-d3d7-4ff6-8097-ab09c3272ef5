"""
策略引擎工厂：负责创建不同类型的策略引擎实例
- 支持多因子策略引擎
- 支持择时策略引擎
- 提供统一的引擎创建接口
"""

from typing import Dict, Any, Optional, Union, Type, List
import importlib
from abc import ABC, abstractmethod

class StrategyEngine(ABC):
    """
    策略引擎基类，所有策略引擎必须继承此类
    """
    
    @abstractmethod
    def initialize(self, **kwargs) -> None:
        """初始化策略引擎"""
        pass
    
    @abstractmethod
    def run(self, **kwargs) -> Dict[str, Any]:
        """运行策略引擎"""
        pass
    
    @abstractmethod
    def get_results(self) -> Dict[str, Any]:
        """获取策略运行结果"""
        pass

class EngineFactory:
    """
    策略引擎工厂类，用于创建各种策略引擎实例
    """
    
    # 策略引擎类型映射
    _engines = {
        # 多因子策略引擎
        'factor': 'strategy.engines.factor_engine.FactorEngine',
        'alpha': 'strategy.engines.alpha_engine.AlphaEngine',
        'multi_factor': 'strategy.engines.multi_factor_engine.MultiFactorEngine',
        
        # 择时策略引擎
        'timing': 'strategy.engines.timing_engine.TimingEngine',
        'trend': 'strategy.engines.trend_engine.TrendEngine',
        
        # 组合策略引擎
        'portfolio': 'strategy.engines.portfolio_engine.PortfolioEngine',
        'sector_rotation': 'strategy.engines.sector_rotation_engine.SectorRotationEngine',
        
        # 混合策略引擎
        'hybrid': 'strategy.engines.hybrid_engine.HybridEngine'
    }
    
    @classmethod
    def create(cls, engine_type: str, config: Dict[str, Any] = None) -> StrategyEngine:
        """
        创建指定类型的策略引擎实例
        
        参数：
            engine_type: 引擎类型，如'factor', 'timing', 'portfolio'等
            config: 引擎配置参数
            
        返回：
            StrategyEngine: 策略引擎实例
            
        异常：
            ValueError: 引擎类型不支持时抛出
            ImportError: 无法导入对应模块时抛出
        """
        if engine_type not in cls._engines:
            supported = ', '.join(cls._engines.keys())
            raise ValueError(f"不支持的策略引擎类型: {engine_type}，支持的类型包括: {supported}")
        
        # 导入引擎类
        module_path = cls._engines[engine_type]
        module_name, class_name = module_path.rsplit('.', 1)
        
        try:
            module = importlib.import_module(module_name)
            engine_class = getattr(module, class_name)
        except (ImportError, AttributeError) as e:
            raise ImportError(f"无法导入策略引擎 {module_path}: {str(e)}")
        
        # 创建引擎实例
        if config is None:
            config = {}
            
        return engine_class(**config)
    
    @classmethod
    def create_from_config(cls, config: Dict[str, Any]) -> StrategyEngine:
        """
        从配置字典创建策略引擎实例
        
        参数：
            config: 配置字典，必须包含'type'字段
            
        返回：
            StrategyEngine: 策略引擎实例
            
        异常：
            ValueError: 配置不正确时抛出
        """
        if 'type' not in config:
            raise ValueError("配置必须包含'type'字段")
        
        engine_type = config.pop('type')
        return cls.create(engine_type, config)
    
    @classmethod
    def create_multi_engine(cls, configs: List[Dict[str, Any]]) -> List[StrategyEngine]:
        """
        批量创建多个策略引擎实例
        
        参数：
            configs: 配置字典列表，每个字典必须包含'type'字段
            
        返回：
            List[StrategyEngine]: 策略引擎实例列表
            
        异常：
            ValueError: 配置不正确时抛出
        """
        engines = []
        for config in configs:
            engines.append(cls.create_from_config(config))
        return engines
    
    @classmethod
    def register_engine(cls, engine_type: str, module_path: str) -> None:
        """
        注册新的策略引擎类型
        
        参数：
            engine_type: 引擎类型名称
            module_path: 模块路径，格式为'package.module.Class'
            
        异常：
            ValueError: 引擎类型已存在时抛出
        """
        if engine_type in cls._engines:
            raise ValueError(f"引擎类型'{engine_type}'已存在")
        
        cls._engines[engine_type] = module_path
    
    @classmethod
    def get_available_engines(cls) -> Dict[str, str]:
        """
        获取所有可用的引擎类型
        
        返回：
            Dict[str, str]: 引擎类型到模块路径的映射
        """
        return cls._engines.copy()

