"""
估值因子模块
- 实现市盈率（PE）因子
- 实现市净率（PB）因子
- 实现市销率（PS）因子
- 实现股息率因子
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union, Optional, Tuple, Set
from datetime import datetime, date

from data.storage.storage_factory import StorageFactory
from ..base_factor import BaseFactor, ComputeError, DataError

class PEFactor(BaseFactor):
    """
    市盈率（Price-to-Earnings Ratio）因子
    
    计算方法：
    1. 静态市盈率 = 当前股价 / 最近年度每股收益
    2. 动态市盈率 = 当前股价 / 预测每股收益
    3. 滚动市盈率 = 当前股价 / 最近四个季度每股收益之和
    """
    
    # 支持的PE类型
    VALID_PE_TYPES = {'ttm', 'static', 'forward'}
    
    def __init__(self, 
                pe_type: str = "ttm",  # ttm, static, forward
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化PE因子
        
        Args:
            pe_type: PE类型，可选值：
                - ttm: 滚动市盈率（过去12个月）
                - static: 静态市盈率（最近年报）
                - forward: 动态市盈率（预测值）
            name: 因子名称，默认为f"pe_{pe_type}"
            description: 因子描述
            cache_dir: 缓存目录
        """
        pe_type = pe_type.lower()
        if pe_type not in self.VALID_PE_TYPES:
            raise ValueError(f"不支持的PE类型: {pe_type}，支持的类型包括: {', '.join(self.VALID_PE_TYPES)}")
            
        if name is None:
            name = f"pe_{pe_type}"
            
        if not description:
            description = f"市盈率因子 (类型: {pe_type})"
            
        super().__init__(name, description, "fundamental", cache_dir)
        
        self.pe_type = pe_type
        self.name = name  # 明确存储名称为实例变量
        
        # 更新元数据
        self._metadata.update({
            'pe_type': pe_type
        })
        
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        实现PE因子的计算逻辑
        
        Args:
            **kwargs: 计算参数
                data: 输入数据DataFrame，必须包含价格和每股收益数据
                start_date: 开始日期
                end_date: 结束日期
                
        Returns:
            pd.DataFrame: 因子计算结果，包含PE值
        """
        # 获取参数
        data = kwargs.get('data')
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        
        if data is None:
            raise ComputeError("必须提供数据")
        
        if not start_date or not end_date:
            raise ComputeError("必须提供开始日期和结束日期")
        
        try:
            # 确保数据不为空
            if data.empty:
                raise DataError("输入数据为空")
            
            # 确保日期格式正确
            date_col = 'trade_date' if 'trade_date' in data.columns else 'date'
            if not pd.api.types.is_datetime64_dtype(data[date_col]):
                data[date_col] = pd.to_datetime(data[date_col])
            
            # 确保必要的字段存在
            required_fields = ['close']  # 股价数据
            if self.pe_type == 'ttm':
                required_fields.extend(['net_profit_ttm', 'total_share'])
            elif self.pe_type == 'static':
                required_fields.extend(['net_profit', 'total_share'])
            elif self.pe_type == 'forward':
                required_fields.extend(['forecast_net_profit', 'total_share'])
            
            for field in required_fields:
                if field not in data.columns:
                    raise DataError(f"数据中缺少所需字段: {field}")
            
            # 按证券分组计算PE
            result = pd.DataFrame()
            
            for code, group in data.groupby('ts_code'):
                # 排序，确保数据按日期有序
                group = group.sort_values(date_col)
                
                # 计算每股收益
                if self.pe_type == 'ttm':
                    eps = group['net_profit_ttm'] / group['total_share']
                elif self.pe_type == 'static':
                    eps = group['net_profit'] / group['total_share']
                else:  # forward
                    eps = group['forecast_net_profit'] / group['total_share']
                
                # 计算PE值
                # 处理eps为0或负数的情况
                pe = pd.Series(np.nan, index=group.index)
                valid_eps = eps > 0
                pe[valid_eps] = group.loc[valid_eps, 'close'] / eps[valid_eps]
                
                # 创建结果DataFrame
                df = pd.DataFrame({
                    'ts_code': code,
                    date_col: group[date_col],
                    self.name: pe
                })
                
                # 添加到结果集
                result = pd.concat([result, df], ignore_index=True)
            
            # 筛选目标日期范围
            result = result[
                (result[date_col] >= pd.to_datetime(start_date)) & 
                (result[date_col] <= pd.to_datetime(end_date))
            ]
            
            # 设置索引
            result = result.set_index([date_col, 'ts_code']).reset_index()
            
            return result
            
        except Exception as e:
            if isinstance(e, (ComputeError, DataError)):
                raise
            
            self.logger.error(f"计算PE失败: {str(e)}")
            raise ComputeError(f"计算PE失败: {str(e)}")
    
    def get_required_parameters(self) -> List[str]:
        """
        获取必需的计算参数列表
        
        Returns:
            List[str]: 必需参数列表
        """
        return ['data', 'start_date', 'end_date']

class PBFactor(BaseFactor):
    """
    市净率（Price-to-Book Ratio）因子
    
    计算方法：
    市净率 = 当前股价 / 每股净资产
    
    每股净资产 = 净资产 / 总股本
    
    用途：
    1. 评估公司的资产价值
    2. 识别价值投资机会
    3. 不同行业间的估值比较
    """
    
    def __init__(self, 
                name: str = "pb",
                description: str = "市净率因子",
                cache_dir: str = None):
        """
        初始化PB因子
        
        Args:
            name: 因子名称，默认为"pb"
            description: 因子描述
            cache_dir: 缓存目录
        """
        super().__init__(name, description, "fundamental", cache_dir)
        
        self.name = name  # 明确存储名称为实例变量
        
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        实现PB因子的计算逻辑
        
        Args:
            **kwargs: 计算参数
                data: 输入数据DataFrame，必须包含价格和净资产数据
                start_date: 开始日期
                end_date: 结束日期
                
        Returns:
            pd.DataFrame: 因子计算结果，包含PB值
        """
        # 获取参数
        data = kwargs.get('data')
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        
        if data is None:
            raise ComputeError("必须提供数据")
        
        if not start_date or not end_date:
            raise ComputeError("必须提供开始日期和结束日期")
        
        try:
            # 确保数据不为空
            if data.empty:
                raise DataError("输入数据为空")
            
            # 确保日期格式正确
            date_col = 'trade_date' if 'trade_date' in data.columns else 'date'
            if not pd.api.types.is_datetime64_dtype(data[date_col]):
                data[date_col] = pd.to_datetime(data[date_col])
            
            # 确保必要的字段存在
            required_fields = ['close', 'total_assets', 'total_liab', 'total_share']
            for field in required_fields:
                if field not in data.columns:
                    raise DataError(f"数据中缺少所需字段: {field}")
            
            # 按证券分组计算PB
            result = pd.DataFrame()
            
            for code, group in data.groupby('ts_code'):
                # 排序，确保数据按日期有序
                group = group.sort_values(date_col)
                
                # 计算净资产
                net_assets = group['total_assets'] - group['total_liab']
                
                # 计算每股净资产
                bps = net_assets / group['total_share']
                
                # 计算PB值
                # 处理bps为0或负数的情况
                pb = pd.Series(np.nan, index=group.index)
                valid_bps = bps > 0
                pb[valid_bps] = group.loc[valid_bps, 'close'] / bps[valid_bps]
                
                # 创建结果DataFrame
                df = pd.DataFrame({
                    'ts_code': code,
                    date_col: group[date_col],
                    self.name: pb
                })
                
                # 添加到结果集
                result = pd.concat([result, df], ignore_index=True)
            
            # 筛选目标日期范围
            result = result[
                (result[date_col] >= pd.to_datetime(start_date)) & 
                (result[date_col] <= pd.to_datetime(end_date))
            ]
            
            # 设置索引
            result = result.set_index([date_col, 'ts_code']).reset_index()
            
            return result
            
        except Exception as e:
            if isinstance(e, (ComputeError, DataError)):
                raise
            
            self.logger.error(f"计算PB失败: {str(e)}")
            raise ComputeError(f"计算PB失败: {str(e)}")
    
    def get_required_parameters(self) -> List[str]:
        """
        获取必需的计算参数列表
        
        Returns:
            List[str]: 必需参数列表
        """
        return ['data', 'start_date', 'end_date']

class PSFactor(BaseFactor):
    """
    市销率（Price-to-Sales Ratio）因子
    
    计算方法：
    市销率 = 当前股价 / 每股销售额
    
    每股销售额 = 营业收入 / 总股本
    
    用途：
    1. 评估公司的销售能力和定价能力
    2. 对于尚未盈利但有强劲收入的公司特别有用
    3. 不同行业间的估值比较
    """
    
    # 支持的PS类型
    VALID_PS_TYPES = {'ttm', 'static', 'forward'}
    
    def __init__(self, 
                ps_type: str = "ttm",  # ttm, static, forward
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化PS因子
        
        Args:
            ps_type: PS类型，可选值：
                - ttm: 滚动市销率（过去12个月）
                - static: 静态市销率（最近年报）
                - forward: 动态市销率（预测值）
            name: 因子名称，默认为f"ps_{ps_type}"
            description: 因子描述
            cache_dir: 缓存目录
        """
        ps_type = ps_type.lower()
        if ps_type not in self.VALID_PS_TYPES:
            raise ValueError(f"不支持的PS类型: {ps_type}，支持的类型包括: {', '.join(self.VALID_PS_TYPES)}")
            
        if name is None:
            name = f"ps_{ps_type}"
            
        if not description:
            description = f"市销率因子 (类型: {ps_type})"
            
        super().__init__(name, description, "fundamental", cache_dir)
        
        self.ps_type = ps_type
        self.name = name  # 明确存储名称为实例变量
        
        # 更新元数据
        self._metadata.update({
            'ps_type': ps_type
        })
        
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        实现PS因子的计算逻辑
        
        Args:
            **kwargs: 计算参数
                data: 输入数据DataFrame，必须包含价格和营业收入数据
                start_date: 开始日期
                end_date: 结束日期
                
        Returns:
            pd.DataFrame: 因子计算结果，包含PS值
        """
        # 获取参数
        data = kwargs.get('data')
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        
        if data is None:
            raise ComputeError("必须提供数据")
        
        if not start_date or not end_date:
            raise ComputeError("必须提供开始日期和结束日期")
        
        try:
            # 确保数据不为空
            if data.empty:
                raise DataError("输入数据为空")
            
            # 确保日期格式正确
            date_col = 'trade_date' if 'trade_date' in data.columns else 'date'
            if not pd.api.types.is_datetime64_dtype(data[date_col]):
                data[date_col] = pd.to_datetime(data[date_col])
            
            # 确保必要的字段存在
            required_fields = ['close', 'total_share']  # 股价数据和总股本
            
            # 根据PS类型选择不同的收入字段
            if self.ps_type == 'ttm':
                required_fields.append('revenue_ttm')  # 滚动收入
            elif self.ps_type == 'static':
                required_fields.append('revenue')  # 年度收入
            elif self.ps_type == 'forward':
                required_fields.append('forecast_revenue')  # 预测收入
            
            for field in required_fields:
                if field not in data.columns:
                    raise DataError(f"数据中缺少所需字段: {field}")
            
            # 按证券分组计算PS
            result = pd.DataFrame()
            
            for code, group in data.groupby('ts_code'):
                # 排序，确保数据按日期有序
                group = group.sort_values(date_col)
                
                # 计算每股销售额
                if self.ps_type == 'ttm':
                    sales_per_share = group['revenue_ttm'] / group['total_share']
                elif self.ps_type == 'static':
                    sales_per_share = group['revenue'] / group['total_share']
                else:  # forward
                    sales_per_share = group['forecast_revenue'] / group['total_share']
                
                # 计算PS值
                # 处理sales_per_share为0或负数的情况
                ps = pd.Series(np.nan, index=group.index)
                valid_sales = sales_per_share > 0
                ps[valid_sales] = group.loc[valid_sales, 'close'] / sales_per_share[valid_sales]
                
                # 创建结果DataFrame
                df = pd.DataFrame({
                    'ts_code': code,
                    date_col: group[date_col],
                    self.name: ps
                })
                
                # 添加到结果集
                result = pd.concat([result, df], ignore_index=True)
            
            # 筛选目标日期范围
            result = result[
                (result[date_col] >= pd.to_datetime(start_date)) & 
                (result[date_col] <= pd.to_datetime(end_date))
            ]
            
            # 设置索引
            result = result.set_index([date_col, 'ts_code']).reset_index()
            
            return result
            
        except Exception as e:
            if isinstance(e, (ComputeError, DataError)):
                raise
            
            self.logger.error(f"计算PS失败: {str(e)}")
            raise ComputeError(f"计算PS失败: {str(e)}")
    
    def get_required_parameters(self) -> List[str]:
        """
        获取必需的计算参数列表
        
        Returns:
            List[str]: 必需参数列表
        """
        return ['data', 'start_date', 'end_date']

class DividendYieldFactor(BaseFactor):
    """
    股息率（Dividend Yield）因子
    
    计算方法：
    股息率 = 年度股息 / 当前股价
    
    用途：
    1. 评估公司的分红能力和股东回报
    2. 识别稳定的收入型投资机会
    3. 评估公司的分红政策和可持续性
    """
    
    def __init__(self, 
                avg_period: int = 3,  # 使用多少年的平均股息
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化股息率因子
        
        Args:
            avg_period: 用于计算平均股息的年数，默认为3年
            name: 因子名称，默认为f"div_yield_{avg_period}y"
            description: 因子描述
            cache_dir: 缓存目录
        """
        if avg_period <= 0:
            raise ValueError(f"平均周期必须大于0: {avg_period}")
            
        if name is None:
            name = f"div_yield_{avg_period}y"
            
        if not description:
            description = f"股息率因子 (平均{avg_period}年)"
            
        super().__init__(name, description, "fundamental", cache_dir)
        
        self.avg_period = avg_period
        self.name = name  # 明确存储名称为实例变量
        
        # 更新元数据
        self._metadata.update({
            'avg_period': avg_period
        })
        
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        实现股息率因子的计算逻辑
        
        Args:
            **kwargs: 计算参数
                data: 输入数据DataFrame，必须包含价格和股息数据
                start_date: 开始日期
                end_date: 结束日期
                
        Returns:
            pd.DataFrame: 因子计算结果，包含股息率值
        """
        # 获取参数
        data = kwargs.get('data')
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        
        if data is None:
            raise ComputeError("必须提供数据")
        
        if not start_date or not end_date:
            raise ComputeError("必须提供开始日期和结束日期")
        
        try:
            # 确保数据不为空
            if data.empty:
                raise DataError("输入数据为空")
            
            # 确保日期格式正确
            date_col = 'trade_date' if 'trade_date' in data.columns else 'date'
            if not pd.api.types.is_datetime64_dtype(data[date_col]):
                data[date_col] = pd.to_datetime(data[date_col])
            
            # 确保必要的字段存在
            required_fields = ['close', 'div_per_share']
            for field in required_fields:
                if field not in data.columns:
                    raise DataError(f"数据中缺少所需字段: {field}")
            
            # 按证券分组计算股息率
            result = pd.DataFrame()
            
            for code, group in data.groupby('ts_code'):
                # 排序，确保数据按日期有序
                group = group.sort_values(date_col)
                
                # 计算平均股息
                # 对于每个日期，计算过去avg_period年的平均股息
                # 使用滚动窗口计算
                if self.avg_period > 1:
                    # 计算过去几年的平均股息
                    # 注意：这里简化处理，假设数据已经按年组织好
                    # 在实际应用中，可能需要更复杂的逻辑来处理不同频率的数据
                    avg_div = group['div_per_share'].rolling(window=self.avg_period, min_periods=1).mean()
                else:
                    avg_div = group['div_per_share']
                
                # 计算股息率
                div_yield = avg_div / group['close'] * 100  # 转换为百分比
                
                # 创建结果DataFrame
                df = pd.DataFrame({
                    'ts_code': code,
                    date_col: group[date_col],
                    self.name: div_yield
                })
                
                # 添加到结果集
                result = pd.concat([result, df], ignore_index=True)
            
            # 筛选目标日期范围
            result = result[
                (result[date_col] >= pd.to_datetime(start_date)) & 
                (result[date_col] <= pd.to_datetime(end_date))
            ]
            
            # 设置索引
            result = result.set_index([date_col, 'ts_code']).reset_index()
            
            return result
            
        except Exception as e:
            if isinstance(e, (ComputeError, DataError)):
                raise
            
            self.logger.error(f"计算股息率失败: {str(e)}")
            raise ComputeError(f"计算股息率失败: {str(e)}")
    
    def get_required_parameters(self) -> List[str]:
        """
        获取必需的计算参数列表
        
        Returns:
            List[str]: 必需参数列表
        """
        return ['data', 'start_date', 'end_date'] 