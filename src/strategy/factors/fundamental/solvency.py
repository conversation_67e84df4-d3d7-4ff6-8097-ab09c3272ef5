"""
偿债能力因子模块
- 实现资产负债率（Debt-to-Assets Ratio）因子
- 实现流动比率（Current Ratio）因子
- 实现速动比率（Quick Ratio）因子
- 实现利息覆盖率（Interest Coverage Ratio）因子
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union, Optional, Tuple, Set
from datetime import datetime, date

from data.storage.storage_factory import StorageFactory
from ..base_factor import BaseFactor, ComputeError, DataError

class DebtToAssetsRatioFactor(BaseFactor):
    """
    资产负债率（Debt-to-Assets Ratio）因子
    
    计算方法：
    资产负债率 = 总负债 / 总资产
    
    参数：
    - period: 周期，支持 "annual"（年度）, "quarterly"（季度）
    - method: 计算方法，支持 "latest"（最新）, "avg"（平均）
    - window: 对于avg方法，表示计算几期的平均值
    
    用途：
    1. 衡量公司偿债能力和财务风险
    2. 评估公司财务杠杆水平
    3. 较高的资产负债率通常意味着较高的财务风险
    """
    
    # 支持的周期类型
    VALID_PERIOD_TYPES = {'annual', 'quarterly'}
    
    # 支持的计算方法
    VALID_METHODS = {'latest', 'avg'}
    
    def __init__(self, 
                period: str = "annual",    # annual, quarterly
                method: str = "latest",    # latest, avg
                window: int = 4,           # 对于avg方法，计算几期的平均值
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化资产负债率因子
        
        Args:
            period: 周期，支持"annual"（年度）, "quarterly"（季度）
            method: 计算方法，支持"latest"（最新）, "avg"（平均）
            window: 对于avg方法，表示计算几期的平均值
            name: 因子名称，默认为"debt_to_assets_{period}_{method}"
            description: 因子描述
            cache_dir: 缓存目录
        """
        period = period.lower()
        method = method.lower()
        
        if period not in self.VALID_PERIOD_TYPES:
            raise ValueError(f"不支持的周期类型: {period}，支持的类型包括: {', '.join(self.VALID_PERIOD_TYPES)}")
            
        if method not in self.VALID_METHODS:
            raise ValueError(f"不支持的计算方法: {method}，支持的方法包括: {', '.join(self.VALID_METHODS)}")
            
        if window < 1:
            raise ValueError(f"窗口大小必须大于等于1: {window}")
            
        if name is None:
            name = f"debt_to_assets_{period}_{method}"
            if method == "avg":
                name = f"{name}_{window}"
                
        self.period = period
        self.method = method
        self.window = window
        
        super().__init__(name=name, description=description, cache_dir=cache_dir)
        
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        计算资产负债率因子
        
        Args:
            **kwargs: 计算参数，包括：
                start_date: 开始日期
                end_date: 结束日期
                securities: 证券列表
                
        Returns:
            pd.DataFrame: 资产负债率因子值，索引为(date, security_id)，列为因子值
            
        Raises:
            ComputeError: 计算失败时抛出
            DataError: 数据错误时抛出
        """
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        securities = kwargs.get('securities')
        
        if start_date is None or end_date is None:
            raise ValueError("start_date和end_date不能为空")
            
        try:
            # 获取存储接口
            storage = StorageFactory.create_storage('sqlite')
            
            # 获取资产负债表数据
            if self.period == 'annual':
                table_name = 'financial_balance_sheet_annual'
            else:  # quarterly
                table_name = 'financial_balance_sheet_quarterly'
                
            # 查询字段：总资产和总负债
            query = f"""
            SELECT
                report_date,
                security_id,
                total_assets,
                total_liabilities
            FROM {table_name}
            WHERE report_date BETWEEN ? AND ?
            """
            
            # 添加证券过滤条件
            if securities is not None and len(securities) > 0:
                placeholders = ','.join(['?' for _ in securities])
                query += f" AND security_id IN ({placeholders})"
                params = [start_date, end_date] + securities
            else:
                params = [start_date, end_date]
                
            # 执行查询
            df = storage.execute_query(query, params)
            
            if df.empty:
                raise DataError(f"未找到资产负债表数据：表={table_name}, 时间范围={start_date}至{end_date}")
                
            # 计算资产负债率
            df['debt_to_assets_ratio'] = df['total_liabilities'] / df['total_assets']
            
            # 替换无效值
            df['debt_to_assets_ratio'].replace([np.inf, -np.inf], np.nan, inplace=True)
            
            # 根据计算方法处理数据
            if self.method == 'latest':
                # 获取每个证券最新的数据
                result = df.sort_values('report_date', ascending=False)
                result = result.drop_duplicates('security_id')
                # 创建多级索引结果
                result = result.set_index(['report_date', 'security_id'])[['debt_to_assets_ratio']]
            else:  # avg
                # 计算平均值
                # 对每个证券分组，取最近window期数据计算平均值
                latest_dates = df.groupby('security_id')['report_date'].nlargest(self.window).reset_index()
                latest_dates = latest_dates.set_index(['security_id', 'level_1'])
                latest_dates = latest_dates['report_date']
                
                # 筛选最近window期的数据
                multi_index = pd.MultiIndex.from_frame(df[['security_id', 'report_date']])
                mask = multi_index.isin(latest_dates.reset_index()[['security_id', 'report_date']].values)
                filtered_df = df.iloc[mask]
                
                # 计算平均值
                result = filtered_df.groupby('security_id')['debt_to_assets_ratio'].mean().reset_index()
                # 添加report_date列，使用end_date作为统一日期
                result['report_date'] = end_date
                # 创建多级索引结果
                result = result.set_index(['report_date', 'security_id'])[['debt_to_assets_ratio']]
                
            return result
        
        except Exception as e:
            raise ComputeError(f"计算资产负债率因子失败: {str(e)}")
            
    def get_required_parameters(self) -> List[str]:
        """
        获取计算因子所需的参数列表
        
        Returns:
            List[str]: 参数名称列表
        """
        return ['start_date', 'end_date', 'securities']

class CurrentRatioFactor(BaseFactor):
    """
    流动比率（Current Ratio）因子
    
    计算方法：
    流动比率 = 流动资产 / 流动负债
    
    参数：
    - period: 周期，支持 "annual"（年度）, "quarterly"（季度）
    - method: 计算方法，支持 "latest"（最新）, "avg"（平均）
    - window: 对于avg方法，表示计算几期的平均值
    
    用途：
    1. 衡量公司短期偿债能力
    2. 评估公司资产的流动性
    3. 流动比率通常应大于1，表示流动资产可以覆盖流动负债
    """
    
    # 支持的周期类型
    VALID_PERIOD_TYPES = {'annual', 'quarterly'}
    
    # 支持的计算方法
    VALID_METHODS = {'latest', 'avg'}
    
    def __init__(self, 
                period: str = "annual",    # annual, quarterly
                method: str = "latest",    # latest, avg
                window: int = 4,           # 对于avg方法，计算几期的平均值
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化流动比率因子
        
        Args:
            period: 周期，支持"annual"（年度）, "quarterly"（季度）
            method: 计算方法，支持"latest"（最新）, "avg"（平均）
            window: 对于avg方法，表示计算几期的平均值
            name: 因子名称，默认为"current_ratio_{period}_{method}"
            description: 因子描述
            cache_dir: 缓存目录
        """
        period = period.lower()
        method = method.lower()
        
        if period not in self.VALID_PERIOD_TYPES:
            raise ValueError(f"不支持的周期类型: {period}，支持的类型包括: {', '.join(self.VALID_PERIOD_TYPES)}")
            
        if method not in self.VALID_METHODS:
            raise ValueError(f"不支持的计算方法: {method}，支持的方法包括: {', '.join(self.VALID_METHODS)}")
            
        if window < 1:
            raise ValueError(f"窗口大小必须大于等于1: {window}")
            
        if name is None:
            name = f"current_ratio_{period}_{method}"
            if method == "avg":
                name = f"{name}_{window}"
                
        self.period = period
        self.method = method
        self.window = window
        
        super().__init__(name=name, description=description, cache_dir=cache_dir)
        
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        计算流动比率因子
        
        Args:
            **kwargs: 计算参数，包括：
                start_date: 开始日期
                end_date: 结束日期
                securities: 证券列表
                
        Returns:
            pd.DataFrame: 流动比率因子值，索引为(date, security_id)，列为因子值
            
        Raises:
            ComputeError: 计算失败时抛出
            DataError: 数据错误时抛出
        """
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        securities = kwargs.get('securities')
        
        if start_date is None or end_date is None:
            raise ValueError("start_date和end_date不能为空")
            
        try:
            # 获取存储接口
            storage = StorageFactory.create_storage('sqlite')
            
            # 获取资产负债表数据
            if self.period == 'annual':
                table_name = 'financial_balance_sheet_annual'
            else:  # quarterly
                table_name = 'financial_balance_sheet_quarterly'
                
            # 查询字段：流动资产和流动负债
            query = f"""
            SELECT
                report_date,
                security_id,
                current_assets,
                current_liabilities
            FROM {table_name}
            WHERE report_date BETWEEN ? AND ?
            """
            
            # 添加证券过滤条件
            if securities is not None and len(securities) > 0:
                placeholders = ','.join(['?' for _ in securities])
                query += f" AND security_id IN ({placeholders})"
                params = [start_date, end_date] + securities
            else:
                params = [start_date, end_date]
                
            # 执行查询
            df = storage.execute_query(query, params)
            
            if df.empty:
                raise DataError(f"未找到资产负债表数据：表={table_name}, 时间范围={start_date}至{end_date}")
                
            # 计算流动比率
            df['current_ratio'] = df['current_assets'] / df['current_liabilities']
            
            # 替换无效值
            df['current_ratio'].replace([np.inf, -np.inf], np.nan, inplace=True)
            
            # 根据计算方法处理数据
            if self.method == 'latest':
                # 获取每个证券最新的数据
                result = df.sort_values('report_date', ascending=False)
                result = result.drop_duplicates('security_id')
                # 创建多级索引结果
                result = result.set_index(['report_date', 'security_id'])[['current_ratio']]
            else:  # avg
                # 计算平均值
                # 对每个证券分组，取最近window期数据计算平均值
                latest_dates = df.groupby('security_id')['report_date'].nlargest(self.window).reset_index()
                latest_dates = latest_dates.set_index(['security_id', 'level_1'])
                latest_dates = latest_dates['report_date']
                
                # 筛选最近window期的数据
                multi_index = pd.MultiIndex.from_frame(df[['security_id', 'report_date']])
                mask = multi_index.isin(latest_dates.reset_index()[['security_id', 'report_date']].values)
                filtered_df = df.iloc[mask]
                
                # 计算平均值
                result = filtered_df.groupby('security_id')['current_ratio'].mean().reset_index()
                # 添加report_date列，使用end_date作为统一日期
                result['report_date'] = end_date
                # 创建多级索引结果
                result = result.set_index(['report_date', 'security_id'])[['current_ratio']]
                
            return result
        
        except Exception as e:
            raise ComputeError(f"计算流动比率因子失败: {str(e)}")
            
    def get_required_parameters(self) -> List[str]:
        """
        获取计算因子所需的参数列表
        
        Returns:
            List[str]: 参数名称列表
        """
        return ['start_date', 'end_date', 'securities']

class QuickRatioFactor(BaseFactor):
    """
    速动比率（Quick Ratio）因子
    
    计算方法：
    速动比率 = (流动资产 - 存货) / 流动负债
    
    参数：
    - period: 周期，支持 "annual"（年度）, "quarterly"（季度）
    - method: 计算方法，支持 "latest"（最新）, "avg"（平均）
    - window: 对于avg方法，表示计算几期的平均值
    
    用途：
    1. 衡量公司的即期偿债能力
    2. 评估公司在不依赖存货的情况下偿还短期债务的能力
    3. 速动比率通常应接近或大于1，表示公司可以在不依赖存货的情况下覆盖流动负债
    """
    
    # 支持的周期类型
    VALID_PERIOD_TYPES = {'annual', 'quarterly'}
    
    # 支持的计算方法
    VALID_METHODS = {'latest', 'avg'}
    
    def __init__(self, 
                period: str = "annual",    # annual, quarterly
                method: str = "latest",    # latest, avg
                window: int = 4,           # 对于avg方法，计算几期的平均值
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化速动比率因子
        
        Args:
            period: 周期，支持"annual"（年度）, "quarterly"（季度）
            method: 计算方法，支持"latest"（最新）, "avg"（平均）
            window: 对于avg方法，表示计算几期的平均值
            name: 因子名称，默认为"quick_ratio_{period}_{method}"
            description: 因子描述
            cache_dir: 缓存目录
        """
        period = period.lower()
        method = method.lower()
        
        if period not in self.VALID_PERIOD_TYPES:
            raise ValueError(f"不支持的周期类型: {period}，支持的类型包括: {', '.join(self.VALID_PERIOD_TYPES)}")
            
        if method not in self.VALID_METHODS:
            raise ValueError(f"不支持的计算方法: {method}，支持的方法包括: {', '.join(self.VALID_METHODS)}")
            
        if window < 1:
            raise ValueError(f"窗口大小必须大于等于1: {window}")
            
        if name is None:
            name = f"quick_ratio_{period}_{method}"
            if method == "avg":
                name = f"{name}_{window}"
                
        self.period = period
        self.method = method
        self.window = window
        
        super().__init__(name=name, description=description, cache_dir=cache_dir)
        
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        计算速动比率因子
        
        Args:
            **kwargs: 计算参数，包括：
                start_date: 开始日期
                end_date: 结束日期
                securities: 证券列表
                
        Returns:
            pd.DataFrame: 速动比率因子值，索引为(date, security_id)，列为因子值
            
        Raises:
            ComputeError: 计算失败时抛出
            DataError: 数据错误时抛出
        """
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        securities = kwargs.get('securities')
        
        if start_date is None or end_date is None:
            raise ValueError("start_date和end_date不能为空")
            
        try:
            # 获取存储接口
            storage = StorageFactory.create_storage('sqlite')
            
            # 获取资产负债表数据
            if self.period == 'annual':
                table_name = 'financial_balance_sheet_annual'
            else:  # quarterly
                table_name = 'financial_balance_sheet_quarterly'
                
            # 查询字段：流动资产、存货和流动负债
            query = f"""
            SELECT
                report_date,
                security_id,
                current_assets,
                inventories,
                current_liabilities
            FROM {table_name}
            WHERE report_date BETWEEN ? AND ?
            """
            
            # 添加证券过滤条件
            if securities is not None and len(securities) > 0:
                placeholders = ','.join(['?' for _ in securities])
                query += f" AND security_id IN ({placeholders})"
                params = [start_date, end_date] + securities
            else:
                params = [start_date, end_date]
                
            # 执行查询
            df = storage.execute_query(query, params)
            
            if df.empty:
                raise DataError(f"未找到资产负债表数据：表={table_name}, 时间范围={start_date}至{end_date}")
                
            # 计算速动比率
            df['quick_ratio'] = (df['current_assets'] - df['inventories']) / df['current_liabilities']
            
            # 替换无效值
            df['quick_ratio'].replace([np.inf, -np.inf], np.nan, inplace=True)
            
            # 根据计算方法处理数据
            if self.method == 'latest':
                # 获取每个证券最新的数据
                result = df.sort_values('report_date', ascending=False)
                result = result.drop_duplicates('security_id')
                # 创建多级索引结果
                result = result.set_index(['report_date', 'security_id'])[['quick_ratio']]
            else:  # avg
                # 计算平均值
                # 对每个证券分组，取最近window期数据计算平均值
                latest_dates = df.groupby('security_id')['report_date'].nlargest(self.window).reset_index()
                latest_dates = latest_dates.set_index(['security_id', 'level_1'])
                latest_dates = latest_dates['report_date']
                
                # 筛选最近window期的数据
                multi_index = pd.MultiIndex.from_frame(df[['security_id', 'report_date']])
                mask = multi_index.isin(latest_dates.reset_index()[['security_id', 'report_date']].values)
                filtered_df = df.iloc[mask]
                
                # 计算平均值
                result = filtered_df.groupby('security_id')['quick_ratio'].mean().reset_index()
                # 添加report_date列，使用end_date作为统一日期
                result['report_date'] = end_date
                # 创建多级索引结果
                result = result.set_index(['report_date', 'security_id'])[['quick_ratio']]
                
            return result
        
        except Exception as e:
            raise ComputeError(f"计算速动比率因子失败: {str(e)}")
            
    def get_required_parameters(self) -> List[str]:
        """
        获取计算因子所需的参数列表
        
        Returns:
            List[str]: 参数名称列表
        """
        return ['start_date', 'end_date', 'securities']

class InterestCoverageRatioFactor(BaseFactor):
    """
    利息覆盖率（Interest Coverage Ratio）因子
    
    计算方法：
    利息覆盖率 = 息税前利润(EBIT) / 利息费用
    
    参数：
    - period: 周期，支持 "annual"（年度）, "quarterly"（季度）, "ttm"（滚动12个月）
    - method: 计算方法，支持 "latest"（最新）, "avg"（平均）
    - window: 对于avg方法，表示计算几期的平均值
    
    用途：
    1. 衡量公司偿还利息的能力
    2. 评估公司的债务风险水平
    3. 利息覆盖率越高，表示公司偿还利息的能力越强
    """
    
    # 支持的周期类型
    VALID_PERIOD_TYPES = {'annual', 'quarterly', 'ttm'}
    
    # 支持的计算方法
    VALID_METHODS = {'latest', 'avg'}
    
    def __init__(self, 
                period: str = "ttm",      # annual, quarterly, ttm
                method: str = "latest",    # latest, avg
                window: int = 4,           # 对于avg方法，计算几期的平均值
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化利息覆盖率因子
        
        Args:
            period: 周期，支持"annual"（年度）, "quarterly"（季度）, "ttm"（滚动12个月）
            method: 计算方法，支持"latest"（最新）, "avg"（平均）
            window: 对于avg方法，表示计算几期的平均值
            name: 因子名称，默认为"interest_coverage_{period}_{method}"
            description: 因子描述
            cache_dir: 缓存目录
        """
        period = period.lower()
        method = method.lower()
        
        if period not in self.VALID_PERIOD_TYPES:
            raise ValueError(f"不支持的周期类型: {period}，支持的类型包括: {', '.join(self.VALID_PERIOD_TYPES)}")
            
        if method not in self.VALID_METHODS:
            raise ValueError(f"不支持的计算方法: {method}，支持的方法包括: {', '.join(self.VALID_METHODS)}")
            
        if window < 1:
            raise ValueError(f"窗口大小必须大于等于1: {window}")
            
        if name is None:
            name = f"interest_coverage_{period}_{method}"
            if method == "avg":
                name = f"{name}_{window}"
                
        self.period = period
        self.method = method
        self.window = window
        
        super().__init__(name=name, description=description, cache_dir=cache_dir)
        
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        计算利息覆盖率因子
        
        Args:
            **kwargs: 计算参数，包括：
                start_date: 开始日期
                end_date: 结束日期
                securities: 证券列表
                
        Returns:
            pd.DataFrame: 利息覆盖率因子值，索引为(date, security_id)，列为因子值
            
        Raises:
            ComputeError: 计算失败时抛出
            DataError: 数据错误时抛出
        """
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        securities = kwargs.get('securities')
        
        if start_date is None or end_date is None:
            raise ValueError("start_date和end_date不能为空")
            
        try:
            # 获取存储接口
            storage = StorageFactory.create_storage('sqlite')
            
            # 获取利润表数据
            if self.period == 'annual':
                table_name = 'financial_income_statement_annual'
                ttm_flag = False
            elif self.period == 'quarterly':
                table_name = 'financial_income_statement_quarterly'
                ttm_flag = False
            else:  # ttm
                table_name = 'financial_income_statement_quarterly'
                ttm_flag = True
                
            # 查询字段：营业利润、营业外收入、营业外支出、利息费用
            query = f"""
            SELECT
                report_date,
                security_id,
                operating_profit,
                non_operating_income,
                non_operating_expense,
                financial_expense,
                interest_expense
            FROM {table_name}
            WHERE report_date BETWEEN ? AND ?
            """
            
            # 添加证券过滤条件
            if securities is not None and len(securities) > 0:
                placeholders = ','.join(['?' for _ in securities])
                query += f" AND security_id IN ({placeholders})"
                params = [start_date, end_date] + securities
            else:
                params = [start_date, end_date]
                
            # 执行查询
            df = storage.execute_query(query, params)
            
            if df.empty:
                raise DataError(f"未找到利润表数据：表={table_name}, 时间范围={start_date}至{end_date}")
                
            # 计算EBIT（息税前利润）
            df['ebit'] = df['operating_profit'] + df['non_operating_income'] - df['non_operating_expense']
            
            # 对于没有直接提供利息费用的情况，使用财务费用作为近似
            df['interest'] = df['interest_expense'].fillna(df['financial_expense'])
            
            # 确保利息费用不为零
            epsilon = 1e-10  # 避免除以零
            df['interest'] = df['interest'].apply(lambda x: max(x, epsilon))
            
            # 计算利息覆盖率
            df['interest_coverage_ratio'] = df['ebit'] / df['interest']
            
            # 替换无效值
            df['interest_coverage_ratio'].replace([np.inf, -np.inf], np.nan, inplace=True)
            
            # 如果是TTM数据，需要计算滚动四个季度
            if ttm_flag:
                # 确保数据按日期排序
                df = df.sort_values(['security_id', 'report_date'])
                
                # 计算TTM值
                ttm_result = []
                for security, group in df.groupby('security_id'):
                    if len(group) >= 4:
                        # 对每个证券，按时间倒序排列
                        group = group.sort_values('report_date', ascending=False)
                        
                        # 取最近4个季度的数据
                        recent_quarters = group.head(4)
                        
                        # 计算TTM的EBIT和利息费用
                        ttm_ebit = recent_quarters['ebit'].sum()
                        ttm_interest = recent_quarters['interest'].sum()
                        
                        # 计算TTM利息覆盖率
                        ttm_coverage = ttm_ebit / ttm_interest
                        
                        # 将结果添加到列表
                        ttm_result.append({
                            'report_date': recent_quarters['report_date'].iloc[0],
                            'security_id': security,
                            'interest_coverage_ratio': ttm_coverage
                        })
                
                if not ttm_result:
                    raise DataError("无法计算TTM利息覆盖率，数据不足")
                    
                # 转换为DataFrame
                df = pd.DataFrame(ttm_result)
            
            # 根据计算方法处理数据
            if self.method == 'latest':
                # 获取每个证券最新的数据
                result = df.sort_values('report_date', ascending=False)
                result = result.drop_duplicates('security_id')
                # 创建多级索引结果
                result = result.set_index(['report_date', 'security_id'])[['interest_coverage_ratio']]
            else:  # avg
                # 计算平均值
                # 对每个证券分组，取最近window期数据计算平均值
                latest_dates = df.groupby('security_id')['report_date'].nlargest(self.window).reset_index()
                latest_dates = latest_dates.set_index(['security_id', 'level_1'])
                latest_dates = latest_dates['report_date']
                
                # 筛选最近window期的数据
                multi_index = pd.MultiIndex.from_frame(df[['security_id', 'report_date']])
                mask = multi_index.isin(latest_dates.reset_index()[['security_id', 'report_date']].values)
                filtered_df = df.iloc[mask]
                
                # 计算平均值
                result = filtered_df.groupby('security_id')['interest_coverage_ratio'].mean().reset_index()
                # 添加report_date列，使用end_date作为统一日期
                result['report_date'] = end_date
                # 创建多级索引结果
                result = result.set_index(['report_date', 'security_id'])[['interest_coverage_ratio']]
                
            return result
        
        except Exception as e:
            raise ComputeError(f"计算利息覆盖率因子失败: {str(e)}")
            
    def get_required_parameters(self) -> List[str]:
        """
        获取计算因子所需的参数列表
        
        Returns:
            List[str]: 参数名称列表
        """
        return ['start_date', 'end_date', 'securities'] 