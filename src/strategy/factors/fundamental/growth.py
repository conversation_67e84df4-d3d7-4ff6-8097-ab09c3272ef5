"""
成长性因子模块
- 实现营收增长率（Revenue Growth）因子
- 实现净利润增长率（Net Profit Growth）因子
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union, Optional, Tuple, Set
from datetime import datetime, date

# {{ AURA-X: Modify - 修复导入路径，使用绝对导入. Approval: 寸止(ID:最终架构验证修复). }}
from src.data.storage.storage_factory import StorageFactory
# {{ AURA-X: Modify - 修复导入路径，使用绝对导入. Approval: 寸止(ID:最终架构验证修复). }}
from src.strategy.factors.base_factor import BaseFactor, ComputeError, DataError

class RevenueGrowthFactor(BaseFactor):
    """
    营收增长率因子
    
    计算方法：
    营收增长率 = (当期营收 - 基期营收) / 基期营收
    
    参数：
    - growth_type: 增长类型，支持 "yoy"（同比）, "qoq"（环比）, "cagr"（复合年增长率）
    - period: 周期，支持 "annual"（年度）, "quarterly"（季度）
    - window: 对于cagr，表示计算几年的复合增长率
    
    用途：
    1. 评估公司收入增长趋势
    2. 反映公司业务扩张速度
    3. 高营收增长通常表明公司处于扩张期
    """
    
    # 支持的增长类型
    VALID_GROWTH_TYPES = {'yoy', 'qoq', 'cagr'}
    
    # 支持的周期类型
    VALID_PERIOD_TYPES = {'annual', 'quarterly'}
    
    def __init__(self, 
                growth_type: str = "yoy",  # yoy, qoq, cagr
                period: str = "annual",    # annual, quarterly
                window: int = 3,           # 对于cagr类型，计算几年的复合增长率
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化营收增长率因子
        
        Args:
            growth_type: 增长类型，支持"yoy"（同比）, "qoq"（环比）, "cagr"（复合年增长率）
            period: 周期，支持"annual"（年度）, "quarterly"（季度）
            window: 对于cagr，表示计算几年的复合增长率
            name: 因子名称，如果为None则自动生成
            description: 因子描述
            cache_dir: 缓存目录
        """
        # 参数验证
        if growth_type not in self.VALID_GROWTH_TYPES:
            raise ValueError(f"不支持的增长类型: {growth_type}，支持的类型为: {self.VALID_GROWTH_TYPES}")
            
        if period not in self.VALID_PERIOD_TYPES:
            raise ValueError(f"不支持的周期类型: {period}，支持的类型为: {self.VALID_PERIOD_TYPES}")
            
        if window <= 0:
            raise ValueError(f"window参数必须大于0，当前值为: {window}")
        
        self.growth_type = growth_type
        self.period = period
        self.window = window
        
        # 自动生成名称
        if name is None:
            name = f"revenue_growth_{growth_type}"
            if growth_type == "cagr":
                name = f"{name}_{window}y"
        
        # 保存名称为对象属性
        self.name = name
            
        # 自动生成描述
        if not description:
            description_map = {
                'yoy': '同比营收增长率',
                'qoq': '环比营收增长率',
                'cagr': f'{window}年营收复合增长率'
            }
            description = description_map[growth_type]
        
        super().__init__(name=name, description=description, category="fundamental", cache_dir=cache_dir)
    
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        计算营收增长率
        
        Args:
            data: 包含财务数据的DataFrame，必须包含以下列：
                - trade_date: 交易日期
                - ts_code: 股票代码
                - revenue: 营业收入
                - report_type: 报告类型，如果使用period=quarterly，则必须含有此列
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            包含营收增长率的DataFrame
        """
        # 获取必要参数
        data = kwargs.get('data')
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        
        # 参数检查
        if data is None or data.empty:
            raise DataError("输入数据为空")
            
        required_fields = ['trade_date', 'ts_code', 'revenue']
        if self.period == 'quarterly':
            required_fields.append('report_type')
            
        for field in required_fields:
            if field not in data.columns:
                raise DataError(f"输入数据缺少必要的字段: {field}")
        
        # 确保数据类型正确
        data = data.copy()
        data['trade_date'] = pd.to_datetime(data['trade_date'])
        
        # 根据增长类型计算
        result_df = pd.DataFrame()
        
        if self.growth_type == 'yoy':  # 同比增长率
            result_df = self._calculate_yoy_growth(data, start_date, end_date)
        elif self.growth_type == 'qoq':  # 环比增长率
            result_df = self._calculate_qoq_growth(data, start_date, end_date)
        elif self.growth_type == 'cagr':  # 复合年增长率
            result_df = self._calculate_cagr_growth(data, start_date, end_date)
        
        return result_df
    
    def _calculate_yoy_growth(self, data: pd.DataFrame, start_date: str, end_date: str) -> pd.DataFrame:
        """计算同比增长率"""
        # 确保数据按ts_code和trade_date排序
        data = data.sort_values(['ts_code', 'trade_date'])
        
        # 筛选日期范围
        if start_date:
            data = data[data['trade_date'] >= pd.to_datetime(start_date)]
        if end_date:
            data = data[data['trade_date'] <= pd.to_datetime(end_date)]
        
        # 创建结果DataFrame，一开始只包含基础列
        result = data[['trade_date', 'ts_code']].copy()
        result[self.name] = np.nan  # 预先添加结果列，所有值设为NaN
        
        # 根据周期类型处理
        if self.period == 'annual':
            # 提取年度数据
            data['year'] = data['trade_date'].dt.year
            
            # 对每个ts_code分组处理
            groups = []
            for ts_code, group in data.groupby('ts_code'):
                # 按年份提取最新财报数据
                latest_by_year = group.sort_values('trade_date').drop_duplicates('year', keep='last')
                
                if len(latest_by_year) < 2:
                    continue
                
                # 计算同比增长率
                latest_by_year = latest_by_year.sort_values('year')
                # 计算同比增长率: (当期 - 上期) / 上期 * 100
                latest_by_year[self.name] = (
                    (latest_by_year['revenue'] - latest_by_year['revenue'].shift(1)) / 
                    latest_by_year['revenue'].shift(1) * 100
                )
                
                groups.append(latest_by_year[['trade_date', 'ts_code', self.name]])
            
            if groups:
                growth_df = pd.concat(groups)
                # 更新结果DataFrame
                result = pd.merge(result, growth_df, on=['trade_date', 'ts_code'], how='left')
        
        elif self.period == 'quarterly':
            # 处理季度数据
            data['year'] = data['trade_date'].dt.year
            data['quarter'] = data['trade_date'].dt.quarter
            
            # 对每个ts_code分组
            for ts_code, group in data.groupby('ts_code'):
                # 按报告类型分组
                for report_type, report_data in group.groupby('report_type'):
                    # 排序确保时间顺序
                    report_data = report_data.sort_values('trade_date')
                    
                    if len(report_data) < 5:  # 需要至少有5个数据点才能进行同比计算
                        continue
                    
                    # 创建一个临时DataFrame用于计算
                    temp_df = report_data.copy()
                    # 添加上一年同期的数据
                    temp_df['prev_year_date'] = temp_df['trade_date'] - pd.DateOffset(years=1)
                    temp_df['prev_year_revenue'] = np.nan
                    
                    # 为每行找到上一年同期的数据
                    for i, row in temp_df.iterrows():
                        # 找到上一年同期数据
                        prev_year_data = report_data[
                            (report_data['trade_date'] == row['prev_year_date'])
                        ]
                        if not prev_year_data.empty:
                            temp_df.at[i, 'prev_year_revenue'] = prev_year_data['revenue'].values[0]
                    
                    # 计算同比增长率
                    temp_df[self.name] = (
                        (temp_df['revenue'] - temp_df['prev_year_revenue']) / 
                        temp_df['prev_year_revenue'] * 100
                    )
                    
                    # 将结果更新到原始的result DataFrame
                    for i, row in temp_df.iterrows():
                        if pd.notna(row[self.name]):
                            result.loc[
                                (result['trade_date'] == row['trade_date']) & 
                                (result['ts_code'] == ts_code), 
                                self.name
                            ] = row[self.name]
        
        return result
    
    def _calculate_qoq_growth(self, data: pd.DataFrame, start_date: str, end_date: str) -> pd.DataFrame:
        """计算环比增长率"""
        # 确保数据按ts_code和trade_date排序
        data = data.sort_values(['ts_code', 'trade_date'])
        
        # 筛选日期范围
        if start_date:
            data = data[data['trade_date'] >= pd.to_datetime(start_date)]
        if end_date:
            data = data[data['trade_date'] <= pd.to_datetime(end_date)]
        
        # 创建结果DataFrame
        result = data[['trade_date', 'ts_code']].copy()
        result[self.name] = np.nan  # 预先添加结果列，所有值设为NaN
        
        # 仅处理季度数据，年度数据无法计算环比
        if self.period == 'quarterly':
            # 处理季度数据
            data['year'] = data['trade_date'].dt.year
            data['quarter'] = data['trade_date'].dt.quarter
            
            # 对每个ts_code分组
            for ts_code, group in data.groupby('ts_code'):
                # 按报告类型分组
                for report_type, report_data in group.groupby('report_type'):
                    # 排序确保时间顺序
                    report_data = report_data.sort_values('trade_date')
                    
                    if len(report_data) < 2:  # 需要至少有2个数据点才能计算环比
                        continue
                    
                    # 创建一个临时DataFrame用于计算
                    temp_df = report_data.copy()
                    # 添加前一季度的数据
                    temp_df['prev_quarter_revenue'] = temp_df['revenue'].shift(1)
                    
                    # 计算环比增长率
                    temp_df[self.name] = (
                        (temp_df['revenue'] - temp_df['prev_quarter_revenue']) / 
                        temp_df['prev_quarter_revenue'] * 100
                    )
                    
                    # 更新结果到原始的result DataFrame
                    for i, row in temp_df.iterrows():
                        if pd.notna(row[self.name]):
                            result.loc[
                                (result['trade_date'] == row['trade_date']) & 
                                (result['ts_code'] == ts_code), 
                                self.name
                            ] = row[self.name]
        
        return result
    
    def _calculate_cagr_growth(self, data: pd.DataFrame, start_date: str, end_date: str) -> pd.DataFrame:
        """计算复合年增长率"""
        # 确保数据按ts_code和trade_date排序
        data = data.sort_values(['ts_code', 'trade_date'])
        
        # 筛选日期范围
        if start_date:
            data = data[data['trade_date'] >= pd.to_datetime(start_date)]
        if end_date:
            data = data[data['trade_date'] <= pd.to_datetime(end_date)]
        
        # 创建结果DataFrame
        result = data[['trade_date', 'ts_code']].copy()
        result[self.name] = np.nan  # 预先添加结果列，所有值设为NaN
        
        # 添加年份列
        data['year'] = data['trade_date'].dt.year
        
        # 对每个ts_code分组
        for ts_code, group in data.groupby('ts_code'):
            # 按年份提取最新财报数据
            latest_by_year = group.sort_values('trade_date').drop_duplicates('year', keep='last')
            
            if len(latest_by_year) <= self.window:  # 需要至少比窗口多1个数据点
                continue
            
            # 排序并计算CAGR
            latest_by_year = latest_by_year.sort_values('year')
            
            # 滑动窗口计算每个时间点的CAGR
            for i in range(self.window, len(latest_by_year)):
                end_value = latest_by_year.iloc[i]['revenue']
                start_value = latest_by_year.iloc[i - self.window]['revenue']
                
                if start_value > 0 and end_value > 0:  # 避免负值或除零
                    # CAGR = (end_value / start_value)^(1/years) - 1
                    cagr = ((end_value / start_value) ** (1 / self.window) - 1) * 100
                    
                    # 更新结果
                    date = latest_by_year.iloc[i]['trade_date']
                    result.loc[
                        (result['trade_date'] == date) & 
                        (result['ts_code'] == ts_code), 
                        self.name
                    ] = cagr
        
        return result
    
    def get_required_parameters(self) -> List[str]:
        """获取计算所需的参数列表"""
        return ['data', 'start_date', 'end_date']


class ProfitGrowthFactor(BaseFactor):
    """
    净利润增长率因子
    
    计算方法：
    净利润增长率 = (当期净利润 - 基期净利润) / 基期净利润
    
    参数：
    - growth_type: 增长类型，支持 "yoy"（同比）, "qoq"（环比）, "cagr"（复合年增长率）
    - period: 周期，支持 "annual"（年度）, "quarterly"（季度）
    - window: 对于cagr，表示计算几年的复合增长率
    
    用途：
    1. 评估公司盈利能力的增长趋势
    2. 反映公司盈利模式的可持续性
    3. 高净利润增长通常表明公司业务模式具有良好的扩张性
    """
    
    # 支持的增长类型
    VALID_GROWTH_TYPES = {'yoy', 'qoq', 'cagr'}
    
    # 支持的周期类型
    VALID_PERIOD_TYPES = {'annual', 'quarterly'}
    
    def __init__(self, 
                growth_type: str = "yoy",  # yoy, qoq, cagr
                period: str = "annual",    # annual, quarterly
                window: int = 3,           # 对于cagr类型，计算几年的复合增长率
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化净利润增长率因子
        
        Args:
            growth_type: 增长类型，支持"yoy"（同比）, "qoq"（环比）, "cagr"（复合年增长率）
            period: 周期，支持"annual"（年度）, "quarterly"（季度）
            window: 对于cagr，表示计算几年的复合增长率
            name: 因子名称，如果为None则自动生成
            description: 因子描述
            cache_dir: 缓存目录
        """
        # 参数验证
        if growth_type not in self.VALID_GROWTH_TYPES:
            raise ValueError(f"不支持的增长类型: {growth_type}，支持的类型为: {self.VALID_GROWTH_TYPES}")
            
        if period not in self.VALID_PERIOD_TYPES:
            raise ValueError(f"不支持的周期类型: {period}，支持的类型为: {self.VALID_PERIOD_TYPES}")
            
        if window <= 0:
            raise ValueError(f"window参数必须大于0，当前值为: {window}")
        
        self.growth_type = growth_type
        self.period = period
        self.window = window
        
        # 自动生成名称
        if name is None:
            name = f"profit_growth_{growth_type}"
            if growth_type == "cagr":
                name = f"{name}_{window}y"
        
        # 保存名称为对象属性
        self.name = name
            
        # 自动生成描述
        if not description:
            description_map = {
                'yoy': '同比净利润增长率',
                'qoq': '环比净利润增长率',
                'cagr': f'{window}年净利润复合增长率'
            }
            description = description_map[growth_type]
        
        super().__init__(name=name, description=description, category="fundamental", cache_dir=cache_dir)
    
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        计算净利润增长率
        
        Args:
            data: 包含财务数据的DataFrame，必须包含以下列：
                - trade_date: 交易日期
                - ts_code: 股票代码
                - net_profit: 净利润
                - report_type: 报告类型，如果使用period=quarterly，则必须含有此列
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            包含净利润增长率的DataFrame
        """
        # 获取必要参数
        data = kwargs.get('data')
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        
        # 参数检查
        if data is None or data.empty:
            raise DataError("输入数据为空")
            
        required_fields = ['trade_date', 'ts_code', 'net_profit']
        if self.period == 'quarterly':
            required_fields.append('report_type')
            
        for field in required_fields:
            if field not in data.columns:
                raise DataError(f"输入数据缺少必要的字段: {field}")
        
        # 确保数据类型正确
        data = data.copy()
        data['trade_date'] = pd.to_datetime(data['trade_date'])
        
        # 根据增长类型计算
        result_df = pd.DataFrame()
        
        if self.growth_type == 'yoy':  # 同比增长率
            result_df = self._calculate_yoy_growth(data, start_date, end_date)
        elif self.growth_type == 'qoq':  # 环比增长率
            result_df = self._calculate_qoq_growth(data, start_date, end_date)
        elif self.growth_type == 'cagr':  # 复合年增长率
            result_df = self._calculate_cagr_growth(data, start_date, end_date)
        
        return result_df
    
    def _calculate_yoy_growth(self, data: pd.DataFrame, start_date: str, end_date: str) -> pd.DataFrame:
        """计算同比增长率"""
        # 确保数据按ts_code和trade_date排序
        data = data.sort_values(['ts_code', 'trade_date'])
        
        # 筛选日期范围
        if start_date:
            data = data[data['trade_date'] >= pd.to_datetime(start_date)]
        if end_date:
            data = data[data['trade_date'] <= pd.to_datetime(end_date)]
        
        # 创建结果DataFrame
        result = data[['trade_date', 'ts_code']].copy()
        result[self.name] = np.nan  # 预先添加结果列，所有值设为NaN
        
        # 根据周期类型处理
        if self.period == 'annual':
            # 提取年度数据
            data['year'] = data['trade_date'].dt.year
            
            # 对每个ts_code分组
            groups = []
            for ts_code, group in data.groupby('ts_code'):
                # 按年份提取最新财报数据
                latest_by_year = group.sort_values('trade_date').drop_duplicates('year', keep='last')
                
                if len(latest_by_year) < 2:
                    continue
                
                # 计算同比增长率
                latest_by_year = latest_by_year.sort_values('year')
                
                # 计算同比增长率，特殊处理亏损转盈利和盈利转亏损的情况
                for i in range(1, len(latest_by_year)):
                    current = latest_by_year.iloc[i]['net_profit']
                    previous = latest_by_year.iloc[i-1]['net_profit']
                    
                    # 计算增长率:
                    # 1. 两期都为正: (当期 - 上期) / 上期 * 100
                    # 2. 上期为负当期为正(亏损转盈利): 视为正增长
                    # 3. 上期为正当期为负(盈利转亏损): 视为负增长
                    # 4. 两期都为负: (|上期| - |当期|) / |上期| * 100 (亏损减少视为正增长)
                    
                    if previous > 0 and current > 0:  # 两期都为正
                        growth_rate = (current - previous) / previous * 100
                    elif previous < 0 and current > 0:  # 亏损转盈利
                        growth_rate = abs(previous - current) / abs(previous) * 100
                    elif previous > 0 and current < 0:  # 盈利转亏损
                        growth_rate = (current - previous) / previous * 100
                    elif previous < 0 and current < 0:  # 两期都为负
                        # 亏损减少视为正增长，亏损增加视为负增长
                        growth_rate = (abs(previous) - abs(current)) / abs(previous) * 100
                    else:  # previous == 0，避免除零错误
                        if current > 0:
                            growth_rate = 100  # 从零到正值，视为100%增长
                        elif current < 0:
                            growth_rate = -100  # 从零到负值，视为-100%增长
                        else:
                            growth_rate = 0  # 从零到零，无增长
                    
                    latest_by_year.iloc[i, latest_by_year.columns.get_loc(self.name)] = growth_rate
                
                groups.append(latest_by_year[['trade_date', 'ts_code', self.name]])
            
            if groups:
                growth_df = pd.concat(groups)
                # 更新结果DataFrame
                result = pd.merge(result, growth_df, on=['trade_date', 'ts_code'], how='left')
        
        elif self.period == 'quarterly':
            # 处理季度数据
            data['year'] = data['trade_date'].dt.year
            data['quarter'] = data['trade_date'].dt.quarter
            
            # 对每个ts_code分组
            for ts_code, group in data.groupby('ts_code'):
                # 按报告类型分组
                for report_type, report_data in group.groupby('report_type'):
                    # 排序确保时间顺序
                    report_data = report_data.sort_values('trade_date')
                    
                    if len(report_data) < 5:  # 需要至少有5个数据点才能进行同比计算
                        continue
                    
                    # 创建一个临时DataFrame用于计算
                    temp_df = report_data.copy()
                    # 添加上一年同期的数据
                    temp_df['prev_year_date'] = temp_df['trade_date'] - pd.DateOffset(years=1)
                    temp_df['prev_year_profit'] = np.nan
                    
                    # 为每行找到上一年同期的数据
                    for i, row in temp_df.iterrows():
                        # 找到上一年同期数据
                        prev_year_data = report_data[
                            (report_data['trade_date'] == row['prev_year_date'])
                        ]
                        if not prev_year_data.empty:
                            temp_df.at[i, 'prev_year_profit'] = prev_year_data['net_profit'].values[0]
                    
                    # 计算同比增长率，特殊处理亏损转盈利和盈利转亏损的情况
                    for i, row in temp_df.iterrows():
                        if pd.notna(row['prev_year_profit']):
                            current = row['net_profit']
                            previous = row['prev_year_profit']
                            
                            # 计算增长率，处理不同情况
                            if previous > 0 and current > 0:  # 两期都为正
                                growth_rate = (current - previous) / previous * 100
                            elif previous < 0 and current > 0:  # 亏损转盈利
                                growth_rate = abs(previous - current) / abs(previous) * 100
                            elif previous > 0 and current < 0:  # 盈利转亏损
                                growth_rate = (current - previous) / previous * 100
                            elif previous < 0 and current < 0:  # 两期都为负
                                # 亏损减少视为正增长，亏损增加视为负增长
                                growth_rate = (abs(previous) - abs(current)) / abs(previous) * 100
                            else:  # previous == 0，避免除零错误
                                if current > 0:
                                    growth_rate = 100  # 从零到正值，视为100%增长
                                elif current < 0:
                                    growth_rate = -100  # 从零到负值，视为-100%增长
                                else:
                                    growth_rate = 0  # 从零到零，无增长
                            
                            # 更新结果到结果DataFrame
                            result.loc[
                                (result['trade_date'] == row['trade_date']) & 
                                (result['ts_code'] == ts_code), 
                                self.name
                            ] = growth_rate
        
        return result
    
    def _calculate_qoq_growth(self, data: pd.DataFrame, start_date: str, end_date: str) -> pd.DataFrame:
        """计算环比增长率"""
        # 确保数据按ts_code和trade_date排序
        data = data.sort_values(['ts_code', 'trade_date'])
        
        # 筛选日期范围
        if start_date:
            data = data[data['trade_date'] >= pd.to_datetime(start_date)]
        if end_date:
            data = data[data['trade_date'] <= pd.to_datetime(end_date)]
        
        # 创建结果DataFrame
        result = data[['trade_date', 'ts_code']].copy()
        result[self.name] = np.nan  # 预先添加结果列，所有值设为NaN
        
        # 仅处理季度数据，年度数据无法计算环比
        if self.period == 'quarterly':
            # 处理季度数据
            data['year'] = data['trade_date'].dt.year
            data['quarter'] = data['trade_date'].dt.quarter
            
            # 对每个ts_code分组
            for ts_code, group in data.groupby('ts_code'):
                # 按报告类型分组
                for report_type, report_data in group.groupby('report_type'):
                    # 排序确保时间顺序
                    report_data = report_data.sort_values('trade_date')
                    
                    if len(report_data) < 2:  # 需要至少有2个数据点才能计算环比
                        continue
                    
                    # 创建一个临时DataFrame用于计算
                    temp_df = report_data.copy()
                    # 添加前一季度的数据
                    temp_df['prev_quarter_profit'] = temp_df['net_profit'].shift(1)
                    
                    # 计算环比增长率，特殊处理亏损转盈利和盈利转亏损的情况
                    for i, row in temp_df.iterrows():
                        if pd.notna(row['prev_quarter_profit']):
                            current = row['net_profit']
                            previous = row['prev_quarter_profit']
                            
                            # 计算增长率，处理不同情况
                            if previous > 0 and current > 0:  # 两期都为正
                                growth_rate = (current - previous) / previous * 100
                            elif previous < 0 and current > 0:  # 亏损转盈利
                                growth_rate = abs(previous - current) / abs(previous) * 100
                            elif previous > 0 and current < 0:  # 盈利转亏损
                                growth_rate = (current - previous) / previous * 100
                            elif previous < 0 and current < 0:  # 两期都为负
                                # 亏损减少视为正增长，亏损增加视为负增长
                                growth_rate = (abs(previous) - abs(current)) / abs(previous) * 100
                            else:  # previous == 0，避免除零错误
                                if current > 0:
                                    growth_rate = 100  # 从零到正值，视为100%增长
                                elif current < 0:
                                    growth_rate = -100  # 从零到负值，视为-100%增长
                                else:
                                    growth_rate = 0  # 从零到零，无增长
                            
                            # 更新结果到结果DataFrame
                            result.loc[
                                (result['trade_date'] == row['trade_date']) & 
                                (result['ts_code'] == ts_code), 
                                self.name
                            ] = growth_rate
        
        return result
    
    def _calculate_cagr_growth(self, data: pd.DataFrame, start_date: str, end_date: str) -> pd.DataFrame:
        """计算复合年增长率"""
        # 确保数据按ts_code和trade_date排序
        data = data.sort_values(['ts_code', 'trade_date'])
        
        # 筛选日期范围
        if start_date:
            data = data[data['trade_date'] >= pd.to_datetime(start_date)]
        if end_date:
            data = data[data['trade_date'] <= pd.to_datetime(end_date)]
        
        # 创建结果DataFrame
        result = data[['trade_date', 'ts_code']].copy()
        result[self.name] = np.nan  # 预先添加结果列，所有值设为NaN
        
        # 添加年份列
        data['year'] = data['trade_date'].dt.year
        
        # 对每个ts_code分组
        for ts_code, group in data.groupby('ts_code'):
            # 按年份提取最新财报数据
            latest_by_year = group.sort_values('trade_date').drop_duplicates('year', keep='last')
            
            if len(latest_by_year) <= self.window:  # 需要至少比窗口多1个数据点
                continue
            
            # 排序并计算CAGR
            latest_by_year = latest_by_year.sort_values('year')
            
            # 滑动窗口计算每个时间点的CAGR
            for i in range(self.window, len(latest_by_year)):
                end_value = latest_by_year.iloc[i]['net_profit']
                start_value = latest_by_year.iloc[i - self.window]['net_profit']
                
                # 对于净利润，只有当起始和结束都为正时才计算CAGR
                if start_value > 0 and end_value > 0:
                    # CAGR = (end_value / start_value)^(1/years) - 1
                    cagr = ((end_value / start_value) ** (1 / self.window) - 1) * 100
                    
                    # 更新结果
                    date = latest_by_year.iloc[i]['trade_date']
                    result.loc[
                        (result['trade_date'] == date) & 
                        (result['ts_code'] == ts_code), 
                        self.name
                    ] = cagr
        
        return result
    
    def get_required_parameters(self) -> List[str]:
        """获取计算所需的参数列表"""
        return ['data', 'start_date', 'end_date'] 