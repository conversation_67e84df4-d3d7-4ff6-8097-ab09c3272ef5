"""
盈利能力因子模块
- 实现净资产收益率（ROE）因子
- 实现总资产收益率（ROA）因子
- 实现毛利率（Gross Profit Margin）因子
- 实现净利率（Net Profit Margin）因子
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union, Optional, Tuple, Set
from datetime import datetime, date

from data.storage.storage_factory import StorageFactory
from ..base_factor import BaseFactor, ComputeError, DataError

class ROEFactor(BaseFactor):
    """
    净资产收益率（Return on Equity）因子
    
    计算方法：
    ROE = 净利润 / 平均股东权益
        = 净利润 / ((期初股东权益 + 期末股东权益) / 2)
    
    用途：
    1. 评估公司利用股东投入资本创造利润的能力
    2. 反映公司资本配置和运营效率
    3. 净资产收益率高的公司通常具有更好的竞争优势
    """
    
    # 支持的ROE类型
    VALID_ROE_TYPES = {'ttm', 'annual', 'quarterly'}
    
    def __init__(self, 
                roe_type: str = "ttm",  # ttm, annual, quarterly
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化净资产收益率因子
        
        参数:
            roe_type: 计算类型，可选值为：
                - ttm: 使用过去12个月数据（滚动计算）
                - annual: 使用年度数据
                - quarterly: 使用季度数据
            name: 因子名称，默认为'roe_{roe_type}'
            description: 因子描述
            cache_dir: 缓存目录
        """
        # 验证ROE类型
        if roe_type not in self.VALID_ROE_TYPES:
            raise ValueError(f"不支持的ROE类型: {roe_type}，支持的类型有: {', '.join(self.VALID_ROE_TYPES)}")
            
        self.roe_type = roe_type
        
        # 设置默认名称
        if name is None:
            name = f"roe_{roe_type}"
            
        # 设置默认描述
        if not description:
            description = f"净资产收益率因子 ({roe_type})"
            
        super().__init__(name=name, description=description, cache_dir=cache_dir)
    
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        计算净资产收益率
        
        参数:
            data: 输入数据，包含财务数据和市场数据
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            包含净资产收益率的DataFrame
        """
        # 检查必要参数
        for param in self.get_required_parameters():
            if param not in kwargs:
                raise ComputeError(f"计算ROE因子需要参数: {param}")
        
        data = kwargs.get("data")
        start_date = kwargs.get("start_date")
        end_date = kwargs.get("end_date")
        
        # 验证输入数据
        if data.empty:
            raise DataError("输入数据为空")
        
        # 检查必要的列
        required_columns = ['ts_code', 'trade_date', 'net_profit', 'total_equity']
        for col in required_columns:
            if col not in data.columns:
                raise DataError(f"输入数据缺少必要的列: {col}")
        
        # 处理日期类型
        if isinstance(data['trade_date'].iloc[0], str):
            data['trade_date'] = pd.to_datetime(data['trade_date'])
        
        # 设置索引以便于计算
        data = data.sort_values(['ts_code', 'trade_date'])
        data = data.set_index(['ts_code', 'trade_date']).reset_index()
        
        result = pd.DataFrame()
        
        # 根据不同类型计算ROE
        for ts_code, group in data.groupby('ts_code'):
            # 确保数据按日期排序
            group = group.sort_values('trade_date')
            
            if self.roe_type == 'ttm':
                # 使用TTM数据计算
                group['net_profit_ttm'] = group['net_profit'].rolling(4).sum()
                group['avg_equity'] = (group['total_equity'] + group['total_equity'].shift(4)) / 2
                group.loc[group['avg_equity'] <= 0, 'avg_equity'] = np.nan  # 避免除以零或负值
                group['roe'] = group['net_profit_ttm'] / group['avg_equity']
            
            elif self.roe_type == 'annual':
                # 使用年度数据计算
                # 筛选年报数据（通常Q4代表年报）
                annual_data = group[group['report_type'] == 'annual']
                if annual_data.empty:
                    annual_data = group[group['trade_date'].dt.month == 12]  # 如果没有report_type，使用12月数据
                
                # 计算当年净利润和平均股东权益
                annual_data['avg_equity'] = (annual_data['total_equity'] + annual_data['total_equity'].shift(1)) / 2
                annual_data.loc[annual_data['avg_equity'] <= 0, 'avg_equity'] = np.nan  # 避免除以零或负值
                annual_data['roe'] = annual_data['net_profit'] / annual_data['avg_equity']
                
                # 合并回原始数据中
                group = pd.merge(group[['ts_code', 'trade_date']], 
                                annual_data[['ts_code', 'trade_date', 'roe']], 
                                on=['ts_code', 'trade_date'], 
                                how='left')
                group['roe'] = group['roe'].ffill()  # 用最近的年度数据填充
            
            elif self.roe_type == 'quarterly':
                # 使用季度数据计算
                # 对每个季度数据单独计算
                # 注意：这里简化处理，实际应该考虑单季数据
                group['avg_equity'] = (group['total_equity'] + group['total_equity'].shift(1)) / 2
                group.loc[group['avg_equity'] <= 0, 'avg_equity'] = np.nan  # 避免除以零或负值
                group['roe'] = group['net_profit'] / group['avg_equity']
            
            # 将结果添加到结果集
            if not result.empty:
                result = pd.concat([result, group[['ts_code', 'trade_date', 'roe']]])
            else:
                result = group[['ts_code', 'trade_date', 'roe']]
        
        # 过滤日期范围
        if start_date:
            if isinstance(start_date, str):
                start_date = pd.to_datetime(start_date)
            result = result[result['trade_date'] >= start_date]
        
        if end_date:
            if isinstance(end_date, str):
                end_date = pd.to_datetime(end_date)
            result = result[result['trade_date'] <= end_date]
        
        return result
    
    def get_required_parameters(self) -> List[str]:
        """
        获取计算因子所需的参数列表
        
        返回:
            参数列表，包含'data', 'start_date', 'end_date'
        """
        return ['data', 'start_date', 'end_date']


class ROAFactor(BaseFactor):
    """
    总资产收益率（Return on Assets）因子
    
    计算方法：
    ROA = 净利润 / 平均总资产
        = 净利润 / ((期初总资产 + 期末总资产) / 2)
    
    用途：
    1. 评估公司利用全部资产创造利润的效率
    2. 反映公司整体资产管理和运营效率
    3. 适合比较不同资本结构的公司
    """
    
    # 支持的ROA类型
    VALID_ROA_TYPES = {'ttm', 'annual', 'quarterly'}
    
    def __init__(self, 
                roa_type: str = "ttm",  # ttm, annual, quarterly
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化总资产收益率因子
        
        参数:
            roa_type: 计算类型，可选值为：
                - ttm: 使用过去12个月数据（滚动计算）
                - annual: 使用年度数据
                - quarterly: 使用季度数据
            name: 因子名称，默认为'roa_{roa_type}'
            description: 因子描述
            cache_dir: 缓存目录
        """
        # 验证ROA类型
        if roa_type not in self.VALID_ROA_TYPES:
            raise ValueError(f"不支持的ROA类型: {roa_type}，支持的类型有: {', '.join(self.VALID_ROA_TYPES)}")
            
        self.roa_type = roa_type
        
        # 设置默认名称
        if name is None:
            name = f"roa_{roa_type}"
            
        # 设置默认描述
        if not description:
            description = f"总资产收益率因子 ({roa_type})"
            
        super().__init__(name=name, description=description, cache_dir=cache_dir)
    
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        计算总资产收益率
        
        参数:
            data: 输入数据，包含财务数据和市场数据
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            包含总资产收益率的DataFrame
        """
        # 检查必要参数
        for param in self.get_required_parameters():
            if param not in kwargs:
                raise ComputeError(f"计算ROA因子需要参数: {param}")
        
        data = kwargs.get("data")
        start_date = kwargs.get("start_date")
        end_date = kwargs.get("end_date")
        
        # 验证输入数据
        if data.empty:
            raise DataError("输入数据为空")
        
        # 检查必要的列
        required_columns = ['ts_code', 'trade_date', 'net_profit', 'total_assets']
        for col in required_columns:
            if col not in data.columns:
                raise DataError(f"输入数据缺少必要的列: {col}")
        
        # 处理日期类型
        if isinstance(data['trade_date'].iloc[0], str):
            data['trade_date'] = pd.to_datetime(data['trade_date'])
        
        # 设置索引以便于计算
        data = data.sort_values(['ts_code', 'trade_date'])
        data = data.set_index(['ts_code', 'trade_date']).reset_index()
        
        result = pd.DataFrame()
        
        # 根据不同类型计算ROA
        for ts_code, group in data.groupby('ts_code'):
            # 确保数据按日期排序
            group = group.sort_values('trade_date')
            
            if self.roa_type == 'ttm':
                # 使用TTM数据计算
                group['net_profit_ttm'] = group['net_profit'].rolling(4).sum()
                group['avg_assets'] = (group['total_assets'] + group['total_assets'].shift(4)) / 2
                group.loc[group['avg_assets'] <= 0, 'avg_assets'] = np.nan  # 避免除以零或负值
                group['roa'] = group['net_profit_ttm'] / group['avg_assets']
            
            elif self.roa_type == 'annual':
                # 使用年度数据计算
                # 筛选年报数据（通常Q4代表年报）
                annual_data = group[group['report_type'] == 'annual']
                if annual_data.empty:
                    annual_data = group[group['trade_date'].dt.month == 12]  # 如果没有report_type，使用12月数据
                
                # 计算当年净利润和平均总资产
                annual_data['avg_assets'] = (annual_data['total_assets'] + annual_data['total_assets'].shift(1)) / 2
                annual_data.loc[annual_data['avg_assets'] <= 0, 'avg_assets'] = np.nan  # 避免除以零或负值
                annual_data['roa'] = annual_data['net_profit'] / annual_data['avg_assets']
                
                # 合并回原始数据中
                group = pd.merge(group[['ts_code', 'trade_date']], 
                                annual_data[['ts_code', 'trade_date', 'roa']], 
                                on=['ts_code', 'trade_date'], 
                                how='left')
                group['roa'] = group['roa'].ffill()  # 用最近的年度数据填充
            
            elif self.roa_type == 'quarterly':
                # 使用季度数据计算
                # 对每个季度数据单独计算
                group['avg_assets'] = (group['total_assets'] + group['total_assets'].shift(1)) / 2
                group.loc[group['avg_assets'] <= 0, 'avg_assets'] = np.nan  # 避免除以零或负值
                group['roa'] = group['net_profit'] / group['avg_assets']
            
            # 将结果添加到结果集
            if not result.empty:
                result = pd.concat([result, group[['ts_code', 'trade_date', 'roa']]])
            else:
                result = group[['ts_code', 'trade_date', 'roa']]
        
        # 过滤日期范围
        if start_date:
            if isinstance(start_date, str):
                start_date = pd.to_datetime(start_date)
            result = result[result['trade_date'] >= start_date]
        
        if end_date:
            if isinstance(end_date, str):
                end_date = pd.to_datetime(end_date)
            result = result[result['trade_date'] <= end_date]
        
        return result
    
    def get_required_parameters(self) -> List[str]:
        """
        获取计算因子所需的参数列表
        
        返回:
            参数列表，包含'data', 'start_date', 'end_date'
        """
        return ['data', 'start_date', 'end_date']


class GrossProfitMarginFactor(BaseFactor):
    """
    毛利率（Gross Profit Margin）因子
    
    计算方法：
    毛利率 = 毛利润 / 营业收入
          = (营业收入 - 营业成本) / 营业收入
    
    用途：
    1. 评估公司产品的定价能力和成本控制能力
    2. 反映公司在行业中的竞争地位
    3. 高毛利率通常意味着公司具有较强的产品差异化或成本优势
    """
    
    # 支持的计算类型
    VALID_CALC_TYPES = {'ttm', 'annual', 'quarterly'}
    
    def __init__(self, 
                calc_type: str = "ttm",  # ttm, annual, quarterly
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化毛利率因子
        
        参数:
            calc_type: 计算类型，可选值为：
                - ttm: 使用过去12个月数据（滚动计算）
                - annual: 使用年度数据
                - quarterly: 使用季度数据
            name: 因子名称，默认为'gross_margin_{calc_type}'
            description: 因子描述
            cache_dir: 缓存目录
        """
        # 验证计算类型
        if calc_type not in self.VALID_CALC_TYPES:
            raise ValueError(f"不支持的计算类型: {calc_type}，支持的类型有: {', '.join(self.VALID_CALC_TYPES)}")
            
        self.calc_type = calc_type
        
        # 设置默认名称
        if name is None:
            name = f"gross_margin_{calc_type}"
            
        # 设置默认描述
        if not description:
            description = f"毛利率因子 ({calc_type})"
            
        super().__init__(name=name, description=description, cache_dir=cache_dir)
    
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        计算毛利率
        
        参数:
            data: 输入数据，包含财务数据
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            包含毛利率的DataFrame
        """
        # 检查必要参数
        for param in self.get_required_parameters():
            if param not in kwargs:
                raise ComputeError(f"计算毛利率因子需要参数: {param}")
        
        data = kwargs.get("data")
        start_date = kwargs.get("start_date")
        end_date = kwargs.get("end_date")
        
        # 验证输入数据
        if data.empty:
            raise DataError("输入数据为空")
        
        # 检查必要的列
        required_columns = ['ts_code', 'trade_date', 'revenue', 'cost']
        for col in required_columns:
            if col not in data.columns:
                raise DataError(f"输入数据缺少必要的列: {col}")
        
        # 处理日期类型
        if isinstance(data['trade_date'].iloc[0], str):
            data['trade_date'] = pd.to_datetime(data['trade_date'])
        
        # 设置索引以便于计算
        data = data.sort_values(['ts_code', 'trade_date'])
        data = data.set_index(['ts_code', 'trade_date']).reset_index()
        
        result = pd.DataFrame()
        
        # 根据不同类型计算毛利率
        for ts_code, group in data.groupby('ts_code'):
            # 确保数据按日期排序
            group = group.sort_values('trade_date')
            
            if self.calc_type == 'ttm':
                # 使用TTM数据计算
                group['revenue_ttm'] = group['revenue'].rolling(4).sum()
                group['cost_ttm'] = group['cost'].rolling(4).sum()
                group['gross_profit_ttm'] = group['revenue_ttm'] - group['cost_ttm']
                
                # 避免除以零
                group.loc[group['revenue_ttm'] <= 0, 'revenue_ttm'] = np.nan
                group['gross_margin'] = group['gross_profit_ttm'] / group['revenue_ttm']
            
            elif self.calc_type == 'annual':
                # 使用年度数据计算
                # 筛选年报数据
                annual_data = group[group['report_type'] == 'annual']
                if annual_data.empty:
                    annual_data = group[group['trade_date'].dt.month == 12]  # 如果没有report_type，使用12月数据
                
                # 计算毛利润和毛利率
                annual_data['gross_profit'] = annual_data['revenue'] - annual_data['cost']
                
                # 避免除以零
                annual_data.loc[annual_data['revenue'] <= 0, 'revenue'] = np.nan
                annual_data['gross_margin'] = annual_data['gross_profit'] / annual_data['revenue']
                
                # 合并回原始数据中
                group = pd.merge(group[['ts_code', 'trade_date']], 
                                annual_data[['ts_code', 'trade_date', 'gross_margin']], 
                                on=['ts_code', 'trade_date'], 
                                how='left')
                group['gross_margin'] = group['gross_margin'].ffill()  # 用最近的年度数据填充
            
            elif self.calc_type == 'quarterly':
                # 使用季度数据计算
                # 对每个季度数据单独计算
                group['gross_profit'] = group['revenue'] - group['cost']
                
                # 避免除以零
                group.loc[group['revenue'] <= 0, 'revenue'] = np.nan
                group['gross_margin'] = group['gross_profit'] / group['revenue']
            
            # 将结果添加到结果集
            if not result.empty:
                result = pd.concat([result, group[['ts_code', 'trade_date', 'gross_margin']]])
            else:
                result = group[['ts_code', 'trade_date', 'gross_margin']]
        
        # 过滤日期范围
        if start_date:
            if isinstance(start_date, str):
                start_date = pd.to_datetime(start_date)
            result = result[result['trade_date'] >= start_date]
        
        if end_date:
            if isinstance(end_date, str):
                end_date = pd.to_datetime(end_date)
            result = result[result['trade_date'] <= end_date]
        
        return result
    
    def get_required_parameters(self) -> List[str]:
        """
        获取计算因子所需的参数列表
        
        返回:
            参数列表，包含'data', 'start_date', 'end_date'
        """
        return ['data', 'start_date', 'end_date']


class NetProfitMarginFactor(BaseFactor):
    """
    净利率（Net Profit Margin）因子
    
    计算方法：
    净利率 = 净利润 / 营业收入
    
    用途：
    1. 评估公司整体盈利能力和成本控制能力
    2. 反映公司各项成本费用控制的综合效果
    3. 高净利率通常意味着公司具有良好的成本管理和运营效率
    """
    
    # 支持的计算类型
    VALID_CALC_TYPES = {'ttm', 'annual', 'quarterly'}
    
    def __init__(self, 
                calc_type: str = "ttm",  # ttm, annual, quarterly
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化净利率因子
        
        参数:
            calc_type: 计算类型，可选值为：
                - ttm: 使用过去12个月数据（滚动计算）
                - annual: 使用年度数据
                - quarterly: 使用季度数据
            name: 因子名称，默认为'net_margin_{calc_type}'
            description: 因子描述
            cache_dir: 缓存目录
        """
        # 验证计算类型
        if calc_type not in self.VALID_CALC_TYPES:
            raise ValueError(f"不支持的计算类型: {calc_type}，支持的类型有: {', '.join(self.VALID_CALC_TYPES)}")
            
        self.calc_type = calc_type
        
        # 设置默认名称
        if name is None:
            name = f"net_margin_{calc_type}"
            
        # 设置默认描述
        if not description:
            description = f"净利率因子 ({calc_type})"
            
        super().__init__(name=name, description=description, cache_dir=cache_dir)
    
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        计算净利率
        
        参数:
            data: 输入数据，包含财务数据
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            包含净利率的DataFrame
        """
        # 检查必要参数
        for param in self.get_required_parameters():
            if param not in kwargs:
                raise ComputeError(f"计算净利率因子需要参数: {param}")
        
        data = kwargs.get("data")
        start_date = kwargs.get("start_date")
        end_date = kwargs.get("end_date")
        
        # 验证输入数据
        if data.empty:
            raise DataError("输入数据为空")
        
        # 检查必要的列
        required_columns = ['ts_code', 'trade_date', 'revenue', 'net_profit']
        for col in required_columns:
            if col not in data.columns:
                raise DataError(f"输入数据缺少必要的列: {col}")
        
        # 处理日期类型
        if isinstance(data['trade_date'].iloc[0], str):
            data['trade_date'] = pd.to_datetime(data['trade_date'])
        
        # 设置索引以便于计算
        data = data.sort_values(['ts_code', 'trade_date'])
        data = data.set_index(['ts_code', 'trade_date']).reset_index()
        
        result = pd.DataFrame()
        
        # 根据不同类型计算净利率
        for ts_code, group in data.groupby('ts_code'):
            # 确保数据按日期排序
            group = group.sort_values('trade_date')
            
            if self.calc_type == 'ttm':
                # 使用TTM数据计算
                group['revenue_ttm'] = group['revenue'].rolling(4).sum()
                group['net_profit_ttm'] = group['net_profit'].rolling(4).sum()
                
                # 避免除以零
                group.loc[group['revenue_ttm'] <= 0, 'revenue_ttm'] = np.nan
                group['net_margin'] = group['net_profit_ttm'] / group['revenue_ttm']
            
            elif self.calc_type == 'annual':
                # 使用年度数据计算
                # 筛选年报数据
                annual_data = group[group['report_type'] == 'annual']
                if annual_data.empty:
                    annual_data = group[group['trade_date'].dt.month == 12]  # 如果没有report_type，使用12月数据
                
                # 避免除以零
                annual_data.loc[annual_data['revenue'] <= 0, 'revenue'] = np.nan
                annual_data['net_margin'] = annual_data['net_profit'] / annual_data['revenue']
                
                # 合并回原始数据中
                group = pd.merge(group[['ts_code', 'trade_date']], 
                                annual_data[['ts_code', 'trade_date', 'net_margin']], 
                                on=['ts_code', 'trade_date'], 
                                how='left')
                group['net_margin'] = group['net_margin'].ffill()  # 用最近的年度数据填充
            
            elif self.calc_type == 'quarterly':
                # 使用季度数据计算
                # 对每个季度数据单独计算
                # 避免除以零
                group.loc[group['revenue'] <= 0, 'revenue'] = np.nan
                group['net_margin'] = group['net_profit'] / group['revenue']
            
            # 将结果添加到结果集
            if not result.empty:
                result = pd.concat([result, group[['ts_code', 'trade_date', 'net_margin']]])
            else:
                result = group[['ts_code', 'trade_date', 'net_margin']]
        
        # 过滤日期范围
        if start_date:
            if isinstance(start_date, str):
                start_date = pd.to_datetime(start_date)
            result = result[result['trade_date'] >= start_date]
        
        if end_date:
            if isinstance(end_date, str):
                end_date = pd.to_datetime(end_date)
            result = result[result['trade_date'] <= end_date]
        
        return result
    
    def get_required_parameters(self) -> List[str]:
        """
        获取计算因子所需的参数列表
        
        返回:
            参数列表，包含'data', 'start_date', 'end_date'
        """
        return ['data', 'start_date', 'end_date'] 