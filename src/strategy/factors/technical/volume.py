"""
成交量(Volume)相关因子模块
- 实现成交量变化率(Volume Change Rate)计算
- 实现相对成交量(Relative Volume)计算
- 实现资金流向(Money Flow)指标计算
- 支持自定义参数配置
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union, Optional, Tuple, Set
from datetime import datetime, date

from data.storage.storage_factory import StorageFactory
from ..base_factor import BaseFactor, ComputeError, DataError

class VolumeChangeRate(BaseFactor):
    """
    成交量变化率因子
    
    计算当前成交量与过去N日平均成交量的变化率，用于衡量成交量的异常变动
    """
    
    def __init__(self, 
                window: int = 5, 
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化成交量变化率因子
        
        Args:
            window: 对比窗口大小，默认为5
            name: 因子名称，默认为f"vol_change_{window}"
            description: 因子描述
            cache_dir: 缓存目录
        """
        if name is None:
            name = f"vol_change_{window}"
            
        if not description:
            description = f"成交量变化率, 窗口: {window}"
            
        super().__init__(name, description, "technical", cache_dir)
        
        self.window = window
        
        # 更新元数据
        self._metadata.update({
            'window': window
        })
    
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        实现成交量变化率因子的计算逻辑
        
        Args:
            **kwargs: 计算参数
                data: 输入数据DataFrame，必须包含vol和日期列
                start_date: 开始日期
                end_date: 结束日期
                
        Returns:
            pd.DataFrame: 因子计算结果
        """
        # 获取参数
        data = kwargs.get('data')
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        
        if data is None:
            raise ComputeError("必须提供数据")
            
        if not start_date or not end_date:
            raise ComputeError("必须提供开始日期和结束日期")
        
        try:
            # 确保数据不为空
            if data.empty:
                raise DataError("输入数据为空")
            
            # 确保日期格式正确
            date_col = 'trade_date' if 'trade_date' in data.columns else 'date'
            if not pd.api.types.is_datetime64_dtype(data[date_col]):
                data[date_col] = pd.to_datetime(data[date_col])
            
            # 确保成交量字段存在
            if 'vol' not in data.columns:
                raise DataError("数据中缺少成交量字段")
            
            # 按证券分组计算成交量变化率
            result = pd.DataFrame()
            
            for code, group in data.groupby('ts_code'):
                # 排序，确保数据按日期有序
                group = group.sort_values(date_col)
                
                # 计算N日平均成交量
                group[f'avg_vol_{self.window}'] = group['vol'].rolling(window=self.window).mean()
                
                # 计算成交量变化率
                factor_name = self.get_name()
                group[factor_name] = (group['vol'] / group[f'avg_vol_{self.window}'] - 1) * 100
                
                # 选取结果列
                df = pd.DataFrame({
                    'ts_code': code,
                    date_col: group[date_col],
                    factor_name: group[factor_name]
                })
                
                # 添加到结果集
                result = pd.concat([result, df], ignore_index=True)
            
            # 筛选目标日期范围
            result = result[
                (result[date_col] >= pd.to_datetime(start_date)) & 
                (result[date_col] <= pd.to_datetime(end_date))
            ]
            
            # 设置索引
            result = result.set_index([date_col, 'ts_code'])
            
            return result
            
        except Exception as e:
            if isinstance(e, (ComputeError, DataError)):
                raise
            
            self.logger.error(f"计算成交量变化率失败: {str(e)}")
            raise ComputeError(f"计算成交量变化率失败: {str(e)}")
    
    def get_required_parameters(self) -> List[str]:
        """
        获取必需的计算参数列表
        
        Returns:
            List[str]: 必需参数列表
        """
        return ['data', 'start_date', 'end_date']


class RelativeVolume(BaseFactor):
    """
    相对成交量因子
    
    计算当前成交量与过去N日同期成交量均值的比值，用于判断当前成交量的相对大小
    """
    
    def __init__(self, 
                window: int = 20, 
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化相对成交量因子
        
        Args:
            window: 历史对比窗口大小，默认为20
            name: 因子名称，默认为f"rel_vol_{window}"
            description: 因子描述
            cache_dir: 缓存目录
        """
        if name is None:
            name = f"rel_vol_{window}"
            
        if not description:
            description = f"相对成交量, 窗口: {window}"
            
        super().__init__(name, description, "technical", cache_dir)
        
        self.window = window
        
        # 更新元数据
        self._metadata.update({
            'window': window
        })
    
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        实现相对成交量因子的计算逻辑
        
        Args:
            **kwargs: 计算参数
                data: 输入数据DataFrame，必须包含vol和日期列
                start_date: 开始日期
                end_date: 结束日期
                
        Returns:
            pd.DataFrame: 因子计算结果
        """
        # 获取参数
        data = kwargs.get('data')
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        
        if data is None:
            raise ComputeError("必须提供数据")
            
        if not start_date or not end_date:
            raise ComputeError("必须提供开始日期和结束日期")
        
        try:
            # 确保数据不为空
            if data.empty:
                raise DataError("输入数据为空")
            
            # 确保日期格式正确
            date_col = 'trade_date' if 'trade_date' in data.columns else 'date'
            if not pd.api.types.is_datetime64_dtype(data[date_col]):
                data[date_col] = pd.to_datetime(data[date_col])
            
            # 确保成交量字段存在
            if 'vol' not in data.columns:
                raise DataError("数据中缺少成交量字段")
            
            # 按证券分组计算相对成交量
            result = pd.DataFrame()
            
            for code, group in data.groupby('ts_code'):
                # 排序，确保数据按日期有序
                group = group.sort_values(date_col)
                
                # 计算相对成交量
                current_vol = group['vol']
                
                # 创建与当前日期对应的历史同期成交量列表
                historical_vols = []
                for i in range(1, self.window + 1):
                    historical_vols.append(current_vol.shift(i))
                
                # 计算历史同期平均成交量
                hist_mean_vol = pd.concat(historical_vols, axis=1).mean(axis=1)
                
                # 计算相对成交量比值
                factor_name = self.get_name()
                group[factor_name] = current_vol / hist_mean_vol
                
                # 选取结果列
                df = pd.DataFrame({
                    'ts_code': code,
                    date_col: group[date_col],
                    factor_name: group[factor_name]
                })
                
                # 添加到结果集
                result = pd.concat([result, df], ignore_index=True)
            
            # 筛选目标日期范围
            result = result[
                (result[date_col] >= pd.to_datetime(start_date)) & 
                (result[date_col] <= pd.to_datetime(end_date))
            ]
            
            # 设置索引
            result = result.set_index([date_col, 'ts_code'])
            
            return result
            
        except Exception as e:
            if isinstance(e, (ComputeError, DataError)):
                raise
            
            self.logger.error(f"计算相对成交量失败: {str(e)}")
            raise ComputeError(f"计算相对成交量失败: {str(e)}")
    
    def get_required_parameters(self) -> List[str]:
        """
        获取必需的计算参数列表
        
        Returns:
            List[str]: 必需参数列表
        """
        return ['data', 'start_date', 'end_date']


class MoneyFlowIndex(BaseFactor):
    """
    资金流向指标(Money Flow Index, MFI)因子
    
    MFI结合了价格和成交量变化，是RSI的成交量加权版本，用于判断资金流入流出情况
    """
    
    def __init__(self, 
                window: int = 14, 
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化资金流向指标因子
        
        Args:
            window: 计算窗口大小，默认为14
            name: 因子名称，默认为f"mfi_{window}"
            description: 因子描述
            cache_dir: 缓存目录
        """
        if name is None:
            name = f"mfi_{window}"
            
        if not description:
            description = f"资金流向指标, 窗口: {window}"
            
        super().__init__(name, description, "technical", cache_dir)
        
        self.window = window
        
        # 更新元数据
        self._metadata.update({
            'window': window
        })
    
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        实现资金流向指标因子的计算逻辑
        
        Args:
            **kwargs: 计算参数
                data: 输入数据DataFrame，必须包含open、high、low、close、vol和日期列
                start_date: 开始日期
                end_date: 结束日期
                
        Returns:
            pd.DataFrame: 因子计算结果
        """
        # 获取参数
        data = kwargs.get('data')
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        
        if data is None:
            raise ComputeError("必须提供数据")
            
        if not start_date or not end_date:
            raise ComputeError("必须提供开始日期和结束日期")
        
        try:
            # 确保数据不为空
            if data.empty:
                raise DataError("输入数据为空")
            
            # 确保日期格式正确
            date_col = 'trade_date' if 'trade_date' in data.columns else 'date'
            if not pd.api.types.is_datetime64_dtype(data[date_col]):
                data[date_col] = pd.to_datetime(data[date_col])
            
            # 确保必要字段存在
            required_fields = ['open', 'high', 'low', 'close', 'vol']
            for field in required_fields:
                if field not in data.columns:
                    raise DataError(f"数据中缺少必要字段: {field}")
            
            # 按证券分组计算MFI
            result = pd.DataFrame()
            
            for code, group in data.groupby('ts_code'):
                # 排序，确保数据按日期有序
                group = group.sort_values(date_col)
                
                # 计算典型价格 (Typical Price)
                group['typical_price'] = (group['high'] + group['low'] + group['close']) / 3
                
                # 计算资金流 (Money Flow)
                group['money_flow'] = group['typical_price'] * group['vol']
                
                # 计算价格变动方向
                group['price_direction'] = group['typical_price'].diff().fillna(0)
                
                # 计算正向资金流 (Positive Money Flow)
                group['positive_flow'] = np.where(group['price_direction'] > 0, group['money_flow'], 0)
                
                # 计算负向资金流 (Negative Money Flow)
                group['negative_flow'] = np.where(group['price_direction'] < 0, group['money_flow'], 0)
                
                # 计算N日正向资金流总和
                group['positive_flow_sum'] = group['positive_flow'].rolling(window=self.window).sum()
                
                # 计算N日负向资金流总和
                group['negative_flow_sum'] = group['negative_flow'].rolling(window=self.window).sum()
                
                # 计算资金比率 (Money Ratio)
                group['money_ratio'] = np.where(
                    group['negative_flow_sum'] != 0, 
                    group['positive_flow_sum'] / group['negative_flow_sum'],
                    100  # 避免除零错误
                )
                
                # 计算MFI
                factor_name = self.get_name()
                group[factor_name] = 100 - (100 / (1 + group['money_ratio']))
                
                # 选取结果列
                df = pd.DataFrame({
                    'ts_code': code,
                    date_col: group[date_col],
                    factor_name: group[factor_name]
                })
                
                # 添加到结果集
                result = pd.concat([result, df], ignore_index=True)
            
            # 筛选目标日期范围
            result = result[
                (result[date_col] >= pd.to_datetime(start_date)) & 
                (result[date_col] <= pd.to_datetime(end_date))
            ]
            
            # 设置索引
            result = result.set_index([date_col, 'ts_code'])
            
            return result
            
        except Exception as e:
            if isinstance(e, (ComputeError, DataError)):
                raise
            
            self.logger.error(f"计算资金流向指标失败: {str(e)}")
            raise ComputeError(f"计算资金流向指标失败: {str(e)}")
    
    def get_required_parameters(self) -> List[str]:
        """
        获取必需的计算参数列表
        
        Returns:
            List[str]: 必需参数列表
        """
        return ['data', 'start_date', 'end_date']


class OnBalanceVolume(BaseFactor):
    """
    能量潮(On-Balance Volume, OBV)因子
    
    OBV通过累计成交量来反映股价变动的动能，是量价关系分析的基础指标
    """
    
    def __init__(self, 
                name: str = "obv",
                description: str = "能量潮指标",
                cache_dir: str = None):
        """
        初始化能量潮因子
        
        Args:
            name: 因子名称，默认为"obv"
            description: 因子描述
            cache_dir: 缓存目录
        """
        super().__init__(name, description, "technical", cache_dir)
    
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        实现能量潮因子的计算逻辑
        
        Args:
            **kwargs: 计算参数
                data: 输入数据DataFrame，必须包含close、vol和日期列
                start_date: 开始日期
                end_date: 结束日期
                
        Returns:
            pd.DataFrame: 因子计算结果
        """
        # 获取参数
        data = kwargs.get('data')
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        
        if data is None:
            raise ComputeError("必须提供数据")
            
        if not start_date or not end_date:
            raise ComputeError("必须提供开始日期和结束日期")
        
        try:
            # 确保数据不为空
            if data.empty:
                raise DataError("输入数据为空")
            
            # 确保日期格式正确
            date_col = 'trade_date' if 'trade_date' in data.columns else 'date'
            if not pd.api.types.is_datetime64_dtype(data[date_col]):
                data[date_col] = pd.to_datetime(data[date_col])
            
            # 确保必要字段存在
            required_fields = ['close', 'vol']
            for field in required_fields:
                if field not in data.columns:
                    raise DataError(f"数据中缺少必要字段: {field}")
            
            # 按证券分组计算OBV
            result = pd.DataFrame()
            
            for code, group in data.groupby('ts_code'):
                # 排序，确保数据按日期有序
                group = group.sort_values(date_col)
                
                # 计算价格变动方向
                group['price_change'] = group['close'].diff().fillna(0)
                
                # 根据价格变动方向确定OBV的增减
                group['volume_direction'] = np.where(
                    group['price_change'] > 0, 
                    group['vol'],
                    np.where(
                        group['price_change'] < 0,
                        -group['vol'],
                        0  # 价格不变时，OBV不变
                    )
                )
                
                # 计算OBV累积值
                factor_name = self.get_name()
                group[factor_name] = group['volume_direction'].cumsum()
                
                # 选取结果列
                df = pd.DataFrame({
                    'ts_code': code,
                    date_col: group[date_col],
                    factor_name: group[factor_name]
                })
                
                # 添加到结果集
                result = pd.concat([result, df], ignore_index=True)
            
            # 筛选目标日期范围
            result = result[
                (result[date_col] >= pd.to_datetime(start_date)) & 
                (result[date_col] <= pd.to_datetime(end_date))
            ]
            
            # 设置索引
            result = result.set_index([date_col, 'ts_code'])
            
            return result
            
        except Exception as e:
            if isinstance(e, (ComputeError, DataError)):
                raise
            
            self.logger.error(f"计算能量潮指标失败: {str(e)}")
            raise ComputeError(f"计算能量潮指标失败: {str(e)}")
    
    def get_required_parameters(self) -> List[str]:
        """
        获取必需的计算参数列表
        
        Returns:
            List[str]: 必需参数列表
        """
        return ['data', 'start_date', 'end_date'] 