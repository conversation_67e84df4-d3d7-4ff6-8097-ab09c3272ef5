"""
移动平均收敛发散(MACD)因子模块
- 实现MACD技术指标计算
- 提供DIF、DEA、MACD柱状图计算
- 支持自定义参数配置
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union, Optional, Tuple, Set
from datetime import datetime, date

from data.storage.storage_factory import StorageFactory
from ..base_factor import BaseFactor, ComputeError, DataError

class MACD(BaseFactor):
    """
    移动平均收敛发散(MACD)因子
    
    MACD由三部分组成：
    - DIF(差离值): 快速EMA与慢速EMA的差
    - DEA(信号线): DIF的平滑移动平均
    - MACD柱状图: (DIF-DEA)*2
    
    常用于判断趋势变化和买卖信号：
    - DIF上穿DEA: 买入信号
    - DIF下穿DEA: 卖出信号
    - DIF与DEA的发散与收敛可反映趋势强度变化
    """
    
    def __init__(self, 
                fast_period: int = 12, 
                slow_period: int = 26,
                signal_period: int = 9,
                price_field: str = "close",
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化MACD因子
        
        Args:
            fast_period: 快线EMA周期，默认为12
            slow_period: 慢线EMA周期，默认为26
            signal_period: 信号线DEA周期，默认为9
            price_field: 价格字段，默认为"close"
            name: 因子名称，默认为"macd"
            description: 因子描述
            cache_dir: 缓存目录
        """
        # 验证参数：快周期必须小于慢周期
        if fast_period >= slow_period:
            raise ValueError("快速周期(fast_period)必须小于慢速周期(slow_period)")
            
        if name is None:
            name = f"macd_{fast_period}_{slow_period}_{signal_period}"
            
        if not description:
            description = f"移动平均收敛发散(MACD), 参数: {fast_period}/{slow_period}/{signal_period}"
            
        super().__init__(name, description, "technical", cache_dir)
        
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
        self.price_field = price_field
        self.name = name  # 明确存储名称为实例变量
        
        # 更新元数据
        self._metadata.update({
            'fast_period': fast_period,
            'slow_period': slow_period,
            'signal_period': signal_period,
            'price_field': price_field
        })
    
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        实现MACD因子的计算逻辑
        
        Args:
            **kwargs: 计算参数
                data: 输入数据DataFrame，必须包含price_field和日期列
                start_date: 开始日期
                end_date: 结束日期
                
        Returns:
            pd.DataFrame: 因子计算结果，包含DIF、DEA和MACD值
        """
        # 获取参数
        data = kwargs.get('data')
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        
        if data is None:
            raise ComputeError("必须提供数据")
        
        if not start_date or not end_date:
            raise ComputeError("必须提供开始日期和结束日期")
        
        try:
            # 确保数据不为空
            if data.empty:
                raise DataError("输入数据为空")
            
            # 确保日期格式正确
            date_col = 'trade_date' if 'trade_date' in data.columns else 'date'
            if not pd.api.types.is_datetime64_dtype(data[date_col]):
                data[date_col] = pd.to_datetime(data[date_col])
            
            # 确保价格字段存在
            if self.price_field not in data.columns:
                raise DataError(f"数据中缺少所需价格字段: {self.price_field}")
            
            # 按证券分组计算MACD
            result = pd.DataFrame()
            
            for code, group in data.groupby('ts_code'):
                # 排序，确保数据按日期有序
                group = group.sort_values(date_col)
                
                # 计算快速和慢速EMA
                fast_ema = group[self.price_field].ewm(span=self.fast_period, adjust=False).mean()
                slow_ema = group[self.price_field].ewm(span=self.slow_period, adjust=False).mean()
                
                # 计算DIF(差离值)
                dif = fast_ema - slow_ema
                
                # 计算DEA(信号线)
                dea = dif.ewm(span=self.signal_period, adjust=False).mean()
                
                # 计算MACD柱状图
                macd_hist = (dif - dea) * 2
                
                # 创建结果DataFrame
                df = pd.DataFrame({
                    'ts_code': code,
                    date_col: group[date_col],
                    f"{self.name}_dif": dif,
                    f"{self.name}_dea": dea,
                    f"{self.name}_hist": macd_hist
                })
                
                # 添加到结果集
                result = pd.concat([result, df], ignore_index=True)
            
            # 筛选目标日期范围
            result = result[
                (result[date_col] >= pd.to_datetime(start_date)) & 
                (result[date_col] <= pd.to_datetime(end_date))
            ]
            
            # 设置索引
            result = result.set_index([date_col, 'ts_code'])
            
            return result
            
        except Exception as e:
            if isinstance(e, (ComputeError, DataError)):
                raise
            
            self.logger.error(f"计算MACD失败: {str(e)}")
            raise ComputeError(f"计算MACD失败: {str(e)}")
    
    def get_required_parameters(self) -> List[str]:
        """
        获取必需的计算参数列表
        
        Returns:
            List[str]: 必需参数列表
        """
        return ['data', 'start_date', 'end_date'] 