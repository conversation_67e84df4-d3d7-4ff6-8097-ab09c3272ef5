"""
相对强弱指数(RSI)因子模块
- 实现RSI技术指标计算
- 支持自定义参数配置
- 支持多种计算方法
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union, Optional, Tuple, Set
from datetime import datetime, date

from data.storage.storage_factory import StorageFactory
from ..base_factor import BaseFactor, ComputeError, DataError

class RSI(BaseFactor):
    """
    相对强弱指数(RSI)因子
    
    RSI用于衡量价格变动的强度，通常用于判断市场是否处于超买或超卖状态。
    RSI的取值范围是0-100：
    - RSI > 70: 可能超买
    - RSI < 30: 可能超卖
    """
    
    def __init__(self, 
                window: int = 14, 
                price_field: str = "close",
                name: str = None,
                method: str = "wilder",  # wilder, ema, sma
                description: str = "",
                cache_dir: str = None):
        """
        初始化RSI因子
        
        Args:
            window: RSI窗口大小，默认为14
            price_field: 价格字段，默认为"close"
            name: 因子名称，默认为f"rsi_{window}"
            method: 计算方法，可选 "wilder", "ema", "sma"
                - wilder: 威尔德平滑，传统的RSI计算方法
                - ema: 使用指数移动平均计算
                - sma: 使用简单移动平均计算
            description: 因子描述
            cache_dir: 缓存目录
        """
        if name is None:
            name = f"rsi_{window}"
            
        if not description:
            description = f"相对强弱指数(RSI), 窗口: {window}, 方法: {method}"
            
        super().__init__(name, description, "technical", cache_dir)
        
        self.window = window
        self.price_field = price_field
        self.method = method.lower()
        self.name = name  # 明确存储名称为实例变量
        
        # 更新元数据
        self._metadata.update({
            'window': window,
            'price_field': price_field,
            'method': method
        })
    
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        实现RSI因子的计算逻辑
        
        Args:
            **kwargs: 计算参数
                data: 输入数据DataFrame，必须包含price_field和日期列
                start_date: 开始日期
                end_date: 结束日期
                
        Returns:
            pd.DataFrame: 因子计算结果，包含RSI值
        """
        # 获取参数
        data = kwargs.get('data')
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        
        if data is None:
            raise ComputeError("必须提供数据")
        
        if not start_date or not end_date:
            raise ComputeError("必须提供开始日期和结束日期")
        
        try:
            # 确保数据不为空
            if data.empty:
                raise DataError("输入数据为空")
            
            # 确保日期格式正确
            date_col = 'trade_date' if 'trade_date' in data.columns else 'date'
            if not pd.api.types.is_datetime64_dtype(data[date_col]):
                data[date_col] = pd.to_datetime(data[date_col])
            
            # 确保价格字段存在
            if self.price_field not in data.columns:
                raise DataError(f"数据中缺少所需价格字段: {self.price_field}")
            
            # 按证券分组计算RSI
            result = pd.DataFrame()
            
            for code, group in data.groupby('ts_code'):
                # 排序，确保数据按日期有序
                group = group.sort_values(date_col)
                
                # 计算价格变化
                price_changes = group[self.price_field].diff()
                
                # 分离上涨和下跌
                gains = price_changes.copy()
                losses = price_changes.copy()
                gains[gains < 0] = 0
                losses[losses > 0] = 0
                losses = abs(losses)  # 转为正值
                
                # 根据不同方法计算RSI
                if self.method == 'wilder':
                    # 威尔德平滑法 (Wilder's Smoothing)
                    # 第一个平均值是简单平均，后续使用平滑公式: prev_avg * (period-1)/period + current/period
                    avg_gain = pd.Series(np.nan, index=gains.index)
                    avg_loss = pd.Series(np.nan, index=losses.index)
                    
                    # 计算第一个平均值
                    if len(gains) >= self.window + 1:
                        first_avg_gain = gains.iloc[1:self.window+1].mean()
                        first_avg_loss = losses.iloc[1:self.window+1].mean()
                        
                        # 设置第一个有效值
                        avg_gain.iloc[self.window] = first_avg_gain
                        avg_loss.iloc[self.window] = first_avg_loss
                        
                        # 使用威尔德平滑公式计算后续值
                        for i in range(self.window + 1, len(gains)):
                            avg_gain.iloc[i] = (avg_gain.iloc[i-1] * (self.window-1) + gains.iloc[i]) / self.window
                            avg_loss.iloc[i] = (avg_loss.iloc[i-1] * (self.window-1) + losses.iloc[i]) / self.window
                    
                elif self.method == 'ema':
                    # 使用指数移动平均法 (EMA)
                    avg_gain = gains.ewm(span=self.window, min_periods=self.window).mean()
                    avg_loss = losses.ewm(span=self.window, min_periods=self.window).mean()
                    
                elif self.method == 'sma':
                    # 使用简单移动平均法 (SMA)
                    avg_gain = gains.rolling(window=self.window, min_periods=self.window).mean()
                    avg_loss = losses.rolling(window=self.window, min_periods=self.window).mean()
                    
                else:
                    raise ComputeError(f"不支持的RSI计算方法: {self.method}")
                
                # 计算相对强度 (RS) 和 RSI
                # 避免除以零错误
                rs = avg_gain / avg_loss.replace(0, np.finfo(float).eps)
                rsi = 100 - (100 / (1 + rs))
                
                # 创建结果DataFrame
                df = pd.DataFrame({
                    'ts_code': code,
                    date_col: group[date_col],
                    self.name: rsi
                })
                
                # 添加到结果集
                result = pd.concat([result, df], ignore_index=True)
            
            # 筛选目标日期范围
            result = result[
                (result[date_col] >= pd.to_datetime(start_date)) & 
                (result[date_col] <= pd.to_datetime(end_date))
            ]
            
            # 设置索引
            result = result.set_index([date_col, 'ts_code'])
            
            return result
            
        except Exception as e:
            if isinstance(e, (ComputeError, DataError)):
                raise
            
            self.logger.error(f"计算RSI失败: {str(e)}")
            raise ComputeError(f"计算RSI失败: {str(e)}")
    
    def get_required_parameters(self) -> List[str]:
        """
        获取必需的计算参数列表
        
        Returns:
            List[str]: 必需参数列表
        """
        return ['data', 'start_date', 'end_date'] 