"""
移动平均因子模块
- 实现简单移动平均 (SMA)
- 实现指数移动平均 (EMA)
- 实现加权移动平均 (WMA)
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union, Optional, Tuple, Set
from datetime import datetime, date

from src.data.storage.storage_factory import StorageFactory
from ..base_factor import BaseFactor, ComputeError, DataError

class MovingAverageFactor(BaseFactor):
    """
    移动平均因子，计算不同类型的移动平均
    """
    
    def __init__(self, name: str = "ma", 
                window: int = 20, 
                ma_type: str = "simple",
                price_field: str = "close",
                description: str = "",
                cache_dir: str = None):
        """
        初始化移动平均因子
        
        Args:
            name: 因子名称，默认为"ma"
            window: 移动平均窗口，默认为20
            ma_type: 移动平均类型，可选 "simple", "exponential", "weighted"
            price_field: 价格字段，默认为"close"
            description: 因子描述
            cache_dir: 缓存目录
        """
        if not description:
            description = f"{ma_type.capitalize()} 移动平均因子 (窗口: {window}, 价格字段: {price_field})"
        
        super().__init__(name, description, "technical", cache_dir)
        
        self.window = window
        self.ma_type = ma_type.lower()
        self.price_field = price_field
        self.name = name  # 明确存储名称为实例变量
        
        # 更新元数据
        self._metadata.update({
            'window': window,
            'ma_type': ma_type,
            'price_field': price_field
        })
    
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        实现移动平均因子的计算逻辑
        
        Args:
            **kwargs: 计算参数
                data: 输入数据DataFrame，必须包含price_field和日期列
                start_date: 开始日期
                end_date: 结束日期
                
        Returns:
            pd.DataFrame: 因子计算结果，包含移动平均值
        """
        # 获取参数
        data = kwargs.get('data')
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        
        if data is None:
            raise ComputeError("必须提供数据")
        
        if not start_date or not end_date:
            raise ComputeError("必须提供开始日期和结束日期")
        
        try:
            # 确保数据不为空
            if data.empty:
                raise DataError("输入数据为空")
            
            # 确保日期格式正确
            date_col = 'trade_date' if 'trade_date' in data.columns else 'date'
            if not pd.api.types.is_datetime64_dtype(data[date_col]):
                data[date_col] = pd.to_datetime(data[date_col])
            
            # 确保价格字段存在
            if self.price_field not in data.columns:
                raise DataError(f"数据中缺少所需价格字段: {self.price_field}")
            
            # 按证券分组计算移动平均
            result = pd.DataFrame()
            
            for code, group in data.groupby('ts_code'):
                # 排序，确保数据按日期有序
                group = group.sort_values(date_col)
                
                # 计算移动平均
                if self.ma_type == "simple":
                    ma_values = group[self.price_field].rolling(window=self.window).mean()
                elif self.ma_type == "exponential":
                    ma_values = group[self.price_field].ewm(span=self.window).mean()
                elif self.ma_type == "weighted":
                    weights = np.arange(1, self.window + 1)
                    ma_values = group[self.price_field].rolling(window=self.window).apply(
                        lambda x: np.sum(weights * x) / weights.sum(), raw=True
                    )
                else:
                    raise ComputeError(f"不支持的移动平均类型: {self.ma_type}")
                
                # 创建结果DataFrame
                df = pd.DataFrame({
                    'ts_code': code,
                    date_col: group[date_col],
                    self.name: ma_values
                })
                
                # 添加到结果集
                result = pd.concat([result, df], ignore_index=True)
            
            # 筛选目标日期范围
            result = result[
                (result[date_col] >= pd.to_datetime(start_date)) & 
                (result[date_col] <= pd.to_datetime(end_date))
            ]
            
            # 设置索引
            result = result.set_index([date_col, 'ts_code'])
            
            return result
            
        except Exception as e:
            if isinstance(e, (ComputeError, DataError)):
                raise
            
            self.logger.error(f"计算移动平均失败: {str(e)}")
            raise ComputeError(f"计算移动平均失败: {str(e)}")
    
    def get_required_parameters(self) -> List[str]:
        """
        获取必需的计算参数列表
        
        Returns:
            List[str]: 必需参数列表
        """
        return ['data', 'start_date', 'end_date']
        
class SMA(MovingAverageFactor):
    """
    简单移动平均因子
    """
    
    def __init__(self, window: int = 20, 
                price_field: str = "close",
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化简单移动平均因子
        
        Args:
            window: 移动平均窗口，默认为20
            price_field: 价格字段，默认为"close"
            name: 因子名称，默认为f"sma_{window}"
            description: 因子描述
            cache_dir: 缓存目录
        """
        if name is None:
            name = f"sma_{window}"
        
        super().__init__(
            name=name,
            window=window,
            ma_type="simple",
            price_field=price_field,
            description=description,
            cache_dir=cache_dir
        )

class EMA(MovingAverageFactor):
    """
    指数移动平均因子
    """
    
    def __init__(self, window: int = 20, 
                price_field: str = "close",
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化指数移动平均因子
        
        Args:
            window: 移动平均窗口，默认为20
            price_field: 价格字段，默认为"close"
            name: 因子名称，默认为f"ema_{window}"
            description: 因子描述
            cache_dir: 缓存目录
        """
        if name is None:
            name = f"ema_{window}"
        
        super().__init__(
            name=name,
            window=window,
            ma_type="exponential",
            price_field=price_field,
            description=description,
            cache_dir=cache_dir
        )

class WMA(MovingAverageFactor):
    """
    加权移动平均因子
    """
    
    def __init__(self, window: int = 20, 
                price_field: str = "close",
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化加权移动平均因子
        
        Args:
            window: 移动平均窗口，默认为20
            price_field: 价格字段，默认为"close"
            name: 因子名称，默认为f"wma_{window}"
            description: 因子描述
            cache_dir: 缓存目录
        """
        if name is None:
            name = f"wma_{window}"
        
        super().__init__(
            name=name,
            window=window,
            ma_type="weighted",
            price_field=price_field,
            description=description,
            cache_dir=cache_dir
        ) 