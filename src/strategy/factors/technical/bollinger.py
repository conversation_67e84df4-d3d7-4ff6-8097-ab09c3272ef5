"""
布林带(Bollinger Bands)因子模块
- 实现布林带技术指标计算
- 提供上轨、中轨、下轨计算
- 支持自定义参数配置
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union, Optional, Tuple, Set
from datetime import datetime, date

from data.storage.storage_factory import StorageFactory
from ..base_factor import BaseFactor, ComputeError, DataError

class BollingerBands(BaseFactor):
    """
    布林带(Bollinger Bands)因子
    
    布林带由三条线组成：
    - 中轨：N日移动平均线
    - 上轨：中轨 + K倍标准差
    - 下轨：中轨 - K倍标准差
    
    布林带可以用来判断价格波动范围和趋势变化：
    - 价格触及或突破上轨：可能超买
    - 价格触及或突破下轨：可能超卖
    - 带宽扩大：波动加剧
    - 带宽收窄：波动减弱，可能即将突破
    """
    
    def __init__(self, 
                window: int = 20, 
                num_std: float = 2.0,
                price_field: str = "close",
                ma_type: str = "simple",  # simple, exponential
                name: str = None,
                description: str = "",
                cache_dir: str = None):
        """
        初始化布林带因子
        
        Args:
            window: 移动平均窗口大小，默认为20
            num_std: 标准差倍数，默认为2.0
            price_field: 价格字段，默认为"close"
            ma_type: 移动平均类型，可选 "simple", "exponential"
            name: 因子名称，默认为f"boll_{window}_{num_std}"
            description: 因子描述
            cache_dir: 缓存目录
        """
        if name is None:
            name = f"boll_{window}_{num_std}".replace('.', '_')
            
        if not description:
            description = f"布林带, 窗口: {window}, 标准差倍数: {num_std}, MA类型: {ma_type}"
            
        super().__init__(name, description, "technical", cache_dir)
        
        self.window = window
        self.num_std = num_std
        self.price_field = price_field
        self.ma_type = ma_type.lower()
        self.name = name  # 明确存储名称为实例变量
        
        # 更新元数据
        self._metadata.update({
            'window': window,
            'num_std': num_std,
            'price_field': price_field,
            'ma_type': ma_type
        })
    
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        实现布林带因子的计算逻辑
        
        Args:
            **kwargs: 计算参数
                data: 输入数据DataFrame，必须包含price_field和日期列
                start_date: 开始日期
                end_date: 结束日期
                
        Returns:
            pd.DataFrame: 因子计算结果，包含上轨、中轨、下轨、带宽和%B值
        """
        # 获取参数
        data = kwargs.get('data')
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        
        if data is None:
            raise ComputeError("必须提供数据")
        
        if not start_date or not end_date:
            raise ComputeError("必须提供开始日期和结束日期")
        
        try:
            # 确保数据不为空
            if data.empty:
                raise DataError("输入数据为空")
            
            # 确保日期格式正确
            date_col = 'trade_date' if 'trade_date' in data.columns else 'date'
            if not pd.api.types.is_datetime64_dtype(data[date_col]):
                data[date_col] = pd.to_datetime(data[date_col])
            
            # 确保价格字段存在
            if self.price_field not in data.columns:
                raise DataError(f"数据中缺少所需价格字段: {self.price_field}")
            
            # 按证券分组计算布林带
            result = pd.DataFrame()
            
            for code, group in data.groupby('ts_code'):
                # 排序，确保数据按日期有序
                group = group.sort_values(date_col)
                
                # 计算中轨（移动平均）
                if self.ma_type == 'simple':
                    middle_band = group[self.price_field].rolling(window=self.window).mean()
                    # 计算移动标准差
                    std = group[self.price_field].rolling(window=self.window).std()
                elif self.ma_type == 'exponential':
                    middle_band = group[self.price_field].ewm(span=self.window, min_periods=self.window).mean()
                    # 计算指数移动标准差
                    # 因为pandas没有直接提供ewmstd，我们需要计算ewmvar的平方根
                    std = np.sqrt(
                        group[self.price_field].ewm(span=self.window, min_periods=self.window).var(bias=False)
                    )
                else:
                    raise ComputeError(f"不支持的移动平均类型: {self.ma_type}")
                
                # 计算上轨和下轨
                upper_band = middle_band + (std * self.num_std)
                lower_band = middle_band - (std * self.num_std)
                
                # 计算带宽 (Bandwidth)
                bandwidth = (upper_band - lower_band) / middle_band * 100
                
                # 计算%B值 (%B) - 价格在带内的相对位置
                percent_b = (group[self.price_field] - lower_band) / (upper_band - lower_band)
                
                # 创建结果DataFrame
                df = pd.DataFrame({
                    'ts_code': code,
                    date_col: group[date_col],
                    f"{self.name}_upper": upper_band,
                    f"{self.name}_middle": middle_band,
                    f"{self.name}_lower": lower_band,
                    f"{self.name}_bandwidth": bandwidth,
                    f"{self.name}_percent_b": percent_b
                })
                
                # 添加到结果集
                result = pd.concat([result, df], ignore_index=True)
            
            # 筛选目标日期范围
            result = result[
                (result[date_col] >= pd.to_datetime(start_date)) & 
                (result[date_col] <= pd.to_datetime(end_date))
            ]
            
            # 设置索引
            result = result.set_index([date_col, 'ts_code'])
            
            return result
            
        except Exception as e:
            if isinstance(e, (ComputeError, DataError)):
                raise
            
            self.logger.error(f"计算布林带失败: {str(e)}")
            raise ComputeError(f"计算布林带失败: {str(e)}")
    
    def get_required_parameters(self) -> List[str]:
        """
        获取必需的计算参数列表
        
        Returns:
            List[str]: 必需参数列表
        """
        return ['data', 'start_date', 'end_date'] 