"""
因子包
- 包含因子接口和基础类
- 提供因子计算和管理功能
"""

from .factor_interface import (
    FactorInterface, 
    FactorException, 
    ComputeError, 
    DependencyError, 
    DataError, 
    LookAheadError
)

from .base_factor import BaseFactor

# 导入技术分析因子
from .technical.moving_average import (
    MovingAverageFactor,
    SMA,
    EMA,
    WMA
)

__all__ = [
    'FactorInterface',
    'BaseFactor',
    'FactorException',
    'ComputeError',
    'DependencyError',
    'DataError',
    'LookAheadError',
    'MovingAverageFactor',
    'SMA',
    'EMA',
    'WMA'
]
