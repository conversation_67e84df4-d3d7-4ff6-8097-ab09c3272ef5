"""
基础因子类
- 实现因子接口
- 提供通用的因子功能
- 支持因子计算、管理和评估
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union, Optional, Tuple, Set
import logging
from datetime import datetime, date
import os
import pickle
from abc import ABC, abstractmethod

from .factor_interface import FactorInterface, ComputeError, DataError, DependencyError
from src.utils.temporary.file_utils import FileUtils

class BaseFactor(FactorInterface):
    """
    基础因子类，实现因子接口，提供通用的因子功能
    """
    
    def __init__(self, name: str, description: str = "", 
                 category: str = "custom", cache_dir: str = None):
        """
        初始化基础因子
        
        Args:
            name: 因子名称
            description: 因子描述
            category: 因子类别，如'technical', 'fundamental', 'sentiment'等
            cache_dir: 因子缓存目录，None表示不缓存
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self._name = name
        self._description = description
        self._category = category
        self._cache_dir = cache_dir
        self._metadata = {
            'name': name,
            'description': description,
            'category': category,
            'class': self.__class__.__name__,
            'created_at': datetime.now().isoformat()
        }
        self._factor_data = None  # 因子计算结果缓存
    
    def compute(self, **kwargs) -> pd.DataFrame:
        """
        计算因子值
        
        Args:
            **kwargs: 计算参数，可能包括：
                start_date: 开始日期
                end_date: 结束日期
                securities: 证券列表
                context: 计算上下文
                use_cache: 是否使用缓存
                
        Returns:
            pd.DataFrame: 因子计算结果，索引为日期和证券代码，列为因子值
            
        Raises:
            ComputeError: 计算失败时抛出
            DataError: 数据错误时抛出
            DependencyError: 依赖错误时抛出
        """
        try:
            # 检查参数
            self.check_parameters(**kwargs)
            
            # 是否使用缓存
            use_cache = kwargs.get('use_cache', True)
            if use_cache and self._factor_data is not None:
                return self._factor_data
            
            # 尝试从文件加载缓存
            if use_cache and self._cache_dir and self._try_load_from_cache(**kwargs):
                return self._factor_data
            
            # 执行计算
            self._factor_data = self._compute_impl(**kwargs)
            
            # 验证结果
            if self._factor_data is None or self._factor_data.empty:
                raise ComputeError(f"因子 {self._name} 计算结果为空")
            
            # 保存缓存
            if use_cache and self._cache_dir:
                self._save_to_cache(**kwargs)
            
            return self._factor_data
            
        except Exception as e:
            if isinstance(e, (ComputeError, DataError, DependencyError)):
                raise
            
            self.logger.error(f"因子 {self._name} 计算失败: {str(e)}")
            raise ComputeError(f"因子 {self._name} 计算失败: {str(e)}")
    
    @abstractmethod
    def _compute_impl(self, **kwargs) -> pd.DataFrame:
        """
        实现具体的因子计算逻辑
        
        Args:
            **kwargs: 计算参数
            
        Returns:
            pd.DataFrame: 因子计算结果
        """
        pass
    
    def get_history(self, start_date: Union[str, datetime, date], 
                   end_date: Union[str, datetime, date], 
                   securities: List[str] = None) -> pd.DataFrame:
        """
        获取因子历史数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            securities: 证券列表，为None则获取所有证券
            
        Returns:
            pd.DataFrame: 因子历史数据，索引为日期和证券代码，列为因子值
            
        Raises:
            DataError: 数据错误时抛出
        """
        try:
            if self._factor_data is None:
                # 如果尚未计算，先计算因子
                self.compute(start_date=start_date, end_date=end_date, 
                             securities=securities)
            
            if self._factor_data is None or self._factor_data.empty:
                raise DataError(f"因子 {self._name} 没有历史数据")
            
            # 创建一个DataFrame的副本
            history = self._factor_data.copy()
            
            # 转换日期格式
            if isinstance(start_date, str):
                start_date = pd.to_datetime(start_date)
            if isinstance(end_date, str):
                end_date = pd.to_datetime(end_date)
            
            # 过滤日期范围
            if isinstance(history.index, pd.MultiIndex):
                # 多级索引
                date_level = history.index.names.index('date')
                idx = pd.IndexSlice
                history = history.loc[idx[:, (history.index.get_level_values(date_level) >= start_date) & 
                                        (history.index.get_level_values(date_level) <= end_date)], :]
            else:
                # 单级索引
                history = history[(history.index >= start_date) & (history.index <= end_date)]
            
            # 过滤证券
            if securities is not None and len(securities) > 0:
                if isinstance(history.index, pd.MultiIndex):
                    # 多级索引
                    sec_level = history.index.names.index('asset')
                    idx = pd.IndexSlice
                    history = history.loc[idx[history.index.get_level_values(sec_level).isin(securities), :], :]
                else:
                    # 单列索引，证券代码在列中
                    if 'asset' in history.columns:
                        history = history[history['asset'].isin(securities)]
            
            return history
            
        except Exception as e:
            if isinstance(e, DataError):
                raise
            
            self.logger.error(f"获取因子 {self._name} 历史数据失败: {str(e)}")
            raise DataError(f"获取因子 {self._name} 历史数据失败: {str(e)}")
    
    def get_dependencies(self) -> List[str]:
        """
        获取因子依赖的其他因子列表
        
        Returns:
            List[str]: 依赖因子名称列表
        """
        # 基础因子可能没有依赖，子类可以覆盖此方法
        return []
    
    def get_metadata(self) -> Dict[str, Any]:
        """
        获取因子元数据
        
        Returns:
            Dict[str, Any]: 因子元数据，包括名称、描述、创建时间等
        """
        return self._metadata
    
    def get_name(self) -> str:
        """
        获取因子名称
        
        Returns:
            str: 因子名称
        """
        return self._name
    
    def get_description(self) -> str:
        """
        获取因子描述
        
        Returns:
            str: 因子描述
        """
        return self._description
    
    def get_required_parameters(self) -> List[str]:
        """
        获取必需的计算参数列表
        
        Returns:
            List[str]: 必需参数列表
        """
        return ['start_date', 'end_date']
    
    def _try_load_from_cache(self, **kwargs) -> bool:
        """
        尝试从缓存加载因子数据
        
        Args:
            **kwargs: 计算参数
            
        Returns:
            bool: 是否成功加载
        """
        if not self._cache_dir:
            return False
        
        try:
            # 构建缓存文件路径
            cache_file = self._get_cache_file_path(**kwargs)
            
            if not os.path.exists(cache_file):
                return False
            
            # 加载缓存
            with open(cache_file, 'rb') as f:
                self._factor_data = pickle.load(f)
            
            return True
            
        except Exception as e:
            self.logger.warning(f"加载因子 {self._name} 缓存失败: {str(e)}")
            return False
    
    def _save_to_cache(self, **kwargs) -> bool:
        """
        保存因子数据到缓存
        
        Args:
            **kwargs: 计算参数
            
        Returns:
            bool: 是否成功保存
        """
        if not self._cache_dir or self._factor_data is None or self._factor_data.empty:
            return False
        
        try:
            # 确保缓存目录存在
            os.makedirs(self._cache_dir, exist_ok=True)
            
            # 构建缓存文件路径
            cache_file = self._get_cache_file_path(**kwargs)
            
            # 保存缓存
            with open(cache_file, 'wb') as f:
                pickle.dump(self._factor_data, f)
            
            return True
            
        except Exception as e:
            self.logger.warning(f"保存因子 {self._name} 缓存失败: {str(e)}")
            return False
    
    def _get_cache_file_path(self, **kwargs) -> str:
        """
        获取缓存文件路径
        
        Args:
            **kwargs: 计算参数
            
        Returns:
            str: 缓存文件路径
        """
        # 获取关键参数
        start_date = kwargs.get('start_date', '')
        end_date = kwargs.get('end_date', '')
        
        # 构建文件名
        file_name = f"{self._name}_{start_date}_{end_date}.pkl"
        return os.path.join(self._cache_dir, file_name)
    
    def clear_cache(self) -> None:
        """
        清除因子缓存
        """
        self._factor_data = None
        
        if not self._cache_dir:
            return
        
        try:
            # 查找所有与此因子相关的缓存文件
            if os.path.exists(self._cache_dir):
                for file_name in os.listdir(self._cache_dir):
                    if file_name.startswith(f"{self._name}_") and file_name.endswith(".pkl"):
                        FileUtils.remove_file(os.path.join(self._cache_dir, file_name))
        except Exception as e:
            self.logger.warning(f"清除因子 {self._name} 缓存失败: {str(e)}") 