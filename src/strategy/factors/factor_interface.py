"""
因子接口抽象类：定义所有因子的标准接口
- 提供统一的因子计算方法
- 支持因子历史数据获取
- 定义因子元数据访问
- 支持因子依赖管理
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Set, Tuple, TypeVar, Generic, Callable
import pandas as pd
import numpy as np
from datetime import datetime, date

# 泛型参数定义
T = TypeVar('T')

class FactorException(Exception):
    """因子异常的基类"""
    pass

class ComputeError(FactorException):
    """计算异常"""
    pass

class DependencyError(FactorException):
    """依赖错误"""
    pass

class DataError(FactorException):
    """数据错误"""
    pass

class LookAheadError(FactorException):
    """前瞻偏差错误"""
    pass

class FactorInterface(ABC):
    """
    因子接口抽象类，所有因子必须实现此接口
    定义了因子的基本操作，包括计算、获取历史数据等
    """
    
    @abstractmethod
    def compute(self, **kwargs) -> pd.DataFrame:
        """
        计算因子值
        
        参数：
            **kwargs: 计算参数，可能包括：
                start_date: 开始日期
                end_date: 结束日期
                securities: 证券列表
                context: 计算上下文
                
        返回：
            pd.DataFrame: 因子计算结果，索引为日期和证券代码，列为因子值
            
        异常：
            ComputeError: 计算失败时抛出
            DataError: 数据错误时抛出
            DependencyError: 依赖错误时抛出
        """
        pass
    
    @abstractmethod
    def get_history(self, start_date: Union[str, datetime, date], 
                   end_date: Union[str, datetime, date], 
                   securities: List[str] = None) -> pd.DataFrame:
        """
        获取因子历史数据
        
        参数：
            start_date: 开始日期
            end_date: 结束日期
            securities: 证券列表，为None则获取所有证券
            
        返回：
            pd.DataFrame: 因子历史数据，索引为日期和证券代码，列为因子值
            
        异常：
            DataError: 数据错误时抛出
        """
        pass
    
    @abstractmethod
    def get_dependencies(self) -> List[str]:
        """
        获取因子依赖的其他因子列表
        
        返回：
            List[str]: 依赖因子名称列表
        """
        pass
    
    @abstractmethod
    def get_metadata(self) -> Dict[str, Any]:
        """
        获取因子元数据
        
        返回：
            Dict[str, Any]: 因子元数据，包括名称、描述、创建时间等
        """
        pass
    
    def get_name(self) -> str:
        """
        获取因子名称
        
        返回：
            str: 因子名称
        """
        return self.get_metadata().get('name', self.__class__.__name__)
    
    def get_description(self) -> str:
        """
        获取因子描述
        
        返回：
            str: 因子描述
        """
        return self.get_metadata().get('description', '')
    
    def check_parameters(self, **kwargs) -> bool:
        """
        检查计算参数是否有效
        
        参数：
            **kwargs: 计算参数
            
        返回：
            bool: 参数是否有效
            
        异常：
            ValueError: 参数无效时抛出
        """
        required_params = self.get_required_parameters()
        for param in required_params:
            if param not in kwargs:
                raise ValueError(f"缺少必需参数: {param}")
        return True
    
    def get_required_parameters(self) -> List[str]:
        """
        获取必需的计算参数列表
        
        返回：
            List[str]: 必需参数列表
        """
        return []
    
    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        预处理数据
        
        参数：
            data: 输入数据
            
        返回：
            pd.DataFrame: 预处理后的数据
            
        异常：
            DataError: 数据预处理失败时抛出
        """
        try:
            # 默认实现：处理缺失值
            return data.fillna(method='ffill').fillna(0)
        except Exception as e:
            raise DataError(f"数据预处理失败: {str(e)}") from e
    
    def check_for_look_ahead_bias(self, data: pd.DataFrame, 
                                 current_date: Union[str, datetime, date]) -> bool:
        """
        检查是否存在前瞻偏差
        
        参数：
            data: 输入数据
            current_date: 当前日期
            
        返回：
            bool: 是否不存在前瞻偏差
            
        异常：
            LookAheadError: 存在前瞻偏差时抛出
        """
        if isinstance(current_date, str):
            current_date = pd.to_datetime(current_date)
        
        # 检查数据日期是否都早于或等于当前日期
        data_dates = pd.to_datetime(data.index.get_level_values('date') 
                                     if isinstance(data.index, pd.MultiIndex) 
                                     else data.index)
        
        if any(date > current_date for date in data_dates):
            future_dates = [date for date in data_dates if date > current_date]
            raise LookAheadError(f"存在前瞻偏差: 使用了{future_dates}的未来数据")
        
        return True
    
    def normalize(self, factor_data: pd.DataFrame, method: str = 'zscore') -> pd.DataFrame:
        """
        标准化因子值
        
        参数：
            factor_data: 因子数据
            method: 标准化方法，可选值：
                'zscore': Z-Score标准化
                'rank': 排序标准化
                'min_max': 最小-最大标准化
                
        返回：
            pd.DataFrame: 标准化后的因子数据
            
        异常：
            ValueError: 无效的标准化方法
            ComputeError: 标准化失败时抛出
        """
        try:
            if method == 'zscore':
                # Z-Score标准化: (x - mean) / std
                return (factor_data - factor_data.mean()) / factor_data.std()
            elif method == 'rank':
                # 排序标准化: 转换为排名百分比
                return factor_data.rank(pct=True)
            elif method == 'min_max':
                # 最小-最大标准化: (x - min) / (max - min)
                min_val = factor_data.min()
                max_val = factor_data.max()
                return (factor_data - min_val) / (max_val - min_val)
            else:
                raise ValueError(f"无效的标准化方法: {method}")
        except Exception as e:
            if isinstance(e, ValueError):
                raise
            raise ComputeError(f"因子标准化失败: {str(e)}") from e
    
    def winsorize(self, factor_data: pd.DataFrame, 
                 lower_bound: float = 0.025, 
                 upper_bound: float = 0.975) -> pd.DataFrame:
        """
        因子去极值处理
        
        参数：
            factor_data: 因子数据
            lower_bound: 下界分位数（0-1之间）
            upper_bound: 上界分位数（0-1之间）
            
        返回：
            pd.DataFrame: 去极值后的因子数据
            
        异常：
            ValueError: 分位数取值无效时抛出
            ComputeError: 去极值处理失败时抛出
        """
        try:
            if not (0 <= lower_bound < upper_bound <= 1):
                raise ValueError("分位数必须在0-1之间，且下界必须小于上界")
            
            # 计算分位数
            lower_value = factor_data.quantile(lower_bound)
            upper_value = factor_data.quantile(upper_bound)
            
            # 去极值
            return factor_data.clip(lower=lower_value, upper=upper_value)
        except Exception as e:
            if isinstance(e, ValueError):
                raise
            raise ComputeError(f"因子去极值处理失败: {str(e)}") from e
    
    def is_valid(self) -> bool:
        """
        检查因子是否有效
        
        返回：
            bool: 因子是否有效
        """
        # 检查元数据是否存在必需字段
        try:
            metadata = self.get_metadata()
            if 'name' not in metadata:
                return False
            
            # 检查依赖因子列表是否有效
            dependencies = self.get_dependencies()
            if not isinstance(dependencies, list):
                return False
            
            return True
        except Exception:
            return False
