"""
价值策略模块：基于价值因子的选股策略实现
- 使用PE、PB等估值因子选择低估值股票
- 支持多因子组合和权重优化
- 提供参数配置和自定义选项
"""

from typing import Dict, List, Any, Union, Optional, Tuple, Set
import pandas as pd
import numpy as np
from datetime import datetime, date

from ..base_strategy import BaseStrategy
from ..strategy_interface import StrategyRunError, StrategyParameterError
from ...factors.fundamental import PEFactor, PBFactor, PSFactor, DividendYieldFactor

class ValueStrategy(BaseStrategy):
    """
    价值策略：基于价值因子的选股策略
    
    策略逻辑：
    1. 使用PE、PB等估值因子，选择低估值的股票
    2. 支持多种权重计算方法
    3. 可设置持仓数量限制和再平衡周期
    
    参数：
    - value_factors: 使用的价值因子列表，支持 'pe', 'pb', 'ps', 'dividend_yield'
    - factor_weights: 各因子的权重，默认等权重
    - rebalance_freq: 再平衡频率，支持 'daily', 'weekly', 'monthly', 'quarterly'
    - top_n: 选择的股票数量
    - weighting_scheme: 权重计算方法，支持 'equal', 'market_cap', 'factor_weight', 'factor_score'
    """
    
    # 支持的因子类型
    VALID_FACTORS = {'pe', 'pb', 'ps', 'dividend_yield'}
    
    # 支持的再平衡频率
    VALID_REBALANCE_FREQ = {'daily', 'weekly', 'monthly', 'quarterly'}
    
    # 支持的权重计算方法
    VALID_WEIGHTING_SCHEMES = {'equal', 'market_cap', 'factor_weight', 'factor_score'}
    
    def __init__(self, 
                name: str = None,
                description: str = "基于价值因子的选股策略",
                value_factors: List[str] = None,
                factor_weights: Dict[str, float] = None,
                rebalance_freq: str = 'monthly',
                top_n: int = 20,
                weighting_scheme: str = 'equal',
                **kwargs):
        """
        初始化价值策略
        
        参数：
            name: 策略名称，默认为类名
            description: 策略描述
            value_factors: 使用的价值因子列表，默认使用 PE 和 PB
            factor_weights: 各因子的权重，默认等权重
            rebalance_freq: 再平衡频率，默认为月度再平衡
            top_n: 选择的股票数量，默认为20
            weighting_scheme: 权重计算方法，默认为等权重
            **kwargs: 其他参数
        """
        if name is None:
            name = "ValueStrategy"
            
        # 设置默认值
        if value_factors is None:
            value_factors = ['pe', 'pb']
            
        # 验证参数
        self._validate_factors(value_factors)
        
        if rebalance_freq not in self.VALID_REBALANCE_FREQ:
            raise ValueError(f"不支持的再平衡频率: {rebalance_freq}，支持的频率包括: {', '.join(self.VALID_REBALANCE_FREQ)}")
        
        if weighting_scheme not in self.VALID_WEIGHTING_SCHEMES:
            raise ValueError(f"不支持的权重计算方法: {weighting_scheme}，支持的方法包括: {', '.join(self.VALID_WEIGHTING_SCHEMES)}")
        
        # 设置参数
        self.value_factors = value_factors
        self.factor_weights = self._normalize_weights(factor_weights)
        self.rebalance_freq = rebalance_freq
        self.top_n = top_n
        self.weighting_scheme = weighting_scheme
        
        # 再平衡状态
        self.last_rebalance_date = None
        self.next_rebalance_date = None
        
        super().__init__(name=name, description=description, **kwargs)
        
    def _validate_factors(self, factors: List[str]) -> None:
        """
        验证因子列表
        
        参数：
            factors: 因子列表
            
        异常：
            ValueError: 因子无效时抛出
        """
        if not factors:
            raise ValueError("因子列表不能为空")
            
        for factor in factors:
            if factor not in self.VALID_FACTORS:
                raise ValueError(f"不支持的因子: {factor}，支持的因子包括: {', '.join(self.VALID_FACTORS)}")
    
    def _normalize_weights(self, weights: Dict[str, float]) -> Dict[str, float]:
        """
        归一化因子权重
        
        参数：
            weights: 权重字典
            
        返回：
            Dict[str, float]: 归一化后的权重字典
        """
        if weights is None:
            # 如果未提供权重，则使用等权重
            weights = {factor: 1.0 for factor in self.value_factors}
        else:
            # 确保所有因子都有权重
            for factor in self.value_factors:
                if factor not in weights:
                    weights[factor] = 1.0
        
        # 归一化
        total_weight = sum(weights.values())
        if total_weight > 0:
            for factor in weights:
                weights[factor] /= total_weight
                
        return weights
    
    def _initialize_impl(self, **kwargs) -> None:
        """
        策略特定的初始化逻辑
        
        参数：
            **kwargs: 初始化参数
        """
        # 设置初始再平衡日期
        self.last_rebalance_date = None
        self.next_rebalance_date = self.start_date
    
    def _initialize_factors(self) -> None:
        """
        初始化策略依赖的因子
        """
        # 创建因子实例
        if 'pe' in self.value_factors:
            pe_factor = PEFactor(pe_type="ttm", name="pe_ttm")
            self.add_factor('pe', pe_factor)
            
        if 'pb' in self.value_factors:
            pb_factor = PBFactor(name="pb")
            self.add_factor('pb', pb_factor)
            
        if 'ps' in self.value_factors:
            ps_factor = PSFactor(ps_type="ttm", name="ps_ttm")
            self.add_factor('ps', ps_factor)
            
        if 'dividend_yield' in self.value_factors:
            dividend_factor = DividendYieldFactor(avg_period=3, name="dividend_yield")
            self.add_factor('dividend_yield', dividend_factor)
    
    def _before_trading_start_impl(self, context: Dict[str, Any]) -> None:
        """
        交易前准备的具体实现
        
        参数：
            context: 上下文信息
        """
        current_date = context.get('current_date')
        
        # 检查是否需要再平衡
        if self._is_rebalance_day(current_date):
            self.logger.info(f"今日 {current_date} 为再平衡日，将更新持仓")
            context['rebalance_today'] = True
            self.last_rebalance_date = current_date
            self.next_rebalance_date = self._calculate_next_rebalance_date(current_date)
        else:
            context['rebalance_today'] = False
    
    def _is_rebalance_day(self, current_date: str) -> bool:
        """
        判断当前日期是否为再平衡日
        
        参数：
            current_date: 当前日期
            
        返回：
            bool: 是否为再平衡日
        """
        # 如果是第一个交易日，直接再平衡
        if self.last_rebalance_date is None:
            return True
            
        # 检查是否到达下一个再平衡日期
        return current_date >= self.next_rebalance_date
    
    def _calculate_next_rebalance_date(self, current_date: str) -> str:
        """
        计算下一个再平衡日期
        
        参数：
            current_date: 当前日期
            
        返回：
            str: 下一个再平衡日期
        """
        # 这里简化处理，实际应根据交易日历计算
        current_dt = datetime.strptime(current_date, '%Y-%m-%d')
        
        if self.rebalance_freq == 'daily':
            # 下一个交易日，简化处理
            next_dt = current_dt.replace(day=current_dt.day + 1)
        elif self.rebalance_freq == 'weekly':
            # 下周同一天
            next_dt = current_dt.replace(day=current_dt.day + 7)
        elif self.rebalance_freq == 'monthly':
            # 下个月同一天
            if current_dt.month == 12:
                next_dt = current_dt.replace(year=current_dt.year + 1, month=1)
            else:
                next_dt = current_dt.replace(month=current_dt.month + 1)
        elif self.rebalance_freq == 'quarterly':
            # 下个季度同一天
            month = current_dt.month
            if month in [1, 2, 3]:
                next_dt = current_dt.replace(month=4)
            elif month in [4, 5, 6]:
                next_dt = current_dt.replace(month=7)
            elif month in [7, 8, 9]:
                next_dt = current_dt.replace(month=10)
            else:
                next_dt = current_dt.replace(year=current_dt.year + 1, month=1)
        
        return next_dt.strftime('%Y-%m-%d')
    
    def _generate_signals_impl(self, context: Dict[str, Any], data: pd.DataFrame) -> pd.DataFrame:
        """
        生成信号的具体实现
        
        参数：
            context: 上下文信息
            data: 市场数据
            
        返回：
            pd.DataFrame: 交易信号
        """
        # 如果今天不是再平衡日，返回空信号
        if not context.get('rebalance_today', False):
            return pd.DataFrame(index=self.universe, columns=['signal']).fillna(0)
        
        # 获取因子数据
        factor_values = {}
        for factor_name in self.value_factors:
            factor_data = context.get('factors', {}).get(factor_name)
            if factor_data is None or factor_data.empty:
                self.logger.warning(f"未找到因子 {factor_name} 的数据")
                continue
                
            # 提取因子值，处理多级索引
            if isinstance(factor_data.index, pd.MultiIndex):
                # 转换多级索引为单级索引DataFrame
                factor_values[factor_name] = factor_data.reset_index(level=0, drop=True)
            else:
                factor_values[factor_name] = factor_data
        
        # 如果没有因子数据，返回空信号
        if not factor_values:
            self.logger.warning("没有可用的因子数据")
            return pd.DataFrame(index=self.universe, columns=['signal']).fillna(0)
        
        # 计算综合分数
        scores = self._calculate_factor_scores(factor_values)
        
        # 选择前N名股票
        selected_stocks = scores.nlargest(self.top_n, 'score')
        
        # 生成信号
        signals = pd.DataFrame(index=self.universe, columns=['signal']).fillna(0)
        signals.loc[selected_stocks.index, 'signal'] = 1
        
        return signals
    
    def _calculate_factor_scores(self, factor_values: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        计算因子综合得分
        
        参数：
            factor_values: 因子值字典
            
        返回：
            pd.DataFrame: 因子得分
        """
        # 标准化因子值
        normalized_factors = {}
        for factor_name, factor_df in factor_values.items():
            # 对于估值因子，值越小越好
            if factor_name in ['pe', 'pb', 'ps']:
                # 处理异常值
                valid_values = factor_df[factor_df > 0]
                if not valid_values.empty:
                    # 倒数变换，使得小值对应高分
                    normalized = 1 / valid_values
                    # z-score标准化
                    mean = normalized.mean()
                    std = normalized.std()
                    if std > 0:
                        normalized = (normalized - mean) / std
                        normalized_factors[factor_name] = normalized
            # 对于股息率，值越大越好
            elif factor_name == 'dividend_yield':
                # 处理异常值
                valid_values = factor_df[factor_df >= 0]
                if not valid_values.empty:
                    # z-score标准化
                    mean = valid_values.mean()
                    std = valid_values.std()
                    if std > 0:
                        normalized = (valid_values - mean) / std
                        normalized_factors[factor_name] = normalized
        
        # 如果没有有效的标准化因子值，返回空结果
        if not normalized_factors:
            return pd.DataFrame(columns=['score'])
        
        # 组合计算得分
        scores = pd.DataFrame(index=self.universe, columns=['score']).fillna(0)
        
        for factor_name, normalized_factor in normalized_factors.items():
            weight = self.factor_weights.get(factor_name, 1.0 / len(self.value_factors))
            scores.loc[normalized_factor.index, 'score'] += normalized_factor * weight
        
        return scores
    
    def _generate_weights_impl(self, context: Dict[str, Any], signals: pd.DataFrame) -> pd.DataFrame:
        """
        生成权重的具体实现
        
        参数：
            context: 上下文信息
            signals: 交易信号
            
        返回：
            pd.DataFrame: 目标持仓权重
        """
        # 获取有信号的股票
        selected_stocks = signals[signals['signal'] > 0].index.tolist()
        
        # 如果没有选中的股票，返回空持仓
        if not selected_stocks:
            return pd.DataFrame(index=self.universe, columns=['weight']).fillna(0)
        
        # 根据权重计算方法，生成权重
        if self.weighting_scheme == 'equal':
            # 等权重
            weights = pd.DataFrame(index=self.universe, columns=['weight']).fillna(0)
            weights.loc[selected_stocks, 'weight'] = 1.0 / len(selected_stocks)
            
        elif self.weighting_scheme == 'market_cap':
            # 按市值加权
            weights = self._calculate_market_cap_weights(context, selected_stocks)
            
        elif self.weighting_scheme == 'factor_weight':
            # 按因子权重加权
            weights = self._calculate_factor_weighted_weights(context, selected_stocks)
            
        elif self.weighting_scheme == 'factor_score':
            # 按因子得分加权
            weights = self._calculate_factor_score_weights(context, selected_stocks)
            
        else:
            # 默认等权重
            weights = pd.DataFrame(index=self.universe, columns=['weight']).fillna(0)
            weights.loc[selected_stocks, 'weight'] = 1.0 / len(selected_stocks)
        
        return weights
    
    def _calculate_market_cap_weights(self, context: Dict[str, Any], selected_stocks: List[str]) -> pd.DataFrame:
        """
        计算市值加权权重
        
        参数：
            context: 上下文信息
            selected_stocks: 选中的股票列表
            
        返回：
            pd.DataFrame: 权重
        """
        # 获取市值数据
        market_caps = context.get('market_caps')
        if market_caps is None:
            self.logger.warning("未找到市值数据，使用等权重")
            weights = pd.DataFrame(index=self.universe, columns=['weight']).fillna(0)
            weights.loc[selected_stocks, 'weight'] = 1.0 / len(selected_stocks)
            return weights
        
        # 计算市值权重
        selected_caps = market_caps.loc[selected_stocks]
        total_cap = selected_caps.sum()
        
        weights = pd.DataFrame(index=self.universe, columns=['weight']).fillna(0)
        if total_cap > 0:
            weights.loc[selected_stocks, 'weight'] = selected_caps / total_cap
            
        return weights
    
    def _calculate_factor_weighted_weights(self, context: Dict[str, Any], selected_stocks: List[str]) -> pd.DataFrame:
        """
        计算因子加权权重
        
        参数：
            context: 上下文信息
            selected_stocks: 选中的股票列表
            
        返回：
            pd.DataFrame: 权重
        """
        # 获取因子数据
        factor_values = {}
        for factor_name in self.value_factors:
            factor_data = context.get('factors', {}).get(factor_name)
            if factor_data is None or factor_data.empty:
                continue
                
            # 提取因子值，处理多级索引
            if isinstance(factor_data.index, pd.MultiIndex):
                # 转换多级索引为单级索引DataFrame
                factor_values[factor_name] = factor_data.reset_index(level=0, drop=True)
            else:
                factor_values[factor_name] = factor_data
        
        # 如果没有因子数据，使用等权重
        if not factor_values:
            self.logger.warning("未找到因子数据，使用等权重")
            weights = pd.DataFrame(index=self.universe, columns=['weight']).fillna(0)
            weights.loc[selected_stocks, 'weight'] = 1.0 / len(selected_stocks)
            return weights
        
        # 计算因子加权权重
        weights = pd.DataFrame(index=self.universe, columns=['weight']).fillna(0)
        
        # 使用第一个因子的逆权重
        if 'pe' in factor_values:
            factor_df = factor_values['pe']
            factor_df = factor_df[factor_df > 0]  # 只使用正PE
            selected_factors = factor_df.loc[factor_df.index.intersection(selected_stocks)]
            
            if not selected_factors.empty:
                # 计算逆权重
                inverse_factor = 1 / selected_factors
                total_inverse = inverse_factor.sum()
                
                if total_inverse > 0:
                    for stock in selected_factors.index:
                        weights.loc[stock, 'weight'] = (1 / selected_factors.loc[stock]) / total_inverse
        
        # 如果没有计算出权重，使用等权重
        if weights['weight'].sum() == 0:
            weights.loc[selected_stocks, 'weight'] = 1.0 / len(selected_stocks)
            
        return weights
    
    def _calculate_factor_score_weights(self, context: Dict[str, Any], selected_stocks: List[str]) -> pd.DataFrame:
        """
        计算因子得分加权权重
        
        参数：
            context: 上下文信息
            selected_stocks: 选中的股票列表
            
        返回：
            pd.DataFrame: 权重
        """
        # 获取因子数据
        factor_values = {}
        for factor_name in self.value_factors:
            factor_data = context.get('factors', {}).get(factor_name)
            if factor_data is None or factor_data.empty:
                continue
                
            # 提取因子值，处理多级索引
            if isinstance(factor_data.index, pd.MultiIndex):
                # 转换多级索引为单级索引DataFrame
                factor_values[factor_name] = factor_data.reset_index(level=0, drop=True)
            else:
                factor_values[factor_name] = factor_data
        
        # 如果没有因子数据，使用等权重
        if not factor_values:
            self.logger.warning("未找到因子数据，使用等权重")
            weights = pd.DataFrame(index=self.universe, columns=['weight']).fillna(0)
            weights.loc[selected_stocks, 'weight'] = 1.0 / len(selected_stocks)
            return weights
        
        # 计算因子得分
        scores = self._calculate_factor_scores(factor_values)
        
        # 选择已选中的股票的得分
        selected_scores = scores.loc[scores.index.intersection(selected_stocks)]
        
        # 计算得分权重
        weights = pd.DataFrame(index=self.universe, columns=['weight']).fillna(0)
        
        if not selected_scores.empty:
            total_score = selected_scores['score'].sum()
            if total_score != 0:
                for stock in selected_scores.index:
                    weights.loc[stock, 'weight'] = selected_scores.loc[stock, 'score'] / total_score
        
        # 如果没有计算出权重，使用等权重
        if weights['weight'].sum() == 0:
            weights.loc[selected_stocks, 'weight'] = 1.0 / len(selected_stocks)
            
        return weights
    
    def _generate_orders_impl(self, context: Dict[str, Any], weights: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        生成订单的具体实现
        
        参数：
            context: 上下文信息
            weights: 目标持仓权重
            
        返回：
            List[Dict[str, Any]]: 订单列表
        """
        # 如果今天不是再平衡日，不产生订单
        if not context.get('rebalance_today', False):
            return []
        
        # 获取当前持仓
        current_positions = context.get('positions', {})
        
        # 计算目标持仓金额
        portfolio_value = context.get('portfolio_value', self.capital)
        target_positions = {}
        for stock, row in weights.iterrows():
            if row['weight'] > 0:
                target_positions[stock] = row['weight'] * portfolio_value
        
        # 生成订单
        orders = []
        
        # 先处理卖出订单
        for stock in current_positions:
            current_value = current_positions[stock]['value']
            target_value = target_positions.get(stock, 0)
            
            if target_value < current_value:
                # 需要卖出
                orders.append({
                    'security_id': stock,
                    'direction': 'SELL',
                    'amount': current_value - target_value,
                    'reason': '再平衡卖出'
                })
        
        # 再处理买入订单
        for stock, target_value in target_positions.items():
            current_value = current_positions.get(stock, {}).get('value', 0)
            
            if target_value > current_value:
                # 需要买入
                orders.append({
                    'security_id': stock,
                    'direction': 'BUY',
                    'amount': target_value - current_value,
                    'reason': '再平衡买入'
                })
        
        return orders
    
    def _apply_parameters(self) -> None:
        """
        应用策略参数
        """
        # 从参数中获取并设置
        for key, value in self.parameters.items():
            if key == 'value_factors':
                self._validate_factors(value)
                self.value_factors = value
            elif key == 'factor_weights':
                self.factor_weights = self._normalize_weights(value)
            elif key == 'rebalance_freq':
                if value not in self.VALID_REBALANCE_FREQ:
                    raise StrategyParameterError(f"不支持的再平衡频率: {value}")
                self.rebalance_freq = value
            elif key == 'top_n':
                if not isinstance(value, int) or value <= 0:
                    raise StrategyParameterError(f"top_n必须是正整数: {value}")
                self.top_n = value
            elif key == 'weighting_scheme':
                if value not in self.VALID_WEIGHTING_SCHEMES:
                    raise StrategyParameterError(f"不支持的权重计算方法: {value}")
                self.weighting_scheme = value
    
    def _get_data_requirements(self) -> Dict[str, Any]:
        """
        获取策略特定的数据需求
        
        返回：
            Dict[str, Any]: 数据需求字典
        """
        return {
            'fundamental_data': {
                'frequency': 'quarterly',
                'fields': ['pe_ratio', 'pb_ratio', 'ps_ratio', 'dividend_yield']
            },
            'market_data': {
                'frequency': self.frequency,
                'fields': ['open', 'high', 'low', 'close', 'volume', 'market_cap']
            }
        }
        
# 注册策略
from ..base_strategy import StrategyFactory
StrategyFactory.register('value', ValueStrategy) 