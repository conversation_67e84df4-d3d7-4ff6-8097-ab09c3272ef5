"""
策略接口定义模块：提供策略开发的标准接口
- 定义策略生命周期方法
- 定义信号生成接口
- 定义仓位管理接口
- 提供策略回测和评估接口
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Union, Optional, Tuple, Set
from datetime import datetime, date
import pandas as pd
import numpy as np

class StrategyException(Exception):
    """策略异常的基类"""
    pass

class StrategyInitError(StrategyException):
    """策略初始化错误"""
    pass

class StrategyRunError(StrategyException):
    """策略运行错误"""
    pass

class StrategyParameterError(StrategyException):
    """策略参数错误"""
    pass

class StrategyInterface(ABC):
    """
    策略接口抽象类，所有策略必须实现此接口
    定义了策略的生命周期方法和核心功能
    """
    
    @abstractmethod
    def initialize(self, **kwargs) -> None:
        """
        初始化策略，设置策略参数和依赖
        
        参数：
            **kwargs: 初始化参数，可能包括：
                start_date: 回测开始日期
                end_date: 回测结束日期
                universe: 股票池
                benchmark: 基准指数
                frequency: 策略频率（'daily', 'weekly', 'monthly'）
                capital: 初始资金
                
        异常：
            StrategyInitError: 初始化失败时抛出
            StrategyParameterError: 参数错误时抛出
        """
        pass
    
    @abstractmethod
    def before_trading_start(self, context: Dict[str, Any]) -> None:
        """
        在每个交易日开始前调用
        用于准备当日交易所需的数据和状态
        
        参数：
            context: 上下文信息，包含当前日期、持仓等信息
            
        异常：
            StrategyRunError: 执行失败时抛出
        """
        pass
    
    @abstractmethod
    def handle_data(self, context: Dict[str, Any], data: pd.DataFrame) -> Dict[str, Any]:
        """
        处理每个bar的数据，生成交易信号
        这是策略的核心方法，包含主要的交易逻辑
        
        参数：
            context: 上下文信息，包含当前日期、持仓等信息
            data: 当前bar的市场数据
            
        返回：
            Dict[str, Any]: 包含交易决策的字典，如目标持仓、订单等
            
        异常：
            StrategyRunError: 执行失败时抛出
        """
        pass
    
    @abstractmethod
    def after_trading_end(self, context: Dict[str, Any]) -> None:
        """
        在每个交易日结束后调用
        用于更新状态、记录结果等
        
        参数：
            context: 上下文信息，包含当前日期、持仓等信息
            
        异常：
            StrategyRunError: 执行失败时抛出
        """
        pass
    
    @abstractmethod
    def finalize(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        策略结束时调用，进行最后的清理和统计
        
        参数：
            context: 上下文信息，包含回测结果等
            
        返回：
            Dict[str, Any]: 策略统计结果
            
        异常：
            StrategyRunError: 执行失败时抛出
        """
        pass
    
    @abstractmethod
    def generate_signals(self, context: Dict[str, Any], data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号
        
        参数：
            context: 上下文信息
            data: 市场数据
            
        返回：
            pd.DataFrame: 交易信号，索引为证券代码，包含信号强度、方向等
            
        异常：
            StrategyRunError: 执行失败时抛出
        """
        pass
    
    @abstractmethod
    def generate_weights(self, context: Dict[str, Any], signals: pd.DataFrame) -> pd.DataFrame:
        """
        根据信号生成目标持仓权重
        
        参数：
            context: 上下文信息
            signals: 交易信号
            
        返回：
            pd.DataFrame: 目标持仓权重，索引为证券代码，值为目标权重
            
        异常：
            StrategyRunError: 执行失败时抛出
        """
        pass
    
    @abstractmethod
    def generate_orders(self, context: Dict[str, Any], weights: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        根据目标权重生成订单
        
        参数：
            context: 上下文信息
            weights: 目标持仓权重
            
        返回：
            List[Dict[str, Any]]: 订单列表，每个订单包含证券代码、方向、数量等
            
        异常：
            StrategyRunError: 执行失败时抛出
        """
        pass
    
    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取策略参数
        
        返回：
            Dict[str, Any]: 策略参数字典
        """
        pass
    
    @abstractmethod
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """
        设置策略参数
        
        参数：
            parameters: 参数字典
            
        异常：
            StrategyParameterError: 参数错误时抛出
        """
        pass
    
    @abstractmethod
    def get_required_data(self) -> Dict[str, Any]:
        """
        获取策略所需的数据描述
        
        返回：
            Dict[str, Any]: 描述所需数据的字典，包括数据类型、频率等
        """
        pass 