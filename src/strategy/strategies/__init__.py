"""
量化交易策略包
- 提供各种类型的量化交易策略
- 包含基于价值、动量、均值回归等投资风格的策略
"""

import logging

from .base_strategy import BaseStrategy, StrategyFactory
from .strategy_interface import (
    StrategyInterface, 
    StrategyException, 
    StrategyInitError,
    StrategyRunError,
    StrategyParameterError
)

# 导入策略子包 - 使用异常处理避免缺少实现的模块导致整个导入失败
try:
    from .value import ValueStrategy
except ImportError:
    logging.warning("价值策略模块不可用")
    # 创建一个空的占位类
    class ValueStrategy(BaseStrategy):
        def __init__(self, *args, **kwargs):
            super().__init__("ValueStrategy")
            raise NotImplementedError("价值策略尚未实现")

try:
    from .momentum import MomentumStrategy
except ImportError:
    logging.warning("动量策略模块不可用")
    # 创建一个空的占位类
    class MomentumStrategy(BaseStrategy):
        def __init__(self, *args, **kwargs):
            super().__init__("MomentumStrategy")
            raise NotImplementedError("动量策略尚未实现")

try:
    from .mean_reversion import MeanReversionStrategy
except ImportError:
    # 均值回归策略可能尚未实现
    pass

__all__ = [
    # 接口和基类
    'StrategyInterface',
    'BaseStrategy',
    'StrategyFactory',
    
    # 异常类
    'StrategyException',
    'StrategyInitError',
    'StrategyRunError',
    'StrategyParameterError',
    
    # 策略类
    'ValueStrategy',
    'MomentumStrategy'
] 