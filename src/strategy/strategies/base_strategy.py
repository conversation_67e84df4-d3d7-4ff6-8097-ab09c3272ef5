"""
策略基类模块：实现策略接口的基本功能
- 提供策略生命周期的默认实现
- 提供参数管理机制
- 提供依赖因子管理
- 提供通用的信号生成和仓位管理功能
"""

from typing import Dict, List, Any, Union, Optional, Tuple, Set
from datetime import datetime, date
import pandas as pd
import numpy as np
import logging
import json
import os
from abc import abstractmethod

from .strategy_interface import (
    StrategyInterface,
    StrategyException,
    StrategyInitError,
    StrategyRunError,
    StrategyParameterError
)

from ..factors.factor_interface import FactorInterface

class BaseStrategy(StrategyInterface):
    """
    策略基类，提供策略接口的基本实现
    所有具体策略应继承此类，重写必要的方法
    """
    
    def __init__(self, name: str = None, description: str = "", **kwargs):
        """
        初始化策略
        
        参数：
            name: 策略名称
            description: 策略描述
            **kwargs: 其他参数
        """
        self.name = name if name is not None else self.__class__.__name__
        self.description = description
        
        # 策略参数
        self.parameters = {}
        
        # 策略依赖的因子
        self.factors = {}
        
        # 回测相关参数
        self.start_date = None
        self.end_date = None
        self.universe = None
        self.benchmark = None
        self.frequency = 'daily'
        self.capital = 1000000  # 默认初始资金100万
        
        # 交易记录和持仓
        self.trades = []
        self.positions = {}
        
        # 日志
        self.logger = logging.getLogger(f"strategy.{self.name}")
        
        # 设置初始参数
        self.set_parameters(kwargs)
    
    def initialize(self, **kwargs) -> None:
        """
        初始化策略，设置策略参数和依赖
        
        参数：
            **kwargs: 初始化参数
                
        异常：
            StrategyInitError: 初始化失败时抛出
        """
        try:
            # 设置回测参数
            self.start_date = kwargs.get('start_date', self.start_date)
            self.end_date = kwargs.get('end_date', self.end_date)
            self.universe = kwargs.get('universe', self.universe)
            self.benchmark = kwargs.get('benchmark', self.benchmark)
            self.frequency = kwargs.get('frequency', self.frequency)
            self.capital = kwargs.get('capital', self.capital)
            
            # 初始化因子
            self._initialize_factors()
            
            # 调用子类实现的初始化
            self._initialize_impl(**kwargs)
            
            # 验证初始化结果
            self._validate_initialization()
            
            self.logger.info(f"策略 {self.name} 初始化完成")
            
        except Exception as e:
            self.logger.error(f"策略初始化失败: {str(e)}")
            raise StrategyInitError(f"策略初始化失败: {str(e)}")
    
    def _initialize_impl(self, **kwargs) -> None:
        """
        策略特定的初始化逻辑，子类应重写此方法
        
        参数：
            **kwargs: 初始化参数
        """
        pass
    
    def _validate_initialization(self) -> None:
        """
        验证初始化结果
        
        异常：
            StrategyInitError: 验证失败时抛出
        """
        if self.start_date is None:
            raise StrategyInitError("未设置回测开始日期")
            
        if self.end_date is None:
            raise StrategyInitError("未设置回测结束日期")
            
        if self.universe is None or len(self.universe) == 0:
            raise StrategyInitError("未设置或设置了空的证券池")
    
    def _initialize_factors(self) -> None:
        """
        初始化策略依赖的因子
        
        异常：
            StrategyInitError: 初始化因子失败时抛出
        """
        # 此方法应由子类重写
        pass
    
    def before_trading_start(self, context: Dict[str, Any]) -> None:
        """
        在每个交易日开始前调用
        
        参数：
            context: 上下文信息
        """
        try:
            # 获取最新因子值
            self._update_factors(context)
            
            # 调用子类实现
            self._before_trading_start_impl(context)
            
        except Exception as e:
            self.logger.error(f"交易前准备失败: {str(e)}")
            raise StrategyRunError(f"交易前准备失败: {str(e)}")
    
    def _before_trading_start_impl(self, context: Dict[str, Any]) -> None:
        """
        交易前准备的具体实现，子类应重写此方法
        
        参数：
            context: 上下文信息
        """
        pass
    
    def _update_factors(self, context: Dict[str, Any]) -> None:
        """
        更新因子值
        
        参数：
            context: 上下文信息
            
        异常：
            StrategyRunError: 更新因子失败时抛出
        """
        current_date = context.get('current_date')
        if current_date is None:
            raise StrategyRunError("上下文中缺少当前日期")
            
        try:
            # 对每个因子，获取最新值
            for factor_name, factor in self.factors.items():
                # 调用因子的compute方法获取最新值
                factor_data = factor.compute(
                    start_date=current_date,
                    end_date=current_date,
                    securities=self.universe
                )
                
                # 将因子值存储到上下文中
                if 'factors' not in context:
                    context['factors'] = {}
                context['factors'][factor_name] = factor_data
                
        except Exception as e:
            self.logger.error(f"更新因子值失败: {str(e)}")
            raise StrategyRunError(f"更新因子值失败: {str(e)}")
    
    def handle_data(self, context: Dict[str, Any], data: pd.DataFrame) -> Dict[str, Any]:
        """
        处理每个bar的数据，生成交易信号和订单
        
        参数：
            context: 上下文信息
            data: 当前bar的市场数据
            
        返回：
            Dict[str, Any]: 包含交易决策的字典
        """
        try:
            # 生成信号
            signals = self.generate_signals(context, data)
            context['signals'] = signals
            
            # 生成目标权重
            weights = self.generate_weights(context, signals)
            context['weights'] = weights
            
            # 生成订单
            orders = self.generate_orders(context, weights)
            context['orders'] = orders
            
            # 记录订单
            self._record_orders(context, orders)
            
            # 调用子类实现
            result = self._handle_data_impl(context, data)
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理数据失败: {str(e)}")
            raise StrategyRunError(f"处理数据失败: {str(e)}")
    
    def _handle_data_impl(self, context: Dict[str, Any], data: pd.DataFrame) -> Dict[str, Any]:
        """
        处理数据的具体实现，子类可重写此方法
        
        参数：
            context: 上下文信息
            data: 当前bar的市场数据
            
        返回：
            Dict[str, Any]: 处理结果
        """
        return {'status': 'success'}
    
    def _record_orders(self, context: Dict[str, Any], orders: List[Dict[str, Any]]) -> None:
        """
        记录订单
        
        参数：
            context: 上下文信息
            orders: 订单列表
        """
        current_date = context.get('current_date')
        
        for order in orders:
            # 添加日期信息
            order['date'] = current_date
            # 添加到交易记录
            self.trades.append(order)
    
    def after_trading_end(self, context: Dict[str, Any]) -> None:
        """
        在每个交易日结束后调用
        
        参数：
            context: 上下文信息
        """
        try:
            # 更新持仓
            self._update_positions(context)
            
            # 调用子类实现
            self._after_trading_end_impl(context)
            
        except Exception as e:
            self.logger.error(f"交易后处理失败: {str(e)}")
            raise StrategyRunError(f"交易后处理失败: {str(e)}")
    
    def _after_trading_end_impl(self, context: Dict[str, Any]) -> None:
        """
        交易后处理的具体实现，子类可重写此方法
        
        参数：
            context: 上下文信息
        """
        pass
    
    def _update_positions(self, context: Dict[str, Any]) -> None:
        """
        更新持仓
        
        参数：
            context: 上下文信息
        """
        current_date = context.get('current_date')
        positions = context.get('positions', {})
        
        # 更新策略持仓记录
        self.positions[current_date] = positions.copy()
    
    def finalize(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        策略结束时调用
        
        参数：
            context: 上下文信息
            
        返回：
            Dict[str, Any]: 策略统计结果
        """
        try:
            # 计算绩效指标
            performance = self._calculate_performance(context)
            
            # 调用子类实现
            result = self._finalize_impl(context)
            
            # 合并结果
            if isinstance(result, dict):
                performance.update(result)
                
            return performance
            
        except Exception as e:
            self.logger.error(f"策略结束处理失败: {str(e)}")
            raise StrategyRunError(f"策略结束处理失败: {str(e)}")
    
    def _finalize_impl(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        策略结束处理的具体实现，子类可重写此方法
        
        参数：
            context: 上下文信息
            
        返回：
            Dict[str, Any]: 处理结果
        """
        return {}
    
    def _calculate_performance(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算策略绩效
        
        参数：
            context: 上下文信息
            
        返回：
            Dict[str, Any]: 绩效指标
        """
        # 从上下文获取收益率曲线
        returns = context.get('returns', pd.Series())
        benchmark_returns = context.get('benchmark_returns', pd.Series())
        
        # 如果没有收益率数据，返回空结果
        if len(returns) == 0:
            return {'warning': '无收益率数据，无法计算绩效'}
            
        # 计算基本指标
        total_return = returns.iloc[-1] - returns.iloc[0]
        annualized_return = (1 + total_return) ** (252 / len(returns)) - 1
        
        # 如果有基准收益率，计算超额收益
        alpha = None
        if len(benchmark_returns) > 0:
            benchmark_total_return = benchmark_returns.iloc[-1] - benchmark_returns.iloc[0]
            benchmark_annualized_return = (1 + benchmark_total_return) ** (252 / len(benchmark_returns)) - 1
            alpha = annualized_return - benchmark_annualized_return
        
        # 计算波动率
        volatility = returns.std() * np.sqrt(252)
        
        # 计算夏普比率
        risk_free_rate = 0.03  # 假设无风险利率为3%
        sharpe = (annualized_return - risk_free_rate) / volatility if volatility > 0 else 0
        
        # 计算最大回撤
        cumulative_returns = (1 + returns).cumprod()
        max_drawdown = ((cumulative_returns.cummax() - cumulative_returns) / cumulative_returns.cummax()).max()
        
        return {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'alpha': alpha,
            'volatility': volatility,
            'sharpe': sharpe,
            'max_drawdown': max_drawdown,
            'trades_count': len(self.trades),
            'final_positions_count': len(context.get('positions', {}))
        }
    
    def generate_signals(self, context: Dict[str, Any], data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号
        
        参数：
            context: 上下文信息
            data: 市场数据
            
        返回：
            pd.DataFrame: 交易信号
        """
        try:
            # 调用子类实现
            return self._generate_signals_impl(context, data)
            
        except Exception as e:
            self.logger.error(f"生成信号失败: {str(e)}")
            raise StrategyRunError(f"生成信号失败: {str(e)}")
    
    @abstractmethod
    def _generate_signals_impl(self, context: Dict[str, Any], data: pd.DataFrame) -> pd.DataFrame:
        """
        生成信号的具体实现，子类必须重写此方法
        
        参数：
            context: 上下文信息
            data: 市场数据
            
        返回：
            pd.DataFrame: 交易信号，索引为证券代码，包含信号强度、方向等
        """
        pass
    
    def generate_weights(self, context: Dict[str, Any], signals: pd.DataFrame) -> pd.DataFrame:
        """
        根据信号生成目标持仓权重
        
        参数：
            context: 上下文信息
            signals: 交易信号
            
        返回：
            pd.DataFrame: 目标持仓权重
        """
        try:
            # 调用子类实现
            return self._generate_weights_impl(context, signals)
            
        except Exception as e:
            self.logger.error(f"生成权重失败: {str(e)}")
            raise StrategyRunError(f"生成权重失败: {str(e)}")
    
    @abstractmethod
    def _generate_weights_impl(self, context: Dict[str, Any], signals: pd.DataFrame) -> pd.DataFrame:
        """
        生成权重的具体实现，子类必须重写此方法
        
        参数：
            context: 上下文信息
            signals: 交易信号
            
        返回：
            pd.DataFrame: 目标持仓权重，索引为证券代码，值为目标权重
        """
        pass
    
    def generate_orders(self, context: Dict[str, Any], weights: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        根据目标权重生成订单
        
        参数：
            context: 上下文信息
            weights: 目标持仓权重
            
        返回：
            List[Dict[str, Any]]: 订单列表
        """
        try:
            # 调用子类实现
            return self._generate_orders_impl(context, weights)
            
        except Exception as e:
            self.logger.error(f"生成订单失败: {str(e)}")
            raise StrategyRunError(f"生成订单失败: {str(e)}")
    
    @abstractmethod
    def _generate_orders_impl(self, context: Dict[str, Any], weights: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        生成订单的具体实现，子类必须重写此方法
        
        参数：
            context: 上下文信息
            weights: 目标持仓权重
            
        返回：
            List[Dict[str, Any]]: 订单列表，每个订单包含证券代码、方向、数量等
        """
        pass
    
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取策略参数
        
        返回：
            Dict[str, Any]: 策略参数字典
        """
        return self.parameters.copy()
    
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """
        设置策略参数
        
        参数：
            parameters: 参数字典
            
        异常：
            StrategyParameterError: 参数错误时抛出
        """
        if not isinstance(parameters, dict):
            raise StrategyParameterError("参数必须是字典类型")
            
        # 更新参数
        self.parameters.update(parameters)
        
        # 应用参数
        try:
            self._apply_parameters()
        except Exception as e:
            self.logger.error(f"应用参数失败: {str(e)}")
            raise StrategyParameterError(f"应用参数失败: {str(e)}")
    
    def _apply_parameters(self) -> None:
        """
        应用策略参数
        
        异常：
            StrategyParameterError: 参数应用失败时抛出
        """
        # 此方法应由子类重写
        pass
    
    def add_factor(self, name: str, factor: FactorInterface) -> None:
        """
        添加因子
        
        参数：
            name: 因子名称
            factor: 因子实例
            
        异常：
            ValueError: 名称已存在或因子类型错误时抛出
        """
        if name in self.factors:
            raise ValueError(f"因子名称 '{name}' 已存在")
            
        if not isinstance(factor, FactorInterface):
            raise ValueError(f"因子必须实现FactorInterface接口")
            
        self.factors[name] = factor
    
    def get_factor(self, name: str) -> Optional[FactorInterface]:
        """
        获取因子
        
        参数：
            name: 因子名称
            
        返回：
            Optional[FactorInterface]: 因子实例，不存在则返回None
        """
        return self.factors.get(name)
    
    def get_required_data(self) -> Dict[str, Any]:
        """
        获取策略所需的数据描述
        
        返回：
            Dict[str, Any]: 描述所需数据的字典
        """
        # 整合所有因子需要的数据
        data_requirements = {
            'market_data': {
                'frequency': self.frequency,
                'fields': ['open', 'high', 'low', 'close', 'volume']
            }
        }
        
        # 加入自定义数据需求
        custom_requirements = self._get_data_requirements()
        if custom_requirements:
            data_requirements.update(custom_requirements)
            
        return data_requirements
    
    def _get_data_requirements(self) -> Dict[str, Any]:
        """
        获取策略特定的数据需求，子类可重写此方法
        
        返回：
            Dict[str, Any]: 数据需求字典
        """
        return {}
    
    def save(self, path: str) -> None:
        """
        保存策略状态
        
        参数：
            path: 保存路径
            
        异常：
            IOError: 保存失败时抛出
        """
        try:
            # 创建目录
            os.makedirs(os.path.dirname(path), exist_ok=True)
            
            # 准备保存数据
            save_data = {
                'name': self.name,
                'description': self.description,
                'parameters': self.parameters,
                'start_date': self.start_date,
                'end_date': self.end_date,
                'universe': self.universe,
                'benchmark': self.benchmark,
                'frequency': self.frequency,
                'capital': self.capital
            }
            
            # 写入文件
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=4)
                
            self.logger.info(f"策略状态已保存到 {path}")
            
        except Exception as e:
            self.logger.error(f"保存策略状态失败: {str(e)}")
            raise IOError(f"保存策略状态失败: {str(e)}")
    
    @classmethod
    def load(cls, path: str) -> 'BaseStrategy':
        """
        加载策略状态
        
        参数：
            path: 加载路径
            
        返回：
            BaseStrategy: 策略实例
            
        异常：
            IOError: 加载失败时抛出
        """
        try:
            # {{ AURA-X: Modify - 使用ConfigFactory统一配置管理，消除直接JSON读取. Approval: 寸止(ID:深度架构复查修复). }}
            # 读取文件
            from src.utils.config.config_factory import config_factory
            import os

            # 解析文件路径
            config_dir = os.path.dirname(path)
            config_name = os.path.splitext(os.path.basename(path))[0]

            # 使用ConfigFactory加载配置
            load_data = config_factory.load_config(config_name, config_dir)
                
            # 创建实例
            strategy = cls(
                name=load_data.get('name'),
                description=load_data.get('description')
            )
            
            # 设置属性
            strategy.parameters = load_data.get('parameters', {})
            strategy.start_date = load_data.get('start_date')
            strategy.end_date = load_data.get('end_date')
            strategy.universe = load_data.get('universe')
            strategy.benchmark = load_data.get('benchmark')
            strategy.frequency = load_data.get('frequency', 'daily')
            strategy.capital = load_data.get('capital', 1000000)
            
            # 应用参数
            strategy._apply_parameters()
            
            return strategy
            
        except Exception as e:
            logging.error(f"加载策略状态失败: {str(e)}")
            raise IOError(f"加载策略状态失败: {str(e)}")

"""
策略工厂：负责创建不同类型的策略实例
"""
class StrategyFactory:
    """
    策略工厂类，用于根据配置创建策略实例
    """
    
    # 注册的策略类
    _strategy_classes = {}
    
    @classmethod
    def register(cls, strategy_type: str, strategy_class: type) -> None:
        """
        注册策略类
        
        参数：
            strategy_type: 策略类型名称
            strategy_class: 策略类
            
        异常：
            ValueError: 策略类型已存在或类型错误时抛出
        """
        if strategy_type in cls._strategy_classes:
            raise ValueError(f"策略类型 '{strategy_type}' 已经注册")
            
        if not issubclass(strategy_class, BaseStrategy):
            raise ValueError(f"策略类必须继承BaseStrategy")
            
        cls._strategy_classes[strategy_type] = strategy_class
    
    @classmethod
    def create(cls, strategy_type: str, **kwargs) -> BaseStrategy:
        """
        创建策略实例
        
        参数：
            strategy_type: 策略类型名称
            **kwargs: 策略参数
            
        返回：
            BaseStrategy: 策略实例
            
        异常：
            ValueError: 策略类型不存在时抛出
        """
        if strategy_type not in cls._strategy_classes:
            raise ValueError(f"未知的策略类型: '{strategy_type}'")
            
        strategy_class = cls._strategy_classes[strategy_type]
        return strategy_class(**kwargs)
    
    @classmethod
    def create_from_config(cls, config: Dict[str, Any]) -> BaseStrategy:
        """
        从配置创建策略实例
        
        参数：
            config: 配置字典，必须包含'type'键
            
        返回：
            BaseStrategy: 策略实例
            
        异常：
            ValueError: 配置错误时抛出
        """
        if 'type' not in config:
            raise ValueError("配置必须包含'type'键")
            
        strategy_type = config.pop('type')
        return cls.create(strategy_type, **config)
    
    @classmethod
    def get_available_strategies(cls) -> List[str]:
        """
        获取可用的策略类型列表
        
        返回：
            List[str]: 策略类型列表
        """
        return list(cls._strategy_classes.keys()) 