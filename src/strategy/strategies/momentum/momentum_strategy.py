"""
动量策略模块：基于价格动量的选股策略实现
- 使用价格趋势和历史收益率选择具有上升动量的股票
- 支持多周期动量计算和组合
- 提供参数配置和自定义选项
"""

from typing import Dict, List, Any, Union, Optional, Tuple, Set
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta

from ..base_strategy import BaseStrategy
from ..strategy_interface import StrategyRunError, StrategyParameterError
from ...factors.technical import SMA, EMA, WMA

class MomentumStrategy(BaseStrategy):
    """
    动量策略：基于价格动量的选股策略
    
    策略逻辑：
    1. 计算股票在不同周期的收益率（如1个月、3个月、6个月等）
    2. 根据收益率选择具有正向动量的股票
    3. 支持多种权重计算方法
    4. 可设置持仓数量限制和再平衡周期
    
    参数：
    - momentum_periods: 计算动量的周期列表，单位为交易日
    - period_weights: 各周期的权重，默认等权重
    - rebalance_freq: 再平衡频率，支持 'daily', 'weekly', 'monthly', 'quarterly'
    - top_n: 选择的股票数量
    - weighting_scheme: 权重计算方法，支持 'equal', 'market_cap', 'momentum_score'
    - exclude_period: 是否排除最近的N个交易日，避免反转效应
    """
    
    # 支持的再平衡频率
    VALID_REBALANCE_FREQ = {'daily', 'weekly', 'monthly', 'quarterly'}
    
    # 支持的权重计算方法
    VALID_WEIGHTING_SCHEMES = {'equal', 'market_cap', 'momentum_score'}
    
    def __init__(self, 
                name: str = None,
                description: str = "基于价格动量的选股策略",
                momentum_periods: List[int] = None,
                period_weights: Dict[int, float] = None,
                rebalance_freq: str = 'monthly',
                top_n: int = 20,
                weighting_scheme: str = 'momentum_score',
                exclude_period: int = 5,
                **kwargs):
        """
        初始化动量策略
        
        参数：
            name: 策略名称，默认为类名
            description: 策略描述
            momentum_periods: 计算动量的周期列表，单位为交易日，默认为[20, 60, 120]
            period_weights: 各周期的权重，默认等权重
            rebalance_freq: 再平衡频率，默认为月度再平衡
            top_n: 选择的股票数量，默认为20
            weighting_scheme: 权重计算方法，默认为按动量得分加权
            exclude_period: 排除最近的N个交易日，避免反转效应，默认为5天
            **kwargs: 其他参数
        """
        if name is None:
            name = "MomentumStrategy"
            
        # 设置默认值
        if momentum_periods is None:
            momentum_periods = [20, 60, 120]  # 分别对应约1个月、3个月和6个月
            
        # 验证参数
        self._validate_parameters(momentum_periods, rebalance_freq, weighting_scheme)
        
        # 设置参数
        self.momentum_periods = momentum_periods
        self.period_weights = self._normalize_weights(period_weights)
        self.rebalance_freq = rebalance_freq
        self.top_n = top_n
        self.weighting_scheme = weighting_scheme
        self.exclude_period = exclude_period
        
        # 再平衡状态
        self.last_rebalance_date = None
        self.next_rebalance_date = None
        
        # 历史价格数据缓存
        self.price_history = None
        
        super().__init__(name=name, description=description, **kwargs)
        
    def _validate_parameters(self, momentum_periods: List[int], rebalance_freq: str, weighting_scheme: str) -> None:
        """
        验证参数
        
        参数：
            momentum_periods: 动量周期列表
            rebalance_freq: 再平衡频率
            weighting_scheme: 权重计算方法
            
        异常：
            ValueError: 参数无效时抛出
        """
        if not momentum_periods:
            raise ValueError("动量周期列表不能为空")
            
        for period in momentum_periods:
            if not isinstance(period, int) or period <= 0:
                raise ValueError(f"动量周期必须是正整数: {period}")
                
        if rebalance_freq not in self.VALID_REBALANCE_FREQ:
            raise ValueError(f"不支持的再平衡频率: {rebalance_freq}，支持的频率包括: {', '.join(self.VALID_REBALANCE_FREQ)}")
        
        if weighting_scheme not in self.VALID_WEIGHTING_SCHEMES:
            raise ValueError(f"不支持的权重计算方法: {weighting_scheme}，支持的方法包括: {', '.join(self.VALID_WEIGHTING_SCHEMES)}")
    
    def _normalize_weights(self, weights: Dict[int, float]) -> Dict[int, float]:
        """
        归一化周期权重
        
        参数：
            weights: 权重字典
            
        返回：
            Dict[int, float]: 归一化后的权重字典
        """
        if weights is None:
            # 如果未提供权重，则使用等权重
            weights = {period: 1.0 for period in self.momentum_periods}
        else:
            # 确保所有周期都有权重
            for period in self.momentum_periods:
                if period not in weights:
                    weights[period] = 1.0
        
        # 归一化
        total_weight = sum(weights.values())
        if total_weight > 0:
            for period in weights:
                weights[period] /= total_weight
                
        return weights
    
    def _initialize_impl(self, **kwargs) -> None:
        """
        策略特定的初始化逻辑
        
        参数：
            **kwargs: 初始化参数
        """
        # 设置初始再平衡日期
        self.last_rebalance_date = None
        self.next_rebalance_date = self.start_date
        
        # 计算所需的最大历史数据长度
        max_period = max(self.momentum_periods) + self.exclude_period
        self.lookback_days = max_period
    
    def _initialize_factors(self) -> None:
        """
        初始化策略依赖的因子
        """
        # 在动量策略中，我们直接使用价格数据计算收益率，不需要特定因子
        pass
    
    def _before_trading_start_impl(self, context: Dict[str, Any]) -> None:
        """
        交易前准备的具体实现
        
        参数：
            context: 上下文信息
        """
        current_date = context.get('current_date')
        
        # 检查是否需要再平衡
        if self._is_rebalance_day(current_date):
            self.logger.info(f"今日 {current_date} 为再平衡日，将更新持仓")
            context['rebalance_today'] = True
            self.last_rebalance_date = current_date
            self.next_rebalance_date = self._calculate_next_rebalance_date(current_date)
        else:
            context['rebalance_today'] = False
    
    def _is_rebalance_day(self, current_date: str) -> bool:
        """
        判断当前日期是否为再平衡日
        
        参数：
            current_date: 当前日期
            
        返回：
            bool: 是否为再平衡日
        """
        # 如果是第一个交易日，直接再平衡
        if self.last_rebalance_date is None:
            return True
            
        # 检查是否到达下一个再平衡日期
        return current_date >= self.next_rebalance_date
    
    def _calculate_next_rebalance_date(self, current_date: str) -> str:
        """
        计算下一个再平衡日期
        
        参数：
            current_date: 当前日期
            
        返回：
            str: 下一个再平衡日期
        """
        # 这里简化处理，实际应根据交易日历计算
        current_dt = datetime.strptime(current_date, '%Y-%m-%d')
        
        if self.rebalance_freq == 'daily':
            # 下一个交易日，简化处理
            next_dt = current_dt + timedelta(days=1)
        elif self.rebalance_freq == 'weekly':
            # 下周同一天
            next_dt = current_dt + timedelta(days=7)
        elif self.rebalance_freq == 'monthly':
            # 下个月同一天
            if current_dt.month == 12:
                next_dt = current_dt.replace(year=current_dt.year + 1, month=1)
            else:
                next_dt = current_dt.replace(month=current_dt.month + 1)
        elif self.rebalance_freq == 'quarterly':
            # 下个季度同一天
            month = current_dt.month
            if month in [1, 2, 3]:
                next_dt = current_dt.replace(month=4)
            elif month in [4, 5, 6]:
                next_dt = current_dt.replace(month=7)
            elif month in [7, 8, 9]:
                next_dt = current_dt.replace(month=10)
            else:
                next_dt = current_dt.replace(year=current_dt.year + 1, month=1)
        
        return next_dt.strftime('%Y-%m-%d')
    
    def _generate_signals_impl(self, context: Dict[str, Any], data: pd.DataFrame) -> pd.DataFrame:
        """
        生成信号的具体实现
        
        参数：
            context: 上下文信息
            data: 市场数据
            
        返回：
            pd.DataFrame: 交易信号
        """
        # 如果今天不是再平衡日，返回空信号
        if not context.get('rebalance_today', False):
            return pd.DataFrame(index=self.universe, columns=['signal']).fillna(0)
        
        # 获取历史价格数据
        price_history = context.get('price_history')
        if price_history is None or price_history.empty:
            self.logger.warning("未找到历史价格数据")
            return pd.DataFrame(index=self.universe, columns=['signal']).fillna(0)
        
        # 计算各个周期的动量得分
        momentum_scores = self._calculate_momentum_scores(price_history)
        
        # 如果没有动量得分，返回空信号
        if momentum_scores.empty:
            return pd.DataFrame(index=self.universe, columns=['signal']).fillna(0)
        
        # 选择前N名股票
        selected_stocks = momentum_scores.nlargest(self.top_n, 'score')
        
        # 生成信号
        signals = pd.DataFrame(index=self.universe, columns=['signal']).fillna(0)
        signals.loc[selected_stocks.index, 'signal'] = 1
        
        return signals
    
    def _calculate_momentum_scores(self, price_history: pd.DataFrame) -> pd.DataFrame:
        """
        计算动量得分
        
        参数：
            price_history: 历史价格数据，包含收盘价
            
        返回：
            pd.DataFrame: 动量得分
        """
        # 初始化空的得分DataFrame
        scores = pd.DataFrame(index=self.universe, columns=['score']).fillna(0)
        
        # 遍历每个动量周期计算收益率
        for period in self.momentum_periods:
            # 获取对应权重
            weight = self.period_weights.get(period, 1.0 / len(self.momentum_periods))
            
            try:
                # 计算收益率，排除最近的exclude_period天
                if self.exclude_period > 0:
                    returns = price_history.iloc[-period-self.exclude_period:-self.exclude_period].pct_change(period).iloc[-1]
                else:
                    returns = price_history.iloc[-period:].pct_change(period).iloc[-1]
                
                # 标准化收益率
                if not returns.empty:
                    mean = returns.mean()
                    std = returns.std()
                    if std > 0:
                        normalized_returns = (returns - mean) / std
                        # 将标准化后的收益率乘以权重，累加到总分中
                        scores.loc[normalized_returns.index, 'score'] += normalized_returns * weight
            except Exception as e:
                self.logger.warning(f"计算{period}天动量时出错: {str(e)}")
        
        return scores
    
    def _generate_weights_impl(self, context: Dict[str, Any], signals: pd.DataFrame) -> pd.DataFrame:
        """
        生成权重的具体实现
        
        参数：
            context: 上下文信息
            signals: 交易信号
            
        返回：
            pd.DataFrame: 目标持仓权重
        """
        # 获取有信号的股票
        selected_stocks = signals[signals['signal'] > 0].index.tolist()
        
        # 如果没有选中的股票，返回空持仓
        if not selected_stocks:
            return pd.DataFrame(index=self.universe, columns=['weight']).fillna(0)
        
        # 根据权重计算方法，生成权重
        if self.weighting_scheme == 'equal':
            # 等权重
            weights = pd.DataFrame(index=self.universe, columns=['weight']).fillna(0)
            weights.loc[selected_stocks, 'weight'] = 1.0 / len(selected_stocks)
            
        elif self.weighting_scheme == 'market_cap':
            # 按市值加权
            weights = self._calculate_market_cap_weights(context, selected_stocks)
            
        elif self.weighting_scheme == 'momentum_score':
            # 按动量得分加权
            weights = self._calculate_momentum_score_weights(context, selected_stocks)
            
        else:
            # 默认等权重
            weights = pd.DataFrame(index=self.universe, columns=['weight']).fillna(0)
            weights.loc[selected_stocks, 'weight'] = 1.0 / len(selected_stocks)
        
        return weights
    
    def _calculate_market_cap_weights(self, context: Dict[str, Any], selected_stocks: List[str]) -> pd.DataFrame:
        """
        计算市值加权权重
        
        参数：
            context: 上下文信息
            selected_stocks: 选中的股票列表
            
        返回：
            pd.DataFrame: 权重
        """
        # 获取市值数据
        market_caps = context.get('market_caps')
        if market_caps is None:
            self.logger.warning("未找到市值数据，使用等权重")
            weights = pd.DataFrame(index=self.universe, columns=['weight']).fillna(0)
            weights.loc[selected_stocks, 'weight'] = 1.0 / len(selected_stocks)
            return weights
        
        # 计算市值权重
        selected_caps = market_caps.loc[selected_stocks]
        total_cap = selected_caps.sum()
        
        weights = pd.DataFrame(index=self.universe, columns=['weight']).fillna(0)
        if total_cap > 0:
            weights.loc[selected_stocks, 'weight'] = selected_caps / total_cap
            
        return weights
    
    def _calculate_momentum_score_weights(self, context: Dict[str, Any], selected_stocks: List[str]) -> pd.DataFrame:
        """
        计算动量得分加权权重
        
        参数：
            context: 上下文信息
            selected_stocks: 选中的股票列表
            
        返回：
            pd.DataFrame: 权重
        """
        # 获取历史价格数据
        price_history = context.get('price_history')
        if price_history is None or price_history.empty:
            self.logger.warning("未找到历史价格数据，使用等权重")
            weights = pd.DataFrame(index=self.universe, columns=['weight']).fillna(0)
            weights.loc[selected_stocks, 'weight'] = 1.0 / len(selected_stocks)
            return weights
        
        # 计算动量得分
        momentum_scores = self._calculate_momentum_scores(price_history)
        
        # 选择已选中的股票的得分
        selected_scores = momentum_scores.loc[momentum_scores.index.intersection(selected_stocks)]
        
        # 将负得分设为0，避免负权重
        selected_scores.loc[selected_scores['score'] < 0, 'score'] = 0
        
        # 计算得分权重
        weights = pd.DataFrame(index=self.universe, columns=['weight']).fillna(0)
        
        if not selected_scores.empty:
            total_score = selected_scores['score'].sum()
            if total_score > 0:
                for stock in selected_scores.index:
                    weights.loc[stock, 'weight'] = selected_scores.loc[stock, 'score'] / total_score
        
        # 如果没有计算出权重，使用等权重
        if weights['weight'].sum() == 0:
            weights.loc[selected_stocks, 'weight'] = 1.0 / len(selected_stocks)
            
        return weights
    
    def _generate_orders_impl(self, context: Dict[str, Any], weights: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        生成订单的具体实现
        
        参数：
            context: 上下文信息
            weights: 目标持仓权重
            
        返回：
            List[Dict[str, Any]]: 订单列表
        """
        # 如果今天不是再平衡日，不产生订单
        if not context.get('rebalance_today', False):
            return []
        
        # 获取当前持仓
        current_positions = context.get('positions', {})
        
        # 计算目标持仓金额
        portfolio_value = context.get('portfolio_value', self.capital)
        target_positions = {}
        for stock, row in weights.iterrows():
            if row['weight'] > 0:
                target_positions[stock] = row['weight'] * portfolio_value
        
        # 生成订单
        orders = []
        
        # 先处理卖出订单
        for stock in current_positions:
            current_value = current_positions[stock]['value']
            target_value = target_positions.get(stock, 0)
            
            if target_value < current_value:
                # 需要卖出
                orders.append({
                    'security_id': stock,
                    'direction': 'SELL',
                    'amount': current_value - target_value,
                    'reason': '再平衡卖出'
                })
        
        # 再处理买入订单
        for stock, target_value in target_positions.items():
            current_value = current_positions.get(stock, {}).get('value', 0)
            
            if target_value > current_value:
                # 需要买入
                orders.append({
                    'security_id': stock,
                    'direction': 'BUY',
                    'amount': target_value - current_value,
                    'reason': '再平衡买入'
                })
        
        return orders
    
    def _apply_parameters(self) -> None:
        """
        应用策略参数
        """
        # 从参数中获取并设置
        for key, value in self.parameters.items():
            if key == 'momentum_periods':
                self._validate_parameters(value, self.rebalance_freq, self.weighting_scheme)
                self.momentum_periods = value
            elif key == 'period_weights':
                self.period_weights = self._normalize_weights(value)
            elif key == 'rebalance_freq':
                if value not in self.VALID_REBALANCE_FREQ:
                    raise StrategyParameterError(f"不支持的再平衡频率: {value}")
                self.rebalance_freq = value
            elif key == 'top_n':
                if not isinstance(value, int) or value <= 0:
                    raise StrategyParameterError(f"top_n必须是正整数: {value}")
                self.top_n = value
            elif key == 'weighting_scheme':
                if value not in self.VALID_WEIGHTING_SCHEMES:
                    raise StrategyParameterError(f"不支持的权重计算方法: {value}")
                self.weighting_scheme = value
            elif key == 'exclude_period':
                if not isinstance(value, int) or value < 0:
                    raise StrategyParameterError(f"exclude_period必须是非负整数: {value}")
                self.exclude_period = value
    
    def _get_data_requirements(self) -> Dict[str, Any]:
        """
        获取策略特定的数据需求
        
        返回：
            Dict[str, Any]: 数据需求字典
        """
        # 计算所需的最大历史数据长度
        max_lookback = max(self.momentum_periods) + self.exclude_period
        
        return {
            'market_data': {
                'frequency': self.frequency,
                'fields': ['close', 'volume', 'market_cap'],
                'lookback_periods': max_lookback
            }
        }
        
# 注册策略
from ..base_strategy import StrategyFactory
StrategyFactory.register('momentum', MomentumStrategy) 