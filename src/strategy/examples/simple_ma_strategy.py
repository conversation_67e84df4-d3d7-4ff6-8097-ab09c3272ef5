#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单均线策略示例
"""

from src.strategy.strategies.base_strategy import BaseStrategy

class SimpleMAStrategy(BaseStrategy):
    """
    简单的均线交叉策略
    
    当短期均线上穿长期均线时买入，下穿时卖出
    """
    
    def __init__(self, short_window=5, long_window=20, position_pct=0.8):
        """
        初始化策略参数
        
        Args:
            short_window: 短期均线窗口期
            long_window: 长期均线窗口期
            position_pct: 仓位比例
        """
        super().__init__(name="SimpleMAStrategy")
        
        self.short_window = short_window
        self.long_window = long_window
        self.position_pct = position_pct
        
        # 记录仓位状态
        self.positions = {}
        
        # 设置策略配置
        self.set_config({
            'type': 'trend_following',
            'data_frequency': 'daily',
            'parameters': {
                'short_window': short_window,
                'long_window': long_window,
                'position_pct': position_pct
            }
        })
    
    def initialize(self, **kwargs):
        """
        策略初始化
        
        Args:
            **kwargs: 其它参数
        """
        self.logger.info(f"初始化简单均线策略: 短期={self.short_window}, 长期={self.long_window}")
    
    def on_data(self, data, **kwargs):
        """
        处理行情数据
        
        Args:
            data: 行情数据
            **kwargs: 其它参数
            
        Returns:
            list: 信号列表
        """
        signals = []
        for code, group in data.groupby('ts_code'):
            # 如果数据量不足，跳过
            if len(group) < self.long_window:
                continue
                
            # 计算均线
            group = group.sort_values('trade_date')
            group['short_ma'] = group['close'].rolling(window=self.short_window).mean()
            group['long_ma'] = group['close'].rolling(window=self.long_window).mean()
            
            # 生成交叉信号
            group['signal'] = 0
            group.loc[group['short_ma'] > group['long_ma'], 'signal'] = 1
            group.loc[group['short_ma'] < group['long_ma'], 'signal'] = -1
            
            # 计算实际交易信号（信号变化点）
            group['position'] = group['signal'].shift(1)
            group.loc[group.index[0], 'position'] = 0
            
            # 交易信号
            buy_signals = group[(group['signal'] == 1) & (group['position'] != 1)]
            sell_signals = group[(group['signal'] == -1) & (group['position'] != -1)]
            
            # 如果有有效信号，生成信号数据
            for idx, row in buy_signals.iterrows():
                signals.append({
                    'ts_code': code,
                    'trade_date': row['trade_date'],
                    'signal': 'buy',
                    'price': row['close'],
                    'volume': self.calculate_position_size(row['close'], code),
                    'reason': '短期均线上穿长期均线'
                })
                self.positions[code] = 1
                
            for idx, row in sell_signals.iterrows():
                signals.append({
                    'ts_code': code,
                    'trade_date': row['trade_date'],
                    'signal': 'sell',
                    'price': row['close'],
                    'volume': 0,  # 全部卖出
                    'reason': '短期均线下穿长期均线'
                })
                self.positions[code] = -1
                
        return signals
    
    def calculate_position_size(self, price, code):
        """
        计算持仓数量
        
        Args:
            price: 价格
            code: 股票代码
            
        Returns:
            int: 持仓数量
        """
        # 简单的持仓计算，假设总资金为100万
        total_capital = 1000000
        position_value = total_capital * self.position_pct
        
        # 计算可买入的股数（取整百）
        return int((position_value / price) / 100) * 100
    
    def on_order_filled(self, order, **kwargs):
        """
        订单成交回调
        
        Args:
            order: 订单信息
            **kwargs: 其它参数
        """
        self.logger.info(f"订单成交: {order['ts_code']} {order['signal']} {order['volume']}股")
    
    def on_backtest_end(self, context, **kwargs):
        """
        回测结束回调
        
        Args:
            context: 回测上下文
            **kwargs: 其它参数
        """
        self.logger.info("回测结束")
        return {
            'strategy': self.name,
            'parameters': {
                'short_window': self.short_window,
                'long_window': self.long_window
            }
        } 