"""
运维自动化管理器

统一管理系统监控、告警、自动化运维任务和故障自愈功能。
提供企业级运维自动化能力，支持智能故障检测、自动恢复、容量规划等。
"""

import time
import logging
import threading
import asyncio
import json
import os
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from collections import deque, defaultdict

from .system_monitor import SystemMonitor, SystemMetrics, DatabaseMetrics


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AutomationAction(Enum):
    """自动化操作类型"""
    RESTART_SERVICE = "restart_service"
    CLEAR_CACHE = "clear_cache"
    SCALE_UP = "scale_up"
    SCALE_DOWN = "scale_down"
    BACKUP_DATA = "backup_data"
    OPTIMIZE_DATABASE = "optimize_database"
    CLEAN_LOGS = "clean_logs"
    NOTIFY_ADMIN = "notify_admin"


@dataclass
class Alert:
    """告警信息"""
    id: str
    level: AlertLevel
    title: str
    message: str
    timestamp: datetime
    source: str
    metrics: Dict[str, Any]
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    auto_resolved: bool = False


@dataclass
class AutomationRule:
    """自动化规则"""
    id: str
    name: str
    description: str
    condition: str  # 触发条件表达式
    action: AutomationAction
    parameters: Dict[str, Any]
    enabled: bool = True
    cooldown_minutes: int = 30  # 冷却时间
    last_executed: Optional[datetime] = None


@dataclass
class AutomationTask:
    """自动化任务"""
    id: str
    rule_id: str
    action: AutomationAction
    parameters: Dict[str, Any]
    status: str  # pending, running, completed, failed
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class OpsAutomationManager:
    """
    运维自动化管理器
    
    功能：
    1. 统一监控管理
    2. 智能告警系统
    3. 自动化运维任务
    4. 故障自愈机制
    5. 容量规划建议
    6. 性能优化建议
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化运维自动化管理器
        
        参数:
            config: 配置字典
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 监控组件
        self.system_monitor = SystemMonitor()
        
        # 告警管理
        self.alerts: Dict[str, Alert] = {}
        self.alert_history: deque = deque(maxlen=1000)
        self.alert_handlers: List[Callable] = []
        
        # 自动化规则和任务
        self.automation_rules: Dict[str, AutomationRule] = {}
        self.automation_tasks: Dict[str, AutomationTask] = {}
        self.task_queue: deque = deque()
        
        # 运行状态
        self.is_running = False
        self.automation_thread = None
        self.task_executor_thread = None
        
        # 性能统计
        self.stats = {
            'alerts_generated': 0,
            'alerts_resolved': 0,
            'tasks_executed': 0,
            'tasks_successful': 0,
            'auto_recovery_count': 0,
            'last_update': datetime.now()
        }
        
        # 初始化默认规则
        self._init_default_rules()
    
    def _init_default_rules(self):
        """初始化默认自动化规则"""
        default_rules = [
            AutomationRule(
                id="high_cpu_usage",
                name="高CPU使用率处理",
                description="当CPU使用率超过90%时自动清理缓存",
                condition="cpu_usage > 90",
                action=AutomationAction.CLEAR_CACHE,
                parameters={"cache_types": ["memory", "disk"]},
                cooldown_minutes=15
            ),
            AutomationRule(
                id="low_disk_space",
                name="磁盘空间不足处理",
                description="当磁盘剩余空间小于5GB时清理日志",
                condition="disk_free_gb < 5",
                action=AutomationAction.CLEAN_LOGS,
                parameters={"retention_days": 7},
                cooldown_minutes=60
            ),
            AutomationRule(
                id="high_memory_usage",
                name="高内存使用率处理",
                description="当内存使用率超过95%时重启服务",
                condition="memory_usage > 95",
                action=AutomationAction.RESTART_SERVICE,
                parameters={"service_name": "quantification"},
                cooldown_minutes=30
            ),
            AutomationRule(
                id="database_slow_query",
                name="数据库慢查询优化",
                description="当数据库查询时间超过1000ms时优化数据库",
                condition="query_time_ms > 1000",
                action=AutomationAction.OPTIMIZE_DATABASE,
                parameters={"optimization_type": "index"},
                cooldown_minutes=120
            ),
            AutomationRule(
                id="system_health_check",
                name="系统健康检查",
                description="定期备份重要数据",
                condition="True",  # 定期执行
                action=AutomationAction.BACKUP_DATA,
                parameters={"backup_type": "incremental"},
                cooldown_minutes=360  # 6小时执行一次
            )
        ]
        
        for rule in default_rules:
            self.automation_rules[rule.id] = rule
    
    def start(self):
        """启动运维自动化管理器"""
        if self.is_running:
            self.logger.warning("运维自动化管理器已在运行")
            return
        
        self.is_running = True
        
        # 启动系统监控
        self.system_monitor.start_monitoring()
        
        # 启动自动化线程
        self.automation_thread = threading.Thread(target=self._automation_loop, daemon=True)
        self.automation_thread.start()
        
        # 启动任务执行线程
        self.task_executor_thread = threading.Thread(target=self._task_executor_loop, daemon=True)
        self.task_executor_thread.start()
        
        self.logger.info("运维自动化管理器已启动")
    
    def stop(self):
        """停止运维自动化管理器"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 停止系统监控
        self.system_monitor.stop_monitoring()
        
        # 等待线程结束
        if self.automation_thread and self.automation_thread.is_alive():
            self.automation_thread.join(timeout=10)
        
        if self.task_executor_thread and self.task_executor_thread.is_alive():
            self.task_executor_thread.join(timeout=10)
        
        self.logger.info("运维自动化管理器已停止")
    
    def _automation_loop(self):
        """自动化主循环"""
        while self.is_running:
            try:
                # 获取当前系统指标
                current_metrics = self.system_monitor.get_current_metrics()
                if current_metrics:
                    # 检查告警条件
                    self._check_alert_conditions(current_metrics)
                    
                    # 检查自动化规则
                    self._check_automation_rules(current_metrics)
                
                # 等待下次检查
                time.sleep(30)  # 30秒检查一次
                
            except Exception as e:
                self.logger.error(f"自动化循环异常: {e}")
                time.sleep(30)
    
    def _check_alert_conditions(self, metrics: Dict[str, Any]):
        """检查告警条件"""
        # CPU使用率告警
        if metrics.get('cpu_usage', 0) > 80:
            self._generate_alert(
                AlertLevel.WARNING if metrics['cpu_usage'] < 90 else AlertLevel.ERROR,
                "高CPU使用率",
                f"CPU使用率达到 {metrics['cpu_usage']:.1f}%",
                "system",
                metrics
            )
        
        # 内存使用率告警
        if metrics.get('memory_usage', 0) > 85:
            self._generate_alert(
                AlertLevel.WARNING if metrics['memory_usage'] < 95 else AlertLevel.CRITICAL,
                "高内存使用率",
                f"内存使用率达到 {metrics['memory_usage']:.1f}%",
                "system",
                metrics
            )
        
        # 磁盘空间告警
        if metrics.get('disk_free_gb', 100) < 10:
            self._generate_alert(
                AlertLevel.WARNING if metrics['disk_free_gb'] > 5 else AlertLevel.ERROR,
                "磁盘空间不足",
                f"磁盘剩余空间仅 {metrics['disk_free_gb']:.1f}GB",
                "system",
                metrics
            )
    
    def _check_automation_rules(self, metrics: Dict[str, Any]):
        """检查自动化规则"""
        for rule in self.automation_rules.values():
            if not rule.enabled:
                continue
            
            # 检查冷却时间
            if rule.last_executed:
                cooldown_end = rule.last_executed + timedelta(minutes=rule.cooldown_minutes)
                if datetime.now() < cooldown_end:
                    continue
            
            # 评估条件
            try:
                if self._evaluate_condition(rule.condition, metrics):
                    self._trigger_automation_task(rule)
            except Exception as e:
                self.logger.error(f"评估规则 {rule.id} 条件失败: {e}")
    
    def _evaluate_condition(self, condition: str, metrics: Dict[str, Any]) -> bool:
        """评估条件表达式"""
        # 简单的条件评估器
        # 在生产环境中应该使用更安全的表达式评估器
        try:
            # 创建安全的评估环境
            safe_dict = {
                'cpu_usage': metrics.get('cpu_usage', 0),
                'memory_usage': metrics.get('memory_usage', 0),
                'disk_free_gb': metrics.get('disk_free_gb', 100),
                'query_time_ms': metrics.get('query_time_ms', 0),
                'True': True,
                'False': False
            }
            
            return eval(condition, {"__builtins__": {}}, safe_dict)
        except Exception as e:
            self.logger.error(f"条件评估失败: {condition}, 错误: {e}")
            return False
    
    def _trigger_automation_task(self, rule: AutomationRule):
        """触发自动化任务"""
        import uuid
        
        task = AutomationTask(
            id=str(uuid.uuid4()),
            rule_id=rule.id,
            action=rule.action,
            parameters=rule.parameters.copy(),
            status="pending",
            created_at=datetime.now()
        )
        
        self.automation_tasks[task.id] = task
        self.task_queue.append(task.id)
        
        # 更新规则执行时间
        rule.last_executed = datetime.now()
        
        self.logger.info(f"触发自动化任务: {rule.name} (任务ID: {task.id})")
    
    def _task_executor_loop(self):
        """任务执行循环"""
        while self.is_running:
            try:
                if self.task_queue:
                    task_id = self.task_queue.popleft()
                    task = self.automation_tasks.get(task_id)
                    
                    if task and task.status == "pending":
                        self._execute_task(task)
                
                time.sleep(5)  # 5秒检查一次任务队列

            except Exception as e:
                self.logger.error(f"任务执行循环异常: {e}")
                time.sleep(5)

    def _execute_task(self, task: AutomationTask):
        """执行自动化任务"""
        task.status = "running"
        task.started_at = datetime.now()

        try:
            self.logger.info(f"开始执行任务: {task.action.value} (ID: {task.id})")

            result = None
            if task.action == AutomationAction.CLEAR_CACHE:
                result = self._clear_cache(task.parameters)
            elif task.action == AutomationAction.CLEAN_LOGS:
                result = self._clean_logs(task.parameters)
            elif task.action == AutomationAction.RESTART_SERVICE:
                result = self._restart_service(task.parameters)
            elif task.action == AutomationAction.OPTIMIZE_DATABASE:
                result = self._optimize_database(task.parameters)
            elif task.action == AutomationAction.BACKUP_DATA:
                result = self._backup_data(task.parameters)
            elif task.action == AutomationAction.NOTIFY_ADMIN:
                result = self._notify_admin(task.parameters)
            else:
                raise ValueError(f"未知的任务类型: {task.action}")

            task.status = "completed"
            task.result = result
            self.stats['tasks_successful'] += 1

            self.logger.info(f"任务执行成功: {task.id}")

        except Exception as e:
            task.status = "failed"
            task.error = str(e)
            self.logger.error(f"任务执行失败: {task.id}, 错误: {e}")

        finally:
            task.completed_at = datetime.now()
            self.stats['tasks_executed'] += 1

    def _clear_cache(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """清理缓存"""
        cache_types = parameters.get('cache_types', ['memory'])
        cleared_size = 0

        for cache_type in cache_types:
            if cache_type == 'memory':
                # 清理内存缓存
                import gc
                gc.collect()
                cleared_size += 1  # 模拟清理大小
            elif cache_type == 'disk':
                # 清理磁盘缓存
                cache_dir = parameters.get('cache_dir', '/tmp/cache')
                if os.path.exists(cache_dir):
                    import shutil
                    shutil.rmtree(cache_dir, ignore_errors=True)
                    os.makedirs(cache_dir, exist_ok=True)
                    cleared_size += 10  # 模拟清理大小

        return {
            'cache_types_cleared': cache_types,
            'estimated_size_mb': cleared_size,
            'timestamp': datetime.now().isoformat()
        }

    def _clean_logs(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """清理日志文件"""
        retention_days = parameters.get('retention_days', 7)
        log_dirs = parameters.get('log_dirs', ['logs', '/var/log/quantification'])

        cleaned_files = 0
        freed_space_mb = 0

        cutoff_date = datetime.now() - timedelta(days=retention_days)

        for log_dir in log_dirs:
            if os.path.exists(log_dir):
                for root, dirs, files in os.walk(log_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                            if file_mtime < cutoff_date:
                                file_size = os.path.getsize(file_path)
                                os.remove(file_path)
                                cleaned_files += 1
                                freed_space_mb += file_size / (1024 * 1024)
                        except Exception as e:
                            self.logger.warning(f"清理日志文件失败: {file_path}, 错误: {e}")

        return {
            'cleaned_files': cleaned_files,
            'freed_space_mb': round(freed_space_mb, 2),
            'retention_days': retention_days,
            'timestamp': datetime.now().isoformat()
        }

    def _restart_service(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """重启服务"""
        service_name = parameters.get('service_name', 'quantification')

        # 在实际环境中，这里会执行真实的服务重启命令
        # 这里只是模拟
        self.logger.info(f"模拟重启服务: {service_name}")

        return {
            'service_name': service_name,
            'restart_time': datetime.now().isoformat(),
            'status': 'simulated_restart'
        }

    def _optimize_database(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """优化数据库"""
        optimization_type = parameters.get('optimization_type', 'index')

        # 模拟数据库优化
        optimizations_performed = []

        if optimization_type == 'index':
            optimizations_performed.append('重建索引')
        elif optimization_type == 'vacuum':
            optimizations_performed.append('清理数据库')
        elif optimization_type == 'analyze':
            optimizations_performed.append('更新统计信息')

        return {
            'optimization_type': optimization_type,
            'optimizations_performed': optimizations_performed,
            'estimated_improvement': '10-15%',
            'timestamp': datetime.now().isoformat()
        }

    def _backup_data(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """备份数据"""
        backup_type = parameters.get('backup_type', 'incremental')
        backup_path = parameters.get('backup_path', '/backup')

        # 模拟数据备份
        backup_size_mb = 100  # 模拟备份大小

        return {
            'backup_type': backup_type,
            'backup_path': backup_path,
            'backup_size_mb': backup_size_mb,
            'timestamp': datetime.now().isoformat(),
            'status': 'simulated_backup'
        }

    def _notify_admin(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """通知管理员"""
        message = parameters.get('message', '系统告警')
        channels = parameters.get('channels', ['log'])

        # 模拟通知发送
        notifications_sent = []

        for channel in channels:
            if channel == 'log':
                self.logger.warning(f"管理员通知: {message}")
                notifications_sent.append('log')
            elif channel == 'email':
                # 这里可以集成邮件发送
                notifications_sent.append('email')

        return {
            'message': message,
            'channels': notifications_sent,
            'timestamp': datetime.now().isoformat()
        }

    def _generate_alert(self, level: AlertLevel, title: str, message: str,
                       source: str, metrics: Dict[str, Any]):
        """生成告警"""
        import uuid

        alert_id = str(uuid.uuid4())
        alert = Alert(
            id=alert_id,
            level=level,
            title=title,
            message=message,
            timestamp=datetime.now(),
            source=source,
            metrics=metrics
        )

        self.alerts[alert_id] = alert
        self.alert_history.append(alert)
        self.stats['alerts_generated'] += 1

        # 触发告警处理器
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                self.logger.error(f"告警处理器执行失败: {e}")

        self.logger.warning(f"生成告警: [{level.value.upper()}] {title} - {message}")

    def add_alert_handler(self, handler: Callable[[Alert], None]):
        """添加告警处理器"""
        self.alert_handlers.append(handler)

    def add_automation_rule(self, rule: AutomationRule):
        """添加自动化规则"""
        self.automation_rules[rule.id] = rule
        self.logger.info(f"添加自动化规则: {rule.name}")

    def remove_automation_rule(self, rule_id: str):
        """移除自动化规则"""
        if rule_id in self.automation_rules:
            del self.automation_rules[rule_id]
            self.logger.info(f"移除自动化规则: {rule_id}")

    def get_automation_stats(self) -> Dict[str, Any]:
        """获取自动化统计"""
        active_alerts = len([a for a in self.alerts.values() if not a.resolved])
        pending_tasks = len([t for t in self.automation_tasks.values() if t.status == "pending"])
        running_tasks = len([t for t in self.automation_tasks.values() if t.status == "running"])

        return {
            **self.stats,
            'active_alerts': active_alerts,
            'total_alerts': len(self.alerts),
            'pending_tasks': pending_tasks,
            'running_tasks': running_tasks,
            'total_tasks': len(self.automation_tasks),
            'automation_rules': len(self.automation_rules),
            'is_running': self.is_running
        }
