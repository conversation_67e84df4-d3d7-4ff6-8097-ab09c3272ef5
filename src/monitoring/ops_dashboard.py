"""
运维监控仪表板

提供Web界面的运维监控和管理功能，包括：
1. 实时系统监控
2. 告警管理
3. 自动化任务管理
4. 性能分析
5. 容量规划
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from flask import Flask, render_template_string, jsonify, request
import threading
import time

from .ops_automation_manager import OpsAutomationManager, Alert, AutomationTask, AutomationRule
from .intelligent_ops_engine import IntelligentOpsEngine, AnomalyDetection, CapacityPrediction, PerformanceInsight
from .system_monitor import SystemMonitor


class OpsDashboard:
    """
    运维监控仪表板
    
    提供Web界面的运维监控和管理功能
    """
    
    def __init__(self, automation_manager: OpsAutomationManager, 
                 intelligent_engine: IntelligentOpsEngine,
                 port: int = 8002):
        """
        初始化运维仪表板
        
        参数:
            automation_manager: 运维自动化管理器
            intelligent_engine: 智能运维引擎
            port: Web服务端口
        """
        self.automation_manager = automation_manager
        self.intelligent_engine = intelligent_engine
        self.port = port
        self.logger = logging.getLogger(__name__)
        
        # Flask应用
        self.app = Flask(__name__)
        self.app.secret_key = 'ops_dashboard_secret_key'
        
        # 注册路由
        self._register_routes()
        
        # 运行状态
        self.is_running = False
        self.server_thread = None
    
    def _register_routes(self):
        """注册Web路由"""
        
        @self.app.route('/')
        def dashboard():
            """主仪表板页面"""
            return render_template_string(self._get_dashboard_template())
        
        @self.app.route('/api/system/metrics')
        def get_system_metrics():
            """获取系统指标"""
            try:
                metrics = self.automation_manager.system_monitor.get_current_metrics()
                return jsonify({
                    'success': True,
                    'data': metrics,
                    'timestamp': datetime.now().isoformat()
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/alerts')
        def get_alerts():
            """获取告警列表"""
            try:
                alerts = []
                for alert in self.automation_manager.alerts.values():
                    alerts.append({
                        'id': alert.id,
                        'level': alert.level.value,
                        'title': alert.title,
                        'message': alert.message,
                        'timestamp': alert.timestamp.isoformat(),
                        'source': alert.source,
                        'resolved': alert.resolved
                    })
                
                # 按时间倒序排列
                alerts.sort(key=lambda x: x['timestamp'], reverse=True)
                
                return jsonify({
                    'success': True,
                    'data': alerts[:50],  # 最近50个告警
                    'total': len(alerts)
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/tasks')
        def get_tasks():
            """获取自动化任务列表"""
            try:
                tasks = []
                for task in self.automation_manager.automation_tasks.values():
                    tasks.append({
                        'id': task.id,
                        'rule_id': task.rule_id,
                        'action': task.action.value,
                        'status': task.status,
                        'created_at': task.created_at.isoformat(),
                        'started_at': task.started_at.isoformat() if task.started_at else None,
                        'completed_at': task.completed_at.isoformat() if task.completed_at else None,
                        'result': task.result,
                        'error': task.error
                    })
                
                # 按创建时间倒序排列
                tasks.sort(key=lambda x: x['created_at'], reverse=True)
                
                return jsonify({
                    'success': True,
                    'data': tasks[:100],  # 最近100个任务
                    'total': len(tasks)
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/automation/stats')
        def get_automation_stats():
            """获取自动化统计"""
            try:
                stats = self.automation_manager.get_automation_stats()
                engine_stats = self.intelligent_engine.get_engine_stats()
                
                return jsonify({
                    'success': True,
                    'data': {
                        'automation': stats,
                        'intelligence': engine_stats
                    }
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/anomalies')
        def get_anomalies():
            """获取异常检测结果"""
            try:
                anomalies = []
                for anomaly in list(self.intelligent_engine.anomaly_history)[-50:]:
                    anomalies.append({
                        'timestamp': anomaly.timestamp.isoformat(),
                        'metric_name': anomaly.metric_name,
                        'value': anomaly.value,
                        'anomaly_score': anomaly.anomaly_score,
                        'severity': anomaly.severity,
                        'description': anomaly.description
                    })
                
                return jsonify({
                    'success': True,
                    'data': anomalies
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/capacity/predictions')
        def get_capacity_predictions():
            """获取容量预测"""
            try:
                predictions = []
                metrics_to_predict = ['cpu_usage', 'memory_usage', 'disk_usage']
                
                for metric in metrics_to_predict:
                    prediction = self.intelligent_engine.predict_capacity(metric, 24)
                    if prediction:
                        predictions.append({
                            'metric_name': prediction.metric_name,
                            'current_value': prediction.current_value,
                            'predicted_value': prediction.predicted_value,
                            'prediction_horizon_hours': prediction.prediction_horizon_hours,
                            'confidence': prediction.confidence,
                            'trend': prediction.trend,
                            'recommendation': prediction.recommendation
                        })
                
                return jsonify({
                    'success': True,
                    'data': predictions
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/insights')
        def get_performance_insights():
            """获取性能洞察"""
            try:
                # 获取当前系统指标
                current_metrics = self.automation_manager.system_monitor.get_current_metrics()
                if not current_metrics:
                    return jsonify({
                        'success': False,
                        'error': '无法获取当前系统指标'
                    }), 500
                
                # 模拟SystemMetrics对象
                from .system_monitor import SystemMetrics
                system_metrics = SystemMetrics(
                    timestamp=datetime.now().isoformat(),
                    cpu_usage=current_metrics.get('cpu_usage', 0),
                    memory_usage=current_metrics.get('memory_usage', 0),
                    memory_available_gb=current_metrics.get('memory_available_gb', 0),
                    disk_usage=current_metrics.get('disk_usage', 0),
                    disk_free_gb=current_metrics.get('disk_free_gb', 0),
                    network_sent_mb=current_metrics.get('network_sent_mb', 0),
                    network_recv_mb=current_metrics.get('network_recv_mb', 0),
                    process_count=current_metrics.get('process_count', 0),
                    load_average=current_metrics.get('load_average', [0, 0, 0])
                )
                
                insights = self.intelligent_engine.generate_performance_insights(system_metrics)
                
                insights_data = []
                for insight in insights:
                    insights_data.append({
                        'category': insight.category,
                        'issue_type': insight.issue_type,
                        'severity': insight.severity,
                        'description': insight.description,
                        'impact': insight.impact,
                        'recommendation': insight.recommendation,
                        'estimated_improvement': insight.estimated_improvement
                    })
                
                return jsonify({
                    'success': True,
                    'data': insights_data
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/rules')
        def get_automation_rules():
            """获取自动化规则"""
            try:
                rules = []
                for rule in self.automation_manager.automation_rules.values():
                    rules.append({
                        'id': rule.id,
                        'name': rule.name,
                        'description': rule.description,
                        'condition': rule.condition,
                        'action': rule.action.value,
                        'enabled': rule.enabled,
                        'cooldown_minutes': rule.cooldown_minutes,
                        'last_executed': rule.last_executed.isoformat() if rule.last_executed else None
                    })
                
                return jsonify({
                    'success': True,
                    'data': rules
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/rules/<rule_id>/toggle', methods=['POST'])
        def toggle_rule(rule_id):
            """启用/禁用自动化规则"""
            try:
                if rule_id in self.automation_manager.automation_rules:
                    rule = self.automation_manager.automation_rules[rule_id]
                    rule.enabled = not rule.enabled
                    
                    return jsonify({
                        'success': True,
                        'message': f"规则 {rule.name} 已{'启用' if rule.enabled else '禁用'}",
                        'enabled': rule.enabled
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': '规则不存在'
                    }), 404
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
    
    def _get_dashboard_template(self) -> str:
        """获取仪表板HTML模板"""
        return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>运维监控仪表板</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
        .header { background: #2c3e50; color: white; padding: 1rem; text-align: center; }
        .container { max-width: 1400px; margin: 0 auto; padding: 2rem; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; }
        .card { background: white; border-radius: 8px; padding: 1.5rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .card h3 { color: #2c3e50; margin-bottom: 1rem; border-bottom: 2px solid #3498db; padding-bottom: 0.5rem; }
        .metric { display: flex; justify-content: space-between; margin: 0.5rem 0; padding: 0.5rem; background: #f8f9fa; border-radius: 4px; }
        .metric-value { font-weight: bold; color: #27ae60; }
        .alert { padding: 0.75rem; margin: 0.5rem 0; border-radius: 4px; border-left: 4px solid; }
        .alert.info { background: #d1ecf1; border-color: #17a2b8; }
        .alert.warning { background: #fff3cd; border-color: #ffc107; }
        .alert.error { background: #f8d7da; border-color: #dc3545; }
        .alert.critical { background: #f5c6cb; border-color: #721c24; }
        .task { padding: 0.5rem; margin: 0.25rem 0; background: #f8f9fa; border-radius: 4px; font-size: 0.9rem; }
        .task.completed { background: #d4edda; }
        .task.failed { background: #f8d7da; }
        .task.running { background: #fff3cd; }
        .btn { padding: 0.5rem 1rem; border: none; border-radius: 4px; cursor: pointer; margin: 0.25rem; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .refresh-btn { position: fixed; bottom: 2rem; right: 2rem; background: #007bff; color: white; border: none; border-radius: 50%; width: 60px; height: 60px; font-size: 1.2rem; cursor: pointer; box-shadow: 0 4px 12px rgba(0,0,0,0.3); }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 0.5rem; }
        .status-online { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
        .loading { text-align: center; color: #6c757d; padding: 2rem; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 运维监控仪表板</h1>
        <p>实时系统监控 | 智能运维自动化 | 性能分析</p>
    </div>
    
    <div class="container">
        <div class="grid">
            <!-- 系统指标 -->
            <div class="card">
                <h3>📊 系统指标</h3>
                <div id="system-metrics" class="loading">加载中...</div>
            </div>
            
            <!-- 告警信息 -->
            <div class="card">
                <h3>🚨 告警信息</h3>
                <div id="alerts" class="loading">加载中...</div>
            </div>
            
            <!-- 自动化任务 -->
            <div class="card">
                <h3>🤖 自动化任务</h3>
                <div id="tasks" class="loading">加载中...</div>
            </div>
            
            <!-- 性能洞察 -->
            <div class="card">
                <h3>💡 性能洞察</h3>
                <div id="insights" class="loading">加载中...</div>
            </div>
            
            <!-- 容量预测 -->
            <div class="card">
                <h3>📈 容量预测</h3>
                <div id="predictions" class="loading">加载中...</div>
            </div>
            
            <!-- 自动化规则 -->
            <div class="card">
                <h3>⚙️ 自动化规则</h3>
                <div id="rules" class="loading">加载中...</div>
            </div>
        </div>
    </div>
    
    <button class="refresh-btn" onclick="refreshAll()" title="刷新所有数据">🔄</button>
    
    <script>
        // 自动刷新间隔（秒）
        const REFRESH_INTERVAL = 30;
        
        // 加载系统指标
        async function loadSystemMetrics() {
            try {
                const response = await fetch('/api/system/metrics');
                const result = await response.json();
                
                if (result.success && result.data) {
                    const metrics = result.data;
                    document.getElementById('system-metrics').innerHTML = `
                        <div class="metric">
                            <span>CPU使用率</span>
                            <span class="metric-value">${metrics.cpu_usage?.toFixed(1) || 0}%</span>
                        </div>
                        <div class="metric">
                            <span>内存使用率</span>
                            <span class="metric-value">${metrics.memory_usage?.toFixed(1) || 0}%</span>
                        </div>
                        <div class="metric">
                            <span>磁盘使用率</span>
                            <span class="metric-value">${metrics.disk_usage?.toFixed(1) || 0}%</span>
                        </div>
                        <div class="metric">
                            <span>可用内存</span>
                            <span class="metric-value">${metrics.memory_available_gb?.toFixed(1) || 0}GB</span>
                        </div>
                        <div class="metric">
                            <span>剩余磁盘</span>
                            <span class="metric-value">${metrics.disk_free_gb?.toFixed(1) || 0}GB</span>
                        </div>
                        <div class="metric">
                            <span>进程数量</span>
                            <span class="metric-value">${metrics.process_count || 0}</span>
                        </div>
                    `;
                } else {
                    document.getElementById('system-metrics').innerHTML = '<div class="alert error">无法获取系统指标</div>';
                }
            } catch (error) {
                document.getElementById('system-metrics').innerHTML = '<div class="alert error">加载失败: ' + error.message + '</div>';
            }
        }
        
        // 加载告警信息
        async function loadAlerts() {
            try {
                const response = await fetch('/api/alerts');
                const result = await response.json();
                
                if (result.success) {
                    const alerts = result.data.slice(0, 10); // 显示最近10个告警
                    if (alerts.length === 0) {
                        document.getElementById('alerts').innerHTML = '<div class="alert info">暂无告警</div>';
                    } else {
                        document.getElementById('alerts').innerHTML = alerts.map(alert => `
                            <div class="alert ${alert.level}">
                                <strong>${alert.title}</strong><br>
                                ${alert.message}<br>
                                <small>${new Date(alert.timestamp).toLocaleString()}</small>
                            </div>
                        `).join('');
                    }
                } else {
                    document.getElementById('alerts').innerHTML = '<div class="alert error">无法获取告警信息</div>';
                }
            } catch (error) {
                document.getElementById('alerts').innerHTML = '<div class="alert error">加载失败: ' + error.message + '</div>';
            }
        }
        
        // 加载自动化任务
        async function loadTasks() {
            try {
                const response = await fetch('/api/tasks');
                const result = await response.json();
                
                if (result.success) {
                    const tasks = result.data.slice(0, 10); // 显示最近10个任务
                    if (tasks.length === 0) {
                        document.getElementById('tasks').innerHTML = '<div class="alert info">暂无任务</div>';
                    } else {
                        document.getElementById('tasks').innerHTML = tasks.map(task => `
                            <div class="task ${task.status}">
                                <strong>${task.action}</strong> - ${task.status}<br>
                                <small>创建时间: ${new Date(task.created_at).toLocaleString()}</small>
                                ${task.error ? '<br><span style="color: red;">错误: ' + task.error + '</span>' : ''}
                            </div>
                        `).join('');
                    }
                } else {
                    document.getElementById('tasks').innerHTML = '<div class="alert error">无法获取任务信息</div>';
                }
            } catch (error) {
                document.getElementById('tasks').innerHTML = '<div class="alert error">加载失败: ' + error.message + '</div>';
            }
        }
        
        // 加载性能洞察
        async function loadInsights() {
            try {
                const response = await fetch('/api/insights');
                const result = await response.json();
                
                if (result.success) {
                    const insights = result.data;
                    if (insights.length === 0) {
                        document.getElementById('insights').innerHTML = '<div class="alert info">系统运行正常</div>';
                    } else {
                        document.getElementById('insights').innerHTML = insights.map(insight => `
                            <div class="alert ${insight.severity}">
                                <strong>${insight.description}</strong><br>
                                影响: ${insight.impact}<br>
                                建议: ${insight.recommendation}<br>
                                <small>预期改善: ${insight.estimated_improvement}</small>
                            </div>
                        `).join('');
                    }
                } else {
                    document.getElementById('insights').innerHTML = '<div class="alert error">无法获取性能洞察</div>';
                }
            } catch (error) {
                document.getElementById('insights').innerHTML = '<div class="alert error">加载失败: ' + error.message + '</div>';
            }
        }
        
        // 加载容量预测
        async function loadPredictions() {
            try {
                const response = await fetch('/api/capacity/predictions');
                const result = await response.json();
                
                if (result.success) {
                    const predictions = result.data;
                    if (predictions.length === 0) {
                        document.getElementById('predictions').innerHTML = '<div class="alert info">暂无预测数据</div>';
                    } else {
                        document.getElementById('predictions').innerHTML = predictions.map(pred => `
                            <div class="metric">
                                <div>
                                    <strong>${pred.metric_name}</strong><br>
                                    当前: ${pred.current_value.toFixed(1)}% → 预测: ${pred.predicted_value.toFixed(1)}%<br>
                                    趋势: ${pred.trend} (置信度: ${(pred.confidence * 100).toFixed(1)}%)<br>
                                    <small>${pred.recommendation}</small>
                                </div>
                            </div>
                        `).join('');
                    }
                } else {
                    document.getElementById('predictions').innerHTML = '<div class="alert error">无法获取容量预测</div>';
                }
            } catch (error) {
                document.getElementById('predictions').innerHTML = '<div class="alert error">加载失败: ' + error.message + '</div>';
            }
        }
        
        // 加载自动化规则
        async function loadRules() {
            try {
                const response = await fetch('/api/rules');
                const result = await response.json();
                
                if (result.success) {
                    const rules = result.data;
                    document.getElementById('rules').innerHTML = rules.map(rule => `
                        <div class="task">
                            <span class="status-indicator ${rule.enabled ? 'status-online' : 'status-error'}"></span>
                            <strong>${rule.name}</strong><br>
                            ${rule.description}<br>
                            <small>条件: ${rule.condition} | 操作: ${rule.action}</small><br>
                            <button class="btn ${rule.enabled ? 'btn-danger' : 'btn-success'}" 
                                    onclick="toggleRule('${rule.id}')">
                                ${rule.enabled ? '禁用' : '启用'}
                            </button>
                        </div>
                    `).join('');
                } else {
                    document.getElementById('rules').innerHTML = '<div class="alert error">无法获取规则信息</div>';
                }
            } catch (error) {
                document.getElementById('rules').innerHTML = '<div class="alert error">加载失败: ' + error.message + '</div>';
            }
        }
        
        // 切换规则状态
        async function toggleRule(ruleId) {
            try {
                const response = await fetch(`/api/rules/${ruleId}/toggle`, { method: 'POST' });
                const result = await response.json();
                
                if (result.success) {
                    loadRules(); // 重新加载规则列表
                } else {
                    alert('操作失败: ' + result.error);
                }
            } catch (error) {
                alert('操作失败: ' + error.message);
            }
        }
        
        // 刷新所有数据
        function refreshAll() {
            loadSystemMetrics();
            loadAlerts();
            loadTasks();
            loadInsights();
            loadPredictions();
            loadRules();
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshAll();
            
            // 设置自动刷新
            setInterval(refreshAll, REFRESH_INTERVAL * 1000);
        });
    </script>
</body>
</html>
        '''
    
    def start(self):
        """启动仪表板Web服务"""
        if self.is_running:
            self.logger.warning("运维仪表板已在运行")
            return
        
        self.is_running = True
        
        def run_server():
            self.app.run(host='0.0.0.0', port=self.port, debug=False, use_reloader=False)
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        
        self.logger.info(f"运维仪表板已启动，访问地址: http://localhost:{self.port}")
    
    def stop(self):
        """停止仪表板Web服务"""
        self.is_running = False
        self.logger.info("运维仪表板已停止")
