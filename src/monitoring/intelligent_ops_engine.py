"""
智能运维决策引擎

基于机器学习和规则引擎的智能运维决策系统，提供：
1. 异常检测和预测
2. 智能故障诊断
3. 自适应阈值调整
4. 容量规划建议
5. 性能优化建议
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import deque
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

from .system_monitor import SystemMetrics, DatabaseMetrics


@dataclass
class AnomalyDetection:
    """异常检测结果"""
    timestamp: datetime
    metric_name: str
    value: float
    is_anomaly: bool
    anomaly_score: float
    severity: str  # low, medium, high
    description: str


@dataclass
class CapacityPrediction:
    """容量预测结果"""
    metric_name: str
    current_value: float
    predicted_value: float
    prediction_horizon_hours: int
    confidence: float
    trend: str  # increasing, decreasing, stable
    recommendation: str


@dataclass
class PerformanceInsight:
    """性能洞察"""
    category: str  # cpu, memory, disk, network, database
    issue_type: str
    severity: str
    description: str
    impact: str
    recommendation: str
    estimated_improvement: str


class IntelligentOpsEngine:
    """
    智能运维决策引擎
    
    使用机器学习算法进行异常检测、容量预测和性能优化建议
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化智能运维引擎
        
        参数:
            config: 配置字典
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 历史数据存储
        self.metrics_history: deque = deque(maxlen=10000)  # 存储最近10000个数据点
        self.anomaly_history: deque = deque(maxlen=1000)
        
        # 机器学习模型
        self.anomaly_detector = None
        self.scaler = StandardScaler()
        self.model_trained = False
        
        # 自适应阈值
        self.adaptive_thresholds = {
            'cpu_usage': {'warning': 80, 'critical': 90},
            'memory_usage': {'warning': 85, 'critical': 95},
            'disk_usage': {'warning': 80, 'critical': 90},
            'query_time_ms': {'warning': 500, 'critical': 1000}
        }
        
        # 性能基线
        self.performance_baseline = {}
        self.baseline_window_hours = 24
        
        # 统计信息
        self.stats = {
            'anomalies_detected': 0,
            'predictions_made': 0,
            'insights_generated': 0,
            'model_accuracy': 0.0,
            'last_training': None
        }
    
    def add_metrics(self, system_metrics: SystemMetrics, db_metrics: Optional[DatabaseMetrics] = None):
        """
        添加系统指标数据
        
        参数:
            system_metrics: 系统指标
            db_metrics: 数据库指标（可选）
        """
        # 转换为数值向量
        metrics_vector = self._metrics_to_vector(system_metrics, db_metrics)
        
        # 添加到历史数据
        self.metrics_history.append({
            'timestamp': datetime.now(),
            'system_metrics': system_metrics,
            'db_metrics': db_metrics,
            'vector': metrics_vector
        })
        
        # 如果有足够的历史数据，训练模型
        if len(self.metrics_history) >= 100 and not self.model_trained:
            self._train_anomaly_detector()
        
        # 更新性能基线
        self._update_performance_baseline()
    
    def _metrics_to_vector(self, system_metrics: SystemMetrics, 
                          db_metrics: Optional[DatabaseMetrics] = None) -> np.ndarray:
        """将指标转换为数值向量"""
        vector = [
            system_metrics.cpu_usage,
            system_metrics.memory_usage,
            system_metrics.disk_usage,
            system_metrics.network_sent_mb,
            system_metrics.network_recv_mb,
            system_metrics.process_count,
            system_metrics.load_average[0] if system_metrics.load_average else 0
        ]
        
        if db_metrics:
            vector.extend([
                db_metrics.connections,
                db_metrics.query_time_ms,
                db_metrics.health_score
            ])
        else:
            vector.extend([0, 0, 100])  # 默认值
        
        return np.array(vector)
    
    def _train_anomaly_detector(self):
        """训练异常检测模型"""
        try:
            # 准备训练数据
            vectors = [item['vector'] for item in list(self.metrics_history)[-500:]]  # 使用最近500个数据点
            X = np.array(vectors)
            
            # 标准化数据
            X_scaled = self.scaler.fit_transform(X)
            
            # 训练Isolation Forest模型
            self.anomaly_detector = IsolationForest(
                contamination=0.1,  # 假设10%的数据是异常
                random_state=42,
                n_estimators=100
            )
            self.anomaly_detector.fit(X_scaled)
            
            self.model_trained = True
            self.stats['last_training'] = datetime.now()
            
            self.logger.info("异常检测模型训练完成")
            
        except Exception as e:
            self.logger.error(f"训练异常检测模型失败: {e}")
    
    def detect_anomalies(self, system_metrics: SystemMetrics, 
                        db_metrics: Optional[DatabaseMetrics] = None) -> List[AnomalyDetection]:
        """
        检测异常
        
        参数:
            system_metrics: 系统指标
            db_metrics: 数据库指标
            
        返回:
            异常检测结果列表
        """
        anomalies = []
        
        if not self.model_trained:
            return anomalies
        
        try:
            # 转换为向量
            vector = self._metrics_to_vector(system_metrics, db_metrics)
            X = vector.reshape(1, -1)
            X_scaled = self.scaler.transform(X)
            
            # 预测异常
            anomaly_score = self.anomaly_detector.decision_function(X_scaled)[0]
            is_anomaly = self.anomaly_detector.predict(X_scaled)[0] == -1
            
            if is_anomaly:
                # 确定异常的具体指标
                metric_names = ['cpu_usage', 'memory_usage', 'disk_usage', 'network_sent_mb', 
                               'network_recv_mb', 'process_count', 'load_average']
                if db_metrics:
                    metric_names.extend(['connections', 'query_time_ms', 'health_score'])
                
                # 找出最异常的指标
                for i, (metric_name, value) in enumerate(zip(metric_names, vector)):
                    if i < len(metric_names):
                        severity = self._calculate_anomaly_severity(anomaly_score)
                        
                        anomaly = AnomalyDetection(
                            timestamp=datetime.now(),
                            metric_name=metric_name,
                            value=value,
                            is_anomaly=True,
                            anomaly_score=abs(anomaly_score),
                            severity=severity,
                            description=f"{metric_name} 出现异常，当前值: {value:.2f}"
                        )
                        anomalies.append(anomaly)
                        self.anomaly_history.append(anomaly)
                
                self.stats['anomalies_detected'] += len(anomalies)
        
        except Exception as e:
            self.logger.error(f"异常检测失败: {e}")
        
        return anomalies
    
    def _calculate_anomaly_severity(self, anomaly_score: float) -> str:
        """计算异常严重程度"""
        abs_score = abs(anomaly_score)
        if abs_score > 0.5:
            return "high"
        elif abs_score > 0.3:
            return "medium"
        else:
            return "low"
    
    def predict_capacity(self, metric_name: str, horizon_hours: int = 24) -> Optional[CapacityPrediction]:
        """
        预测容量需求
        
        参数:
            metric_name: 指标名称
            horizon_hours: 预测时间范围（小时）
            
        返回:
            容量预测结果
        """
        try:
            # 获取历史数据
            recent_data = list(self.metrics_history)[-100:]  # 最近100个数据点
            if len(recent_data) < 10:
                return None
            
            # 提取指标值
            values = []
            timestamps = []
            
            for item in recent_data:
                timestamp = item['timestamp']
                system_metrics = item['system_metrics']
                db_metrics = item['db_metrics']
                
                value = self._extract_metric_value(metric_name, system_metrics, db_metrics)
                if value is not None:
                    values.append(value)
                    timestamps.append(timestamp)
            
            if len(values) < 5:
                return None
            
            # 简单线性趋势预测
            x = np.arange(len(values))
            coeffs = np.polyfit(x, values, 1)
            trend_slope = coeffs[0]
            
            # 预测未来值
            future_x = len(values) + horizon_hours
            predicted_value = np.polyval(coeffs, future_x)
            
            # 计算置信度（基于R²）
            predicted_values = np.polyval(coeffs, x)
            ss_res = np.sum((values - predicted_values) ** 2)
            ss_tot = np.sum((values - np.mean(values)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
            confidence = max(0, min(1, r_squared))
            
            # 确定趋势
            if abs(trend_slope) < 0.01:
                trend = "stable"
            elif trend_slope > 0:
                trend = "increasing"
            else:
                trend = "decreasing"
            
            # 生成建议
            recommendation = self._generate_capacity_recommendation(
                metric_name, values[-1], predicted_value, trend
            )
            
            prediction = CapacityPrediction(
                metric_name=metric_name,
                current_value=values[-1],
                predicted_value=predicted_value,
                prediction_horizon_hours=horizon_hours,
                confidence=confidence,
                trend=trend,
                recommendation=recommendation
            )
            
            self.stats['predictions_made'] += 1
            return prediction
            
        except Exception as e:
            self.logger.error(f"容量预测失败: {e}")
            return None
    
    def _extract_metric_value(self, metric_name: str, system_metrics: SystemMetrics, 
                             db_metrics: Optional[DatabaseMetrics]) -> Optional[float]:
        """从指标对象中提取指定指标的值"""
        if hasattr(system_metrics, metric_name):
            return getattr(system_metrics, metric_name)
        elif db_metrics and hasattr(db_metrics, metric_name):
            return getattr(db_metrics, metric_name)
        return None
    
    def _generate_capacity_recommendation(self, metric_name: str, current_value: float, 
                                        predicted_value: float, trend: str) -> str:
        """生成容量建议"""
        if trend == "increasing":
            if metric_name in ['cpu_usage', 'memory_usage', 'disk_usage']:
                if predicted_value > 90:
                    return f"建议立即扩容，预测{metric_name}将达到{predicted_value:.1f}%"
                elif predicted_value > 80:
                    return f"建议准备扩容，预测{metric_name}将达到{predicted_value:.1f}%"
                else:
                    return f"当前容量充足，但需要监控{metric_name}增长趋势"
            else:
                return f"监控{metric_name}增长趋势，当前值{current_value:.2f}"
        elif trend == "decreasing":
            return f"{metric_name}呈下降趋势，系统负载减轻"
        else:
            return f"{metric_name}保持稳定，无需特殊处理"
    
    def generate_performance_insights(self, system_metrics: SystemMetrics, 
                                    db_metrics: Optional[DatabaseMetrics] = None) -> List[PerformanceInsight]:
        """
        生成性能洞察
        
        参数:
            system_metrics: 系统指标
            db_metrics: 数据库指标
            
        返回:
            性能洞察列表
        """
        insights = []
        
        # CPU性能分析
        if system_metrics.cpu_usage > 80:
            insights.append(PerformanceInsight(
                category="cpu",
                issue_type="high_usage",
                severity="high" if system_metrics.cpu_usage > 90 else "medium",
                description=f"CPU使用率过高: {system_metrics.cpu_usage:.1f}%",
                impact="可能导致系统响应缓慢",
                recommendation="考虑优化CPU密集型任务或增加CPU资源",
                estimated_improvement="20-30%性能提升"
            ))
        
        # 内存性能分析
        if system_metrics.memory_usage > 85:
            insights.append(PerformanceInsight(
                category="memory",
                issue_type="high_usage",
                severity="critical" if system_metrics.memory_usage > 95 else "high",
                description=f"内存使用率过高: {system_metrics.memory_usage:.1f}%",
                impact="可能导致系统交换或OOM",
                recommendation="清理内存缓存或增加内存容量",
                estimated_improvement="15-25%性能提升"
            ))
        
        # 磁盘性能分析
        if system_metrics.disk_usage > 80:
            insights.append(PerformanceInsight(
                category="disk",
                issue_type="high_usage",
                severity="high" if system_metrics.disk_usage > 90 else "medium",
                description=f"磁盘使用率过高: {system_metrics.disk_usage:.1f}%",
                impact="可能影响数据写入和系统稳定性",
                recommendation="清理不必要文件或扩展存储空间",
                estimated_improvement="10-20%性能提升"
            ))
        
        # 数据库性能分析
        if db_metrics:
            if db_metrics.query_time_ms > 500:
                insights.append(PerformanceInsight(
                    category="database",
                    issue_type="slow_query",
                    severity="high" if db_metrics.query_time_ms > 1000 else "medium",
                    description=f"数据库查询缓慢: {db_metrics.query_time_ms:.1f}ms",
                    impact="影响应用响应时间和用户体验",
                    recommendation="优化查询语句、添加索引或升级数据库硬件",
                    estimated_improvement="30-50%查询性能提升"
                ))
        
        self.stats['insights_generated'] += len(insights)
        return insights
    
    def _update_performance_baseline(self):
        """更新性能基线"""
        if len(self.metrics_history) < 10:
            return
        
        # 计算最近24小时的平均值作为基线
        cutoff_time = datetime.now() - timedelta(hours=self.baseline_window_hours)
        recent_metrics = [
            item for item in self.metrics_history 
            if item['timestamp'] > cutoff_time
        ]
        
        if not recent_metrics:
            return
        
        # 计算各指标的平均值
        cpu_values = [item['system_metrics'].cpu_usage for item in recent_metrics]
        memory_values = [item['system_metrics'].memory_usage for item in recent_metrics]
        disk_values = [item['system_metrics'].disk_usage for item in recent_metrics]
        
        self.performance_baseline = {
            'cpu_usage': np.mean(cpu_values),
            'memory_usage': np.mean(memory_values),
            'disk_usage': np.mean(disk_values),
            'updated_at': datetime.now()
        }
    
    def get_engine_stats(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        return {
            **self.stats,
            'metrics_history_size': len(self.metrics_history),
            'anomaly_history_size': len(self.anomaly_history),
            'model_trained': self.model_trained,
            'baseline_updated': self.performance_baseline.get('updated_at'),
            'adaptive_thresholds': self.adaptive_thresholds
        }
