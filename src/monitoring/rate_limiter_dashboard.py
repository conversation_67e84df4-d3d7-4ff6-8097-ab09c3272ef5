#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
限流器实时性能监控仪表板
提供限流器性能的可视化监控和分析
"""

import time
import json
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from collections import defaultdict, deque
import logging

# {{ AURA-X: Add - 告警系统增强. Source: 最优实践告警系统 }}
from enum import Enum

class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class AlertType(Enum):
    """告警类型"""
    HIGH_RESPONSE_TIME = "high_response_time"
    LOW_SUCCESS_RATE = "low_success_rate"
    FREQUENT_LEVEL_CHANGES = "frequent_level_changes"
    API_CONGESTION = "api_congestion"
    THROUGHPUT_DROP = "throughput_drop"

class Alert:
    """告警记录"""

    def __init__(self, alert_type: AlertType, level: AlertLevel,
                 api_name: str, message: str, value: float = None,
                 threshold: float = None):
        self.alert_type = alert_type
        self.level = level
        self.api_name = api_name
        self.message = message
        self.value = value
        self.threshold = threshold
        self.timestamp = time.time()
        self.resolved = False
        self.resolve_time = None

    def resolve(self):
        """解决告警"""
        self.resolved = True
        self.resolve_time = time.time()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'alert_type': self.alert_type.value,
            'level': self.level.value,
            'api_name': self.api_name,
            'message': self.message,
            'value': self.value,
            'threshold': self.threshold,
            'timestamp': self.timestamp,
            'time_str': datetime.fromtimestamp(self.timestamp).strftime('%H:%M:%S'),
            'resolved': self.resolved,
            'resolve_time': self.resolve_time,
            'duration': (self.resolve_time or time.time()) - self.timestamp
        }

class RateLimiterDashboard:
    """
    限流器实时性能监控仪表板
    
    {{ AURA-X: Add - 监控可视化增强，实时性能仪表板. Source: 最优实践监控可视化 }}
    """
    
    def __init__(self, max_history_minutes: int = 60):
        """
        初始化监控仪表板

        参数:
            max_history_minutes: 历史数据保留时间（分钟）
        """
        self.max_history_minutes = max_history_minutes
        self.logger = logging.getLogger(__name__)

        # {{ AURA-X: Add - 告警系统初始化. Source: 最优实践告警系统 }}
        # 告警配置
        self.alert_thresholds = {
            'response_time_warning': 2.0,      # 响应时间警告阈值（秒）
            'response_time_critical': 5.0,     # 响应时间严重阈值（秒）
            'success_rate_warning': 90.0,      # 成功率警告阈值（%）
            'success_rate_critical': 80.0,     # 成功率严重阈值（%）
            'level_changes_warning': 10,       # 等级变化警告阈值（次/10分钟）
            'level_changes_critical': 20,      # 等级变化严重阈值（次/10分钟）
            'throughput_drop_warning': 0.5,    # 吞吐量下降警告阈值（倍数）
            'throughput_drop_critical': 0.3    # 吞吐量下降严重阈值（倍数）
        }

        # 告警存储
        self.active_alerts = {}  # 活跃告警 {alert_key: Alert}
        self.alert_history = deque(maxlen=1000)  # 告警历史
        self.alert_callbacks = []  # 告警回调函数

        # 告警抑制（避免重复告警）
        self.alert_suppression = defaultdict(float)  # {alert_key: last_alert_time}
        
        # 性能数据存储
        self.api_metrics = defaultdict(lambda: {
            'response_times': deque(maxlen=1000),  # 最近1000次响应时间
            'timestamps': deque(maxlen=1000),      # 对应时间戳
            'success_count': 0,                    # 成功次数
            'error_count': 0,                      # 错误次数
            'level_history': deque(maxlen=100),    # 性能等级历史
            'level_timestamps': deque(maxlen=100), # 等级变化时间戳
            'current_level': 'normal',             # 当前性能等级
            'last_update': time.time()             # 最后更新时间
        })
        
        # 全局统计
        self.global_stats = {
            'total_calls': 0,
            'total_errors': 0,
            'start_time': time.time(),
            'peak_throughput': 0.0,
            'peak_throughput_time': None
        }
        
        # 实时计算缓存
        self._cache = {}
        self._cache_expiry = 0
        self._cache_duration = 5  # 缓存5秒
        
        # 线程锁
        self._lock = threading.RLock()
        
        self.logger.info("限流器监控仪表板初始化完成")
    
    def record_api_call(self, api_name: str, response_time: float, 
                       success: bool, level: str, **kwargs):
        """
        记录API调用数据
        
        参数:
            api_name: API名称
            response_time: 响应时间
            success: 是否成功
            level: 性能等级
            **kwargs: 其他参数
        """
        with self._lock:
            current_time = time.time()
            metrics = self.api_metrics[api_name]
            
            # 记录响应时间和时间戳
            metrics['response_times'].append(response_time)
            metrics['timestamps'].append(current_time)
            
            # 更新计数器
            if success:
                metrics['success_count'] += 1
                self.global_stats['total_calls'] += 1
            else:
                metrics['error_count'] += 1
                self.global_stats['total_errors'] += 1
            
            # 记录性能等级变化
            if metrics['current_level'] != level:
                metrics['level_history'].append({
                    'from': metrics['current_level'],
                    'to': level,
                    'timestamp': current_time,
                    'response_time': response_time
                })
                metrics['level_timestamps'].append(current_time)
                metrics['current_level'] = level
                
                self.logger.debug(f"API {api_name} 性能等级变化: {metrics['current_level']} → {level}")
            
            metrics['last_update'] = current_time
            
            # 清理过期数据
            self._cleanup_expired_data()
            
            # 更新峰值吞吐量
            self._update_peak_throughput()

            # {{ AURA-X: Add - 告警检查. Source: 最优实践告警系统 }}
            # 检查告警条件
            self._check_alerts(api_name, response_time, success, level)
    
    def _cleanup_expired_data(self):
        """清理过期的历史数据"""
        cutoff_time = time.time() - (self.max_history_minutes * 60)
        
        for api_name, metrics in self.api_metrics.items():
            # 清理响应时间数据
            while (metrics['timestamps'] and 
                   metrics['timestamps'][0] < cutoff_time):
                metrics['timestamps'].popleft()
                metrics['response_times'].popleft()
            
            # 清理等级变化数据
            while (metrics['level_timestamps'] and 
                   metrics['level_timestamps'][0] < cutoff_time):
                metrics['level_timestamps'].popleft()
                metrics['level_history'].popleft()
    
    def _update_peak_throughput(self):
        """更新峰值吞吐量"""
        current_time = time.time()
        
        # 计算最近1分钟的吞吐量
        minute_ago = current_time - 60
        total_calls = 0
        
        for metrics in self.api_metrics.values():
            for timestamp in metrics['timestamps']:
                if timestamp >= minute_ago:
                    total_calls += 1
        
        throughput = total_calls / 60.0  # 每秒调用数
        
        if throughput > self.global_stats['peak_throughput']:
            self.global_stats['peak_throughput'] = throughput
            self.global_stats['peak_throughput_time'] = current_time

    def _check_alerts(self, api_name: str, response_time: float, success: bool, level: str):
        """
        检查告警条件

        {{ AURA-X: Add - 告警检查核心逻辑. Source: 最优实践告警系统 }}

        参数:
            api_name: API名称
            response_time: 响应时间
            success: 是否成功
            level: 性能等级
        """
        current_time = time.time()

        # 1. 检查响应时间告警
        self._check_response_time_alert(api_name, response_time, current_time)

        # 2. 检查成功率告警
        self._check_success_rate_alert(api_name, current_time)

        # 3. 检查API拥塞告警
        self._check_congestion_alert(api_name, level, current_time)

        # 4. 检查等级变化频率告警
        self._check_level_changes_alert(api_name, current_time)

        # 5. 检查吞吐量下降告警
        self._check_throughput_drop_alert(api_name, current_time)

    def _check_response_time_alert(self, api_name: str, response_time: float, current_time: float):
        """检查响应时间告警"""
        alert_key = f"{api_name}_response_time"

        if response_time >= self.alert_thresholds['response_time_critical']:
            self._trigger_alert(
                alert_key, AlertType.HIGH_RESPONSE_TIME, AlertLevel.CRITICAL,
                api_name, f"响应时间严重超标: {response_time:.3f}s",
                response_time, self.alert_thresholds['response_time_critical'], current_time
            )
        elif response_time >= self.alert_thresholds['response_time_warning']:
            self._trigger_alert(
                alert_key, AlertType.HIGH_RESPONSE_TIME, AlertLevel.WARNING,
                api_name, f"响应时间超标: {response_time:.3f}s",
                response_time, self.alert_thresholds['response_time_warning'], current_time
            )
        else:
            # 响应时间正常，解决相关告警
            self._resolve_alert(alert_key)

    def _check_success_rate_alert(self, api_name: str, current_time: float):
        """检查成功率告警"""
        if api_name not in self.api_metrics:
            return

        metrics = self.api_metrics[api_name]
        total_calls = metrics['success_count'] + metrics['error_count']

        if total_calls >= 10:  # 至少10次调用才检查成功率
            success_rate = metrics['success_count'] / total_calls * 100
            alert_key = f"{api_name}_success_rate"

            if success_rate <= self.alert_thresholds['success_rate_critical']:
                self._trigger_alert(
                    alert_key, AlertType.LOW_SUCCESS_RATE, AlertLevel.CRITICAL,
                    api_name, f"成功率严重偏低: {success_rate:.1f}%",
                    success_rate, self.alert_thresholds['success_rate_critical'], current_time
                )
            elif success_rate <= self.alert_thresholds['success_rate_warning']:
                self._trigger_alert(
                    alert_key, AlertType.LOW_SUCCESS_RATE, AlertLevel.WARNING,
                    api_name, f"成功率偏低: {success_rate:.1f}%",
                    success_rate, self.alert_thresholds['success_rate_warning'], current_time
                )
            else:
                self._resolve_alert(alert_key)

    def _check_congestion_alert(self, api_name: str, level: str, current_time: float):
        """检查API拥塞告警"""
        alert_key = f"{api_name}_congestion"

        if level == 'congested':
            self._trigger_alert(
                alert_key, AlertType.API_CONGESTION, AlertLevel.ERROR,
                api_name, f"API进入拥塞状态", None, None, current_time
            )
        else:
            self._resolve_alert(alert_key)

    def _check_level_changes_alert(self, api_name: str, current_time: float):
        """检查等级变化频率告警"""
        if api_name not in self.api_metrics:
            return

        metrics = self.api_metrics[api_name]

        # 统计最近10分钟的等级变化次数
        ten_minutes_ago = current_time - 600
        recent_changes = sum(1 for ts in metrics['level_timestamps'] if ts >= ten_minutes_ago)

        alert_key = f"{api_name}_level_changes"

        if recent_changes >= self.alert_thresholds['level_changes_critical']:
            self._trigger_alert(
                alert_key, AlertType.FREQUENT_LEVEL_CHANGES, AlertLevel.CRITICAL,
                api_name, f"等级变化过于频繁: {recent_changes}次/10分钟",
                recent_changes, self.alert_thresholds['level_changes_critical'], current_time
            )
        elif recent_changes >= self.alert_thresholds['level_changes_warning']:
            self._trigger_alert(
                alert_key, AlertType.FREQUENT_LEVEL_CHANGES, AlertLevel.WARNING,
                api_name, f"等级变化频繁: {recent_changes}次/10分钟",
                recent_changes, self.alert_thresholds['level_changes_warning'], current_time
            )
        else:
            self._resolve_alert(alert_key)

    def _check_throughput_drop_alert(self, api_name: str, current_time: float):
        """检查吞吐量下降告警"""
        if api_name not in self.api_metrics:
            return

        metrics = self.api_metrics[api_name]

        # 计算最近1分钟和5分钟的吞吐量
        one_minute_ago = current_time - 60
        five_minutes_ago = current_time - 300

        recent_calls = sum(1 for ts in metrics['timestamps'] if ts >= one_minute_ago)
        baseline_calls = sum(1 for ts in metrics['timestamps'] if five_minutes_ago <= ts < one_minute_ago)

        if baseline_calls >= 10:  # 基线至少10次调用
            recent_throughput = recent_calls / 60.0
            baseline_throughput = baseline_calls / 240.0  # 4分钟平均

            if baseline_throughput > 0:
                throughput_ratio = recent_throughput / baseline_throughput
                alert_key = f"{api_name}_throughput_drop"

                if throughput_ratio <= self.alert_thresholds['throughput_drop_critical']:
                    self._trigger_alert(
                        alert_key, AlertType.THROUGHPUT_DROP, AlertLevel.CRITICAL,
                        api_name, f"吞吐量严重下降: {throughput_ratio:.2f}倍",
                        throughput_ratio, self.alert_thresholds['throughput_drop_critical'], current_time
                    )
                elif throughput_ratio <= self.alert_thresholds['throughput_drop_warning']:
                    self._trigger_alert(
                        alert_key, AlertType.THROUGHPUT_DROP, AlertLevel.WARNING,
                        api_name, f"吞吐量下降: {throughput_ratio:.2f}倍",
                        throughput_ratio, self.alert_thresholds['throughput_drop_warning'], current_time
                    )
                else:
                    self._resolve_alert(alert_key)

    def _trigger_alert(self, alert_key: str, alert_type: AlertType, level: AlertLevel,
                      api_name: str, message: str, value: float, threshold: float,
                      current_time: float):
        """
        触发告警

        参数:
            alert_key: 告警键
            alert_type: 告警类型
            level: 告警级别
            api_name: API名称
            message: 告警消息
            value: 当前值
            threshold: 阈值
            current_time: 当前时间
        """
        # 检查告警抑制（避免重复告警）
        suppression_interval = 300  # 5分钟抑制间隔
        if (alert_key in self.alert_suppression and
            current_time - self.alert_suppression[alert_key] < suppression_interval):
            return

        # 创建告警
        alert = Alert(alert_type, level, api_name, message, value, threshold)

        # 更新活跃告警
        if alert_key in self.active_alerts:
            # 解决旧告警
            self.active_alerts[alert_key].resolve()

        self.active_alerts[alert_key] = alert
        self.alert_history.append(alert)
        self.alert_suppression[alert_key] = current_time

        # 记录日志
        self.logger.warning(f"告警触发: {alert.level.value.upper()} - {alert.message}")

        # 调用告警回调
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                self.logger.error(f"告警回调执行失败: {e}")

    def _resolve_alert(self, alert_key: str):
        """解决告警"""
        if alert_key in self.active_alerts:
            alert = self.active_alerts[alert_key]
            if not alert.resolved:
                alert.resolve()
                self.logger.info(f"告警解决: {alert.message}")
                del self.active_alerts[alert_key]

    def add_alert_callback(self, callback: Callable[[Alert], None]):
        """
        添加告警回调函数

        参数:
            callback: 回调函数，接收Alert对象
        """
        self.alert_callbacks.append(callback)

    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """获取活跃告警列表"""
        return [alert.to_dict() for alert in self.active_alerts.values()]

    def get_alert_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """
        获取告警历史

        参数:
            hours: 历史时间范围（小时）

        返回:
            List[Dict]: 告警历史列表
        """
        cutoff_time = time.time() - (hours * 3600)
        return [alert.to_dict() for alert in self.alert_history
                if alert.timestamp >= cutoff_time]

    def get_alert_summary(self) -> Dict[str, Any]:
        """获取告警摘要"""
        active_count = len(self.active_alerts)

        # 按级别统计活跃告警
        level_counts = defaultdict(int)
        for alert in self.active_alerts.values():
            level_counts[alert.level.value] += 1

        # 统计最近24小时告警
        recent_alerts = self.get_alert_history(24)

        return {
            'active_alerts_count': active_count,
            'active_by_level': dict(level_counts),
            'recent_24h_count': len(recent_alerts),
            'alert_rate': len(recent_alerts) / 24.0,  # 每小时告警数
            'most_frequent_api': self._get_most_frequent_alert_api(recent_alerts),
            'alert_types_distribution': self._get_alert_types_distribution(recent_alerts)
        }

    def _get_most_frequent_alert_api(self, alerts: List[Dict[str, Any]]) -> str:
        """获取告警最频繁的API"""
        api_counts = defaultdict(int)
        for alert in alerts:
            api_counts[alert['api_name']] += 1

        if api_counts:
            return max(api_counts.items(), key=lambda x: x[1])[0]
        return "无"

    def _get_alert_types_distribution(self, alerts: List[Dict[str, Any]]) -> Dict[str, int]:
        """获取告警类型分布"""
        type_counts = defaultdict(int)
        for alert in alerts:
            type_counts[alert['alert_type']] += 1
        return dict(type_counts)
    
    def get_api_summary(self, api_name: str) -> Dict[str, Any]:
        """
        获取API性能摘要
        
        参数:
            api_name: API名称
            
        返回:
            Dict: API性能摘要
        """
        with self._lock:
            if api_name not in self.api_metrics:
                return {}
            
            metrics = self.api_metrics[api_name]
            response_times = list(metrics['response_times'])
            
            if not response_times:
                return {'api_name': api_name, 'no_data': True}
            
            # 计算统计指标
            sorted_times = sorted(response_times)
            n = len(sorted_times)
            
            summary = {
                'api_name': api_name,
                'current_level': metrics['current_level'],
                'total_calls': metrics['success_count'] + metrics['error_count'],
                'success_count': metrics['success_count'],
                'error_count': metrics['error_count'],
                'success_rate': metrics['success_count'] / (metrics['success_count'] + metrics['error_count']) * 100,
                'avg_response_time': sum(response_times) / n,
                'min_response_time': min(response_times),
                'max_response_time': max(response_times),
                'p50_response_time': sorted_times[int(n * 0.50)],
                'p95_response_time': sorted_times[int(n * 0.95)],
                'p99_response_time': sorted_times[int(n * 0.99)],
                'level_changes': len(metrics['level_history']),
                'last_update': datetime.fromtimestamp(metrics['last_update']).strftime('%H:%M:%S'),
                'data_points': n
            }
            
            return summary
    
    def get_global_summary(self) -> Dict[str, Any]:
        """
        获取全局性能摘要
        
        返回:
            Dict: 全局性能摘要
        """
        with self._lock:
            current_time = time.time()
            uptime = current_time - self.global_stats['start_time']
            
            # 计算当前吞吐量（最近1分钟）
            minute_ago = current_time - 60
            recent_calls = 0
            
            for metrics in self.api_metrics.values():
                for timestamp in metrics['timestamps']:
                    if timestamp >= minute_ago:
                        recent_calls += 1
            
            current_throughput = recent_calls / 60.0
            
            summary = {
                'uptime_seconds': uptime,
                'uptime_formatted': str(timedelta(seconds=int(uptime))),
                'total_apis': len(self.api_metrics),
                'total_calls': self.global_stats['total_calls'],
                'total_errors': self.global_stats['total_errors'],
                'overall_success_rate': (
                    (self.global_stats['total_calls'] - self.global_stats['total_errors']) / 
                    max(self.global_stats['total_calls'], 1) * 100
                ),
                'current_throughput': current_throughput,
                'peak_throughput': self.global_stats['peak_throughput'],
                'peak_throughput_time': (
                    datetime.fromtimestamp(self.global_stats['peak_throughput_time']).strftime('%H:%M:%S')
                    if self.global_stats['peak_throughput_time'] else None
                ),
                'active_apis': sum(1 for m in self.api_metrics.values() 
                                 if current_time - m['last_update'] < 300)  # 5分钟内活跃
            }
            
            return summary
    
    def get_performance_trends(self, api_name: str, minutes: int = 10) -> Dict[str, List]:
        """
        获取API性能趋势数据
        
        参数:
            api_name: API名称
            minutes: 时间范围（分钟）
            
        返回:
            Dict: 趋势数据
        """
        with self._lock:
            if api_name not in self.api_metrics:
                return {}
            
            metrics = self.api_metrics[api_name]
            cutoff_time = time.time() - (minutes * 60)
            
            # 提取时间范围内的数据
            trend_data = {
                'timestamps': [],
                'response_times': [],
                'level_changes': []
            }
            
            # 响应时间趋势
            for i, timestamp in enumerate(metrics['timestamps']):
                if timestamp >= cutoff_time:
                    trend_data['timestamps'].append(
                        datetime.fromtimestamp(timestamp).strftime('%H:%M:%S')
                    )
                    trend_data['response_times'].append(metrics['response_times'][i])
            
            # 等级变化趋势
            for change in metrics['level_history']:
                if change['timestamp'] >= cutoff_time:
                    trend_data['level_changes'].append({
                        'time': datetime.fromtimestamp(change['timestamp']).strftime('%H:%M:%S'),
                        'from': change['from'],
                        'to': change['to'],
                        'response_time': change['response_time']
                    })
            
            return trend_data
    
    def generate_dashboard_report(self) -> str:
        """
        生成仪表板文本报告
        
        返回:
            str: 格式化的报告文本
        """
        report_lines = []
        report_lines.append("🚀 限流器性能监控仪表板")
        report_lines.append("=" * 60)

        # {{ AURA-X: Add - 告警信息显示. Source: 最优实践告警系统 }}
        # 告警摘要
        alert_summary = self.get_alert_summary()
        active_alerts = self.get_active_alerts()

        if active_alerts:
            report_lines.append("🚨 活跃告警")
            report_lines.append("-" * 30)
            for alert in active_alerts:
                level_icon = {
                    'info': 'ℹ️', 'warning': '⚠️',
                    'error': '❌', 'critical': '🔥'
                }.get(alert['level'], '❓')

                report_lines.append(f"   {level_icon} {alert['api_name']}: {alert['message']}")
                report_lines.append(f"      时间: {alert['time_str']}, "
                                  f"持续: {alert['duration']:.0f}秒")
            report_lines.append("")
        else:
            report_lines.append("✅ 无活跃告警")
            report_lines.append("")
        
        # 全局摘要
        global_summary = self.get_global_summary()
        report_lines.append(f"📊 全局统计 (运行时间: {global_summary['uptime_formatted']})")
        report_lines.append(f"   总调用数: {global_summary['total_calls']:,}")
        report_lines.append(f"   成功率: {global_summary['overall_success_rate']:.1f}%")
        report_lines.append(f"   当前吞吐量: {global_summary['current_throughput']:.1f} 调用/秒")
        report_lines.append(f"   峰值吞吐量: {global_summary['peak_throughput']:.1f} 调用/秒")
        report_lines.append(f"   活跃API数: {global_summary['active_apis']}")
        report_lines.append("")
        
        # API详细信息
        report_lines.append("📈 API性能详情")
        report_lines.append("-" * 60)
        
        for api_name in sorted(self.api_metrics.keys()):
            summary = self.get_api_summary(api_name)
            if 'no_data' in summary:
                continue
                
            report_lines.append(f"🔧 {api_name}")
            report_lines.append(f"   等级: {summary['current_level']:>10s} | "
                              f"调用: {summary['total_calls']:>6,} | "
                              f"成功率: {summary['success_rate']:>5.1f}%")
            report_lines.append(f"   响应时间: 平均 {summary['avg_response_time']:.3f}s | "
                              f"P95 {summary['p95_response_time']:.3f}s | "
                              f"P99 {summary['p99_response_time']:.3f}s")
            report_lines.append(f"   等级变化: {summary['level_changes']} 次 | "
                              f"最后更新: {summary['last_update']}")
            report_lines.append("")
        
        return "\n".join(report_lines)
    
    def export_metrics_json(self) -> str:
        """
        导出指标数据为JSON格式
        
        返回:
            str: JSON格式的指标数据
        """
        with self._lock:
            export_data = {
                'timestamp': datetime.now().isoformat(),
                'global_summary': self.get_global_summary(),
                'api_summaries': {},
                'performance_trends': {}
            }
            
            for api_name in self.api_metrics.keys():
                export_data['api_summaries'][api_name] = self.get_api_summary(api_name)
                export_data['performance_trends'][api_name] = self.get_performance_trends(api_name, 30)
            
            return json.dumps(export_data, indent=2, ensure_ascii=False)

# 全局仪表板实例
_dashboard_instance = None
_dashboard_lock = threading.Lock()

def get_dashboard() -> RateLimiterDashboard:
    """获取全局仪表板实例（单例模式）"""
    global _dashboard_instance
    
    if _dashboard_instance is None:
        with _dashboard_lock:
            if _dashboard_instance is None:
                _dashboard_instance = RateLimiterDashboard()
    
    return _dashboard_instance

def record_api_call(api_name: str, response_time: float, success: bool, level: str, **kwargs):
    """记录API调用到全局仪表板"""
    dashboard = get_dashboard()
    dashboard.record_api_call(api_name, response_time, success, level, **kwargs)
