"""
系统监控模块 - 按最优实践设计
提供实时系统性能监控、告警和分析功能
"""

import time
import logging
import threading
import psutil
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import deque
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)


@dataclass
class SystemMetrics:
    """系统指标数据类"""
    timestamp: str
    cpu_usage: float
    memory_usage: float
    memory_available_gb: float
    disk_usage: float
    disk_free_gb: float
    network_sent_mb: float
    network_recv_mb: float
    process_count: int
    load_average: List[float]


@dataclass
class DatabaseMetrics:
    """数据库指标数据类"""
    timestamp: str
    status: str
    connections: int
    stock_count: int
    data_size_mb: float
    query_time_ms: float
    health_score: float


class SystemMonitor:
    """
    系统监控器 - 企业级监控解决方案
    
    功能特性:
    - 实时系统资源监控
    - 历史数据存储和分析
    - 智能告警机制
    - 性能趋势分析
    - 自动化报告生成
    """
    
    def __init__(self, history_size: int = 1000, alert_thresholds: Dict[str, float] = None):
        """
        初始化系统监控器
        
        参数:
            history_size: 历史数据保存条数
            alert_thresholds: 告警阈值配置
        """
        self.history_size = history_size
        self.alert_thresholds = alert_thresholds or {
            'cpu_usage': 80.0,
            'memory_usage': 85.0,
            'disk_usage': 90.0,
            'response_time': 1000.0  # ms
        }
        
        # 历史数据存储
        self.system_history = deque(maxlen=history_size)
        self.database_history = deque(maxlen=history_size)
        self.alert_history = deque(maxlen=100)

        # {{ AURA-X: Add - 集成性能监控体系到数据获取流程. Approval: 寸止(ID:性能监控集成). }}
        # 操作记录存储
        self.operation_history = deque(maxlen=1000)
        self.active_operations = {}
        
        # 监控状态
        self.monitoring = False
        self.monitor_thread = None
        self.monitor_interval = 5.0  # 5秒监控间隔
        
        # 网络基准值（用于计算增量）
        self.network_baseline = None
        
        logger.info(f"系统监控器初始化完成，历史数据容量: {history_size}")
    
    def start_monitoring(self):
        """启动监控"""
        if self.monitoring:
            logger.warning("监控已在运行中")
            return
            
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("系统监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=10)
        logger.info("系统监控已停止")
    
    def _monitor_loop(self):
        """监控主循环"""
        while self.monitoring:
            try:
                # 收集系统指标
                system_metrics = self._collect_system_metrics()
                self.system_history.append(system_metrics)
                
                # 检查告警
                self._check_alerts(system_metrics)
                
                # 等待下次监控
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                time.sleep(self.monitor_interval)
    
    def _collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        # CPU使用率
        cpu_usage = psutil.cpu_percent(interval=0.1)
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_usage = memory.percent
        memory_available_gb = memory.available / (1024**3)
        
        # 磁盘信息
        disk = psutil.disk_usage('/')
        disk_usage = disk.percent
        disk_free_gb = disk.free / (1024**3)
        
        # 网络信息
        network = psutil.net_io_counters()
        if self.network_baseline is None:
            self.network_baseline = network
            network_sent_mb = 0
            network_recv_mb = 0
        else:
            network_sent_mb = (network.bytes_sent - self.network_baseline.bytes_sent) / (1024**2)
            network_recv_mb = (network.bytes_recv - self.network_baseline.bytes_recv) / (1024**2)
        
        # 进程数量
        process_count = len(psutil.pids())
        
        # 系统负载
        try:
            load_average = list(psutil.getloadavg())
        except AttributeError:
            # Windows系统不支持getloadavg
            load_average = [0.0, 0.0, 0.0]
        
        return SystemMetrics(
            timestamp=datetime.now().isoformat(),
            cpu_usage=round(cpu_usage, 2),
            memory_usage=round(memory_usage, 2),
            memory_available_gb=round(memory_available_gb, 2),
            disk_usage=round(disk_usage, 2),
            disk_free_gb=round(disk_free_gb, 2),
            network_sent_mb=round(network_sent_mb, 2),
            network_recv_mb=round(network_recv_mb, 2),
            process_count=process_count,
            load_average=[round(x, 2) for x in load_average]
        )
    
    def collect_database_metrics(self, db_manager) -> Optional[DatabaseMetrics]:
        """
        收集数据库指标
        
        参数:
            db_manager: 数据库管理器实例
        """
        try:
            start_time = time.time()
            
            # 连接数据库
            if not db_manager.connect():
                return None
            
            # 获取基本统计
            stock_result = db_manager.execute_query("SELECT COUNT(*) as count FROM stock_basic")
            stock_count = int(stock_result.iloc[0]['count']) if not stock_result.empty else 0
            
            # 获取数据库大小
            size_result = db_manager.execute_query("""
                SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS db_size_mb
                FROM information_schema.tables
                WHERE table_schema = DATABASE()
            """)
            data_size_mb = float(size_result.iloc[0]['db_size_mb']) if not size_result.empty else 0.0
            
            # 获取连接数
            try:
                conn_result = db_manager.execute_query("SHOW STATUS LIKE 'Threads_connected'")
                if not conn_result.empty and len(conn_result.columns) >= 2:
                    connections = int(conn_result.iloc[0].iloc[1])
                else:
                    connections = 0
            except Exception:
                connections = 0
            
            # 计算查询时间
            query_time_ms = (time.time() - start_time) * 1000
            
            # 计算健康分数 (0-100)
            health_score = self._calculate_db_health_score(query_time_ms, connections, stock_count)
            
            db_manager.disconnect()
            
            metrics = DatabaseMetrics(
                timestamp=datetime.now().isoformat(),
                status="healthy" if health_score > 70 else "warning" if health_score > 40 else "critical",
                connections=connections,
                stock_count=stock_count,
                data_size_mb=round(data_size_mb, 2),
                query_time_ms=round(query_time_ms, 2),
                health_score=round(health_score, 2)
            )
            
            self.database_history.append(metrics)
            return metrics
            
        except Exception as e:
            logger.error(f"收集数据库指标失败: {e}")
            return None
    
    def _calculate_db_health_score(self, query_time_ms: float, connections: int, stock_count: int) -> float:
        """计算数据库健康分数"""
        score = 100.0
        
        # 查询时间影响 (0-40分)
        if query_time_ms > 1000:
            score -= 40
        elif query_time_ms > 500:
            score -= 20
        elif query_time_ms > 200:
            score -= 10
        
        # 连接数影响 (0-30分)
        if connections > 150:
            score -= 30
        elif connections > 100:
            score -= 20
        elif connections > 50:
            score -= 10
        
        # 数据完整性影响 (0-30分)
        if stock_count < 5000:
            score -= 30
        elif stock_count < 5200:
            score -= 15
        
        return max(0.0, score)
    
    def _check_alerts(self, metrics: SystemMetrics):
        """检查告警条件"""
        alerts = []
        
        # CPU告警
        if metrics.cpu_usage > self.alert_thresholds['cpu_usage']:
            alerts.append({
                'type': 'cpu_high',
                'level': 'warning',
                'message': f'CPU使用率过高: {metrics.cpu_usage}%',
                'value': metrics.cpu_usage,
                'threshold': self.alert_thresholds['cpu_usage']
            })
        
        # 内存告警
        if metrics.memory_usage > self.alert_thresholds['memory_usage']:
            alerts.append({
                'type': 'memory_high',
                'level': 'warning',
                'message': f'内存使用率过高: {metrics.memory_usage}%',
                'value': metrics.memory_usage,
                'threshold': self.alert_thresholds['memory_usage']
            })
        
        # 磁盘告警
        if metrics.disk_usage > self.alert_thresholds['disk_usage']:
            alerts.append({
                'type': 'disk_high',
                'level': 'critical',
                'message': f'磁盘使用率过高: {metrics.disk_usage}%',
                'value': metrics.disk_usage,
                'threshold': self.alert_thresholds['disk_usage']
            })
        
        # 记录告警
        for alert in alerts:
            alert['timestamp'] = datetime.now().isoformat()
            self.alert_history.append(alert)
            logger.warning(f"系统告警: {alert['message']}")
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前系统状态"""
        if not self.system_history:
            return {"status": "no_data", "message": "暂无监控数据"}
        
        latest_system = self.system_history[-1]
        latest_database = self.database_history[-1] if self.database_history else None
        
        # 计算整体状态
        overall_status = "healthy"
        if latest_system.cpu_usage > 80 or latest_system.memory_usage > 85:
            overall_status = "warning"
        if latest_system.disk_usage > 90:
            overall_status = "critical"
        
        return {
            "overall_status": overall_status,
            "system": asdict(latest_system),
            "database": asdict(latest_database) if latest_database else None,
            "monitoring": self.monitoring,
            "history_count": len(self.system_history),
            "alert_count": len([a for a in self.alert_history if 
                              datetime.fromisoformat(a['timestamp']) > datetime.now() - timedelta(hours=1)])
        }
    
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.system_history:
            return {"status": "no_data"}
        
        # 过滤指定时间范围内的数据
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_data = [m for m in self.system_history 
                      if datetime.fromisoformat(m.timestamp) > cutoff_time]
        
        if not recent_data:
            return {"status": "insufficient_data"}
        
        # 计算统计指标
        cpu_values = [m.cpu_usage for m in recent_data]
        memory_values = [m.memory_usage for m in recent_data]
        
        return {
            "time_range_hours": hours,
            "data_points": len(recent_data),
            "cpu": {
                "avg": round(sum(cpu_values) / len(cpu_values), 2),
                "max": max(cpu_values),
                "min": min(cpu_values)
            },
            "memory": {
                "avg": round(sum(memory_values) / len(memory_values), 2),
                "max": max(memory_values),
                "min": min(memory_values)
            },
            "alerts_count": len([a for a in self.alert_history if
                               datetime.fromisoformat(a['timestamp']) > cutoff_time])
        }

    def record_operation_start(self, operation_name: str, metadata: Dict[str, Any] = None):
        """
        记录操作开始

        参数:
            operation_name: 操作名称
            metadata: 操作元数据
        """
        # {{ AURA-X: Add - 集成性能监控体系到数据获取流程. Approval: 寸止(ID:性能监控集成). }}
        operation_id = f"{operation_name}_{int(time.time() * 1000)}"

        self.active_operations[operation_id] = {
            'name': operation_name,
            'start_time': time.time(),
            'metadata': metadata or {}
        }

        return operation_id

    def record_operation_end(self, operation_id: str, result: Dict[str, Any] = None):
        """
        记录操作结束

        参数:
            operation_id: 操作ID
            result: 操作结果
        """
        if operation_id not in self.active_operations:
            return

        operation = self.active_operations.pop(operation_id)
        end_time = time.time()
        duration = end_time - operation['start_time']

        # 记录到历史
        self.operation_history.append({
            'operation_id': operation_id,
            'name': operation['name'],
            'start_time': operation['start_time'],
            'end_time': end_time,
            'duration': duration,
            'metadata': operation['metadata'],
            'result': result or {}
        })

    def get_operation_stats(self, operation_name: str = None, hours: int = 1) -> Dict[str, Any]:
        """
        获取操作统计

        参数:
            operation_name: 操作名称（可选）
            hours: 统计时间范围（小时）

        返回:
            Dict[str, Any]: 操作统计数据
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)

        # 过滤操作记录
        filtered_ops = [
            op for op in self.operation_history
            if datetime.fromtimestamp(op['start_time']) > cutoff_time
            and (operation_name is None or op['name'] == operation_name)
        ]

        if not filtered_ops:
            return {'total_operations': 0}

        durations = [op['duration'] for op in filtered_ops]

        return {
            'total_operations': len(filtered_ops),
            'avg_duration': round(sum(durations) / len(durations), 3),
            'max_duration': max(durations),
            'min_duration': min(durations),
            'operations_per_hour': round(len(filtered_ops) / hours, 2),
            'active_operations': len(self.active_operations)
        }

    def get_current_metrics(self) -> Optional[Dict[str, Any]]:
        """
        获取当前系统指标

        返回:
            Dict[str, Any]: 当前系统指标，如果获取失败返回None
        """
        # {{ AURA-X: Add - 集成性能监控体系到数据获取流程. Approval: 寸止(ID:性能监控集成). }}
        try:
            metrics = self._collect_system_metrics()
            return asdict(metrics)
        except Exception as e:
            logger.error(f"获取当前系统指标失败: {e}")
            return None

    def get_database_metrics(self) -> Optional[Dict[str, Any]]:
        """
        获取数据库指标

        返回:
            Dict[str, Any]: 数据库指标，如果获取失败返回None
        """
        try:
            # 尝试使用默认存储获取数据库指标
            from src.data.storage.storage_factory import StorageFactory
            storage = StorageFactory.create('sqlite')

            db_metrics = self.collect_database_metrics(storage)
            return asdict(db_metrics) if db_metrics else None

        except Exception as e:
            logger.debug(f"获取数据库指标失败: {e}")
            return None


# 全局监控器实例
system_monitor = SystemMonitor()
