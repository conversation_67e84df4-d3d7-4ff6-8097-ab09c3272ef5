"""
结果命令处理模块
处理结果分析功能
"""

import os
import json
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False
from utils.path_utils import get_output_path

def run_results_command(args):
    """执行结果分析命令"""
    try:
        # 检查结果文件是否存在
        if not os.path.exists(args.file):
            print(f"错误: 结果文件不存在: {args.file}")
            return 1
            
        # 读取结果文件
        with open(args.file, 'r', encoding='utf-8') as f:
            result = json.load(f)
            
        # 显示结果
        print("\n===== 回测结果 =====")
        
        # 显示策略参数
        if 'params' in result:
            print("\n-- 策略参数 --")
            for key, value in result['params'].items():
                print(f"{key}: {value}")
                
        # 显示性能指标
        if 'performance' in result:
            print("\n-- 性能指标 --")
            for key, value in result['performance'].items():
                print(f"{key}: {value}")
        
        # 生成图表
        if args.plot:
            # 如果指定了输出文件，使用它；否则使用默认文件
            plot_file = args.output if args.output else get_output_path('results_plot.png')
            plot_results(result, plot_file)
            print(f"\n图表已保存到: {plot_file}")
        
        return 0
        
    except Exception as e:
        print(f"分析结果时发生错误: {e}")
        return 1
        
def plot_results(result, filename):
    """绘制结果图表"""
    # 创建一个简单的示例图表
    plt.figure(figsize=(12, 8))
    
    # 绘制性能指标
    if 'performance' in result:
        performance = result['performance']
        
        # 绘制条形图
        metrics = []
        values = []
        
        for key, value in performance.items():
            if isinstance(value, (int, float)):
                metrics.append(key)
                values.append(value)
        
        if metrics:
            plt.subplot(211)
            plt.title('性能指标')
            plt.bar(metrics, values)
            plt.xticks(rotation=45)
            plt.tight_layout()
    
    # 如果有资产曲线数据，绘制资产曲线
    if 'portfolio' in result and 'equity_curve' in result['portfolio']:
        equity_curve = result['portfolio']['equity_curve']
        
        plt.subplot(212)
        plt.title('资产曲线')
        plt.plot(equity_curve)
        plt.grid(True)
    elif 'portfolio' in result and 'initial_capital' in result['portfolio'] and 'final_capital' in result['portfolio']:
        # 如果没有完整的资产曲线，仅绘制初始和最终资金
        initial = result['portfolio']['initial_capital']
        final = result['portfolio']['final_capital']
        
        plt.subplot(212)
        plt.title('资金变化')
        plt.bar(['初始资金', '最终资金'], [initial, final])
    
    plt.tight_layout()
    plt.savefig(filename)
    plt.close() 