"""
数据命令处理模块
处理数据获取、转换和保存功能
"""

import os
import sys
import pandas as pd
import datetime
import concurrent.futures
from src.utils.temporary.path_utils import get_output_path, ensure_directory_exists
from src.utils.ui_utils import get_default_periods
from src.data.sources.data_source_factory import DataSourceFactory
from src.utils.config.config_factory import config_factory
from typing import Dict, Any, List, Optional, Union

def run_data_command(args):
    """运行数据命令"""
    # 如果指定了输出文件，处理路径
    if args.output:
        args.output = get_output_path(args.output)
    
    # 获取默认时间周期
    default_periods = get_default_periods()
    
    # 如果没有指定开始或结束日期，使用默认值
    if not args.start:
        args.start = default_periods.get('start_date')
        print(f"未指定开始日期，使用默认值: {args.start}")
        
    if not args.end:
        args.end = default_periods.get('end_date')
        print(f"未指定结束日期，使用默认值: {args.end}")
        
    try:
        # 使用工厂方法创建Tushare数据源适配器
        # 从配置文件获取token
        tushare_config = config_factory.get_tushare_config()
        api_config = tushare_config.get('api', {})
        token = api_config.get('token', '')
        
        if not token:
            print("警告: 未找到Tushare API Token，将使用配置文件中的默认值")
        
        # 使用数据源工厂创建Tushare数据源适配器
        data_source = DataSourceFactory.create('tushare', {'token': token})
        
        # 根据类型获取不同的数据
        if args.type == 'calendar':
            print(f"正在获取交易日历数据...")
            data = data_source.get_trade_calendar(
                exchange='SSE',
                start_date=args.start, 
                end_date=args.end
            )
            print(f"成功获取了 {len(data)} 条交易日历记录")
        elif args.type == 'stock':
            print(f"正在获取股票数据: {args.code}...")
            data = data_source.get_daily_bars(
                ts_code=args.code,
                start_date=args.start,
                end_date=args.end
            )
            print(f"成功获取了 {len(data)} 条股票数据记录")
        elif args.type == 'index':
            print(f"正在获取指数数据: {args.code}...")
            data = data_source.get_index_data(
                ts_code=args.code,
                start_date=args.start,
                end_date=args.end
            )
            print(f"成功获取了 {len(data)} 条指数数据记录")
        elif args.type == 'fundamental':
            print(f"获取基本面数据功能尚未实现")
            return 1
        else:
            print(f"暂不支持的数据类型: {args.type}")
            return 1
        
        # 保存数据
        if data is not None and args.output:
            # 确保输出目录存在
            output_dir = os.path.dirname(args.output)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                
            if args.type == 'calendar':
                # 交易日历格式化处理
                data = data[data['is_open'] == 1][['cal_date']]
                data.columns = ['trade_date']
                
            # 保存为CSV文件
            data.to_csv(args.output, index=False)
            print(f"数据已保存到: {args.output}")
        
        return 0
    except Exception as e:
        print(f"获取数据时发生错误: {e}")
        return 1

def fetch_trading_calendar(data_source, start_date, end_date):
    """获取交易日历数据"""
    try:
        # 尝试从数据源获取交易日历
        print(f"正在获取交易日历数据 ({start_date} 至 {end_date})...")
        trading_calendar = data_source.get_trade_calendar(
            exchange='SSE',
            start_date=start_date,
            end_date=end_date
        )
        
        # 过滤出交易日
        if 'is_open' in trading_calendar.columns:
            trading_calendar = trading_calendar[trading_calendar['is_open'] == 1]
        
        # 提取日期列
        calendar_dates = None
        if 'cal_date' in trading_calendar.columns:
            calendar_dates = trading_calendar['cal_date'].tolist()
        elif 'trade_date' in trading_calendar.columns:
            calendar_dates = trading_calendar['trade_date'].tolist()
        
        if calendar_dates:
            print(f"成功获取了 {len(calendar_dates)} 个交易日")
            return calendar_dates
        else:
            print("警告: 获取的交易日历数据格式异常")
            return []
            
    except Exception as e:
        print(f"获取交易日历时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return []

def get_stock_list(data_source, stocks_param):
    """解析并获取股票列表"""
    try:
        stock_list = []
        
        # 如果直接提供了股票代码列表
        if stocks_param and "," in stocks_param:
            stock_list = [code.strip() for code in stocks_param.split(",")]
            print(f"使用指定的 {len(stock_list)} 支股票")
            return stock_list
            
        # 如果提供了单个股票代码
        elif stocks_param:
            stock_list = [stocks_param.strip()]
            print(f"使用单支股票: {stock_list[0]}")
            return stock_list
        
        # 默认使用创业板指数成分股（作为示例）
        print("未指定股票，将尝试获取创业板指数成分股...")
        # 这里简化处理，实际应用中应从数据源获取
        default_stocks = ["300750.SZ", "300760.SZ", "300770.SZ", "300780.SZ", "300790.SZ"]
        return default_stocks
        
    except Exception as e:
        print(f"解析股票列表时发生错误: {e}")
        # 返回一些默认股票作为后备
        return ["000001.SZ", "600000.SH"]

def fetch_stock_data(data_source, stock_list, start_date, end_date):
    """获取股票数据，根据时间周期智能选择获取方式"""
    if not stock_list:
        print("错误: 未提供股票列表")
        return pd.DataFrame()
        
    try:
        print(f"正在获取 {len(stock_list)} 支股票的数据...")
        
        # 获取数据获取策略配置
        tushare_config = config_factory.get_tushare_config()
        fetch_strategy = tushare_config.get('fetch_strategy', {})
        
        # 获取阈值配置
        period_threshold_days = fetch_strategy.get('period_threshold_days', 30)  # 默认30天
        batch_size_by_date = fetch_strategy.get('batch_size_by_date', 30)  # 减小按日期批处理大小
        batch_size_by_stock = fetch_strategy.get('batch_size_by_stock', 10)  # 减小按股票批处理大小
        max_workers = fetch_strategy.get('max_workers', 3)  # 减少并发数
        
        # 计算请求的周期长度
        try:
            start_dt = datetime.datetime.strptime(start_date, '%Y%m%d') if isinstance(start_date, str) else start_date
            end_dt = datetime.datetime.strptime(end_date, '%Y%m%d') if isinstance(end_date, str) else end_date
            period_days = (end_dt - start_dt).days
        except (ValueError, TypeError):
            # 如果日期解析失败，默认使用按股票批量获取方式
            print("警告: 无法解析日期格式，将使用默认数据获取方式")
            period_days = 0
        
        print(f"数据请求周期: {period_days}天，阈值: {period_threshold_days}天")
        
        # 统一采用按股票批量获取方式，无论周期长短
        print(f"统一采用按股票批量获取方式，股票数: {len(stock_list)}，周期: {start_date} ~ {end_date}")
        return fetch_stock_data_by_stock(data_source, stock_list, start_date, end_date, 
                                        batch_size=batch_size_by_stock, max_workers=max_workers)
            
    except Exception as e:
        print(f"获取股票数据时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def fetch_stock_data_by_date(data_source, stock_list, start_date, end_date, batch_size=30, max_workers=3):
    """按日期批量获取股票数据 (适合短周期)
    
    对于每个交易日，批量获取多只股票的数据
    """
    try:
        # 获取交易日列表
        trade_dates = fetch_trading_dates(data_source, start_date, end_date)
        if not trade_dates:
            print("错误: 未能获取交易日列表")
            return pd.DataFrame()
        
        print(f"共有 {len(trade_dates)} 个交易日需要处理")
        
        # 按批次处理股票
        stock_batches = [stock_list[i:i+batch_size] for i in range(0, len(stock_list), batch_size)]
        
        all_data = []
        
        # 对每个交易日获取所有股票数据
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            for trade_date in trade_dates:
                print(f"处理交易日: {trade_date}")
                
                # 为每批股票创建任务
                future_to_batch = {
                    executor.submit(fetch_stocks_for_date, data_source, batch, trade_date): batch
                    for batch in stock_batches
                }
                
                for future in concurrent.futures.as_completed(future_to_batch):
                    batch = future_to_batch[future]
                    try:
                        batch_data = future.result()
                        if not batch_data.empty:
                            all_data.append(batch_data)
                    except Exception as e:
                        print(f"获取批次数据时发生错误: {e}")
        
        # 合并所有结果
        if all_data:
            combined_data = pd.concat(all_data, ignore_index=True)
            print(f"成功获取共 {len(combined_data)} 条股票数据记录")
            return combined_data
        else:
            print("警告: 未获取到任何股票数据")
            return pd.DataFrame()
    
    except Exception as e:
        print(f"按日期批量获取股票数据时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def fetch_stock_data_by_stock(data_source, stock_list, start_date, end_date, batch_size=10, max_workers=3):
    """按股票批量获取数据 (适合长周期)
    
    对于每只股票，获取其所有交易日的数据
    """
    try:
        # 用于存储所有股票数据的列表
        all_stock_data = []
        total = len(stock_list)
        # 并行获取每只股票的数据
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_stock = {
                executor.submit(fetch_single_stock_data, data_source, stock_code, start_date, end_date): stock_code
                for stock_code in stock_list
            }
            finished = 0
            for future in concurrent.futures.as_completed(future_to_stock):
                stock_code = future_to_stock[future]
                finished += 1
                try:
                    single_stock_data = future.result()
                    if not single_stock_data.empty:
                        all_stock_data.append(single_stock_data)
                        print(f"[{finished}/{total}] 成功获取 {stock_code} 的 {len(single_stock_data)} 条记录")
                    else:
                        print(f"[{finished}/{total}] 警告: 未能获取到 {stock_code} 的数据")
                except Exception as e:
                    print(f"[{finished}/{total}] 获取 {stock_code} 数据时发生错误: {e}")
        # 合并所有股票的数据
        if all_stock_data:
            combined_data = pd.concat(all_stock_data, ignore_index=True)
            print(f"成功获取共 {len(combined_data)} 条股票数据记录")
            return combined_data
        else:
            print("错误: 未获取到任何股票数据")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"按股票批量获取数据时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def fetch_single_stock_data(data_source, stock_code, start_date, end_date):
    """获取单只股票的历史数据"""
    try:
        print(f"获取 {stock_code} 的日线数据...")
        
        # 获取单个股票的日线数据
        single_stock_data = data_source.get_daily_bars(
            ts_code=stock_code,
            start_date=start_date,
            end_date=end_date
        )
        
        return single_stock_data
    except Exception as e:
        print(f"获取单只股票 {stock_code} 数据时发生错误: {e}")
        return pd.DataFrame()

def fetch_stocks_for_date(data_source, stock_batch, trade_date):
    """获取特定交易日的批量股票数据"""
    try:
        # 将日期格式化为字符串
        date_str = trade_date
        if isinstance(trade_date, datetime.datetime):
            date_str = trade_date.strftime('%Y%m%d')
        
        print(f"获取 {len(stock_batch)} 只股票在 {date_str} 的数据")
        
        all_data = []
        for stock_code in stock_batch:
            try:
                # 获取单个股票特定日期的数据
                df = data_source.get_daily_bars(
                    ts_code=stock_code,
                    start_date=date_str,
                    end_date=date_str
                )
                
                if not df.empty:
                    all_data.append(df)
            except Exception as e:
                print(f"获取 {stock_code} 在 {date_str} 的数据时发生错误: {e}")
        
        # 合并结果
        if all_data:
            return pd.concat(all_data, ignore_index=True)
        else:
            return pd.DataFrame()
    except Exception as e:
        print(f"批量获取 {trade_date} 的股票数据时发生错误: {e}")
        return pd.DataFrame()

def fetch_trading_dates(data_source, start_date, end_date):
    """获取交易日期列表"""
    try:
        # 尝试从数据源获取交易日历
        print(f"正在获取交易日历数据 ({start_date} 至 {end_date})...")
        trading_calendar = data_source.get_trade_calendar(
            exchange='SSE',
            start_date=start_date,
            end_date=end_date
        )
        
        # 过滤出交易日
        if 'is_open' in trading_calendar.columns:
            trading_calendar = trading_calendar[trading_calendar['is_open'] == 1]
        
        # 提取日期列
        calendar_dates = None
        if 'cal_date' in trading_calendar.columns:
            calendar_dates = trading_calendar['cal_date'].tolist()
        elif 'trade_date' in trading_calendar.columns:
            calendar_dates = trading_calendar['trade_date'].tolist()
        
        if calendar_dates:
            print(f"成功获取了 {len(calendar_dates)} 个交易日")
            return calendar_dates
        else:
            print("警告: 获取的交易日历数据格式异常")
            return []
            
    except Exception as e:
        print(f"获取交易日期列表时发生错误: {e}")
        return []

def fetch_fundamental_data(data_source, stock_list, start_date, end_date):
    """获取基本面数据，根据时间周期智能选择获取方式"""
    if not stock_list:
        print("错误: 未提供股票列表")
        return pd.DataFrame()
        
    try:
        print(f"正在获取 {len(stock_list)} 支股票的基本面数据...")
        
        # 获取数据获取策略配置
        tushare_config = config_factory.get_tushare_config()
        fetch_strategy = tushare_config.get('fetch_strategy', {})
        
        # 获取阈值配置
        period_threshold_days = fetch_strategy.get('period_threshold_days', 30)  # 默认30天
        batch_size_by_date = fetch_strategy.get('batch_size_by_date', 30)  # 基本面数据批量小一些
        batch_size_by_stock = fetch_strategy.get('batch_size_by_stock', 10)  # 基本面数据批量小一些
        max_workers = fetch_strategy.get('max_workers', 3)  # 基本面数据并行度小一些
        
        # 计算请求的周期长度
        try:
            start_dt = datetime.datetime.strptime(start_date, '%Y%m%d') if isinstance(start_date, str) else start_date
            end_dt = datetime.datetime.strptime(end_date, '%Y%m%d') if isinstance(end_date, str) else end_date
            period_days = (end_dt - start_dt).days
        except (ValueError, TypeError):
            # 如果日期解析失败，默认使用按股票批量获取方式
            print("警告: 无法解析日期格式，将使用默认数据获取方式")
            period_days = 0
        
        print(f"基本面数据请求周期: {period_days}天，阈值: {period_threshold_days}天")
        
        # 统一采用按股票批量获取方式，无论周期长短
        print(f"统一采用按股票批量获取基本面数据，股票数: {len(stock_list)}，周期: {start_date} ~ {end_date}")
        fundamental_data = fetch_fundamental_data_by_stock(data_source, stock_list, start_date, end_date, 
                                                         batch_size=batch_size_by_stock, max_workers=max_workers)
        # 如果无法获取基本面数据，退出系统
        if fundamental_data.empty:
            print("错误: 未获取到任何基本面数据，程序将停止")
            sys.exit(1)  # 直接退出程序，返回错误码1
        return fundamental_data
            
    except Exception as e:
        print(f"获取基本面数据时发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)  # 直接退出程序，返回错误码1

def fetch_fundamental_data_by_stock(data_source, stock_list, start_date, end_date, batch_size=10, max_workers=3):
    """按股票批量获取基本面数据 (适合长周期)"""
    try:
        # 用于存储所有基本面数据的列表
        all_fundamental_data = []
        # 要获取的财务报表类型
        report_types = ['balancesheet', 'income', 'cashflow']
        total = len(stock_list) * len(report_types)
        finished = 0
        # 并行获取每只股票的基本面数据
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            for stock_code in stock_list:
                for report_type in report_types:
                    futures.append(
                        (executor.submit(
                            fetch_single_stock_fundamental, 
                            data_source, 
                            stock_code, 
                            report_type, 
                            start_date, 
                            end_date
                        ), stock_code, report_type)
                    )
            for future, stock_code, report_type in futures:
                try:
                    single_fund_data = future.result()
                    finished += 1
                    if not single_fund_data.empty:
                        all_fundamental_data.append(single_fund_data)
                        print(f"[{finished}/{total}] 成功获取 {stock_code} 的 {report_type} 报表，{len(single_fund_data)}行")
                    else:
                        print(f"[{finished}/{total}] 警告: 未能获取到 {stock_code} 的 {report_type} 报表")
                except Exception as e:
                    print(f"[{finished}/{total}] 获取 {stock_code} 的 {report_type} 报表时发生错误: {e}")
        # 合并所有股票的基本面数据
        if all_fundamental_data:
            combined_data = pd.concat(all_fundamental_data, ignore_index=True)
            print(f"成功获取共 {len(combined_data)} 条基本面数据记录")
            return combined_data
        else:
            print("错误: 未获取到任何基本面数据")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"按股票批量获取基本面数据时发生错误: {e}")
        return pd.DataFrame()

def fetch_single_stock_fundamental(data_source, stock_code, report_type, start_date, end_date):
    """获取单只股票的特定类型基本面数据"""
    try:
        print(f"获取 {stock_code} 的 {report_type} 报表...")
        
        # 获取特定报表数据
        df = data_source.get_financial_data(
            api_name=report_type,
            ts_code=stock_code,
            start_date=start_date,
            end_date=end_date
        )
        
        return df
    except Exception as e:
        print(f"获取单只股票基本面数据时发生错误: {e}")
        return pd.DataFrame()

def fetch_fundamental_batch(data_source, stock_batch, report_type, period):
    """获取特定财务报告期的批量股票基本面数据"""
    try:
        all_data = []
        for stock_code in stock_batch:
            try:
                # 获取单个股票的特定报表数据
                df = data_source.get_financial_data(
                    api_name=report_type,
                    ts_code=stock_code,
                    period=period
                )
                
                if not df.empty:
                    all_data.append(df)
            except Exception as e:
                print(f"获取 {stock_code} 的 {report_type} 报表时发生错误: {e}")
        
        # 合并结果
        if all_data:
            return pd.concat(all_data, ignore_index=True)
        else:
            return pd.DataFrame()
    except Exception as e:
        print(f"批量获取基本面数据时发生错误: {e}")
        return pd.DataFrame()

def get_report_periods(start_date, end_date):
    """获取指定时间范围内的财务报告期"""
    try:
        # 将日期转换为datetime对象
        if isinstance(start_date, str):
            start_dt = datetime.datetime.strptime(start_date, '%Y%m%d')
        else:
            start_dt = start_date
            
        if isinstance(end_date, str):
            end_dt = datetime.datetime.strptime(end_date, '%Y%m%d')
        else:
            end_dt = end_date
        
        # 获取起始年和结束年
        start_year = start_dt.year
        end_year = end_dt.year
        
        # 季度报告期
        periods = []
        
        for year in range(start_year, end_year + 1):
            for quarter in [1, 2, 3, 4]:
                # 生成报告期字符串，格式为"YYYYQX"
                period_str = f"{year}Q{quarter}"
                
                # 计算季度末日期
                if quarter == 1:
                    quarter_end = datetime.datetime(year, 3, 31)
                elif quarter == 2:
                    quarter_end = datetime.datetime(year, 6, 30)
                elif quarter == 3:
                    quarter_end = datetime.datetime(year, 9, 30)
                else:  # quarter == 4
                    quarter_end = datetime.datetime(year, 12, 31)
                
                # 如果季度末在日期范围内，添加该报告期
                if start_dt <= quarter_end <= end_dt:
                    periods.append(period_str)
        
        return periods
    except Exception as e:
        print(f"计算财务报告期时发生错误: {e}")
        # 返回一个默认的报告期（上一年年报）
        current_year = datetime.datetime.now().year
        return [f"{current_year-1}Q4"]

def process_data(stock_data, fundamental_data):
    """处理股票数据和基本面数据，合并这些数据"""
    # 由于此函数很长，这里只保留关键部分
    print("合并股票日线数据和基本面数据...")
    
    try:
        # 确保两个数据框都有必要的列
        for col in ['ts_code', 'trade_date']:
            if col not in stock_data.columns:
                raise ValueError(f"股票数据缺少必要的列: {col}")
            if fundamental_data is not None and not fundamental_data.empty and col not in fundamental_data.columns:
                raise ValueError(f"基本面数据缺少必要的列: {col}")
        
        # 如果没有基本面数据，直接返回股票数据
        if fundamental_data is None or fundamental_data.empty:
            print("没有基本面数据，直接返回股票数据")
            return stock_data
            
        # 合并数据
        merged_data = pd.merge(
            stock_data, 
            fundamental_data, 
            on=['ts_code', 'trade_date'], 
            how='left'
        )
        
        print(f"合并后数据包含 {merged_data.shape[0]} 行，{merged_data.shape[1]} 列")
        return merged_data
        
    except Exception as e:
        print(f"处理数据时发生错误: {e}")
        return stock_data  # 出错时返回原始股票数据 

class DataCommandHandler:
    def handle_action(self, action: str, args: Dict[str, Any]) -> None:
        """
        处理具体数据命令
        
        参数:
            action: 数据操作类型
            args: 参数字典
        """
        try:
            # 创建数据源
            data_source = self._create_data_source()
            
            # 根据操作类型执行相应操作
            if action == 'calendar':
                # 获取交易日历
                start_date = args.get('start_date')
                end_date = args.get('end_date')
                
                data = data_source.get_trade_calendar(
                    start_date=start_date,
                    end_date=end_date
                )
                self._display_result(data, "交易日历数据")
                
            elif action == 'stock':
                # 获取股票数据
                ts_code = args.get('code')
                start_date = args.get('start_date')
                end_date = args.get('end_date')
                
                data = data_source.get_daily_bars(
                    ts_code=ts_code,
                    start_date=start_date,
                    end_date=end_date
                )
                self._display_result(data, f"股票 {ts_code} 行情数据")
                
            elif action == 'index':
                # 获取指数数据
                ts_code = args.get('code')
                start_date = args.get('start_date')
                end_date = args.get('end_date')
                
                data = data_source.get_index_data(
                    ts_code=ts_code,
                    start_date=start_date,
                    end_date=end_date
                )
                self._display_result(data, f"指数 {ts_code} 行情数据")
                
            elif action == 'fundamental':
                print(f"获取基本面数据功能尚未实现")
                return
            else:
                print(f"暂不支持的数据类型: {action}")
                return
            
            # 保存数据
            if data is not None and args.get('output_file'):
                # 确保输出目录存在
                output_dir = os.path.dirname(args['output_file'])
                if output_dir and not os.path.exists(output_dir):
                    os.makedirs(output_dir)
                
                if action == 'calendar':
                    # 交易日历格式化处理
                    data = data[data['is_open'] == 1][['cal_date']]
                    data.columns = ['trade_date']
                
                # 保存为CSV文件
                data.to_csv(args['output_file'], index=False)
                print(f"数据已保存到: {args['output_file']}")
            
        except Exception as e:
            print(f"处理数据命令时发生错误: {e}")

    def execute_data_export(self, args: Dict[str, Any]) -> None:
        """
        执行数据导出命令
        
        参数:
            args: 参数字典，包含股票代码、开始日期、结束日期和输出文件路径
        """
        try:
            # 获取参数
            stock_list = args.get('stock_list', [])
            start_date = args.get('start_date')
            end_date = args.get('end_date')
            output_file = args.get('output_file')
            
            # 创建数据源
            data_source = self._create_data_source()
            
            # 获取交易日历
            trading_calendar = data_source.get_trade_calendar(
                start_date=start_date,
                end_date=end_date
            )
            
            # 获取单只股票数据
            for stock_code in stock_list:
                single_stock_data = data_source.get_daily_bars(
                    ts_code=stock_code,
                    start_date=start_date,
                    end_date=end_date
                )
                
                # 保存单只股票数据
                if single_stock_data is not None and not single_stock_data.empty:
                    single_stock_data.to_csv(f"{output_file}_{stock_code}.csv", index=False)
                    print(f"股票 {stock_code} 的数据已保存到: {output_file}_{stock_code}.csv")
                else:
                    print(f"警告: 未能获取到股票 {stock_code} 的数据")
            
            # 保存交易日历数据
            if trading_calendar is not None and len(trading_calendar) > 0:
                trading_calendar_df = pd.DataFrame(trading_calendar, columns=['trade_date'])
                trading_calendar_df.to_csv(f"{output_file}_trading_calendar.csv", index=False)
                print(f"交易日历数据已保存到: {output_file}_trading_calendar.csv")
            
        except Exception as e:
            print(f"执行数据导出命令时发生错误: {e}") 