"""
命令工厂模块
负责创建和分发命令处理函数
"""

import importlib

class CommandFactory:
    """命令工厂类，根据命令名称返回对应的命令处理函数"""
    
    @staticmethod
    def get_command_handler(command_name):
        """获取命令处理函数
        
        参数:
            command_name: 命令名称
            
        返回:
            函数: 对应的命令处理函数
        """
        # 命令与模块的映射
        command_map = {
            'data': ('commands.data_command', 'run_data_command'),
            'autobacktest': ('commands.autobacktest_command', 'run_autobacktest_command'),
            'backtest': ('commands.backtest_command', 'run_backtest_command'),
            'strategy': ('commands.strategy_command', 'run_strategy_command'),
            'trade': ('commands.trade_command', 'run_trade_command'),
            'risk': ('commands.risk_command', 'run_risk_command'),
            'results': ('commands.results_command', 'run_results_command'),
            'env': ('commands.env_command', 'check_environment'),
        }
        
        if command_name not in command_map:
            # 未知命令，返回None
            return None
            
        # 获取模块和函数名
        module_name, function_name = command_map[command_name]
        
        try:
            # 动态导入模块
            module = importlib.import_module(module_name)
            
            # 获取函数对象
            function = getattr(module, function_name)
            
            return function
        except (ImportError, AttributeError) as e:
            print(f"警告: 无法加载命令处理函数 {command_name}: {e}")
            return None 