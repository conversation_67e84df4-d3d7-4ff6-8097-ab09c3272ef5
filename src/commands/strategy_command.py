"""
策略命令处理模块
处理策略开发、测试和修改功能
"""

import os
import json
from src.utils.temporary.path_utils import get_output_path
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False
from src.strategies.base_strategy import BaseStrategy

def run_strategy_command(args):
    """执行策略命令"""
    try:
        action = args.action
        strategy_name = args.name
        
        print(f"执行策略操作: {action}, 策略名称: {strategy_name}")
        
        if action == 'create':
            return create_strategy(args)
        elif action == 'test':
            return test_strategy(args)
        elif action == 'edit':
            return edit_strategy(args)
        else:
            print(f"错误: 不支持的操作 {action}")
            return 1
            
    except Exception as e:
        print(f"策略操作过程中发生错误: {e}")
        return 1

def create_strategy(args):
    """创建新策略"""
    print(f"创建新策略: {args.name}")
    
    # 使用指定的模板
    template = args.template or 'moving_average'
    
    # 检查策略文件是否已存在
    strategy_file = f"strategies/{args.name}_strategy.py"
    if os.path.exists(strategy_file):
        print(f"错误: 策略文件已存在: {strategy_file}")
        return 1
    
    # 根据模板创建策略文件
    if template == 'moving_average':
        strategy_content = get_moving_average_template(args.name)
    elif template == 'factor':
        strategy_content = get_factor_template(args.name)
    elif template == 'momentum':
        strategy_content = get_momentum_template(args.name)
    else:
        print(f"错误: 不支持的策略模板 {template}")
        return 1
    
    # 确保strategies目录存在
    os.makedirs("strategies", exist_ok=True)
    
    # 写入策略文件
    with open(strategy_file, 'w', encoding='utf-8') as f:
        f.write(strategy_content)
    
    print(f"策略文件已创建: {strategy_file}")
    return 0

def test_strategy(args):
    """测试策略"""
    print(f"测试策略: {args.name}")
    
    # 这里可以添加对策略的测试代码
    # 实际测试通常需要调用回测功能
    print("策略测试功能尚未实现")
    return 0

def edit_strategy(args):
    """编辑策略"""
    print(f"编辑策略: {args.name}")
    
    # 检查策略文件是否存在
    strategy_file = f"strategies/{args.name}_strategy.py"
    if not os.path.exists(strategy_file):
        print(f"错误: 策略文件不存在: {strategy_file}")
        return 1
    
    # 这里可以添加打开策略文件的代码
    print(f"请使用文本编辑器打开文件: {strategy_file}")
    return 0

def get_moving_average_template(strategy_name):
    """获取移动平均线策略模板"""
    return f'''"""
{strategy_name.capitalize()} 策略
基于移动平均线的交易策略
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from strategies.base_strategy import BaseStrategy

class {strategy_name.capitalize()}Strategy(BaseStrategy):
    """基于移动平均线的交易策略"""
    
    def __init__(self, short_window=5, long_window=20):
        """初始化策略参数"""
        self.short_window = short_window
        self.long_window = long_window
        self.name = "{strategy_name}"
    
    def generate_signals(self, data):
        """生成交易信号
        
        当短期均线上穿长期均线时，产生买入信号；
        当短期均线下穿长期均线时，产生卖出信号。
        
        参数:
            data: 包含价格数据的DataFrame
            
        返回:
            包含信号的DataFrame
        """
        # 复制数据
        signals = data.copy()
        
        # 计算移动平均线
        signals['short_ma'] = signals['close'].rolling(window=self.short_window).mean()
        signals['long_ma'] = signals['close'].rolling(window=self.long_window).mean()
        
        # 初始化信号列
        signals['signal'] = 0
        
        # 生成交易信号
        signals['signal'] = np.where(
            (signals['short_ma'] > signals['long_ma']) & 
            (signals['short_ma'].shift(1) <= signals['long_ma'].shift(1)),
            1,  # 买入信号
            signals['signal']
        )
        signals['signal'] = np.where(
            (signals['short_ma'] < signals['long_ma']) & 
            (signals['short_ma'].shift(1) >= signals['long_ma'].shift(1)),
            -1,  # 卖出信号
            signals['signal']
        )
        
        return signals
    
    def backtest(self, data, start_date=None, end_date=None, initial_capital=1000000, benchmark=None):
        """执行回测
        
        参数:
            data: 包含价格数据的DataFrame
            start_date: 回测开始日期
            end_date: 回测结束日期
            initial_capital: 初始资金
            benchmark: 基准指数代码
            
        返回:
            回测结果字典
        """
        print(f"执行移动平均线策略回测，短期窗口:{self.short_window}，长期窗口:{self.long_window}")
        
        # 过滤数据
        if start_date:
            data = data[data['trade_date'] >= start_date]
        if end_date:
            data = data[data['trade_date'] <= end_date]
            
        # 确保数据按日期排序
        data = data.sort_values('trade_date')
        
        # 为每只股票生成信号
        signals_by_stock = {}
        for stock_code in data['ts_code'].unique():
            stock_data = data[data['ts_code'] == stock_code].copy()
            
            # 生成信号
            stock_signals = self.generate_signals(stock_data)
            signals_by_stock[stock_code] = stock_signals
        
        # 模拟交易
        portfolio = self.simulate_trades(signals_by_stock, initial_capital)
        
        # 计算绩效指标
        performance = self.calculate_performance(portfolio)
        
        # 构造结果
        result = {
            'signals': signals_by_stock,
            'portfolio': portfolio,
            'performance': performance,
            'params': {
                'short_window': self.short_window,
                'long_window': self.long_window
            }
        }
        
        return result
    
    def simulate_trades(self, signals_by_stock, initial_capital):
        """模拟交易"""
        # 在实际应用中，应实现完整的交易模拟逻辑
        # 这里仅返回一个简单的示例
        return {
            'initial_capital': initial_capital,
            'final_capital': initial_capital * 1.1,  # 假设收益10%
            'total_return': 0.1,
            'annual_return': 0.1 * 252 / 30,  # 假设回测了30个交易日
            'max_drawdown': 0.05
        }
    
    def calculate_performance(self, portfolio):
        """计算绩效指标"""
        return {
            'total_return': portfolio['total_return'],
            'annual_return': portfolio['annual_return'],
            'max_drawdown': portfolio['max_drawdown'],
            'sharpe_ratio': 1.5,  # 示例值
            'win_rate': 0.6,  # 示例值
        }
    
    def save_result(self, result, filename):
        """保存回测结果"""
        import json
        
        # 转换为可序列化的格式
        serializable_result = {
            'performance': result['performance'],
            'params': result['params']
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(serializable_result, f, indent=4)
    
    def plot_result(self, result, filename):
        """绘制回测结果图表"""
        # 在实际应用中，应实现完整的绘图逻辑
        # 这里仅创建一个简单的示例图表
        plt.figure(figsize=(12, 6))
        
        plt.subplot(211)
        plt.title('回测结果')
        plt.ylabel('资金')
        plt.plot([1, 2, 3, 4, 5], [
            result['portfolio']['initial_capital'],
            result['portfolio']['initial_capital'] * 1.02,
            result['portfolio']['initial_capital'] * 1.05,
            result['portfolio']['initial_capital'] * 1.07,
            result['portfolio']['final_capital']
        ])
        
        plt.subplot(212)
        plt.ylabel('收益率')
        plt.xlabel('时间')
        plt.plot([1, 2, 3, 4, 5], [0, 0.02, 0.05, 0.07, 0.1])
        
        plt.tight_layout()
        plt.savefig(filename)
        plt.close()
'''

def get_factor_template(strategy_name):
    """获取因子策略模板"""
    # 简化实现，返回基本框架
    return f'''"""
{strategy_name.capitalize()} 策略
基于多因子的选股策略
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from strategies.base_strategy import BaseStrategy

class {strategy_name.capitalize()}Strategy(BaseStrategy):
    """基于多因子的选股策略"""
    
    def __init__(self, factors=None, weights=None):
        """初始化策略参数"""
        self.factors = factors or ['pe_ratio', 'pb_ratio']
        self.weights = weights or [0.5, 0.5]
        self.name = "{strategy_name}"
    
    # 实现策略具体逻辑...
'''

def get_momentum_template(strategy_name):
    """获取动量策略模板"""
    # 简化实现，返回基本框架
    return f'''"""
{strategy_name.capitalize()} 策略
基于价格动量的交易策略
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from strategies.base_strategy import BaseStrategy

class {strategy_name.capitalize()}Strategy(BaseStrategy):
    """基于价格动量的交易策略"""
    
    def __init__(self, lookback_period=60, holding_period=20):
        """初始化策略参数"""
        self.lookback_period = lookback_period
        self.holding_period = holding_period
        self.name = "{strategy_name}"
    
    # 实现策略具体逻辑...
''' 