"""
环境检查命令处理模块
提供系统环境检查功能
"""

import os
import sys
import platform
import pandas as pd
import numpy as np
import logging
import subprocess
from src.utils.temporary.path_utils import get_output_path

def check_environment(output_file=None):
    """检查系统环境"""
    
    if output_file:
        output_file = get_output_path(output_file, 'doc')
    
    # 准备环境信息
    env_info = []
    
    # 系统信息
    env_info.append(("系统信息", ""))
    env_info.append(("操作系统", platform.platform()))
    env_info.append(("Python版本", sys.version))
    env_info.append(("工作目录", os.getcwd()))
    
    # 依赖库版本
    env_info.append(("", ""))
    env_info.append(("依赖库版本", ""))
    env_info.append(("NumPy", np.__version__))
    env_info.append(("Pandas", pd.__version__))
    
    # 尝试导入其他可能的依赖库
    try:
        import matplotlib
        env_info.append(("Matplotlib", matplotlib.__version__))
    except ImportError:
        env_info.append(("Matplotlib", "未安装"))
    
    try:
        import tushare
        env_info.append(("Tushare", tushare.__version__))
    except ImportError:
        env_info.append(("Tushare", "未安装"))
    
    # 数据源配置
    env_info.append(("", ""))
    env_info.append(("数据源配置", ""))

    try:
        from src.utils.config.config_factory import config_factory
        data_source_config = config_factory.load_yaml('data_source.yaml')

        if data_source_config:
            if 'tushare' in data_source_config:
                ts_config = data_source_config['tushare']
                # {{ AURA-X: Modify - 修正Tushare Token检查逻辑，支持新配置结构. Approval: 寸止(ID:1737742800). }}
                # 支持两种配置结构：tushare.token 和 tushare.api.token
                token = ts_config.get('token', '') or ts_config.get('api', {}).get('token', '')
                if token:
                    # 改进token掩码显示逻辑，确保安全性
                    if len(token) > 10:
                        masked_token = token[:6] + '*' * (len(token) - 10) + token[-4:]
                    else:
                        masked_token = token[:2] + '*' * (len(token) - 2)
                    env_info.append(("Tushare Token", f"已配置 ({masked_token})"))
                else:
                    env_info.append(("Tushare Token", "未配置"))
            else:
                env_info.append(("Tushare配置", "未找到"))

            if 'akshare' in data_source_config:
                env_info.append(("AKShare配置", "已找到"))
            else:
                env_info.append(("AKShare配置", "未找到"))
        else:
            env_info.append(("数据源配置", "未找到配置文件"))
    except Exception as e:
        env_info.append(("数据源配置", f"检查出错: {str(e)}"))
    
    # 目录结构检查
    env_info.append(("", ""))
    env_info.append(("目录结构检查", ""))
    
    # output目录
    output_dir = os.path.join(os.getcwd(), 'output')
    env_info.append(("output目录", "存在" if os.path.exists(output_dir) else "不存在"))
    # docs目录
    docs_dir = os.path.join(os.getcwd(), 'docs')
    env_info.append(("docs目录", "存在" if os.path.exists(docs_dir) else "不存在"))
    # logs目录
    logs_dir = os.path.join(os.getcwd(), 'logs')
    env_info.append(("logs目录", "存在" if os.path.exists(logs_dir) else "不存在"))
    
    # 输出信息
    print("\n===== 环境检查报告 =====")
    
    for name, value in env_info:
        if name and value:
            print(f"{name}: {value}")
        elif name:
            print(f"\n-- {name} --")
        else:
            print("")
    
    # 如果指定了输出文件，将结果保存到文件
    if output_file:
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("===== 环境检查报告 =====\n\n")
                
                for name, value in env_info:
                    if name and value:
                        f.write(f"{name}: {value}\n")
                    elif name:
                        f.write(f"\n-- {name} --\n")
                    else:
                        f.write("\n")
            
            print(f"\n环境检查报告已保存到: {output_file}")
        except Exception as e:
            print(f"保存环境检查报告时出错: {e}")
    
    return 0 