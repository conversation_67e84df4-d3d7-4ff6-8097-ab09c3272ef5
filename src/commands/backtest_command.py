"""
回测命令处理模块
处理策略回测功能
"""

import os
import sys
import json
import pandas as pd
from src.utils.temporary.path_utils import get_output_path
from src.data.sources.data_source_factory import DataSourceFactory
from src.commands.data_command import fetch_stock_data, fetch_fundamental_data, process_data
from src.strategies.moving_average_strategy import MovingAverageStrategy

# 尝试导入其他策略
try:
    from src.strategies.factor_strategy import FactorStrategy
    from src.strategies.momentum_strategy import MomentumStrategy
except ImportError:
    pass

def run_backtest_command(args):
    """执行回测命令"""
    try:
        print(f"开始回测策略: {args.strategy}")
        
        # 创建数据源
        data_source = DataSourceFactory.create_tushare(token="c4176f1746c8638b2ebff780654d7aceb0adb3a189a884eae65e2f95")
        
        # 解析股票列表
        stock_list = []
        if args.stocks:
            stock_list = [code.strip() for code in args.stocks.split(",")]
        else:
            # 使用默认股票
            stock_list = ["000001.SZ"]
            
        print(f"使用股票列表: {stock_list}")
        
        # 获取股票数据
        stock_data = fetch_stock_data(data_source, stock_list, args.start, args.end)
        if stock_data.empty:
            print("错误: 未能获取股票数据，无法继续回测")
            return 1
            
        # 获取基本面数据
        try:
            fundamental_data = fetch_fundamental_data(data_source, stock_list, args.start, args.end)
        except SystemExit:
            # 捕获fetch_fundamental_data中的sys.exit调用
            print("警告: 基本面数据获取失败，将继续使用股票数据进行回测")
            fundamental_data = pd.DataFrame()
        
        # 处理数据
        processed_data = process_data(stock_data, fundamental_data)
        
        # 解析策略参数
        strategy_params = {}
        if args.params:
            try:
                strategy_params = json.loads(args.params)
            except json.JSONDecodeError as e:
                print(f"解析策略参数错误: {e}")
                # 使用默认参数继续
        
        # 初始化策略
        if args.strategy == 'moving_average':
            # 使用默认参数
            short_window = strategy_params.get('short_window', 5)
            long_window = strategy_params.get('long_window', 20)
            
            print(f"初始化移动平均线策略，短期窗口: {short_window}，长期窗口: {long_window}")
            strategy = MovingAverageStrategy(short_window=short_window, long_window=long_window)
            
        elif args.strategy == 'factor':
            if 'FactorStrategy' not in globals():
                print("错误: 因子策略模块未找到")
                return 1
                
            factors = strategy_params.get('factors', ['pe_ratio', 'pb_ratio'])
            weights = strategy_params.get('weights', [0.5, 0.5])
            
            print(f"初始化因子策略，因子: {factors}，权重: {weights}")
            strategy = FactorStrategy(factors=factors, weights=weights)
            
        elif args.strategy == 'momentum':
            if 'MomentumStrategy' not in globals():
                print("错误: 动量策略模块未找到")
                return 1
                
            lookback_period = strategy_params.get('lookback_period', 60)
            holding_period = strategy_params.get('holding_period', 20)
            
            print(f"初始化动量策略，回溯期: {lookback_period}，持有期: {holding_period}")
            strategy = MomentumStrategy(lookback_period=lookback_period, holding_period=holding_period)
            
        else:
            print(f"错误: 不支持的策略类型 {args.strategy}")
            return 1
        
        # 设置初始资金
        initial_capital = args.capital or 1000000  # 默认100万
        
        # 设置基准指数
        benchmark = args.benchmark or '000300.SH'  # 默认使用沪深300指数
        
        # 执行回测
        print(f"执行回测: 初始资金={initial_capital}, 基准指数={benchmark}")
        backtest_result = strategy.backtest(
            data=processed_data,
            start_date=args.start,
            end_date=args.end,
            initial_capital=initial_capital,
            benchmark=benchmark
        )
        
        # 保存回测结果
        result_file = get_output_path('backtest_result.json')
        strategy.save_result(backtest_result, result_file)
        print(f"回测结果已保存到: {result_file}")
        
        # 生成图表
        if args.plot:
            plot_file = get_output_path('backtest_plot.png')
            strategy.plot_result(backtest_result, plot_file)
            print(f"回测图表已保存到: {plot_file}")
        
        # 打印性能指标
        performance = backtest_result.get('performance', {})
        print("\n===== 回测性能指标 =====")
        for key, value in performance.items():
            print(f"{key}: {value}")
        
        return 0
        
    except Exception as e:
        import traceback
        print(f"回测过程中发生错误: {e}")
        traceback.print_exc()
        return 1 