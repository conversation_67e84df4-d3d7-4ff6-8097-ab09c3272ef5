"""
自动回测命令处理模块
处理自动化的回测流程
"""

import os
import pandas as pd
from src.data.sources.data_source_factory import DataSourceFactory
from src.utils.temporary.path_utils import get_output_path
from src.commands.data_command import fetch_trading_calendar, get_stock_list, fetch_stock_data, fetch_fundamental_data, process_data
from src.strategies.moving_average_strategy import MovingAverageStrategy
from src.utils.config.config_factory import config_factory

# 尝试导入其他策略
try:
    from src.strategies.factor_strategy import FactorStrategy
    from src.strategies.momentum_strategy import MomentumStrategy
except ImportError:
    pass

def run_autobacktest_command(args):
    """执行自动化回测流程命令"""
    try:
        print("===== 开始自动化回测流程 =====")
        
        # 步骤1: 准备参数
        print("\n步骤1: 准备参数...")
        
        strategy_name = args.strategy
        start_date = args.start
        end_date = args.end
        
        # 如果未提供资金参数，使用默认值
        initial_capital = args.capital or 1000000  # 默认100万
        
        # 设置基准指数
        benchmark = args.benchmark or '000300.SH'  # 默认使用沪深300指数
        
        print(f"策略: {strategy_name}")
        print(f"回测区间: {start_date} 至 {end_date}")
        print(f"初始资金: {initial_capital}")
        print(f"基准指数: {benchmark}")
        
        # 步骤2: 获取数据
        print("\n步骤2: 获取数据...")
        
        # 从配置文件获取Tushare token
        tushare_config = config_factory.get_tushare_config()
        api_config = tushare_config.get('api', {})
        token = api_config.get('token', '')
        
        if not token:
            print("警告: 未找到Tushare API Token，将使用配置文件中的默认值")
            
        # 创建数据源，显式传递token
        data_source = DataSourceFactory.create('tushare', {'token': token})
        
        # 获取交易日历
        trading_days = fetch_trading_calendar(data_source, start_date, end_date)
        if not trading_days:
            print("错误: 未能获取交易日历，无法继续回测")
            return 1
            
        # 获取股票列表
        if args.stock_pool == 'ashare':
            # 使用A股股票池
            if args.pool_size:
                print(f"使用A股股票池，大小: {args.pool_size}")
                stock_list = get_ashare_stock_pool(data_source, top_n=int(args.pool_size))
            else:
                print("使用A股全部股票")
                stock_list = get_ashare_stock_pool(data_source, top_n=None)
        else:
            # 使用指定的股票列表
            stock_list = get_stock_list(data_source, args.stocks)
            
        if not stock_list:
            print("错误: 未能获取股票列表，无法继续回测")
            return 1
            
        # 获取股票数据
        stock_data = fetch_stock_data(data_source, stock_list, start_date, end_date)
        if stock_data.empty:
            print("错误: 未能获取股票数据，无法继续回测")
            return 1
            
        # 获取基本面数据
        try:
            fundamental_data = fetch_fundamental_data(data_source, stock_list, start_date, end_date)
        except SystemExit:
            # 捕获fetch_fundamental_data中的sys.exit调用
            print("警告: 基本面数据获取失败，将继续使用股票数据进行回测")
            fundamental_data = pd.DataFrame()
        
        # 处理数据
        processed_data = process_data(stock_data, fundamental_data)
        
        # 步骤3: 初始化策略
        print("\n步骤3: 初始化策略...")
        
        strategy_params = {}
        if args.params:
            import json
            try:
                strategy_params = json.loads(args.params)
            except json.JSONDecodeError as e:
                print(f"解析策略参数错误: {e}")
                # 使用默认参数继续
        
        # 根据策略名称创建相应的策略实例
        if strategy_name == 'moving_average':
            # 使用默认参数
            short_window = strategy_params.get('short_window', 5)
            long_window = strategy_params.get('long_window', 20)
            
            print(f"初始化移动平均线策略，短期窗口: {short_window}，长期窗口: {long_window}")
            strategy = MovingAverageStrategy(short_window=short_window, long_window=long_window)
            
        elif strategy_name == 'factor':
            if 'FactorStrategy' not in globals():
                print("错误: 因子策略模块未找到")
                return 1
                
            factors = strategy_params.get('factors', ['pe_ratio', 'pb_ratio'])
            weights = strategy_params.get('weights', [0.5, 0.5])
            
            print(f"初始化因子策略，因子: {factors}，权重: {weights}")
            strategy = FactorStrategy(factors=factors, weights=weights)
            
        elif strategy_name == 'momentum':
            if 'MomentumStrategy' not in globals():
                print("错误: 动量策略模块未找到")
                return 1
                
            lookback_period = strategy_params.get('lookback_period', 60)
            holding_period = strategy_params.get('holding_period', 20)
            
            print(f"初始化动量策略，回溯期: {lookback_period}，持有期: {holding_period}")
            strategy = MomentumStrategy(lookback_period=lookback_period, holding_period=holding_period)
            
        else:
            print(f"错误: 不支持的策略类型 {strategy_name}")
            return 1
        
        # 步骤4: 执行回测
        print("\n步骤4: 执行回测...")
        
        backtest_result = strategy.backtest(
            data=processed_data,
            start_date=start_date,
            end_date=end_date,
            initial_capital=initial_capital,
            benchmark=benchmark
        )
        
        # 步骤5: 生成报告
        print("\n步骤5: 生成报告...")
        
        # 保存回测结果
        result_file = get_output_path('backtest_result.json')
        strategy.save_result(backtest_result, result_file)
        print(f"回测结果已保存到: {result_file}")
        
        # 生成图表
        if args.plot:
            plot_file = get_output_path('backtest_plot.png')
            strategy.plot_result(backtest_result, plot_file)
            print(f"回测图表已保存到: {plot_file}")
        
        # 打印性能指标
        performance = backtest_result.get('performance', {})
        print("\n===== 回测性能指标 =====")
        for key, value in performance.items():
            print(f"{key}: {value}")
        
        print("\n===== 自动化回测流程完成 =====")
        return 0
        
    except Exception as e:
        import traceback
        print(f"自动化回测过程中发生错误: {e}")
        traceback.print_exc()
        return 1

def get_ashare_stock_pool(data_source, exclude_st=True, top_n=None, by_market_cap=True):
    """获取A股股票池"""
    try:
        print("获取A股股票池...")
        
        # 获取股票列表
        stock_list_data = data_source.get_stock_basic()
        
        if stock_list_data is None or stock_list_data.empty:
            print("警告: 未能从数据源获取股票列表")
            # 返回一些默认股票作为后备
            return ["000001.SZ", "600000.SH", "600519.SH", "000858.SZ", "601398.SH"]
            
        # 过滤出A股股票
        a_share = stock_list_data[
            (stock_list_data['market'].str.contains('主板|中小板|创业板|科创板', na=False)) &
            (stock_list_data['ts_code'].str.endswith(('.SZ', '.SH')))
        ]
        
        # 过滤ST股票
        if exclude_st:
            a_share = a_share[~a_share['name'].str.contains('ST|退', na=False)]
            
        # 获取上市时间较长的股票
        # a_share = a_share[a_share['list_date'] < '20220101']
        
        # 按市值排序
        if by_market_cap and 'market_value' in a_share.columns:
            a_share = a_share.sort_values('market_value', ascending=False)
        elif by_market_cap:
            print("警告: 股票数据中没有市值信息，使用默认排序")
            
        # 限制股票数量
        if top_n and top_n > 0 and top_n < len(a_share):
            print(f"根据pool_size限制，使用排名前 {top_n} 的股票")
            a_share = a_share.iloc[:top_n]
        else:
            print(f"使用全部 {len(a_share)} 支A股股票")
            
        # 提取股票代码列表
        stock_codes = a_share['ts_code'].tolist()
        
        print(f"获取到 {len(stock_codes)} 支A股股票")
        return stock_codes
        
    except Exception as e:
        print(f"获取A股股票池时发生错误: {e}")
        # 返回一些默认股票作为后备
        return ["000001.SZ", "600000.SH", "600519.SH", "000858.SZ", "601398.SH"] 