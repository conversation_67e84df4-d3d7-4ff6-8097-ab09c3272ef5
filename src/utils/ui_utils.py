"""
用户界面工具
提供菜单展示、用户交互等功能
"""

import os
import time
import logging
from src.utils.temporary.terminal_utils import clear_screen, input_with_timeout
from src.utils.temporary.session_manager import is_first_time_run
from src.utils.config.config_factory import config_factory

logger = logging.getLogger(__name__)

def display_menu_and_get_choice(skip_clear=False):
    """显示菜单并获取用户选择
    
    参数:
        skip_clear: 是否跳过清屏，用于保留之前的执行日志
        
    返回:
        tuple: (选项编号, 是否超时)
    """
    # 每次显示菜单时检查是否为首次运行
    current_is_first_run = is_first_time_run()
    logger.debug(f"菜单显示时检测到当前是否首次运行: {current_is_first_run}")
    
    # 清屏，使界面更整洁（除非要求跳过清屏）
    if not skip_clear:
        clear_screen()
    
    print("\n欢迎使用量化交易策略平台！")
    print("请选择一项操作：")
    
    # 获取默认时间周期配置
    default_periods = get_default_periods()
    
    # 使用配置文件中的默认时间周期
    default_start = default_periods.get('start_date', '20230101')
    default_end = default_periods.get('end_date', '20230110')
    
    options = [
        ("运行自动化回测流程", f"autobacktest --strategy moving_average --start {default_start} --end {default_end} --stock_pool ashare --plot"),
        ("获取A股股票基本数据、日线数据、财务数据、市值数据", "data_fetcher --all --1month"),
        ("运行移动平均线策略回测", f"backtest --strategy moving_average --start {default_start} --end {default_end} --capital 1000000 --plot"),
        ("查看回测结果", "results --file backtest_result.json --plot"),
        ("运维自动化管理", "ops_automation"),
        ("缓存性能基准测试", "cache_performance_test"),
        ("缓存系统深度诊断", "cache_diagnosis"),
        ("1000只股票A/B性能测试", "ab_performance_test"),
        ("Redis环境检查", "redis_environment_check"),
        ("Redis缓存功能测试", "redis_cache_test"),
        ("多级缓存架构测试", "multi_level_cache_test"),
        ("简化多级缓存测试", "simple_multi_cache_test"),
        ("真实场景多级缓存测试", "real_world_multi_cache_test"),
        ("缓存性能实时监控", "cache_performance_monitor"),
        ("缓存预热管理", "cache_preload"),
        ("缓存预热自动测试", "cache_preload_test"),
    ]
    
    for i, option in enumerate(options, 1):
        print(f"{i}. {option[0]}")
    print("0. 退出程序")
    
    # 根据是否首次运行，设置不同的超时时间
    timeout = 5 if current_is_first_run else 5
    
    # 根据是否首次运行设置不同的操作消息
    action_msg = "执行默认选项2" if current_is_first_run else "执行默认选项2"

    print("\n===== 自动倒计时 =====")
    if current_is_first_run:
        print(f"首次运行: {timeout}秒内无操作将默认执行选项2 - 获取A股股票基本数据、日线数据、财务数据、市值数据")
    else:
        print(f"后续运行: {timeout}秒内无操作将默认执行选项2 - 获取A股股票基本数据、日线数据、财务数据、市值数据")
    print(f"数据获取将包含完整的股票基本信息、历史价格数据和财务报表数据")
    print("========================")

    print()  # 空行，美观

    # 使用input_with_timeout函数
    prompt = f"请输入选项编号 (倒计时: {timeout}秒后执行默认选项2): "
    user_input = input_with_timeout(timeout, prompt)
    
    # 处理超时情况
    if user_input is None:
        print("\n[超时] 自动执行默认选项: 获取A股股票基本数据、日线数据、财务数据、市值数据")
        return "2", True
    
    # 处理用户输入
    try:
        choice = int(user_input)
        if 0 <= choice <= len(options):
            return str(choice), False
        else:
            print(f"无效选项：{choice}，请输入0-{len(options)}之间的数字")
            time.sleep(1)
            return display_menu_and_get_choice(skip_clear=skip_clear)  # 递归调用自身重新获取选择
    except ValueError:
        # 特殊检查"0"字符串输入
        if user_input == "0":
            return "0", False
        print(f"无效输入：'{user_input}'，请输入数字")
        time.sleep(1)
        return display_menu_and_get_choice(skip_clear=skip_clear)  # 递归调用自身重新获取选择

def get_default_periods():
    """从配置文件获取默认时间周期设置
    
    返回:
        包含默认时间周期的字典
    """
    data_source_config = config_factory.load_yaml('data_source.yaml')
    
    # 获取Tushare默认周期配置
    tushare_config = data_source_config.get('tushare', {})
    default_periods = tushare_config.get('default_periods', {})
    
    return default_periods

def confirm_action(message, default=True, timeout=10):
    """请求用户确认操作
    
    参数:
        message: 提示信息
        default: 默认选择，True为确认，False为取消
        timeout: 超时时间（秒）
        
    返回:
        bool: 用户是否确认操作
    """
    default_str = "Y/n" if default else "y/N"
    prompt = f"{message} [{default_str}]: "
    
    user_input = input_with_timeout(timeout, prompt)
    
    # 处理超时情况
    if user_input is None:
        print(f" [自动选择: {'是' if default else '否'}]")
        return default
    
    # 处理用户输入
    if user_input.lower() in ['y', 'yes', '是', '确认']:
        return True
    elif user_input.lower() in ['n', 'no', '否', '取消']:
        return False
    else:
        # 输入为空或其他情况，使用默认值
        return default

def show_progress(current, total, description="进度"):
    """显示进度
    
    参数:
        current: 当前进度
        total: 总进度
        description: 进度描述
    """
    percent = 100.0 * current / total
    bar_length = 40
    filled_length = int(bar_length * current // total)
    bar = '█' * filled_length + '-' * (bar_length - filled_length)
    
    print(f'\r{description}: |{bar}| {percent:.1f}% ({current}/{total})', end='')
    
    # 如果完成，打印换行
    if current == total:
        print()

def parse_autobacktest_args():
    """生成自动回测的参数对象
    
    返回:
        argparse.Namespace: 模拟的命令行参数对象
    """
    # 创建一个命名空间对象模拟命令行参数
    class Args:
        pass
    
    args = Args()
    
    # 从配置获取默认时间周期
    default_periods = get_default_periods()
    
    # 设置回测参数
    args.strategy = 'moving_average'  # 默认使用移动平均线策略
    args.start = default_periods.get('start_date')  # 使用默认开始日期
    args.end = default_periods.get('end_date')  # 使用默认结束日期
    args.stock_pool = 'ashare'  # 使用A股股票池
    args.pool_size = None  # 默认使用全部股票
    args.stocks = None  # 不指定具体股票
    args.capital = 1000000  # 初始资金100万
    args.params = None  # 使用默认策略参数
    args.benchmark = '000300.SH'  # 沪深300作为基准
    args.plot = True  # 生成图表
    
    print(f"创建自动回测参数: 策略={args.strategy}, 开始日期={args.start}, 结束日期={args.end}")
    return args 