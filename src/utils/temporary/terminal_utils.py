"""
终端交互工具
提供命令行交互、输入输出处理等功能
"""

import os
import sys
import time
import select
import termios
import tty
import signal

def input_with_timeout(timeout, prompt=""):
    """带有超时的输入函数，可处理管道输入和交互式输入
    
    Args:
        timeout (int): 超时时间（秒）
        prompt (str, optional): 提示信息
        
    Returns:
        str or None: 用户输入，如果超时返回None
    """
    # 首先打印提示信息
    print(prompt, end='', flush=True)
    
    # 检查是否在终端环境中
    is_terminal = sys.stdin.isatty()
    
    # 如果不在终端环境中，回退到简单的带超时功能的读取
    if not is_terminal:
        # 创建一个信号处理器，用于处理超时
        def timeout_handler(signum, frame):
            # 超时时引发异常
            raise TimeoutError("Input timed out")
        
        # 设置信号处理器
        old_handler = signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(timeout)
        
        try:
            # 尝试读取输入
            user_input = sys.stdin.readline().rstrip('\n')
            return user_input
        except TimeoutError:
            # 超时，返回None
            return None
        finally:
            # 无论如何都要恢复原来的信号处理器和取消闹钟
            signal.alarm(0)
            signal.signal(signal.SIGALRM, old_handler)
    
    # 在终端环境中，使用select和tty来处理超时
    old_settings = termios.tcgetattr(sys.stdin)
    try:
        # 设置终端为raw模式，这样可以读取单个字符
        tty.setcbreak(sys.stdin.fileno())
        
        # 创建一个字符缓冲区
        chars = []
        
        # 记录开始时间
        start_time = time.time()
        remaining_time = timeout
        
        while remaining_time > 0:
            # 等待输入，最多等待remaining_time秒
            rlist, _, _ = select.select([sys.stdin], [], [], remaining_time)
            
            # 如果有输入可读
            if rlist:
                char = sys.stdin.read(1)
                
                # 如果是回车，结束输入
                if char == '\n' or char == '\r':
                    print()  # 打印一个换行
                    break
                
                # 如果是退格键
                elif char == '\x7f' or char == '\b':
                    if chars:  # 缓冲区非空时才处理退格
                        chars.pop()  # 删除最后一个字符
                        # 在终端上，模拟退格效果（删除光标前的字符）
                        sys.stdout.write('\b \b')
                        sys.stdout.flush()
                else:
                    # 其他情况，添加字符到缓冲区并回显
                    chars.append(char)
                    sys.stdout.write(char)
                    sys.stdout.flush()
            
            # 更新剩余时间
            elapsed = time.time() - start_time
            remaining_time = timeout - elapsed
        
        # 如果超时且没有输入任何字符，返回None
        if not chars and remaining_time <= 0:
            return None
        
        # 返回输入的字符串
        return ''.join(chars)
    
    finally:
        # 恢复终端设置
        termios.tcsetattr(sys.stdin, termios.TCSADRAIN, old_settings)

def clear_screen():
    """清屏函数，根据操作系统选择适当的清屏命令"""
    if os.name == 'nt':  # Windows
        os.system('cls')
    else:  # Unix/Linux/MacOS
        os.system('clear')

def print_color(text, color='reset'):
    """打印彩色文本
    
    参数:
        text: 要打印的文本
        color: 颜色，可选值包括 'red', 'green', 'yellow', 'blue', 'purple', 'cyan', 'reset'
    """
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'purple': '\033[95m',
        'cyan': '\033[96m',
        'reset': '\033[0m'
    }
    
    start_color = colors.get(color.lower(), colors['reset'])
    end_color = colors['reset']
    
    print(f"{start_color}{text}{end_color}")

def print_progress_bar(iteration, total, prefix='', suffix='', decimals=1, length=50, fill='█'):
    """打印进度条
    
    参数:
        iteration: 当前迭代次数
        total: 总迭代次数
        prefix: 前缀字符串
        suffix: 后缀字符串
        decimals: 百分比小数位数
        length: 进度条长度
        fill: 进度条填充字符
    """
    percent = f"{100 * (iteration / float(total)):.{decimals}f}"
    filled_length = int(length * iteration // total)
    bar = fill * filled_length + '-' * (length - filled_length)
    print(f'\r{prefix} |{bar}| {percent}% {suffix}', end='\r')
    
    # 当迭代完成时打印换行
    if iteration == total:
        print() 