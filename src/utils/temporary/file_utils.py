import os
import errno
import logging

logger = logging.getLogger(__name__)

class FileUtils:
    """
    文件操作工具类，统一处理文件相关IO操作
    """
    @staticmethod
    def read_file(file_path, mode='r', encoding='utf-8'):
        try:
            with open(file_path, mode, encoding=encoding) as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取文件失败: {file_path}, 错误: {e}")
            return None

    @staticmethod
    def write_file(file_path, content, mode='w', encoding='utf-8'):
        try:
            with open(file_path, mode, encoding=encoding) as f:
                f.write(content)
            logger.info(f"写入文件成功: {file_path}")
            return True
        except Exception as e:
            logger.error(f"写入文件失败: {file_path}, 错误: {e}")
            return False

    @staticmethod
    def remove_file(file_path):
        try:
            os.remove(file_path)
            logger.debug(f"删除文件成功: {file_path}")
            return True
        except OSError as e:
            if e.errno == errno.ENOENT:
                # 文件不存在，静默处理
                logger.debug(f"文件不存在，跳过删除: {file_path}")
                return False
            else:
                logger.warning(f"删除文件失败: {file_path}, 错误: {e}")
                return False 