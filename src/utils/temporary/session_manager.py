"""
会话管理工具
管理应用程序会话状态、会话标记和会话恢复
"""

import os
import json
import time
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class SessionManager:
    """会话管理器类"""
    
    def __init__(self, session_dir='output', session_file='.session_marker'):
        """
        初始化会话管理器
        
        参数:
            session_dir: 会话标记文件所在目录
            session_file: 会话标记文件名
        """
        self.session_dir = session_dir
        self.session_file = session_file
        self.session_path = os.path.join(session_dir, session_file)
        
        # 确保会话目录存在
        if not os.path.exists(session_dir):
            os.makedirs(session_dir)
    
    def is_first_time_run(self):
        """
        检查是否为首次运行
        
        返回:
            bool: 是否为首次运行
        """
        logger.debug(f"检查是否为首次运行: {self.session_path}")
        
        # 如果会话标记文件不存在，则视为首次运行
        if not os.path.exists(self.session_path):
            logger.debug(f"会话标记文件不存在，视为首次运行")
            return True
        
        try:
            # 读取会话标记文件内容
            with open(self.session_path, 'r') as f:
                content = f.read().strip()
            logger.debug(f"会话标记内容: '{content}'")
            
            # 如果内容为"first_run"，则视为首次运行
            if content == "first_run":
                logger.debug(f"识别为首次运行")
                return True
            else:
                logger.debug(f"非首次运行，标记为: {content}")
                return False
        except Exception as e:
            logger.error(f"读取会话标记失败: {e}，默认视为首次运行")
            return True  # 出错时默认视为首次运行
    
    def mark_session_as_first(self):
        """
        标记当前会话为首次运行状态
        """
        try:
            with open(self.session_path, 'w') as f:
                f.write("first_run")
            logger.debug(f"会话已成功标记为首次运行")
            
            # 验证写入是否成功
            self._verify_session_marker("first_run")
        except Exception as e:
            logger.error(f"标记会话状态失败: {e}")
    
    def mark_session_as_used(self):
        """
        标记当前会话为已使用状态
        """
        logger.debug(f"正在标记会话为已使用状态: {self.session_path}")
        try:
            with open(self.session_path, 'w') as f:
                f.write("used")
            logger.debug(f"会话已成功标记为已使用")
            
            # 验证写入是否成功
            self._verify_session_marker("used")
        except Exception as e:
            logger.error(f"标记会话状态失败: {e}")
    
    def _verify_session_marker(self, expected_content):
        """
        验证会话标记内容
        
        参数:
            expected_content: 期望的内容
        """
        if os.path.exists(self.session_path):
            with open(self.session_path, 'r') as f:
                content = f.read().strip()
            logger.debug(f"验证会话标记内容: '{content}'")
            if content != expected_content:
                logger.warning(f"会话标记内容与预期不符: 预期 '{expected_content}'，实际 '{content}'")
        else:
            logger.warning(f"警告: 写入后无法找到会话标记文件")
    
    def get_session_info(self):
        """
        获取会话信息
        
        返回:
            dict: 会话信息
        """
        if not os.path.exists(self.session_path):
            return {"status": "new", "message": "会话未初始化"}
            
        try:
            with open(self.session_path, 'r') as f:
                content = f.read().strip()
                
            if content == "first_run":
                return {"status": "first_run", "message": "首次运行会话"}
            elif content == "used":
                return {"status": "used", "message": "已使用的会话"}
            else:
                return {"status": "unknown", "message": f"未知会话状态: {content}"}
        except Exception as e:
            logger.error(f"获取会话信息失败: {e}")
            return {"status": "error", "message": f"读取会话信息出错: {str(e)}"}
    
    def save_session_data(self, data):
        """
        保存会话数据
        
        参数:
            data: 要保存的数据字典
        """
        session_data_file = os.path.join(self.session_dir, "session_data.json")
        
        try:
            # 添加时间戳
            data['timestamp'] = datetime.now().isoformat()
            
            with open(session_data_file, 'w') as f:
                json.dump(data, f, indent=2)
            logger.debug(f"会话数据已保存到 {session_data_file}")
            return True
        except Exception as e:
            logger.error(f"保存会话数据失败: {e}")
            return False
    
    def load_session_data(self):
        """
        加载会话数据
        
        返回:
            dict: 会话数据
        """
        session_data_file = os.path.join(self.session_dir, "session_data.json")
        
        if not os.path.exists(session_data_file):
            logger.warning(f"会话数据文件不存在: {session_data_file}")
            return {}
            
        try:
            with open(session_data_file, 'r') as f:
                data = json.load(f)
            logger.debug(f"已加载会话数据")
            return data
        except Exception as e:
            logger.error(f"加载会话数据失败: {e}")
            return {}

# 创建默认会话管理器实例，方便直接导入使用
session_manager = SessionManager()

# 为了保持与原main.py中函数签名一致，提供简便函数
def is_first_time_run():
    """检查是否为首次运行"""
    return session_manager.is_first_time_run()

def mark_session_as_used():
    """标记当前会话为已使用状态"""
    session_manager.mark_session_as_used() 