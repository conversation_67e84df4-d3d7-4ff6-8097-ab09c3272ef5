"""
日期处理工具
提供日期格式转换、日期计算等功能
"""

from datetime import datetime, timedelta
import calendar  # 标准库 calendar 模块

def get_lookback_date(date_str, months=3):
    """向前推算指定月数的日期
    
    参数:
        date_str: 原始日期字符串，格式为YYYYMMDD
        months: 向前推算的月数
        
    返回:
        推算后的日期字符串，格式为YYYYMMDD
    """
    # 将字符串转换为datetime对象
    if len(date_str) == 8:  # YYYYMMDD格式
        date = datetime.strptime(date_str, '%Y%m%d')
    else:
        raise ValueError(f"不支持的日期格式: {date_str}")
    
    # 计算月份和年份变化
    year = date.year
    month = date.month - months
    
    # 处理月份为负数或零的情况
    while month <= 0:
        year -= 1
        month += 12
    
    # 创建新的日期对象
    try:
        new_date = date.replace(year=year, month=month)
    except ValueError:
        # 处理无效日期（如2月30日），选择月末
        if month == 2:
            # 处理闰年情况
            if year % 4 == 0 and (year % 100 != 0 or year % 400 == 0):
                day = min(date.day, 29)
            else:
                day = min(date.day, 28)
            new_date = date.replace(year=year, month=month, day=day)
        else:
            # 其他月份的月末
            last_day = {
                1: 31, 3: 31, 4: 30, 5: 31, 6: 30, 
                7: 31, 8: 31, 9: 30, 10: 31, 11: 30, 12: 31
            }
            day = min(date.day, last_day[month])
            new_date = date.replace(year=year, month=month, day=day)
    
    # 格式化为YYYYMMDD字符串并返回
    return new_date.strftime('%Y%m%d')

def format_date(date_str, input_format='%Y%m%d', output_format='%Y-%m-%d'):
    """转换日期格式
    
    参数:
        date_str: 日期字符串
        input_format: 输入格式
        output_format: 输出格式
        
    返回:
        格式化后的日期字符串
    """
    date_obj = datetime.strptime(date_str, input_format)
    return date_obj.strftime(output_format)

def get_today(format='%Y%m%d'):
    """获取今天的日期
    
    参数:
        format: 日期格式
        
    返回:
        今天的日期字符串
    """
    return datetime.now().strftime(format)

def date_diff(start_date, end_date, format='%Y%m%d'):
    """计算两个日期之间的天数差
    
    参数:
        start_date: 开始日期字符串
        end_date: 结束日期字符串
        format: 日期格式
        
    返回:
        两个日期之间的天数
    """
    start = datetime.strptime(start_date, format)
    end = datetime.strptime(end_date, format)
    return (end - start).days

def timegm(tuple_time):
    """
    将时间元组转换为UTC时间戳
    这个函数直接使用 Python 标准库中的 calendar.timegm 函数
    
    参数:
        tuple_time: 时间元组 (年, 月, 日, 时, 分, 秒, 周几, 年日, 夏令时)
        
    返回:
        int: UTC时间戳
    """
    return calendar.timegm(tuple_time)

def dt_to_timestamp(dt, utc=True):
    """
    将 datetime 对象转换为时间戳
    
    参数:
        dt: datetime 对象
        utc: 是否使用 UTC 时间，默认为 True
    
    返回:
        int: 时间戳
    """
    if utc:
        return timegm(dt.utctimetuple())
    else:
        return int(dt.timestamp())

def timestamp_to_dt(timestamp, utc=True):
    """
    将时间戳转换为 datetime 对象
    
    参数:
        timestamp: 时间戳
        utc: 是否转换为 UTC 时间，默认为 True
    
    返回:
        datetime: datetime 对象
    """
    if utc:
        return datetime.utcfromtimestamp(timestamp)
    else:
        return datetime.fromtimestamp(timestamp)

