"""
文件路径处理工具
提供路径管理、文件名处理等功能
"""

import os
from pathlib import Path

def get_output_path(filename, output_type=None):
    """
    根据文件类型确定输出路径
    
    参数:
        filename: 原始文件名
        output_type: 可选的输出类型指定，可以是 'script', 'doc', 或 'output'
        
    返回:
        带路径的文件名
    """
    # 确保输出目录存在
    if not os.path.exists('script'):
        os.makedirs('script')
    if not os.path.exists('docs'):
        os.makedirs('docs')
    if not os.path.exists('output'):
        os.makedirs('output')
    
    # 如果指定了输出类型，直接使用
    if output_type:
        if output_type == 'script':
            return os.path.join('script', os.path.basename(filename))
        elif output_type == 'docs':
            return os.path.join('docs', os.path.basename(filename))
        else:  # output 或其他
            return os.path.join('output', os.path.basename(filename))
    
    # 根据文件扩展名确定类型
    file_ext = os.path.splitext(filename)[1].lower()
    
    # 脚本文件
    if file_ext in ['.py', '.sh', '.bat']:
        return os.path.join('script', os.path.basename(filename))
    
    # 文档文件
    elif file_ext in ['.txt', '.md', '.pdf', '.doc', '.docx', '.html', '.json']:
        return os.path.join('docs', os.path.basename(filename))
    
    # 其他输出文件 (数据文件、图片等)
    else:
        return os.path.join('output', os.path.basename(filename))

def ensure_directory_exists(path):
    """
    确保指定的目录存在，如果不存在则创建
    
    参数:
        path: 目录路径
    """
    os.makedirs(path, exist_ok=True)
    
def get_project_root():
    """
    获取项目根目录
    
    返回:
        项目根目录的绝对路径
    """
    # 假设当前文件在项目的utils目录下
    current_file = Path(__file__).resolve()
    return current_file.parent.parent

def join_path(*paths):
    """
    连接路径组件
    
    参数:
        *paths: 路径组件
        
    返回:
        连接后的路径
    """
    return os.path.join(*paths)

def get_filename(path):
    """
    从路径中提取文件名（不含扩展名）
    
    参数:
        path: 文件路径
        
    返回:
        文件名（不含扩展名）
    """
    return os.path.splitext(os.path.basename(path))[0]

def get_extension(path):
    """
    从路径中提取文件扩展名
    
    参数:
        path: 文件路径
        
    返回:
        文件扩展名（含点号）
    """
    return os.path.splitext(path)[1] 