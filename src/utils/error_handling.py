#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一错误处理和重试机制
消除项目中重复的错误处理代码
"""

import time
import random
import logging
import asyncio
import functools
from typing import Callable, Any, List, Optional, Type, Union, Dict, Tuple
from abc import ABC, abstractmethod
from enum import Enum


class RetryStrategy(Enum):
    """重试策略枚举"""
    FIXED = "fixed"  # 固定延迟
    LINEAR = "linear"  # 线性增长
    EXPONENTIAL = "exponential"  # 指数退避
    FIBONACCI = "fibonacci"  # 斐波那契序列


class ErrorCategory(Enum):
    """错误分类枚举"""
    NETWORK = "network"  # 网络错误
    RATE_LIMIT = "rate_limit"  # 限流错误
    AUTHENTICATION = "authentication"  # 认证错误
    DATA = "data"  # 数据错误
    SYSTEM = "system"  # 系统错误
    UNKNOWN = "unknown"  # 未知错误


class BaseErrorHandler(ABC):
    """错误处理基类"""
    
    def __init__(self, 
                 max_retries: int = 3,
                 base_delay: float = 1.0,
                 max_delay: float = 60.0,
                 strategy: RetryStrategy = RetryStrategy.EXPONENTIAL,
                 jitter: bool = True,
                 logger: Optional[logging.Logger] = None):
        """
        初始化错误处理器
        
        参数:
            max_retries: 最大重试次数
            base_delay: 基础延迟时间
            max_delay: 最大延迟时间
            strategy: 重试策略
            jitter: 是否添加随机抖动
            logger: 日志记录器
        """
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.strategy = strategy
        self.jitter = jitter
        self.logger = logger or logging.getLogger(__name__)
        
        # 错误分类映射
        self.error_mapping = self._build_error_mapping()
    
    @abstractmethod
    def _build_error_mapping(self) -> Dict[Type[Exception], ErrorCategory]:
        """构建错误分类映射"""
        pass
    
    @abstractmethod
    def should_retry(self, exception: Exception, attempt: int) -> bool:
        """判断是否应该重试"""
        pass
    
    def categorize_error(self, exception: Exception) -> ErrorCategory:
        """对错误进行分类"""
        for exc_type, category in self.error_mapping.items():
            if isinstance(exception, exc_type):
                return category
        return ErrorCategory.UNKNOWN
    
    def calculate_delay(self, attempt: int, error_category: ErrorCategory = None) -> float:
        """
        计算重试延迟时间
        
        参数:
            attempt: 当前重试次数
            error_category: 错误分类
            
        返回:
            延迟时间（秒）
        """
        if self.strategy == RetryStrategy.FIXED:
            delay = self.base_delay
        elif self.strategy == RetryStrategy.LINEAR:
            delay = self.base_delay * attempt
        elif self.strategy == RetryStrategy.EXPONENTIAL:
            delay = self.base_delay * (2 ** (attempt - 1))
        elif self.strategy == RetryStrategy.FIBONACCI:
            delay = self.base_delay * self._fibonacci(attempt)
        else:
            delay = self.base_delay
        
        # 根据错误类型调整延迟
        if error_category == ErrorCategory.RATE_LIMIT:
            delay *= 2  # 限流错误延迟加倍
        elif error_category == ErrorCategory.NETWORK:
            delay *= 1.5  # 网络错误延迟增加50%
        
        # 限制最大延迟
        delay = min(delay, self.max_delay)
        
        # 添加随机抖动
        if self.jitter:
            jitter_amount = random.uniform(0.1, 0.3) * delay
            delay += jitter_amount
        
        return delay
    
    def _fibonacci(self, n: int) -> int:
        """计算斐波那契数列第n项"""
        if n <= 1:
            return 1
        elif n == 2:
            return 1
        else:
            a, b = 1, 1
            for _ in range(2, n):
                a, b = b, a + b
            return b
    
    def execute_with_retry(self, func: Callable, *args, **kwargs) -> Any:
        """
        执行函数并处理重试逻辑
        
        参数:
            func: 要执行的函数
            *args, **kwargs: 函数参数
            
        返回:
            函数的返回值
        """
        last_exception = None
        
        for attempt in range(1, self.max_retries + 2):  # +1是因为第一次不算重试
            try:
                result = func(*args, **kwargs)
                
                # 如果有自定义的结果验证逻辑
                if hasattr(self, 'validate_result') and not self.validate_result(result):
                    if attempt <= self.max_retries:
                        self.logger.warning(f"第 {attempt} 次尝试返回无效结果，准备重试")
                        continue
                
                if attempt > 1:
                    self.logger.info(f"第 {attempt} 次尝试成功")
                
                return result
                
            except Exception as e:
                last_exception = e
                error_category = self.categorize_error(e)
                
                if self.should_retry(e, attempt) and attempt <= self.max_retries:
                    delay = self.calculate_delay(attempt, error_category)
                    self.logger.warning(
                        f"第 {attempt} 次尝试失败 ({error_category.value}): {str(e)}，"
                        f"将在 {delay:.2f} 秒后重试"
                    )
                    time.sleep(delay)
                else:
                    if attempt > self.max_retries:
                        self.logger.error(f"达到最大重试次数 {self.max_retries}，最终失败: {str(e)}")
                    else:
                        self.logger.error(f"不可重试的错误: {str(e)}")
                    raise e
        
        # 理论上不会执行到这里
        if last_exception:
            raise last_exception


class AsyncErrorHandler(BaseErrorHandler):
    """异步错误处理器"""
    
    async def execute_with_retry_async(self, func: Callable, *args, **kwargs) -> Any:
        """
        异步执行函数并处理重试逻辑
        
        参数:
            func: 要执行的异步函数
            *args, **kwargs: 函数参数
            
        返回:
            函数的返回值
        """
        last_exception = None
        
        for attempt in range(1, self.max_retries + 2):
            try:
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                if hasattr(self, 'validate_result') and not self.validate_result(result):
                    if attempt <= self.max_retries:
                        self.logger.warning(f"第 {attempt} 次尝试返回无效结果，准备重试")
                        continue
                
                if attempt > 1:
                    self.logger.info(f"第 {attempt} 次尝试成功")
                
                return result
                
            except Exception as e:
                last_exception = e
                error_category = self.categorize_error(e)
                
                if self.should_retry(e, attempt) and attempt <= self.max_retries:
                    delay = self.calculate_delay(attempt, error_category)
                    self.logger.warning(
                        f"第 {attempt} 次尝试失败 ({error_category.value}): {str(e)}，"
                        f"将在 {delay:.2f} 秒后重试"
                    )
                    await asyncio.sleep(delay)
                else:
                    if attempt > self.max_retries:
                        self.logger.error(f"达到最大重试次数 {self.max_retries}，最终失败: {str(e)}")
                    else:
                        self.logger.error(f"不可重试的错误: {str(e)}")
                    raise e
        
        if last_exception:
            raise last_exception


class NetworkErrorHandler(BaseErrorHandler):
    """网络错误处理器"""
    
    def _build_error_mapping(self) -> Dict[Type[Exception], ErrorCategory]:
        """构建网络相关的错误分类映射"""
        import requests
        import urllib.error
        import socket
        
        return {
            # 网络错误
            requests.exceptions.ConnectionError: ErrorCategory.NETWORK,
            requests.exceptions.Timeout: ErrorCategory.NETWORK,
            requests.exceptions.ConnectTimeout: ErrorCategory.NETWORK,
            requests.exceptions.ReadTimeout: ErrorCategory.NETWORK,
            urllib.error.URLError: ErrorCategory.NETWORK,
            socket.timeout: ErrorCategory.NETWORK,
            socket.gaierror: ErrorCategory.NETWORK,
            ConnectionError: ErrorCategory.NETWORK,
            TimeoutError: ErrorCategory.NETWORK,
            
            # 限流错误
            requests.exceptions.HTTPError: ErrorCategory.RATE_LIMIT,  # 可能是429状态码
            
            # 认证错误
            requests.exceptions.HTTPError: ErrorCategory.AUTHENTICATION,  # 可能是401/403状态码
        }
    
    def should_retry(self, exception: Exception, attempt: int) -> bool:
        """判断网络错误是否应该重试"""
        error_category = self.categorize_error(exception)
        
        # 网络错误和限流错误可以重试
        if error_category in [ErrorCategory.NETWORK, ErrorCategory.RATE_LIMIT]:
            return True
        
        # HTTP错误需要根据状态码判断
        if hasattr(exception, 'response') and hasattr(exception.response, 'status_code'):
            status_code = exception.response.status_code
            # 5xx服务器错误和429限流可以重试
            if status_code >= 500 or status_code == 429:
                return True
            # 4xx客户端错误（除了429）通常不重试
            if 400 <= status_code < 500:
                return False
        
        # 认证错误通常不重试
        if error_category == ErrorCategory.AUTHENTICATION:
            return False
        
        # 其他错误默认重试
        return True


class DataFetchErrorHandler(NetworkErrorHandler):
    """数据获取错误处理器"""
    
    def _build_error_mapping(self) -> Dict[Type[Exception], ErrorCategory]:
        """构建数据获取相关的错误分类映射"""
        base_mapping = super()._build_error_mapping()
        
        # 添加数据获取特定的错误
        try:
            # 动态导入可能不存在的异常类
            from src.data.exceptions import DataFetchError, RateLimitError, AuthenticationError
            base_mapping.update({
                DataFetchError: ErrorCategory.DATA,
                RateLimitError: ErrorCategory.RATE_LIMIT,
                AuthenticationError: ErrorCategory.AUTHENTICATION,
            })
        except ImportError:
            pass
        
        return base_mapping
    
    def validate_result(self, result: Any) -> bool:
        """验证数据获取结果是否有效"""
        if result is None:
            return False
        
        # 如果是DataFrame，检查是否为空
        if hasattr(result, 'empty') and result.empty:
            return False
        
        # 如果是列表，检查是否为空
        if isinstance(result, list) and len(result) == 0:
            return False
        
        return True


def retry_on_error(handler_class: Type[BaseErrorHandler] = NetworkErrorHandler,
                   max_retries: int = 3,
                   base_delay: float = 1.0,
                   strategy: RetryStrategy = RetryStrategy.EXPONENTIAL,
                   **handler_kwargs):
    """
    重试装饰器
    
    参数:
        handler_class: 错误处理器类
        max_retries: 最大重试次数
        base_delay: 基础延迟时间
        strategy: 重试策略
        **handler_kwargs: 错误处理器的其他参数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            handler = handler_class(
                max_retries=max_retries,
                base_delay=base_delay,
                strategy=strategy,
                **handler_kwargs
            )
            return handler.execute_with_retry(func, *args, **kwargs)
        return wrapper
    return decorator


def async_retry_on_error(handler_class: Type[AsyncErrorHandler] = AsyncErrorHandler,
                        max_retries: int = 3,
                        base_delay: float = 1.0,
                        strategy: RetryStrategy = RetryStrategy.EXPONENTIAL,
                        **handler_kwargs):
    """
    异步重试装饰器
    
    参数:
        handler_class: 异步错误处理器类
        max_retries: 最大重试次数
        base_delay: 基础延迟时间
        strategy: 重试策略
        **handler_kwargs: 错误处理器的其他参数
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 确保使用异步错误处理器
            if not issubclass(handler_class, AsyncErrorHandler):
                # 如果传入的不是异步处理器，创建一个异步版本
                class AsyncVersion(handler_class, AsyncErrorHandler):
                    pass
                handler = AsyncVersion(
                    max_retries=max_retries,
                    base_delay=base_delay,
                    strategy=strategy,
                    **handler_kwargs
                )
            else:
                handler = handler_class(
                    max_retries=max_retries,
                    base_delay=base_delay,
                    strategy=strategy,
                    **handler_kwargs
                )
            return await handler.execute_with_retry_async(func, *args, **kwargs)
        return wrapper
    return decorator
