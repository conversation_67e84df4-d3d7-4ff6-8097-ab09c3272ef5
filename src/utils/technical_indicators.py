#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术指标计算工具类
提取公共的技术指标计算逻辑，消除重复代码
"""

import pandas as pd
import numpy as np
from typing import Union, Optional, Dict, Any


class TechnicalIndicators:
    """技术指标计算工具类"""
    
    @staticmethod
    def calculate_moving_average(data: pd.Series, window: int, ma_type: str = 'simple') -> pd.Series:
        """
        计算移动平均线
        
        参数:
            data: 价格序列
            window: 窗口大小
            ma_type: 移动平均类型 ('simple', 'exponential', 'weighted')
            
        返回:
            移动平均线序列
        """
        if ma_type == 'simple':
            return data.rolling(window=window, min_periods=1).mean()
        elif ma_type == 'exponential':
            return data.ewm(span=window).mean()
        elif ma_type == 'weighted':
            weights = np.arange(1, window + 1)
            return data.rolling(window=window).apply(
                lambda x: np.sum(weights * x) / weights.sum(), raw=True
            )
        else:
            raise ValueError(f"不支持的移动平均类型: {ma_type}")
    
    @staticmethod
    def calculate_dual_ma_signals(data: pd.DataFrame, 
                                  short_window: int, 
                                  long_window: int,
                                  price_col: str = 'close',
                                  ma_type: str = 'simple') -> pd.DataFrame:
        """
        计算双移动平均线信号
        
        参数:
            data: 包含价格数据的DataFrame
            short_window: 短期移动平均窗口
            long_window: 长期移动平均窗口
            price_col: 价格列名
            ma_type: 移动平均类型
            
        返回:
            包含移动平均线和信号的DataFrame
        """
        result = data.copy()
        
        # 计算短期和长期移动平均线
        result['short_mavg'] = TechnicalIndicators.calculate_moving_average(
            result[price_col], short_window, ma_type
        )
        result['long_mavg'] = TechnicalIndicators.calculate_moving_average(
            result[price_col], long_window, ma_type
        )
        
        # 生成信号：短期均线上穿长期均线为买入(1)，下穿为卖出(0)
        result['signal'] = np.where(result['short_mavg'] > result['long_mavg'], 1.0, 0.0)
        
        # 计算持仓方向的变化
        result['position_change'] = result['signal'].diff()
        
        return result
    
    @staticmethod
    def calculate_volume_filter(data: pd.DataFrame, 
                               volume_col: str = 'vol',
                               window: int = 5,
                               threshold: float = 1.2) -> pd.Series:
        """
        计算成交量过滤条件
        
        参数:
            data: 包含成交量数据的DataFrame
            volume_col: 成交量列名
            window: 成交量移动平均窗口
            threshold: 成交量阈值倍数
            
        返回:
            成交量过滤条件序列
        """
        volume_ma = data[volume_col].rolling(window=window).mean()
        return data[volume_col] > (volume_ma * threshold)
    
    @staticmethod
    def apply_signal_confirmation(signals: pd.Series, 
                                 confirm_days: int = 2) -> pd.Series:
        """
        应用信号确认机制
        
        参数:
            signals: 原始信号序列
            confirm_days: 确认天数
            
        返回:
            确认后的信号序列
        """
        confirmed_signals = pd.Series(0.0, index=signals.index)
        
        for i in range(confirm_days, len(signals)):
            # 检查过去N天的信号是否一致
            recent_signals = signals.iloc[i-confirm_days+1:i+1]
            
            # 如果信号一致，则确认信号
            if len(recent_signals.unique()) == 1:
                confirmed_signals.iloc[i] = recent_signals.iloc[-1]
        
        return confirmed_signals
    
    @staticmethod
    def calculate_rsi(data: pd.Series, window: int = 14) -> pd.Series:
        """
        计算相对强弱指数(RSI)
        
        参数:
            data: 价格序列
            window: 计算窗口
            
        返回:
            RSI序列
        """
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    @staticmethod
    def calculate_bollinger_bands(data: pd.Series, 
                                 window: int = 20, 
                                 num_std: float = 2) -> Dict[str, pd.Series]:
        """
        计算布林带
        
        参数:
            data: 价格序列
            window: 移动平均窗口
            num_std: 标准差倍数
            
        返回:
            包含上轨、中轨、下轨的字典
        """
        ma = data.rolling(window=window).mean()
        std = data.rolling(window=window).std()
        
        return {
            'upper': ma + (std * num_std),
            'middle': ma,
            'lower': ma - (std * num_std)
        }
    
    @staticmethod
    def calculate_macd(data: pd.Series, 
                      fast_period: int = 12, 
                      slow_period: int = 26, 
                      signal_period: int = 9) -> Dict[str, pd.Series]:
        """
        计算MACD指标
        
        参数:
            data: 价格序列
            fast_period: 快线周期
            slow_period: 慢线周期
            signal_period: 信号线周期
            
        返回:
            包含MACD线、信号线、柱状图的字典
        """
        ema_fast = data.ewm(span=fast_period).mean()
        ema_slow = data.ewm(span=slow_period).mean()
        
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal_period).mean()
        histogram = macd_line - signal_line
        
        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }


class BacktestUtils:
    """回测工具类"""

    @staticmethod
    def execute_backtest_with_signals(data: pd.DataFrame,
                                     signals: pd.DataFrame,
                                     initial_capital: float = 1000000.0,
                                     price_col: str = 'close',
                                     signal_col: str = 'signal',
                                     enable_stop_loss: bool = False,
                                     stop_loss_pct: float = 0.05) -> pd.DataFrame:
        """
        执行完整的回测流程

        参数:
            data: 原始数据
            signals: 包含信号的数据
            initial_capital: 初始资金
            price_col: 价格列名
            signal_col: 信号列名
            enable_stop_loss: 是否启用止损
            stop_loss_pct: 止损百分比

        返回:
            包含完整回测结果的DataFrame
        """
        positions = signals.copy()

        # 初始化资产跟踪变量
        cash = initial_capital
        shares = 0
        entry_price = 0  # 记录买入价格，用于止损

        # 创建列表来存储每日的资产状态
        position_values = []
        cash_values = []
        total_values = []
        returns_list = []

        prev_total = initial_capital

        # 逐日计算资产变化
        for i, row in positions.iterrows():
            current_price = row[price_col]
            current_signal = row[signal_col]

            # 止损检查（如果启用）
            if enable_stop_loss and shares > 0 and entry_price > 0:
                loss_pct = (entry_price - current_price) / entry_price
                if loss_pct > stop_loss_pct:
                    # 执行止损
                    cash = shares * current_price
                    shares = 0
                    entry_price = 0

            # 信号处理
            if i == 0:
                # 第一天：根据信号决定是否买入
                if current_signal == 1.0:  # 买入信号
                    shares = cash / current_price
                    entry_price = current_price if enable_stop_loss else 0
                    cash = 0
                else:  # 空仓
                    shares = 0
                    entry_price = 0
            else:
                prev_signal = positions.iloc[i-1][signal_col]
                if current_signal != prev_signal:
                    if current_signal == 1.0:  # 买入信号
                        if shares == 0:  # 当前空仓才买入
                            shares = cash / current_price
                            entry_price = current_price if enable_stop_loss else 0
                            cash = 0
                    else:  # 卖出信号
                        if shares > 0:  # 当前持仓才卖出
                            cash = shares * current_price
                            shares = 0
                            entry_price = 0

            # 计算当日资产价值
            position_value = shares * current_price
            total_value = position_value + cash

            # 计算收益率
            if i == 0:
                daily_return = 0.0
            else:
                daily_return = (total_value - prev_total) / prev_total

            # 记录当日状态
            position_values.append(position_value)
            cash_values.append(cash)
            total_values.append(total_value)
            returns_list.append(daily_return)

            prev_total = total_value

        # 添加到结果DataFrame
        positions['position_value'] = position_values
        positions['cash'] = cash_values
        positions['total'] = total_values
        positions['returns'] = returns_list

        return positions

    @staticmethod
    def calculate_portfolio_value(positions: pd.DataFrame,
                                 price_col: str = 'close',
                                 signal_col: str = 'signal',
                                 initial_capital: float = 1000000.0) -> pd.DataFrame:
        """
        计算投资组合价值
        
        参数:
            positions: 包含价格和信号的DataFrame
            price_col: 价格列名
            signal_col: 信号列名
            initial_capital: 初始资金
            
        返回:
            包含资产跟踪信息的DataFrame
        """
        result = positions.copy()
        
        # 初始化资产跟踪变量
        cash = initial_capital
        shares = 0
        
        # 创建列表来存储每日的资产状态
        position_values = []
        cash_values = []
        total_values = []
        returns_list = []
        
        prev_total = initial_capital
        
        # 逐日计算资产变化
        for i, row in result.iterrows():
            current_price = row[price_col]
            current_signal = row[signal_col]
            
            # 如果信号发生变化，执行交易
            if i == 0:
                # 第一天：根据信号决定是否买入
                if current_signal == 1.0:  # 买入信号
                    shares = cash / current_price
                    cash = 0
                else:  # 空仓
                    shares = 0
            else:
                prev_signal = result.iloc[i-1][signal_col]
                if current_signal != prev_signal:
                    if current_signal == 1.0:  # 买入信号
                        shares = cash / current_price
                        cash = 0
                    else:  # 卖出信号
                        cash = shares * current_price
                        shares = 0
            
            # 计算当日资产价值
            position_value = shares * current_price
            total_value = position_value + cash
            
            # 计算收益率
            if i == 0:
                daily_return = 0.0
            else:
                daily_return = (total_value - prev_total) / prev_total
            
            # 记录当日状态
            position_values.append(position_value)
            cash_values.append(cash)
            total_values.append(total_value)
            returns_list.append(daily_return)
            
            prev_total = total_value
        
        # 添加到结果DataFrame
        result['position_value'] = position_values
        result['cash'] = cash_values
        result['total'] = total_values
        result['returns'] = returns_list
        
        return result

    @staticmethod
    def calculate_performance_metrics(backtest_results: pd.DataFrame,
                                     initial_capital: float = 1000000.0) -> dict:
        """
        计算策略性能指标

        参数:
            backtest_results: 回测结果DataFrame
            initial_capital: 初始资金

        返回:
            包含性能指标的字典
        """
        if backtest_results.empty or 'total' not in backtest_results.columns:
            return {}

        # 基础指标
        total_return = (backtest_results['total'].iloc[-1] / initial_capital) - 1

        # 计算年化收益率
        total_days = len(backtest_results)
        annual_return = (backtest_results['total'].iloc[-1] / initial_capital) ** (252 / total_days) - 1

        # 计算最大回撤
        backtest_results['cummax'] = backtest_results['total'].cummax()
        backtest_results['drawdown'] = (backtest_results['cummax'] - backtest_results['total']) / backtest_results['cummax']
        max_drawdown = backtest_results['drawdown'].max()

        # 计算夏普比率
        if 'returns' in backtest_results.columns:
            risk_free_rate = 0.03 / 252  # 假设无风险年化收益率为3%
            excess_return = backtest_results['returns'] - risk_free_rate
            sharpe_ratio = excess_return.mean() / backtest_results['returns'].std() * np.sqrt(252) if backtest_results['returns'].std() > 0 else 0
        else:
            sharpe_ratio = 0

        # 计算交易次数
        if 'signal' in backtest_results.columns:
            signal_changes = (backtest_results['signal'].diff() != 0).sum()
        else:
            signal_changes = 0

        # 计算胜率
        win_rate = 0
        if 'returns' in backtest_results.columns:
            positive_returns = backtest_results['returns'][backtest_results['returns'] > 0]
            win_rate = len(positive_returns) / len(backtest_results['returns']) if len(backtest_results['returns']) > 0 else 0

        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'trades': signal_changes,
            'win_rate': win_rate,
            'final_capital': backtest_results['total'].iloc[-1],
            'total_days': total_days
        }
