"""
配置工厂
用于加载、验证和管理配置文件的工厂类
"""

import os
import yaml
import json
import shutil
import datetime
import logging
from typing import Dict, Any, Optional, List, Tuple, Union, Type, Set

# 配置日志记录器
logger = logging.getLogger(__name__)

class ConfigValidationError(Exception):
    """配置验证异常"""
    pass

class ConfigFactory:
    """配置工厂类"""
    
    # 配置架构定义，用于类型和范围验证
    DEFAULT_SCHEMA = {
        "app": {
            "type": "dict",
            "required": True,
            "schema": {
                "name": {"type": "str", "required": True},
                "version": {"type": "str", "required": True},
                "log_level": {"type": "str", "required": True, "allowed": ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]}
            }
        },
        "environment": {
            "type": "dict",
            "required": True,
            "schema": {
                "mode": {"type": "str", "required": True, "allowed": ["development", "production", "testing"]}
            }
        },
        "data_sources": {
            "type": "dict",
            "required": True,
            "schema": {
                "default": {"type": "str", "required": True}
            }
        }
    }
    
    def __init__(self, backup_dir: str = None):
        """
        初始化配置工厂
        
        Args:
            backup_dir: 配置备份目录，默认为config目录下的backup子目录
        """
        self._config_cache = {}
        self._schemas = {}
        self._config_versions = {}
        self._change_history = {}
        self._backup_dir = backup_dir
    
    def load_config(self, config_name: str, config_dir: str, use_cache: bool = True) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_name: 配置文件名（不含扩展名）
            config_dir: 配置文件目录
            use_cache: 是否使用缓存
            
        Returns:
            配置字典
            
        Raises:
            FileNotFoundError: 配置文件不存在
            yaml.YAMLError: YAML格式错误
        """
        config_path = os.path.join(config_dir, f"{config_name}.yaml")
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
            
        if use_cache and config_name in self._config_cache:
            return self._config_cache[config_name]
            
        with open(config_path, 'r', encoding='utf-8') as f:
            try:
                config = yaml.safe_load(f)
                
                # 初始化版本信息（如果文件是首次加载）
                if config_name not in self._config_versions:
                    self._config_versions[config_name] = 1
                    self._change_history[config_name] = [{
                        'version': 1,
                        'timestamp': datetime.datetime.now().isoformat(),
                        'description': '初始加载'
                    }]
                
                # 更新缓存
                if use_cache:
                    self._config_cache[config_name] = config
                    
                return config
            except yaml.YAMLError as e:
                logger.error(f"加载配置文件 {config_path} 失败: {str(e)}")
                raise
    
    def get_value(self, config: Dict[str, Any], key: str, default: Any = None, validate_type: Type = None) -> Any:
        """
        获取配置值
        
        Args:
            config: 配置字典
            key: 配置键（支持点号分隔的路径）
            default: 默认值
            validate_type: 验证值的类型
            
        Returns:
            配置值
            
        Raises:
            TypeError: 如果类型验证失败
        """
        keys = key.split('.')
        value = config
        
        for k in keys:
            if isinstance(value, dict):
                value = value.get(k, default)
            else:
                return default
        
        # 类型验证
        if validate_type is not None and value is not default and not isinstance(value, validate_type):
            actual_type = type(value).__name__
            expected_type = validate_type.__name__
            error_msg = f"配置值类型错误: {key} 应为 {expected_type}，实际为 {actual_type}"
            logger.error(error_msg)
            raise TypeError(error_msg)
                
        return value
    
    def set_value(self, config: Dict[str, Any], key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            config: 配置字典
            key: 配置键（支持点号分隔的路径）
            value: 配置值
        """
        keys = key.split('.')
        current = config
        
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
            
        current[keys[-1]] = value
    
    def save_config(self, config: Dict[str, Any], config_name: str, config_dir: str, 
                   description: str = "", backup: bool = True) -> bool:
        """
        保存配置到文件
        
        Args:
            config: 配置字典
            config_name: 配置文件名（不含扩展名）
            config_dir: 配置文件目录
            description: 更改描述
            backup: 是否备份旧版本
            
        Returns:
            是否保存成功
        """
        config_path = os.path.join(config_dir, f"{config_name}.yaml")
        
        try:
            # 备份旧配置
            if backup and os.path.exists(config_path):
                self._backup_config(config_name, config_dir)
            
            # 更新版本信息
            if config_name in self._config_versions:
                self._config_versions[config_name] += 1
            else:
                self._config_versions[config_name] = 1
                self._change_history[config_name] = []
            
            # 记录变更历史
            current_version = self._config_versions[config_name]
            self._change_history[config_name].append({
                'version': current_version,
                'timestamp': datetime.datetime.now().isoformat(),
                'description': description or f"配置更新至版本 {current_version}"
            })
            
            # 保存配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, allow_unicode=True, default_flow_style=False)
            
            # 更新缓存
            self._config_cache[config_name] = config
            
            logger.info(f"配置 {config_name} 已保存至 {config_path}，版本：{current_version}")
            return True
        except Exception as e:
            logger.error(f"保存配置 {config_name} 失败: {str(e)}")
            return False
    
    def _backup_config(self, config_name: str, config_dir: str) -> bool:
        """
        备份配置文件
        
        Args:
            config_name: 配置文件名
            config_dir: 配置文件目录
            
        Returns:
            是否备份成功
        """
        try:
            # 创建备份目录
            backup_dir = self._backup_dir or os.path.join(config_dir, "backup")
            os.makedirs(backup_dir, exist_ok=True)
            
            # 源文件和目标文件路径
            source_path = os.path.join(config_dir, f"{config_name}.yaml")
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 如果有版本信息则包含在文件名中
            version = self._config_versions.get(config_name, 0)
            backup_path = os.path.join(backup_dir, f"{config_name}_v{version}_{timestamp}.yaml")
            
            # 复制文件
            shutil.copy2(source_path, backup_path)
            logger.info(f"配置 {config_name} 已备份至 {backup_path}")
            return True
        except Exception as e:
            logger.error(f"备份配置 {config_name} 失败: {str(e)}")
            return False
    
    def restore_config(self, config_name: str, config_dir: str, version: int = None, 
                      backup_timestamp: str = None) -> Dict[str, Any]:
        """
        恢复配置文件的历史版本
        
        Args:
            config_name: 配置文件名
            config_dir: 配置文件目录
            version: 要恢复的版本号
            backup_timestamp: 要恢复的备份时间戳，格式为 YYYYMMDD_HHMMSS
            
        Returns:
            恢复的配置字典
            
        Raises:
            FileNotFoundError: 指定的备份版本不存在
        """
        # 寻找匹配的备份文件
        backup_dir = self._backup_dir or os.path.join(config_dir, "backup")
        if not os.path.exists(backup_dir):
            raise FileNotFoundError(f"备份目录不存在: {backup_dir}")
        
        # 获取所有备份文件
        backup_files = [f for f in os.listdir(backup_dir) if f.startswith(f"{config_name}_v")]
        
        # 根据条件筛选
        target_file = None
        if version is not None:
            target_files = [f for f in backup_files if f"_v{version}_" in f]
            if target_files:
                target_file = target_files[0]  # 取第一个匹配的文件
        elif backup_timestamp is not None:
            target_files = [f for f in backup_files if backup_timestamp in f]
            if target_files:
                target_file = target_files[0]  # 取第一个匹配的文件
        
        if not target_file:
            raise FileNotFoundError(f"未找到匹配的备份文件: 版本={version}, 时间戳={backup_timestamp}")
        
        # 恢复备份
        backup_path = os.path.join(backup_dir, target_file)
        target_path = os.path.join(config_dir, f"{config_name}.yaml")
        
        # 先备份当前配置
        self._backup_config(config_name, config_dir)
        
        # 恢复备份文件
        shutil.copy2(backup_path, target_path)
        
        # 更新缓存和版本信息
        with open(target_path, 'r', encoding='utf-8') as f:
            restored_config = yaml.safe_load(f)
            self._config_cache[config_name] = restored_config
            
            # 记录恢复操作
            self._config_versions[config_name] += 1
            current_version = self._config_versions[config_name]
            self._change_history[config_name].append({
                'version': current_version,
                'timestamp': datetime.datetime.now().isoformat(),
                'description': f"从备份 {target_file} 恢复配置"
            })
            
            logger.info(f"配置 {config_name} 已从 {backup_path} 恢复")
            return restored_config
    
    def merge_config(self, base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并配置
        
        Args:
            base_config: 基础配置
            override_config: 覆盖配置
            
        Returns:
            合并后的配置
        """
        merged = base_config.copy()
        
        for key, value in override_config.items():
            if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                merged[key] = self.merge_config(merged[key], value)
            else:
                merged[key] = value
                
        return merged
    
    def register_schema(self, config_name: str, schema: Dict[str, Any]) -> None:
        """
        注册配置架构定义，用于配置验证
        
        Args:
            config_name: 配置名称
            schema: 配置架构定义
        """
        self._schemas[config_name] = schema
    
    def validate_config(self, config: Dict[str, Any], schema: Dict[str, Any] = None) -> bool:
        """
        验证配置有效性，包括必要字段和类型检查
        
        Args:
            config: 配置字典
            schema: 配置架构定义，如果为None则使用默认架构
            
        Returns:
            是否有效
        """
        if schema is None:
            schema = self.DEFAULT_SCHEMA
        
        try:
            return self._validate_against_schema(config, schema)
        except Exception as e:
            logger.warning(f"配置验证失败: {str(e)}")
            return False
    
    def _validate_against_schema(self, config: Dict[str, Any], schema: Dict[str, Any], 
                                path: str = "") -> bool:
        """
        根据架构定义验证配置
        
        Args:
            config: 配置字典或子项
            schema: 架构定义
            path: 当前配置路径，用于错误提示
            
        Returns:
            是否有效
            
        Raises:
            ConfigValidationError: 配置验证失败时抛出异常
        """
        # 检查必要字段
        for key, definition in schema.items():
            current_path = f"{path}.{key}" if path else key
            
            if definition.get("required", False) and key not in config:
                error_msg = f"缺少必要配置项: {current_path}"
                logger.error(error_msg)
                raise ConfigValidationError(error_msg)
            
            # 如果配置中没有此项，但不是必需的，则跳过
            if key not in config:
                continue
            
            value = config[key]
            value_type = definition.get("type")
            
            # 类型检查
            if value_type == "dict":
                if not isinstance(value, dict):
                    error_msg = f"配置项类型错误: {current_path} 应为字典，实际为 {type(value).__name__}"
                    logger.error(error_msg)
                    raise ConfigValidationError(error_msg)
                
                # 递归验证子项
                if "schema" in definition:
                    self._validate_against_schema(value, definition["schema"], current_path)
            
            elif value_type == "list":
                if not isinstance(value, list):
                    error_msg = f"配置项类型错误: {current_path} 应为列表，实际为 {type(value).__name__}"
                    logger.error(error_msg)
                    raise ConfigValidationError(error_msg)
                
                # 检查列表元素类型
                if "item_type" in definition:
                    item_type = definition["item_type"]
                    for i, item in enumerate(value):
                        if not self._check_type(item, item_type):
                            error_msg = f"配置项类型错误: {current_path}[{i}] 应为 {item_type}，实际为 {type(item).__name__}"
                            logger.error(error_msg)
                            raise ConfigValidationError(error_msg)
            
            # 基本类型检查
            elif value_type in ["str", "int", "float", "bool"]:
                if not self._check_type(value, value_type):
                    error_msg = f"配置项类型错误: {current_path} 应为 {value_type}，实际为 {type(value).__name__}"
                    logger.error(error_msg)
                    raise ConfigValidationError(error_msg)
            
            # 值范围检查
            if "allowed" in definition and value not in definition["allowed"]:
                allowed_values = ", ".join(map(str, definition["allowed"]))
                error_msg = f"配置项值错误: {current_path} 的值必须为以下之一 [{allowed_values}]，实际为 {value}"
                logger.error(error_msg)
                raise ConfigValidationError(error_msg)
                
            # 数值范围检查
            if value_type in ["int", "float"]:
                if "min" in definition and value < definition["min"]:
                    error_msg = f"配置项值错误: {current_path} 的值必须大于等于 {definition['min']}，实际为 {value}"
                    logger.error(error_msg)
                    raise ConfigValidationError(error_msg)
                
                if "max" in definition and value > definition["max"]:
                    error_msg = f"配置项值错误: {current_path} 的值必须小于等于 {definition['max']}，实际为 {value}"
                    logger.error(error_msg)
                    raise ConfigValidationError(error_msg)
        
        return True
    
    def _check_type(self, value: Any, expected_type: str) -> bool:
        """
        检查值类型
        
        Args:
            value: 要检查的值
            expected_type: 期望的类型名
            
        Returns:
            是否匹配
        """
        if expected_type == "str":
            return isinstance(value, str)
        elif expected_type == "int":
            return isinstance(value, int) and not isinstance(value, bool)
        elif expected_type == "float":
            return isinstance(value, (int, float)) and not isinstance(value, bool)
        elif expected_type == "bool":
            return isinstance(value, bool)
        elif expected_type == "dict":
            return isinstance(value, dict)
        elif expected_type == "list":
            return isinstance(value, list)
        else:
            return True
    
    def get_version(self, config_name: str) -> int:
        """
        获取配置文件的当前版本
        
        Args:
            config_name: 配置文件名
            
        Returns:
            当前版本号
        """
        return self._config_versions.get(config_name, 0)
    
    def get_change_history(self, config_name: str) -> List[Dict[str, Any]]:
        """
        获取配置文件的变更历史
        
        Args:
            config_name: 配置文件名
            
        Returns:
            变更历史记录列表
        """
        return self._change_history.get(config_name, [])
    
    def export_change_history(self, config_name: str, export_file: str) -> bool:
        """
        导出配置文件的变更历史
        
        Args:
            config_name: 配置文件名
            export_file: 导出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(self._change_history.get(config_name, []), f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"导出变更历史失败: {str(e)}")
            return False
    
    def get_backup_list(self, config_name: str, config_dir: str) -> List[Dict[str, Any]]:
        """
        获取配置文件的备份列表
        
        Args:
            config_name: 配置文件名
            config_dir: 配置文件目录
            
        Returns:
            备份信息列表
        """
        backup_dir = self._backup_dir or os.path.join(config_dir, "backup")
        if not os.path.exists(backup_dir):
            return []
        
        backup_files = [f for f in os.listdir(backup_dir) if f.startswith(f"{config_name}_v")]
        result = []
        
        for file in backup_files:
            try:
                # 解析文件名，格式为 {config_name}_v{version}_{timestamp}.yaml
                parts = file.split('_')
                version_part = [p for p in parts if p.startswith('v')][0]
                version = int(version_part[1:])
                
                # 提取时间戳部分，可能格式为 YYYYMMDD_HHMMSS
                timestamp_part = file.replace(f"{config_name}_v{version}_", "").replace(".yaml", "")
                
                result.append({
                    'file': file,
                    'version': version,
                    'timestamp': timestamp_part,
                    'path': os.path.join(backup_dir, file)
                })
            except Exception:
                # 跳过无法解析的文件名
                continue
        
        # 按版本号排序
        result.sort(key=lambda x: x['version'], reverse=True)
        return result
    
    def clear_cache(self) -> None:
        """清除配置缓存"""
        self._config_cache.clear()
        logger.debug("已清除配置缓存")
        
    def create_from_file(self, file_path: str, use_cache: bool = True) -> Dict[str, Any]:
        """
        从文件路径创建配置对象
        
        Args:
            file_path: 配置文件路径
            use_cache: 是否使用缓存
            
        Returns:
            Dict[str, Any]: 配置对象
            
        Raises:
            FileNotFoundError: 配置文件不存在
            yaml.YAMLError: YAML格式错误
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"配置文件不存在: {file_path}")
            
        config_dir = os.path.dirname(file_path)
        config_name = os.path.splitext(os.path.basename(file_path))[0]
        
        # 使用load_config方法加载配置文件
        config = self.load_config(config_name, config_dir, use_cache)
        
        # 创建一个Proxy对象来模拟配置访问
        class ConfigProxy:
            def __init__(self, config_dict, factory):
                self._config = config_dict
                self._factory = factory
                
            def get(self, key, default=None):
                return self._factory.get_value(self._config, key, default)
                
            def __getitem__(self, key):
                return self.get(key)
                
            def __contains__(self, key):
                keys = key.split('.')
                value = self._config
                
                for k in keys:
                    if isinstance(value, dict) and k in value:
                        value = value[k]
                    else:
                        return False
                return True
                
            def to_dict(self):
                return self._config
        
        return ConfigProxy(config, self)

    # 添加Tushare配置相关方法，方便替换ConfigLoader
    def get_tushare_config(self, config_dir: str = None) -> Dict[str, Any]:
        """
        获取Tushare配置
        
        Args:
            config_dir: 配置文件目录，如果为None则使用默认配置目录
            
        Returns:
            Tushare配置字典
        """
        if config_dir is None:
            # 获取当前文件所在目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            
            # 尝试获取项目根目录下的config目录（和src同级）
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
            root_config_dir = os.path.join(root_dir, 'config')
            
            # 如果根目录下存在config目录，使用它
            if os.path.exists(root_config_dir):
                config_dir = root_config_dir
                logger.debug(f"使用项目根目录下的config目录: {config_dir}")
            else:
                # 否则使用项目根目录下的config目录 - 修复配置目录路径
                config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), 'config')
                logger.debug(f"使用项目根目录config目录: {config_dir}")
        
        try:
            # 加载data_source.yaml配置
            logger.debug(f"尝试从 {config_dir} 加载 data_source.yaml")
            data_source_config = self.load_config('data_source', config_dir)
            
            # 获取Tushare配置部分
            tushare_config = data_source_config.get('tushare', {})
            
            return tushare_config
        except Exception as e:
            logger.error(f"获取Tushare配置时出错: {str(e)}")
            return {}
    
    def get_tushare_token(self, config_dir: str = None) -> str:
        """
        获取Tushare API Token
        
        Args:
            config_dir: 配置文件目录，如果为None则使用默认配置目录
            
        Returns:
            Tushare API Token字符串
        """
        tushare_config = self.get_tushare_config(config_dir)
        
        # 获取API配置中的token
        api_config = tushare_config.get('api', {})
        token = api_config.get('token', '')
        
        if not token:
            logger.warning("未找到Tushare API Token")
        
        return token
        
    def load_yaml(self, filename: str, config_dir: str = None) -> Dict[str, Any]:
        """
        加载YAML配置文件（兼容ConfigLoader的接口）
        
        Args:
            filename: 配置文件名（含扩展名）
            config_dir: 配置文件目录，如果为None则使用默认配置目录
            
        Returns:
            配置字典
        """
        if config_dir is None:
            # 获取当前文件所在目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            
            # 尝试获取项目根目录下的config目录（和src同级）
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
            root_config_dir = os.path.join(root_dir, 'config')
            
            # 如果根目录下存在config目录，使用它
            if os.path.exists(root_config_dir):
                config_dir = root_config_dir
                logger.debug(f"使用项目根目录下的config目录: {config_dir}")
            else:
                # 否则使用项目根目录下的config目录 - 修复配置目录路径
                config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), 'config')
                logger.debug(f"使用项目根目录config目录: {config_dir}")
            
        # 去掉可能的扩展名
        config_name = os.path.splitext(filename)[0]
        
        try:
            logger.debug(f"尝试从 {config_dir} 加载 {filename}")
            return self.load_config(config_name, config_dir)
        except Exception as e:
            logger.error(f"加载YAML配置文件失败 {filename}: {str(e)}")
            return {}

# 创建全局单例实例，方便替换ConfigLoader.config_loader
config_factory = ConfigFactory()

