"""
日志接口
"""

from abc import ABC, abstractmethod

class LoggerInterface(ABC):
    """日志接口抽象类"""
    
    @abstractmethod
    def debug(self, message):
        """记录DEBUG级别日志"""
        pass
        
    @abstractmethod
    def info(self, message):
        """记录INFO级别日志"""
        pass
        
    @abstractmethod
    def warning(self, message):
        """记录WARNING级别日志"""
        pass
        
    @abstractmethod
    def error(self, message):
        """记录ERROR级别日志"""
        pass
        
    @abstractmethod
    def critical(self, message):
        """记录CRITICAL级别日志"""
        pass
        
    @abstractmethod
    def get_logger(self, name):
        """获取指定名称的日志记录器"""
        pass

