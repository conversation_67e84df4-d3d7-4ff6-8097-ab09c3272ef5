"""
控制台日志
"""

import logging
import sys
from src.utils.logging.logger_interface import LoggerInterface

class ConsoleLogger(LoggerInterface):
    """控制台日志记录器"""
    
    def __init__(self, log_level=logging.INFO):
        """
        初始化控制台日志记录器
        
        参数:
            log_level: 日志级别，默认为INFO
        """
        self.log_level = log_level
        
        # 配置根日志记录器
        self._configure_root_logger()
    
    def _configure_root_logger(self):
        """配置根日志记录器"""
        # 获取根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.log_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    def get_logger(self, name):
        """
        获取指定名称的日志记录器
        
        参数:
            name: 日志记录器名称
            
        返回:
            logger: 日志记录器实例
        """
        return logging.getLogger(name)
        
    def debug(self, message):
        """
        记录DEBUG级别日志
        
        参数:
            message: 日志消息
        """
        logging.debug(message)
        
    def info(self, message):
        """
        记录INFO级别日志
        
        参数:
            message: 日志消息
        """
        logging.info(message)
        
    def warning(self, message):
        """
        记录WARNING级别日志
        
        参数:
            message: 日志消息
        """
        logging.warning(message)
        
    def error(self, message):
        """
        记录ERROR级别日志
        
        参数:
            message: 日志消息
        """
        logging.error(message)
        
    def critical(self, message):
        """
        记录CRITICAL级别日志
        
        参数:
            message: 日志消息
        """
        logging.critical(message)

