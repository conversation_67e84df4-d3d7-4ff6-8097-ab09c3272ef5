"""
日志工厂
"""

import os
import logging
from datetime import datetime
from src.utils.logging.file_logger import FileLogger
from src.utils.logging.console_logger import ConsoleLogger
from src.utils.logging.logger_interface import LoggerInterface

class LoggerFactory:
    """日志工厂类"""
    
    @staticmethod
    def create_logger(logger_type="file", log_level=logging.INFO, log_file=None, max_bytes=10485760, backup_count=5):
        """
        创建日志记录器
        
        参数:
            logger_type: 日志记录器类型，可选值为"file"或"console"，默认为"file"
            log_level: 日志级别，默认为INFO
            log_file: 日志文件路径，仅当logger_type为"file"时有效，默认为None（自动生成）
            max_bytes: 单个日志文件最大字节数，默认为10MB
            backup_count: 保留的日志文件数量，默认为5
            
        返回:
            logger: 日志记录器实例
        """
        if logger_type == "file":
            # 如果未指定日志文件路径，自动生成
            if log_file is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                log_file = f"logs/app_{timestamp}.log"
            
            return FileLogger(log_level, log_file, max_bytes, backup_count)
        elif logger_type == "console":
            return ConsoleLogger(log_level)
        else:
            raise ValueError(f"不支持的日志记录器类型: {logger_type}")

def setup_logging(log_path=None, level=logging.INFO, console=True, file=True):
    """
    设置日志记录
    
    参数:
        log_path: 日志文件路径，默认为None（自动生成）
        level: 日志级别，默认为INFO
        console: 是否输出到控制台，默认为True
        file: 是否输出到文件，默认为True
    """
    # 如果未指定日志文件路径，自动生成
    if log_path is None and file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_path = f"logs/app_{timestamp}.log"
    
    # 创建日志目录
    if file and log_path:
        log_dir = os.path.dirname(log_path)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
    
    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 添加控制台处理器
    if console:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # 添加文件处理器
    if file and log_path:
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            log_path,
            maxBytes=10485760,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    return root_logger

def get_logger(name, level=None):
    """
    获取指定名称的日志记录器
    
    参数:
        name: 日志记录器名称
        level: 日志级别，默认为None（使用root logger的级别）
        
    返回:
        logger: 日志记录器实例
    """
    logger = logging.getLogger(name)
    if level is not None:
        logger.setLevel(level)
    return logger

def log_exception(logger, exception, message=None):
    """
    记录异常信息
    
    参数:
        logger: 日志记录器
        exception: 异常对象
        message: 额外消息，默认为None
    """
    if message:
        logger.error(f"{message}: {str(exception)}")
    else:
        logger.error(str(exception))
    import traceback
    logger.debug("".join(traceback.format_exception(type(exception), exception, exception.__traceback__)))

def log_function_call(func):
    """
    装饰器：记录函数调用
    
    参数:
        func: 被装饰的函数
        
    返回:
        wrapper: 装饰后的函数
    """
    def wrapper(*args, **kwargs):
        import time
        logger = logging.getLogger(func.__module__)
        logger.debug(f"调用函数: {func.__name__}")
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            logger.debug(f"函数 {func.__name__} 执行完成，耗时: {end_time - start_time:.6f}秒")
            return result
        except Exception as e:
            end_time = time.time()
            logger.error(f"函数 {func.__name__} 执行异常，耗时: {end_time - start_time:.6f}秒")
            log_exception(logger, e)
            raise
    return wrapper

