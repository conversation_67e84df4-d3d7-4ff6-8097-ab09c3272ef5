#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
历史数据管理模块

负责加载、处理和管理回测所需的历史市场数据
"""

import os
import json
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Union, Optional, Tuple, Any, Callable
from pathlib import Path

logger = logging.getLogger(__name__)

class DataHandlerException(Exception):
    """数据处理异常"""
    pass

class DataHandler:
    """
    历史数据管理器
    
    负责加载和管理回测所需的各类历史数据，包括：
    1. 价格数据（开盘价、收盘价、最高价、最低价等）
    2. 交易量数据
    3. 基本面数据
    4. 因子数据
    
    支持多种数据源和数据格式，并提供缓存机制以提高性能
    """
    
    def __init__(self, 
                 data_root: Optional[str] = None, 
                 start_date: Optional[str] = None, 
                 end_date: Optional[str] = None,
                 use_cache: bool = True,
                 cache_dir: Optional[str] = None):
        """
        初始化数据管理器
        
        参数:
            data_root: 数据根目录
            start_date: 开始日期，格式 YYYY-MM-DD
            end_date: 结束日期，格式 YYYY-MM-DD
            use_cache: 是否使用缓存
            cache_dir: 缓存目录
        """
        self.data_root = data_root or os.environ.get('DATA_ROOT', './data')
        self.start_date = start_date
        self.end_date = end_date
        self.use_cache = use_cache
        self.cache_dir = cache_dir or os.path.join(self.data_root, 'cache')
        
        # 确保缓存目录存在
        if self.use_cache:
            os.makedirs(self.cache_dir, exist_ok=True)
            
        # 数据缓存
        self._price_data = {}  # 价格数据缓存
        self._volume_data = {}  # 成交量数据缓存
        self._fundamental_data = {}  # 基本面数据缓存
        self._factor_data = {}  # 因子数据缓存
        self._calendar = None  # 交易日历缓存
        
        # 数据源配置
        self._data_sources = {}
        
        # 预处理函数
        self._preprocessors = {}
        
    def set_date_range(self, start_date: str, end_date: str) -> None:
        """
        设置数据日期范围
        
        参数:
            start_date: 开始日期，格式 YYYY-MM-DD
            end_date: 结束日期，格式 YYYY-MM-DD
        """
        self.start_date = start_date
        self.end_date = end_date
        
        # 清除缓存
        self._price_data = {}
        self._volume_data = {}
        self._fundamental_data = {}
        self._factor_data = {}
        self._calendar = None
        
    def register_data_source(self, name: str, source_config: Dict[str, Any]) -> None:
        """
        注册数据源
        
        参数:
            name: 数据源名称
            source_config: 数据源配置，包含类型、路径等信息
        """
        self._data_sources[name] = source_config
        
    def register_preprocessor(self, data_type: str, preprocessor: Callable) -> None:
        """
        注册数据预处理函数
        
        参数:
            data_type: 数据类型
            preprocessor: 预处理函数
        """
        self._preprocessors[data_type] = preprocessor
        
    def get_trading_calendar(self) -> List[str]:
        """
        获取交易日历
        
        返回:
            List[str]: 交易日列表，格式为 YYYY-MM-DD
        """
        if self._calendar is not None:
            return self._calendar
        
        # 尝试从文件加载
        calendar_path = os.path.join(self.data_root, 'calendar.csv')
        if os.path.exists(calendar_path):
            calendar_df = pd.read_csv(calendar_path)
            if 'date' in calendar_df.columns:
                calendar = calendar_df['date'].astype(str).tolist()
            else:
                calendar = calendar_df.iloc[:, 0].astype(str).tolist()
        else:
            # 如果没有日历文件，从价格数据生成
            price_data = self.get_price_data(['000001.SH'], start_date=self.start_date, end_date=self.end_date)
            if not price_data.empty:
                calendar = price_data.index.unique().astype(str).tolist()
            else:
                # 生成工作日日历
                start = datetime.strptime(self.start_date, '%Y-%m-%d')
                end = datetime.strptime(self.end_date, '%Y-%m-%d')
                calendar = []
                current = start
                while current <= end:
                    if current.weekday() < 5:  # 0-4 是周一至周五
                        calendar.append(current.strftime('%Y-%m-%d'))
                    current += timedelta(days=1)
        
        # 过滤日期范围
        if self.start_date and self.end_date:
            calendar = [d for d in calendar if self.start_date <= d <= self.end_date]
        
        self._calendar = sorted(calendar)
        return self._calendar
    
    def get_price_data(self, 
                       symbols: List[str],
                       fields: List[str] = None,
                       start_date: Optional[str] = None,
                       end_date: Optional[str] = None,
                       frequency: str = 'daily',
                       adjust: str = 'none') -> pd.DataFrame:
        """
        获取价格数据
        
        参数:
            symbols: 股票代码列表
            fields: 字段列表，默认为 ['open', 'high', 'low', 'close', 'volume']
            start_date: 开始日期，格式 YYYY-MM-DD，默认使用初始化时的 start_date
            end_date: 结束日期，格式 YYYY-MM-DD，默认使用初始化时的 end_date
            frequency: 数据频率，支持 'daily', 'weekly', 'monthly'
            adjust: 价格调整方式，支持 'none', 'forward', 'backward'
            
        返回:
            DataFrame: 价格数据，索引为日期，列包含symbol和请求的字段
        """
        fields = fields or ['open', 'high', 'low', 'close', 'volume']
        start_date = start_date or self.start_date
        end_date = end_date or self.end_date
        
        # 用于缓存的键
        cache_key = f"price_{'+'.join(symbols)}_{'+'.join(fields)}_{start_date}_{end_date}_{frequency}_{adjust}"
        
        # 如果已有缓存，直接返回
        if cache_key in self._price_data:
            return self._price_data[cache_key]
        
        # 检查磁盘缓存
        if self.use_cache:
            cache_file = os.path.join(self.cache_dir, f"{cache_key.replace('/', '_')}.csv")
            if os.path.exists(cache_file):
                try:
                    cached_data = pd.read_csv(cache_file)
                    cached_data['date'] = pd.to_datetime(cached_data['date'])
                    cached_data.set_index('date', inplace=True)
                    self._price_data[cache_key] = cached_data
                    return cached_data
                except Exception as e:
                    logger.warning(f"加载缓存失败: {e}")
        
        # 从数据源加载数据
        all_data = []
        for symbol in symbols:
            # 决定使用哪个数据源
            data_source = self._get_data_source_for_symbol(symbol)
            
            # 从指定的数据源加载数据
            symbol_data = self._load_price_data_from_source(
                symbol, data_source, fields, start_date, end_date, frequency, adjust)
            
            if symbol_data is not None and not symbol_data.empty:
                # 添加股票代码列
                if 'symbol' not in symbol_data.columns:
                    symbol_data['symbol'] = symbol
                all_data.append(symbol_data)
        
        # 合并所有股票数据
        if all_data:
            result_df = pd.concat(all_data, axis=0)
            
            # 应用数据预处理
            if 'price' in self._preprocessors:
                result_df = self._preprocessors['price'](result_df)
            
            # 缓存结果
            self._price_data[cache_key] = result_df
            
            # 保存到磁盘缓存
            if self.use_cache:
                try:
                    result_df.reset_index().to_csv(cache_file, index=False)
                except Exception as e:
                    logger.warning(f"保存缓存失败: {e}")
            
            return result_df
        else:
            # 返回空DataFrame，保持一致的结构
            empty_df = pd.DataFrame(columns=['symbol'] + fields)
            empty_df['date'] = pd.to_datetime([])
            empty_df.set_index('date', inplace=True)
            return empty_df
    
    def get_fundamental_data(self,
                            symbols: List[str],
                            fields: List[str],
                            start_date: Optional[str] = None,
                            end_date: Optional[str] = None,
                            report_type: str = 'quarterly') -> pd.DataFrame:
        """
        获取基本面数据
        
        参数:
            symbols: 股票代码列表
            fields: 字段列表
            start_date: 开始日期
            end_date: 结束日期
            report_type: 报告类型，支持 'quarterly', 'annual'
            
        返回:
            DataFrame: 基本面数据
        """
        start_date = start_date or self.start_date
        end_date = end_date or self.end_date
        
        # 缓存键
        cache_key = f"fundamental_{'+'.join(symbols)}_{'+'.join(fields)}_{start_date}_{end_date}_{report_type}"
        
        # 检查内存缓存
        if cache_key in self._fundamental_data:
            return self._fundamental_data[cache_key]
        
        # 检查磁盘缓存
        if self.use_cache:
            cache_file = os.path.join(self.cache_dir, f"{cache_key.replace('/', '_')}.csv")
            if os.path.exists(cache_file):
                try:
                    cached_data = pd.read_csv(cache_file)
                    cached_data['date'] = pd.to_datetime(cached_data['date'])
                    cached_data.set_index('date', inplace=True)
                    self._fundamental_data[cache_key] = cached_data
                    return cached_data
                except Exception as e:
                    logger.warning(f"加载基本面数据缓存失败: {e}")
        
        # 加载基本面数据
        all_data = []
        for symbol in symbols:
            # 获取适用的数据源
            data_source = self._get_data_source_for_symbol(symbol, data_type='fundamental')
            
            # 加载数据
            symbol_data = self._load_fundamental_data_from_source(
                symbol, data_source, fields, start_date, end_date, report_type)
            
            if symbol_data is not None and not symbol_data.empty:
                if 'symbol' not in symbol_data.columns:
                    symbol_data['symbol'] = symbol
                all_data.append(symbol_data)
        
        # 合并数据
        if all_data:
            result_df = pd.concat(all_data, axis=0)
            
            # 应用预处理
            if 'fundamental' in self._preprocessors:
                result_df = self._preprocessors['fundamental'](result_df)
            
            # 缓存
            self._fundamental_data[cache_key] = result_df
            
            # 保存到磁盘
            if self.use_cache:
                try:
                    result_df.reset_index().to_csv(cache_file, index=False)
                except Exception as e:
                    logger.warning(f"保存基本面数据缓存失败: {e}")
            
            return result_df
        else:
            empty_df = pd.DataFrame(columns=['symbol'] + fields)
            empty_df['date'] = pd.to_datetime([])
            empty_df.set_index('date', inplace=True)
            return empty_df
    
    def get_factor_data(self,
                       symbols: List[str],
                       factors: List[str],
                       start_date: Optional[str] = None,
                       end_date: Optional[str] = None) -> pd.DataFrame:
        """
        获取因子数据
        
        参数:
            symbols: 股票代码列表
            factors: 因子列表
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            DataFrame: 因子数据
        """
        start_date = start_date or self.start_date
        end_date = end_date or self.end_date
        
        # 缓存键
        cache_key = f"factor_{'+'.join(symbols)}_{'+'.join(factors)}_{start_date}_{end_date}"
        
        # 检查内存缓存
        if cache_key in self._factor_data:
            return self._factor_data[cache_key]
        
        # 检查磁盘缓存
        if self.use_cache:
            cache_file = os.path.join(self.cache_dir, f"{cache_key.replace('/', '_')}.csv")
            if os.path.exists(cache_file):
                try:
                    cached_data = pd.read_csv(cache_file)
                    cached_data['date'] = pd.to_datetime(cached_data['date'])
                    cached_data.set_index('date', inplace=True)
                    self._factor_data[cache_key] = cached_data
                    return cached_data
                except Exception as e:
                    logger.warning(f"加载因子数据缓存失败: {e}")
        
        # 加载因子数据
        all_data = []
        for symbol in symbols:
            # 获取适用的数据源
            data_source = self._get_data_source_for_symbol(symbol, data_type='factor')
            
            # 加载数据
            symbol_data = self._load_factor_data_from_source(
                symbol, data_source, factors, start_date, end_date)
            
            if symbol_data is not None and not symbol_data.empty:
                if 'symbol' not in symbol_data.columns:
                    symbol_data['symbol'] = symbol
                all_data.append(symbol_data)
        
        # 合并数据
        if all_data:
            result_df = pd.concat(all_data, axis=0)
            
            # 应用预处理
            if 'factor' in self._preprocessors:
                result_df = self._preprocessors['factor'](result_df)
            
            # 缓存
            self._factor_data[cache_key] = result_df
            
            # 保存到磁盘
            if self.use_cache:
                try:
                    result_df.reset_index().to_csv(cache_file, index=False)
                except Exception as e:
                    logger.warning(f"保存因子数据缓存失败: {e}")
            
            return result_df
        else:
            empty_df = pd.DataFrame(columns=['symbol'] + factors)
            empty_df['date'] = pd.to_datetime([])
            empty_df.set_index('date', inplace=True)
            return empty_df
    
    def _get_data_source_for_symbol(self, symbol: str, data_type: str = 'price') -> Dict[str, Any]:
        """
        获取适合当前股票的数据源配置
        
        参数:
            symbol: 股票代码
            data_type: 数据类型，'price', 'fundamental', 'factor'
            
        返回:
            Dict: 数据源配置
        """
        # 如果有专门为该股票配置的数据源，优先使用
        symbol_specific_source = f"{symbol}_{data_type}"
        if symbol_specific_source in self._data_sources:
            return self._data_sources[symbol_specific_source]
        
        # 根据股票代码特征选择数据源
        if symbol.endswith('.SH') or symbol.endswith('.SZ'):
            # A股股票
            if f"a_stock_{data_type}" in self._data_sources:
                return self._data_sources[f"a_stock_{data_type}"]
        elif symbol.endswith('.HK'):
            # 港股
            if f"hk_stock_{data_type}" in self._data_sources:
                return self._data_sources[f"hk_stock_{data_type}"]
        elif '.' not in symbol:
            # 美股
            if f"us_stock_{data_type}" in self._data_sources:
                return self._data_sources[f"us_stock_{data_type}"]
        
        # 使用默认数据源
        if f"default_{data_type}" in self._data_sources:
            return self._data_sources[f"default_{data_type}"]
        
        # 如果没有任何匹配的数据源，返回空配置
        return {}
    
    def _load_price_data_from_source(self,
                                    symbol: str,
                                    data_source: Dict[str, Any],
                                    fields: List[str],
                                    start_date: str,
                                    end_date: str,
                                    frequency: str,
                                    adjust: str) -> pd.DataFrame:
        """
        从指定的数据源加载价格数据
        
        参数:
            symbol: 股票代码
            data_source: 数据源配置
            fields: 需要的字段
            start_date: 开始日期
            end_date: 结束日期
            frequency: 频率
            adjust: 价格调整方式
            
        返回:
            DataFrame: 价格数据
        """
        source_type = data_source.get('type', 'csv')
        
        if source_type == 'csv':
            # 从CSV文件加载
            file_path = data_source.get('path', '')
            if not file_path:
                file_path = os.path.join(self.data_root, 'price', f"{symbol}_{frequency}.csv")
            
            if not os.path.exists(file_path):
                return None
            
            try:
                df = pd.read_csv(file_path)
                
                # 处理日期列
                date_col = data_source.get('date_column', 'date')
                if date_col in df.columns:
                    df[date_col] = pd.to_datetime(df[date_col])
                    df.set_index(date_col, inplace=True)
                
                # 按日期过滤
                if start_date:
                    df = df[df.index >= start_date]
                if end_date:
                    df = df[df.index <= end_date]
                
                # 选择需要的字段
                available_fields = [f for f in fields if f in df.columns]
                if available_fields:
                    df = df[available_fields]
                
                # 处理价格调整
                if adjust != 'none' and 'adj_factor' in df.columns:
                    price_fields = [f for f in available_fields if f in ['open', 'high', 'low', 'close']]
                    if adjust == 'forward':
                        for field in price_fields:
                            df[field] = df[field] * df['adj_factor']
                    elif adjust == 'backward':
                        latest_factor = df['adj_factor'].iloc[-1]
                        for field in price_fields:
                            df[field] = df[field] * df['adj_factor'] / latest_factor
                
                return df
            except Exception as e:
                logger.error(f"从CSV加载价格数据失败: {e}")
                return None
        
        elif source_type == 'sql':
            # 从SQL数据库加载
            # (这里需要实现SQL连接和查询逻辑)
            pass
        
        elif source_type == 'api':
            # 从API加载
            # (这里需要实现API调用逻辑)
            pass
        
        return None
    
    def _load_fundamental_data_from_source(self,
                                         symbol: str,
                                         data_source: Dict[str, Any],
                                         fields: List[str],
                                         start_date: str,
                                         end_date: str,
                                         report_type: str) -> pd.DataFrame:
        """
        从指定的数据源加载基本面数据
        
        参数:
            symbol: 股票代码
            data_source: 数据源配置
            fields: 需要的字段
            start_date: 开始日期
            end_date: 结束日期
            report_type: 报告类型
            
        返回:
            DataFrame: 基本面数据
        """
        source_type = data_source.get('type', 'csv')
        
        if source_type == 'csv':
            # 从CSV文件加载
            file_path = data_source.get('path', '')
            if not file_path:
                file_path = os.path.join(self.data_root, 'fundamental', f"{symbol}_{report_type}.csv")
            
            if not os.path.exists(file_path):
                return None
            
            try:
                df = pd.read_csv(file_path)
                
                # 处理日期列
                date_col = data_source.get('date_column', 'date')
                if date_col in df.columns:
                    df[date_col] = pd.to_datetime(df[date_col])
                    df.set_index(date_col, inplace=True)
                
                # 按日期过滤
                if start_date:
                    df = df[df.index >= start_date]
                if end_date:
                    df = df[df.index <= end_date]
                
                # 选择需要的字段
                available_fields = [f for f in fields if f in df.columns]
                if available_fields:
                    df = df[available_fields]
                
                return df
            except Exception as e:
                logger.error(f"从CSV加载基本面数据失败: {e}")
                return None
        
        elif source_type == 'sql':
            # 从SQL数据库加载
            pass
        
        elif source_type == 'api':
            # 从API加载
            pass
        
        return None
    
    def _load_factor_data_from_source(self,
                                    symbol: str,
                                    data_source: Dict[str, Any],
                                    factors: List[str],
                                    start_date: str,
                                    end_date: str) -> pd.DataFrame:
        """
        从指定的数据源加载因子数据
        
        参数:
            symbol: 股票代码
            data_source: 数据源配置
            factors: 需要的因子
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            DataFrame: 因子数据
        """
        source_type = data_source.get('type', 'csv')
        
        if source_type == 'csv':
            # 从CSV文件加载
            file_path = data_source.get('path', '')
            if not file_path:
                file_path = os.path.join(self.data_root, 'factor', f"{symbol}_factors.csv")
            
            if not os.path.exists(file_path):
                return None
            
            try:
                df = pd.read_csv(file_path)
                
                # 处理日期列
                date_col = data_source.get('date_column', 'date')
                if date_col in df.columns:
                    df[date_col] = pd.to_datetime(df[date_col])
                    df.set_index(date_col, inplace=True)
                
                # 按日期过滤
                if start_date:
                    df = df[df.index >= start_date]
                if end_date:
                    df = df[df.index <= end_date]
                
                # 选择需要的因子
                available_factors = [f for f in factors if f in df.columns]
                if available_factors:
                    df = df[available_factors]
                
                return df
            except Exception as e:
                logger.error(f"从CSV加载因子数据失败: {e}")
                return None
        
        elif source_type == 'sql':
            # 从SQL数据库加载
            pass
        
        elif source_type == 'api':
            # 从API加载
            pass
        
        return None
    
    def clear_cache(self, cache_type: Optional[str] = None) -> None:
        """
        清除缓存
        
        参数:
            cache_type: 缓存类型，可选 'price', 'fundamental', 'factor', 'all'
        """
        if cache_type == 'price' or cache_type == 'all':
            self._price_data = {}
        
        if cache_type == 'fundamental' or cache_type == 'all':
            self._fundamental_data = {}
        
        if cache_type == 'factor' or cache_type == 'all':
            self._factor_data = {}
        
        if cache_type == 'all':
            self._calendar = None
            
            # 清除磁盘缓存
            if self.use_cache and os.path.exists(self.cache_dir):
                import shutil
                try:
                    shutil.rmtree(self.cache_dir)
                    os.makedirs(self.cache_dir, exist_ok=True)
                except Exception as e:
                    logger.error(f"清除磁盘缓存失败: {e}")

# 创建用于测试的数据生成函数
def generate_test_market_data(output_dir: str = "./data/price",
                           num_stocks: int = 50,
                           start_date: str = "2018-01-01",
                           end_date: str = "2022-12-31",
                           market: str = "SH",
                           seed: int = 42) -> None:
    """
    生成测试用的市场数据
    
    参数:
        output_dir: 输出目录
        num_stocks: 股票数量
        start_date: 开始日期
        end_date: 结束日期
        market: 市场代码
        seed: 随机种子
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置随机种子
    np.random.seed(seed)
    
    # 生成日期范围
    date_range = pd.date_range(start=start_date, end=end_date, freq='B')  # 'B' 表示工作日
    
    # 生成股票代码
    stock_codes = [f"{i+1:06d}.{market}" for i in range(num_stocks)]
    
    # 为每支股票生成数据
    for code in stock_codes:
        # 生成基础价格曲线
        base_price = np.random.uniform(10, 100)  # 基础价格
        daily_returns = np.random.normal(0.0005, 0.02, len(date_range))  # 日收益率
        
        # 计算价格序列
        price_series = [base_price]
        for ret in daily_returns:
            price_series.append(price_series[-1] * (1 + ret))
        price_series = price_series[1:]  # 移除初始基础价格
        
        # 生成OHLCV数据
        data = []
        for i, date in enumerate(date_range):
            close_price = price_series[i]
            
            # 生成开盘价、最高价、最低价
            daily_volatility = close_price * np.random.uniform(0.01, 0.05)
            open_price = close_price * (1 + np.random.normal(0, 0.01))
            high_price = max(open_price, close_price) + abs(np.random.normal(0, daily_volatility))
            low_price = min(open_price, close_price) - abs(np.random.normal(0, daily_volatility))
            
            # 确保 high >= open/close >= low
            high_price = max(high_price, open_price, close_price)
            low_price = min(low_price, open_price, close_price)
            
            # 生成成交量
            volume = int(np.random.normal(1e6, 5e5))
            volume = max(100, volume)  # 确保成交量为正
            
            # 添加到数据列表
            data.append({
                'date': date.strftime('%Y-%m-%d'),
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume,
                'adj_factor': 1.0  # 默认的复权因子
            })
        
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 添加调整因子变化
        # 模拟股票分红、送股等事件
        num_adjustments = np.random.randint(0, 5)  # 随机生成0-4次调整
        adj_dates = np.random.choice(range(len(df)), num_adjustments, replace=False)
        
        for adj_idx in sorted(adj_dates):
            adj_factor = np.random.uniform(0.5, 2.0)  # 随机调整因子
            df.loc[adj_idx:, 'adj_factor'] = df.loc[adj_idx:, 'adj_factor'] * adj_factor
        
        # 保存为CSV
        output_file = os.path.join(output_dir, f"{code}_daily.csv")
        df.to_csv(output_file, index=False)
        
    print(f"已生成 {num_stocks} 只股票的测试数据到目录 {output_dir}")
    
    # 生成交易日历
    calendar_df = pd.DataFrame({'date': date_range.strftime('%Y-%m-%d')})
    calendar_file = os.path.join(os.path.dirname(output_dir), 'calendar.csv')
    calendar_df.to_csv(calendar_file, index=False)
    print(f"已生成交易日历到 {calendar_file}") 