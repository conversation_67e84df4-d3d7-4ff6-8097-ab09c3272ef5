"""
回测性能分析模块

该模块提供了一系列用于分析和评估策略性能的工具函数和类：
1. 基础性能指标计算 - 如夏普比率、最大回撤、年化收益等
2. 高级性能分析 - 如多因子归因、风险贡献分析等
3. 性能可视化工具 - 用于绘制各类性能和风险图表
4. 性能报告生成 - 用于生成丰富的PDF和HTML性能报告

用法示例：
```python
from backtest.performance import (
    calculate_performance_metrics,
    plot_equity_curve,
    plot_drawdown,
    generate_performance_report
)

# 计算基础性能指标
metrics = calculate_performance_metrics(returns_series)

# 绘制权益曲线
plot_equity_curve(returns_series)

# 生成性能报告
report = generate_performance_report(returns_series, benchmark_returns)
```

高级归因分析用法：
```python
from backtest.performance.multifactor_attribution import (
    MultifactorAttribution,
    perform_multifactor_attribution,
    create_attribution_dashboard
)

# 执行多因子归因分析
attribution_result = perform_multifactor_attribution(
    returns=strategy_returns,
    factor_exposures=factor_data,
    benchmark_returns=benchmark_returns
)

# 创建归因分析仪表盘
dashboard = create_attribution_dashboard(
    returns=strategy_returns,
    factor_exposures=factor_data,
    benchmark_returns=benchmark_returns
)
```
"""

import pandas as pd
import numpy as np
import json
import logging
from typing import Dict, Any, List, Optional, Union

from src.backtest.performance.metrics import (
    calculate_returns_stats,
    calculate_risk_metrics,
    calculate_drawdown_stats,
    calculate_performance_metrics
)

try:
    from src.backtest.performance.visualization import (
        plot_equity_curve,
        plot_drawdown,
        plot_monthly_returns,
        plot_annual_returns,
        plot_rolling_sharpe,
        plot_rolling_volatility,
        plot_return_distribution,
        plot_underwater,
        create_performance_tearsheet,
        create_risk_tearsheet
    )
except ImportError:
    # 可视化模块可能不存在或依赖缺失
    logging.warning("性能可视化模块不可用，相关功能将被禁用")

try:
    from src.backtest.performance.reporting import (
        generate_performance_report,
        generate_risk_report,
        generate_attribution_report
    )
except ImportError:
    # 报告模块可能不存在或依赖缺失
    logging.warning("性能报告模块不可用，相关功能将被禁用")

try:
    from src.backtest.performance.multifactor_attribution import (
        MultifactorAttribution,
        perform_multifactor_attribution,
        perform_dynamic_attribution,
        visualize_attribution_results,
        compare_attribution_methods,
        create_attribution_dashboard
    )
except ImportError:
    # 归因分析模块可能不存在或依赖缺失
    logging.warning("多因子归因模块不可用，相关功能将被禁用")


class PerformanceAnalyzer:
    """性能分析器类，用于分析和评估回测结果"""
    
    def __init__(self, risk_free_rate: float = 0.03, benchmark_returns: pd.Series = None):
        """
        初始化性能分析器
        
        参数:
            risk_free_rate: 无风险利率，默认为3%
            benchmark_returns: 基准收益率序列，默认为None
        """
        self.risk_free_rate = risk_free_rate
        self.benchmark_returns = benchmark_returns
        self.logger = logging.getLogger(__name__)
    
    def analyze(self, results: pd.DataFrame) -> Dict[str, Any]:
        """
        分析回测结果并生成性能指标
        
        参数:
            results: 回测结果数据框，需要包含portfolio_value列
            
        返回:
            Dict[str, Any]: 性能指标字典
        """
        self.logger.info("开始分析回测结果...")
        
        if results is None or results.empty:
            self.logger.error("回测结果为空，无法分析")
            return {}
        
        try:
            # 确保结果数据格式正确
            if 'portfolio_value' not in results.columns:
                if 'equity' in results.columns:
                    results['portfolio_value'] = results['equity']
                elif 'total_value' in results.columns:
                    results['portfolio_value'] = results['total_value']
                else:
                    self.logger.error("结果数据缺少portfolio_value或equity或total_value列")
                    return {}
            
            # 计算每日收益率
            results['returns'] = results['portfolio_value'].pct_change()
            
            # 计算性能指标
            performance = {}
            
            # 基本信息
            initial_capital = results['portfolio_value'].iloc[0]
            final_capital = results['portfolio_value'].iloc[-1]
            performance['initial_capital'] = initial_capital
            performance['final_capital'] = final_capital
            performance['total_return'] = (final_capital - initial_capital) / initial_capital
            
            # 计算常用指标
            returns = results['returns'].dropna()
            
            # 使用metrics模块中的函数计算综合指标
            try:
                # 计算收益率统计
                returns_stats = calculate_returns_stats(returns)
                performance.update(returns_stats)
                
                # 计算风险指标
                risk_metrics = calculate_risk_metrics(returns, self.benchmark_returns, self.risk_free_rate)
                performance.update(risk_metrics)
                
                # 计算回撤统计
                drawdown_stats = calculate_drawdown_stats(returns)
                performance.update(drawdown_stats)
                
            except Exception as e:
                self.logger.warning(f"计算性能指标失败: {str(e)}")
            
            self.logger.info("回测结果分析完成")
            return performance
            
        except Exception as e:
            self.logger.error(f"分析回测结果时出错: {str(e)}")
            return {}
    
    def to_json(self, performance: Dict[str, Any], file_path: str = None) -> Optional[str]:
        """
        将性能指标转换为JSON格式
        
        参数:
            performance: 性能指标字典
            file_path: 保存JSON的文件路径，可选
            
        返回:
            Optional[str]: JSON字符串，如果指定了file_path则返回None
        """
        # 将numpy类型转换为Python原生类型
        cleaned_perf = {}
        for k, v in performance.items():
            if isinstance(v, (np.integer, np.floating)):
                cleaned_perf[k] = float(v)
            elif isinstance(v, np.ndarray):
                cleaned_perf[k] = v.tolist()
            else:
                cleaned_perf[k] = v
                
        json_str = json.dumps(cleaned_perf, indent=4)
        
        if file_path:
            try:
                with open(file_path, 'w') as f:
                    f.write(json_str)
                self.logger.info(f"性能指标已保存到: {file_path}")
                return None
            except Exception as e:
                self.logger.error(f"保存性能指标到文件失败: {str(e)}")
                
        return json_str

__all__ = [
    # 性能分析器类
    'PerformanceAnalyzer',
    
    # 基础性能指标计算
    'calculate_returns_stats',
    'calculate_risk_metrics',
    'calculate_drawdown_stats',
    'calculate_performance_metrics',
    
    # 性能可视化函数
    'plot_equity_curve',
    'plot_drawdown',
    'plot_monthly_returns',
    'plot_annual_returns',
    'plot_rolling_sharpe',
    'plot_rolling_volatility',
    'plot_return_distribution',
    'plot_underwater',
    'create_performance_tearsheet',
    'create_risk_tearsheet',
    
    # 报告生成函数
    'generate_performance_report',
    'generate_risk_report',
    'generate_attribution_report',
    
    # 多因子归因分析
    'MultifactorAttribution',
    'perform_multifactor_attribution',
    'perform_dynamic_attribution',
    'visualize_attribution_results',
    'compare_attribution_methods',
    'create_attribution_dashboard'
]
