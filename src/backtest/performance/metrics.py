"""
绩效指标计算模块
- 提供各种回测绩效指标计算功能
- 支持风险调整收益指标
- 包含最大回撤、夏普比率、索提诺比率等常用指标
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, Union, List, Tuple
import math

def calculate_returns_stats(returns: pd.Series) -> Dict[str, float]:
    """
    计算收益率基本统计量
    
    参数：
        returns: 收益率序列
        
    返回：
        Dict[str, float]: 统计量字典
    """
    if returns.empty:
        return {
            'total_return': 0.0,
            'annualized_return': 0.0,
            'annualized_volatility': 0.0,
            'daily_volatility': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0,
            'profit_loss_ratio': 0.0
        }
    
    # 计算基本统计量
    daily_returns = returns
    cumulative_returns = (1 + daily_returns).cumprod() - 1
    total_return = cumulative_returns.iloc[-1]
    
    # 计算年化收益率
    trading_days_per_year = 252
    years = len(daily_returns) / trading_days_per_year
    annualized_return = (1 + total_return) ** (1 / years) - 1 if years > 0 else 0
    
    # 计算波动率
    daily_volatility = daily_returns.std()
    annualized_volatility = daily_volatility * (trading_days_per_year ** 0.5)
    
    # 计算胜率
    win_rate = len(daily_returns[daily_returns > 0]) / len(daily_returns)
    
    # 计算盈亏比
    avg_profit = daily_returns[daily_returns > 0].mean() if len(daily_returns[daily_returns > 0]) > 0 else 0
    avg_loss = abs(daily_returns[daily_returns < 0].mean()) if len(daily_returns[daily_returns < 0]) > 0 else 1
    profit_loss_ratio = avg_profit / avg_loss if avg_loss > 0 else 0
    
    # 计算最大回撤
    cumulative_max = cumulative_returns.cummax()
    drawdown = (cumulative_returns - cumulative_max) / (1 + cumulative_max)
    max_drawdown = abs(drawdown.min())
    
    return {
        'total_return': total_return,
        'annualized_return': annualized_return,
        'annualized_volatility': annualized_volatility,
        'daily_volatility': daily_volatility,
        'win_rate': win_rate,
        'profit_loss_ratio': profit_loss_ratio,
        'max_drawdown': max_drawdown
    }

def calculate_risk_metrics(returns: pd.Series, 
                           benchmark_returns: Optional[pd.Series] = None,
                           risk_free_rate: float = 0.0) -> Dict[str, float]:
    """
    计算风险调整收益指标
    
    参数：
        returns: 收益率序列
        benchmark_returns: 基准收益率序列
        risk_free_rate: 无风险利率，日频率
        
    返回：
        Dict[str, float]: 风险指标字典
    """
    if returns.empty:
        return {
            'sharpe_ratio': 0.0,
            'sortino_ratio': 0.0,
            'calmar_ratio': 0.0,
            'information_ratio': 0.0,
            'downside_risk': 0.0,
            'upside_potential': 0.0,
            'beta': 0.0,
            'alpha': 0.0,
            'r_squared': 0.0
        }
    
    # 基本统计
    stats = calculate_returns_stats(returns)
    
    # 计算夏普比率
    excess_returns = returns - risk_free_rate
    sharpe_ratio = excess_returns.mean() / returns.std() * (252 ** 0.5) if returns.std() > 0 else 0
    
    # 计算索提诺比率
    downside_returns = returns[returns < 0]
    downside_risk = downside_returns.std() * (252 ** 0.5) if len(downside_returns) > 0 else 0
    sortino_ratio = excess_returns.mean() / downside_risk * (252 ** 0.5) if downside_risk > 0 else 0
    
    # 计算Calmar比率
    calmar_ratio = stats['annualized_return'] / stats['max_drawdown'] if stats['max_drawdown'] > 0 else 0
    
    # 计算上行捕获率
    upside_returns = returns[returns > 0]
    upside_potential = upside_returns.mean() * 252 if len(upside_returns) > 0 else 0
    
    # 如果有基准收益率，计算相对指标
    beta = 0.0
    alpha = 0.0
    r_squared = 0.0
    information_ratio = 0.0
    
    if benchmark_returns is not None and not benchmark_returns.empty:
        # 确保两个序列长度一致，对齐日期
        aligned_returns = returns.align(benchmark_returns, join='inner')
        if len(aligned_returns[0]) > 0:
            strategy_returns = aligned_returns[0]
            benchmark_returns_aligned = aligned_returns[1]
            
            # 计算Beta
            covariance = np.cov(strategy_returns, benchmark_returns_aligned)[0][1]
            benchmark_variance = np.var(benchmark_returns_aligned)
            beta = covariance / benchmark_variance if benchmark_variance > 0 else 0
            
            # 计算Alpha (Jensen's Alpha)
            risk_adjusted_benchmark_return = risk_free_rate + beta * (benchmark_returns_aligned.mean() - risk_free_rate)
            alpha = strategy_returns.mean() - risk_adjusted_benchmark_return
            alpha = alpha * 252  # 年化
            
            # 计算R方
            correlation = np.corrcoef(strategy_returns, benchmark_returns_aligned)[0, 1]
            r_squared = correlation ** 2
            
            # 计算信息比率
            tracking_error = (strategy_returns - benchmark_returns_aligned).std() * (252 ** 0.5)
            information_ratio = (strategy_returns.mean() - benchmark_returns_aligned.mean()) * 252 / tracking_error if tracking_error > 0 else 0
    
    return {
        'sharpe_ratio': sharpe_ratio,
        'sortino_ratio': sortino_ratio,
        'calmar_ratio': calmar_ratio,
        'information_ratio': information_ratio,
        'downside_risk': downside_risk,
        'upside_potential': upside_potential,
        'beta': beta,
        'alpha': alpha,
        'r_squared': r_squared
    }

def calculate_drawdown_stats(returns: pd.Series) -> Dict[str, Any]:
    """
    计算回撤统计
    
    参数：
        returns: 收益率序列
        
    返回：
        Dict[str, Any]: 回撤统计字典
    """
    if returns.empty:
        return {
            'max_drawdown': 0.0,
            'max_drawdown_duration': 0,
            'avg_drawdown': 0.0,
            'avg_drawdown_duration': 0,
            'drawdown_details': []
        }
    
    # 计算累积收益
    cumulative_returns = (1 + returns).cumprod()
    
    # 计算滚动最大值
    running_max = cumulative_returns.cummax()
    
    # 计算回撤
    drawdown = (cumulative_returns - running_max) / running_max
    
    # 最大回撤及时间点
    max_drawdown = abs(drawdown.min())
    max_drawdown_idx = drawdown.idxmin()
    
    # 回撤期分析
    is_in_drawdown = drawdown < 0
    drawdown_periods = []
    current_period = None
    
    for date, value in drawdown.items():
        if is_in_drawdown[date]:
            if current_period is None:
                current_period = {
                    'start_date': date,
                    'start_value': cumulative_returns[date],
                    'current_date': date,
                    'current_value': cumulative_returns[date],
                    'current_drawdown': value
                }
            else:
                current_period['current_date'] = date
                current_period['current_value'] = cumulative_returns[date]
                current_period['current_drawdown'] = value
        elif current_period is not None:
            # 回撤结束
            current_period['end_date'] = date
            current_period['end_value'] = cumulative_returns[date]
            current_period['max_drawdown'] = abs(min(drawdown[current_period['start_date']:date]))
            
            # 添加到回撤期列表
            drawdown_periods.append(current_period)
            current_period = None
    
    # 如果最后一个回撤期尚未结束
    if current_period is not None:
        current_period['end_date'] = drawdown.index[-1]
        current_period['end_value'] = cumulative_returns.iloc[-1]
        current_period['max_drawdown'] = abs(min(drawdown[current_period['start_date']:]))
        drawdown_periods.append(current_period)
    
    # 计算平均回撤和平均回撤持续时间
    if drawdown_periods:
        drawdown_values = [period['max_drawdown'] for period in drawdown_periods]
        avg_drawdown = sum(drawdown_values) / len(drawdown_values)
        
        # 计算回撤持续时间（交易日）
        for period in drawdown_periods:
            start_idx = drawdown.index.get_loc(period['start_date'])
            end_idx = drawdown.index.get_loc(period['end_date'])
            period['duration'] = end_idx - start_idx + 1
        
        durations = [period['duration'] for period in drawdown_periods]
        avg_drawdown_duration = sum(durations) / len(durations)
        max_drawdown_duration = max(durations)
    else:
        avg_drawdown = 0
        avg_drawdown_duration = 0
        max_drawdown_duration = 0
    
    return {
        'max_drawdown': max_drawdown,
        'max_drawdown_duration': max_drawdown_duration,
        'avg_drawdown': avg_drawdown,
        'avg_drawdown_duration': avg_drawdown_duration,
        'drawdown_details': drawdown_periods
    }

def calculate_streaks(returns: pd.Series) -> Dict[str, int]:
    """
    计算最大连续盈利/亏损交易次数
    
    参数：
        returns: 收益率序列
        
    返回：
        Dict[str, int]: 包含最大连续盈利/亏损交易次数的字典
    """
    if returns.empty:
        return {
            'max_winning_streak': 0,
            'max_losing_streak': 0,
            'current_winning_streak': 0,
            'current_losing_streak': 0
        }
    
    # 标记盈利/亏损交易
    win = returns > 0
    lose = returns < 0
    
    # 计算连续盈利/亏损次数
    win_streaks = []
    lose_streaks = []
    
    current_win_streak = 0
    current_lose_streak = 0
    
    for is_win, is_lose in zip(win, lose):
        if is_win:
            current_win_streak += 1
            if current_lose_streak > 0:
                lose_streaks.append(current_lose_streak)
                current_lose_streak = 0
        elif is_lose:
            current_lose_streak += 1
            if current_win_streak > 0:
                win_streaks.append(current_win_streak)
                current_win_streak = 0
        else:  # 收益为0
            # 收益为0的情况，结束当前连续计数
            if current_win_streak > 0:
                win_streaks.append(current_win_streak)
                current_win_streak = 0
            if current_lose_streak > 0:
                lose_streaks.append(current_lose_streak)
                current_lose_streak = 0
    
    # 处理最后一个连续序列
    if current_win_streak > 0:
        win_streaks.append(current_win_streak)
    if current_lose_streak > 0:
        lose_streaks.append(current_lose_streak)
    
    # 计算最大连续次数
    max_winning_streak = max(win_streaks) if win_streaks else 0
    max_losing_streak = max(lose_streaks) if lose_streaks else 0
    
    # 当前连续次数
    current_streak = 0
    is_winning_streak = False
    
    for i in range(len(returns) - 1, -1, -1):
        if current_streak == 0:
            if returns.iloc[i] > 0:
                current_streak = 1
                is_winning_streak = True
            elif returns.iloc[i] < 0:
                current_streak = 1
                is_winning_streak = False
            else:
                break
        else:
            if (is_winning_streak and returns.iloc[i] > 0) or \
               (not is_winning_streak and returns.iloc[i] < 0):
                current_streak += 1
            else:
                break
    
    current_winning_streak = current_streak if is_winning_streak else 0
    current_losing_streak = current_streak if not is_winning_streak else 0
    
    return {
        'max_winning_streak': max_winning_streak,
        'max_losing_streak': max_losing_streak,
        'current_winning_streak': current_winning_streak,
        'current_losing_streak': current_losing_streak
    }

def calculate_advanced_risk_metrics(returns: pd.Series) -> Dict[str, float]:
    """
    计算高级风险指标
    
    参数：
        returns: 收益率序列
        
    返回：
        Dict[str, float]: 高级风险指标字典
    """
    if returns.empty:
        return {
            'skewness': 0.0,
            'kurtosis': 0.0,
            'var_95': 0.0,
            'var_99': 0.0,
            'cvar_95': 0.0,
            'cvar_99': 0.0,
            'omega_ratio': 0.0
        }
    
    # 计算偏度和峰度
    skewness = returns.skew()
    kurtosis = returns.kurt()
    
    # 计算VaR (Value at Risk)
    var_95 = np.percentile(returns, 5)  # 95% VaR
    var_99 = np.percentile(returns, 1)  # 99% VaR
    
    # 计算CVaR (Conditional Value at Risk / Expected Shortfall)
    cvar_95 = returns[returns <= var_95].mean()
    cvar_99 = returns[returns <= var_99].mean()
    
    # 计算Omega比率（使用0作为阈值）
    threshold = 0
    returns_above = returns[returns > threshold]
    returns_below = returns[returns <= threshold]
    
    # 正收益和负收益的面积
    positive_area = returns_above.sum()
    negative_area = abs(returns_below.sum()) if len(returns_below) > 0 else 0
    
    omega_ratio = positive_area / negative_area if negative_area > 0 else float('inf')
    
    return {
        'skewness': skewness,
        'kurtosis': kurtosis,
        'var_95': var_95,
        'var_99': var_99,
        'cvar_95': cvar_95,
        'cvar_99': cvar_99,
        'omega_ratio': omega_ratio
    }

def calculate_return_to_risk_ratios(returns: pd.Series, risk_free_rate: float = 0.0) -> Dict[str, float]:
    """
    计算收益风险比率指标
    
    参数：
        returns: 收益率序列
        risk_free_rate: 无风险利率，日频率
        
    返回：
        Dict[str, float]: 收益风险比率指标字典
    """
    if returns.empty:
        return {
            'return_to_drawdown_ratio': 0.0,
            'ulcer_performance_index': 0.0,
            'pain_ratio': 0.0,
            'martin_ratio': 0.0,
            'tail_ratio': 0.0
        }
    
    # 基本统计
    stats = calculate_returns_stats(returns)
    drawdown_stats = calculate_drawdown_stats(returns)
    
    # 计算收益回撤比
    return_to_drawdown_ratio = stats['annualized_return'] / drawdown_stats['max_drawdown'] if drawdown_stats['max_drawdown'] > 0 else float('inf')
    
    # 计算Ulcer Index (UI)
    # UI衡量回撤的深度和持续时间
    equity_curve = (1 + returns).cumprod()
    peak = equity_curve.cummax()
    drawdown_pct = (equity_curve - peak) / peak
    squared_dd = np.square(drawdown_pct)
    ulcer_index = np.sqrt(np.mean(squared_dd))
    
    # 计算UPI (Ulcer Performance Index)
    excess_return = returns.mean() * 252 - risk_free_rate
    ulcer_performance_index = excess_return / ulcer_index if ulcer_index > 0 else 0
    
    # 计算Pain Ratio和Martin Ratio
    pain_index = abs(drawdown_pct.mean())
    pain_ratio = stats['annualized_return'] / pain_index if pain_index > 0 else 0
    martin_ratio = excess_return / ulcer_index if ulcer_index > 0 else 0
    
    # 计算Tail Ratio
    # Tail Ratio衡量右尾（收益）与左尾（损失）的比率
    tail_ratio = abs(np.percentile(returns, 95)) / abs(np.percentile(returns, 5)) \
                if abs(np.percentile(returns, 5)) > 0 else float('inf')
    
    return {
        'return_to_drawdown_ratio': return_to_drawdown_ratio,
        'ulcer_index': ulcer_index,
        'ulcer_performance_index': ulcer_performance_index,
        'pain_index': pain_index,
        'pain_ratio': pain_ratio,
        'martin_ratio': martin_ratio,
        'tail_ratio': tail_ratio
    }

def calculate_timing_metrics(returns: pd.Series, benchmark_returns: Optional[pd.Series] = None) -> Dict[str, float]:
    """
    计算市场择时指标
    
    参数：
        returns: 收益率序列
        benchmark_returns: 基准收益率序列
        
    返回：
        Dict[str, float]: 市场择时指标字典
    """
    if returns.empty or benchmark_returns is None or benchmark_returns.empty:
        return {
            'up_capture': 0.0,
            'down_capture': 0.0,
            'up_number_ratio': 0.0,
            'down_number_ratio': 0.0,
            'up_percentage': 0.0,
            'down_percentage': 0.0
        }
    
    # 对齐日期
    aligned_returns = returns.align(benchmark_returns, join='inner')
    if len(aligned_returns[0]) == 0:
        return {
            'up_capture': 0.0,
            'down_capture': 0.0,
            'up_number_ratio': 0.0,
            'down_number_ratio': 0.0,
            'up_percentage': 0.0,
            'down_percentage': 0.0
        }
    
    strategy_returns = aligned_returns[0]
    benchmark_returns_aligned = aligned_returns[1]
    
    # 上升市场和下跌市场的筛选
    up_market = benchmark_returns_aligned > 0
    down_market = benchmark_returns_aligned < 0
    
    # 上升市场捕获率
    if up_market.sum() > 0:
        up_capture = strategy_returns[up_market].mean() / benchmark_returns_aligned[up_market].mean()
    else:
        up_capture = 0.0
    
    # 下跌市场捕获率
    if down_market.sum() > 0:
        down_capture = strategy_returns[down_market].mean() / benchmark_returns_aligned[down_market].mean()
    else:
        down_capture = 0.0
    
    # 上涨次数比
    up_strategy = strategy_returns > 0
    down_strategy = strategy_returns < 0
    
    up_number_ratio = (up_strategy & up_market).sum() / up_market.sum() if up_market.sum() > 0 else 0
    down_number_ratio = (down_strategy & down_market).sum() / down_market.sum() if down_market.sum() > 0 else 0
    
    # 上涨/下跌市场的百分比
    up_percentage = up_market.mean()
    down_percentage = down_market.mean()
    
    return {
        'up_capture': up_capture,
        'down_capture': down_capture,
        'up_number_ratio': up_number_ratio,
        'down_number_ratio': down_number_ratio,
        'up_percentage': up_percentage,
        'down_percentage': down_percentage
    }

def calculate_high_watermark_stats(returns: pd.Series) -> Dict[str, Any]:
    """
    计算高水位线相关统计
    
    参数：
        returns: 收益率序列
        
    返回：
        Dict[str, Any]: 高水位线统计指标字典
    """
    if returns.empty:
        return {
            'time_to_recovery': 0,
            'recovery_percentage': 0.0,
            'underwater_percentage': 0.0,
            'peak_to_peak': [],
            'current_underwater_duration': 0,
            'high_watermark': 1.0
        }
    
    # 计算累积收益
    equity_curve = (1 + returns).cumprod()
    high_watermark = equity_curve.cummax()
    drawdown = (equity_curve - high_watermark) / high_watermark
    
    # 识别峰值点（高水位线点）
    hwm_idx = []
    for i in range(1, len(equity_curve)):
        if equity_curve.iloc[i] > high_watermark.iloc[i-1]:
            hwm_idx.append(i)
    
    # 计算从回撤恢复所需的时间
    recovery_periods = []
    total_periods = len(returns)
    
    for i in range(len(drawdown) - 1):
        if drawdown.iloc[i] < 0 and drawdown.iloc[i+1] >= 0:
            # 找到回撤结束点，回溯找到回撤开始点
            recovery_start = i
            for j in range(i, -1, -1):
                if drawdown.iloc[j] >= 0:
                    recovery_start = j + 1
                    break
            recovery_periods.append(i - recovery_start + 1)
    
    # 计算水下时间百分比
    underwater_days = (drawdown < 0).sum()
    underwater_percentage = underwater_days / total_periods if total_periods > 0 else 0
    
    # 计算当前水下持续时间
    current_underwater_duration = 0
    if drawdown.iloc[-1] < 0:
        for i in range(len(drawdown) - 1, -1, -1):
            if drawdown.iloc[i] < 0:
                current_underwater_duration += 1
            else:
                break
    
    # 计算峰值到峰值的周期
    peak_to_peak = []
    if len(hwm_idx) > 1:
        for i in range(1, len(hwm_idx)):
            peak_to_peak.append(hwm_idx[i] - hwm_idx[i-1])
    
    # 恢复时间（如果有回撤）
    time_to_recovery = np.mean(recovery_periods) if recovery_periods else 0
    
    # 恢复的百分比
    recovery_percentage = 1 - underwater_percentage
    
    return {
        'time_to_recovery': time_to_recovery,
        'recovery_percentage': recovery_percentage,
        'underwater_percentage': underwater_percentage,
        'peak_to_peak': peak_to_peak,
        'current_underwater_duration': current_underwater_duration,
        'high_watermark': high_watermark.iloc[-1]
    }

def calculate_performance_metrics(returns: pd.Series, 
                                 benchmark_returns: Optional[pd.Series] = None,
                                 risk_free_rate: float = 0.0) -> Dict[str, Any]:
    """
    计算全面的绩效指标
    
    参数：
        returns: 收益率序列
        benchmark_returns: 基准收益率序列
        risk_free_rate: 无风险利率，日频率
        
    返回：
        Dict[str, Any]: 绩效指标字典
    """
    # 计算各类指标
    returns_stats = calculate_returns_stats(returns)
    risk_metrics = calculate_risk_metrics(returns, benchmark_returns, risk_free_rate)
    drawdown_stats = calculate_drawdown_stats(returns)
    streak_stats = calculate_streaks(returns)
    advanced_risk = calculate_advanced_risk_metrics(returns)
    return_risk_ratios = calculate_return_to_risk_ratios(returns, risk_free_rate)
    hwm_stats = calculate_high_watermark_stats(returns)
    
    # 如果有基准收益率，添加市场择时指标
    timing_metrics = {}
    if benchmark_returns is not None and not benchmark_returns.empty:
        timing_metrics = calculate_timing_metrics(returns, benchmark_returns)
    
    # 合并结果
    metrics = {}
    metrics.update(returns_stats)
    metrics.update(risk_metrics)
    metrics.update(drawdown_stats)
    metrics.update(streak_stats)
    metrics.update(advanced_risk)
    metrics.update(return_risk_ratios)
    metrics.update(hwm_stats)
    metrics.update(timing_metrics)
    
    return metrics 