"""
绩效报告生成器模块（兼容性包装）
保持向后兼容性，内部使用统一报告
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any, List, Optional, Union, Tuple
import os
import datetime
from IPython.display import display, HTML
import io
import base64

from src.backtest.performance.metrics import (
    calculate_returns_stats,
    calculate_risk_metrics,
    calculate_drawdown_stats,
    calculate_performance_metrics
)

# {{ AURA-X: Add - 导入统一报告，消除重复代码. Approval: 寸止(ID:PerformanceReport整合). }}
from src.reports.backtest_report import UnifiedBacktestReport

class PerformanceReport:
    """绩效报告生成器类（兼容性包装）"""

    def __init__(self,
                 returns: pd.Series,
                 benchmark_returns: Optional[pd.Series] = None,
                 positions: Optional[pd.DataFrame] = None,
                 trades: Optional[pd.DataFrame] = None,
                 risk_free_rate: float = 0.0,
                 strategy_name: str = "策略回测"):
        """
        初始化绩效报告生成器

        参数:
            returns: 策略日收益率序列
            benchmark_returns: 基准日收益率序列
            positions: 持仓数据
            trades: 交易记录
            risk_free_rate: 无风险利率(日频率)
            strategy_name: 策略名称
        """
        # {{ AURA-X: Modify - 使用统一报告，保持向后兼容性. Approval: 寸止(ID:PerformanceReport整合). }}
        self.strategy_name = strategy_name
        self.returns = returns
        self.benchmark_returns = benchmark_returns
        self.positions = positions
        self.trades = trades
        self.risk_free_rate = risk_free_rate

        # 创建统一报告实例
        self._unified_report = UnifiedBacktestReport(
            returns=returns,
            benchmark_returns=benchmark_returns,
            positions=positions,
            trades=trades,
            risk_free_rate=risk_free_rate,
            strategy_name=strategy_name
        )

        # 保持兼容性的属性
        self.equity_curve = self._unified_report.equity_curve
        self.benchmark_curve = self._unified_report.benchmark_curve
        self.metrics = self._unified_report.metrics

        # 设置绘图风格
        sns.set_style('whitegrid')
        plt.rcParams['figure.figsize'] = (12, 8)
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
        plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号
    
    def plot_equity_curve(self, figsize: Tuple[int, int] = (12, 6)) -> plt.Figure:
        """
        绘制净值曲线（兼容性方法）

        参数:
            figsize: 图表尺寸

        返回:
            matplotlib Figure对象
        """
        # {{ AURA-X: Modify - 委托给统一报告，消除重复的图表生成代码. Approval: 寸止(ID:PerformanceReport整合). }}
        charts = self._unified_report.generate_charts()
        return charts.get('equity_curve', plt.figure())
    
    def plot_drawdowns(self, top_n: int = 5, figsize: Tuple[int, int] = (12, 6)) -> plt.Figure:
        """
        绘制回撤曲线（兼容性方法）

        参数:
            top_n: 显示前N个最大回撤
            figsize: 图表尺寸

        返回:
            matplotlib Figure对象
        """
        # {{ AURA-X: Modify - 委托给统一报告，消除重复的回撤图表代码. Approval: 寸止(ID:PerformanceReport整合). }}
        charts = self._unified_report.generate_charts()
        return charts.get('drawdown', plt.figure())
    
    def plot_monthly_returns(self, figsize: Tuple[int, int] = (12, 8)) -> plt.Figure:
        """
        绘制月度收益热力图（兼容性方法）

        参数:
            figsize: 图表尺寸

        返回:
            matplotlib Figure对象
        """
        # {{ AURA-X: Modify - 委托给统一报告，消除重复的月度收益图表代码. Approval: 寸止(ID:PerformanceReport整合). }}
        charts = self._unified_report.generate_charts()
        return charts.get('monthly_returns', plt.figure())
        
        # 格式化月份名称
        month_names = ['1月', '2月', '3月', '4月', '5月', '6月', 
                       '7月', '8月', '9月', '10月', '11月', '12月', '年度收益']
        
        # 创建热力图
        sns.heatmap(
            pivot_table_with_yearly,
            ax=ax,
            annot=True,
            fmt=".2%",
            cmap="RdYlGn",
            center=0,
            linewidths=1,
            cbar=True,
            cbar_kws={"label": "月度收益率"},
            xticklabels=month_names
        )
        
        ax.set_title('月度收益率热力图 (%)', fontsize=15)
        plt.tight_layout()
        
        return fig
    
    def plot_rolling_stats(self, window: int = 60, figsize: Tuple[int, int] = (15, 12)) -> plt.Figure:
        """
        绘制滚动统计指标
        
        参数:
            window: 滚动窗口大小(交易日)
            figsize: 图表尺寸
            
        返回:
            matplotlib Figure对象
        """
        returns = self.returns
        
        # 计算滚动指标
        rolling_sharpe = returns.rolling(window).apply(
            lambda x: (x.mean() - self.risk_free_rate) / x.std() * (252 ** 0.5) if x.std() != 0 else 0
        )
        
        rolling_vol = returns.rolling(window).std() * (252 ** 0.5)
        
        # 创建图表
        fig, axes = plt.subplots(2, 1, figsize=figsize, sharex=True)
        
        # 滚动夏普比率
        axes[0].plot(rolling_sharpe, color='blue', linewidth=2)
        axes[0].axhline(y=self.metrics['sharpe_ratio'], color='red', linestyle='--', 
                        label=f'总体夏普: {self.metrics["sharpe_ratio"]:.2f}')
        axes[0].set_title('滚动夏普比率', fontsize=14)
        axes[0].legend()
        axes[0].grid(True)
        
        # 滚动波动率
        axes[1].plot(rolling_vol, color='green', linewidth=2)
        axes[1].axhline(y=self.metrics['annualized_volatility'], color='red', linestyle='--',
                        label=f'总体波动率: {self.metrics["annualized_volatility"]:.2%}')
        axes[1].set_title('滚动年化波动率', fontsize=14)
        axes[1].legend()
        axes[1].yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))
        axes[1].grid(True)
        
        plt.tight_layout()
        return fig
        
    def create_performance_table(self) -> pd.DataFrame:
        """
        创建性能指标表格
        
        返回:
            pandas DataFrame: 格式化的性能指标表格
        """
        metrics = self.metrics
        
        # 创建基础性能指标表格
        basic_metrics = {
            '指标': [
                '总收益率', '年化收益率', '年化波动率', '夏普比率', '索提诺比率',
                '最大回撤', '最大回撤持续期(天)', 'Calmar比率', '胜率', '盈亏比'
            ],
            '值': [
                f"{metrics['total_return']:.2%}",
                f"{metrics['annualized_return']:.2%}",
                f"{metrics['annualized_volatility']:.2%}",
                f"{metrics['sharpe_ratio']:.2f}",
                f"{metrics['sortino_ratio']:.2f}",
                f"{metrics['max_drawdown']:.2%}",
                f"{metrics['max_drawdown_duration']}",
                f"{metrics['calmar_ratio']:.2f}",
                f"{metrics['win_rate']:.2%}",
                f"{metrics['profit_loss_ratio']:.2f}"
            ]
        }
        
        # 添加高级风险指标
        risk_metrics = {
            '指标': [
                '偏度', '峰度', '95% VaR', '95% CVaR', 'Omega比率',
                '水下时间百分比', '平均恢复时间', '平均回撤',
                '最大连续盈利', '最大连续亏损'
            ],
            '值': [
                f"{metrics.get('skewness', 0):.2f}",
                f"{metrics.get('kurtosis', 0):.2f}",
                f"{metrics.get('var_95', 0):.2%}",
                f"{metrics.get('cvar_95', 0):.2%}",
                f"{metrics.get('omega_ratio', 0):.2f}",
                f"{metrics.get('underwater_percentage', 0):.2%}",
                f"{metrics.get('time_to_recovery', 0):.1f}",
                f"{metrics.get('avg_drawdown', 0):.2%}",
                f"{metrics.get('max_winning_streak', 0)}",
                f"{metrics.get('max_losing_streak', 0)}"
            ]
        }
        
        # 添加收益风险比率指标
        risk_return_metrics = {
            '指标': [
                '收益回撤比', 'Ulcer指数', 'Pain指数', 'Martin比率', 'Tail比率'
            ],
            '值': [
                f"{metrics.get('return_to_drawdown_ratio', 0):.2f}",
                f"{metrics.get('ulcer_index', 0):.4f}",
                f"{metrics.get('pain_index', 0):.4f}",
                f"{metrics.get('martin_ratio', 0):.2f}",
                f"{metrics.get('tail_ratio', 0):.2f}"
            ]
        }
        
        # 如果有基准收益率，添加相对指标
        if self.benchmark_returns is not None:
            relative_metrics = {
                '指标': ['Alpha', 'Beta', 'Information Ratio', 'R平方'],
                '值': [
                    f"{metrics['alpha']:.2%}",
                    f"{metrics['beta']:.2f}",
                    f"{metrics['information_ratio']:.2f}",
                    f"{metrics['r_squared']:.2f}"
                ]
            }
            
            # 添加市场择时指标
            timing_metrics = {
                '指标': ['上升市场捕获率', '下跌市场捕获率', '上涨次数比', '下跌次数比'],
                '值': [
                    f"{metrics.get('up_capture', 0):.2f}",
                    f"{metrics.get('down_capture', 0):.2f}",
                    f"{metrics.get('up_number_ratio', 0):.2f}",
                    f"{metrics.get('down_number_ratio', 0):.2f}"
                ]
            }
            
            # 将所有指标合并到一个DataFrame
            table_data = {
                '基础指标': pd.DataFrame(basic_metrics),
                '风险指标': pd.DataFrame(risk_metrics),
                '收益风险比率': pd.DataFrame(risk_return_metrics),
                '相对指标': pd.DataFrame(relative_metrics),
                '市场择时': pd.DataFrame(timing_metrics)
            }
        else:
            # 无基准的情况，只包含绝对指标
            table_data = {
                '基础指标': pd.DataFrame(basic_metrics),
                '风险指标': pd.DataFrame(risk_metrics),
                '收益风险比率': pd.DataFrame(risk_return_metrics)
            }
        
        # 使用多级索引组织表格
        result_tables = []
        for section, df in table_data.items():
            df = df.copy()
            df['分类'] = section
            result_tables.append(df)
        
        table = pd.concat(result_tables)
        table = table.set_index(['分类', '指标'])['值'].unstack(level=0)
        
        # 重置索引以便于显示
        table = table.reset_index()
        
        return table
    
    def generate_report(self, output_dir: Optional[str] = None,
                       filename: Optional[str] = None,
                       format: str = 'html') -> Optional[str]:
        """
        生成完整报告（兼容性方法）

        参数:
            output_dir: 输出目录
            filename: 输出文件名
            format: 输出格式 ('html', 'pdf')

        返回:
            输出文件路径或HTML字符串
        """
        # {{ AURA-X: Modify - 委托给统一报告，消除重复的报告生成代码. Approval: 寸止(ID:PerformanceReport整合). }}
        if format not in ['html', 'pdf']:
            raise ValueError("输出格式必须为 'html' 或 'pdf'")

        # 如果未指定输出目录，返回HTML内容
        if output_dir is None:
            return self._unified_report.generate_html_report()

        # 生成文件路径
        if filename is None:
            filename = f"{self.strategy_name}_report.{format}"

        output_path = os.path.join(output_dir, filename)

        # 使用统一报告导出
        from src.reports.base_report import ReportFormat
        report_format = ReportFormat.HTML if format == 'html' else ReportFormat.PDF

        return self._unified_report.export_report(output_path, report_format)
        
        # 如果未指定输出目录，则直接返回HTML内容
        if output_dir is None:
            return html_content
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 默认文件名
        if filename is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.strategy_name.replace(' ', '_')}_{timestamp}"
        
        # 写入HTML文件
        html_path = os.path.join(output_dir, f"{filename}.html")
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        # 如果需要PDF格式
        if format == 'pdf':
            try:
                from weasyprint import HTML
                pdf_path = os.path.join(output_dir, f"{filename}.pdf")
                HTML(string=html_content).write_pdf(pdf_path)
                return pdf_path
            except ImportError:
                print("警告: 未安装WeasyPrint库，无法生成PDF报告。请使用 pip install weasyprint 安装。")
                return html_path
        
        return html_path
    
    def _create_html_report(self, equity_fig, drawdown_fig, monthly_fig, rolling_fig, perf_table) -> str:
        """
        创建HTML格式的报告（兼容性方法）

        参数:
            equity_fig: 净值曲线图
            drawdown_fig: 回撤曲线图
            monthly_fig: 月度收益图
            rolling_fig: 滚动统计图
            perf_table: 性能指标表格
            
        返回:
            str: HTML报告内容
        """
        # 将图表转换为base64编码
        def fig_to_base64(fig):
            buf = io.BytesIO()
            fig.savefig(buf, format='png', dpi=120)
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)  # 释放内存
            return img_str
        
        equity_base64 = fig_to_base64(equity_fig)
        drawdown_base64 = fig_to_base64(drawdown_fig)
        monthly_base64 = fig_to_base64(monthly_fig)
        rolling_base64 = fig_to_base64(rolling_fig)
        
        # HTML模板
        html_template = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{self.strategy_name} - 回测报告</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 20px;
                    color: #333;
                }}
                h1, h2 {{
                    color: #2c3e50;
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                    padding-bottom: 10px;
                    border-bottom: 1px solid #eee;
                }}
                .section {{
                    margin-bottom: 40px;
                }}
                .chart {{
                    text-align: center;
                    margin: 20px 0;
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                }}
                th, td {{
                    border: 1px solid #ddd;
                    padding: 12px;
                    text-align: left;
                }}
                th {{
                    background-color: #f8f9fa;
                }}
                tr:nth-child(even) {{
                    background-color: #f9f9f9;
                }}
                .positive {{
                    color: #28a745;
                }}
                .negative {{
                    color: #dc3545;
                }}
                .footer {{
                    margin-top: 50px;
                    padding-top: 20px;
                    border-top: 1px solid #eee;
                    text-align: center;
                    font-size: 0.8em;
                    color: #777;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{self.strategy_name} - 回测报告</h1>
                <p>回测期间: {self.returns.index[0].strftime('%Y-%m-%d')} 至 {self.returns.index[-1].strftime('%Y-%m-%d')}</p>
            </div>
            
            <div class="section">
                <h2>策略表现摘要</h2>
                <table>
                    <tr>
                        <th>指标</th>
                        <th>值</th>
                    </tr>
                    {''.join(f"<tr><td>{row['指标']}</td><td>{row['值']}</td></tr>" for _, row in perf_table.iterrows())}
                </table>
            </div>
            
            <div class="section">
                <h2>净值曲线</h2>
                <div class="chart">
                    <img src="data:image/png;base64,{equity_base64}" width="100%">
                </div>
            </div>
            
            <div class="section">
                <h2>回撤分析</h2>
                <div class="chart">
                    <img src="data:image/png;base64,{drawdown_base64}" width="100%">
                </div>
            </div>
            
            <div class="section">
                <h2>月度收益热力图</h2>
                <div class="chart">
                    <img src="data:image/png;base64,{monthly_base64}" width="100%">
                </div>
            </div>
            
            <div class="section">
                <h2>滚动指标分析</h2>
                <div class="chart">
                    <img src="data:image/png;base64,{rolling_base64}" width="100%">
                </div>
            </div>
            
            <div class="footer">
                <p>报告生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>量化交易策略回测平台</p>
            </div>
        </body>
        </html>
        """
        
        return html_template

def generate_backtest_report(backtest_results: Dict[str, Any],
                            output_dir: Optional[str] = None,
                            filename: Optional[str] = None,
                            format: str = 'html') -> Optional[str]:
    """
    从回测结果生成报告的便捷函数
    
    参数:
        backtest_results: 回测结果字典
        output_dir: 输出目录
        filename: 输出文件名
        format: 输出格式 ('html', 'pdf')
        
    返回:
        str: 输出文件路径或HTML字符串
    """
    if 'returns' not in backtest_results:
        raise ValueError("回测结果中必须包含 'returns' 键")
    
    report = PerformanceReport(
        returns=backtest_results['returns'],
        benchmark_returns=backtest_results.get('benchmark_returns'),
        positions=backtest_results.get('positions'),
        trades=backtest_results.get('trades'),
        risk_free_rate=backtest_results.get('risk_free_rate', 0.0),
        strategy_name=backtest_results.get('strategy_name', '策略回测')
    )
    
    return report.generate_report(
        output_dir=output_dir,
        filename=filename,
        format=format
    ) 