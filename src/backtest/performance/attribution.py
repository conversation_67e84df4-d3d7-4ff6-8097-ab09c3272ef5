"""
绩效归因分析模块

该模块提供多种归因分析方法，用于解释投资组合的表现来源：
1. 因子归因 - 分析各因子对策略表现的贡献
2. 行业归因 - 分析各行业对策略表现的贡献
3. 风格归因 - 分析不同投资风格对策略表现的贡献
4. 时间序列归因 - 分析绩效的时间贡献分解

用法示例：
```python
from src.backtest.performance.attribution import (
    perform_factor_attribution,
    perform_sector_attribution,
    perform_style_attribution,
    perform_time_series_attribution
)

# 因子归因
factor_contrib = perform_factor_attribution(
    returns=strategy_returns,
    factor_exposures=factor_data,
    factor_returns=factor_returns
)

# 行业归因
sector_contrib = perform_sector_attribution(
    returns=strategy_returns,
    holdings=portfolio_holdings,
    sector_mapping=sector_data
)
```
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
import statsmodels.api as sm


def perform_factor_attribution(
    returns: pd.Series,
    factor_exposures: pd.DataFrame,
    factor_returns: Optional[pd.DataFrame] = None,
    risk_free_rate: Optional[pd.Series] = None
) -> Dict[str, Any]:
    """
    执行基于因子模型的绩效归因分析
    
    参数:
        returns: 策略日收益率序列
        factor_exposures: 因子暴露数据框，索引为日期，列为因子
        factor_returns: 因子收益率数据框，索引为日期，列为因子。如果为None，则从收益率和因子暴露回归估计
        risk_free_rate: 无风险利率序列，索引为日期。如果为None，则假设为0
        
    返回:
        Dict[str, Any]: 归因分析结果字典，包含:
            - factor_contributions: 各因子贡献
            - specific_returns: 特质收益率
            - factor_returns: 因子收益率
            - total_attribution: 总归因明细
            - r_squared: 模型解释力
    """
    # 准备数据
    if risk_free_rate is None:
        excess_returns = returns
    else:
        # 确保索引对齐
        aligned_rf = risk_free_rate.reindex(returns.index)
        excess_returns = returns - aligned_rf
    
    # 确保因子暴露数据与收益率数据的日期对齐
    common_dates = excess_returns.index.intersection(factor_exposures.index)
    if len(common_dates) == 0:
        raise ValueError("收益率数据与因子暴露数据没有共同的日期")
    
    excess_returns = excess_returns.loc[common_dates]
    factor_exposures = factor_exposures.loc[common_dates]
    
    # 如果未提供因子收益率，通过横截面回归估计
    if factor_returns is None:
        factor_returns_estimated = pd.DataFrame(index=common_dates, columns=factor_exposures.columns)
        
        for date in common_dates:
            # 准备当日数据
            y = excess_returns[date]
            X = factor_exposures.loc[date]
            
            # 添加常数项
            X_with_const = sm.add_constant(X)
            
            # 进行回归
            model = sm.OLS(y, X_with_const)
            results = model.fit()
            
            # 保存因子收益率
            factor_returns_estimated.loc[date] = results.params[1:]  # 跳过常数项
        
        factor_returns = factor_returns_estimated
    else:
        # 确保因子收益率与其他数据的日期对齐
        factor_returns = factor_returns.reindex(common_dates)
    
    # 计算每个因子的贡献
    factor_contributions = pd.DataFrame(index=common_dates, columns=factor_exposures.columns)
    for factor in factor_exposures.columns:
        factor_contributions[factor] = factor_exposures[factor] * factor_returns[factor]
    
    # 计算特质收益率（策略收益率减去所有因子贡献）
    total_factor_contribution = factor_contributions.sum(axis=1)
    specific_returns = excess_returns - total_factor_contribution
    
    # 计算累积贡献
    cumulative_factor_contributions = factor_contributions.cumsum()
    cumulative_specific_returns = specific_returns.cumsum()
    
    # 计算每个因子对总收益的贡献比例
    total_return = excess_returns.sum()
    factor_contribution_pct = {}
    for factor in factor_exposures.columns:
        factor_contribution_pct[factor] = factor_contributions[factor].sum() / total_return if total_return != 0 else 0
    
    # 特质收益贡献百分比
    specific_contribution_pct = specific_returns.sum() / total_return if total_return != 0 else 0
    
    # 计算模型的解释力（R平方）
    explained_variance = total_factor_contribution.var()
    total_variance = excess_returns.var()
    r_squared = explained_variance / total_variance if total_variance != 0 else 0
    
    # 整合归因结果
    attribution_summary = {
        'factor_contributions': factor_contributions,
        'factor_contribution_pct': factor_contribution_pct,
        'specific_returns': specific_returns,
        'specific_contribution_pct': specific_contribution_pct,
        'factor_returns': factor_returns,
        'cumulative_factor_contributions': cumulative_factor_contributions,
        'cumulative_specific_returns': cumulative_specific_returns,
        'r_squared': r_squared
    }
    
    return attribution_summary


def perform_sector_attribution(
    returns: pd.Series,
    holdings: pd.DataFrame,
    sector_mapping: pd.DataFrame,
    benchmark_holdings: Optional[pd.DataFrame] = None
) -> Dict[str, Any]:
    """
    执行基于行业的绩效归因分析
    
    参数:
        returns: 策略日收益率序列
        holdings: 持仓数据框，索引为日期，列为资产，值为权重
        sector_mapping: 行业映射数据框，索引为资产，列包含'sector'字段
        benchmark_holdings: 基准持仓数据框，格式同holdings。如果提供，将计算相对于基准的超额贡献
        
    返回:
        Dict[str, Any]: 归因分析结果字典，包含:
            - sector_contributions: 各行业贡献
            - allocation_effect: 资产配置效应
            - selection_effect: 个股选择效应
            - interaction_effect: 交互效应
            - total_attribution: 总归因明细
    """
    # 确保数据对齐
    common_dates = returns.index.intersection(holdings.index)
    if len(common_dates) == 0:
        raise ValueError("收益率数据与持仓数据没有共同的日期")
    
    returns = returns.loc[common_dates]
    holdings = holdings.loc[common_dates]
    
    # 准备行业分类数据
    all_assets = holdings.columns.tolist()
    if not all(asset in sector_mapping.index for asset in all_assets):
        missing_assets = [asset for asset in all_assets if asset not in sector_mapping.index]
        raise ValueError(f"以下资产在行业映射中缺失: {missing_assets}")
    
    # 获取所有行业
    sectors = sector_mapping['sector'].unique()
    
    # 初始化结果
    sector_contributions = pd.DataFrame(index=common_dates, columns=sectors, dtype=float)
    sector_contributions.fillna(0, inplace=True)
    
    # 如果提供了基准持仓，初始化BF分解的效应
    allocation_effect = pd.DataFrame(index=common_dates, columns=sectors, dtype=float)
    selection_effect = pd.DataFrame(index=common_dates, columns=sectors, dtype=float)
    interaction_effect = pd.DataFrame(index=common_dates, columns=sectors, dtype=float)
    
    if benchmark_holdings is not None:
        benchmark_holdings = benchmark_holdings.reindex(common_dates)
        allocation_effect.fillna(0, inplace=True)
        selection_effect.fillna(0, inplace=True)
        interaction_effect.fillna(0, inplace=True)
    
    # 执行行业归因分析
    for date in common_dates:
        # 获取当日持仓
        portfolio_weights = holdings.loc[date]
        
        # 计算每个行业的贡献
        for sector in sectors:
            # 获取该行业的所有资产
            sector_assets = sector_mapping[sector_mapping['sector'] == sector].index
            
            # 计算该行业在投资组合中的权重
            sector_weight = portfolio_weights[sector_assets].sum()
            
            # 如果该行业有权重，计算其贡献
            if sector_weight > 0:
                # 当日收益率（假设我们有资产的日收益率数据）
                # 这里需要补充资产收益率数据的获取逻辑
                # asset_returns = ...
                
                # 计算行业贡献
                # sector_contributions.loc[date, sector] = sector_weight * sector_return
                pass
    
    # 整合归因结果
    attribution_summary = {
        'sector_contributions': sector_contributions
    }
    
    # 如果进行了BF分解，添加相关结果
    if benchmark_holdings is not None:
        attribution_summary.update({
            'allocation_effect': allocation_effect,
            'selection_effect': selection_effect,
            'interaction_effect': interaction_effect
        })
    
    return attribution_summary


def perform_style_attribution(
    returns: pd.Series,
    style_exposures: pd.DataFrame,
    style_returns: Optional[pd.DataFrame] = None
) -> Dict[str, Any]:
    """
    执行基于投资风格的绩效归因分析
    
    参数:
        returns: 策略日收益率序列
        style_exposures: 风格暴露数据框，索引为日期，列为风格因子（如价值、成长、规模等）
        style_returns: 风格因子收益率数据框，索引为日期，列为风格因子。如果为None，则从收益率和风格暴露回归估计
        
    返回:
        Dict[str, Any]: 归因分析结果字典，包含:
            - style_contributions: 各风格因子贡献
            - specific_returns: 特质收益率
            - total_attribution: 总归因明细
    """
    # 其实风格归因本质上是因子归因的一种，可以复用因子归因的方法
    return perform_factor_attribution(
        returns=returns,
        factor_exposures=style_exposures,
        factor_returns=style_returns
    )


def perform_time_series_attribution(
    returns: pd.Series,
    benchmark_returns: Optional[pd.Series] = None,
    window: str = 'M'
) -> Dict[str, Any]:
    """
    执行时间序列归因分析，分解不同时间段的贡献
    
    参数:
        returns: 策略日收益率序列
        benchmark_returns: 基准日收益率序列
        window: 时间窗口，如'M'表示月度分解，'Q'表示季度分解，'Y'表示年度分解
        
    返回:
        Dict[str, Any]: 归因分析结果字典，包含:
            - period_contributions: 各时间段贡献
            - excess_contributions: 超额贡献（如果提供了基准）
    """
    # 重采样到指定时间窗口
    period_returns = returns.resample(window).apply(lambda x: (1 + x).prod() - 1)
    
    # 计算累积贡献
    cumulative_contribution = (1 + period_returns).cumprod() - 1
    
    # 如果有基准，计算超额贡献
    excess_contributions = None
    if benchmark_returns is not None:
        # 确保基准收益率与策略收益率日期对齐
        aligned_benchmark = benchmark_returns.reindex(returns.index)
        
        # 重采样基准收益率
        period_benchmark = aligned_benchmark.resample(window).apply(lambda x: (1 + x).prod() - 1)
        
        # 计算超额收益
        excess_returns = period_returns - period_benchmark
        
        # 计算累积超额贡献
        excess_contributions = excess_returns.cumsum()
    
    return {
        'period_contributions': period_returns,
        'cumulative_contribution': cumulative_contribution,
        'excess_contributions': excess_contributions
    }


def plot_factor_attribution(
    attribution_result: Dict[str, Any],
    figsize: Tuple[int, int] = (12, 8),
    title: str = "因子归因分析",
    top_n: int = None
) -> plt.Figure:
    """
    可视化因子归因分析结果
    
    参数:
        attribution_result: 因子归因分析结果，来自perform_factor_attribution函数
        figsize: 图表尺寸
        title: 图表标题
        top_n: 显示贡献最大的前N个因子，如果为None则显示全部
        
    返回:
        matplotlib Figure对象
    """
    import matplotlib.pyplot as plt
    import seaborn as sns
    
    # 准备数据
    factor_contrib_pct = attribution_result['factor_contribution_pct']
    specific_contrib_pct = attribution_result['specific_contribution_pct']
    
    # 转换为Series
    contrib_series = pd.Series(factor_contrib_pct)
    
    # 添加特质贡献
    contrib_series['特质收益'] = specific_contrib_pct
    
    # 如果指定了top_n，选择贡献最大的因子
    if top_n is not None and top_n < len(contrib_series):
        top_factors = contrib_series.abs().sort_values(ascending=False).head(top_n)
        contrib_series = contrib_series[top_factors.index]
    
    # 按贡献绝对值排序
    contrib_series = contrib_series.reindex(contrib_series.abs().sort_values(ascending=False).index)
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 设置颜色
    colors = ['green' if x >= 0 else 'red' for x in contrib_series]
    
    # 绘制水平条形图
    contrib_series.plot(kind='barh', ax=ax, color=colors)
    
    # 添加数值标签
    for i, v in enumerate(contrib_series):
        ax.text(v + (0.01 if v >= 0 else -0.01), 
                i, 
                f'{v:.2%}', 
                va='center',
                ha='left' if v >= 0 else 'right',
                fontweight='bold')
    
    # 设置图表样式
    ax.set_title(title, fontsize=15)
    ax.set_xlabel('贡献占比')
    ax.axvline(x=0, color='black', linestyle='-', alpha=0.3)
    ax.grid(axis='x', alpha=0.3)
    
    # 添加R方值
    r_squared = attribution_result['r_squared']
    ax.annotate(f'模型解释力 (R²): {r_squared:.2%}', 
               xy=(0.02, 0.02), 
               xycoords='figure fraction',
               fontsize=12)
    
    plt.tight_layout()
    return fig


def plot_cumulative_attribution(
    attribution_result: Dict[str, Any],
    figsize: Tuple[int, int] = (12, 8),
    title: str = "累积因子贡献",
    top_n: int = 5
) -> plt.Figure:
    """
    可视化累积因子贡献
    
    参数:
        attribution_result: 归因分析结果
        figsize: 图表尺寸
        title: 图表标题
        top_n: 显示贡献最大的前N个因子
        
    返回:
        matplotlib Figure对象
    """
    import matplotlib.pyplot as plt
    import seaborn as sns
    
    # 准备数据
    cum_factor_contrib = attribution_result['cumulative_factor_contributions']
    cum_specific = attribution_result['cumulative_specific_returns']
    
    # 选择贡献最大的因子
    total_contrib = cum_factor_contrib.iloc[-1].abs()
    top_factors = total_contrib.nlargest(top_n).index
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 绘制累积因子贡献
    for factor in top_factors:
        ax.plot(cum_factor_contrib.index, cum_factor_contrib[factor], 
                label=factor, linewidth=2)
    
    # 绘制特质收益累积贡献
    ax.plot(cum_specific.index, cum_specific, 
            label='特质收益', linewidth=2, linestyle='--')
    
    # 绘制总累积收益
    total_return = cum_factor_contrib.sum(axis=1) + cum_specific
    ax.plot(total_return.index, total_return, 
            label='总收益', linewidth=3, color='black')
    
    # 设置图表样式
    ax.set_title(title, fontsize=15)
    ax.set_ylabel('累积贡献')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    return fig


def plot_sector_attribution(
    attribution_result: Dict[str, Any],
    figsize: Tuple[int, int] = (12, 8),
    title: str = "行业归因分析"
) -> plt.Figure:
    """
    可视化行业归因分析结果
    
    参数:
        attribution_result: 行业归因分析结果，来自perform_sector_attribution函数
        figsize: 图表尺寸
        title: 图表标题
        
    返回:
        matplotlib Figure对象
    """
    import matplotlib.pyplot as plt
    import seaborn as sns
    
    # 准备数据
    sector_contrib = attribution_result['sector_contributions']
    
    # 计算每个行业的总贡献
    total_sector_contrib = sector_contrib.sum()
    
    # 按贡献绝对值排序
    total_sector_contrib = total_sector_contrib.reindex(
        total_sector_contrib.abs().sort_values(ascending=False).index
    )
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 设置颜色
    colors = ['green' if x >= 0 else 'red' for x in total_sector_contrib]
    
    # 绘制水平条形图
    total_sector_contrib.plot(kind='barh', ax=ax, color=colors)
    
    # 添加数值标签
    for i, v in enumerate(total_sector_contrib):
        ax.text(v + (0.01 if v >= 0 else -0.01), 
                i, 
                f'{v:.2%}', 
                va='center',
                ha='left' if v >= 0 else 'right',
                fontweight='bold')
    
    # 设置图表样式
    ax.set_title(title, fontsize=15)
    ax.set_xlabel('贡献占比')
    ax.axvline(x=0, color='black', linestyle='-', alpha=0.3)
    ax.grid(axis='x', alpha=0.3)
    
    plt.tight_layout()
    return fig


def plot_brinson_attribution(
    attribution_result: Dict[str, Any],
    figsize: Tuple[int, int] = (14, 10),
    title: str = "Brinson归因分析"
) -> plt.Figure:
    """
    可视化Brinson-Fachler归因分析结果
    
    参数:
        attribution_result: 行业归因分析结果，包含allocation_effect和selection_effect
        figsize: 图表尺寸
        title: 图表标题
        
    返回:
        matplotlib Figure对象
    """
    import matplotlib.pyplot as plt
    import seaborn as sns
    
    # 检查是否包含BF分解结果
    if not all(k in attribution_result for k in ['allocation_effect', 'selection_effect', 'interaction_effect']):
        raise ValueError("归因结果中缺少Brinson-Fachler分解效应")
    
    # 准备数据
    allocation = attribution_result['allocation_effect'].sum()
    selection = attribution_result['selection_effect'].sum()
    interaction = attribution_result['interaction_effect'].sum()
    
    # 合并数据
    effects = pd.DataFrame({
        '资产配置效应': allocation,
        '个股选择效应': selection,
        '交互效应': interaction
    })
    
    # 按总效应排序
    total_effect = effects.sum(axis=1)
    effects = effects.reindex(total_effect.abs().sort_values(ascending=False).index)
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 绘制堆叠条形图
    effects.plot(kind='barh', stacked=True, ax=ax, 
                 color=['#1f77b4', '#ff7f0e', '#2ca02c'])
    
    # 添加总效应标签
    for i, v in enumerate(total_effect):
        ax.text(v + 0.01, i, f'{v:.2%}', va='center', fontweight='bold')
    
    # 设置图表样式
    ax.set_title(title, fontsize=15)
    ax.set_xlabel('贡献占比')
    ax.axvline(x=0, color='black', linestyle='-', alpha=0.3)
    ax.grid(axis='x', alpha=0.3)
    ax.legend(loc='lower right')
    
    # 添加效应总计
    total_allocation = allocation.sum()
    total_selection = selection.sum()
    total_interaction = interaction.sum()
    total_all = total_allocation + total_selection + total_interaction
    
    summary_text = (
        f"资产配置效应: {total_allocation:.2%}\n"
        f"个股选择效应: {total_selection:.2%}\n"
        f"交互效应: {total_interaction:.2%}\n"
        f"总超额收益: {total_all:.2%}"
    )
    
    ax.annotate(summary_text, 
               xy=(0.02, 0.02), 
               xycoords='figure fraction',
               fontsize=12)
    
    plt.tight_layout()
    return fig 