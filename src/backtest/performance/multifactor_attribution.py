"""
多因子绩效归因分析模块

该模块提供高级的多因子归因分析功能，用于深入分析投资组合表现的来源：
1. 多因子归因 - 基于多因子模型分析各因子对策略表现的贡献
2. 动态归因 - 分析因子贡献随时间的变化
3. 风险预算归因 - 将收益归因与风险贡献相结合
4. 跨期分解 - 将归因结果分解为选择和分配效应

用法示例：
```python
from src.backtest.performance.multifactor_attribution import (
    MultifactorAttribution,
    perform_multifactor_attribution,
    perform_dynamic_attribution
)

# 创建多因子归因分析器
attributor = MultifactorAttribution(
    returns=strategy_returns,
    benchmark_returns=benchmark_returns,
    factor_exposures=factor_data,
    risk_model=risk_model
)

# 执行归因分析
attribution_result = attributor.run_attribution()

# 或使用便捷函数
attribution_result = perform_multifactor_attribution(
    returns=strategy_returns,
    benchmark_returns=benchmark_returns,
    factor_exposures=factor_data
)
```
"""

import numpy as np
import pandas as pd
import statsmodels.api as sm
from sklearn.linear_model import LinearRegression, Lasso, Ridge, ElasticNet
from statsmodels.regression.rolling import RollingOLS
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass, field
from scipy import stats
from datetime import datetime
import warnings


class MultifactorAttribution:
    """
    多因子归因分析类

    该类实现了基于多因子模型的归因分析，支持多种归因方法、
    时变归因和风险调整归因等高级分析功能。
    """

    def __init__(self, 
                 returns: pd.Series,
                 factor_exposures: pd.DataFrame,
                 benchmark_returns: Optional[pd.Series] = None,
                 factor_returns: Optional[pd.DataFrame] = None,
                 risk_model: Optional[Dict[str, pd.DataFrame]] = None,
                 risk_free_rate: Optional[pd.Series] = None,
                 method: str = "linear",
                 window: int = 252,
                 min_periods: int = 60,
                 regularization: Optional[Dict[str, Any]] = None):
        """
        初始化多因子归因分析器

        参数:
            returns: 投资组合或策略的收益率序列
            factor_exposures: 因子暴露数据，DataFrame格式，每行是一个日期，每列是一个因子
            benchmark_returns: 基准的收益率序列，如果提供则执行相对归因分析
            factor_returns: 因子收益率数据，如果不提供则从数据中估计
            risk_model: 风险模型数据，包含因子协方差矩阵和特异风险
            risk_free_rate: 无风险利率序列
            method: 归因方法，可选"linear", "lasso", "ridge", "wls", "gls"
            window: 滚动窗口大小，用于时变归因分析
            min_periods: 滚动分析的最小样本量
            regularization: 正则化参数，如果使用lasso或ridge方法
        """
        # 存储输入数据
        self.returns = returns
        self.factor_exposures = factor_exposures
        self.benchmark_returns = benchmark_returns
        self.factor_returns = factor_returns
        self.risk_model = risk_model
        self.risk_free_rate = risk_free_rate
        
        # 设置方法参数
        self.method = method
        self.window = window
        self.min_periods = min_periods
        self.regularization = regularization or {}
        
        # 归因结果存储
        self.attribution_result = {}
        self.rolling_attribution = {}
        self.risk_attribution = {}
        
        # 预处理数据
        self._preprocess_data()
        
    def _preprocess_data(self):
        """
        预处理数据：对齐日期，计算超额收益等
        """
        # 验证输入数据
        if self.returns is None or len(self.returns) == 0:
            raise ValueError("收益率数据为空")
            
        if self.factor_exposures is None or len(self.factor_exposures) == 0:
            raise ValueError("因子暴露数据为空")
        
        # 检查日期索引是否对齐，如果不对齐则取交集
        common_index = self.returns.index.intersection(self.factor_exposures.index)
        
        if len(common_index) == 0:
            # 尝试自动转换索引格式
            try:
                # 尝试将收益率索引转换为与因子暴露相同的格式
                if isinstance(self.returns.index, pd.DatetimeIndex) and not isinstance(self.factor_exposures.index, pd.DatetimeIndex):
                    # 将DatetimeIndex转换为字符串
                    self.returns.index = self.returns.index.strftime('%Y%m%d')
                elif not isinstance(self.returns.index, pd.DatetimeIndex) and isinstance(self.factor_exposures.index, pd.DatetimeIndex):
                    # 将字符串转换为DatetimeIndex
                    self.returns.index = pd.DatetimeIndex(self.returns.index)
                
                # 重新检查交集
                common_index = self.returns.index.intersection(self.factor_exposures.index)
                
                if len(common_index) == 0:
                    print("警告: 收益率和因子暴露数据的日期索引没有交集")
                    # 创建一些假数据用于测试
                    if len(self.returns) > 10 and len(self.factor_exposures) > 10:
                        # 使用较短的数据集的索引
                        if len(self.returns) < len(self.factor_exposures):
                            # 使用收益率的索引并将因子暴露重新索引
                            self.factor_exposures = self.factor_exposures.reset_index(drop=True)
                            self.factor_exposures.index = self.returns.index[:len(self.factor_exposures)]
                        else:
                            # 使用因子暴露的索引并将收益率重新索引
                            self.returns = self.returns.reset_index(drop=True)
                            self.returns.index = self.factor_exposures.index[:len(self.returns)]
                        
                        # 重新检查交集
                        common_index = self.returns.index.intersection(self.factor_exposures.index)
                    else:
                        raise ValueError("返回序列和因子暴露数据没有共同的日期索引")
            except Exception as e:
                print(f"预处理数据时出错: {str(e)}")
                raise ValueError("返回序列和因子暴露数据没有共同的日期索引")
                
        # 对齐数据
        self.returns = self.returns.loc[common_index]
        self.factor_exposures = self.factor_exposures.loc[common_index]
        
        # 处理基准收益率
        if self.benchmark_returns is not None:
            if len(self.benchmark_returns) > 0:
                # 如果基准收益率不为空，则也需要对齐
                benchmark_common_index = common_index.intersection(self.benchmark_returns.index)
                
                if len(benchmark_common_index) == 0:
                    # 尝试自动转换基准收益率的索引格式
                    try:
                        if isinstance(common_index, pd.DatetimeIndex) and not isinstance(self.benchmark_returns.index, pd.DatetimeIndex):
                            # 将基准收益率的索引转换为DatetimeIndex
                            self.benchmark_returns.index = pd.DatetimeIndex(self.benchmark_returns.index)
                        elif not isinstance(common_index, pd.DatetimeIndex) and isinstance(self.benchmark_returns.index, pd.DatetimeIndex):
                            # 将基准收益率的索引转换为字符串
                            self.benchmark_returns.index = self.benchmark_returns.index.strftime('%Y%m%d')
                            
                        # 重新检查交集
                        benchmark_common_index = common_index.intersection(self.benchmark_returns.index)
                        
                        if len(benchmark_common_index) == 0:
                            print("警告: 基准收益率与其他数据的日期索引没有交集，将使用零收益率作为基准")
                            # 创建一个与returns相同长度的零Series作为基准
                            self.benchmark_returns = pd.Series(0, index=common_index)
                            benchmark_common_index = common_index
                    except Exception:
                        print("警告: 无法对齐基准收益率，将使用零收益率作为基准")
                        # 创建一个与returns相同长度的零Series作为基准
                        self.benchmark_returns = pd.Series(0, index=common_index)
                        benchmark_common_index = common_index
                
                # 再次对齐所有数据
                self.returns = self.returns.loc[benchmark_common_index]
                self.factor_exposures = self.factor_exposures.loc[benchmark_common_index]
                self.benchmark_returns = self.benchmark_returns.loc[benchmark_common_index]
                
                # 计算超额收益率
                self.excess_returns = self.returns - self.benchmark_returns
            else:
                # 如果基准收益率为空DataFrame/Series，则直接使用收益率作为超额收益率
                self.excess_returns = self.returns
        else:
            # 如果没有提供基准收益率，则直接使用收益率作为超额收益率
            self.excess_returns = self.returns
            
        # 处理无风险利率
        if self.risk_free_rate is not None:
            if len(self.risk_free_rate) > 0:
                # 对齐无风险利率
                rf_common_index = self.returns.index.intersection(self.risk_free_rate.index)
                
                if len(rf_common_index) == 0:
                    print("警告: 无风险利率与其他数据的日期索引没有交集，将使用零无风险利率")
                    # 创建一个与returns相同长度的零Series作为无风险利率
                    self.risk_free_rate = pd.Series(0, index=self.returns.index)
                else:
                    # 对齐无风险利率
                    self.risk_free_rate = self.risk_free_rate.loc[rf_common_index]
                    
                    # 重新对齐其他数据
                    self.returns = self.returns.loc[rf_common_index]
                    self.factor_exposures = self.factor_exposures.loc[rf_common_index]
                    if self.benchmark_returns is not None:
                        self.benchmark_returns = self.benchmark_returns.loc[rf_common_index]
                    self.excess_returns = self.excess_returns.loc[rf_common_index]
            else:
                # 如果无风险利率为空DataFrame/Series，则使用零无风险利率
                self.risk_free_rate = pd.Series(0, index=self.returns.index)
    
    def run_attribution(self) -> Dict[str, Any]:
        """
        执行因子归因分析，根据指定方法计算因子贡献
        
        返回:
            Dict: 包含归因分析结果的字典
        """
        # 预处理数据
        self._preprocess_data()
        
        # 检查数据是否有效
        if len(self.returns) == 0 or len(self.factor_exposures) == 0:
            raise ValueError("处理后的收益率或因子暴露数据为空")
            
        # 检查并处理NaN值
        returns_na = self.returns.isna().sum()
        exposures_na = self.factor_exposures.isna().sum().sum()
        
        if returns_na > 0 or exposures_na > 0:
            # 有NaN值，输出警告
            import warnings
            warnings.warn(f"输入数据包含NaN值：收益率中有{returns_na}个，因子暴露中有{exposures_na}个，结果可能不可靠")
            
            # 过滤出所有非NaN的行
            mask = ~self.returns.isna()
            for col in self.factor_exposures.columns:
                mask = mask & ~self.factor_exposures[col].isna()
                
            # 重新过滤数据
            if mask.sum() == 0:
                raise ValueError("过滤NaN值后没有剩余的有效数据")
                
            self.returns = self.returns[mask]
            self.excess_returns = self.excess_returns[mask]
            self.factor_exposures = self.factor_exposures.loc[mask]
            
            if self.benchmark_returns is not None:
                self.benchmark_returns = self.benchmark_returns[mask]
                
            if self.risk_free_rate is not None:
                self.risk_free_rate = self.risk_free_rate[mask]
        
        # 根据方法执行归因分析
        if self.method.lower() == 'linear':
            self._run_linear_attribution()
        elif self.method.lower() in ['lasso', 'ridge', 'elasticnet']:
            # 如果未提供正则化参数，使用默认参数
            if self.regularization is None:
                if self.method.lower() == 'lasso':
                    self.regularization = {'alpha': 0.01, 'l1_ratio': 1.0}
                elif self.method.lower() == 'ridge':
                    self.regularization = {'alpha': 0.01, 'l1_ratio': 0.0}
                else:  # elasticnet
                    self.regularization = {'alpha': 0.01, 'l1_ratio': 0.5}
            
            # 运行正则化归因
            self._run_regularized_attribution(
                alpha=self.regularization.get('alpha', 0.01),
                l1_ratio=self.regularization.get('l1_ratio', 0.5)
            )
        elif self.method.lower() in ['wls', 'weighted']:
            self._run_weighted_attribution(weighted_type=self.method.lower())
        else:
            raise ValueError(f"不支持的归因方法: {self.method}")
            
        # 计算模型诊断信息
        self._compute_model_diagnostics()
        
        # 构建并返回结果
        attribution_result = {
            'factor_loadings': self.factor_loadings,
            'factor_returns': self.factor_returns,
            'specific_returns': self.specific_returns,
            'factor_contribution': self.factor_contribution,
            'factor_contribution_pct': self.factor_contribution_pct,
            'specific_contribution_pct': self.specific_contribution_pct,
            'cumulative_factor_contribution': self.cumulative_factor_contribution,
            'cumulative_specific_contribution': self.cumulative_specific_contribution,
            'attribution_summary': self.attribution_summary,
            'model_results': self.model_results,
            'model_diagnostics': self.model_diagnostics
        }
        
        return attribution_result
    
    def _run_linear_attribution(self):
        """
        使用普通线性回归执行归因分析
        """
        # 准备数据
        y = self.excess_returns
        X = self.factor_exposures
        
        # 进行数据清洗 - 处理NaN值
        mask = ~y.isna()
        y_clean = y[mask]
        X_clean = X.loc[mask]
        
        # 检查数据是否有效
        if len(y_clean) == 0 or X_clean.shape[0] == 0:
            # 如果数据为空，创建空结果
            self.attribution_result = {
                'factor_loadings': self.factor_exposures,
                'factor_returns': pd.Series(np.nan, index=list(self.factor_exposures.columns) + ['const']),
                'specific_returns': pd.Series(np.nan, index=self.excess_returns.index),
                'factor_contribution': pd.DataFrame(np.nan, index=self.excess_returns.index, columns=self.factor_exposures.columns),
                'factor_contribution_pct': pd.Series(0, index=self.factor_exposures.columns),
                'specific_contribution_pct': 0,
                'cumulative_factor_contribution': pd.DataFrame(np.nan, index=self.excess_returns.index, columns=self.factor_exposures.columns),
                'cumulative_specific_contribution': pd.Series(np.nan, index=self.excess_returns.index),
                'attribution_summary': pd.Series({
                    'Total Excess Return': 0.0,
                    'Total Factor Contribution': 0.0,
                    'Total Specific Return': 0.0,
                    'Difference': 0.0
                }),
                'model_results': None,
                'model_diagnostics': {
                    'r_squared': np.nan,
                    'adj_r_squared': np.nan,
                    'f_statistic': np.nan,
                    'p_value': np.nan,
                    'aic': np.nan,
                    'bic': np.nan,
                    'condition_number': np.nan,
                    't_values': pd.Series(np.nan, index=list(self.factor_exposures.columns) + ['const']),
                    'p_values': pd.Series(np.nan, index=list(self.factor_exposures.columns) + ['const']),
                    'std_errors': pd.Series(np.nan, index=list(self.factor_exposures.columns) + ['const']),
                    'confidence_intervals': pd.DataFrame(np.nan, 
                                                       index=list(self.factor_exposures.columns) + ['const'], 
                                                       columns=['lower', 'upper'])
                }
            }
            return
            
        try:
            # 添加常数项
            X_with_const = sm.add_constant(X_clean)
            
            # 拟合线性模型
            model = sm.OLS(y_clean, X_with_const)
            results = model.fit()
            
            # 提取因子收益率（系数）
            factor_returns = results.params[1:]  # 排除常数项
            intercept = results.params[0]  # 常数项
            
            # 计算预测值和残差
            y_pred = results.predict(X_with_const)
            specific_returns = pd.Series(np.nan, index=self.excess_returns.index)
            specific_returns.loc[mask] = y_clean - y_pred
            
            # 计算各因子的贡献
            factor_contribution = pd.DataFrame(np.nan, index=self.excess_returns.index, 
                                              columns=self.factor_exposures.columns)
            
            for factor in self.factor_exposures.columns:
                # 计算每个时间点的因子贡献
                factor_values = pd.Series(np.nan, index=self.excess_returns.index)
                factor_values.loc[mask] = X_clean[factor] * factor_returns[factor]
                factor_contribution[factor] = factor_values
                
            # 添加截距项贡献
            alpha_contribution = pd.Series(np.nan, index=self.excess_returns.index)
            alpha_contribution.loc[mask] = intercept
            
            # 计算总因子贡献和特异贡献
            total_factor_contribution = factor_contribution.sum(axis=1).sum()
            total_excess_return = y_clean.sum()
            total_specific_return = specific_returns.sum()
            
            # 验证：总超额收益 = 总因子贡献 + 总特异贡献
            attribution_check = pd.Series({
                'Total Excess Return': total_excess_return,
                'Total Factor Contribution': total_factor_contribution + intercept * len(y_clean),
                'Total Specific Return': total_specific_return,
                'Difference': total_excess_return - ((total_factor_contribution + intercept * len(y_clean)) + total_specific_return)
            })
            
            # 计算因子贡献百分比
            if total_excess_return != 0:
                factor_contribution_pct = factor_contribution.sum().apply(lambda x: x / total_excess_return if not np.isnan(x) else 0)
                specific_contribution_pct = total_specific_return / total_excess_return
            else:
                factor_contribution_pct = pd.Series(0, index=factor_contribution.columns)
                specific_contribution_pct = 0
            
            # 计算累积贡献
            cumulative_factor_contrib = factor_contribution.cumsum()
            cumulative_specific_contrib = specific_returns.cumsum()
            
            # 计算模型诊断信息
            model_diagnostics = {
                'r_squared': results.rsquared,
                'adj_r_squared': results.rsquared_adj,
                'f_statistic': results.fvalue,
                'p_value': results.f_pvalue,
                'aic': results.aic,
                'bic': results.bic,
                'condition_number': results.condition_number,
                't_values': results.tvalues,
                'p_values': results.pvalues,
                'std_errors': results.bse,
                'confidence_intervals': pd.DataFrame({
                    'lower': results.conf_int()[0],
                    'upper': results.conf_int()[1]
                })
            }
            
            # 存储结果
            self.attribution_result = {
                'factor_loadings': self.factor_exposures,
                'factor_returns': pd.Series(list(factor_returns) + [intercept], 
                                          index=list(self.factor_exposures.columns) + ['Alpha']),
                'specific_returns': specific_returns,
                'factor_contribution': factor_contribution,
                'factor_contribution_pct': factor_contribution_pct,
                'specific_contribution_pct': specific_contribution_pct,
                'cumulative_factor_contribution': cumulative_factor_contrib,
                'cumulative_specific_contribution': cumulative_specific_contrib,
                'attribution_summary': attribution_check,
                'model_results': results,
                'model_diagnostics': model_diagnostics
            }
        except Exception as e:
            # 创建空结果或默认结果
            self.attribution_result = {
                'factor_loadings': self.factor_exposures,
                'factor_returns': pd.Series(np.nan, index=list(self.factor_exposures.columns) + ['Alpha']),
                'specific_returns': pd.Series(np.nan, index=self.excess_returns.index),
                'factor_contribution': pd.DataFrame(np.nan, index=self.excess_returns.index, columns=self.factor_exposures.columns),
                'factor_contribution_pct': pd.Series(0, index=self.factor_exposures.columns),
                'specific_contribution_pct': 0,
                'cumulative_factor_contribution': pd.DataFrame(np.nan, index=self.excess_returns.index, columns=self.factor_exposures.columns),
                'cumulative_specific_contribution': pd.Series(np.nan, index=self.excess_returns.index),
                'attribution_summary': pd.Series({
                    'Total Excess Return': 0.0,
                    'Total Factor Contribution': 0.0,
                    'Total Specific Return': 0.0,
                    'Difference': 0.0
                }),
                'model_results': None,
                'model_diagnostics': {
                    'r_squared': np.nan,
                    'adj_r_squared': np.nan,
                    'f_statistic': np.nan,
                    'p_value': np.nan,
                    'aic': np.nan,
                    'bic': np.nan,
                    'condition_number': np.nan,
                    't_values': pd.Series(np.nan, index=list(self.factor_exposures.columns) + ['Alpha']),
                    'p_values': pd.Series(np.nan, index=list(self.factor_exposures.columns) + ['Alpha']),
                    'std_errors': pd.Series(np.nan, index=list(self.factor_exposures.columns) + ['Alpha']),
                    'confidence_intervals': pd.DataFrame(np.nan, 
                                                       index=list(self.factor_exposures.columns) + ['Alpha'], 
                                                       columns=['lower', 'upper'])
                },
                'error': str(e)
            }
    
    def _run_regularized_attribution(self, alpha: float = 0.01, l1_ratio: float = 0.5):
        """
        使用正则化方法执行归因分析（Lasso或Ridge）
        
        参数:
            alpha: 正则化强度
            l1_ratio: Lasso (l1)比例，仅适用于elastic net
            
        返回:
            Dict: 归因结果
        """
        # 准备数据
        y = self.excess_returns
        X = self.factor_exposures
        
        # 处理NaN值
        # 填充X中的NaN值，使用前向填充然后后向填充
        X_clean = X.fillna(method='ffill').fillna(method='bfill')
        
        # 对于y中的NaN值，我们需要去除对应的行，因为这些是目标变量
        mask = ~y.isna()
        y_clean = y[mask]
        X_clean = X_clean.loc[mask]
        
        # 检查数据是否有效
        if len(y_clean) == 0 or X_clean.shape[0] == 0:
            raise ValueError("清洗NaN值后数据为空，无法进行归因分析")
        
        # 根据方法选择模型
        if self.method == "lasso":
            model = Lasso(alpha=alpha, fit_intercept=True, max_iter=10000)
        elif self.method == "ridge":
            model = Ridge(alpha=alpha, fit_intercept=True)
        elif self.method == "elastic_net":
            model = ElasticNet(alpha=alpha, l1_ratio=l1_ratio, fit_intercept=True, max_iter=10000)
        else:
            raise ValueError(f"不支持的正则化方法: {self.method}")

        try:
            # 拟合模型
            model.fit(X_clean, y_clean)
            
            # 获取因子贡献
            feature_names = X.columns
            
            # 系数（因子收益率）
            factor_returns = pd.Series(model.coef_, index=feature_names)
            
            # 添加截距（Alpha）
            alpha_value = model.intercept_
            
            # 因子暴露矩阵
            factor_exposures = X
            
            # 计算每个时点的因子贡献
            factor_contribution = pd.DataFrame(index=X.index)
            for factor in feature_names:
                factor_contribution[factor] = factor_exposures[factor] * factor_returns[factor]
                
            # 添加截距贡献
            factor_contribution['Alpha'] = alpha_value
            
            # 计算预测收益率
            X_with_const = sm.add_constant(X_clean)
            params = np.append(model.intercept_, model.coef_)
            predicted = X_with_const.dot(params)
            
            # 计算每个时点的特异收益率
            residuals = pd.Series(y_clean - predicted, index=X_clean.index)
            
            # 计算模型统计量
            r2 = model.score(X_clean, y_clean)
            
            # 计算每个因子的相对贡献
            total_contrib = factor_contribution.sum()
            percent_contrib = total_contrib / total_contrib.sum()
            
            # 修改factor_contribution的索引与excess_returns一致
            factor_contribution = factor_contribution.reindex(index=self.excess_returns.index)
            
            # 修改residuals的索引与excess_returns一致
            full_residuals = pd.Series(index=self.excess_returns.index)
            full_residuals.loc[residuals.index] = residuals
            
            # 汇总结果
            return {
                'model': model,
                'factor_returns': factor_returns,
                'alpha': alpha_value,
                'factor_contribution': factor_contribution,
                'specific_returns': full_residuals,
                'r_squared': r2,
                'predicted': pd.Series(predicted, index=X_clean.index)
            }
        except Exception as e:
            raise ValueError(f"正则化归因分析失败: {str(e)}")
    
    def _run_weighted_attribution(self, weighted_type: str = "wls"):
        """
        使用加权最小二乘法执行归因分析
        
        参数:
            weighted_type: 加权类型 ("wls"为加权最小二乘, "gls"为广义最小二乘)
        """
        # 准备数据
        y = self.excess_returns
        X = sm.add_constant(self.factor_exposures)
        
        if weighted_type == "wls":
            # 使用收益率的绝对值的倒数作为权重 - 给较小的波动更大的权重
            weights = 1.0 / (np.abs(y) + 1e-6)
            model = sm.WLS(y, X, weights=weights)
        elif weighted_type == "gls":
            # 如果有风险模型，使用其协方差矩阵的逆作为权重
            if self.risk_model is not None and 'factor_covariance' in self.risk_model:
                cov_matrix = self.risk_model['factor_covariance']
                # 确保协方差矩阵可逆
                try:
                    weights = np.linalg.inv(cov_matrix.values)
                    model = sm.GLS(y, X, sigma=weights)
                except:
                    warnings.warn("协方差矩阵不可逆，回退到WLS")
                    weights = 1.0 / (np.abs(y) + 1e-6)
                    model = sm.WLS(y, X, weights=weights)
            else:
                warnings.warn("没有提供风险模型，回退到WLS")
                weights = 1.0 / (np.abs(y) + 1e-6)
                model = sm.WLS(y, X, weights=weights)
        else:
            raise ValueError(f"不支持的加权类型: {weighted_type}")
        
        # 拟合模型
        results = model.fit()
        
        # 提取因子收益率（系数）
        factor_returns = results.params
        
        # 计算特异收益
        specific_returns = results.resid
        
        # 计算各因子的贡献
        factor_contribution = pd.DataFrame(index=y.index)
        for factor in self.factor_exposures.columns:
            if factor in factor_returns:
                # 计算每个时间点的因子贡献
                factor_contribution[factor] = self.factor_exposures[factor] * factor_returns[factor]
        
        # 添加常数项贡献
        if 'const' in factor_returns:
            factor_contribution['Alpha'] = factor_returns['const']
        
        # 计算总因子贡献和特异贡献
        total_factor_contribution = factor_contribution.sum(axis=1)
        total_excess_return = y.sum()
        total_factor_return = total_factor_contribution.sum()
        total_specific_return = specific_returns.sum()
        
        # 验证：总超额收益 = 总因子贡献 + 总特异贡献
        attribution_check = pd.Series({
            'Total Excess Return': total_excess_return,
            'Total Factor Contribution': total_factor_return,
            'Total Specific Return': total_specific_return,
            'Difference': total_excess_return - (total_factor_return + total_specific_return)
        })
        
        # 计算因子贡献百分比
        if total_excess_return != 0:
            factor_contribution_pct = factor_contribution.sum() / total_excess_return
            specific_contribution_pct = total_specific_return / total_excess_return
        else:
            factor_contribution_pct = pd.Series(0, index=factor_contribution.columns)
            specific_contribution_pct = 0
        
        # 计算累积贡献
        cumulative_factor_contrib = factor_contribution.cumsum()
        cumulative_specific_contrib = specific_returns.cumsum()
        
        # 存储结果
        self.attribution_result = {
            'factor_loadings': self.factor_exposures,
            'factor_returns': factor_returns,
            'specific_returns': specific_returns,
            'factor_contribution': factor_contribution,
            'factor_contribution_pct': factor_contribution_pct,
            'specific_contribution_pct': specific_contribution_pct,
            'cumulative_factor_contribution': cumulative_factor_contrib,
            'cumulative_specific_contribution': cumulative_specific_contrib,
            'attribution_summary': attribution_check,
            'model_results': results,
            'weights': weights,
            'weighted_type': weighted_type
        }
    
    def _compute_model_diagnostics(self):
        """计算模型诊断信息"""
        if 'model_results' in self.attribution_result:
            results = self.attribution_result['model_results']
            
            # 获取基本统计量
            r_squared = results.rsquared
            adj_r_squared = results.rsquared_adj
            f_stat = results.fvalue
            p_value = results.f_pvalue
            
            # 获取参数t统计量和p值
            t_stats = results.tvalues
            p_values = results.pvalues
            
            # 信息准则
            aic = results.aic
            bic = results.bic
            
            # 存储诊断信息
            self.attribution_result['model_diagnostics'] = {
                'r_squared': r_squared,
                'adj_r_squared': adj_r_squared,
                'f_stat': f_stat,
                'p_value': p_value,
                't_stats': t_stats,
                'p_values': p_values,
                'aic': aic,
                'bic': bic
            }
            
            # 计算因子显著性
            significant_factors = pd.Series(
                index=p_values.index,
                data=['***' if p < 0.01 else 
                      '**' if p < 0.05 else 
                      '*' if p < 0.1 else 
                      '' for p in p_values]
            )
            self.attribution_result['significant_factors'] = significant_factors
        else:
            warnings.warn("无法计算模型诊断，模型结果不可用")

    def plot_factor_contribution(self, 
                               figsize: Tuple[int, int] = (12, 8),
                               title: str = "因子贡献分析",
                               show_pct: bool = True,
                               sort_by_abs: bool = True) -> plt.Figure:
        """
        绘制因子贡献分析图表
        
        参数:
            figsize: 图形大小
            title: 图表标题
            show_pct: 是否显示百分比贡献
            sort_by_abs: 是否按照贡献绝对值排序
            
        返回:
            Figure: Matplotlib图形对象
        """
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            
            # 检查是否已经运行归因分析
            if not hasattr(self, 'factor_contribution_pct') or self.factor_contribution_pct is None:
                self.run_attribution()
                
            # 确保数据有效
            if self.factor_contribution_pct is None or len(self.factor_contribution_pct) == 0:
                raise ValueError("因子贡献数据为空，无法绘制图表")
                
            # 创建数据副本以避免修改原始数据
            if show_pct:
                # 使用百分比贡献
                plot_data = self.factor_contribution_pct.copy() * 100
                y_label = "贡献比例 (%)"
            else:
                # 使用绝对贡献
                if hasattr(self, 'factor_contribution') and self.factor_contribution is not None:
                    # 计算每个因子的总贡献
                    plot_data = self.factor_contribution.sum()
                else:
                    raise ValueError("因子贡献数据未计算")
                y_label = "绝对贡献"
            
            # 处理缺失值
            plot_data = plot_data.fillna(0)
            
            # 按照绝对值排序（如果需要）
            if sort_by_abs:
                plot_data = plot_data.reindex(plot_data.abs().sort_values(ascending=False).index)
                
            # 创建图形
            fig, ax = plt.subplots(figsize=figsize)
            
            # 绘制水平条形图
            bars = ax.barh(
                y=plot_data.index,
                width=plot_data.values,
                color=sns.color_palette("viridis", len(plot_data))
            )
            
            # 在条形上添加标签
            for bar in bars:
                width = bar.get_width()
                label_x_pos = width if width >= 0 else width - 1
                ax.text(
                    label_x_pos, 
                    bar.get_y() + bar.get_height()/2, 
                    f'{width:.2f}',
                    va='center',
                    fontsize=10
                )
            
            # 设置标题和标签
            ax.set_title(title, fontsize=14, pad=20)
            ax.set_xlabel(y_label, fontsize=12)
            ax.set_ylabel("因子", fontsize=12)
            
            # 设置网格线
            ax.grid(axis='x', linestyle='--', alpha=0.7)
            
            # 添加特质收益贡献信息（如果有显示百分比）
            if show_pct and hasattr(self, 'specific_contribution_pct') and self.specific_contribution_pct is not None:
                specific_pct = self.specific_contribution_pct * 100
                ax.annotate(
                    f'特质收益贡献: {specific_pct:.2f}%',
                    xy=(0.02, 0.02),
                    xycoords='figure fraction',
                    fontsize=10,
                    bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8)
                )
                
            plt.tight_layout()
            return fig
            
        except Exception as e:
            import warnings
            warnings.warn(f"绘制因子贡献图表时出错: {str(e)}")
            # 创建一个空白图形并返回
            fig, ax = plt.subplots(figsize=figsize)
            ax.text(0.5, 0.5, f"绘图错误: {str(e)}", 
                   ha='center', va='center', fontsize=12)
            ax.set_title("因子贡献分析 - 错误", fontsize=14)
            return fig
    
    def plot_cumulative_contribution(self,
                                   figsize: Tuple[int, int] = (12, 8),
                                   title: str = "因子累积贡献分析",
                                   include_specific: bool = True) -> plt.Figure:
        """
        绘制因子累积贡献图表
        
        参数:
            figsize: 图形大小
            title: 图表标题
            include_specific: 是否包含特质收益
            
        返回:
            Figure: Matplotlib图形对象
        """
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            
            # 检查是否已经运行归因分析
            if not hasattr(self, 'factor_contribution') or self.factor_contribution is None:
                self.run_attribution()
                
            # 确保数据有效
            if self.factor_contribution is None or len(self.factor_contribution) == 0:
                raise ValueError("因子贡献数据为空，无法绘制图表")
            
            # 获取日期范围
            dates = self.factor_contribution.index
            
            # 创建累积收益数据
            cumulative_factor_contrib = self.factor_contribution.cumsum()
            
            # 创建图形
            fig, ax = plt.subplots(figsize=figsize)
            
            # 绘制总收益
            total_return = self.returns.cumsum() * 100
            ax.plot(dates, total_return, 'k-', linewidth=2, label='总收益')
            
            # 使用颜色渐变
            n_factors = len(self.factor_contribution.columns)
            colors = sns.color_palette("viridis", n_factors)
            
            # 绘制每个因子的累积贡献
            factor_sum = pd.Series(0, index=dates)
            for i, factor in enumerate(self.factor_contribution.columns):
                factor_contribution = cumulative_factor_contrib[factor] * 100
                ax.plot(dates, factor_contribution, 
                        linestyle='-', linewidth=1.5, color=colors[i], 
                        label=f'{factor}')
                factor_sum += self.factor_contribution[factor]
            
            # 绘制所有因子累积贡献
            factor_sum_cumulative = factor_sum.cumsum() * 100
            ax.plot(dates, factor_sum_cumulative, 
                    'b--', linewidth=2, label='因子累积贡献')
            
            # 如果包含特质收益
            if include_specific and hasattr(self, 'specific_return') and self.specific_return is not None:
                specific_cumulative = self.specific_return.cumsum() * 100
                ax.plot(dates, specific_cumulative, 
                        'r--', linewidth=2, label='特质收益')
            
            # 添加图例
            ax.legend(loc='best', fontsize=10)
            
            # 设置标题和标签
            ax.set_title(title, fontsize=14, pad=20)
            ax.set_xlabel('日期', fontsize=12)
            ax.set_ylabel('累积贡献 (%)', fontsize=12)
            
            # 设置网格线
            ax.grid(True, linestyle='--', alpha=0.7)
            
            # 设置x轴日期格式
            fig.autofmt_xdate()
            
            plt.tight_layout()
            return fig
            
        except Exception as e:
            import warnings
            warnings.warn(f"绘制因子累积贡献图表时出错: {str(e)}")
            # 创建一个空白图形并返回
            fig, ax = plt.subplots(figsize=figsize)
            ax.text(0.5, 0.5, f"绘图错误: {str(e)}", 
                   ha='center', va='center', fontsize=12)
            ax.set_title("因子累积贡献分析 - 错误", fontsize=14)
            return fig
    
    def plot_rolling_factor_contribution(self,
                                        window: int = 20,
                                        figsize: Tuple[int, int] = (14, 10),
                                        title: str = "滚动因子贡献分析",
                                        top_n: int = 5) -> plt.Figure:
          """
          绘制滚动窗口因子贡献图表
          
          参数:
              window: 滚动窗口大小（天数）
              figsize: 图形大小
              title: 图表标题
              top_n: 展示贡献最大的前N个因子
              
          返回:
              Figure: Matplotlib图形对象
          """
          try:
              import matplotlib.pyplot as plt
              import seaborn as sns
              
              # 检查是否已经运行归因分析
              if not hasattr(self, 'factor_contribution') or self.factor_contribution is None:
                  self.run_attribution()
                  
              # 确保数据有效
              if self.factor_contribution is None or len(self.factor_contribution) == 0:
                  raise ValueError("因子贡献数据为空，无法绘制图表")
              
              # 确保窗口大小合理
              if window >= len(self.factor_contribution):
                  window = max(int(len(self.factor_contribution) / 2), 5)
                  import warnings
                  warnings.warn(f"窗口大小大于数据长度，已调整为{window}")
              
              # 计算滚动窗口因子贡献
              rolling_contributions = self.factor_contribution.rolling(window=window).sum()
              
              # 选择对总收益贡献最大的前N个因子
              factor_importance = self.factor_contribution.sum().abs()
              top_factors = factor_importance.nlargest(top_n).index.tolist()
              
              # 创建图形，使用子图以便于展示多个面板
              fig, axes = plt.subplots(2, 1, figsize=figsize, gridspec_kw={'height_ratios': [3, 1]})
              
              # 设置颜色
              colors = sns.color_palette("viridis", len(top_factors))
              
              # 在上面的面板绘制每个因子的滚动贡献
              for i, factor in enumerate(top_factors):
                  axes[0].plot(rolling_contributions.index, 
                              rolling_contributions[factor] * 100, 
                              label=factor, linewidth=2, color=colors[i])
              
              # 添加零线
              axes[0].axhline(y=0, color='black', linestyle='-', alpha=0.3)
              
              # 设置上面面板的属性
              axes[0].set_title(f"{title} (窗口: {window}天)", fontsize=14, pad=20)
              axes[0].set_ylabel("滚动贡献 (%)", fontsize=12)
              axes[0].grid(True, linestyle='--', alpha=0.7)
              axes[0].legend(loc='best', fontsize=10)
              
              # 在下面的面板绘制总收益的滚动回报
              rolling_returns = self.returns.rolling(window=window).sum() * 100
              axes[1].plot(rolling_returns.index, rolling_returns, 
                          linewidth=2, color='black', label='总收益')
              
              # 设置下面面板的属性
              axes[1].set_ylabel("滚动总收益 (%)", fontsize=12)
              axes[1].set_xlabel("日期", fontsize=12)
              axes[1].grid(True, linestyle='--', alpha=0.7)
              axes[1].legend(loc='best', fontsize=10)
              
              # 设置x轴日期格式
              fig.autofmt_xdate()
              
              plt.tight_layout()
              return fig
              
          except Exception as e:
              import warnings
              warnings.warn(f"绘制滚动因子贡献图表时出错: {str(e)}")
              # 创建一个空白图形并返回
              fig, ax = plt.subplots(figsize=figsize)
              ax.text(0.5, 0.5, f"绘图错误: {str(e)}", 
                     ha='center', va='center', fontsize=12)
              ax.set_title("滚动因子贡献分析 - 错误", fontsize=14)
              return fig
    
    def plot_factor_exposures(self,
                              factors_to_plot: Optional[List[str]] = None,
                              figsize: Tuple[int, int] = (14, 8),
                              title: str = "因子暴露趋势") -> plt.Figure:
        """
        绘制因子暴露随时间的变化

        参数:
            factors_to_plot: 要绘制的因子列表，默认绘制最重要的因子
            figsize: 图表尺寸
            title: 图表标题

        返回:
            matplotlib.figure.Figure: 图表对象
        """
        # 获取因子暴露数据
        exposures = self.factor_exposures
        
        # 如果没有指定因子，选择最重要的因子
        if factors_to_plot is None:
            if 'factor_contribution' in self.attribution_result:
                factor_importance = self.attribution_result['factor_contribution'].sum().abs()
                factors_to_plot = factor_importance.nlargest(5).index.tolist()
            else:
                factors_to_plot = exposures.columns[:5].tolist()
        
        # 创建图表
        fig, ax = plt.subplots(figsize=figsize)
        
        # 绘制每个因子的暴露
        for factor in factors_to_plot:
            if factor in exposures.columns:
                ax.plot(exposures.index, exposures[factor], 
                        label=factor, linewidth=2)
        
        # 设置图表属性
        ax.set_title(title, fontsize=14)
        ax.set_ylabel("因子暴露", fontsize=12)
        ax.set_xlabel("日期", fontsize=12)
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax.grid(linestyle='--', alpha=0.3)
        ax.legend(loc='best')
        
        plt.tight_layout()
        return fig
    
    def analyze_risk_contribution(self) -> Dict[str, pd.Series]:
        """
        分析因子对风险的贡献
        
        返回:
            Dict[str, pd.Series]: 风险归因结果，包含各因子对总风险的贡献
        """
        if not self.attribution_result or 'factor_loadings' not in self.attribution_result:
            raise ValueError("请先运行归因分析")
            
        if self.risk_model is None or 'factor_covariance' not in self.risk_model:
            raise ValueError("缺少风险模型数据，无法执行风险归因")
            
        # 获取因子暴露和协方差矩阵
        exposures = self.attribution_result['factor_loadings']
        factor_cov = self.risk_model['factor_covariance']
        
        # 确保因子匹配
        common_factors = set(exposures.columns).intersection(set(factor_cov.index))
        if len(common_factors) == 0:
            raise ValueError("因子暴露和协方差矩阵没有共同的因子")
            
        # 计算组合因子暴露（使用平均暴露）
        portfolio_exposure = exposures.mean()
        
        # 计算因子风险贡献
        # RC_i = x_i * Σ * x / sqrt(x' * Σ * x)
        # 其中 x_i 是第i个因子的暴露，x是所有因子的暴露向量，Σ是因子协方差矩阵
        
        # 提取共同因子
        common_factors = list(common_factors)
        exposure_vector = portfolio_exposure[common_factors]
        cov_matrix = factor_cov.loc[common_factors, common_factors]
        
        # 计算组合风险
        portfolio_variance = exposure_vector.dot(cov_matrix).dot(exposure_vector)
        portfolio_risk = np.sqrt(portfolio_variance)
        
        # 计算边际风险贡献
        mrc = cov_matrix.dot(exposure_vector)
        
        # 计算风险贡献
        rc = exposure_vector * mrc
        risk_contrib = rc / portfolio_risk
        
        # 计算风险贡献百分比
        risk_contrib_pct = risk_contrib / risk_contrib.sum()
        
        # 特异风险贡献
        if 'specific_risk' in self.risk_model:
            specific_risk = self.risk_model['specific_risk']
            total_risk = np.sqrt(portfolio_variance + specific_risk**2)
            specific_contrib = specific_risk / total_risk
            
            risk_decomposition = {
                'factor_risk': portfolio_risk / total_risk,
                'specific_risk': specific_contrib
            }
        else:
            risk_decomposition = {
                'factor_risk': 1.0,
                'specific_risk': 0.0
            }
        
        # 存储结果
        self.risk_attribution = {
            'risk_contribution': risk_contrib,
            'risk_contribution_pct': risk_contrib_pct * 100,  # 转为百分比
            'marginal_contribution': mrc,
            'portfolio_risk': portfolio_risk,
            'risk_decomposition': pd.Series(risk_decomposition)
        }
        
        return self.risk_attribution
    
    def plot_risk_contribution(self,
                             figsize: Tuple[int, int] = (12, 8),
                             title: str = "风险贡献分析",
                             sort_by_abs: bool = True) -> plt.Figure:
        """
        绘制风险贡献分析图表
        
        参数:
            figsize: 图表尺寸
            title: 图表标题
            sort_by_abs: 是否按贡献绝对值排序
            
        返回:
            matplotlib.figure.Figure: 图表对象
        """
        # 确保已执行风险归因分析
        if not hasattr(self, 'risk_attribution') or not self.risk_attribution:
            self.analyze_risk_contribution()
            
        # 获取风险贡献
        risk_contrib = self.risk_attribution['risk_contribution_pct'].copy()
            
        # 按绝对值排序
        if sort_by_abs:
            risk_contrib = risk_contrib.reindex(
                risk_contrib.abs().sort_values(ascending=False).index
            )
        
        # 创建图表
        fig, ax = plt.subplots(figsize=figsize)

        # 设置颜色
        colors = ['#1f77b4' if x >= 0 else '#d62728' for x in risk_contrib]
        
        # 绘制条形图
        bars = ax.bar(risk_contrib.index, risk_contrib, color=colors)
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            if height >= 0:
                va = 'bottom'
                y_pos = height + 0.5
            else:
                va = 'top'
                y_pos = height - 0.5
            ax.text(
                bar.get_x() + bar.get_width()/2., 
                y_pos,
                f'{height:.2f}%',
                ha='center', 
                va=va,
                fontsize=8
            )
        
        # 设置图表属性
        ax.set_title(title, fontsize=14)
        ax.set_ylabel("风险贡献百分比 (%)", fontsize=12)
        ax.set_xlabel("因子", fontsize=12)
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax.grid(axis='y', linestyle='--', alpha=0.3)
        
        # 如果因子太多，旋转标签
        if len(risk_contrib) > 6:
            plt.xticks(rotation=45, ha='right')
            
        plt.tight_layout()
        return fig
    
    def compare_return_vs_risk(self,
                              figsize: Tuple[int, int] = (10, 8),
                              title: str = "收益vs风险贡献分析") -> plt.Figure:
        """
        比较收益贡献和风险贡献
        
        参数:
            figsize: 图表尺寸
            title: 图表标题
            
        返回:
            matplotlib.figure.Figure: 图表对象
        """
        # 确保已执行归因分析
        if not self.attribution_result or 'factor_contribution_pct' not in self.attribution_result:
            raise ValueError("请先运行归因分析")
            
        # 确保已执行风险归因分析
        if not hasattr(self, 'risk_attribution') or not self.risk_attribution:
            self.analyze_risk_contribution()
            
        # 获取收益贡献和风险贡献
        return_contrib = self.attribution_result['factor_contribution_pct'] * 100  # 转为百分比
        risk_contrib = self.risk_attribution['risk_contribution_pct']
        
        # 找出共同的因子
        common_factors = set(return_contrib.index).intersection(set(risk_contrib.index))
        if len(common_factors) == 0:
            raise ValueError("收益贡献和风险贡献没有共同的因子")
            
        common_factors = list(common_factors)
        return_contrib = return_contrib[common_factors]
        risk_contrib = risk_contrib[common_factors]
        
        # 创建图表
        fig, ax = plt.subplots(figsize=figsize)
        
        # 计算象限划分
        x_mid = 0
        y_mid = 0
        
        # 绘制散点图
        sc = ax.scatter(risk_contrib, return_contrib, s=100, alpha=0.7, 
                      c=return_contrib/risk_contrib, cmap='RdYlGn')
        
        # 添加因子标签
        for i, factor in enumerate(common_factors):
            ax.annotate(factor, 
                       (risk_contrib[i], return_contrib[i]),
                       xytext=(5, 5), 
                       textcoords='offset points',
                       fontsize=9)
        
        # 绘制象限分隔线
        ax.axhline(y=y_mid, color='black', linestyle='--', alpha=0.5)
        ax.axvline(x=x_mid, color='black', linestyle='--', alpha=0.5)
        
        # 添加象限标签
        ax.text(ax.get_xlim()[1]*0.8, ax.get_ylim()[1]*0.8, 
                "高收益/高风险", ha='center', va='center', 
                bbox=dict(facecolor='white', alpha=0.5))
        ax.text(ax.get_xlim()[0]*0.8, ax.get_ylim()[1]*0.8, 
                "高收益/低风险", ha='center', va='center',
                bbox=dict(facecolor='white', alpha=0.5))
        ax.text(ax.get_xlim()[1]*0.8, ax.get_ylim()[0]*0.8, 
                "低收益/高风险", ha='center', va='center',
                bbox=dict(facecolor='white', alpha=0.5))
        ax.text(ax.get_xlim()[0]*0.8, ax.get_ylim()[0]*0.8, 
                "低收益/低风险", ha='center', va='center',
                bbox=dict(facecolor='white', alpha=0.5))
        
        # 添加颜色条
        cbar = plt.colorbar(sc)
        cbar.set_label('收益/风险比', fontsize=10)
        
        # 设置图表属性
        ax.set_title(title, fontsize=14)
        ax.set_xlabel("风险贡献 (%)", fontsize=12)
        ax.set_ylabel("收益贡献 (%)", fontsize=12)
        ax.grid(linestyle='--', alpha=0.3)

        plt.tight_layout()
        return fig
              
    def perform_brinson_attribution(self, 
                                  sectors: Optional[pd.Series] = None) -> Dict[str, pd.DataFrame]:
        """
        执行Brinson风格的选择/分配效应分解
        
        参数:
            sectors: 资产所属的行业/部门分类，如果不提供则尝试从因子中找出类别因子
            
        返回:
            Dict[str, pd.DataFrame]: Brinson归因结果
        """
        raise NotImplementedError("Brinson归因方法尚未实现")
    
    def generate_attribution_report(self, 
                                  output_format: str = 'html',
                                  output_path: Optional[str] = None,
                                  include_plots: bool = True) -> Optional[str]:
        """
        生成完整的归因报告
        
        参数:
            output_format: 输出格式，可选'html', 'pdf', 'json'
            output_path: 输出文件路径，如果不提供则返回字符串
            include_plots: 是否包含图表
            
        返回:
            Optional[str]: 如果没有提供输出路径，则返回报告内容
        """
        raise NotImplementedError("报告生成功能尚未实现")

    def run_rolling_attribution(self, 
                               window: Optional[int] = None, 
                               min_periods: Optional[int] = None,
                               method: Optional[str] = None) -> Dict[str, pd.DataFrame]:
        """
        执行滚动窗口的归因分析
        
        参数:
            window: 滚动窗口大小，默认使用初始化时设置的窗口
            min_periods: 最小样本量，默认使用初始化时设置的值
            method: 归因方法，默认使用初始化时设置的方法
            
        返回:
            Dict[str, pd.DataFrame]: 滚动归因结果
        """
        if window is None:
            window = self.window
            
        if min_periods is None:
            min_periods = self.min_periods
            
        if method is None:
            method = self.method
        
        # 准备数据
        y = self.excess_returns
        X = sm.add_constant(self.factor_exposures)
        
        # 检查数据是否有效
        if len(y) == 0 or len(X) == 0:
            raise ValueError("数据序列为空，无法进行滚动归因分析")
            
        if len(y) < min_periods:
            raise ValueError(f"数据样本量({len(y)})小于最小要求({min_periods})，无法进行滚动归因分析")
        
        # 使用try/except捕获statsmodels可能的错误
        try:
            # 使用RollingOLS进行滚动回归
            rolling_model = RollingOLS(y, X, window=window, min_nobs=min_periods)
            rolling_results = rolling_model.fit()
            
            # 提取滚动系数和t值
            rolling_params = rolling_results.params
            rolling_tvalues = rolling_results.tvalues
            rolling_pvalues = getattr(rolling_results, 'pvalues', None)
            rolling_rsquared = rolling_results.rsquared
        except Exception as e:
            raise ValueError(f"执行滚动回归失败: {str(e)}")
        
        # 计算滚动因子贡献
        rolling_factor_contrib = {}
        for factor in self.factor_exposures.columns:
            if factor in rolling_params.columns:
                # 计算每个时间点的因子贡献
                factor_return = rolling_params[factor]
                factor_exposure = self.factor_exposures[factor]
                rolling_factor_contrib[factor] = factor_return * factor_exposure
        
        rolling_factor_contrib = pd.DataFrame(rolling_factor_contrib)
        
        # 添加截距项贡献
        if 'const' in rolling_params.columns:
            rolling_factor_contrib['Alpha'] = rolling_params['const']
        
        # 计算滚动特异贡献
        # 注意：这是一个近似值，因为我们使用全样本因子暴露乘以滚动系数
        rolling_fitted = pd.DataFrame(
            index=X.index, 
            columns=['fitted']
        )
        
        for i, date in enumerate(rolling_params.index):
            if i < window and i < len(y):
                continue
                
            # 获取当前日期的系数
            params = rolling_params.loc[date]
            
            # 计算当前日期的拟合值
            X_date = X.loc[date]
            fitted_value = (X_date * params).sum()
            
            rolling_fitted.loc[date, 'fitted'] = fitted_value
        
        rolling_specific = y - rolling_fitted['fitted']
        
        # 存储滚动归因结果
        self.rolling_attribution = {
            'rolling_params': rolling_params,
            'rolling_tvalues': rolling_tvalues,
            'rolling_pvalues': rolling_pvalues,
            'rolling_rsquared': rolling_rsquared,
            'rolling_factor_contribution': rolling_factor_contrib,
            'rolling_specific_contribution': rolling_specific
        }
        
        return self.rolling_attribution


def perform_multifactor_attribution(
    returns: pd.Series,
    factor_exposures: pd.DataFrame,
    benchmark_returns: Optional[pd.Series] = None,
    factor_returns: Optional[pd.DataFrame] = None,
    risk_model: Optional[Dict[str, pd.DataFrame]] = None,
    risk_free_rate: Optional[pd.Series] = None,
    method: str = "linear",
    regularization: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    执行多因子归因分析的便捷函数
    
    参数:
        returns: 投资组合或策略的收益率序列
        factor_exposures: 因子暴露数据，DataFrame格式，每行是一个日期，每列是一个因子
        benchmark_returns: 基准的收益率序列，如果提供则执行相对归因分析
        factor_returns: 因子收益率数据，如果不提供则从数据中估计
        risk_model: 风险模型数据，包含因子协方差矩阵和特异风险
        risk_free_rate: 无风险利率序列
        method: 归因方法，可选"linear", "lasso", "ridge", "wls", "gls"
        regularization: 正则化参数，如果使用lasso或ridge方法
        
    返回:
        Dict[str, Any]: 归因结果
    """
    attributor = MultifactorAttribution(
        returns=returns,
        factor_exposures=factor_exposures,
        benchmark_returns=benchmark_returns,
        factor_returns=factor_returns,
        risk_model=risk_model,
        risk_free_rate=risk_free_rate,
        method=method,
        regularization=regularization
    )
    
    return attributor.run_attribution()


def perform_dynamic_attribution(
    returns: pd.Series,
    factor_exposures: pd.DataFrame,
    benchmark_returns: Optional[pd.Series] = None,
    window: int = 252,
    min_periods: int = 60,
    method: str = "linear"
) -> Dict[str, pd.DataFrame]:
    """
    执行动态多因子归因分析的便捷函数
    
    参数:
        returns: 投资组合或策略的收益率序列
        factor_exposures: 因子暴露数据，DataFrame格式，每行是一个日期，每列是一个因子
        benchmark_returns: 基准的收益率序列，如果提供则执行相对归因分析
        window: 滚动窗口大小
        min_periods: 最小样本量
        method: 归因方法，可选"linear", "lasso", "ridge", "wls"
        
    返回:
        Dict[str, pd.DataFrame]: 动态归因结果
    """
    attributor = MultifactorAttribution(
        returns=returns,
        factor_exposures=factor_exposures,
        benchmark_returns=benchmark_returns,
        method=method,
        window=window,
        min_periods=min_periods
    )
    
    return attributor.run_rolling_attribution()


def visualize_attribution_results(attribution_result: Dict[str, Any],
                               figsize: Tuple[int, int] = (12, 10),
                               title: str = "多因子归因分析结果") -> plt.Figure:
    """
    可视化归因分析结果
    
    参数:
        attribution_result: 归因分析结果字典
        figsize: 图表尺寸
        title: 图表标题
        
    返回:
        plt.Figure: 图表对象
    """
    if 'factor_contribution_pct' not in attribution_result:
        raise ValueError("归因结果缺少必要的数据")
        
    # 提取数据
    factor_contrib = attribution_result['factor_contribution_pct'] * 100  # 转为百分比
    
    # 按绝对值排序
    factor_contrib = factor_contrib.reindex(
        factor_contrib.abs().sort_values(ascending=False).index
    )
    
    # 创建图表
    fig, axes = plt.subplots(2, 1, figsize=figsize)
    
    # 绘制因子贡献图
    colors = ['#1f77b4' if x >= 0 else '#d62728' for x in factor_contrib]
    bars = axes[0].bar(factor_contrib.index, factor_contrib, color=colors)
    
    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        if height >= 0:
            va = 'bottom'
            y_pos = height + 0.5
        else:
            va = 'top'
            y_pos = height - 0.5
        axes[0].text(
            bar.get_x() + bar.get_width()/2., 
            y_pos,
            f'{height:.2f}%',
            ha='center', 
            va=va,
            fontsize=8
        )
    
    # 设置图表属性
    axes[0].set_title("因子贡献分析 (%)", fontsize=12)
    axes[0].axhline(y=0, color='black', linestyle='-', alpha=0.3)
    axes[0].grid(axis='y', linestyle='--', alpha=0.3)
    
    # 绘制累积贡献图（如果有数据）
    if ('cumulative_factor_contribution' in attribution_result and 
        'cumulative_specific_contribution' in attribution_result):
        
        cum_contrib = attribution_result['cumulative_factor_contribution']
        spec_contrib = attribution_result['cumulative_specific_contribution']
        
        # 选择贡献最大的因子
        top_factors = factor_contrib.abs().nlargest(5).index
        
        # 绘制每个因子的累积贡献
        for factor in top_factors:
            if factor in cum_contrib.columns:
                axes[1].plot(cum_contrib.index, cum_contrib[factor], 
                            label=factor, linewidth=2)
        
        # 绘制特异贡献
        axes[1].plot(spec_contrib.index, spec_contrib, 
                    label='Specific', linewidth=2, linestyle='--', color='black')
        
        # 设置图表属性
        axes[1].set_title("累积因子贡献", fontsize=12)
        axes[1].set_xlabel("日期", fontsize=10)
        axes[1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
        axes[1].grid(linestyle='--', alpha=0.3)
        axes[1].legend(loc='best')
    
    # 如果因子太多，旋转标签
    if len(factor_contrib) > 6:
        plt.setp(axes[0].xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # 添加总标题
    fig.suptitle(title, fontsize=14)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.9)  # 为总标题腾出空间
    
    return fig


def compare_attribution_methods(returns: pd.Series,
                             factor_exposures: pd.DataFrame,
                             benchmark_returns: Optional[pd.Series] = None,
                             methods: List[str] = ['linear', 'lasso', 'ridge', 'wls'],
                             figsize: Tuple[int, int] = (12, 8)) -> plt.Figure:
    """
    比较不同归因方法的结果
    
    参数:
        returns: 投资组合或策略的收益率序列
        factor_exposures: 因子暴露数据
        benchmark_returns: 基准的收益率序列
        methods: 要比较的归因方法列表
        figsize: 图表尺寸
        
    返回:
        plt.Figure: 图表对象
    """
    # 执行不同方法的归因分析
    results = {}
    for method in methods:
        results[method] = perform_multifactor_attribution(
            returns=returns,
            factor_exposures=factor_exposures,
            benchmark_returns=benchmark_returns,
            method=method
        )
    
    # 提取各方法的因子贡献
    factors = set()
    factor_contribs = {}
    
    for method, result in results.items():
        if 'factor_contribution_pct' in result:
            contrib = result['factor_contribution_pct'] * 100  # 转为百分比
            factor_contribs[method] = contrib
            factors.update(contrib.index)
    
    # 创建比较DataFrame
    comparison = pd.DataFrame(index=sorted(factors), columns=methods)
    
    for method, contrib in factor_contribs.items():
        for factor in contrib.index:
            comparison.loc[factor, method] = contrib[factor]
    
    # 填充缺失值
    comparison.fillna(0, inplace=True)
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 绘制热力图
    sns.heatmap(comparison, annot=True, cmap='RdBu_r', center=0, ax=ax,
               fmt=".2f", linewidths=.5)
    
    # 设置图表属性
    ax.set_title("不同归因方法的因子贡献比较 (%)", fontsize=14)
    plt.tight_layout()

    return fig
    

def create_attribution_dashboard(
    returns: pd.Series,
    factor_exposures: pd.DataFrame,
    benchmark_returns: Optional[pd.Series] = None,
    risk_model: Optional[Dict[str, pd.DataFrame]] = None,
    method: str = "linear",
    rolling_window: int = 252,
    figsize: Tuple[int, int] = (18, 14)
) -> plt.Figure:
    """
    创建一个完整的多因子归因分析仪表盘
    
    参数:
        returns: 投资组合或策略的收益率序列
        factor_exposures: 因子暴露数据
        benchmark_returns: 基准的收益率序列
        risk_model: 风险模型数据
        method: 归因方法
        rolling_window: 滚动窗口大小
        figsize: 图表尺寸
        
    返回:
        plt.Figure: 仪表盘图表对象
    """
    # 执行归因分析
    attribution = MultifactorAttribution(
        returns=returns,
        factor_exposures=factor_exposures,
        benchmark_returns=benchmark_returns,
        risk_model=risk_model,
        method=method,
        window=rolling_window
    )
    
    # 运行归因分析
    attribution.run_attribution()
    
    # 如果有风险模型，执行风险归因
    has_risk_model = risk_model is not None and 'factor_covariance' in risk_model
    if has_risk_model:
        attribution.analyze_risk_contribution()
    
    # 运行滚动归因
    attribution.run_rolling_attribution()
    
    # 创建仪表盘
    fig = plt.figure(figsize=figsize)
    
    # 设置网格
    if has_risk_model:
        grid = plt.GridSpec(3, 2, figure=fig, hspace=0.4, wspace=0.3)
    else:
        grid = plt.GridSpec(2, 2, figure=fig, hspace=0.4, wspace=0.3)
    
    # 绘制因子贡献
    ax1 = fig.add_subplot(grid[0, 0])
    factor_contrib = attribution.attribution_result['factor_contribution_pct'] * 100
    factor_contrib = factor_contrib.reindex(
        factor_contrib.abs().sort_values(ascending=False).index
    )
    colors = ['#1f77b4' if x >= 0 else '#d62728' for x in factor_contrib]
    bars = ax1.bar(factor_contrib.index, factor_contrib, color=colors)
    ax1.set_title("因子贡献分析 (%)", fontsize=12)
    ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax1.grid(axis='y', linestyle='--', alpha=0.3)
    if len(factor_contrib) > 6:
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # 绘制累积贡献
    ax2 = fig.add_subplot(grid[0, 1])
    cum_contrib = attribution.attribution_result['cumulative_factor_contribution']
    spec_contrib = attribution.attribution_result['cumulative_specific_contribution']
    
    # 选择贡献最大的因子
    top_factors = factor_contrib.abs().nlargest(5).index
    
    # 绘制每个因子的累积贡献
    for factor in top_factors:
        if factor in cum_contrib.columns:
            ax2.plot(cum_contrib.index, cum_contrib[factor], 
                    label=factor, linewidth=2)
    
    # 绘制特异贡献
    ax2.plot(spec_contrib.index, spec_contrib, 
            label='Specific', linewidth=2, linestyle='--', color='black')
    
    # 绘制总超额收益
    if hasattr(attribution, 'excess_returns'):
        cum_excess = attribution.excess_returns.cumsum()
        ax2.plot(cum_excess.index, cum_excess, 
                label='Total Excess Return', linewidth=3, color='red')
    
    ax2.set_title("累积因子贡献", fontsize=12)
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax2.grid(linestyle='--', alpha=0.3)
    ax2.legend(loc='best')
    
    # 绘制滚动因子贡献
    ax3 = fig.add_subplot(grid[1, 0])
    rolling_contrib = attribution.rolling_attribution['rolling_factor_contribution']
    
    # 绘制滚动因子贡献
    for factor in top_factors:
        if factor in rolling_contrib.columns:
            # 应用平滑
            smoothed = rolling_contrib[factor].rolling(window=20, min_periods=1).mean()
            ax3.plot(smoothed.index, smoothed, linewidth=2, label=factor)
    
    ax3.set_title(f"滚动因子贡献 (窗口: 20天)", fontsize=12)
    ax3.grid(linestyle='--', alpha=0.3)
    ax3.legend(loc='best')
    
    # 绘制滚动R方
    ax4 = fig.add_subplot(grid[1, 1])
    rsq = attribution.rolling_attribution['rolling_rsquared']
    ax4.plot(rsq.index, rsq, color='black', linewidth=2)
    ax4.set_title("归因模型解释力 (R-squared)", fontsize=12)
    ax4.set_ylim([0, 1])
    ax4.grid(linestyle='--', alpha=0.3)
    
    # 如果有风险模型，绘制风险贡献
    if has_risk_model:
        ax5 = fig.add_subplot(grid[2, 0])
        risk_contrib = attribution.risk_attribution['risk_contribution_pct']
        risk_contrib = risk_contrib.reindex(
            risk_contrib.abs().sort_values(ascending=False).index
        )
        colors = ['#1f77b4' if x >= 0 else '#d62728' for x in risk_contrib]
        bars = ax5.bar(risk_contrib.index, risk_contrib, color=colors)
        ax5.set_title("风险贡献分析 (%)", fontsize=12)
        ax5.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax5.grid(axis='y', linestyle='--', alpha=0.3)
        if len(risk_contrib) > 6:
            plt.setp(ax5.xaxis.get_majorticklabels(), rotation=45, ha='right')
        
        # 绘制收益vs风险散点图
        ax6 = fig.add_subplot(grid[2, 1])
        
        # 获取收益贡献和风险贡献
        return_contrib = attribution.attribution_result['factor_contribution_pct'] * 100
        risk_contrib = attribution.risk_attribution['risk_contribution_pct']
        
        # 找出共同的因子
        common_factors = set(return_contrib.index).intersection(set(risk_contrib.index))
        common_factors = list(common_factors)
        return_contrib = return_contrib[common_factors]
        risk_contrib = risk_contrib[common_factors]
        
        # 绘制散点图
        sc = ax6.scatter(risk_contrib, return_contrib, s=100, alpha=0.7, 
                        c=return_contrib/risk_contrib, cmap='RdYlGn')
        
        # 添加因子标签
        for i, factor in enumerate(common_factors):
            ax6.annotate(factor, 
                        (risk_contrib[i], return_contrib[i]),
                        xytext=(5, 5), 
                        textcoords='offset points',
                        fontsize=9)
        
        # 绘制象限分隔线
        ax6.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax6.axvline(x=0, color='black', linestyle='--', alpha=0.5)
        
        # 添加颜色条
        cbar = plt.colorbar(sc, ax=ax6)
        cbar.set_label('收益/风险比', fontsize=10)
        
        ax6.set_title("收益vs风险贡献分析", fontsize=12)
        ax6.set_xlabel("风险贡献 (%)", fontsize=10)
        ax6.set_ylabel("收益贡献 (%)", fontsize=10)
        ax6.grid(linestyle='--', alpha=0.3)
    
    # 添加总标题
    fig.suptitle(f"多因子归因分析仪表盘 - {method.upper()}方法", fontsize=16)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.92)  # 为总标题腾出空间
    
    return fig 