#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
滑点模型模块

提供各种交易执行滑点的实现，包括：
1. 简单固定滑点模型
2. 百分比滑点模型
3. 市场价格影响滑点模型 
4. 随机滑点模型
"""

import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Union, Optional, Tuple, Any, Callable

class SlippageModelInterface(ABC):
    """
    滑点模型接口
    
    定义滑点计算的基本接口
    """
    
    @abstractmethod
    def apply_slippage(self, context: Dict[str, Any], order: Dict[str, Any]) -> float:
        """
        应用滑点，计算实际成交价格
        
        参数:
            context: 交易上下文，包含当前市场状态等信息
            order: 订单信息，包含交易方向、数量、价格等
            
        返回:
            float: 考虑滑点后的实际成交价格
        """
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """
        获取模型名称
        
        返回:
            str: 模型名称
        """
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """
        获取模型描述
        
        返回:
            str: 模型描述
        """
        pass


class FixedSlippageModel(SlippageModelInterface):
    """
    固定滑点模型
    
    按照固定点数对价格进行调整
    
    参数:
        price_impact: 固定价格变动点数
    """
    
    def __init__(self, price_impact: float = 0.01):
        """
        初始化固定滑点模型
        
        参数:
            price_impact: 固定价格变动点数
        """
        self.price_impact = price_impact
        
    def apply_slippage(self, context: Dict[str, Any], order: Dict[str, Any]) -> float:
        """
        应用固定滑点
        
        参数:
            context: 交易上下文
            order: 订单信息
            
        返回:
            float: 考虑滑点后的实际成交价格
        """
        price = order.get('price', 0)
        quantity = order.get('quantity', 0)
        
        if price <= 0 or quantity == 0:
            return price
        
        # 买入订单价格上升，卖出订单价格下降
        direction = 1 if quantity > 0 else -1
        adjusted_price = price + direction * self.price_impact
        
        # 确保价格不为负数
        return max(0.0001, adjusted_price)
    
    def get_name(self) -> str:
        """
        获取模型名称
        
        返回:
            str: 模型名称
        """
        return "固定滑点模型"
    
    def get_description(self) -> str:
        """
        获取模型描述
        
        返回:
            str: 模型描述
        """
        return f"固定滑点：每笔交易固定调整 {self.price_impact} 点"


class PercentageSlippageModel(SlippageModelInterface):
    """
    百分比滑点模型
    
    按照百分比对价格进行调整
    
    参数:
        percentage: 滑点百分比，例如0.001表示0.1%
    """
    
    def __init__(self, percentage: float = 0.001):
        """
        初始化百分比滑点模型
        
        参数:
            percentage: 滑点百分比，例如0.001表示0.1%
        """
        self.percentage = percentage
        
    def apply_slippage(self, context: Dict[str, Any], order: Dict[str, Any]) -> float:
        """
        应用百分比滑点
        
        参数:
            context: 交易上下文
            order: 订单信息
            
        返回:
            float: 考虑滑点后的实际成交价格
        """
        price = order.get('price', 0)
        quantity = order.get('quantity', 0)
        
        if price <= 0 or quantity == 0:
            return price
        
        # 买入订单价格上升，卖出订单价格下降
        direction = 1 if quantity > 0 else -1
        adjusted_price = price * (1 + direction * self.percentage)
        
        # 确保价格不为负数
        return max(0.0001, adjusted_price)
    
    def get_name(self) -> str:
        """
        获取模型名称
        
        返回:
            str: 模型名称
        """
        return "百分比滑点模型"
    
    def get_description(self) -> str:
        """
        获取模型描述
        
        返回:
            str: 模型描述
        """
        return f"百分比滑点：每笔交易调整 {self.percentage * 100}%"


class VolumeShareSlippageModel(SlippageModelInterface):
    """
    交易量占比滑点模型
    
    基于交易量占比计算价格影响，交易量占比越大，价格影响越大
    
    参数:
        volume_limit: 交易量限制，超过此限制的交易会产生额外的滑点
        price_impact: 价格影响因子，用于计算滑点大小
        volume_field: 成交量字段名称
    """
    
    def __init__(self, 
                volume_limit: float = 0.1, 
                price_impact: float = 0.1,
                volume_field: str = 'volume'):
        """
        初始化交易量占比滑点模型
        
        参数:
            volume_limit: 交易量限制，超过此限制的交易会产生额外的滑点
            price_impact: 价格影响因子，用于计算滑点大小
            volume_field: 成交量字段名称
        """
        self.volume_limit = volume_limit
        self.price_impact = price_impact
        self.volume_field = volume_field
        
    def apply_slippage(self, context: Dict[str, Any], order: Dict[str, Any]) -> float:
        """
        应用交易量占比滑点
        
        参数:
            context: 交易上下文
            order: 订单信息
            
        返回:
            float: 考虑滑点后的实际成交价格
        """
        price = order.get('price', 0)
        quantity = order.get('quantity', 0)
        symbol = order.get('symbol')
        
        if price <= 0 or quantity == 0 or symbol is None:
            return price
        
        # 获取当前证券的成交量
        symbols_data = context.get('symbols_data', {})
        symbol_data = symbols_data.get(symbol, {})
        volume = symbol_data.get(self.volume_field, 0)
        
        if volume <= 0:
            # 如果没有成交量信息，使用固定百分比滑点
            direction = 1 if quantity > 0 else -1
            adjusted_price = price * (1 + direction * 0.001)
            return max(0.0001, adjusted_price)
        
        # 计算交易量占比
        volume_share = abs(quantity) / volume
        
        # 计算滑点
        # 当交易量占比超过限制时，滑点会急剧增加
        if volume_share <= self.volume_limit:
            slippage_factor = volume_share / self.volume_limit
        else:
            # 超出限制部分的滑点增加更快
            excess = volume_share - self.volume_limit
            slippage_factor = 1 + excess / self.volume_limit
            
        slippage = price * slippage_factor * self.price_impact
        
        # 买入订单价格上升，卖出订单价格下降
        direction = 1 if quantity > 0 else -1
        adjusted_price = price + direction * slippage
        
        # 确保价格不为负数
        return max(0.0001, adjusted_price)
    
    def get_name(self) -> str:
        """
        获取模型名称
        
        返回:
            str: 模型名称
        """
        return "交易量占比滑点模型"
    
    def get_description(self) -> str:
        """
        获取模型描述
        
        返回:
            str: 模型描述
        """
        return f"交易量占比滑点：交易量限制 {self.volume_limit * 100}%，价格影响因子 {self.price_impact}"


class RandomSlippageModel(SlippageModelInterface):
    """
    随机滑点模型
    
    在基础滑点的基础上增加随机扰动
    
    参数:
        base_model: 基础滑点模型
        min_random: 最小随机滑点因子
        max_random: 最大随机滑点因子
        seed: 随机数种子，用于重现随机结果
    """
    
    def __init__(self, 
                base_model: SlippageModelInterface = None, 
                min_random: float = 0.0, 
                max_random: float = 0.002,
                seed: Optional[int] = None):
        """
        初始化随机滑点模型
        
        参数:
            base_model: 基础滑点模型
            min_random: 最小随机滑点因子
            max_random: 最大随机滑点因子
            seed: 随机数种子，用于重现随机结果
        """
        if base_model is None:
            self.base_model = PercentageSlippageModel(percentage=0.001)
        else:
            self.base_model = base_model
            
        self.min_random = min_random
        self.max_random = max_random
        
        # 初始化随机数生成器
        if seed is not None:
            np.random.seed(seed)
        
    def apply_slippage(self, context: Dict[str, Any], order: Dict[str, Any]) -> float:
        """
        应用随机滑点
        
        参数:
            context: 交易上下文
            order: 订单信息
            
        返回:
            float: 考虑滑点后的实际成交价格
        """
        # 先应用基础滑点模型
        base_price = self.base_model.apply_slippage(context, order)
        
        price = order.get('price', 0)
        quantity = order.get('quantity', 0)
        
        if price <= 0 or quantity == 0:
            return base_price
        
        # 生成随机滑点因子
        random_factor = np.random.uniform(self.min_random, self.max_random)
        
        # 买入订单价格上升，卖出订单价格下降
        direction = 1 if quantity > 0 else -1
        random_slippage = price * random_factor * direction
        
        # 应用随机滑点
        adjusted_price = base_price + random_slippage
        
        # 确保价格不为负数
        return max(0.0001, adjusted_price)
    
    def get_name(self) -> str:
        """
        获取模型名称
        
        返回:
            str: 模型名称
        """
        return f"随机滑点模型({self.base_model.get_name()})"
    
    def get_description(self) -> str:
        """
        获取模型描述
        
        返回:
            str: 模型描述
        """
        return f"随机滑点：基于{self.base_model.get_description()}，随机扰动范围[{self.min_random * 100}%, {self.max_random * 100}%]"


class VolatilitySlippageModel(SlippageModelInterface):
    """
    波动率滑点模型
    
    基于证券的波动率计算滑点，波动率越高，滑点越大
    
    参数:
        volatility_field: 波动率字段名称
        scale_factor: 缩放因子，控制波动率对滑点的影响程度
        base_slippage: 基础滑点百分比
    """
    
    def __init__(self, 
                volatility_field: str = 'volatility', 
                scale_factor: float = 0.1,
                base_slippage: float = 0.0005):
        """
        初始化波动率滑点模型
        
        参数:
            volatility_field: 波动率字段名称
            scale_factor: 缩放因子，控制波动率对滑点的影响程度
            base_slippage: 基础滑点百分比
        """
        self.volatility_field = volatility_field
        self.scale_factor = scale_factor
        self.base_slippage = base_slippage
        
    def apply_slippage(self, context: Dict[str, Any], order: Dict[str, Any]) -> float:
        """
        应用波动率滑点
        
        参数:
            context: 交易上下文
            order: 订单信息
            
        返回:
            float: 考虑滑点后的实际成交价格
        """
        price = order.get('price', 0)
        quantity = order.get('quantity', 0)
        symbol = order.get('symbol')
        
        if price <= 0 or quantity == 0 or symbol is None:
            return price
        
        # 获取当前证券的波动率
        symbols_data = context.get('symbols_data', {})
        symbol_data = symbols_data.get(symbol, {})
        volatility = symbol_data.get(self.volatility_field)
        
        # 如果没有波动率信息，使用基础滑点
        if volatility is None or volatility <= 0:
            slippage_percentage = self.base_slippage
        else:
            # 根据波动率计算滑点百分比
            slippage_percentage = self.base_slippage + volatility * self.scale_factor
        
        # 买入订单价格上升，卖出订单价格下降
        direction = 1 if quantity > 0 else -1
        adjusted_price = price * (1 + direction * slippage_percentage)
        
        # 确保价格不为负数
        return max(0.0001, adjusted_price)
    
    def get_name(self) -> str:
        """
        获取模型名称
        
        返回:
            str: 模型名称
        """
        return "波动率滑点模型"
    
    def get_description(self) -> str:
        """
        获取模型描述
        
        返回:
            str: 模型描述
        """
        return f"波动率滑点：基础滑点 {self.base_slippage * 100}%，波动率缩放因子 {self.scale_factor}"


# 工厂函数，创建各种滑点模型
def create_slippage_model(model_type: str = 'percentage', **kwargs) -> SlippageModelInterface:
    """
    创建滑点模型
    
    参数:
        model_type: 模型类型，支持 'fixed'、'percentage'、'volume_share'、'random'、'volatility'
        **kwargs: 模型参数
        
    返回:
        SlippageModelInterface: 滑点模型
    """
    if model_type == 'fixed':
        price_impact = kwargs.get('price_impact', 0.01)
        return FixedSlippageModel(price_impact=price_impact)
    
    elif model_type == 'percentage':
        percentage = kwargs.get('percentage', 0.001)
        return PercentageSlippageModel(percentage=percentage)
    
    elif model_type == 'volume_share':
        volume_limit = kwargs.get('volume_limit', 0.1)
        price_impact = kwargs.get('price_impact', 0.1)
        volume_field = kwargs.get('volume_field', 'volume')
        return VolumeShareSlippageModel(
            volume_limit=volume_limit, 
            price_impact=price_impact, 
            volume_field=volume_field
        )
    
    elif model_type == 'random':
        base_model_type = kwargs.get('base_model_type', 'percentage')
        base_model = create_slippage_model(base_model_type)
        min_random = kwargs.get('min_random', 0.0)
        max_random = kwargs.get('max_random', 0.002)
        seed = kwargs.get('seed')
        return RandomSlippageModel(
            base_model=base_model, 
            min_random=min_random, 
            max_random=max_random,
            seed=seed
        )
    
    elif model_type == 'volatility':
        volatility_field = kwargs.get('volatility_field', 'volatility')
        scale_factor = kwargs.get('scale_factor', 0.1)
        base_slippage = kwargs.get('base_slippage', 0.0005)
        return VolatilitySlippageModel(
            volatility_field=volatility_field, 
            scale_factor=scale_factor, 
            base_slippage=base_slippage
        )
    
    else:
        raise ValueError(f"不支持的滑点模型类型：{model_type}")


# 预定义市场滑点模型
def create_a_stock_slippage() -> SlippageModelInterface:
    """
    创建A股市场滑点模型
    
    返回:
        SlippageModelInterface: A股滑点模型
    """
    # A股市场使用交易量占比滑点模型
    return VolumeShareSlippageModel(volume_limit=0.05, price_impact=0.1)


def create_hk_stock_slippage() -> SlippageModelInterface:
    """
    创建港股市场滑点模型
    
    返回:
        SlippageModelInterface: 港股滑点模型
    """
    # 港股市场使用百分比滑点模型
    return PercentageSlippageModel(percentage=0.0015)


def create_us_stock_slippage() -> SlippageModelInterface:
    """
    创建美股市场滑点模型
    
    返回:
        SlippageModelInterface: 美股滑点模型
    """
    # 美股市场使用百分比滑点模型
    return PercentageSlippageModel(percentage=0.001) 