"""
回测模块
- 提供回测引擎和性能分析工具
- 支持向量化回测和事件驱动回测
"""

# {{ AURA-X: Modify - 暂时跳过有问题的导入，专注于工厂整合测试. Approval: 寸止(ID:BacktestEngineFactory整合). }}
try:
    from src.backtest.engine.factory import create_backtest_engine, BacktestEngineFactory
except ImportError as e:
    print(f"Warning: 无法导入回测引擎工厂: {e}")

# 暂时跳过有问题的导入
# from src.backtest.engine.vector_engine import VectorBacktestEngine
# from src.backtest.performance.metrics import (
#     calculate_returns_stats,
#     calculate_risk_metrics,
#     calculate_drawdown_stats,
#     calculate_performance_metrics
# )
# from src.backtest.performance.report import PerformanceReport, generate_backtest_report

__all__ = [
    'create_backtest_engine',
    'BacktestEngineFactory',
    'VectorBacktestEngine',
    'calculate_returns_stats',
    'calculate_risk_metrics',
    'calculate_drawdown_stats',
    'calculate_performance_metrics',
    'PerformanceReport',
    'generate_backtest_report'
]
