"""
向量化回测引擎
- 基于矢量化计算的高性能回测引擎
- 适用于大规模历史数据回测
- 支持多股票回测和详细的性能分析
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple, Callable
import datetime
import warnings
import logging
from pathlib import Path
import os

from src.data.fetcher.market_data_fetcher import MarketData
from src.strategy.strategies.strategy_interface import StrategyInterface
from src.backtest.engine.engine_interface import BacktestEngineInterface
from src.backtest.performance.metrics import calculate_performance_metrics
from src.backtest.performance.report import PerformanceReport, generate_backtest_report

# 配置日志
logger = logging.getLogger(__name__)

class VectorBacktestEngine(BacktestEngineInterface):
    """向量化回测引擎，使用矢量化计算实现高性能回测"""
    
    def __init__(self):
        """初始化回测引擎"""
        # 策略相关
        self.strategy = None
        self.strategy_params = {}
        
        # 回测范围
        self.start_date = None
        self.end_date = None
        self.universe = []  # 股票池
        
        # 资金设置
        self.initial_capital = 1_000_000  # 默认初始资金100万
        self.cash = self.initial_capital  # 可用资金
        
        # 交易设置
        self.commission_rate = 0.0003  # 佣金率，默认万3
        self.slippage = 0.0001  # 滑点，默认万1
        self.tax_rate = 0.001   # 印花税，默认千1
        self.min_commission = 5  # 最低佣金，默认5元
        
        # 基准设置
        self.benchmark = None  # 基准代码，如'000300.SH'
        self.benchmark_returns = None
        
        # 回测结果
        self._results = None
        
        # 行情数据
        self.market_data = None
        self.trading_calendar = None
        self.all_dates = None
        
        # 持仓数据
        self.positions = {}  # 持仓信息 {symbol: {quantity, cost, market_value}}
        self.position_history = []  # 持仓历史
        
        # 交易记录
        self.trades = []  # 交易记录 [{date, symbol, direction, price, quantity, amount, commission}]
        
        # 回测过程数据
        self.portfolio_value_history = []  # 组合价值历史
        self.cash_history = []  # 现金历史
        self.daily_returns = []  # 每日收益率
        self.daily_pnl = []  # 每日盈亏
        
        # 是否已准备数据
        self._data_prepared = False
        
    def set_parameters(self, **kwargs):
        """
        设置回测参数
        
        参数:
            **kwargs: 参数键值对
        """
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                raise ValueError(f"参数 '{key}' 不是有效的回测引擎参数")
        
        # 重置数据准备状态
        self._data_prepared = False
        return self
    
    def set_strategy(self, strategy: StrategyInterface, **strategy_params):
        """
        设置回测策略
        
        参数:
            strategy: 策略实例
            **strategy_params: 策略参数
        """
        self.strategy = strategy
        self.strategy_params = strategy_params
        return self
    
    def set_date_range(self, start_date, end_date):
        """
        设置回测日期范围
        
        参数:
            start_date: 开始日期 (str or datetime)
            end_date: 结束日期 (str or datetime)
        """
        # 转换为datetime格式
        if isinstance(start_date, str):
            self.start_date = pd.to_datetime(start_date)
        else:
            self.start_date = start_date
            
        if isinstance(end_date, str):
            self.end_date = pd.to_datetime(end_date)
        else:
            self.end_date = end_date
            
        # 重置数据准备状态
        self._data_prepared = False
        return self
    
    def set_universe(self, universe):
        """
        设置回测股票池
        
        参数:
            universe: 股票代码列表
        """
        if not isinstance(universe, list):
            raise ValueError("股票池必须是股票代码列表")
        
        self.universe = universe
        
        # 重置数据准备状态
        self._data_prepared = False
        return self
    
    def set_initial_capital(self, capital):
        """
        设置初始资金
        
        参数:
            capital: 初始资金金额
        """
        if capital <= 0:
            raise ValueError("初始资金必须大于0")
        
        self.initial_capital = capital
        self.cash = capital  # 同时更新可用资金
        return self
    
    def set_trading_cost(self, commission_rate=None, slippage=None, 
                        tax_rate=None, min_commission=None):
        """
        设置交易成本
        
        参数:
            commission_rate: 佣金率
            slippage: 滑点比例
            tax_rate: 印花税率
            min_commission: 最低佣金金额
        """
        if commission_rate is not None:
            self.commission_rate = commission_rate
        
        if slippage is not None:
            self.slippage = slippage
        
        if tax_rate is not None:
            self.tax_rate = tax_rate
            
        if min_commission is not None:
            self.min_commission = min_commission
            
        return self
    
    def set_benchmark(self, benchmark):
        """
        设置基准
        
        参数:
            benchmark: 基准代码，如'000300.SH'
        """
        self.benchmark = benchmark
        return self
    
    def prepare_data(self, market_data=None):
        """
        准备回测所需数据
        
        参数:
            market_data: 市场数据对象，如果为None则使用系统默认数据源
        """
        if not self.start_date or not self.end_date:
            raise ValueError("回测日期范围未设置")
            
        if not self.universe:
            raise ValueError("回测股票池未设置")
            
        # 设置市场数据源
        if market_data:
            self.market_data = market_data
        else:
            # 创建MarketData实例，无需从core模块导入
            self.market_data = MarketData()
        
        # 获取交易日历
        self.trading_calendar = self.market_data.get_trading_calendar(
            self.start_date, self.end_date)
        
        self.all_dates = self.trading_calendar
        
        # 如果设置了基准，获取基准数据
        if self.benchmark:
            try:
                benchmark_data = self.market_data.get_price(
                    symbol=self.benchmark,
                    start_date=self.start_date,
                    end_date=self.end_date,
                    fields=['close']
                )
                
                if not benchmark_data.empty:
                    # 计算基准日收益率
                    self.benchmark_returns = benchmark_data['close'].pct_change().dropna()
            except Exception as e:
                logger.warning(f"获取基准数据失败: {e}")
                self.benchmark_returns = None
        
        self._data_prepared = True
        return self
    
    def _get_trading_dates(self):
        """获取交易日期列表"""
        if not self._data_prepared:
            self.prepare_data()
        return self.all_dates
    
    def run(self):
        """
        运行回测
        
        返回:
            Dict: 回测结果
        """
        # 参数验证
        if not self.strategy:
            raise ValueError("策略未设置")
            
        if not self._data_prepared:
            self.prepare_data()
        
        try:
            # 初始化策略
            self.strategy.initialize(**self.strategy_params)
            
            # 执行回测
            self._run_backtest()
            
            # 计算回测结果
            self._calculate_results()
            
            return self._results
            
        except Exception as e:
            logger.error(f"回测执行失败: {e}", exc_info=True)
            raise RuntimeError(f"回测执行失败: {e}")
    
    def _run_backtest(self):
        """
        执行回测过程
        """
        # 重置回测状态
        self.cash = self.initial_capital
        self.positions = {}
        self.position_history = []
        self.portfolio_value_history = []
        self.cash_history = []
        self.daily_returns = []
        self.daily_pnl = []
        self.trades = []
        
        previous_total_value = self.initial_capital
        
        # 按日期进行回测
        trading_dates = self._get_trading_dates()
        
        for date_idx, date in enumerate(trading_dates):
            try:
                # 创建当天交易环境上下文
                context = self._create_context(date, date_idx)
                
                # 获取当日行情数据
                daily_data = self._get_daily_data(date)
                
                if daily_data.empty:
                    # 如果当日没有数据，跳过当日交易
                    logger.warning(f"日期 {date} 没有市场数据，跳过")
                    continue
                
                # 更新持仓市值
                self._update_portfolio(date, daily_data)
                
                # 计算当天组合总价值
                total_portfolio_value = self.cash
                for symbol, position in self.positions.items():
                    # 确保持仓股票存在于当天数据中
                    if symbol in daily_data.index:
                        price = daily_data.loc[symbol, 'close']
                        position['market_value'] = position['quantity'] * price
                        total_portfolio_value += position['market_value']
                
                # 记录当日资产状态
                self.portfolio_value_history.append({
                    'date': date,
                    'total_value': total_portfolio_value,
                    'cash': self.cash
                })
                
                # 计算当日收益率
                daily_return = (total_portfolio_value / previous_total_value) - 1 if previous_total_value > 0 else 0
                self.daily_returns.append({
                    'date': date,
                    'return': daily_return
                })
                
                # 计算当日盈亏
                daily_pnl = total_portfolio_value - previous_total_value
                self.daily_pnl.append({
                    'date': date,
                    'pnl': daily_pnl
                })
                
                previous_total_value = total_portfolio_value
                
                # 记录当日持仓
                self.position_history.append({
                    'date': date,
                    'positions': {symbol: pos.copy() for symbol, pos in self.positions.items()}
                })
                
                # 生成当日交易信号
                if hasattr(self.strategy, 'generate_signals') and callable(self.strategy.generate_signals):
                    signals = self.strategy.generate_signals(date=date, data=daily_data)
                    
                    # 执行交易订单
                    if signals:
                        for symbol, signal in signals.items():
                            # 忽略不在股票池中的信号
                            if symbol not in self.universe:
                                continue
                                
                            # 如果股票不在当日数据中，跳过
                            if symbol not in daily_data.index:
                                continue
                                
                            close_price = daily_data.loc[symbol, 'close']
                            
                            # 信号可以是方向+权重，也可以是目标持仓
                            if isinstance(signal, dict):
                                # 目标持仓模式
                                if 'target_weight' in signal:
                                    target_weight = signal['target_weight']
                                    target_value = total_portfolio_value * target_weight
                                    current_value = self.positions.get(symbol, {}).get('market_value', 0)
                                    
                                    # 计算需要调整的差值
                                    value_diff = target_value - current_value
                                    
                                    if abs(value_diff) > 100:  # 设置最小交易金额
                                        # 计算交易数量
                                        trade_price = close_price * (1 + self.slippage) if value_diff > 0 else close_price * (1 - self.slippage)
                                        quantity = int(value_diff / trade_price)
                                        
                                        # 执行交易
                                        if quantity != 0:
                                            self._execute_orders(date, symbol, close_price, quantity)
                            else:
                                # 简单信号模式 (1:买入, -1:卖出, 0:持有)
                                direction = signal
                                
                                if direction == 1:  # 买入信号
                                    # 简单策略：使用10%可用资金买入
                                    amount = self.cash * 0.1
                                    if amount > 1000:  # 设置最小交易金额
                                        # 计算可买数量
                                        buy_price = close_price * (1 + self.slippage)
                                        quantity = int(amount / buy_price)
                                        
                                        # 执行买入
                                        if quantity > 0:
                                            self._execute_orders(date, symbol, close_price, quantity)
                                            
                                elif direction == -1:  # 卖出信号
                                    # 如果持有该股票，全部卖出
                                    if symbol in self.positions and self.positions[symbol]['quantity'] > 0:
                                        quantity = -self.positions[symbol]['quantity']
                                        self._execute_orders(date, symbol, close_price, quantity)
                
            except Exception as e:
                logger.error(f"处理日期 {date} 时发生错误: {e}", exc_info=True)
    
    def _create_context(self, date, date_idx):
        """
        创建交易上下文
        
        参数:
            date: 当前日期
            date_idx: 日期索引
            
        返回:
            Dict: 交易上下文
        """
        return {
            'date': date,
            'date_idx': date_idx,
            'cash': self.cash,
            'positions': self.positions,
            'portfolio_value': sum(pos['market_value'] for pos in self.positions.values()) + self.cash
        }
    
    def _get_daily_data(self, date):
        """
        获取当日行情数据
        
        参数:
            date: 当前日期
            
        返回:
            DataFrame: 当日行情数据
        """
        try:
            # 获取股票池当日行情
            data = self.market_data.get_price(
                symbol=self.universe,
                start_date=date,
                end_date=date,
                fields=['open', 'high', 'low', 'close', 'volume']
            )
            
            if not data.empty:
                # 数据透视，使股票代码成为索引
                if 'symbol' in data.columns:
                    data = data.set_index('symbol')
                
            return data
            
        except Exception as e:
            logger.error(f"获取日期 {date} 行情数据失败: {e}", exc_info=True)
            return pd.DataFrame()
    
    def _get_price(self, symbol, date, field='close'):
        """
        获取指定股票在指定日期的价格
        
        参数:
            symbol: 股票代码
            date: 日期
            field: 价格字段，默认为'close'
            
        返回:
            float: 价格
        """
        try:
            price_data = self.market_data.get_price(
                symbol=symbol,
                start_date=date,
                end_date=date,
                fields=[field]
            )
            
            if not price_data.empty:
                return price_data[field].iloc[0]
            else:
                return None
        
        except Exception as e:
            logger.error(f"获取 {symbol} 在 {date} 的 {field} 价格失败: {e}")
            return None
    
    def _update_portfolio(self, date, daily_data):
        """
        更新组合持仓市值
        
        参数:
            date: 当前日期
            daily_data: 当日行情数据
        """
        for symbol, position in list(self.positions.items()):
            # 如果持仓数量为0，则移除该持仓
            if position['quantity'] <= 0:
                del self.positions[symbol]
                continue
                
            # 更新持仓市值
            if symbol in daily_data.index:
                close_price = daily_data.loc[symbol, 'close']
                position['market_value'] = position['quantity'] * close_price
                position['price'] = close_price
            else:
                # 如果当日数据中没有该股票，尝试单独获取
                price = self._get_price(symbol, date)
                if price:
                    position['market_value'] = position['quantity'] * price
                    position['price'] = price
                else:
                    # 如果无法获取价格，保持原有市值
                    logger.warning(f"无法获取 {symbol} 在 {date} 的价格数据")
    
    def _execute_orders(self, date, symbol, price, quantity):
        """
        执行交易订单
        
        参数:
            date: 交易日期
            symbol: 股票代码
            price: 交易价格
            quantity: 交易数量，正数为买入，负数为卖出
        """
        # 计算交易金额
        amount = abs(quantity) * price
        
        # 计算交易成本
        commission = self._calculate_commission(amount)
        slippage_cost = self._calculate_slippage(price, quantity)
        tax = self._calculate_tax(amount, quantity < 0)
        
        total_cost = commission + slippage_cost + tax
        
        # 买入
        if quantity > 0:
            # 检查资金是否足够
            total_amount = amount + total_cost
            if total_amount > self.cash:
                # 资金不足，调整购买数量
                adjusted_quantity = int((self.cash - commission) / (price * (1 + self.slippage)))
                if adjusted_quantity <= 0:
                    return  # 资金不足以购买任何股票
                
                quantity = adjusted_quantity
                amount = quantity * price
                
                # 重新计算成本
                commission = self._calculate_commission(amount)
                slippage_cost = self._calculate_slippage(price, quantity)
                total_cost = commission + slippage_cost  # 买入无印花税
            
            # 更新资金
            self.cash -= (amount + total_cost)
            
            # 更新持仓
            if symbol not in self.positions:
                self.positions[symbol] = {
                    'quantity': quantity,
                    'cost': amount,
                    'price': price,
                    'market_value': amount
                }
            else:
                # 平均成本计算
                current_quantity = self.positions[symbol]['quantity']
                current_cost = self.positions[symbol]['cost']
                
                new_quantity = current_quantity + quantity
                new_cost = current_cost + amount
                
                self.positions[symbol]['quantity'] = new_quantity
                self.positions[symbol]['cost'] = new_cost
                self.positions[symbol]['price'] = price
                self.positions[symbol]['market_value'] = new_quantity * price
        
        # 卖出
        elif quantity < 0:
            # 确保持仓足够
            if symbol not in self.positions or abs(quantity) > self.positions[symbol]['quantity']:
                # 持仓不足，调整卖出数量
                if symbol in self.positions:
                    quantity = -self.positions[symbol]['quantity']
                else:
                    return  # 没有持仓可卖
            
            # 更新资金
            amount = abs(quantity) * price
            commission = self._calculate_commission(amount)
            slippage_cost = self._calculate_slippage(price, quantity)
            tax = self._calculate_tax(amount, True)
            
            total_cost = commission + slippage_cost + tax
            self.cash += (amount - total_cost)
            
            # 更新持仓
            current_quantity = self.positions[symbol]['quantity']
            new_quantity = current_quantity + quantity  # quantity是负数
            
            if new_quantity <= 0:
                # 清仓
                del self.positions[symbol]
            else:
                # 部分卖出，成本按比例减少
                ratio = abs(quantity) / current_quantity
                cost_reduction = self.positions[symbol]['cost'] * ratio
                
                self.positions[symbol]['quantity'] = new_quantity
                self.positions[symbol]['cost'] -= cost_reduction
                self.positions[symbol]['price'] = price
                self.positions[symbol]['market_value'] = new_quantity * price
        
        # 记录交易
        self.trades.append({
            'date': date,
            'symbol': symbol,
            'direction': 'buy' if quantity > 0 else 'sell',
            'price': price,
            'quantity': abs(quantity),
            'amount': amount,
            'commission': commission,
            'slippage': slippage_cost,
            'tax': tax,
            'total_cost': total_cost
        })
    
    def _calculate_commission(self, amount):
        """
        计算佣金
        
        参数:
            amount: 交易金额
            
        返回:
            float: 佣金金额
        """
        commission = amount * self.commission_rate
        return max(commission, self.min_commission) if commission > 0 else 0
    
    def _calculate_slippage(self, price, quantity):
        """
        计算滑点成本
        
        参数:
            price: 交易价格
            quantity: 交易数量
            
        返回:
            float: 滑点成本
        """
        # 买入时价格上浮，卖出时价格下浮
        direction = 1 if quantity > 0 else -1
        slippage_cost = price * abs(quantity) * self.slippage * direction
        return abs(slippage_cost)
    
    def _calculate_tax(self, amount, is_sell):
        """
        计算交易税费（如印花税）
        
        参数:
            amount: 交易金额
            is_sell: 是否为卖出交易
            
        返回:
            float: 税费金额
        """
        # 目前A股只有卖出收取印花税
        if is_sell:
            return amount * self.tax_rate
        return 0
    
    def _calculate_results(self):
        """
        计算回测结果
        """
        # 转换每日收益为DataFrame
        daily_returns_df = pd.DataFrame(self.daily_returns)
        if not daily_returns_df.empty:
            daily_returns_df = daily_returns_df.set_index('date')
            returns_series = daily_returns_df['return']
            
            # 计算累积收益
            cumulative_returns = (1 + returns_series).cumprod() - 1
            
            # 生成交易记录DataFrame
            trades_df = pd.DataFrame(self.trades)
            if not trades_df.empty:
                trades_df = trades_df.set_index('date')
            
            # 生成持仓历史DataFrame
            positions_df = pd.DataFrame([
                {'date': entry['date'], 'symbol': symbol, **pos}
                for entry in self.position_history
                for symbol, pos in entry['positions'].items()
            ])
            
            # 整合每日资产价值历史
            portfolio_df = pd.DataFrame(self.portfolio_value_history)
            if not portfolio_df.empty:
                portfolio_df = portfolio_df.set_index('date')
            
            # 如果有基准收益，确保日期对齐
            if self.benchmark_returns is not None:
                # 确保基准收益覆盖回测期间
                aligned_benchmark = self.benchmark_returns.reindex(returns_series.index)
                self.benchmark_returns = aligned_benchmark
            
            # 计算性能指标
            performance_metrics = calculate_performance_metrics(
                returns=returns_series,
                benchmark_returns=self.benchmark_returns,
                risk_free_rate=0.0  # 可以设置为无风险利率
            )
            
            # 汇总回测结果
            self._results = {
                'strategy_name': getattr(self.strategy, 'name', '未命名策略'),
                'start_date': self.start_date,
                'end_date': self.end_date,
                'initial_capital': self.initial_capital,
                'final_capital': portfolio_df['total_value'].iloc[-1] if not portfolio_df.empty else 0,
                'returns': returns_series,
                'cumulative_returns': cumulative_returns,
                'benchmark_returns': self.benchmark_returns,
                'trades': trades_df,
                'positions': positions_df if not positions_df.empty else pd.DataFrame(),
                'portfolio_history': portfolio_df,
                'performance_metrics': performance_metrics
            }
        else:
            # 没有足够的数据生成结果
            self._results = {
                'strategy_name': getattr(self.strategy, 'name', '未命名策略'),
                'start_date': self.start_date,
                'end_date': self.end_date,
                'initial_capital': self.initial_capital,
                'final_capital': self.initial_capital,
                'returns': pd.Series(),
                'cumulative_returns': pd.Series(),
                'benchmark_returns': None,
                'trades': pd.DataFrame(),
                'positions': pd.DataFrame(),
                'portfolio_history': pd.DataFrame(),
                'performance_metrics': {}
            }
    
    def generate_report(self, output_dir=None, filename=None, format='html'):
        """
        生成回测报告
        
        参数:
            output_dir: 输出目录，默认为当前目录下的 'reports'
            filename: 输出文件名，默认为 'backtest_report_{timestamp}'
            format: 输出格式，'html'或'pdf'
            
        返回:
            str: 报告文件路径或HTML字符串
        """
        if self._results is None:
            raise ValueError("尚未执行回测，请先调用run()方法")
        
        # 默认输出目录
        if output_dir is None:
            output_dir = os.path.join(os.getcwd(), 'reports')
        
        # 确保目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 默认文件名
        if filename is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            strategy_name = self._results['strategy_name'].replace(' ', '_')
            filename = f"{strategy_name}_report_{timestamp}"
        
        # 生成报告
        report = PerformanceReport(
            returns=self._results['returns'],
            benchmark_returns=self._results['benchmark_returns'],
            positions=self._results['positions'],
            trades=self._results['trades'],
            risk_free_rate=0.0,  # 可以设置为无风险利率
            strategy_name=self._results['strategy_name']
        )
        
        # 生成并返回报告
        return report.generate_report(
            output_dir=output_dir,
            filename=filename,
            format=format
        )

# 注册向量化回测引擎到工厂
from .factory import BacktestEngineFactory
BacktestEngineFactory.register_engine('vector', VectorBacktestEngine)

