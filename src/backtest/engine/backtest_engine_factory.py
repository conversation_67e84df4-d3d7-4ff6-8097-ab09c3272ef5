"""
回测引擎工厂（兼容性包装）
保持向后兼容性，内部使用统一工厂
"""

from typing import Dict, Any, Optional, Union, Type, List
import importlib
from .backtest_engine_interface import BacktestEngineInterface

# {{ AURA-X: Add - 导入统一工厂，消除重复代码. Approval: 寸止(ID:BacktestEngineFactory整合). }}
from .unified_factory import UnifiedBacktestEngineFactory, get_backtest_engine_factory

class BacktestEngineFactory:
    """
    回测引擎工厂类（兼容性包装）
    保持向后兼容性，内部使用统一工厂
    """

    @classmethod
    def _get_factory(cls) -> UnifiedBacktestEngineFactory:
        """获取统一工厂实例"""
        return get_backtest_engine_factory()
    
    @classmethod
    def create(cls, engine_type: str, config: Dict[str, Any] = None) -> BacktestEngineInterface:
        """
        创建指定类型的回测引擎实例（兼容性方法）

        参数：
            engine_type: 引擎类型，如'vector', 'event_driven', 'a_stock'等
            config: 引擎配置参数

        返回：
            BacktestEngineInterface: 回测引擎实例

        异常：
            ValueError: 引擎类型不支持时抛出
            ImportError: 无法导入对应模块时抛出
        """
        # {{ AURA-X: Modify - 委托给统一工厂，消除重复的引擎创建逻辑. Approval: 寸止(ID:BacktestEngineFactory整合). }}
        return cls._get_factory().create_engine(engine_type, config)
    
    @classmethod
    def create_from_config(cls, config: Dict[str, Any]) -> BacktestEngineInterface:
        """
        从配置字典创建回测引擎实例（兼容性方法）

        参数：
            config: 配置字典，必须包含'type'字段

        返回：
            BacktestEngineInterface: 回测引擎实例

        异常：
            ValueError: 配置不正确时抛出
        """
        # {{ AURA-X: Modify - 委托给统一工厂，消除重复的配置解析逻辑. Approval: 寸止(ID:BacktestEngineFactory整合). }}
        return cls._get_factory().create_from_config(config)

    @classmethod
    def create_default(cls, config: Optional[Dict[str, Any]] = None) -> BacktestEngineInterface:
        """
        创建默认回测引擎（向量化引擎）（兼容性方法）

        参数：
            config: 引擎配置参数

        返回：
            BacktestEngineInterface: 回测引擎实例
        """
        # {{ AURA-X: Modify - 委托给统一工厂，消除重复的默认引擎创建逻辑. Approval: 寸止(ID:BacktestEngineFactory整合). }}
        return cls._get_factory().create_default(config)
    
    @classmethod
    def create_for_market(cls, market: str, config: Optional[Dict[str, Any]] = None) -> BacktestEngineInterface:
        """
        创建适合特定市场的回测引擎（兼容性方法）

        参数：
            market: 市场类型，如'A', 'futures', 'crypto'等
            config: 引擎配置参数

        返回：
            BacktestEngineInterface: 回测引擎实例

        异常：
            ValueError: 市场类型不支持时抛出
        """
        # {{ AURA-X: Modify - 委托给统一工厂，消除重复的市场映射逻辑. Approval: 寸止(ID:BacktestEngineFactory整合). }}
        return cls._get_factory().create_for_market(market, config)
    
    @classmethod
    def register_engine(cls, engine_type: str, module_path: str) -> None:
        """
        注册新的回测引擎类型（兼容性方法）

        参数：
            engine_type: 引擎类型名称
            module_path: 模块路径，格式为'package.module.Class'

        异常：
            ValueError: 引擎类型已存在时抛出
        """
        # {{ AURA-X: Modify - 委托给统一工厂，消除重复的引擎注册逻辑. Approval: 寸止(ID:BacktestEngineFactory整合). }}
        cls._get_factory().register_engine_path(engine_type, module_path)

    @classmethod
    def get_available_engines(cls) -> Dict[str, str]:
        """
        获取所有可用的引擎类型（兼容性方法）

        返回：
            Dict[str, str]: 引擎类型到模块路径的映射
        """
        # {{ AURA-X: Modify - 委托给统一工厂，消除重复的引擎列表逻辑. Approval: 寸止(ID:BacktestEngineFactory整合). }}
        return cls._get_factory().get_available_engines()

