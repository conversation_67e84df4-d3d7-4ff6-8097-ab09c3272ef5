#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一回测引擎工厂
整合原有的BacktestEngineFactory功能，消除重复代码
"""

import importlib
import logging
from typing import Dict, Type, Optional, Any, Union, List, Tuple
from .engine_interface import BacktestEngineInterface
from src.utils.logging.logger_factory import get_logger

logger = get_logger(__name__)


class UnifiedBacktestEngineFactory:
    """
    统一回测引擎工厂
    
    整合注册式工厂和字符串路径工厂的功能，支持：
    1. 直接类对象注册
    2. 字符串路径注册（动态导入）
    3. 配置文件创建
    4. 市场特定引擎创建
    5. 统一的引擎管理接口
    """
    
    def __init__(self):
        """初始化统一回测引擎工厂"""
        # 存储引擎注册信息：{name: (type, reference, default_config)}
        self._engines: Dict[str, Tuple[str, Union[Type, str], Dict[str, Any]]] = {}

        # {{ AURA-X: Modify - 增强市场类型映射，支持更多市场和别名. Approval: 寸止(ID:架构合规性修正). }}
        # 增强的市场类型映射
        self._market_mappings = {
            # A股市场
            'A': 'a_stock', 'a': 'a_stock', 'china': 'a_stock',
            'SH': 'a_stock', 'SZ': 'a_stock', 'shanghai': 'a_stock', 'shenzhen': 'a_stock',

            # 期货市场
            'futures': 'futures', 'CFFEX': 'futures', 'DCE': 'futures', 'CZCE': 'futures', 'SHFE': 'futures',
            'commodity': 'futures', 'financial_futures': 'futures',

            # 加密货币
            'crypto': 'crypto', 'bitcoin': 'crypto', 'ethereum': 'crypto', 'digital': 'crypto',
            'cryptocurrency': 'crypto', 'btc': 'crypto', 'eth': 'crypto',

            # 国际市场
            'us': 'vector', 'nasdaq': 'vector', 'nyse': 'vector', 'america': 'vector',
            'hk': 'vector', 'hkex': 'vector', 'hongkong': 'vector',
            'jp': 'vector', 'japan': 'vector', 'nikkei': 'vector',
            'eu': 'vector', 'europe': 'vector', 'london': 'vector'
        }

        # 市场特定配置
        self._market_configs = {
            'a_stock': {
                'trading_hours': {'start': '09:30', 'end': '15:00'},
                'commission_rate': 0.0003,
                'min_commission': 5.0,
                'stamp_tax': 0.001,
                'currency': 'CNY'
            },
            'futures': {
                'trading_hours': {'start': '09:00', 'end': '15:15'},
                'commission_rate': 0.0001,
                'margin_rate': 0.1,
                'currency': 'CNY'
            },
            'crypto': {
                'trading_hours': {'start': '00:00', 'end': '23:59'},
                'commission_rate': 0.001,
                'currency': 'USDT',
                '24h_trading': True
            },
            'vector': {
                'trading_hours': {'start': '09:30', 'end': '16:00'},
                'commission_rate': 0.0005,
                'currency': 'USD'
            }
        }

        logger.info("初始化统一回测引擎工厂")

        # 注册默认引擎
        self._register_default_engines()
    
    def _register_default_engines(self):
        """注册默认的回测引擎"""
        # {{ AURA-X: Modify - 暂时跳过有问题的导入，专注于工厂整合测试. Approval: 寸止(ID:BacktestEngineFactory整合). }}
        # 使用字符串路径注册，避免导入问题
        self.register_engine_path('vector', 'src.backtest.engine.vector_engine.VectorBacktestEngine')
        self.register_engine_path('vectorized', 'src.backtest.engine.vector_engine.VectorBacktestEngine')
        
        # 注册其他引擎（字符串路径方式）
        engine_mappings = {
            # 事件驱动引擎
            'event': 'src.backtest.engine.event_engine.EventEngine',
            'event_driven': 'src.backtest.engine.event_engine.EventEngine',
            
            # 特定市场引擎
            'a_stock': 'src.backtest.engine.a_stock_engine.AStockEngine',
            'futures': 'src.backtest.engine.futures_engine.FuturesEngine',
            'crypto': 'src.backtest.engine.crypto_engine.CryptoEngine',
            
            # 高级引擎
            'realtime': 'src.backtest.engine.realtime_engine.RealtimeEngine',
            'parallel': 'src.backtest.engine.parallel_engine.ParallelEngine'
        }
        
        for name, path in engine_mappings.items():
            self.register_engine_path(name, path)
    
    def register_engine_class(self, 
                            name: str, 
                            engine_class: Type[BacktestEngineInterface],
                            default_config: Optional[Dict[str, Any]] = None) -> None:
        """
        注册回测引擎类对象
        
        参数:
            name: 引擎名称
            engine_class: 引擎类对象
            default_config: 默认配置
        """
        if name in self._engines:
            logger.warning(f"引擎类型 '{name}' 已存在，将被覆盖")
        
        self._engines[name] = ('class', engine_class, default_config or {})
        logger.info(f"注册引擎类: {name}")
    
    def register_engine_path(self, 
                           name: str, 
                           module_path: str,
                           default_config: Optional[Dict[str, Any]] = None) -> None:
        """
        注册回测引擎字符串路径
        
        参数:
            name: 引擎名称
            module_path: 模块路径，格式为'package.module.Class'
            default_config: 默认配置
        """
        if name in self._engines:
            logger.warning(f"引擎类型 '{name}' 已存在，将被覆盖")
        
        self._engines[name] = ('path', module_path, default_config or {})
        logger.info(f"注册引擎路径: {name} -> {module_path}")

    def register_from_config(self, config_path: str) -> None:
        """
        从配置文件批量注册引擎

        参数:
            config_path: 配置文件路径
        """
        # {{ AURA-X: Add - 添加配置驱动的批量注册功能. Approval: 寸止(ID:架构合规性修正). }}
        try:
            from src.config.config_factory import ConfigFactory
            config = ConfigFactory.load_config(config_path)

            engines_config = config.get('backtest_engines', {})
            for engine_name, engine_config in engines_config.items():
                if 'module_path' in engine_config:
                    self.register_engine_path(
                        engine_name,
                        engine_config['module_path'],
                        engine_config.get('default_config', {})
                    )
                elif 'class' in engine_config:
                    # 动态导入类并注册
                    engine_class = self._import_engine_class(engine_config['class'])
                    self.register_engine_class(
                        engine_name,
                        engine_class,
                        engine_config.get('default_config', {})
                    )

            logger.info(f"从配置文件 {config_path} 注册了 {len(engines_config)} 个引擎")

        except Exception as e:
            logger.error(f"从配置文件注册引擎失败: {e}")
            raise

    def register_engine_from_path(self, name: str, class_path: str, **kwargs) -> None:
        """
        从类路径字符串注册引擎（便捷方法）

        参数:
            name: 引擎名称
            class_path: 类路径，格式为'module.Class'
            **kwargs: 默认配置参数
        """
        self.register_engine_path(name, class_path, kwargs)
    
    def create_engine(self, 
                     name: str, 
                     config: Optional[Dict[str, Any]] = None,
                     **kwargs) -> BacktestEngineInterface:
        """
        创建回测引擎实例
        
        参数:
            name: 引擎名称
            config: 引擎配置
            **kwargs: 额外的引擎参数
            
        返回:
            BacktestEngineInterface: 回测引擎实例
        """
        if name not in self._engines:
            available = ', '.join(self._engines.keys())
            raise ValueError(f"未知的回测引擎类型: {name}，可用类型: {available}")
        
        engine_type, engine_ref, default_config = self._engines[name]
        
        # 获取引擎类
        if engine_type == 'class':
            engine_class = engine_ref
        else:  # engine_type == 'path'
            engine_class = self._import_engine_class(engine_ref)
        
        # 合并配置
        final_config = {}
        final_config.update(default_config)
        if config:
            final_config.update(config)
        final_config.update(kwargs)
        
        try:
            engine = engine_class(**final_config)
            logger.info(f"创建回测引擎: {name}")
            return engine
        except Exception as e:
            logger.error(f"创建回测引擎 {name} 失败: {e}")
            raise
    
    def _import_engine_class(self, module_path: str) -> Type[BacktestEngineInterface]:
        """
        增强的动态导入引擎类功能

        参数:
            module_path: 模块路径，支持多种格式

        返回:
            引擎类
        """
        try:
            # {{ AURA-X: Modify - 增强动态导入，支持多种路径格式和自动推断. Approval: 寸止(ID:架构合规性修正). }}
            # 支持多种路径格式
            if '.' in module_path:
                module_name, class_name = module_path.rsplit('.', 1)
            else:
                # 自动推断类名
                module_name = module_path
                class_name = self._infer_class_name(module_path)

            # 动态导入模块
            module = importlib.import_module(module_name)
            engine_class = getattr(module, class_name)

            # 验证是否实现了正确的接口
            if not issubclass(engine_class, BacktestEngineInterface):
                raise ValueError(f"引擎类 {class_name} 必须继承 BacktestEngineInterface")

            logger.debug(f"成功导入引擎类: {module_path}")
            return engine_class

        except (ImportError, AttributeError) as e:
            logger.error(f"导入引擎类失败: {module_path}, 错误: {e}")
            raise ImportError(f"无法导入回测引擎 {module_path}: {e}")
        except ValueError as e:
            logger.error(f"引擎类验证失败: {e}")
            raise

    def _infer_class_name(self, module_path: str) -> str:
        """
        从模块路径推断类名

        参数:
            module_path: 模块路径

        返回:
            推断的类名
        """
        # 获取模块名的最后一部分
        module_name = module_path.split('.')[-1]

        # 转换为驼峰命名
        if '_' in module_name:
            parts = module_name.split('_')
            class_name = ''.join(word.capitalize() for word in parts)
        else:
            class_name = module_name.capitalize()

        # 常见的类名模式
        if not class_name.endswith('Engine'):
            class_name += 'Engine'

        return class_name
    
    def create_from_config(self, config: Dict[str, Any]) -> BacktestEngineInterface:
        """
        从配置字典创建回测引擎
        
        参数:
            config: 配置字典，必须包含'type'字段
            
        返回:
            BacktestEngineInterface: 回测引擎实例
        """
        if 'type' not in config:
            raise ValueError("配置必须包含'type'字段")
        
        engine_config = config.copy()
        engine_type = engine_config.pop('type')
        
        return self.create_engine(engine_type, engine_config)
    
    def create_default(self, config: Optional[Dict[str, Any]] = None) -> BacktestEngineInterface:
        """
        创建默认回测引擎（向量化引擎）
        
        参数:
            config: 引擎配置
            
        返回:
            BacktestEngineInterface: 回测引擎实例
        """
        return self.create_engine('vector', config)
    
    def create_for_market(self,
                         market: str,
                         config: Optional[Dict[str, Any]] = None) -> BacktestEngineInterface:
        """
        创建适合特定市场的回测引擎

        参数:
            market: 市场类型
            config: 引擎配置

        返回:
            BacktestEngineInterface: 回测引擎实例
        """
        # {{ AURA-X: Modify - 增强市场特定引擎创建，自动添加市场配置. Approval: 寸止(ID:架构合规性修正). }}
        market_key = market.upper()
        if market_key not in self._market_mappings:
            available = ', '.join(self._market_mappings.keys())
            raise ValueError(f"不支持的市场类型: {market}，支持的类型: {available}")

        engine_type = self._market_mappings[market_key]

        # 自动添加市场特定配置
        market_config = self._get_market_config(engine_type)
        final_config = {}
        final_config.update(market_config)
        if config:
            final_config.update(config)

        logger.info(f"为市场 {market} 创建 {engine_type} 引擎")
        return self.create_engine(engine_type, final_config)

    def _get_market_config(self, engine_type: str) -> Dict[str, Any]:
        """
        获取市场特定配置

        参数:
            engine_type: 引擎类型

        返回:
            市场配置字典
        """
        return self._market_configs.get(engine_type, {}).copy()
    
    def get_available_engines(self) -> Dict[str, str]:
        """
        获取所有可用的引擎类型
        
        返回:
            引擎类型到描述的映射
        """
        result = {}
        for name, (engine_type, engine_ref, _) in self._engines.items():
            if engine_type == 'class':
                description = f"类对象: {engine_ref.__name__}"
            else:
                description = f"模块路径: {engine_ref}"
            result[name] = description
        
        return result
    
    def get_engine_info(self, name: str) -> Dict[str, Any]:
        """
        获取特定引擎的详细信息
        
        参数:
            name: 引擎名称
            
        返回:
            引擎信息字典
        """
        if name not in self._engines:
            raise ValueError(f"未知的引擎类型: {name}")
        
        engine_type, engine_ref, default_config = self._engines[name]
        
        return {
            'name': name,
            'type': engine_type,
            'reference': str(engine_ref),
            'default_config': default_config,
            'available': self._is_engine_available(name)
        }
    
    def _is_engine_available(self, name: str) -> bool:
        """
        检查引擎是否可用
        
        参数:
            name: 引擎名称
            
        返回:
            是否可用
        """
        try:
            engine_type, engine_ref, _ = self._engines[name]
            if engine_type == 'class':
                return True
            else:
                self._import_engine_class(engine_ref)
                return True
        except Exception:
            return False
    
    def list_available_engines(self) -> List[str]:
        """
        列出所有可用的引擎名称
        
        返回:
            可用引擎名称列表
        """
        available = []
        for name in self._engines.keys():
            if self._is_engine_available(name):
                available.append(name)
        return available
    
    def remove_engine(self, name: str) -> bool:
        """
        移除已注册的引擎
        
        参数:
            name: 引擎名称
            
        返回:
            是否成功移除
        """
        if name in self._engines:
            del self._engines[name]
            logger.info(f"移除引擎: {name}")
            return True
        return False


# 创建全局工厂实例
_global_factory = UnifiedBacktestEngineFactory()


def create_backtest_engine(engine_type: str = 'vector', **kwargs) -> BacktestEngineInterface:
    """
    创建回测引擎的便捷函数
    
    参数:
        engine_type: 引擎类型，默认为'vector'
        **kwargs: 引擎参数
        
    返回:
        BacktestEngineInterface: 回测引擎实例
    """
    return _global_factory.create_engine(engine_type, **kwargs)


def get_backtest_engine_factory() -> UnifiedBacktestEngineFactory:
    """
    获取全局回测引擎工厂实例
    
    返回:
        UnifiedBacktestEngineFactory: 工厂实例
    """
    return _global_factory


# 向后兼容性：提供原有的BacktestEngineFactory接口
class BacktestEngineFactory:
    """向后兼容的回测引擎工厂接口"""
    
    @classmethod
    def register_engine(cls, engine_type: str, engine_class: Type[BacktestEngineInterface]) -> None:
        """注册引擎类型（兼容性方法）"""
        _global_factory.register_engine_class(engine_type, engine_class)
    
    @classmethod
    def create_engine(cls, engine_type: str, **kwargs) -> BacktestEngineInterface:
        """创建引擎实例（兼容性方法）"""
        return _global_factory.create_engine(engine_type, **kwargs)
    
    @classmethod
    def get_available_engines(cls) -> Dict[str, str]:
        """获取可用引擎（兼容性方法）"""
        return _global_factory.get_available_engines()
    
    @classmethod
    def create(cls, engine_type: str, config: Dict[str, Any] = None) -> BacktestEngineInterface:
        """创建引擎（兼容性方法）"""
        return _global_factory.create_engine(engine_type, config)
    
    @classmethod
    def create_from_config(cls, config: Dict[str, Any]) -> BacktestEngineInterface:
        """从配置创建（兼容性方法）"""
        return _global_factory.create_from_config(config)
    
    @classmethod
    def create_default(cls, config: Optional[Dict[str, Any]] = None) -> BacktestEngineInterface:
        """创建默认引擎（兼容性方法）"""
        return _global_factory.create_default(config)
    
    @classmethod
    def create_for_market(cls, market: str, config: Optional[Dict[str, Any]] = None) -> BacktestEngineInterface:
        """为市场创建引擎（兼容性方法）"""
        return _global_factory.create_for_market(market, config)
