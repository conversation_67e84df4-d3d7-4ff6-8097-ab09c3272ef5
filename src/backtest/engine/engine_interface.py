"""
回测引擎接口定义
- 定义所有回测引擎必须实现的接口
- 提供统一的方法签名
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List

class BacktestEngineInterface(ABC):
    """
    回测引擎接口
    
    所有回测引擎都必须实现这个接口
    """
    
    @abstractmethod
    def set_parameters(self, **kwargs):
        """
        设置回测参数
        
        参数:
            **kwargs: 参数键值对
        
        返回:
            self，便于链式调用
        """
        pass
    
    @abstractmethod
    def set_strategy(self, strategy, **strategy_params):
        """
        设置回测策略
        
        参数:
            strategy: 策略实例
            **strategy_params: 策略参数
        
        返回:
            self，便于链式调用
        """
        pass
    
    @abstractmethod
    def set_date_range(self, start_date, end_date):
        """
        设置回测日期范围
        
        参数:
            start_date: 开始日期
            end_date: 结束日期
        
        返回:
            self，便于链式调用
        """
        pass
    
    @abstractmethod
    def set_universe(self, universe):
        """
        设置回测股票池
        
        参数:
            universe: 股票代码列表
        
        返回:
            self，便于链式调用
        """
        pass
    
    @abstractmethod
    def set_initial_capital(self, capital):
        """
        设置初始资金
        
        参数:
            capital: 初始资金金额
        
        返回:
            self，便于链式调用
        """
        pass
    
    @abstractmethod
    def prepare_data(self, market_data=None):
        """
        准备回测所需数据
        
        参数:
            market_data: 市场数据对象，如果为None则使用系统默认数据源
        
        返回:
            self，便于链式调用
        """
        pass
    
    @abstractmethod
    def run(self):
        """
        运行回测
        
        返回:
            Dict: 回测结果
        """
        pass
    
    @abstractmethod
    def generate_report(self, output_dir=None, filename=None, format='html'):
        """
        生成回测报告
        
        参数:
            output_dir: 输出目录
            filename: 输出文件名
            format: 输出格式
        
        返回:
            str: 报告文件路径或HTML字符串
        """
        pass 