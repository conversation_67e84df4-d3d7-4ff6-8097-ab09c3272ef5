"""
回测引擎接口抽象类：定义所有回测引擎的标准接口
- 提供统一的回测执行方法
- 支持回测参数设置
- 定义回测结果获取接口
- 提供回测进度跟踪功能
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Set, Tuple, TypeVar, Generic, Callable, Iterator
import pandas as pd
from datetime import datetime, date

# 泛型参数定义
T = TypeVar('T')

class BacktestException(Exception):
    """回测异常的基类"""
    pass

class InitializationError(BacktestException):
    """初始化异常"""
    pass

class ParameterError(BacktestException):
    """参数错误"""
    pass

class ExecutionError(BacktestException):
    """执行异常"""
    pass

class DataError(BacktestException):
    """数据错误"""
    pass

class BacktestEngineInterface(ABC):
    """
    回测引擎接口抽象类，所有回测引擎必须实现此接口
    定义了回测引擎的基本操作，包括参数设置、回测执行和结果获取等
    """
    
    @abstractmethod
    def set_parameters(self, **params) -> None:
        """
        设置回测参数
        
        参数：
            **params: 回测参数字典，可能包括：
                trade_cost: 交易成本
                slippage: 滑点设置
                commission: 佣金设置
                minimum_cost: 最低交易成本
                position_limit: 持仓限制
                risk_control: 风险控制设置
                
        异常：
            ParameterError: 参数无效时抛出
        """
        pass
    
    @abstractmethod
    def set_strategy(self, strategy: Any) -> None:
        """
        设置回测策略
        
        参数：
            strategy: 策略对象
            
        异常：
            ParameterError: 策略无效时抛出
        """
        pass
    
    @abstractmethod
    def set_date_range(self, start_date: Union[str, datetime, date], 
                       end_date: Union[str, datetime, date]) -> None:
        """
        设置回测日期范围
        
        参数：
            start_date: 开始日期
            end_date: 结束日期
            
        异常：
            ParameterError: 日期范围无效时抛出
        """
        pass
    
    @abstractmethod
    def set_universe(self, universe: List[str]) -> None:
        """
        设置回测证券池
        
        参数：
            universe: 证券代码列表
            
        异常：
            ParameterError: 证券池无效时抛出
        """
        pass
    
    @abstractmethod
    def set_initial_capital(self, capital: float) -> None:
        """
        设置初始资金
        
        参数：
            capital: 初始资金金额
            
        异常：
            ParameterError: 初始资金无效时抛出
        """
        pass
    
    @abstractmethod
    def run(self) -> Dict[str, Any]:
        """
        运行回测
        
        返回：
            Dict[str, Any]: 回测结果，包括收益率、持仓、交易记录等
            
        异常：
            ExecutionError: 回测执行失败时抛出
            DataError: 数据错误时抛出
            InitializationError: 初始化失败时抛出
        """
        pass
    
    @abstractmethod
    def get_positions(self) -> pd.DataFrame:
        """
        获取回测持仓记录
        
        返回：
            pd.DataFrame: 持仓数据，索引为日期，列为证券代码，值为持仓数量或权重
            
        异常：
            ExecutionError: 回测未完成时抛出
        """
        pass
    
    @abstractmethod
    def get_trades(self) -> pd.DataFrame:
        """
        获取回测交易记录
        
        返回：
            pd.DataFrame: 交易记录，包含订单信息、成交价格、成交数量等
            
        异常：
            ExecutionError: 回测未完成时抛出
        """
        pass
    
    @abstractmethod
    def get_performance(self) -> Dict[str, Any]:
        """
        获取回测绩效指标
        
        返回：
            Dict[str, Any]: 绩效指标字典，包含收益率、夏普比率、最大回撤等
            
        异常：
            ExecutionError: 回测未完成时抛出
        """
        pass
    
    def get_equity_curve(self) -> pd.Series:
        """
        获取回测权益曲线
        
        返回：
            pd.Series: 权益曲线，索引为日期，值为账户总价值
            
        异常：
            ExecutionError: 回测未完成时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def get_returns(self) -> pd.Series:
        """
        获取回测收益率序列
        
        返回：
            pd.Series: 收益率序列，索引为日期，值为日收益率
            
        异常：
            ExecutionError: 回测未完成时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def get_benchmark_returns(self) -> pd.Series:
        """
        获取基准收益率序列
        
        返回：
            pd.Series: 基准收益率序列，索引为日期，值为日收益率
            
        异常：
            ExecutionError: 回测未完成时抛出
            DataError: 未设置基准时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def get_portfolio_value(self) -> pd.Series:
        """
        获取投资组合价值序列
        
        返回：
            pd.Series: 投资组合价值序列，索引为日期，值为组合价值
            
        异常：
            ExecutionError: 回测未完成时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def get_drawdowns(self) -> pd.Series:
        """
        获取回撤序列
        
        返回：
            pd.Series: 回撤序列，索引为日期，值为回撤幅度
            
        异常：
            ExecutionError: 回测未完成时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def get_daily_stats(self) -> pd.DataFrame:
        """
        获取每日统计数据
        
        返回：
            pd.DataFrame: 每日统计数据，包含收益率、持仓、交易成本等
            
        异常：
            ExecutionError: 回测未完成时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def plot_equity_curve(self, benchmark: bool = True, **kwargs) -> Any:
        """
        绘制权益曲线
        
        参数：
            benchmark: 是否显示基准
            **kwargs: 绘图参数
            
        返回：
            Any: 绘图对象
            
        异常：
            ExecutionError: 回测未完成时抛出
            ImportError: 缺少绘图库时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def plot_drawdown(self, **kwargs) -> Any:
        """
        绘制回撤曲线
        
        参数：
            **kwargs: 绘图参数
            
        返回：
            Any: 绘图对象
            
        异常：
            ExecutionError: 回测未完成时抛出
            ImportError: 缺少绘图库时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def plot_returns_distribution(self, **kwargs) -> Any:
        """
        绘制收益率分布
        
        参数：
            **kwargs: 绘图参数
            
        返回：
            Any: 绘图对象
            
        异常：
            ExecutionError: 回测未完成时抛出
            ImportError: 缺少绘图库时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def save_results(self, path: str, **kwargs) -> bool:
        """
        保存回测结果
        
        参数：
            path: 保存路径
            **kwargs: 保存参数
            
        返回：
            bool: 是否保存成功
            
        异常：
            ExecutionError: 回测未完成时抛出
            IOError: 保存失败时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def set_benchmark(self, benchmark: Union[str, pd.Series]) -> None:
        """
        设置基准
        
        参数：
            benchmark: 基准代码或收益率序列
            
        异常：
            ParameterError: 基准无效时抛出
            DataError: 基准数据获取失败时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def set_rebalance_frequency(self, frequency: str) -> None:
        """
        设置再平衡频率
        
        参数：
            frequency: 再平衡频率，如'daily', 'weekly', 'monthly'
            
        异常：
            ParameterError: 频率无效时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def set_commission_model(self, model: Any) -> None:
        """
        设置佣金模型
        
        参数：
            model: 佣金模型对象
            
        异常：
            ParameterError: 模型无效时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def set_slippage_model(self, model: Any) -> None:
        """
        设置滑点模型
        
        参数：
            model: 滑点模型对象
            
        异常：
            ParameterError: 模型无效时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def add_analyzer(self, analyzer: Any, name: str = None) -> None:
        """
        添加分析器
        
        参数：
            analyzer: 分析器对象
            name: 分析器名称
            
        异常：
            ParameterError: 分析器无效时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def get_analyzer_results(self, name: str = None) -> Union[Dict[str, Any], Any]:
        """
        获取分析器结果
        
        参数：
            name: 分析器名称，为None时返回所有分析器结果
            
        返回：
            Union[Dict[str, Any], Any]: 分析器结果
            
        异常：
            ExecutionError: 回测未完成时抛出
            KeyError: 分析器不存在时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def iter_trades(self) -> Iterator[Dict[str, Any]]:
        """
        迭代获取交易记录
        
        返回：
            Iterator[Dict[str, Any]]: 交易记录迭代器
            
        异常：
            ExecutionError: 回测未完成时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def calculate_transaction_costs(self) -> pd.Series:
        """
        计算交易成本
        
        返回：
            pd.Series: 交易成本序列，索引为日期，值为当日交易成本
            
        异常：
            ExecutionError: 回测未完成时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def calculate_turnover(self) -> pd.Series:
        """
        计算换手率
        
        返回：
            pd.Series: 换手率序列，索引为日期，值为当日换手率
            
        异常：
            ExecutionError: 回测未完成时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def run_with_progress(self, callback: Callable[[float, Dict[str, Any]], None] = None) -> Dict[str, Any]:
        """
        运行回测并报告进度
        
        参数：
            callback: 进度回调函数，接收进度百分比和当前状态
            
        返回：
            Dict[str, Any]: 回测结果
            
        异常：
            ExecutionError: 回测执行失败时抛出
            DataError: 数据错误时抛出
            InitializationError: 初始化失败时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
