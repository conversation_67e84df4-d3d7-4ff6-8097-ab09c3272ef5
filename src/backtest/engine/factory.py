"""
回测引擎工厂模块（兼容性包装）
保持向后兼容性，内部使用统一工厂
"""

from typing import Dict, Type, Optional, Any
import logging

from src.backtest.engine.engine_interface import BacktestEngineInterface

# {{ AURA-X: Add - 导入统一工厂，消除重复代码. Approval: 寸止(ID:BacktestEngineFactory整合). }}
from .unified_factory import UnifiedBacktestEngineFactory, BacktestEngineFactory, create_backtest_engine

# 配置日志
logger = logging.getLogger(__name__)

# 导出兼容性接口
__all__ = ['BacktestEngineFactory', 'create_backtest_engine']