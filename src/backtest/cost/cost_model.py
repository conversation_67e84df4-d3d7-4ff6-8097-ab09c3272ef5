#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易成本模型模块

提供各种交易成本的计算模型，包括：
1. 佣金模型（固定/比例）
2. 印花税模型
3. 滑点模型（固定/百分比/市场影响）
4. 交易成本综合模型
"""

import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Union, Optional, Tuple, Any, Callable

class CostModelException(Exception):
    """交易成本模型异常"""
    pass


class CostModelInterface(ABC):
    """
    交易成本模型接口
    
    定义交易成本计算的基本接口
    """
    
    @abstractmethod
    def calculate(self, price: float, quantity: int, **kwargs) -> float:
        """
        计算交易成本
        
        参数:
            price: 交易价格
            quantity: 交易数量（正数为买入，负数为卖出）
            **kwargs: 其他参数
            
        返回:
            float: 交易成本
        """
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """
        获取模型名称
        
        返回:
            str: 模型名称
        """
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """
        获取模型描述
        
        返回:
            str: 模型描述
        """
        pass
    
    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取模型参数
        
        返回:
            Dict[str, Any]: 模型参数
        """
        pass


class CommissionModel(CostModelInterface):
    """
    佣金模型
    
    支持固定佣金和比例佣金两种计算方式
    
    参数:
        rate: 佣金费率，例如0.0003表示万分之三
        min_commission: 最低佣金（元）
        max_commission: 最高佣金（元），None表示无上限
        per_share: 是否按股数计算（每股收取固定佣金）
        per_order: 是否按订单计算（每笔订单收取固定佣金）
    """
    
    def __init__(self, 
                rate: float = 0.0003, 
                min_commission: float = 5.0, 
                max_commission: Optional[float] = None,
                per_share: bool = False,
                per_order: bool = False):
        """
        初始化佣金模型
        
        参数:
            rate: 佣金费率，例如0.0003表示万分之三
            min_commission: 最低佣金（元）
            max_commission: 最高佣金（元），None表示无上限
            per_share: 是否按股数计算（每股收取固定佣金）
            per_order: 是否按订单计算（每笔订单收取固定佣金）
        """
        self.rate = rate
        self.min_commission = min_commission
        self.max_commission = max_commission
        self.per_share = per_share
        self.per_order = per_order
        
    def calculate(self, price: float, quantity: int, **kwargs) -> float:
        """
        计算佣金
        
        参数:
            price: 交易价格
            quantity: 交易数量（正数为买入，负数为卖出）
            **kwargs: 其他参数
            
        返回:
            float: 佣金金额
        """
        abs_quantity = abs(quantity)
        
        if self.per_order:
            # 按订单收取固定佣金
            return self.rate
        elif self.per_share:
            # 按股数收取固定佣金
            commission = abs_quantity * self.rate
        else:
            # 按金额比例收取佣金
            amount = price * abs_quantity
            commission = amount * self.rate
        
        # 应用最低佣金限制
        if self.min_commission is not None:
            commission = max(commission, self.min_commission)
        
        # 应用最高佣金限制
        if self.max_commission is not None:
            commission = min(commission, self.max_commission)
        
        return commission
    
    def get_name(self) -> str:
        """
        获取模型名称
        
        返回:
            str: 模型名称
        """
        return "佣金模型"
    
    def get_description(self) -> str:
        """
        获取模型描述
        
        返回:
            str: 模型描述
        """
        description = "按"
        if self.per_order:
            description += f"订单收取固定佣金 {self.rate} 元"
        elif self.per_share:
            description += f"股数收取佣金，每股 {self.rate} 元"
        else:
            description += f"成交金额收取佣金，费率 {self.rate * 100}%"
        
        if self.min_commission is not None:
            description += f"，最低佣金 {self.min_commission} 元"
        
        if self.max_commission is not None:
            description += f"，最高佣金 {self.max_commission} 元"
        
        return description
    
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取模型参数
        
        返回:
            Dict[str, Any]: 模型参数
        """
        return {
            "rate": self.rate,
            "min_commission": self.min_commission,
            "max_commission": self.max_commission,
            "per_share": self.per_share,
            "per_order": self.per_order
        }


class TaxModel(CostModelInterface):
    """
    印花税模型
    
    支持各种市场的印花税规则
    
    参数:
        rate: 印花税税率，例如0.001表示千分之一
        buy_tax: 是否对买入征收印花税
        sell_tax: 是否对卖出征收印花税
        min_tax: 最低印花税（元）
        max_tax: 最高印花税（元），None表示无上限
    """
    
    def __init__(self, 
                rate: float = 0.001, 
                buy_tax: bool = False, 
                sell_tax: bool = True,
                min_tax: Optional[float] = None,
                max_tax: Optional[float] = None):
        """
        初始化印花税模型
        
        参数:
            rate: 印花税税率，例如0.001表示千分之一
            buy_tax: 是否对买入征收印花税
            sell_tax: 是否对卖出征收印花税
            min_tax: 最低印花税（元）
            max_tax: 最高印花税（元），None表示无上限
        """
        self.rate = rate
        self.buy_tax = buy_tax
        self.sell_tax = sell_tax
        self.min_tax = min_tax
        self.max_tax = max_tax
        
    def calculate(self, price: float, quantity: int, **kwargs) -> float:
        """
        计算印花税
        
        参数:
            price: 交易价格
            quantity: 交易数量（正数为买入，负数为卖出）
            **kwargs: 其他参数
            
        返回:
            float: 印花税金额
        """
        # 如果买入不征税或卖出不征税，则根据交易方向判断
        if quantity > 0 and not self.buy_tax:
            return 0.0
        
        if quantity < 0 and not self.sell_tax:
            return 0.0
        
        # 计算印花税
        amount = price * abs(quantity)
        tax = amount * self.rate
        
        # 应用最低印花税限制
        if self.min_tax is not None:
            tax = max(tax, self.min_tax)
        
        # 应用最高印花税限制
        if self.max_tax is not None:
            tax = min(tax, self.max_tax)
        
        return tax
    
    def get_name(self) -> str:
        """
        获取模型名称
        
        返回:
            str: 模型名称
        """
        return "印花税模型"
    
    def get_description(self) -> str:
        """
        获取模型描述
        
        返回:
            str: 模型描述
        """
        description = f"印花税率 {self.rate * 100}%"
        
        if self.buy_tax and self.sell_tax:
            description += "，买卖双向征收"
        elif self.buy_tax:
            description += "，仅买入征收"
        elif self.sell_tax:
            description += "，仅卖出征收"
        
        if self.min_tax is not None:
            description += f"，最低税额 {self.min_tax} 元"
        
        if self.max_tax is not None:
            description += f"，最高税额 {self.max_tax} 元"
        
        return description
    
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取模型参数
        
        返回:
            Dict[str, Any]: 模型参数
        """
        return {
            "rate": self.rate,
            "buy_tax": self.buy_tax,
            "sell_tax": self.sell_tax,
            "min_tax": self.min_tax,
            "max_tax": self.max_tax
        }


class SlippageModel(CostModelInterface):
    """
    滑点模型
    
    提供多种滑点计算方式
    
    参数:
        mode: 滑点模式，支持 'fixed'（固定滑点）、'percentage'（百分比滑点）、'market_impact'（市场影响）
        value: 滑点值，对于固定滑点表示固定点数，对于百分比滑点表示百分比（如0.001表示0.1%）
        market_impact_factor: 市场影响因子，用于计算市场影响模式下的滑点
        max_slippage: 最大滑点，None表示无上限
    """
    
    VALID_MODES = {'fixed', 'percentage', 'market_impact'}
    
    def __init__(self, 
                mode: str = 'percentage', 
                value: float = 0.001,
                market_impact_factor: float = 0.1,
                max_slippage: Optional[float] = None):
        """
        初始化滑点模型
        
        参数:
            mode: 滑点模式，支持 'fixed'（固定滑点）、'percentage'（百分比滑点）、'market_impact'（市场影响）
            value: 滑点值，对于固定滑点表示固定点数，对于百分比滑点表示百分比（如0.001表示0.1%）
            market_impact_factor: 市场影响因子，用于计算市场影响模式下的滑点
            max_slippage: 最大滑点，None表示无上限
        """
        if mode not in self.VALID_MODES:
            raise ValueError(f"滑点模式 {mode} 无效，支持的模式有: {', '.join(self.VALID_MODES)}")
        
        self.mode = mode
        self.value = value
        self.market_impact_factor = market_impact_factor
        self.max_slippage = max_slippage
        
    def calculate(self, price: float, quantity: int, **kwargs) -> float:
        """
        计算滑点成本
        
        参数:
            price: 交易价格
            quantity: 交易数量（正数为买入，负数为卖出）
            **kwargs: 其他参数，可能包括：
                - volume: 当日成交量，用于市场影响模式
                - volatility: 波动率，用于某些高级滑点模型
            
        返回:
            float: 滑点成本
        """
        # 根据交易方向调整滑点方向（买入价格上升，卖出价格下降）
        direction = 1 if quantity > 0 else -1
        
        if self.mode == 'fixed':
            # 固定点数滑点
            slippage_cost = abs(quantity) * self.value
        
        elif self.mode == 'percentage':
            # 百分比滑点
            amount = price * abs(quantity)
            slippage_cost = amount * self.value
        
        elif self.mode == 'market_impact':
            # 市场影响模式滑点
            volume = kwargs.get('volume', 0)
            if volume <= 0:
                # 如果没有成交量信息，退化为百分比滑点
                amount = price * abs(quantity)
                slippage_cost = amount * self.value
            else:
                # 计算市场影响
                market_impact = (abs(quantity) / volume) ** self.market_impact_factor
                slippage_cost = price * abs(quantity) * market_impact
        
        # 应用最大滑点限制
        if self.max_slippage is not None:
            max_cost = price * abs(quantity) * self.max_slippage
            slippage_cost = min(slippage_cost, max_cost)
        
        return slippage_cost
    
    def get_name(self) -> str:
        """
        获取模型名称
        
        返回:
            str: 模型名称
        """
        return "滑点模型"
    
    def get_description(self) -> str:
        """
        获取模型描述
        
        返回:
            str: 模型描述
        """
        if self.mode == 'fixed':
            description = f"固定滑点模型，每股滑点 {self.value} 元"
        elif self.mode == 'percentage':
            description = f"百分比滑点模型，滑点比例 {self.value * 100}%"
        elif self.mode == 'market_impact':
            description = f"市场影响滑点模型，基础滑点 {self.value * 100}%，市场影响因子 {self.market_impact_factor}"
        
        if self.max_slippage is not None:
            description += f"，最大滑点 {self.max_slippage * 100}%"
        
        return description
    
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取模型参数
        
        返回:
            Dict[str, Any]: 模型参数
        """
        return {
            "mode": self.mode,
            "value": self.value,
            "market_impact_factor": self.market_impact_factor,
            "max_slippage": self.max_slippage
        }


class CompositeCostModel(CostModelInterface):
    """
    综合交易成本模型
    
    组合多个交易成本模型
    
    参数:
        commission_model: 佣金模型
        tax_model: 印花税模型
        slippage_model: 滑点模型
    """
    
    def __init__(self, 
                commission_model: Optional[CommissionModel] = None,
                tax_model: Optional[TaxModel] = None,
                slippage_model: Optional[SlippageModel] = None,
                other_costs: Optional[List[CostModelInterface]] = None):
        """
        初始化综合交易成本模型
        
        参数:
            commission_model: 佣金模型
            tax_model: 印花税模型
            slippage_model: 滑点模型
            other_costs: 其他交易成本模型列表
        """
        self.commission_model = commission_model or CommissionModel()
        self.tax_model = tax_model or TaxModel()
        self.slippage_model = slippage_model or SlippageModel()
        self.other_costs = other_costs or []
        
    def calculate(self, price: float, quantity: int, **kwargs) -> Dict[str, float]:
        """
        计算综合交易成本
        
        参数:
            price: 交易价格
            quantity: 交易数量（正数为买入，负数为卖出）
            **kwargs: 其他参数
            
        返回:
            Dict[str, float]: 各项交易成本及总成本
        """
        result = {}
        
        # 计算佣金
        commission = self.commission_model.calculate(price, quantity, **kwargs)
        result["commission"] = commission
        
        # 计算印花税
        tax = self.tax_model.calculate(price, quantity, **kwargs)
        result["tax"] = tax
        
        # 计算滑点成本
        slippage_cost = self.slippage_model.calculate(price, quantity, **kwargs)
        result["slippage"] = slippage_cost
        
        # 计算其他成本
        other_total = 0.0
        for i, cost_model in enumerate(self.other_costs):
            cost = cost_model.calculate(price, quantity, **kwargs)
            result[f"other_{i}"] = cost
            other_total += cost
        
        result["other_total"] = other_total
        
        # 计算总成本
        total_cost = commission + tax + slippage_cost + other_total
        result["total"] = total_cost
        
        return result
    
    def get_total_cost(self, price: float, quantity: int, **kwargs) -> float:
        """
        计算总交易成本
        
        参数:
            price: 交易价格
            quantity: 交易数量（正数为买入，负数为卖出）
            **kwargs: 其他参数
            
        返回:
            float: 总交易成本
        """
        costs = self.calculate(price, quantity, **kwargs)
        return costs["total"]
    
    def get_name(self) -> str:
        """
        获取模型名称
        
        返回:
            str: 模型名称
        """
        return "综合交易成本模型"
    
    def get_description(self) -> str:
        """
        获取模型描述
        
        返回:
            str: 模型描述
        """
        description = "综合交易成本模型，包含："
        description += f"\n- 佣金模型: {self.commission_model.get_description()}"
        description += f"\n- 印花税模型: {self.tax_model.get_description()}"
        description += f"\n- 滑点模型: {self.slippage_model.get_description()}"
        
        if self.other_costs:
            description += "\n- 其他成本模型:"
            for cost_model in self.other_costs:
                description += f"\n  - {cost_model.get_name()}: {cost_model.get_description()}"
        
        return description
    
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取模型参数
        
        返回:
            Dict[str, Dict[str, Any]]: 模型参数
        """
        return {
            "commission": self.commission_model.get_parameters(),
            "tax": self.tax_model.get_parameters(),
            "slippage": self.slippage_model.get_parameters(),
            "other_costs": [model.get_parameters() for model in self.other_costs]
        }


# 预定义各市场交易成本模型
def create_a_stock_cost_model() -> CompositeCostModel:
    """
    创建A股市场交易成本模型
    
    返回:
        CompositeCostModel: A股交易成本模型
    """
    # 佣金：万分之2.5，最低5元
    commission = CommissionModel(rate=0.00025, min_commission=5.0)
    
    # 印花税：卖出千分之一，买入不收
    tax = TaxModel(rate=0.001, buy_tax=False, sell_tax=True)
    
    # 滑点：默认0.1%
    slippage = SlippageModel(mode='percentage', value=0.001)
    
    # 组合成本模型
    return CompositeCostModel(commission_model=commission, tax_model=tax, slippage_model=slippage)


def create_hk_stock_cost_model() -> CompositeCostModel:
    """
    创建港股市场交易成本模型
    
    返回:
        CompositeCostModel: 港股交易成本模型
    """
    # 佣金：0.25%，最低100港元
    commission = CommissionModel(rate=0.0025, min_commission=100.0)
    
    # 印花税：0.13%，买卖双向
    tax = TaxModel(rate=0.0013, buy_tax=True, sell_tax=True)
    
    # 滑点：默认0.1%
    slippage = SlippageModel(mode='percentage', value=0.001)
    
    # 组合成本模型
    return CompositeCostModel(commission_model=commission, tax_model=tax, slippage_model=slippage)


def create_us_stock_cost_model() -> CompositeCostModel:
    """
    创建美股市场交易成本模型
    
    返回:
        CompositeCostModel: 美股交易成本模型
    """
    # 佣金：每股0.01美元，最低1美元
    commission = CommissionModel(rate=0.01, min_commission=1.0, per_share=True)
    
    # 印花税：美股无印花税
    tax = TaxModel(rate=0.0, buy_tax=False, sell_tax=False)
    
    # 滑点：默认0.05%
    slippage = SlippageModel(mode='percentage', value=0.0005)
    
    # 组合成本模型
    return CompositeCostModel(commission_model=commission, tax_model=tax, slippage_model=slippage)


# 工厂函数，根据市场类型创建交易成本模型
def create_cost_model(market_type: str = 'A') -> CompositeCostModel:
    """
    根据市场类型创建交易成本模型
    
    参数:
        market_type: 市场类型，支持 'A'（A股）、'HK'（港股）、'US'（美股）
        
    返回:
        CompositeCostModel: 交易成本模型
    """
    market_type = market_type.upper()
    
    if market_type == 'A':
        return create_a_stock_cost_model()
    elif market_type == 'HK':
        return create_hk_stock_cost_model()
    elif market_type == 'US':
        return create_us_stock_cost_model()
    else:
        raise ValueError(f"不支持的市场类型: {market_type}，支持的类型有: A, HK, US") 