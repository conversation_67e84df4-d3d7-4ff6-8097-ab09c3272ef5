#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易规则模块

提供各种交易规则的实现，包括：
1. 涨跌停限制规则
2. 交易量限制规则
3. 持仓限制规则
4. 禁止交易规则
5. 组合规则
"""

import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Union, Optional, Tuple, Any, Callable, Set
from datetime import datetime, timedelta

class RuleException(Exception):
    """交易规则异常"""
    pass


class TradingRuleInterface(ABC):
    """
    交易规则接口
    
    定义交易规则检查的基本接口
    """
    
    @abstractmethod
    def check(self, context: Dict[str, Any], order: Dict[str, Any]) -> Tuple[bool, str]:
        """
        检查交易规则
        
        参数:
            context: 交易上下文，包含当前市场状态、持仓等信息
            order: 订单信息，包含交易方向、数量、价格等
            
        返回:
            Tuple[bool, str]: (是否允许交易，拒绝原因)
        """
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """
        获取规则名称
        
        返回:
            str: 规则名称
        """
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """
        获取规则描述
        
        返回:
            str: 规则描述
        """
        pass
    
    def adjust_order(self, context: Dict[str, Any], order: Dict[str, Any]) -> Dict[str, Any]:
        """
        调整订单以符合规则
        
        默认实现不调整订单，如果规则需要调整订单（如数量限制），可以重写此方法
        
        参数:
            context: 交易上下文
            order: 原始订单
            
        返回:
            Dict[str, Any]: 调整后的订单
        """
        return order.copy()


class PriceLimitRule(TradingRuleInterface):
    """
    涨跌停限制规则
    
    参数:
        up_limit_pct: 涨停百分比，例如0.1表示10%
        down_limit_pct: 跌停百分比，例如0.1表示10%
        price_field: 价格字段名称
        base_price_field: 基准价格字段名称，用于计算涨跌停价格
        price_precision: 价格精度（小数位数）
    """
    
    def __init__(self, 
                up_limit_pct: float = 0.1, 
                down_limit_pct: float = 0.1,
                price_field: str = 'price',
                base_price_field: str = 'prev_close',
                price_precision: int = 2):
        """
        初始化涨跌停限制规则
        
        参数:
            up_limit_pct: 涨停百分比，例如0.1表示10%
            down_limit_pct: 跌停百分比，例如0.1表示10%
            price_field: 价格字段名称
            base_price_field: 基准价格字段名称，用于计算涨跌停价格
            price_precision: 价格精度（小数位数）
        """
        self.up_limit_pct = up_limit_pct
        self.down_limit_pct = down_limit_pct
        self.price_field = price_field
        self.base_price_field = base_price_field
        self.price_precision = price_precision
        
    def check(self, context: Dict[str, Any], order: Dict[str, Any]) -> Tuple[bool, str]:
        """
        检查是否违反涨跌停限制
        
        参数:
            context: 交易上下文
            order: 订单信息
            
        返回:
            Tuple[bool, str]: (是否允许交易，拒绝原因)
        """
        symbol = order.get('symbol')
        price = order.get(self.price_field)
        
        if symbol is None or price is None:
            return False, "订单缺少必要信息：代码或价格"
        
        # 获取当前证券的基准价格（通常是前收盘价）
        symbols_data = context.get('symbols_data', {})
        symbol_data = symbols_data.get(symbol, {})
        base_price = symbol_data.get(self.base_price_field)
        
        if base_price is None or base_price <= 0:
            return False, f"无法获取{symbol}的基准价格({self.base_price_field})"
        
        # 计算涨跌停价格
        up_limit_price = round(base_price * (1 + self.up_limit_pct), self.price_precision)
        down_limit_price = round(base_price * (1 - self.down_limit_pct), self.price_precision)
        
        # 检查价格是否超过涨跌停限制
        if price > up_limit_price:
            return False, f"{symbol}交易价格{price}超过涨停价格{up_limit_price}"
            
        if price < down_limit_price:
            return False, f"{symbol}交易价格{price}低于跌停价格{down_limit_price}"
            
        return True, ""
    
    def adjust_order(self, context: Dict[str, Any], order: Dict[str, Any]) -> Dict[str, Any]:
        """
        调整订单价格以符合涨跌停限制
        
        参数:
            context: 交易上下文
            order: 原始订单
            
        返回:
            Dict[str, Any]: 调整后的订单
        """
        symbol = order.get('symbol')
        price = order.get(self.price_field)
        
        if symbol is None or price is None:
            return order
        
        # 获取当前证券的基准价格（通常是前收盘价）
        symbols_data = context.get('symbols_data', {})
        symbol_data = symbols_data.get(symbol, {})
        base_price = symbol_data.get(self.base_price_field)
        
        if base_price is None or base_price <= 0:
            return order
        
        # 计算涨跌停价格
        up_limit_price = round(base_price * (1 + self.up_limit_pct), self.price_precision)
        down_limit_price = round(base_price * (1 - self.down_limit_pct), self.price_precision)
        
        # 调整价格
        adjusted_order = order.copy()
        if price > up_limit_price:
            adjusted_order[self.price_field] = up_limit_price
        elif price < down_limit_price:
            adjusted_order[self.price_field] = down_limit_price
            
        return adjusted_order
    
    def get_name(self) -> str:
        """
        获取规则名称
        
        返回:
            str: 规则名称
        """
        return "涨跌停限制规则"
    
    def get_description(self) -> str:
        """
        获取规则描述
        
        返回:
            str: 规则描述
        """
        return f"涨停限制{self.up_limit_pct * 100}%，跌停限制{self.down_limit_pct * 100}%"


class VolumeRule(TradingRuleInterface):
    """
    交易量限制规则
    
    参数:
        max_pct_of_volume: 最大成交量占比，例如0.1表示最多占当日成交量的10%
        volume_field: 成交量字段名称
        use_avg_volume: 是否使用平均成交量而非当日成交量
        avg_volume_days: 计算平均成交量的天数
        avg_volume_field: 平均成交量字段名称
    """
    
    def __init__(self, 
                max_pct_of_volume: float = 0.1, 
                volume_field: str = 'volume',
                use_avg_volume: bool = False,
                avg_volume_days: int = 5,
                avg_volume_field: str = 'avg_volume'):
        """
        初始化交易量限制规则
        
        参数:
            max_pct_of_volume: 最大成交量占比，例如0.1表示最多占当日成交量的10%
            volume_field: 成交量字段名称
            use_avg_volume: 是否使用平均成交量而非当日成交量
            avg_volume_days: 计算平均成交量的天数
            avg_volume_field: 平均成交量字段名称
        """
        self.max_pct_of_volume = max_pct_of_volume
        self.volume_field = volume_field
        self.use_avg_volume = use_avg_volume
        self.avg_volume_days = avg_volume_days
        self.avg_volume_field = avg_volume_field
        
    def check(self, context: Dict[str, Any], order: Dict[str, Any]) -> Tuple[bool, str]:
        """
        检查是否违反交易量限制
        
        参数:
            context: 交易上下文
            order: 订单信息
            
        返回:
            Tuple[bool, str]: (是否允许交易，拒绝原因)
        """
        symbol = order.get('symbol')
        quantity = order.get('quantity')
        
        if symbol is None or quantity is None:
            return False, "订单缺少必要信息：代码或数量"
        
        # 获取证券的交易量信息
        symbols_data = context.get('symbols_data', {})
        symbol_data = symbols_data.get(symbol, {})
        
        if self.use_avg_volume:
            volume = symbol_data.get(self.avg_volume_field)
            volume_desc = f"{self.avg_volume_days}日平均成交量"
        else:
            volume = symbol_data.get(self.volume_field)
            volume_desc = "当日成交量"
        
        if volume is None or volume <= 0:
            return False, f"无法获取{symbol}的{volume_desc}"
        
        # 计算最大允许交易量
        max_allowed_quantity = int(volume * self.max_pct_of_volume)
        
        # 检查交易量是否超过限制
        if abs(quantity) > max_allowed_quantity:
            return False, f"{symbol}交易量{abs(quantity)}超过最大允许量{max_allowed_quantity}（{self.max_pct_of_volume * 100}%的{volume_desc}）"
            
        return True, ""
    
    def adjust_order(self, context: Dict[str, Any], order: Dict[str, Any]) -> Dict[str, Any]:
        """
        调整订单数量以符合交易量限制
        
        参数:
            context: 交易上下文
            order: 原始订单
            
        返回:
            Dict[str, Any]: 调整后的订单
        """
        symbol = order.get('symbol')
        quantity = order.get('quantity')
        
        if symbol is None or quantity is None:
            return order
        
        # 获取证券的交易量信息
        symbols_data = context.get('symbols_data', {})
        symbol_data = symbols_data.get(symbol, {})
        
        if self.use_avg_volume:
            volume = symbol_data.get(self.avg_volume_field)
        else:
            volume = symbol_data.get(self.volume_field)
        
        if volume is None or volume <= 0:
            return order
        
        # 计算最大允许交易量
        max_allowed_quantity = int(volume * self.max_pct_of_volume)
        
        # 调整交易量
        adjusted_order = order.copy()
        if abs(quantity) > max_allowed_quantity:
            adjusted_quantity = max_allowed_quantity if quantity > 0 else -max_allowed_quantity
            adjusted_order['quantity'] = adjusted_quantity
            
        return adjusted_order
    
    def get_name(self) -> str:
        """
        获取规则名称
        
        返回:
            str: 规则名称
        """
        return "交易量限制规则"
    
    def get_description(self) -> str:
        """
        获取规则描述
        
        返回:
            str: 规则描述
        """
        volume_desc = f"{self.avg_volume_days}日平均成交量" if self.use_avg_volume else "当日成交量"
        return f"单笔交易最多占{volume_desc}的{self.max_pct_of_volume * 100}%"


class PositionRule(TradingRuleInterface):
    """
    持仓限制规则
    
    参数:
        max_position_pct: 单一持仓最大占组合比例
        max_position_value: 单一持仓最大价值（单位：元）
        max_total_positions: 最大持仓数量
        check_type: 检查类型，可选 'pct'（百分比）、'value'（价值）、'count'（数量）或 'all'（全部）
    """
    
    VALID_CHECK_TYPES = {'pct', 'value', 'count', 'all'}
    
    def __init__(self, 
                max_position_pct: float = 0.1, 
                max_position_value: float = None,
                max_total_positions: int = None,
                check_type: str = 'all'):
        """
        初始化持仓限制规则
        
        参数:
            max_position_pct: 单一持仓最大占组合比例
            max_position_value: 单一持仓最大价值（单位：元）
            max_total_positions: 最大持仓数量
            check_type: 检查类型，可选 'pct'（百分比）、'value'（价值）、'count'（数量）或 'all'（全部）
        """
        if check_type not in self.VALID_CHECK_TYPES:
            raise ValueError(f"检查类型 {check_type} 无效，支持的类型有: {', '.join(self.VALID_CHECK_TYPES)}")
        
        self.max_position_pct = max_position_pct
        self.max_position_value = max_position_value
        self.max_total_positions = max_total_positions
        self.check_type = check_type
        
    def check(self, context: Dict[str, Any], order: Dict[str, Any]) -> Tuple[bool, str]:
        """
        检查是否违反持仓限制
        
        参数:
            context: 交易上下文
            order: 订单信息
            
        返回:
            Tuple[bool, str]: (是否允许交易，拒绝原因)
        """
        symbol = order.get('symbol')
        quantity = order.get('quantity')
        price = order.get('price')
        
        if symbol is None or quantity is None or price is None:
            return False, "订单缺少必要信息：代码、数量或价格"
        
        # 获取当前持仓和组合信息
        portfolio = context.get('portfolio', {})
        total_value = portfolio.get('total_value', 0)
        positions = portfolio.get('positions', {})
        
        # 计算订单执行后的持仓
        current_position = positions.get(symbol, 0)
        new_position = current_position + quantity
        
        # 如果是卖出，不需要检查持仓限制
        if new_position <= current_position:
            return True, ""
        
        # 检查持仓数量限制
        if (self.check_type == 'count' or self.check_type == 'all') and self.max_total_positions is not None:
            # 计算当前持有的不同证券数量
            current_position_count = sum(1 for pos in positions.values() if pos > 0)
            # 如果是新增持仓，检查是否超过最大持仓数量
            if current_position == 0 and current_position_count >= self.max_total_positions:
                return False, f"持仓数量超过最大限制{self.max_total_positions}"
                
        # 检查持仓价值限制
        if (self.check_type == 'value' or self.check_type == 'all') and self.max_position_value is not None:
            new_position_value = new_position * price
            if new_position_value > self.max_position_value:
                return False, f"{symbol}持仓价值{new_position_value}超过最大限制{self.max_position_value}"
                
        # 检查持仓比例限制
        if (self.check_type == 'pct' or self.check_type == 'all') and self.max_position_pct is not None:
            if total_value <= 0:
                return False, "无法计算持仓比例，组合总价值为零或负数"
                
            new_position_value = new_position * price
            new_position_pct = new_position_value / total_value
            
            if new_position_pct > self.max_position_pct:
                return False, f"{symbol}持仓比例{new_position_pct * 100:.2f}%超过最大限制{self.max_position_pct * 100:.2f}%"
                
        return True, ""
    
    def adjust_order(self, context: Dict[str, Any], order: Dict[str, Any]) -> Dict[str, Any]:
        """
        调整订单以符合持仓限制
        
        参数:
            context: 交易上下文
            order: 原始订单
            
        返回:
            Dict[str, Any]: 调整后的订单
        """
        symbol = order.get('symbol')
        quantity = order.get('quantity')
        price = order.get('price')
        
        if symbol is None or quantity is None or price is None or quantity <= 0:
            return order  # 只调整买入订单
        
        # 获取当前持仓和组合信息
        portfolio = context.get('portfolio', {})
        total_value = portfolio.get('total_value', 0)
        positions = portfolio.get('positions', {})
        
        # 当前持仓
        current_position = positions.get(symbol, 0)
        
        adjusted_order = order.copy()
        adjusted_quantity = quantity
        
        # 调整持仓数量限制
        if (self.check_type == 'count' or self.check_type == 'all') and self.max_total_positions is not None:
            current_position_count = sum(1 for pos in positions.values() if pos > 0)
            if current_position == 0 and current_position_count >= self.max_total_positions:
                adjusted_quantity = 0  # 无法新增持仓
        
        # 调整持仓价值限制
        if (self.check_type == 'value' or self.check_type == 'all') and self.max_position_value is not None:
            current_position_value = current_position * price
            max_additional_value = self.max_position_value - current_position_value
            
            if max_additional_value <= 0:
                adjusted_quantity = 0
            else:
                max_additional_quantity = int(max_additional_value / price)
                adjusted_quantity = min(adjusted_quantity, max_additional_quantity)
        
        # 调整持仓比例限制
        if (self.check_type == 'pct' or self.check_type == 'all') and self.max_position_pct is not None and total_value > 0:
            current_position_value = current_position * price
            max_position_value = total_value * self.max_position_pct
            max_additional_value = max_position_value - current_position_value
            
            if max_additional_value <= 0:
                adjusted_quantity = 0
            else:
                max_additional_quantity = int(max_additional_value / price)
                adjusted_quantity = min(adjusted_quantity, max_additional_quantity)
        
        if adjusted_quantity != quantity:
            adjusted_order['quantity'] = adjusted_quantity
            
        return adjusted_order
    
    def get_name(self) -> str:
        """
        获取规则名称
        
        返回:
            str: 规则名称
        """
        return "持仓限制规则"
    
    def get_description(self) -> str:
        """
        获取规则描述
        
        返回:
            str: 规则描述
        """
        descriptions = []
        
        if self.check_type in {'pct', 'all'} and self.max_position_pct is not None:
            descriptions.append(f"单一持仓最大比例{self.max_position_pct * 100:.2f}%")
            
        if self.check_type in {'value', 'all'} and self.max_position_value is not None:
            descriptions.append(f"单一持仓最大价值{self.max_position_value}元")
            
        if self.check_type in {'count', 'all'} and self.max_total_positions is not None:
            descriptions.append(f"最大持仓数量{self.max_total_positions}")
            
        return "，".join(descriptions)


class RestrictedListRule(TradingRuleInterface):
    """
    禁止交易名单规则
    
    参数:
        restricted_symbols: 禁止交易的证券代码列表
        restricted_types: 禁止交易的证券类型列表
        restriction_start_date: 限制开始日期
        restriction_end_date: 限制结束日期
        type_field: 证券类型字段名称
    """
    
    def __init__(self, 
                restricted_symbols: Optional[List[str]] = None, 
                restricted_types: Optional[List[str]] = None,
                restriction_start_date: Optional[datetime] = None,
                restriction_end_date: Optional[datetime] = None,
                type_field: str = 'type'):
        """
        初始化禁止交易名单规则
        
        参数:
            restricted_symbols: 禁止交易的证券代码列表
            restricted_types: 禁止交易的证券类型列表
            restriction_start_date: 限制开始日期
            restriction_end_date: 限制结束日期
            type_field: 证券类型字段名称
        """
        self.restricted_symbols = set(restricted_symbols or [])
        self.restricted_types = set(restricted_types or [])
        self.restriction_start_date = restriction_start_date
        self.restriction_end_date = restriction_end_date
        self.type_field = type_field
        
    def check(self, context: Dict[str, Any], order: Dict[str, Any]) -> Tuple[bool, str]:
        """
        检查是否违反禁止交易规则
        
        参数:
            context: 交易上下文
            order: 订单信息
            
        返回:
            Tuple[bool, str]: (是否允许交易，拒绝原因)
        """
        symbol = order.get('symbol')
        
        if symbol is None:
            return False, "订单缺少必要信息：代码"
        
        # 获取当前日期
        current_date = context.get('current_date')
        
        # 检查日期限制
        if current_date and (self.restriction_start_date or self.restriction_end_date):
            if (self.restriction_start_date and current_date < self.restriction_start_date) or \
               (self.restriction_end_date and current_date > self.restriction_end_date):
                # 不在限制日期范围内，允许交易
                return True, ""
        
        # 检查禁止交易的证券代码
        if symbol in self.restricted_symbols:
            return False, f"{symbol}在禁止交易名单中"
        
        # 检查禁止交易的证券类型
        if self.restricted_types:
            symbols_data = context.get('symbols_data', {})
            symbol_data = symbols_data.get(symbol, {})
            symbol_type = symbol_data.get(self.type_field)
            
            if symbol_type in self.restricted_types:
                return False, f"{symbol}的类型{symbol_type}在禁止交易类型列表中"
                
        return True, ""
    
    def get_name(self) -> str:
        """
        获取规则名称
        
        返回:
            str: 规则名称
        """
        return "禁止交易名单规则"
    
    def get_description(self) -> str:
        """
        获取规则描述
        
        返回:
            str: 规则描述
        """
        descriptions = []
        
        if self.restricted_symbols:
            descriptions.append(f"禁止交易的证券代码：{', '.join(self.restricted_symbols)}")
            
        if self.restricted_types:
            descriptions.append(f"禁止交易的证券类型：{', '.join(self.restricted_types)}")
            
        date_range = []
        if self.restriction_start_date:
            date_range.append(f"从{self.restriction_start_date.strftime('%Y-%m-%d')}开始")
            
        if self.restriction_end_date:
            date_range.append(f"至{self.restriction_end_date.strftime('%Y-%m-%d')}结束")
            
        if date_range:
            descriptions.append("限制时间：" + "".join(date_range))
            
        return "；".join(descriptions)


class CompositeRule(TradingRuleInterface):
    """
    组合规则
    
    将多个规则组合在一起，可以设置规则之间的逻辑关系（AND/OR）
    
    参数:
        rules: 规则列表
        relation: 规则之间的关系，'and' 表示所有规则都必须满足，'or' 表示满足任一规则即可
        adjust_order: 是否调整订单以满足规则
    """
    
    VALID_RELATIONS = {'and', 'or'}
    
    def __init__(self, 
                rules: List[TradingRuleInterface], 
                relation: str = 'and',
                adjust_order: bool = True):
        """
        初始化组合规则
        
        参数:
            rules: 规则列表
            relation: 规则之间的关系，'and' 表示所有规则都必须满足，'or' 表示满足任一规则即可
            adjust_order: 是否调整订单以满足规则
        """
        if relation not in self.VALID_RELATIONS:
            raise ValueError(f"规则关系 {relation} 无效，支持的关系有: {', '.join(self.VALID_RELATIONS)}")
        
        self.rules = rules
        self.relation = relation
        self.adjust_order = adjust_order
        
    def check(self, context: Dict[str, Any], order: Dict[str, Any]) -> Tuple[bool, str]:
        """
        检查是否违反组合规则
        
        参数:
            context: 交易上下文
            order: 订单信息
            
        返回:
            Tuple[bool, str]: (是否允许交易，拒绝原因)
        """
        if not self.rules:
            return True, ""
        
        if self.relation == 'and':
            # 所有规则都必须满足
            for rule in self.rules:
                is_allowed, reason = rule.check(context, order)
                if not is_allowed:
                    return False, reason
            return True, ""
        else:  # 'or'
            # 满足任一规则即可
            reasons = []
            for rule in self.rules:
                is_allowed, reason = rule.check(context, order)
                if is_allowed:
                    return True, ""
                reasons.append(reason)
            return False, "；".join(reasons)
    
    def adjust_order(self, context: Dict[str, Any], order: Dict[str, Any]) -> Dict[str, Any]:
        """
        调整订单以符合组合规则
        
        参数:
            context: 交易上下文
            order: 原始订单
            
        返回:
            Dict[str, Any]: 调整后的订单
        """
        if not self.adjust_order or not self.rules:
            return order
        
        adjusted_order = order.copy()
        
        for rule in self.rules:
            adjusted_order = rule.adjust_order(context, adjusted_order)
            
        return adjusted_order
    
    def get_name(self) -> str:
        """
        获取规则名称
        
        返回:
            str: 规则名称
        """
        return "组合规则"
    
    def get_description(self) -> str:
        """
        获取规则描述
        
        返回:
            str: 规则描述
        """
        relation_desc = "全部满足" if self.relation == 'and' else "满足任一条件"
        rule_descriptions = [f"{i+1}. {rule.get_name()}：{rule.get_description()}" for i, rule in enumerate(self.rules)]
        
        return f"组合规则（{relation_desc}）：\n" + "\n".join(rule_descriptions)


# 预定义市场规则
def create_a_stock_rules() -> CompositeRule:
    """
    创建A股市场交易规则
    
    返回:
        CompositeRule: A股交易规则
    """
    # 涨跌停限制：10%
    price_limit_rule = PriceLimitRule(up_limit_pct=0.1, down_limit_pct=0.1)
    
    # 交易量限制：单笔最多占当日成交量的5%
    volume_rule = VolumeRule(max_pct_of_volume=0.05)
    
    # 持仓限制：单一持仓最多占组合10%，最多持有30只股票
    position_rule = PositionRule(max_position_pct=0.1, max_total_positions=30)
    
    # 组合规则
    return CompositeRule([price_limit_rule, volume_rule, position_rule], relation='and')


def create_hk_stock_rules() -> CompositeRule:
    """
    创建港股市场交易规则
    
    返回:
        CompositeRule: 港股交易规则
    """
    # 港股没有涨跌停限制
    # 交易量限制：单笔最多占当日成交量的10%
    volume_rule = VolumeRule(max_pct_of_volume=0.1)
    
    # 持仓限制：单一持仓最多占组合15%，最多持有20只股票
    position_rule = PositionRule(max_position_pct=0.15, max_total_positions=20)
    
    # 组合规则
    return CompositeRule([volume_rule, position_rule], relation='and')


def create_us_stock_rules() -> CompositeRule:
    """
    创建美股市场交易规则
    
    返回:
        CompositeRule: 美股交易规则
    """
    # 美股没有涨跌停限制，但设置一个较大的限制（比如50%）防止异常交易
    price_limit_rule = PriceLimitRule(up_limit_pct=0.5, down_limit_pct=0.5)
    
    # 交易量限制：单笔最多占当日成交量的10%
    volume_rule = VolumeRule(max_pct_of_volume=0.1)
    
    # 持仓限制：单一持仓最多占组合15%，最多持有20只股票
    position_rule = PositionRule(max_position_pct=0.15, max_total_positions=20)
    
    # 组合规则
    return CompositeRule([price_limit_rule, volume_rule, position_rule], relation='and')


# 工厂函数，根据市场类型创建交易规则
def create_trading_rules(market_type: str = 'A') -> CompositeRule:
    """
    根据市场类型创建交易规则
    
    参数:
        market_type: 市场类型，支持 'A'（A股）、'HK'（港股）、'US'（美股）
        
    返回:
        CompositeRule: 交易规则
    """
    market_type = market_type.upper()
    
    if market_type == 'A':
        return create_a_stock_rules()
    elif market_type == 'HK':
        return create_hk_stock_rules()
    elif market_type == 'US':
        return create_us_stock_rules()
    else:
        raise ValueError(f"不支持的市场类型: {market_type}，支持的类型有: A, HK, US") 