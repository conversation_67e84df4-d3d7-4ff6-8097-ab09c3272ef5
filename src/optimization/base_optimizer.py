#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一优化器基类
提供所有优化器的通用功能和接口
"""

import numpy as np
import pandas as pd
import itertools
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable, Tuple, Union
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
from dataclasses import dataclass
from src.utils.logging.logger_factory import get_logger

logger = get_logger(__name__)


@dataclass
class OptimizationResult:
    """优化结果数据类"""
    best_params: Dict[str, Any]
    best_score: float
    all_results: List[Dict[str, Any]]
    optimization_time: float
    total_combinations: int
    valid_combinations: int
    optimization_type: str
    target_metric: str


class BaseOptimizer(ABC):
    """
    统一优化器基类
    
    提供所有优化器的通用功能：
    1. 参数空间定义和搜索
    2. 并行优化执行
    3. 结果评估和排序
    4. 性能监控和日志
    5. 统一的优化接口
    """
    
    def __init__(self, 
                 name: str = "基础优化器",
                 optimization_type: str = "parameter",
                 max_workers: int = 4,
                 timeout_seconds: Optional[int] = None):
        """
        初始化优化器基类
        
        参数:
            name: 优化器名称
            optimization_type: 优化类型 ('parameter', 'portfolio', 'strategy')
            max_workers: 并行工作线程数
            timeout_seconds: 优化超时时间（秒）
        """
        self.name = name
        self.optimization_type = optimization_type
        self.max_workers = max_workers
        self.timeout_seconds = timeout_seconds
        
        # 优化状态
        self.is_running = False
        self.start_time = None
        self.results_cache = {}
        
        logger.info(f"初始化优化器: {name}, 类型: {optimization_type}")
    
    @abstractmethod
    def _evaluate_single_combination(self, params: Dict[str, Any], context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        评估单个参数组合
        
        参数:
            params: 参数组合
            context: 优化上下文
            
        返回:
            评估结果字典，包含score等字段
        """
        pass
    
    @abstractmethod
    def _get_default_param_ranges(self) -> Dict[str, List[Any]]:
        """
        获取默认参数搜索范围
        
        返回:
            参数名到参数值列表的映射
        """
        pass
    
    @abstractmethod
    def _calculate_optimization_score(self, result: Dict[str, Any]) -> float:
        """
        计算优化评分
        
        参数:
            result: 单次评估结果
            
        返回:
            优化评分（越高越好）
        """
        pass
    
    def generate_parameter_combinations(self, param_ranges: Dict[str, List[Any]]) -> List[Dict[str, Any]]:
        """
        生成参数组合
        
        参数:
            param_ranges: 参数搜索范围
            
        返回:
            参数组合列表
        """
        if not param_ranges:
            param_ranges = self._get_default_param_ranges()
        
        # 生成所有参数组合
        param_names = list(param_ranges.keys())
        param_values = list(param_ranges.values())
        
        combinations = []
        for combination in itertools.product(*param_values):
            param_dict = dict(zip(param_names, combination))
            combinations.append(param_dict)
        
        logger.info(f"生成参数组合数量: {len(combinations)}")
        return combinations
    
    def optimize_parameters(self, 
                          param_ranges: Optional[Dict[str, List[Any]]] = None,
                          context: Optional[Dict[str, Any]] = None,
                          target_metric: str = 'score') -> OptimizationResult:
        """
        执行参数优化
        
        参数:
            param_ranges: 参数搜索范围
            context: 优化上下文
            target_metric: 目标优化指标
            
        返回:
            OptimizationResult: 优化结果
        """
        if context is None:
            context = {}
        
        self.is_running = True
        self.start_time = time.time()
        
        try:
            logger.info(f"开始参数优化: {self.name}")
            
            # 生成参数组合
            param_combinations = self.generate_parameter_combinations(param_ranges)
            
            # 并行优化
            all_results = self._execute_parallel_optimization(param_combinations, context)
            
            # 分析结果
            optimization_result = self._analyze_optimization_results(
                all_results, param_combinations, target_metric
            )
            
            optimization_time = time.time() - self.start_time
            optimization_result.optimization_time = optimization_time
            
            logger.info(f"参数优化完成: {self.name}, 耗时: {optimization_time:.2f}秒")
            return optimization_result
            
        finally:
            self.is_running = False
    
    def _execute_parallel_optimization(self, 
                                     param_combinations: List[Dict[str, Any]], 
                                     context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        执行并行优化
        
        参数:
            param_combinations: 参数组合列表
            context: 优化上下文
            
        返回:
            所有有效结果列表
        """
        all_results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_params = {
                executor.submit(self._evaluate_single_combination, params, context): params
                for params in param_combinations
            }
            
            # 收集结果
            for future in as_completed(future_to_params, timeout=self.timeout_seconds):
                params = future_to_params[future]
                try:
                    result = future.result()
                    if result is not None:
                        result['params'] = params
                        all_results.append(result)
                except Exception as e:
                    logger.warning(f"参数组合 {params} 评估失败: {e}")
        
        logger.info(f"有效结果数量: {len(all_results)}/{len(param_combinations)}")
        return all_results
    
    def _analyze_optimization_results(self, 
                                    all_results: List[Dict[str, Any]], 
                                    param_combinations: List[Dict[str, Any]],
                                    target_metric: str) -> OptimizationResult:
        """
        分析优化结果
        
        参数:
            all_results: 所有结果
            param_combinations: 参数组合
            target_metric: 目标指标
            
        返回:
            OptimizationResult: 分析后的优化结果
        """
        if not all_results:
            logger.warning("没有有效的优化结果")
            return OptimizationResult(
                best_params={},
                best_score=float('-inf'),
                all_results=[],
                optimization_time=0.0,
                total_combinations=len(param_combinations),
                valid_combinations=0,
                optimization_type=self.optimization_type,
                target_metric=target_metric
            )
        
        # 计算优化评分
        for result in all_results:
            result['optimization_score'] = self._calculate_optimization_score(result)
        
        # 按评分排序
        all_results.sort(key=lambda x: x['optimization_score'], reverse=True)
        
        # 获取最佳结果
        best_result = all_results[0]
        
        return OptimizationResult(
            best_params=best_result['params'],
            best_score=best_result['optimization_score'],
            all_results=all_results,
            optimization_time=0.0,  # 将在外层设置
            total_combinations=len(param_combinations),
            valid_combinations=len(all_results),
            optimization_type=self.optimization_type,
            target_metric=target_metric
        )
    
    def get_optimization_summary(self, result: OptimizationResult) -> Dict[str, Any]:
        """
        获取优化摘要
        
        参数:
            result: 优化结果
            
        返回:
            优化摘要字典
        """
        if not result.all_results:
            return {
                'status': 'failed',
                'message': '没有有效的优化结果'
            }
        
        scores = [r['optimization_score'] for r in result.all_results]
        
        return {
            'status': 'success',
            'optimization_type': result.optimization_type,
            'target_metric': result.target_metric,
            'best_score': result.best_score,
            'best_params': result.best_params,
            'total_combinations': result.total_combinations,
            'valid_combinations': result.valid_combinations,
            'success_rate': result.valid_combinations / result.total_combinations,
            'optimization_time': result.optimization_time,
            'score_statistics': {
                'mean': np.mean(scores),
                'std': np.std(scores),
                'min': np.min(scores),
                'max': np.max(scores),
                'median': np.median(scores)
            }
        }
    
    def save_optimization_results(self, result: OptimizationResult, filepath: str) -> bool:
        """
        保存优化结果
        
        参数:
            result: 优化结果
            filepath: 保存路径
            
        返回:
            是否保存成功
        """
        try:
            # 转换为可序列化的格式
            data = {
                'optimization_info': {
                    'name': self.name,
                    'type': result.optimization_type,
                    'target_metric': result.target_metric,
                    'optimization_time': result.optimization_time,
                    'timestamp': time.time()
                },
                'best_result': {
                    'params': result.best_params,
                    'score': result.best_score
                },
                'statistics': {
                    'total_combinations': result.total_combinations,
                    'valid_combinations': result.valid_combinations,
                    'success_rate': result.valid_combinations / result.total_combinations if result.total_combinations > 0 else 0
                },
                'all_results': result.all_results[:100]  # 只保存前100个结果
            }
            
            import json
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"优化结果已保存到: {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"保存优化结果失败: {e}")
            return False
