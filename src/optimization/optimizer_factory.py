#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化器工厂
提供统一的优化器创建接口
"""

from typing import Dict, Type, Any, Optional
from .base_optimizer import BaseOptimizer
from .strategy_optimizer import UnifiedStrategyOptimizer
from src.utils.logging.logger_factory import get_logger

logger = get_logger(__name__)


class OptimizerFactory:
    """
    优化器工厂类
    
    负责创建和管理不同类型的优化器实例
    """
    
    # 存储已注册的优化器类型
    _optimizers: Dict[str, Type[BaseOptimizer]] = {}
    
    @classmethod
    def register_optimizer(cls, optimizer_type: str, optimizer_class: Type[BaseOptimizer]) -> None:
        """
        注册优化器类型
        
        参数:
            optimizer_type: 优化器类型名称
            optimizer_class: 优化器类
        """
        if optimizer_type in cls._optimizers:
            logger.warning(f"优化器类型 '{optimizer_type}' 已存在，将被覆盖")
            
        cls._optimizers[optimizer_type] = optimizer_class
        logger.info(f"注册优化器: {optimizer_type}")
    
    @classmethod
    def create_optimizer(cls, optimizer_type: str, **kwargs) -> Optional[BaseOptimizer]:
        """
        创建优化器实例
        
        参数:
            optimizer_type: 优化器类型名称
            **kwargs: 传递给优化器构造函数的参数
            
        返回:
            优化器实例，如果类型不存在则返回None
        """
        if optimizer_type not in cls._optimizers:
            logger.error(f"未知的优化器类型: {optimizer_type}")
            raise ValueError(f"未知的优化器类型: {optimizer_type}")
            
        optimizer_class = cls._optimizers[optimizer_type]
        
        try:
            optimizer = optimizer_class(**kwargs)
            logger.info(f"创建优化器: {optimizer_type}")
            return optimizer
        except Exception as e:
            logger.error(f"创建优化器 {optimizer_type} 失败: {e}")
            raise
    
    @classmethod
    def get_available_optimizers(cls) -> Dict[str, Type[BaseOptimizer]]:
        """
        获取所有可用的优化器类型
        
        返回:
            Dict: 优化器类型名称到优化器类的映射
        """
        return cls._optimizers.copy()
    
    @classmethod
    def create_strategy_optimizer(cls, **kwargs) -> UnifiedStrategyOptimizer:
        """
        创建策略优化器的便捷方法
        
        参数:
            **kwargs: 优化器参数
            
        返回:
            UnifiedStrategyOptimizer: 策略优化器实例
        """
        return cls.create_optimizer('strategy', **kwargs)


# 注册默认的优化器类型
OptimizerFactory.register_optimizer('strategy', UnifiedStrategyOptimizer)


def create_optimizer(optimizer_type: str = 'strategy', **kwargs) -> BaseOptimizer:
    """
    创建优化器的便捷函数
    
    参数:
        optimizer_type: 优化器类型，默认为'strategy'
        **kwargs: 优化器参数
        
    返回:
        BaseOptimizer: 优化器实例
    """
    return OptimizerFactory.create_optimizer(optimizer_type, **kwargs)


def create_strategy_optimizer(**kwargs) -> UnifiedStrategyOptimizer:
    """
    创建策略优化器的便捷函数
    
    参数:
        **kwargs: 优化器参数
        
    返回:
        UnifiedStrategyOptimizer: 策略优化器实例
    """
    return OptimizerFactory.create_strategy_optimizer(**kwargs)
