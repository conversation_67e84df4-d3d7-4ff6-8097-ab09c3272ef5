#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高频交易策略
基于您的完美数据平台，实现专业级高频交易算法
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False
from src.strategy.strategies.base_strategy import BaseStrategy

class HighFrequencyStrategy(BaseStrategy):
    """
    高频交易策略
    基于微观结构分析和统计套利的高频交易系统
    """
    
    def __init__(self,
                 lookback_window=20,      # 回望窗口
                 volatility_threshold=0.02, # 波动率阈值
                 volume_threshold=2.0,    # 成交量阈值
                 spread_threshold=0.001,  # 价差阈值
                 max_position_size=0.1,   # 最大仓位
                 stop_loss=0.005,         # 止损
                 take_profit=0.003,       # 止盈
                 **kwargs):
        """
        初始化高频策略参数
        """
        # 调用父类初始化
        super().__init__(name="高频交易策略", **kwargs)

        self.lookback_window = lookback_window
        self.volatility_threshold = volatility_threshold
        self.volume_threshold = volume_threshold
        self.spread_threshold = spread_threshold
        self.max_position_size = max_position_size
        self.stop_loss = stop_loss
        self.take_profit = take_profit
        
        self.db_path = 'output/data/db/sqlite/quantification.db'
    
    def get_high_liquidity_stocks(self, limit=20):
        """
        获取高流动性股票
        
        参数:
            limit (int): 股票数量限制
            
        返回:
            list: 高流动性股票代码列表
        """
        conn = sqlite3.connect(self.db_path)
        
        # 获取成交量和成交额最大的股票
        sql = """
        WITH stock_liquidity AS (
            SELECT
                d.ts_code,
                s.name,
                AVG(d.vol) as avg_volume,
                AVG(d.amount) as avg_amount,
                AVG(d.amount / d.vol) as avg_price,
                COUNT(*) as trading_days
            FROM daily d
            JOIN stock_list s ON d.ts_code = s.ts_code
            WHERE d.vol > 0 AND d.amount > 0
            GROUP BY d.ts_code, s.name
            HAVING COUNT(*) >= 50  -- 至少50个交易日
        )
        SELECT
            ts_code, name, avg_volume, avg_amount, avg_price
        FROM stock_liquidity
        WHERE avg_amount > 3000000  -- 日均成交额大于300万
        ORDER BY avg_amount DESC
        LIMIT ?
        """
        
        stocks = pd.read_sql(sql, conn, params=[limit])
        conn.close()
        
        return stocks['ts_code'].tolist()
    
    def calculate_microstructure_indicators(self, data):
        """
        计算微观结构指标
        
        参数:
            data (DataFrame): 股票数据
            
        返回:
            DataFrame: 添加了微观结构指标的数据
        """
        df = data.copy()
        
        # 计算价格变化
        df['price_change'] = df['close'].diff()
        df['price_change_pct'] = df['close'].pct_change()
        
        # 计算成交量变化
        df['volume_change'] = df['vol'].diff()
        df['volume_change_pct'] = df['vol'].pct_change()
        
        # 计算价量关系
        df['price_volume_corr'] = df['price_change'].rolling(
            window=self.lookback_window
        ).corr(df['volume_change'])
        
        # 计算波动率
        df['volatility'] = df['price_change_pct'].rolling(
            window=self.lookback_window
        ).std() * np.sqrt(252)
        
        # 计算相对强弱指标
        df['rsi'] = self.calculate_rsi(df['close'])
        
        # 计算布林带
        df = self.calculate_bollinger_bands(df)
        
        # 计算VWAP (成交量加权平均价格)
        df['vwap'] = (df['amount'] / df['vol']).rolling(
            window=self.lookback_window
        ).mean()
        
        # 计算价格偏离度
        df['price_deviation'] = (df['close'] - df['vwap']) / df['vwap']
        
        return df
    
    def calculate_rsi(self, prices, window=14):
        """
        计算RSI指标
        """
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_bollinger_bands(self, data, window=20, num_std=2):
        """
        计算布林带
        """
        df = data.copy()
        df['bb_middle'] = df['close'].rolling(window=window).mean()
        bb_std = df['close'].rolling(window=window).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * num_std)
        df['bb_lower'] = df['bb_middle'] - (bb_std * num_std)
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        return df
    
    def generate_high_frequency_signals(self, data):
        """
        生成高频交易信号
        
        参数:
            data (DataFrame): 包含微观结构指标的数据
            
        返回:
            DataFrame: 添加了交易信号的数据
        """
        df = data.copy()
        
        # 初始化信号
        df['signal'] = 0
        df['signal_strength'] = 0.0
        df['position'] = 0
        df['entry_price'] = 0.0
        df['exit_reason'] = ''
        
        position = 0
        entry_price = 0.0
        
        for i in range(self.lookback_window, len(df)):
            current_price = df.loc[i, 'close']
            
            # 获取当前指标值
            volatility = df.loc[i, 'volatility']
            rsi = df.loc[i, 'rsi']
            bb_position = df.loc[i, 'bb_position']
            price_deviation = df.loc[i, 'price_deviation']
            volume_change_pct = df.loc[i, 'volume_change_pct']
            
            # 跳过无效数据
            if pd.isna(volatility) or pd.isna(rsi) or pd.isna(bb_position):
                continue
            
            # 买入信号条件
            buy_conditions = (
                position == 0 and  # 当前无持仓
                volatility > self.volatility_threshold and  # 波动率足够
                volume_change_pct > self.volume_threshold and  # 成交量放大
                rsi < 30 and  # 超卖
                bb_position < 0.2 and  # 接近布林带下轨
                price_deviation < -self.spread_threshold  # 价格低于VWAP
            )
            
            # 卖出信号条件
            sell_conditions = (
                position == 0 and  # 当前无持仓
                volatility > self.volatility_threshold and  # 波动率足够
                volume_change_pct > self.volume_threshold and  # 成交量放大
                rsi > 70 and  # 超买
                bb_position > 0.8 and  # 接近布林带上轨
                price_deviation > self.spread_threshold  # 价格高于VWAP
            )
            
            # 执行买入
            if buy_conditions:
                df.loc[i, 'signal'] = 1
                df.loc[i, 'signal_strength'] = min(
                    (self.volume_threshold - volume_change_pct) / self.volume_threshold +
                    (30 - rsi) / 30 +
                    abs(price_deviation) / self.spread_threshold,
                    1.0
                )
                position = 1
                entry_price = current_price
                df.loc[i, 'entry_price'] = entry_price
            
            # 执行卖出
            elif sell_conditions:
                df.loc[i, 'signal'] = -1
                df.loc[i, 'signal_strength'] = min(
                    (self.volume_threshold - volume_change_pct) / self.volume_threshold +
                    (rsi - 70) / 30 +
                    abs(price_deviation) / self.spread_threshold,
                    1.0
                )
                position = -1
                entry_price = current_price
                df.loc[i, 'entry_price'] = entry_price
            
            # 平仓条件
            elif position != 0:
                # 止盈止损
                if position == 1:  # 多头持仓
                    if current_price >= entry_price * (1 + self.take_profit):
                        df.loc[i, 'signal'] = -1
                        df.loc[i, 'exit_reason'] = '止盈'
                        position = 0
                        entry_price = 0.0
                    elif current_price <= entry_price * (1 - self.stop_loss):
                        df.loc[i, 'signal'] = -1
                        df.loc[i, 'exit_reason'] = '止损'
                        position = 0
                        entry_price = 0.0
                    elif rsi > 70 or bb_position > 0.8:  # 技术指标平仓
                        df.loc[i, 'signal'] = -1
                        df.loc[i, 'exit_reason'] = '技术平仓'
                        position = 0
                        entry_price = 0.0
                
                elif position == -1:  # 空头持仓
                    if current_price <= entry_price * (1 - self.take_profit):
                        df.loc[i, 'signal'] = 1
                        df.loc[i, 'exit_reason'] = '止盈'
                        position = 0
                        entry_price = 0.0
                    elif current_price >= entry_price * (1 + self.stop_loss):
                        df.loc[i, 'signal'] = 1
                        df.loc[i, 'exit_reason'] = '止损'
                        position = 0
                        entry_price = 0.0
                    elif rsi < 30 or bb_position < 0.2:  # 技术指标平仓
                        df.loc[i, 'signal'] = 1
                        df.loc[i, 'exit_reason'] = '技术平仓'
                        position = 0
                        entry_price = 0.0
            
            # 记录当前持仓状态
            df.loc[i, 'position'] = position
        
        return df
    
    def backtest_high_frequency_strategy(self, ts_code, days=60, initial_capital=100000):
        """
        回测高频策略
        
        参数:
            ts_code (str): 股票代码
            days (int): 回测天数
            initial_capital (float): 初始资金
            
        返回:
            dict: 回测结果
        """
        conn = sqlite3.connect(self.db_path)
        
        sql = """
        SELECT trade_date, ts_code, open, high, low, close, vol, amount
        FROM daily 
        WHERE ts_code = ?
        ORDER BY trade_date DESC
        LIMIT ?
        """
        
        data = pd.read_sql(sql, conn, params=[ts_code, days])
        conn.close()
        
        if len(data) < self.lookback_window + 10:
            return None
        
        # 按日期排序
        data = data.sort_values('trade_date').reset_index(drop=True)
        
        # 计算微观结构指标
        data = self.calculate_microstructure_indicators(data)
        
        # 生成交易信号
        signals = self.generate_high_frequency_signals(data)
        
        # 计算收益
        return self.calculate_high_frequency_returns(signals, initial_capital)
    
    def calculate_high_frequency_returns(self, signals, initial_capital):
        """
        计算高频策略收益
        """
        df = signals.copy()
        
        # 初始化资金和持仓
        cash = initial_capital
        shares = 0
        portfolio_values = []
        trades = []
        
        for i, row in df.iterrows():
            current_price = row['close']
            
            if row['signal'] == 1 and shares <= 0:  # 买入或平空
                if shares < 0:  # 平空头
                    cash += abs(shares) * current_price
                    trades.append({
                        'type': '平空',
                        'price': current_price,
                        'shares': abs(shares),
                        'date': row['trade_date']
                    })
                    shares = 0
                
                # 买入
                position_size = min(self.max_position_size, cash / current_price)
                if position_size > 0:
                    shares += position_size
                    cash -= position_size * current_price
                    trades.append({
                        'type': '买入',
                        'price': current_price,
                        'shares': position_size,
                        'date': row['trade_date']
                    })
            
            elif row['signal'] == -1 and shares >= 0:  # 卖出或平多
                if shares > 0:  # 平多头
                    cash += shares * current_price
                    trades.append({
                        'type': '平多',
                        'price': current_price,
                        'shares': shares,
                        'date': row['trade_date']
                    })
                    shares = 0
                
                # 卖空（简化处理）
                position_size = min(self.max_position_size, cash / current_price)
                if position_size > 0:
                    shares -= position_size
                    cash += position_size * current_price
                    trades.append({
                        'type': '卖空',
                        'price': current_price,
                        'shares': position_size,
                        'date': row['trade_date']
                    })
            
            # 计算当前资产价值
            portfolio_value = cash + shares * current_price
            portfolio_values.append(portfolio_value)
        
        df['portfolio_value'] = portfolio_values
        df['returns'] = df['portfolio_value'].pct_change()
        
        # 计算性能指标
        performance = self.calculate_high_frequency_performance(df, initial_capital, trades)
        
        return {
            'signals': df,
            'performance': performance,
            'trades': trades,
            'final_value': portfolio_values[-1]
        }
    
    def calculate_high_frequency_performance(self, df, initial_capital, trades):
        """
        计算高频策略性能指标
        """
        final_value = df['portfolio_value'].iloc[-1]
        total_return = (final_value / initial_capital) - 1
        
        # 计算基准收益
        benchmark_return = (df['close'].iloc[-1] / df['close'].iloc[0]) - 1
        
        # 计算最大回撤
        df['cummax'] = df['portfolio_value'].cummax()
        df['drawdown'] = (df['cummax'] - df['portfolio_value']) / df['cummax']
        max_drawdown = df['drawdown'].max()
        
        # 计算夏普比率
        returns_std = df['returns'].std()
        if returns_std > 0:
            sharpe_ratio = df['returns'].mean() / returns_std * np.sqrt(252)
        else:
            sharpe_ratio = 0.0
        
        # 计算交易统计
        trade_count = len(trades)
        
        # 计算胜率
        profitable_trades = 0
        total_profit = 0
        
        buy_price = None
        for trade in trades:
            if trade['type'] in ['买入', '卖空']:
                buy_price = trade['price']
            elif trade['type'] in ['平多', '平空'] and buy_price is not None:
                if trade['type'] == '平多':
                    profit = (trade['price'] - buy_price) * trade['shares']
                else:  # 平空
                    profit = (buy_price - trade['price']) * trade['shares']
                
                total_profit += profit
                if profit > 0:
                    profitable_trades += 1
                buy_price = None
        
        win_rate = profitable_trades / (trade_count // 2) if trade_count > 0 else 0
        
        return {
            'total_return': total_return,
            'benchmark_return': benchmark_return,
            'excess_return': total_return - benchmark_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'trade_count': trade_count,
            'win_rate': win_rate,
            'total_profit': total_profit
        }
    
    def run_high_frequency_analysis(self, stock_limit=5):
        """
        运行高频策略分析
        """
        print("⚡ 高频交易策略分析")
        print("基于您的完美数据平台")
        print("=" * 50)
        
        # 获取高流动性股票
        liquid_stocks = self.get_high_liquidity_stocks(stock_limit)
        
        if not liquid_stocks:
            print("❌ 未找到符合条件的高流动性股票")
            return
        
        print(f"高流动性股票池: {len(liquid_stocks)}只")
        print()
        
        results = []
        
        for i, stock in enumerate(liquid_stocks):
            print(f"分析股票 {i+1}/{len(liquid_stocks)}: {stock}")
            
            try:
                result = self.backtest_high_frequency_strategy(stock, days=90)
                
                if result:
                    perf = result['performance']
                    results.append({
                        'stock_code': stock,
                        'total_return': perf['total_return'],
                        'excess_return': perf['excess_return'],
                        'sharpe_ratio': perf['sharpe_ratio'],
                        'max_drawdown': perf['max_drawdown'],
                        'trade_count': perf['trade_count'],
                        'win_rate': perf['win_rate']
                    })
                    
                    print(f"  总收益: {perf['total_return']:.2%}")
                    print(f"  超额收益: {perf['excess_return']:.2%}")
                    print(f"  夏普比率: {perf['sharpe_ratio']:.2f}")
                    print(f"  最大回撤: {perf['max_drawdown']:.2%}")
                    print(f"  交易次数: {perf['trade_count']}")
                    print(f"  胜率: {perf['win_rate']:.2%}")
                    print()
                
            except Exception as e:
                print(f"  分析失败: {e}")
                continue
        
        # 生成总结
        if results:
            self.generate_high_frequency_summary(results)
        
        return results
    
    def generate_high_frequency_summary(self, results):
        """
        生成高频策略总结
        """
        df = pd.DataFrame(results)
        
        print("📊 高频策略总结")
        print("=" * 50)
        print(f"成功分析股票数: {len(results)}")
        print(f"平均总收益: {df['total_return'].mean():.2%}")
        print(f"平均超额收益: {df['excess_return'].mean():.2%}")
        print(f"平均夏普比率: {df['sharpe_ratio'].mean():.2f}")
        print(f"平均最大回撤: {df['max_drawdown'].mean():.2%}")
        print(f"平均交易次数: {df['trade_count'].mean():.0f}")
        print(f"平均胜率: {df['win_rate'].mean():.2%}")
        print()
        
        # 最佳表现股票
        best_stock = df.loc[df['sharpe_ratio'].idxmax()]
        print(f"🏆 最佳表现股票: {best_stock['stock_code']}")
        print(f"  夏普比率: {best_stock['sharpe_ratio']:.2f}")
        print(f"  总收益: {best_stock['total_return']:.2%}")
        print(f"  胜率: {best_stock['win_rate']:.2%}")

def main():
    """
    主函数
    """
    strategy = HighFrequencyStrategy(
        lookback_window=20,
        volatility_threshold=0.02,
        volume_threshold=1.5,
        spread_threshold=0.001,
        stop_loss=0.005,
        take_profit=0.003
    )
    
    results = strategy.run_high_frequency_analysis(stock_limit=8)

if __name__ == "__main__":
    main()
