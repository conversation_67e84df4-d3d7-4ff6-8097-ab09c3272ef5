"""
优化版移动平均线策略
基于全A股回测结果进行参数和逻辑优化

优化要点：
1. 调整移动平均线窗口：5/20 → 8/25
2. 增加信号确认机制：连续2天信号一致才执行
3. 添加成交量过滤：只在放量时执行交易
4. 增加止损机制：控制单笔最大亏损
"""

import pandas as pd
import numpy as np
from src.strategies.base.moving_average_base import MovingAverageBase
# {{ AURA-X: Add - 导入技术指标工具类，消除重复代码. Approval: 寸止(ID:重构阶段3). }}
from src.utils.technical_indicators import TechnicalIndicators, BacktestUtils


class OptimizedMovingAverageStrategy(MovingAverageBase):
    """
    优化版移动平均线策略
    
    参数优化：
    - 短期窗口：5 → 8 天
    - 长期窗口：20 → 25 天
    - 增加信号确认：连续2天信号一致
    - 成交量过滤：当日成交量 > 5日平均成交量
    - 止损机制：单笔亏损超过5%时止损
    """
    
    def __init__(self, short_window=8, long_window=25, signal_confirm_days=2,
                 volume_threshold=1.2, stop_loss_pct=0.05, **kwargs):
        """
        初始化优化版移动平均线策略

        参数:
            short_window (int): 短期移动平均窗口，默认8天
            long_window (int): 长期移动平均窗口，默认25天
            signal_confirm_days (int): 信号确认天数，默认2天
            volume_threshold (float): 成交量阈值倍数，默认1.2倍
            stop_loss_pct (float): 止损百分比，默认5%
            **kwargs: 其他参数
        """
        # {{ AURA-X: Modify - 继承MovingAverageBase，启用优化功能和止损. Approval: 寸止(ID:策略重复修复). }}
        # 先设置属性，避免在父类初始化时访问不存在的属性
        self.signal_confirm_days = signal_confirm_days
        self.volume_threshold = volume_threshold
        self.stop_loss_pct = stop_loss_pct

        # 调用父类初始化，启用所有优化功能
        super().__init__(
            short_window=short_window,
            long_window=long_window,
            signal_confirm_days=signal_confirm_days,
            volume_threshold=volume_threshold,
            enable_volume_filter=True,
            enable_signal_confirmation=True,
            name=f"优化移动平均策略_{short_window}_{long_window}",
            **kwargs
        )
    
    # {{ AURA-X: Remove - 删除重复的信号生成逻辑，使用基类实现. Approval: 寸止(ID:策略重复修复). }}
    # generate_signals方法已在基类中实现，包含成交量过滤和信号确认功能

    
    def backtest(self, symbols, start_date=None, end_date=None, initial_capital=1000000, benchmark=None):
        """
        执行优化版回测

        参数:
            symbols: 股票代码或DataFrame数据
            start_date: 开始日期
            end_date: 结束日期
            initial_capital (float): 初始资金
            benchmark: 基准指数

        返回:
            DataFrame: 回测结果
        """
        # {{ AURA-X: Modify - 使用基类的统一回测逻辑，启用止损功能. Approval: 寸止(ID:策略重复修复). }}
        # 如果symbols是DataFrame，直接使用；否则需要获取数据
        if isinstance(symbols, pd.DataFrame):
            data = symbols
        else:
            # 这里应该从数据源获取数据，暂时返回空结果
            print("需要传入DataFrame格式的数据")
            return pd.DataFrame()

        # 使用基类的回测方法，启用止损功能
        return self.execute_backtest(
            data=data,
            initial_capital=initial_capital,
            enable_stop_loss=True,
            stop_loss_pct=self.stop_loss_pct
        )

        # 兼容性处理：重命名列以保持向后兼容
        if 'position_value' in positions.columns:
            positions['position'] = positions['position_value']
        
        return positions
    
    def show_performance(self, positions):
        """
        显示策略性能指标
        
        参数:
            positions (DataFrame): 回测结果数据框
        """
        # 计算总收益率
        initial_value = positions['total'].iloc[0]
        final_value = positions['total'].iloc[-1]
        total_return = (final_value / initial_value) - 1
        
        # 计算年化收益率
        total_days = len(positions)
        annual_return = total_return * (252 / total_days)
        
        # 计算最大回撤
        positions['cummax'] = positions['total'].cummax()
        positions['drawdown'] = (positions['cummax'] - positions['total']) / positions['cummax']
        max_drawdown = positions['drawdown'].max()
        
        # 计算夏普比率（处理除零情况）
        returns_std = positions['returns'].std()
        if returns_std > 0:
            sharpe_ratio = positions['returns'].mean() / returns_std * np.sqrt(252)
        else:
            sharpe_ratio = 0.0
        
        # 计算交易次数（信号变化次数）
        signal_changes = (positions['signal'].diff() != 0).sum()
        
        # 计算胜率（盈利交易占比）
        trades = []
        entry_price = None
        for i, row in positions.iterrows():
            if row['signal'] == 1.0 and entry_price is None:
                entry_price = row['close']
            elif row['signal'] == 0.0 and entry_price is not None:
                exit_price = row['close']
                trade_return = (exit_price - entry_price) / entry_price
                trades.append(trade_return)
                entry_price = None
        
        win_rate = 0.0
        if trades:
            winning_trades = [t for t in trades if t > 0]
            win_rate = len(winning_trades) / len(trades)
        
        # 打印结果
        print(f"优化版策略性能指标:")
        print(f"总收益率: {total_return:.2%}")
        print(f"年化收益率: {annual_return:.2%}")
        print(f"最大回撤: {max_drawdown:.2%}")
        print(f"夏普比率: {sharpe_ratio:.2f}")
        print(f"交易次数: {signal_changes}")
        print(f"胜率: {win_rate:.2%}")
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'trades': signal_changes,
            'win_rate': win_rate
        }
    
    def run(self, symbols, params=None, initial_capital=1000000, start_date=None, end_date=None, benchmark=None):
        """
        运行策略

        参数:
            symbols: 股票代码或数据DataFrame
            params: 策略参数（兼容父类接口）
            initial_capital (float): 初始资金
            start_date (str): 开始日期
            end_date (str): 结束日期
            benchmark: 基准指数

        返回:
            dict: 包含回测结果和性能指标的字典
        """
        # 兼容处理：如果symbols是DataFrame，直接使用
        if isinstance(symbols, pd.DataFrame):
            data = symbols
        else:
            # 这里应该从数据源获取数据，暂时返回空结果
            print("需要传入DataFrame格式的数据")
            return {}
        # 数据预处理
        if start_date or end_date:
            if 'trade_date' in data.columns:
                if start_date:
                    data = data[data['trade_date'] >= start_date]
                if end_date:
                    data = data[data['trade_date'] <= end_date]
        
        # {{ AURA-X: Modify - 修复backtest方法调用，传递正确的参数. Approval: 寸止(ID:阶段2.4). }}
        # 执行回测，传递正确的参数
        positions = self.backtest(
            data,
            start_date=start_date,
            end_date=end_date,
            initial_capital=initial_capital,
            benchmark=benchmark
        )

        # 计算性能指标
        performance = self.show_performance(positions)
        
        return {
            'positions': positions,
            'performance': performance,
            'portfolio': {
                'initial_capital': initial_capital,
                'final_capital': positions['total'].iloc[-1]
            }
        }

    # {{ AURA-X: Add - 重写参数管理方法，支持优化策略特有参数. Approval: 寸止(ID:阶段2.4). }}
    def get_parameters(self) -> dict:
        """获取策略参数，包含优化策略特有参数"""
        base_params = super().get_parameters()
        base_params.update({
            'signal_confirm_days': self.signal_confirm_days,
            'volume_threshold': self.volume_threshold,
            'stop_loss_pct': self.stop_loss_pct,
            'strategy_type': 'optimized_momentum'
        })
        return base_params

    def set_parameters(self, parameters: dict) -> None:
        """设置策略参数，包含优化策略特有参数"""
        # 调用父类方法设置基础参数
        super().set_parameters(parameters)

        # 设置优化策略特有参数
        if 'signal_confirm_days' in parameters:
            self.signal_confirm_days = parameters['signal_confirm_days']
        if 'volume_threshold' in parameters:
            self.volume_threshold = parameters['volume_threshold']
        if 'stop_loss_pct' in parameters:
            self.stop_loss_pct = parameters['stop_loss_pct']

        # 更新参数字典
        self.parameters.update({
            'signal_confirm_days': self.signal_confirm_days,
            'volume_threshold': self.volume_threshold,
            'stop_loss_pct': self.stop_loss_pct
        })

    def get_required_data(self) -> dict:
        """获取策略所需的数据描述，包含成交量数据"""
        base_req = super().get_required_data()
        base_req['market_data']['fields'].append('vol')  # 添加成交量字段
        base_req['market_data']['lookback_periods'] = max(
            self.short_window,
            self.long_window,
            self.signal_confirm_days
        ) + 10
        return base_req

# 注册策略到工厂
from src.strategy.strategies.base_strategy import StrategyFactory
try:
    StrategyFactory.register('optimized_moving_average', OptimizedMovingAverageStrategy)
except ValueError:
    # 策略已注册，忽略错误
    pass
