#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略运行器
基于您的完美数据平台，运行和比较不同的量化策略
"""

import pandas as pd
import numpy as np
import sqlite3
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategies.enhanced_dual_ma_strategy import EnhancedDualMAStrategy
from strategies.value_investment_strategy import ValueInvestmentStrategy
from strategies.enhanced_multi_factor_strategy import EnhancedMultiFactorStrategy

class StrategyRunner:
    """
    策略运行器
    用于运行和比较不同的量化策略
    """
    
    def __init__(self):
        self.db_path = 'output/data/db/sqlite/quantification.db'
        self.strategies = {}
        self.results = {}
    
    def get_available_stocks(self, min_market_cap=10):
        """
        获取可用的股票列表
        
        参数:
            min_market_cap (float): 最小市值要求(亿元)
            
        返回:
            list: 股票代码列表
        """
        conn = sqlite3.connect(self.db_path)
        
        sql = """
        SELECT DISTINCT m.ts_code, s.name, m.total_mv
        FROM market_cap m
        JOIN stock_list s ON m.ts_code = s.ts_code
        WHERE m.total_mv >= ?
        ORDER BY m.total_mv DESC
        LIMIT 100
        """
        
        stocks = pd.read_sql(sql, conn, params=[min_market_cap * 10000])
        conn.close()
        
        return stocks['ts_code'].tolist()
    
    def run_dual_ma_strategy(self, stock_codes=None):
        """
        运行增强版双均线策略
        """
        print("🚀 运行增强版双均线策略...")
        
        strategy = EnhancedDualMAStrategy(
            short_window=5,
            long_window=20,
            volume_threshold=1.5,
            stop_loss=0.05,
            take_profit=0.15
        )
        
        if not stock_codes:
            stock_codes = self.get_available_stocks()[:10]  # 测试前10只股票
        
        results = []
        
        for i, stock_code in enumerate(stock_codes[:5]):  # 限制测试数量
            try:
                print(f"  测试股票 {i+1}/{min(5, len(stock_codes))}: {stock_code}")
                
                result = strategy.backtest(
                    ts_code=stock_code,
                    start_date='20240701',
                    end_date='20250701',
                    initial_capital=100000
                )
                
                results.append({
                    'stock_code': stock_code,
                    'total_return': result['performance']['total_return'],
                    'max_drawdown': result['performance']['max_drawdown'],
                    'sharpe_ratio': result['performance']['sharpe_ratio'],
                    'trades': result['performance']['trades'],
                    'final_value': result['final_value']
                })
                
            except Exception as e:
                print(f"    股票 {stock_code} 测试失败: {e}")
                continue
        
        if results:
            df_results = pd.DataFrame(results)
            avg_return = df_results['total_return'].mean()
            avg_sharpe = df_results['sharpe_ratio'].mean()
            avg_drawdown = df_results['max_drawdown'].mean()
            
            print(f"  ✅ 策略测试完成:")
            print(f"    平均收益率: {avg_return:.2%}")
            print(f"    平均夏普比率: {avg_sharpe:.2f}")
            print(f"    平均最大回撤: {avg_drawdown:.2%}")
            
            self.results['dual_ma'] = {
                'strategy_name': '增强版双均线策略',
                'avg_return': avg_return,
                'avg_sharpe': avg_sharpe,
                'avg_drawdown': avg_drawdown,
                'test_stocks': len(results),
                'details': df_results
            }
        else:
            print("  ❌ 策略测试失败，无有效结果")
    
    def run_value_strategy(self):
        """
        运行价值投资策略
        """
        print("💎 运行价值投资策略...")
        
        strategy = ValueInvestmentStrategy(
            pe_max=15,
            pb_max=2,
            roe_min=0.15,
            debt_ratio_max=0.6,
            stock_num=20
        )
        
        try:
            result = strategy.run(
                start_date='20240101',
                end_date='20250701',
                initial_capital=1000000
            )
            
            print(f"  ✅ 策略测试完成:")
            print(f"    总收益率: {result['performance']['total_return']:.2%}")
            print(f"    调仓次数: {result['performance']['rebalance_count']}")
            
            # 显示最新选股
            if result['holdings_history']:
                latest_holdings = result['holdings_history'][-1]
                print(f"    最新选股数量: {len(latest_holdings['holdings'])}")
            
            self.results['value'] = {
                'strategy_name': '价值投资策略',
                'total_return': result['performance']['total_return'],
                'rebalance_count': result['performance']['rebalance_count'],
                'final_value': result['final_value'],
                'details': result
            }
            
        except Exception as e:
            print(f"  ❌ 策略测试失败: {e}")
    
    def run_multi_factor_strategy(self):
        """
        运行多因子选股策略
        """
        print("📊 运行增强版多因子选股策略...")
        
        strategy = EnhancedMultiFactorStrategy(
            value_weight=0.3,
            quality_weight=0.25,
            growth_weight=0.2,
            momentum_weight=0.15,
            volatility_weight=0.1,
            stock_num=30,
            min_market_cap=50
        )
        
        try:
            result = strategy.run(date='20250701')
            
            if not result['selected_stocks'].empty:
                print(f"  ✅ 策略测试完成:")
                print(f"    选中股票数: {len(result['selected_stocks'])}")
                print(f"    平均得分: {result['summary']['avg_score']:.3f}")
                print(f"    平均市值: {result['summary']['avg_market_cap']:.1f}亿元")
                print(f"    平均PE: {result['summary']['avg_pe']:.1f}")
                
                self.results['multi_factor'] = {
                    'strategy_name': '增强版多因子选股策略',
                    'selected_count': len(result['selected_stocks']),
                    'avg_score': result['summary']['avg_score'],
                    'avg_market_cap': result['summary']['avg_market_cap'],
                    'avg_pe': result['summary']['avg_pe'],
                    'details': result
                }
            else:
                print(f"  ⚠️ 策略运行完成，但未选出股票")
                
        except Exception as e:
            print(f"  ❌ 策略测试失败: {e}")
    
    def run_all_strategies(self):
        """
        运行所有策略
        """
        print("🎯 开始运行所有量化策略...")
        print("=" * 60)
        
        # 运行各个策略
        self.run_dual_ma_strategy()
        print()
        
        self.run_value_strategy()
        print()
        
        self.run_multi_factor_strategy()
        print()
        
        # 生成总结报告
        self.generate_summary_report()
    
    def generate_summary_report(self):
        """
        生成策略对比总结报告
        """
        print("📋 策略对比总结报告")
        print("=" * 60)
        
        if not self.results:
            print("❌ 没有可用的策略结果")
            return
        
        print(f"📈 策略表现总览:")
        print()
        
        for strategy_key, result in self.results.items():
            print(f"🔹 {result['strategy_name']}:")
            
            if strategy_key == 'dual_ma':
                print(f"   平均收益率: {result['avg_return']:.2%}")
                print(f"   平均夏普比率: {result['avg_sharpe']:.2f}")
                print(f"   平均最大回撤: {result['avg_drawdown']:.2%}")
                print(f"   测试股票数: {result['test_stocks']}")
                
            elif strategy_key == 'value':
                print(f"   总收益率: {result['total_return']:.2%}")
                print(f"   调仓次数: {result['rebalance_count']}")
                print(f"   最终资产: {result['final_value']:,.0f}元")
                
            elif strategy_key == 'multi_factor':
                print(f"   选中股票数: {result['selected_count']}")
                print(f"   平均得分: {result['avg_score']:.3f}")
                print(f"   平均市值: {result['avg_market_cap']:.1f}亿元")
                print(f"   平均PE: {result['avg_pe']:.1f}")
            
            print()
        
        print("🎉 策略测试完成！")
        print("💡 建议: 可以根据不同市场环境选择合适的策略组合")
    
    def get_strategy_recommendations(self):
        """
        获取策略建议
        """
        recommendations = []
        
        if 'dual_ma' in self.results:
            dual_ma = self.results['dual_ma']
            if dual_ma['avg_return'] > 0.1 and dual_ma['avg_sharpe'] > 1.0:
                recommendations.append("增强版双均线策略表现优秀，适合趋势明显的市场")
        
        if 'value' in self.results:
            value = self.results['value']
            if value['total_return'] > 0.05:
                recommendations.append("价值投资策略稳健，适合长期投资")
        
        if 'multi_factor' in self.results:
            multi_factor = self.results['multi_factor']
            if multi_factor['selected_count'] >= 20:
                recommendations.append("多因子策略选股充分，适合分散投资")
        
        return recommendations

def main():
    """
    主函数
    """
    print("🚀 量化策略测试平台")
    print("基于您的完美数据平台 - 280万条数据记录")
    print("=" * 60)
    
    runner = StrategyRunner()
    
    try:
        # 运行所有策略
        runner.run_all_strategies()
        
        # 获取建议
        recommendations = runner.get_strategy_recommendations()
        if recommendations:
            print("💡 策略建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")
        
    except Exception as e:
        print(f"❌ 策略测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
