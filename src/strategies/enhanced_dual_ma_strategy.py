#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版双均线策略
基于您的完美数据平台，结合技术指标和成交量的双均线策略
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False
from src.strategy.strategies.base_strategy import BaseStrategy

class EnhancedDualMAStrategy(BaseStrategy):
    """
    增强版双均线策略
    结合成交量、波动率等多个技术指标的双均线策略
    """
    
    def __init__(self,
                 short_window=5,
                 long_window=20,
                 volume_threshold=1.5,  # 成交量放大倍数阈值
                 volatility_threshold=0.02,  # 波动率阈值
                 stop_loss=0.05,  # 止损比例
                 take_profit=0.15,  # 止盈比例
                 **kwargs):
        """
        初始化策略参数

        参数:
            short_window (int): 短期均线窗口
            long_window (int): 长期均线窗口
            volume_threshold (float): 成交量放大倍数阈值
            volatility_threshold (float): 波动率阈值
            stop_loss (float): 止损比例
            take_profit (float): 止盈比例
            **kwargs: 其他参数
        """
        # 调用父类初始化
        super().__init__(name="增强版双均线策略", **kwargs)

        self.short_window = short_window
        self.long_window = long_window
        self.volume_threshold = volume_threshold
        self.volatility_threshold = volatility_threshold
        self.stop_loss = stop_loss
        self.take_profit = take_profit
        
        # 数据库连接
        self.db_path = 'output/data/db/sqlite/quantification.db'
    
    def load_stock_data(self, ts_code, start_date=None, end_date=None):
        """
        从数据库加载股票数据
        
        参数:
            ts_code (str): 股票代码
            start_date (str): 开始日期 (YYYYMMDD)
            end_date (str): 结束日期 (YYYYMMDD)
            
        返回:
            DataFrame: 股票日线数据
        """
        conn = sqlite3.connect(self.db_path)
        
        # 构建SQL查询
        sql = """
        SELECT trade_date, ts_code, open, high, low, close, vol, amount
        FROM daily 
        WHERE ts_code = ?
        """
        params = [ts_code]
        
        if start_date:
            sql += " AND trade_date >= ?"
            params.append(start_date)
        
        if end_date:
            sql += " AND trade_date <= ?"
            params.append(end_date)
            
        sql += " ORDER BY trade_date"
        
        data = pd.read_sql(sql, conn, params=params)
        conn.close()
        
        if data.empty:
            raise ValueError(f"未找到股票 {ts_code} 的数据")
        
        # 转换数据类型
        data['trade_date'] = pd.to_datetime(data['trade_date'], format='%Y%m%d')
        for col in ['open', 'high', 'low', 'close', 'vol', 'amount']:
            data[col] = pd.to_numeric(data[col], errors='coerce')
        
        return data.reset_index(drop=True)
    
    def calculate_technical_indicators(self, data):
        """
        计算技术指标
        
        参数:
            data (DataFrame): 股票数据
            
        返回:
            DataFrame: 添加了技术指标的数据
        """
        df = data.copy()
        
        # 计算移动平均线
        df['ma_short'] = df['close'].rolling(window=self.short_window, min_periods=1).mean()
        df['ma_long'] = df['close'].rolling(window=self.long_window, min_periods=1).mean()
        
        # 计算成交量移动平均
        df['vol_ma'] = df['vol'].rolling(window=20, min_periods=1).mean()
        df['vol_ratio'] = df['vol'] / df['vol_ma']
        
        # 计算波动率 (20日历史波动率)
        df['returns'] = df['close'].pct_change()
        df['volatility'] = df['returns'].rolling(window=20, min_periods=1).std() * np.sqrt(252)
        
        # 计算RSI
        df['rsi'] = self.calculate_rsi(df['close'], 14)
        
        # 计算MACD
        df = self.calculate_macd(df)
        
        return df
    
    def calculate_rsi(self, prices, window=14):
        """
        计算RSI指标
        """
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_macd(self, data, fast=12, slow=26, signal=9):
        """
        计算MACD指标
        """
        df = data.copy()
        df['ema_fast'] = df['close'].ewm(span=fast).mean()
        df['ema_slow'] = df['close'].ewm(span=slow).mean()
        df['macd'] = df['ema_fast'] - df['ema_slow']
        df['macd_signal'] = df['macd'].ewm(span=signal).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        return df
    
    def generate_signals(self, data):
        """
        生成交易信号
        
        参数:
            data (DataFrame): 包含技术指标的数据
            
        返回:
            DataFrame: 添加了信号的数据
        """
        df = data.copy()
        
        # 初始化信号
        df['signal'] = 0
        df['position'] = 0
        df['entry_price'] = 0.0
        df['exit_reason'] = ''
        
        position = 0  # 0: 空仓, 1: 多头
        entry_price = 0.0
        
        for i in range(1, len(df)):
            current_price = df.loc[i, 'close']
            prev_ma_short = df.loc[i-1, 'ma_short']
            prev_ma_long = df.loc[i-1, 'ma_long']
            curr_ma_short = df.loc[i, 'ma_short']
            curr_ma_long = df.loc[i, 'ma_long']
            
            # 买入条件
            if (position == 0 and 
                prev_ma_short <= prev_ma_long and 
                curr_ma_short > curr_ma_long and  # 金叉
                df.loc[i, 'vol_ratio'] > self.volume_threshold and  # 成交量放大
                df.loc[i, 'rsi'] < 70 and  # RSI不过热
                df.loc[i, 'macd_histogram'] > 0):  # MACD柱状图为正
                
                df.loc[i, 'signal'] = 1
                position = 1
                entry_price = current_price
                df.loc[i, 'entry_price'] = entry_price
            
            # 卖出条件
            elif position == 1:
                # 止损
                if current_price <= entry_price * (1 - self.stop_loss):
                    df.loc[i, 'signal'] = -1
                    df.loc[i, 'exit_reason'] = '止损'
                    position = 0
                    entry_price = 0.0
                
                # 止盈
                elif current_price >= entry_price * (1 + self.take_profit):
                    df.loc[i, 'signal'] = -1
                    df.loc[i, 'exit_reason'] = '止盈'
                    position = 0
                    entry_price = 0.0
                
                # 技术信号卖出
                elif (prev_ma_short >= prev_ma_long and 
                      curr_ma_short < curr_ma_long):  # 死叉
                    df.loc[i, 'signal'] = -1
                    df.loc[i, 'exit_reason'] = '技术信号'
                    position = 0
                    entry_price = 0.0
            
            # 记录当前持仓状态
            df.loc[i, 'position'] = position
        
        return df
    
    def backtest(self, ts_code, start_date=None, end_date=None, initial_capital=100000.0):
        """
        回测策略
        
        参数:
            ts_code (str): 股票代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            initial_capital (float): 初始资金
            
        返回:
            dict: 回测结果
        """
        # 加载数据
        data = self.load_stock_data(ts_code, start_date, end_date)
        
        # 计算技术指标
        data = self.calculate_technical_indicators(data)
        
        # 生成交易信号
        signals = self.generate_signals(data)
        
        # 计算收益
        return self.calculate_returns(signals, initial_capital)
    
    def calculate_returns(self, signals, initial_capital):
        """
        计算策略收益
        """
        df = signals.copy()
        
        # 初始化资金和持仓
        cash = initial_capital
        shares = 0
        portfolio_values = []
        
        for i, row in df.iterrows():
            if row['signal'] == 1:  # 买入
                shares = cash / row['close']
                cash = 0
            elif row['signal'] == -1:  # 卖出
                cash = shares * row['close']
                shares = 0
            
            # 计算当前资产价值
            portfolio_value = cash + shares * row['close']
            portfolio_values.append(portfolio_value)
        
        df['portfolio_value'] = portfolio_values
        df['returns'] = df['portfolio_value'].pct_change()
        
        # 计算性能指标
        performance = self.calculate_performance(df, initial_capital)
        
        return {
            'signals': df,
            'performance': performance,
            'final_value': portfolio_values[-1]
        }
    
    def calculate_performance(self, df, initial_capital):
        """
        计算性能指标
        """
        final_value = df['portfolio_value'].iloc[-1]
        total_return = (final_value / initial_capital) - 1
        
        # 计算最大回撤
        df['cummax'] = df['portfolio_value'].cummax()
        df['drawdown'] = (df['cummax'] - df['portfolio_value']) / df['cummax']
        max_drawdown = df['drawdown'].max()
        
        # 计算夏普比率
        returns_std = df['returns'].std()
        if returns_std > 0:
            sharpe_ratio = df['returns'].mean() / returns_std * np.sqrt(252)
        else:
            sharpe_ratio = 0.0
        
        # 计算交易次数
        trades = (df['signal'] != 0).sum()
        
        return {
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'trades': trades
        }

# 示例用法
if __name__ == "__main__":
    strategy = EnhancedDualMAStrategy()
    
    # 测试策略
    try:
        result = strategy.backtest('000001.SZ', '20240701', '20250701')
        print(f"策略回测完成")
        print(f"总收益率: {result['performance']['total_return']:.2%}")
        print(f"最大回撤: {result['performance']['max_drawdown']:.2%}")
        print(f"夏普比率: {result['performance']['sharpe_ratio']:.2f}")
        print(f"交易次数: {result['performance']['trades']}")
    except Exception as e:
        print(f"回测失败: {e}")
