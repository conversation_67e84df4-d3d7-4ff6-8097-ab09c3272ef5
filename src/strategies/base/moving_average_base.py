#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动平均策略基类
提供移动平均策略的通用功能，消除重复代码
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List
from src.strategy.strategies.base_strategy import BaseStrategy
from src.utils.technical_indicators import TechnicalIndicators, BacktestUtils
from src.utils.logging.logger_factory import get_logger

logger = get_logger(__name__)


class MovingAverageBase(BaseStrategy):
    """
    移动平均策略基类
    
    提供移动平均策略的通用功能：
    1. 移动平均线计算
    2. 基础信号生成
    3. 成交量过滤
    4. 信号确认机制
    5. 统一的回测逻辑
    """
    
    def __init__(self, 
                 short_window: int = 5,
                 long_window: int = 20,
                 signal_confirm_days: int = 1,
                 volume_threshold: float = 1.0,
                 enable_volume_filter: bool = False,
                 enable_signal_confirmation: bool = False,
                 **kwargs):
        """
        初始化移动平均策略基类
        
        参数:
            short_window: 短期移动平均窗口
            long_window: 长期移动平均窗口
            signal_confirm_days: 信号确认天数
            volume_threshold: 成交量阈值倍数
            enable_volume_filter: 是否启用成交量过滤
            enable_signal_confirmation: 是否启用信号确认
            **kwargs: 其他参数
        """
        super().__init__(**kwargs)
        
        self.short_window = short_window
        self.long_window = long_window
        self.signal_confirm_days = signal_confirm_days
        self.volume_threshold = volume_threshold
        self.enable_volume_filter = enable_volume_filter
        self.enable_signal_confirmation = enable_signal_confirmation
        
        # 验证参数
        if short_window >= long_window:
            raise ValueError("短期窗口必须小于长期窗口")
    
    def calculate_moving_averages(self, data: pd.DataFrame, price_col: str = 'close') -> pd.DataFrame:
        """
        计算移动平均线
        
        参数:
            data: 价格数据
            price_col: 价格列名
            
        返回:
            包含移动平均线的数据框
        """
        df = data.copy()
        
        # 计算短期和长期移动平均线
        df['ma_short'] = TechnicalIndicators.calculate_moving_average(
            df[price_col], self.short_window
        )
        df['ma_long'] = TechnicalIndicators.calculate_moving_average(
            df[price_col], self.long_window
        )
        
        return df
    
    def generate_basic_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成基础移动平均信号
        
        参数:
            data: 包含移动平均线的数据
            
        返回:
            包含基础信号的数据框
        """
        df = data.copy()
        
        # 基础信号：短期均线上穿长期均线为买入，下穿为卖出
        df['basic_signal'] = 0.0
        df.loc[df['ma_short'] > df['ma_long'], 'basic_signal'] = 1.0
        df.loc[df['ma_short'] < df['ma_long'], 'basic_signal'] = 0.0
        
        return df
    
    def apply_volume_filter(self, data: pd.DataFrame, volume_col: str = 'vol') -> pd.DataFrame:
        """
        应用成交量过滤
        
        参数:
            data: 包含成交量数据的数据框
            volume_col: 成交量列名
            
        返回:
            包含成交量过滤结果的数据框
        """
        df = data.copy()
        
        if self.enable_volume_filter and volume_col in df.columns:
            df['volume_filter'] = TechnicalIndicators.calculate_volume_filter(
                data=df,
                volume_col=volume_col,
                window=5,
                threshold=self.volume_threshold
            )
        else:
            df['volume_filter'] = True  # 不启用过滤时，所有信号都通过
        
        return df
    
    def apply_signal_confirmation(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        应用信号确认机制
        
        参数:
            data: 包含基础信号的数据框
            
        返回:
            包含确认信号的数据框
        """
        df = data.copy()
        
        if self.enable_signal_confirmation:
            df['signal_confirmed'] = TechnicalIndicators.apply_signal_confirmation(
                signals=df['basic_signal'],
                confirm_days=self.signal_confirm_days
            )
        else:
            df['signal_confirmed'] = df['basic_signal']  # 不启用确认时，直接使用基础信号
        
        return df
    
    def combine_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        组合所有信号条件
        
        参数:
            data: 包含所有信号组件的数据框
            
        返回:
            包含最终信号的数据框
        """
        df = data.copy()
        
        # 组合成交量过滤和信号确认
        df['signal'] = 0.0
        
        # 使用向量化操作提高性能
        condition = (df['signal_confirmed'] > 0) & df['volume_filter']
        df.loc[condition, 'signal'] = df.loc[condition, 'signal_confirmed']
        
        return df
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成完整的交易信号
        
        参数:
            data: 原始价格数据
            
        返回:
            包含所有信号的数据框
        """
        # 计算移动平均线
        df = self.calculate_moving_averages(data)
        
        # 生成基础信号
        df = self.generate_basic_signals(df)
        
        # 应用成交量过滤
        df = self.apply_volume_filter(df)
        
        # 应用信号确认
        df = self.apply_signal_confirmation(df)
        
        # 组合最终信号
        df = self.combine_signals(df)
        
        return df
    
    def execute_backtest(self, data: pd.DataFrame, initial_capital: float = 1000000,
                        enable_stop_loss: bool = False, stop_loss_pct: float = 0.05) -> pd.DataFrame:
        """
        执行回测
        
        参数:
            data: 历史数据
            initial_capital: 初始资金
            enable_stop_loss: 是否启用止损
            stop_loss_pct: 止损百分比
            
        返回:
            回测结果
        """
        # 生成信号
        signals = self.generate_signals(data)
        
        # 使用回测工具类执行回测
        positions = BacktestUtils.execute_backtest_with_signals(
            data=signals,
            signals=signals,
            initial_capital=initial_capital,
            price_col='close',
            signal_col='signal',
            enable_stop_loss=enable_stop_loss,
            stop_loss_pct=stop_loss_pct
        )
        
        return positions
    
    def get_parameters(self) -> Dict[str, Any]:
        """获取策略参数"""
        return {
            'short_window': self.short_window,
            'long_window': self.long_window,
            'signal_confirm_days': self.signal_confirm_days,
            'volume_threshold': self.volume_threshold,
            'enable_volume_filter': self.enable_volume_filter,
            'enable_signal_confirmation': self.enable_signal_confirmation,
            'strategy_type': 'moving_average_base'
        }
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': self.name,
            'type': 'Moving Average Strategy',
            'parameters': self.get_parameters(),
            'description': f"移动平均策略 ({self.short_window}/{self.long_window})"
        }

    # 实现BaseStrategy的抽象方法
    def _generate_signals_impl(self, context: Dict[str, Any], data: pd.DataFrame) -> pd.DataFrame:
        """生成信号的具体实现"""
        return self.generate_signals(data)

    def _generate_weights_impl(self, context: Dict[str, Any], signals: pd.DataFrame) -> pd.DataFrame:
        """生成权重的具体实现"""
        weights = pd.DataFrame(index=signals.index)
        weights['weight'] = signals['signal']  # 简单的全仓或空仓
        return weights

    def _generate_orders_impl(self, context: Dict[str, Any], weights: pd.DataFrame) -> List[Dict[str, Any]]:
        """生成订单的具体实现"""
        orders = []
        for symbol, weight in weights.iterrows():
            if weight['weight'] > 0:
                orders.append({
                    'symbol': symbol,
                    'side': 'buy',
                    'amount': weight['weight'],
                    'type': 'market'
                })
            elif weight['weight'] < 0:
                orders.append({
                    'symbol': symbol,
                    'side': 'sell',
                    'amount': abs(weight['weight']),
                    'type': 'market'
                })
        return orders
