#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略优化器
基于您的完美数据平台，实现策略参数优化和性能提升
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
import itertools
from concurrent.futures import ThreadPoolExecutor, as_completed
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False

# {{ AURA-X: Add - 导入统一优化器，消除重复代码. Approval: 寸止(ID:StrategyOptimizer整合). }}
from src.optimization.strategy_optimizer import UnifiedStrategyOptimizer

class StrategyOptimizer:
    """
    策略优化器（兼容性包装）
    保持向后兼容性，内部使用统一优化器
    """

    def __init__(self):
        # {{ AURA-X: Modify - 使用统一优化器，保持向后兼容性. Approval: 寸止(ID:StrategyOptimizer整合). }}
        self.db_path = 'output/data/db/sqlite/quantification.db'
        self.optimization_results = {}

        # 创建统一优化器实例
        self._unified_optimizer = UnifiedStrategyOptimizer(
            name="策略优化器",
            db_path=self.db_path
        )
    
    def get_top_stocks_by_market_cap(self, limit=50):
        """
        获取按市值排序的前N只股票
        """
        # {{ AURA-X: Modify - 委托给统一优化器，消除重复代码. Approval: 寸止(ID:StrategyOptimizer整合). }}
        return self._unified_optimizer.get_top_stocks_by_market_cap(limit)
    
    def enhanced_moving_average_strategy(self, ts_code, short_window=5, long_window=20,
                                       volume_threshold=1.5, rsi_threshold=70, days=120):
        """
        增强版移动平均策略（兼容性方法）

        参数:
            ts_code (str): 股票代码
            short_window (int): 短期均线窗口
            long_window (int): 长期均线窗口
            volume_threshold (float): 成交量阈值
            rsi_threshold (float): RSI阈值
            days (int): 回测天数

        返回:
            dict: 策略结果
        """
        # {{ AURA-X: Modify - 委托给统一优化器，消除重复的策略实现代码. Approval: 寸止(ID:StrategyOptimizer整合). }}
        params = {
            'short_window': short_window,
            'long_window': long_window,
            'volume_threshold': volume_threshold,
            'rsi_threshold': rsi_threshold
        }

        return self._unified_optimizer._evaluate_enhanced_ma_strategy(ts_code, params)
    
    def optimize_single_stock(self, ts_code, param_ranges):
        """
        优化单只股票的策略参数（兼容性方法）

        参数:
            ts_code (str): 股票代码
            param_ranges (dict): 参数范围

        返回:
            dict: 优化结果
        """
        # {{ AURA-X: Modify - 委托给统一优化器，消除重复的单股票优化逻辑. Approval: 寸止(ID:StrategyOptimizer整合). }}
        context = {
            'strategy_type': 'enhanced_moving_average',
            'ts_code': ts_code
        }

        result = self._unified_optimizer.optimize_parameters(
            param_ranges=param_ranges,
            context=context,
            target_metric='optimization_score'
        )

        # 转换为兼容格式
        best_result_data = result.all_results[0] if result.all_results else {}
        best_result = {
            'params': result.best_params,
            'score': result.best_score,
            **best_result_data
        }

        return {
            'ts_code': ts_code,
            'best_result': best_result,
            'all_results': result.all_results,
            'total_combinations': result.total_combinations,
            'valid_combinations': result.valid_combinations
        }
    
    def optimize_strategy_parameters(self, stock_limit=10, max_workers=4):
        """
        优化策略参数

        参数:
            stock_limit (int): 测试股票数量
            max_workers (int): 并行工作线程数

        返回:
            dict: 优化结果
        """
        # {{ AURA-X: Modify - 使用统一优化器，消除重复的优化逻辑. Approval: 寸止(ID:StrategyOptimizer整合). }}
        print("🔧 开始策略参数优化...")
        print("=" * 50)

        # 使用统一优化器执行优化
        result = self._unified_optimizer.run_comprehensive_optimization(
            stock_limit=stock_limit,
            strategy_type='enhanced_moving_average'
        )

        # 转换为兼容格式
        if result['analysis']['status'] == 'success':
            optimization_results = []
            for ts_code, opt_result in result['optimization_results'].items():
                if opt_result.best_score > float('-inf'):
                    best_result_data = opt_result.all_results[0] if opt_result.all_results else {}
                    optimization_results.append({
                        'ts_code': ts_code,
                        'best_result': {
                            'params': opt_result.best_params,
                            'score': opt_result.best_score,
                            **best_result_data
                        },
                        'all_results': opt_result.all_results,
                        'total_combinations': opt_result.total_combinations,
                        'valid_combinations': opt_result.valid_combinations
                    })

            # 保存结果到实例变量
            self.optimization_results = result

            return optimization_results
        else:
            print("优化失败")
            return []
    
    def analyze_optimization_results(self, results, param_ranges):
        """
        分析优化结果
        """
        print("📊 优化结果分析")
        print("=" * 50)
        
        # 收集所有最佳结果
        best_results = [r['best_result'] for r in results if r['best_result'] is not None]
        
        if not best_results:
            print("❌ 没有有效的优化结果")
            return
        
        # 转换为DataFrame
        df = pd.DataFrame(best_results)
        
        # 整体统计
        print(f"成功优化股票数: {len(best_results)}")
        print(f"平均超额收益: {df['excess_return'].mean():.2%}")
        print(f"平均夏普比率: {df['sharpe_ratio'].mean():.2f}")
        print(f"平均最大回撤: {df['max_drawdown'].mean():.2%}")
        print(f"平均胜率: {df['win_rate'].mean():.2%}")
        print()
        
        # 最佳参数分析
        print("🏆 最佳参数组合分析:")
        
        # 统计最优参数出现频率
        param_stats = {}
        for param in ['short_window', 'long_window', 'volume_threshold', 'rsi_threshold']:
            param_values = [r['params'][param] for r in best_results]
            param_counts = pd.Series(param_values).value_counts()
            param_stats[param] = param_counts
            
            print(f"{param}:")
            for value, count in param_counts.head(3).items():
                print(f"  {value}: {count}次 ({count/len(best_results):.1%})")
            print()
        
        # 推荐参数组合
        recommended_params = {}
        for param in ['short_window', 'long_window', 'volume_threshold', 'rsi_threshold']:
            recommended_params[param] = param_stats[param].index[0]
        
        print("💡 推荐参数组合:")
        for param, value in recommended_params.items():
            print(f"  {param}: {value}")
        
        # 测试推荐参数
        print()
        print("🧪 推荐参数组合测试:")
        self.test_recommended_parameters(recommended_params)
        
        self.optimization_results = {
            'results': results,
            'recommended_params': recommended_params,
            'statistics': {
                'avg_excess_return': df['excess_return'].mean(),
                'avg_sharpe_ratio': df['sharpe_ratio'].mean(),
                'avg_max_drawdown': df['max_drawdown'].mean(),
                'avg_win_rate': df['win_rate'].mean()
            }
        }
    
    def test_recommended_parameters(self, params):
        """
        测试推荐参数
        """
        test_stocks = self.get_top_stocks_by_market_cap(5)
        results = []
        
        for stock in test_stocks:
            try:
                result = self.enhanced_moving_average_strategy(
                    stock, 
                    params['short_window'],
                    params['long_window'],
                    params['volume_threshold'],
                    params['rsi_threshold']
                )
                
                if result:
                    results.append(result)
                    print(f"  {stock}: 超额收益 {result['excess_return']:.2%}, "
                          f"夏普比率 {result['sharpe_ratio']:.2f}")
                    
            except Exception as e:
                continue
        
        if results:
            avg_excess = np.mean([r['excess_return'] for r in results])
            avg_sharpe = np.mean([r['sharpe_ratio'] for r in results])
            print(f"推荐参数平均表现: 超额收益 {avg_excess:.2%}, 夏普比率 {avg_sharpe:.2f}")
    
    def run_optimization(self):
        """
        运行完整的策略优化流程
        """
        # {{ AURA-X: Modify - 使用统一优化器，保持输出格式兼容性. Approval: 寸止(ID:StrategyOptimizer整合). }}
        print("🚀 策略优化系统")
        print("基于您的完美数据平台进行参数调优")
        print("=" * 60)

        # 执行参数优化
        results = self.optimize_strategy_parameters(stock_limit=15, max_workers=3)

        print()
        print("🎉 策略优化完成！")

        if self.optimization_results:
            print("💡 优化建议:")
            print("1. 使用推荐的参数组合可以获得更好的风险调整收益")
            print("2. 可以根据不同市场环境动态调整参数")
            print("3. 建议定期重新优化参数以适应市场变化")

        return self.optimization_results

def main():
    """
    主函数
    """
    optimizer = StrategyOptimizer()
    results = optimizer.run_optimization()

if __name__ == "__main__":
    main()
