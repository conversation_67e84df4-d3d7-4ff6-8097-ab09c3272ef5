#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动平均线策略
这是一个简单的双均线交叉策略，可用于演示和教学目的。

{{ AURA-X: Modify - 重构为继承BaseStrategy，集成DataFetcherManager. Approval: 寸止(ID:阶段2.1). }}
"""

import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib
from typing import Dict, Any, List
from src.strategies.base.moving_average_base import MovingAverageBase
from src.data.fetcher.data_fetcher_manager import DataFetcherManager
# {{ AURA-X: Add - 导入技术指标工具类，消除重复代码. Approval: 寸止(ID:重构阶段3). }}
from src.utils.technical_indicators import TechnicalIndicators, BacktestUtils
from src.utils.logging.logger_factory import get_logger

logger = get_logger(__name__)

matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False

class MovingAverageStrategy(MovingAverageBase):
    """
    移动平均线策略类
    使用短期均线和长期均线的交叉信号进行交易决策
    """
    def __init__(self, short_window=5, long_window=20, data_manager=None, **kwargs):
        """
        初始化策略参数

        参数:
            short_window (int): 短期均线窗口长度
            long_window (int): 长期均线窗口长度
            data_manager (DataFetcherManager): 数据获取管理器
            **kwargs: 其他参数
        """
        # {{ AURA-X: Modify - 继承MovingAverageBase，简化初始化逻辑. Approval: 寸止(ID:策略重复修复). }}
        # 先设置属性，避免在父类初始化时访问不存在的属性
        self.short_window = short_window
        self.long_window = long_window

        # 调用父类初始化
        super().__init__(
            short_window=short_window,
            long_window=long_window,
            name=f"移动平均策略_{short_window}_{long_window}",
            **kwargs
        )

        # 集成数据管理器
        self.data_manager = data_manager or DataFetcherManager.get_instance()

        # 设置策略配置
        self.strategy_type = 'trend_following'
        self.data_frequency = 'daily'

    def initialize(self, **kwargs):
        """
        策略初始化

        参数:
            **kwargs: 初始化参数
        """
        self.logger.info(f"初始化移动平均线策略: 短期={self.short_window}, 长期={self.long_window}")

        # 更新参数
        if 'short_window' in kwargs:
            self.short_window = kwargs['short_window']
        if 'long_window' in kwargs:
            self.long_window = kwargs['long_window']

    def request_data(self, symbols, start_date, end_date, data_type='daily'):
        """
        请求数据的统一接口

        参数:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            data_type: 数据类型

        返回:
            DataFrame: 市场数据
        """
        try:
            # {{ AURA-X: Modify - 临时直接使用数据源获取DataFrame，避免字符串转换问题. Approval: 寸止(ID:阶段2.1). }}
            # 直接使用数据源获取DataFrame，绕过字符串转换问题
            fetcher = self.data_manager.get_fetcher()
            if hasattr(fetcher, 'sync_fetcher') and fetcher.sync_fetcher and hasattr(fetcher.sync_fetcher, 'data_source'):
                data_source = fetcher.sync_fetcher.data_source
                if hasattr(data_source, 'get_daily_data'):
                    # 直接从数据源获取DataFrame
                    result = data_source.get_daily_data(
                        symbols=symbols,
                        start_date=start_date,
                        end_date=end_date
                    )
                    self.logger.info(f"直接从数据源获取数据: {len(result)}条记录")
                    return result

            # 回退到原有方法
            result = fetcher.fetch_market_data(
                symbols=symbols,
                data_type=data_type,
                start_date=start_date,
                end_date=end_date,
                save=False,
                use_cache=True
            )

            # 如果返回的是字符串，尝试转换为DataFrame
            if isinstance(result, str):
                import io
                try:
                    # 尝试从字符串读取DataFrame，使用空白符分隔
                    result = pd.read_csv(io.StringIO(result), sep=r'\s+', engine='python')
                    self.logger.warning("数据获取返回字符串，已转换为DataFrame")
                except Exception as convert_error:
                    try:
                        # 尝试使用制表符分隔
                        result = pd.read_csv(io.StringIO(result), sep='\t')
                        self.logger.warning("数据获取返回字符串，使用制表符分隔转换为DataFrame")
                    except Exception as convert_error2:
                        self.logger.error(f"字符串转DataFrame失败: {convert_error}, {convert_error2}")
                        raise ValueError(f"数据获取返回了无效的字符串格式: {str(result)[:100]}...")

            return result
        except Exception as e:
            self.logger.error(f"数据获取失败: {e}")
            raise

    def generate_signals(self, data):
        """
        生成交易信号

        参数:
            data (DataFrame): 包含日期和收盘价的数据框

        返回:
            DataFrame: 添加了信号列的数据框
        """
        # {{ AURA-X: Modify - 使用基类的统一信号生成逻辑. Approval: 寸止(ID:策略重复修复). }}
        # 使用基类的信号生成方法，不启用额外过滤
        return super().generate_signals(data)
    
    def backtest(self, data, start_date=None, end_date=None, initial_capital=100000.0, benchmark=None):
        """
        回测策略
        
        参数:
            data (DataFrame): 价格数据
            start_date (str): 回测开始日期
            end_date (str): 回测结束日期
            initial_capital (float): 初始资金
            benchmark (str): 基准指数代码
            
        返回:
            dict: 包含回测结果和性能指标的字典
        """
        # 如果提供了开始和结束日期，则过滤数据
        if start_date and 'trade_date' in data.columns:
            data = data[data['trade_date'] >= start_date]
        if end_date and 'trade_date' in data.columns:
            data = data[data['trade_date'] <= end_date]
            
        # 生成交易信号
        signals = self.generate_signals(data)

        # {{ AURA-X: Modify - 使用BacktestUtils工具类，消除重复的回测代码. Approval: 寸止(ID:重构阶段3). }}
        # 使用回测工具类执行回测
        positions = BacktestUtils.execute_backtest_with_signals(
            data=data,
            signals=signals,
            initial_capital=initial_capital,
            price_col='close',
            signal_col='signal',
            enable_stop_loss=False,
            stop_loss_pct=0.05
        )

        # 兼容性处理：重命名列以保持向后兼容
        if 'position_value' in positions.columns:
            positions['position'] = positions['position_value']
        
        # 计算性能指标
        performance = self.show_performance(positions)
        
        # 整理回测结果
        result = {
            'signals': signals,
            'positions': positions,
            'performance': performance,
            'params': {
                'short_window': self.short_window,
                'long_window': self.long_window
            },
            'portfolio': {
                'initial_capital': initial_capital,
                'final_capital': positions['total'].iloc[-1] if not positions.empty and 'total' in positions.columns and len(positions) > 0 else initial_capital,
                'equity_curve': positions['total'].tolist() if not positions.empty and 'total' in positions.columns else [initial_capital]
            }
        }
        
        # 如果提供了基准指数，可以在这里添加基准对比的代码
        if benchmark:
            result['benchmark'] = benchmark
            
        return result
    
    def show_performance(self, positions):
        """
        显示策略性能指标

        参数:
            positions (DataFrame): 回测结果数据框
        """
        # 检查数据是否为空
        if positions.empty or 'total' not in positions.columns or len(positions) == 0:
            print("警告: 没有足够的数据来计算性能指标")
            return {
                'total_return': 0.0,
                'annual_return': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0,
                'trades': 0
            }

        # 计算总收益率
        initial_value = positions['total'].iloc[0]
        final_value = positions['total'].iloc[-1]
        total_return = (final_value / initial_value) - 1

        # 计算年化收益率
        total_days = len(positions)
        annual_return = total_return * (252 / total_days)

        # 计算最大回撤
        positions['cummax'] = positions['total'].cummax()
        positions['drawdown'] = (positions['cummax'] - positions['total']) / positions['cummax']
        max_drawdown = positions['drawdown'].max()

        # 计算夏普比率（处理除零情况）
        returns_std = positions['returns'].std()
        if returns_std > 0:
            sharpe_ratio = positions['returns'].mean() / returns_std * np.sqrt(252)
        else:
            sharpe_ratio = 0.0

        # 计算交易次数（信号变化次数）
        signal_changes = (positions['signal'].diff() != 0).sum()

        # {{ AURA-X: Modify - 移除print语句，避免干扰返回值. Approval: 寸止(ID:阶段2.1). }}
        # 记录结果到日志而不是打印
        self.logger.info(f"策略性能指标:")
        self.logger.info(f"总收益率: {total_return:.2%}")
        self.logger.info(f"年化收益率: {annual_return:.2%}")
        self.logger.info(f"最大回撤: {max_drawdown:.2%}")
        self.logger.info(f"夏普比率: {sharpe_ratio:.2f}")
        self.logger.info(f"交易次数: {signal_changes}")

        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'trades': signal_changes
        }
    
    def run(self, symbols=None, data=None, params=None, initial_capital=100000.0, start_date=None, end_date=None, benchmark=None):
        """
        运行策略（重构版本，支持统一数据获取）

        参数:
            symbols: 股票代码列表（优先使用）
            data (DataFrame): 价格数据（向后兼容）
            params (dict): 策略参数字典
            initial_capital (float): 初始资金
            start_date (str): 回测开始日期
            end_date (str): 回测结束日期
            benchmark (str): 基准指数代码

        返回:
            dict: 回测结果和性能指标
        """
        # {{ AURA-X: Modify - 重构run方法，支持统一数据获取接口. Approval: 寸止(ID:阶段2.1). }}
        # 更新策略参数
        if params:
            self.set_parameters(params)

        # 获取数据
        if symbols and not data:
            # {{ AURA-X: Modify - 修复symbols参数处理，确保为列表格式. Approval: 寸止(ID:阶段2.1). }}
            # 确保symbols是列表格式
            if isinstance(symbols, str):
                symbols = [symbols]

            # 使用统一数据获取接口
            try:
                data = self.request_data(
                    symbols=symbols,
                    start_date=start_date or '20240101',
                    end_date=end_date or '20241231'
                )
                self.logger.info(f"通过DataFetcherManager获取数据: {len(data)}条记录")
            except Exception as e:
                self.logger.error(f"数据获取失败: {e}")
                raise
        elif data is None or (hasattr(data, 'empty') and data.empty):
            raise ValueError("必须提供symbols或data参数")

        # 运行回测
        backtest_results = self.backtest(data, start_date, end_date, initial_capital, benchmark)

        # {{ AURA-X: Modify - 返回标准化的结果格式，兼容原有接口. Approval: 寸止(ID:阶段2.1). }}
        # 提取性能指标，保持向后兼容
        if isinstance(backtest_results, dict) and 'performance' in backtest_results:
            performance = backtest_results['performance']
            # 返回扁平化的结果，保持向后兼容
            return {
                'total_return': performance.get('total_return', 0),
                'annual_return': performance.get('annual_return', 0),
                'max_drawdown': performance.get('max_drawdown', 0),
                'sharpe_ratio': performance.get('sharpe_ratio', 0),
                'trades': performance.get('trades', 0),
                'detailed_results': backtest_results  # 保留完整结果
            }
        else:
            # 如果格式不符合预期，直接返回
            return backtest_results

    # {{ AURA-X: Add - 实现BaseStrategy的抽象方法. Approval: 寸止(ID:阶段2.1). }}
    def _generate_signals_impl(self, context: Dict[str, Any], data: pd.DataFrame) -> pd.DataFrame:
        """生成信号的具体实现"""
        return self.generate_signals(data)

    def _generate_weights_impl(self, context: Dict[str, Any], signals: pd.DataFrame) -> pd.DataFrame:
        """生成权重的具体实现"""
        weights = pd.DataFrame(index=signals.index)
        weights['weight'] = signals['signal']  # 简单的全仓或空仓
        return weights

    def _generate_orders_impl(self, context: Dict[str, Any], weights: pd.DataFrame) -> List[Dict[str, Any]]:
        """生成订单的具体实现"""
        orders = []
        for symbol, weight in weights.iterrows():
            if weight['weight'] > 0:
                orders.append({
                    'symbol': symbol,
                    'side': 'buy',
                    'amount': weight['weight'],
                    'type': 'market'
                })
            elif weight['weight'] < 0:
                orders.append({
                    'symbol': symbol,
                    'side': 'sell',
                    'amount': abs(weight['weight']),
                    'type': 'market'
                })
        return orders

    def before_trading_start(self, context: Dict[str, Any]) -> None:
        """在每个交易日开始前调用"""
        self.logger.debug(f"交易日开始: {context.get('current_date', 'Unknown')}")

    def handle_data(self, context: Dict[str, Any], data: pd.DataFrame) -> Dict[str, Any]:
        """处理每个bar的数据，生成交易信号"""
        signals = self.generate_signals(data)

        # 获取最新信号
        if not signals.empty:
            latest_signal = signals.iloc[-1]['signal']
            return {
                'signal': latest_signal,
                'position_change': signals.iloc[-1].get('position_change', 0),
                'short_mavg': signals.iloc[-1].get('short_mavg', 0),
                'long_mavg': signals.iloc[-1].get('long_mavg', 0)
            }
        return {}

    def after_trading_end(self, context: Dict[str, Any]) -> None:
        """在每个交易日结束后调用"""
        self.logger.debug(f"交易日结束: {context.get('current_date', 'Unknown')}")

    def finalize(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """策略结束时调用"""
        return {
            'strategy_name': self.name,
            'parameters': self.get_parameters(),
            'total_trades': len(self.trades)
        }

    def generate_weights(self, context: Dict[str, Any], signals: pd.DataFrame) -> pd.DataFrame:
        """根据信号生成目标持仓权重"""
        weights = pd.DataFrame(index=signals.index)
        weights['weight'] = signals['signal']  # 简单的全仓或空仓
        return weights

    def generate_orders(self, context: Dict[str, Any], weights: pd.DataFrame) -> List[Dict[str, Any]]:
        """根据目标权重生成订单"""
        orders = []
        for symbol, weight in weights.iterrows():
            if weight['weight'] > 0:
                orders.append({
                    'symbol': symbol,
                    'side': 'buy',
                    'amount': weight['weight'],
                    'type': 'market'
                })
            elif weight['weight'] < 0:
                orders.append({
                    'symbol': symbol,
                    'side': 'sell',
                    'amount': abs(weight['weight']),
                    'type': 'market'
                })
        return orders

    def get_parameters(self) -> Dict[str, Any]:
        """获取策略参数"""
        return {
            'short_window': self.short_window,
            'long_window': self.long_window,
            'strategy_type': 'trend_following'
        }

    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """设置策略参数"""
        if 'short_window' in parameters:
            self.short_window = parameters['short_window']
        if 'long_window' in parameters:
            self.long_window = parameters['long_window']

        # 更新参数字典（使用BaseStrategy的属性）
        self.parameters.update({
            'short_window': self.short_window,
            'long_window': self.long_window
        })

    def get_required_data(self) -> Dict[str, Any]:
        """获取策略所需的数据描述"""
        return {
            'market_data': {
                'frequency': 'daily',
                'fields': ['open', 'high', 'low', 'close', 'volume'],
                'lookback_periods': max(self.short_window, self.long_window) + 10
            }
        }

    def save_result(self, result, file_path):
        """
        保存回测结果到文件
        
        参数:
            result (dict): 回测结果
            file_path (str): 文件保存路径
        """
        import json
        
        # 将 DataFrame 和 numpy 类型转换为 JSON 可序列化类型
        serializable_result = {}
        
        for key, value in result.items():
            if isinstance(value, pd.DataFrame):
                # 只保存最关键的信息
                if key == 'positions':
                    serializable_result[key] = {
                        'trade_date': value['trade_date'].tolist() if 'trade_date' in value.columns else [],
                        'close': value['close'].tolist() if 'close' in value.columns else [],
                        'signal': value['signal'].tolist() if 'signal' in value.columns else [],
                        'position': value['position'].tolist() if 'position' in value.columns else [],
                        'total': value['total'].tolist() if 'total' in value.columns else [],
                        'returns': value['returns'].tolist() if 'returns' in value.columns else []
                    }
                else:
                    # 简化其他DataFrame
                    serializable_result[key] = {col: value[col].tolist() for col in value.columns if col in ['trade_date', 'signal']}
            elif isinstance(value, dict):
                # 递归处理字典
                serializable_result[key] = {}
                for sub_key, sub_value in value.items():
                    if isinstance(sub_value, (np.integer, np.floating)):
                        serializable_result[key][sub_key] = float(sub_value)
                    else:
                        serializable_result[key][sub_key] = sub_value
            else:
                # 其他类型直接保存
                serializable_result[key] = value
        
        # 保存到文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(serializable_result, f, indent=2, ensure_ascii=False)
        
    def plot_result(self, result, file_path=None):
        """
        绘制回测结果图表
        
        参数:
            result (dict): 回测结果
            file_path (str): 图表保存路径，为None时显示而不保存
        """
        # 从结果中获取数据
        positions = result.get('positions', pd.DataFrame())
        if isinstance(positions, dict):
            # 如果是序列化后的字典，转换回DataFrame
            positions = pd.DataFrame(positions)
        
        # 检查必要的列是否存在
        if positions.empty or 'total' not in positions.columns:
            print("错误: 没有足够的数据来绘制图表")
            return
        
        # 创建图表
        fig, axes = plt.subplots(2, 1, figsize=(12, 10), gridspec_kw={'height_ratios': [3, 1]})
        
        # 绘制资产价值曲线
        positions['total'].plot(ax=axes[0], title='策略资产价值', color='blue')
        
        # 如果有信号数据，绘制买入和卖出信号
        if 'signal' in positions.columns and 'close' in positions.columns and 'position_change' in positions.columns:
            # 买入信号
            buy_signals = positions[positions['position_change'] > 0]
            axes[0].scatter(
                buy_signals.index, 
                buy_signals['close'], 
                marker='^', 
                color='green', 
                s=100, 
                label='买入信号'
            )
            
            # 卖出信号
            sell_signals = positions[positions['position_change'] < 0]
            axes[0].scatter(
                sell_signals.index, 
                sell_signals['close'], 
                marker='v', 
                color='red', 
                s=100, 
                label='卖出信号'
            )
        
        axes[0].legend()
        axes[0].grid(True)
        
        # 绘制收益率曲线
        if 'returns' in positions.columns:
            positions['returns'].plot(ax=axes[1], title='日收益率', color='green')
            axes[1].grid(True)
        
        plt.tight_layout()
        
        # 保存或显示图表
        if file_path:
            plt.savefig(file_path)
            plt.close()
            print(f"图表已保存到: {file_path}")
        else:
            plt.show()

# 示例用法
if __name__ == "__main__":
    # 加载示例数据
    try:
        # 尝试从本地文件加载
        data = pd.read_csv('data_samples/sh_index.csv')
    except FileNotFoundError:
        # 如果没有本地文件，创建一些模拟数据
        print("未找到数据文件，使用模拟数据...")
        dates = pd.date_range('2020-01-01', periods=100, freq='D')
        data = pd.DataFrame({
            'trade_date': dates.strftime('%Y%m%d'),
            'open': np.random.normal(100, 5, 100),
            'high': np.random.normal(105, 5, 100),
            'low': np.random.normal(95, 5, 100),
            'close': np.random.normal(100, 5, 100),
            'vol': np.random.normal(1000000, 200000, 100)
        })
    
    # 创建策略实例
    strategy = MovingAverageStrategy(short_window=5, long_window=20)
    
    # 运行策略
    results = strategy.run(
        data=data,
        start_date='20200101',
        end_date='20201231',
        initial_capital=100000.0,
        benchmark='000001.SH'
    )
    
    # 打印结果
    print("\n策略回测完成，最终资产价值:", results['portfolio']['final_capital'])
    
    # 显示性能指标
    print("\n性能指标:")
    for key, value in results['performance'].items():
        print(f"{key}: {value}")

# {{ AURA-X: Remove - 移除重复的方法定义，已在类中直接实现. Approval: 寸止(ID:阶段2.1). }}

# 注册策略到工厂
from src.strategy.strategies.base_strategy import StrategyFactory
try:
    StrategyFactory.register('moving_average', MovingAverageStrategy)
except ValueError:
    # 策略已注册，忽略错误
    pass
