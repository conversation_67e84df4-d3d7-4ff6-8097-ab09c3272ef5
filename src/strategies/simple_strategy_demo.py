#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版策略演示
基于您的完美数据平台，展示实际可用的策略功能
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False

class SimpleStrategyDemo:
    """
    简化版策略演示
    基于实际数据结构的策略实现
    """
    
    def __init__(self):
        self.db_path = 'output/data/db/sqlite/quantification.db'
    
    def get_stock_list(self, limit=20):
        """
        获取股票列表
        """
        conn = sqlite3.connect(self.db_path)
        
        sql = """
        SELECT DISTINCT s.ts_code, s.name, m.total_mv
        FROM stock_list s
        LEFT JOIN market_cap m ON s.ts_code = m.ts_code
        WHERE m.total_mv IS NOT NULL
        ORDER BY m.total_mv DESC
        LIMIT ?
        """
        
        stocks = pd.read_sql(sql, conn, params=[limit])
        conn.close()
        
        return stocks
    
    def simple_moving_average_strategy(self, ts_code, days=100):
        """
        简单移动平均策略演示
        
        参数:
            ts_code (str): 股票代码
            days (int): 分析天数
            
        返回:
            dict: 策略结果
        """
        conn = sqlite3.connect(self.db_path)
        
        # 获取最近的日线数据
        sql = """
        SELECT trade_date, ts_code, open, high, low, close, vol, amount
        FROM daily 
        WHERE ts_code = ?
        ORDER BY trade_date DESC
        LIMIT ?
        """
        
        data = pd.read_sql(sql, conn, params=[ts_code, days])
        conn.close()
        
        if data.empty:
            return {'error': f'未找到股票 {ts_code} 的数据'}
        
        # 按日期排序
        data = data.sort_values('trade_date').reset_index(drop=True)
        
        # 计算移动平均线
        data['ma5'] = data['close'].rolling(window=5, min_periods=1).mean()
        data['ma20'] = data['close'].rolling(window=20, min_periods=1).mean()
        
        # 生成交易信号
        data['signal'] = 0
        data['signal'] = np.where(data['ma5'] > data['ma20'], 1, 0)  # 1: 买入, 0: 卖出
        
        # 计算收益率
        data['returns'] = data['close'].pct_change()
        data['strategy_returns'] = data['returns'] * data['signal'].shift(1)
        
        # 计算累计收益
        data['cumulative_returns'] = (1 + data['returns']).cumprod()
        data['strategy_cumulative'] = (1 + data['strategy_returns'].fillna(0)).cumprod()
        
        # 计算性能指标
        total_return = data['strategy_cumulative'].iloc[-1] - 1
        benchmark_return = data['cumulative_returns'].iloc[-1] - 1
        
        # 计算夏普比率
        strategy_std = data['strategy_returns'].std()
        sharpe_ratio = data['strategy_returns'].mean() / strategy_std * np.sqrt(252) if strategy_std > 0 else 0
        
        # 计算最大回撤
        data['strategy_peak'] = data['strategy_cumulative'].cummax()
        data['strategy_drawdown'] = (data['strategy_peak'] - data['strategy_cumulative']) / data['strategy_peak']
        max_drawdown = data['strategy_drawdown'].max()
        
        return {
            'stock_code': ts_code,
            'data': data,
            'performance': {
                'strategy_return': total_return,
                'benchmark_return': benchmark_return,
                'excess_return': total_return - benchmark_return,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': (data['strategy_returns'] > 0).sum() / len(data['strategy_returns'].dropna())
            }
        }
    
    def fundamental_screening_strategy(self):
        """
        基本面筛选策略演示
        
        返回:
            dict: 筛选结果
        """
        conn = sqlite3.connect(self.db_path)
        
        # 获取最新的财务数据
        sql = """
        WITH latest_financial AS (
            SELECT 
                i.ts_code,
                i.end_date,
                i.revenue,
                i.n_income,
                i.total_revenue,
                b.total_assets,
                b.total_liab,
                b.total_hldr_eqy_exc_min_int as equity,
                c.n_cashflow_act,
                m.total_mv,
                s.name,
                ROW_NUMBER() OVER (PARTITION BY i.ts_code ORDER BY i.end_date DESC) as rn
            FROM income i
            LEFT JOIN balance b ON i.ts_code = b.ts_code AND i.end_date = b.end_date
            LEFT JOIN cash_flow c ON i.ts_code = c.ts_code AND i.end_date = c.end_date
            LEFT JOIN (
                SELECT ts_code, total_mv,
                ROW_NUMBER() OVER (PARTITION BY ts_code ORDER BY trade_date DESC) as mv_rn
                FROM market_cap WHERE total_mv IS NOT NULL
            ) m ON i.ts_code = m.ts_code AND m.mv_rn = 1
            LEFT JOIN stock_list s ON i.ts_code = s.ts_code
            WHERE i.revenue IS NOT NULL AND i.n_income IS NOT NULL
        )
        SELECT 
            ts_code, name, end_date, revenue, n_income, total_assets, 
            total_liab, equity, n_cashflow_act, total_mv
        FROM latest_financial 
        WHERE rn = 1 AND total_mv IS NOT NULL
        ORDER BY total_mv DESC
        """
        
        data = pd.read_sql(sql, conn)
        conn.close()
        
        if data.empty:
            return {'error': '未找到财务数据'}
        
        # 转换数据类型
        numeric_cols = ['revenue', 'n_income', 'total_assets', 'total_liab', 
                       'equity', 'n_cashflow_act', 'total_mv']
        for col in numeric_cols:
            data[col] = pd.to_numeric(data[col], errors='coerce')
        
        # 计算财务指标
        data['roe'] = data['n_income'] / data['equity']  # ROE
        data['roa'] = data['n_income'] / data['total_assets']  # ROA
        data['debt_ratio'] = data['total_liab'] / data['total_assets']  # 资产负债率
        data['profit_margin'] = data['n_income'] / data['revenue']  # 净利润率
        data['market_cap_billion'] = data['total_mv'] / 10000  # 市值(亿元)
        
        # 筛选条件
        conditions = (
            (data['roe'] > 0.1) &  # ROE > 10%
            (data['roa'] > 0.05) &  # ROA > 5%
            (data['debt_ratio'] < 0.6) &  # 资产负债率 < 60%
            (data['profit_margin'] > 0.05) &  # 净利润率 > 5%
            (data['market_cap_billion'] > 50) &  # 市值 > 50亿
            (data['n_income'] > 0) &  # 盈利
            (data['revenue'] > 0)  # 有营收
        )
        
        filtered_data = data[conditions].copy()
        
        # 按ROE排序
        filtered_data = filtered_data.sort_values('roe', ascending=False)
        
        return {
            'total_stocks': len(data),
            'filtered_stocks': len(filtered_data),
            'top_stocks': filtered_data.head(20),
            'summary': {
                'avg_roe': filtered_data['roe'].mean() if not filtered_data.empty else 0,
                'avg_market_cap': filtered_data['market_cap_billion'].mean() if not filtered_data.empty else 0,
                'avg_debt_ratio': filtered_data['debt_ratio'].mean() if not filtered_data.empty else 0
            }
        }
    
    def market_overview(self):
        """
        市场概览
        
        返回:
            dict: 市场统计
        """
        conn = sqlite3.connect(self.db_path)
        
        # 获取市场统计
        sql = """
        SELECT 
            COUNT(DISTINCT ts_code) as total_stocks,
            SUM(total_mv) / 100000000 as total_market_cap_trillion,
            AVG(total_mv) / 10000 as avg_market_cap_billion,
            MAX(total_mv) / 10000 as max_market_cap_billion,
            MIN(total_mv) / 10000 as min_market_cap_billion
        FROM (
            SELECT ts_code, total_mv,
            ROW_NUMBER() OVER (PARTITION BY ts_code ORDER BY trade_date DESC) as rn
            FROM market_cap WHERE total_mv IS NOT NULL
        ) WHERE rn = 1
        """
        
        market_stats = pd.read_sql(sql, conn)
        
        # 获取行业分布（简化）
        industry_sql = """
        SELECT 
            SUBSTR(ts_code, -2) as market,
            COUNT(*) as count,
            SUM(total_mv) / 10000 as total_mv_billion
        FROM (
            SELECT ts_code, total_mv,
            ROW_NUMBER() OVER (PARTITION BY ts_code ORDER BY trade_date DESC) as rn
            FROM market_cap WHERE total_mv IS NOT NULL
        ) WHERE rn = 1
        GROUP BY SUBSTR(ts_code, -2)
        ORDER BY total_mv_billion DESC
        """
        
        industry_stats = pd.read_sql(industry_sql, conn)
        conn.close()
        
        return {
            'market_overview': market_stats.iloc[0].to_dict(),
            'market_distribution': industry_stats.to_dict('records')
        }
    
    def run_demo(self):
        """
        运行策略演示
        """
        print("🎯 量化策略演示平台")
        print("基于您的完美数据平台 - 280万条数据记录")
        print("=" * 60)
        
        # 1. 市场概览
        print("📊 市场概览")
        market_info = self.market_overview()
        overview = market_info['market_overview']
        
        print(f"总股票数: {overview['total_stocks']:,}")
        print(f"总市值: {overview['total_market_cap_trillion']:.1f}万亿元")
        print(f"平均市值: {overview['avg_market_cap_billion']:.1f}亿元")
        print(f"最大市值: {overview['max_market_cap_billion']:.1f}亿元")
        print()
        
        # 2. 基本面筛选策略
        print("💎 基本面筛选策略")
        fundamental_result = self.fundamental_screening_strategy()
        
        if 'error' not in fundamental_result:
            print(f"筛选前股票数: {fundamental_result['total_stocks']:,}")
            print(f"筛选后股票数: {fundamental_result['filtered_stocks']:,}")
            
            if fundamental_result['filtered_stocks'] > 0:
                summary = fundamental_result['summary']
                print(f"平均ROE: {summary['avg_roe']:.2%}")
                print(f"平均市值: {summary['avg_market_cap']:.1f}亿元")
                print(f"平均资产负债率: {summary['avg_debt_ratio']:.2%}")
                
                print("\n前10只优质股票:")
                top_stocks = fundamental_result['top_stocks'].head(10)
                for _, stock in top_stocks.iterrows():
                    print(f"  {stock['ts_code']} {stock['name']}: "
                          f"ROE {stock['roe']:.2%}, 市值 {stock['market_cap_billion']:.1f}亿")
        else:
            print(f"筛选失败: {fundamental_result['error']}")
        
        print()
        
        # 3. 技术分析策略演示
        print("📈 技术分析策略演示")
        
        # 获取一些大盘股进行演示
        stocks = self.get_stock_list(5)
        
        if not stocks.empty:
            results = []
            
            for _, stock in stocks.iterrows():
                try:
                    result = self.simple_moving_average_strategy(stock['ts_code'], 60)
                    
                    if 'error' not in result:
                        perf = result['performance']
                        results.append({
                            'stock_code': stock['ts_code'],
                            'name': stock['name'],
                            'strategy_return': perf['strategy_return'],
                            'benchmark_return': perf['benchmark_return'],
                            'excess_return': perf['excess_return'],
                            'sharpe_ratio': perf['sharpe_ratio'],
                            'max_drawdown': perf['max_drawdown']
                        })
                        
                        print(f"  {stock['ts_code']} {stock['name']}:")
                        print(f"    策略收益: {perf['strategy_return']:.2%}")
                        print(f"    基准收益: {perf['benchmark_return']:.2%}")
                        print(f"    超额收益: {perf['excess_return']:.2%}")
                        print(f"    夏普比率: {perf['sharpe_ratio']:.2f}")
                        print(f"    最大回撤: {perf['max_drawdown']:.2%}")
                        print()
                    
                except Exception as e:
                    print(f"  {stock['ts_code']} 分析失败: {e}")
            
            # 策略总结
            if results:
                df_results = pd.DataFrame(results)
                print("📋 策略总结:")
                print(f"平均策略收益: {df_results['strategy_return'].mean():.2%}")
                print(f"平均超额收益: {df_results['excess_return'].mean():.2%}")
                print(f"平均夏普比率: {df_results['sharpe_ratio'].mean():.2f}")
                print(f"胜率: {(df_results['excess_return'] > 0).mean():.2%}")
        
        print()
        print("🎉 策略演示完成！")
        print("💡 这只是基于您数据平台的基础演示，可以开发更复杂的策略")

def main():
    """
    主函数
    """
    demo = SimpleStrategyDemo()
    demo.run_demo()

if __name__ == "__main__":
    main()
