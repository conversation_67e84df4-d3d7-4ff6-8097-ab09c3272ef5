#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版多因子选股策略
基于您的完美数据平台，结合技术面、基本面、市场面的综合多因子策略
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False
from src.strategy.strategies.base_strategy import BaseStrategy

class EnhancedMultiFactorStrategy(BaseStrategy):
    """
    增强版多因子选股策略
    综合技术面、基本面、市场面多个维度的因子
    """
    
    def __init__(self,
                 # 基本面因子权重
                 value_weight=0.3,      # 价值因子
                 quality_weight=0.25,   # 质量因子
                 growth_weight=0.2,     # 成长因子
                 # 技术面因子权重
                 momentum_weight=0.15,  # 动量因子
                 volatility_weight=0.1, # 波动率因子
                 # 策略参数
                 stock_num=30,          # 持仓股票数量
                 rebalance_days=20,     # 调仓周期
                 min_market_cap=50,     # 最小市值要求(亿元)
                 **kwargs):
        """
        初始化策略参数
        """
        # 调用父类初始化
        super().__init__(name="增强版多因子选股策略", **kwargs)

        self.value_weight = value_weight
        self.quality_weight = quality_weight
        self.growth_weight = growth_weight
        self.momentum_weight = momentum_weight
        self.volatility_weight = volatility_weight
        self.stock_num = stock_num
        self.rebalance_days = rebalance_days
        self.min_market_cap = min_market_cap
        
        # 验证权重
        total_weight = (value_weight + quality_weight + growth_weight + 
                       momentum_weight + volatility_weight)
        if abs(total_weight - 1.0) > 0.001:
            raise ValueError(f"因子权重之和必须为1，当前为{total_weight}")
        
        # 数据库连接
        self.db_path = 'output/data/db/sqlite/quantification.db'
    
    def load_comprehensive_data(self, date):
        """
        加载综合数据（基本面+技术面+市场面）
        
        参数:
            date (str): 数据日期 (YYYYMMDD)
            
        返回:
            DataFrame: 综合数据
        """
        conn = sqlite3.connect(self.db_path)
        
        # 获取基本面数据
        fundamental_sql = """
        WITH latest_fundamental AS (
            SELECT 
                i.ts_code,
                i.end_date,
                i.revenue,
                i.n_income,
                i.total_revenue,
                b.total_assets,
                b.total_liab,
                b.total_hldr_eqy_exc_min_int as equity,
                c.n_cashflow_act,
                ROW_NUMBER() OVER (PARTITION BY i.ts_code ORDER BY i.end_date DESC) as rn
            FROM income i
            LEFT JOIN balance b ON i.ts_code = b.ts_code AND i.end_date = b.end_date
            LEFT JOIN cash_flow c ON i.ts_code = c.ts_code AND i.end_date = c.end_date
            WHERE i.end_date <= ? AND i.end_date >= ?
        )
        SELECT * FROM latest_fundamental WHERE rn = 1
        """
        
        # 计算一年前的日期
        date_dt = pd.to_datetime(date, format='%Y%m%d')
        one_year_ago = (date_dt - timedelta(days=365)).strftime('%Y%m%d')
        
        fundamental_data = pd.read_sql(fundamental_sql, conn, 
                                     params=[date, one_year_ago])
        
        # 获取市值和估值数据
        market_sql = """
        SELECT 
            ts_code,
            trade_date,
            total_mv,
            circ_mv,
            pe,
            pb,
            ps,
            dv_ratio
        FROM market_cap 
        WHERE trade_date <= ? 
        AND ts_code IN (SELECT DISTINCT ts_code FROM stock_list)
        ORDER BY ts_code, trade_date DESC
        """
        
        market_data = pd.read_sql(market_sql, conn, params=[date])
        # 取每只股票最新的市值数据
        market_data = market_data.groupby('ts_code').first().reset_index()
        
        # 获取技术面数据（最近20个交易日）
        technical_sql = """
        SELECT 
            ts_code,
            trade_date,
            close,
            vol,
            amount,
            pct_chg
        FROM daily 
        WHERE trade_date <= ? AND trade_date >= ?
        ORDER BY ts_code, trade_date
        """
        
        twenty_days_ago = (date_dt - timedelta(days=30)).strftime('%Y%m%d')
        technical_data = pd.read_sql(technical_sql, conn, 
                                   params=[date, twenty_days_ago])
        
        conn.close()
        
        # 合并数据
        data = pd.merge(fundamental_data, market_data, on='ts_code', how='inner')
        
        # 计算技术指标
        technical_factors = self.calculate_technical_factors(technical_data)
        data = pd.merge(data, technical_factors, on='ts_code', how='left')
        
        return data
    
    def calculate_technical_factors(self, technical_data):
        """
        计算技术面因子
        
        参数:
            technical_data (DataFrame): 技术数据
            
        返回:
            DataFrame: 技术因子
        """
        factors = []
        
        for ts_code, group in technical_data.groupby('ts_code'):
            if len(group) < 10:  # 数据不足
                continue
            
            group = group.sort_values('trade_date').reset_index(drop=True)
            
            # 动量因子
            if len(group) >= 20:
                momentum_20d = (group['close'].iloc[-1] / group['close'].iloc[-20] - 1)
            else:
                momentum_20d = (group['close'].iloc[-1] / group['close'].iloc[0] - 1)
            
            momentum_5d = (group['close'].iloc[-1] / group['close'].iloc[-5] - 1) if len(group) >= 5 else 0
            
            # 波动率因子
            returns = group['pct_chg'] / 100
            volatility = returns.std() * np.sqrt(252)  # 年化波动率
            
            # 成交量因子
            avg_volume = group['vol'].mean()
            recent_volume = group['vol'].tail(5).mean()
            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1
            
            # 换手率因子（简化）
            avg_turnover = group['amount'].mean() / group['close'].mean() if group['close'].mean() > 0 else 0
            
            factors.append({
                'ts_code': ts_code,
                'momentum_20d': momentum_20d,
                'momentum_5d': momentum_5d,
                'volatility': volatility,
                'volume_ratio': volume_ratio,
                'avg_turnover': avg_turnover
            })
        
        return pd.DataFrame(factors)
    
    def calculate_fundamental_factors(self, data):
        """
        计算基本面因子
        
        参数:
            data (DataFrame): 原始数据
            
        返回:
            DataFrame: 添加了基本面因子的数据
        """
        df = data.copy()
        
        # 转换数据类型
        numeric_cols = ['revenue', 'n_income', 'total_assets', 'total_liab', 
                       'equity', 'n_cashflow_act', 'total_mv', 'pe', 'pb', 'ps']
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 价值因子
        df['pe_inv'] = 1 / df['pe']  # PE倒数
        df['pb_inv'] = 1 / df['pb']  # PB倒数
        df['ps_inv'] = 1 / df['ps'] if 'ps' in df.columns else 0  # PS倒数
        
        # 质量因子
        df['roe'] = df['n_income'] / df['equity']  # ROE
        df['roa'] = df['n_income'] / df['total_assets']  # ROA
        df['debt_ratio'] = df['total_liab'] / df['total_assets']  # 资产负债率
        df['profit_margin'] = df['n_income'] / df['revenue']  # 净利润率
        
        # 成长因子（简化处理）
        df['revenue_growth'] = 0.1  # 简化：假设营收增长率
        df['profit_growth'] = 0.1   # 简化：假设利润增长率
        
        # 现金流因子
        df['cash_flow_yield'] = df['n_cashflow_act'] / df['total_mv']
        
        return df
    
    def calculate_factor_scores(self, data):
        """
        计算各类因子得分
        
        参数:
            data (DataFrame): 包含所有因子的数据
            
        返回:
            DataFrame: 添加了因子得分的数据
        """
        df = data.copy()
        
        # 过滤市值要求
        df = df[df['total_mv'] >= self.min_market_cap * 10000]  # 转换为万元
        
        if df.empty:
            return df
        
        # 价值因子得分
        value_factors = ['pe_inv', 'pb_inv', 'ps_inv']
        df['value_score'] = 0
        for factor in value_factors:
            if factor in df.columns:
                df[f'{factor}_rank'] = df[factor].rank(pct=True, method='min')
                df['value_score'] += df[f'{factor}_rank'] / len(value_factors)
        
        # 质量因子得分
        quality_factors = ['roe', 'roa', 'profit_margin', 'cash_flow_yield']
        df['quality_score'] = 0
        for factor in quality_factors:
            if factor in df.columns and df[factor].notna().any():
                df[f'{factor}_rank'] = df[factor].rank(pct=True, method='min')
                df['quality_score'] += df[f'{factor}_rank'] / len(quality_factors)
        
        # 债务因子（负向）
        if 'debt_ratio' in df.columns:
            df['debt_rank'] = df['debt_ratio'].rank(pct=True, method='min', ascending=False)
            df['quality_score'] = (df['quality_score'] + df['debt_rank']) / 2
        
        # 成长因子得分
        growth_factors = ['revenue_growth', 'profit_growth']
        df['growth_score'] = 0
        for factor in growth_factors:
            if factor in df.columns:
                df[f'{factor}_rank'] = df[factor].rank(pct=True, method='min')
                df['growth_score'] += df[f'{factor}_rank'] / len(growth_factors)
        
        # 动量因子得分
        momentum_factors = ['momentum_20d', 'momentum_5d']
        df['momentum_score'] = 0
        for factor in momentum_factors:
            if factor in df.columns and df[factor].notna().any():
                df[f'{factor}_rank'] = df[factor].rank(pct=True, method='min')
                df['momentum_score'] += df[f'{factor}_rank'] / len(momentum_factors)
        
        # 波动率因子得分（低波动率更好）
        if 'volatility' in df.columns and df['volatility'].notna().any():
            df['volatility_score'] = df['volatility'].rank(pct=True, method='min', ascending=False)
        else:
            df['volatility_score'] = 0.5
        
        # 综合得分
        df['total_score'] = (
            df['value_score'] * self.value_weight +
            df['quality_score'] * self.quality_weight +
            df['growth_score'] * self.growth_weight +
            df['momentum_score'] * self.momentum_weight +
            df['volatility_score'] * self.volatility_weight
        )
        
        return df
    
    def select_stocks(self, data):
        """
        选择股票
        
        参数:
            data (DataFrame): 包含因子得分的数据
            
        返回:
            DataFrame: 选中的股票
        """
        df = data.copy()
        
        # 基本筛选条件
        conditions = (
            (df['total_mv'] >= self.min_market_cap * 10000) &  # 市值要求
            (df['pe'] > 0) & (df['pe'] < 100) &  # PE合理范围
            (df['pb'] > 0) & (df['pb'] < 10) &   # PB合理范围
            (df['revenue'] > 0) &                # 有营收
            (df['n_income'] > 0)                 # 盈利
        )
        
        filtered_df = df[conditions].copy()
        
        if filtered_df.empty:
            return filtered_df
        
        # 计算因子得分
        scored_df = self.calculate_factor_scores(filtered_df)
        
        # 按综合得分排序，选择前N只股票
        selected_stocks = scored_df.nlargest(self.stock_num, 'total_score')
        
        return selected_stocks
    
    def run(self, date='20250701'):
        """
        运行策略
        
        参数:
            date (str): 运行日期
            
        返回:
            dict: 选股结果
        """
        print(f"运行增强版多因子选股策略...")
        print(f"因子权重: 价值{self.value_weight}, 质量{self.quality_weight}, "
              f"成长{self.growth_weight}, 动量{self.momentum_weight}, 波动率{self.volatility_weight}")
        
        try:
            # 加载数据
            data = self.load_comprehensive_data(date)
            
            if data.empty:
                print("未找到数据")
                return {'selected_stocks': pd.DataFrame(), 'summary': {}}
            
            # 计算基本面因子
            data = self.calculate_fundamental_factors(data)
            
            # 选择股票
            selected_stocks = self.select_stocks(data)
            
            if selected_stocks.empty:
                print("未选出符合条件的股票")
                return {'selected_stocks': pd.DataFrame(), 'summary': {}}
            
            # 生成摘要
            summary = {
                'total_stocks_analyzed': len(data),
                'stocks_after_filter': len(selected_stocks),
                'avg_score': selected_stocks['total_score'].mean(),
                'avg_market_cap': selected_stocks['total_mv'].mean() / 10000,  # 转换为亿元
                'avg_pe': selected_stocks['pe'].mean(),
                'avg_pb': selected_stocks['pb'].mean(),
                'avg_roe': selected_stocks['roe'].mean() if 'roe' in selected_stocks.columns else 0
            }
            
            print(f"\n选股完成:")
            print(f"分析股票数: {summary['total_stocks_analyzed']}")
            print(f"选中股票数: {summary['stocks_after_filter']}")
            print(f"平均得分: {summary['avg_score']:.3f}")
            print(f"平均市值: {summary['avg_market_cap']:.1f}亿元")
            print(f"平均PE: {summary['avg_pe']:.1f}")
            print(f"平均PB: {summary['avg_pb']:.2f}")
            
            # 显示前10只股票
            print(f"\n前10只选中股票:")
            top_10 = selected_stocks.head(10)
            for _, stock in top_10.iterrows():
                print(f"  {stock['ts_code']}: 得分{stock['total_score']:.3f}, "
                      f"市值{stock['total_mv']/10000:.1f}亿, PE{stock['pe']:.1f}")
            
            return {
                'selected_stocks': selected_stocks,
                'summary': summary,
                'date': date
            }
            
        except Exception as e:
            print(f"策略运行失败: {e}")
            return {'selected_stocks': pd.DataFrame(), 'summary': {}, 'error': str(e)}

# 示例用法
if __name__ == "__main__":
    strategy = EnhancedMultiFactorStrategy(
        value_weight=0.3,
        quality_weight=0.25,
        growth_weight=0.2,
        momentum_weight=0.15,
        volatility_weight=0.1,
        stock_num=30
    )
    
    result = strategy.run()
    
    if not result['selected_stocks'].empty:
        print(f"\n策略运行成功，选出{len(result['selected_stocks'])}只股票")
    else:
        print("策略运行完成，但未选出股票")
