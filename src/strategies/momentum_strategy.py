#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动量策略
基于股票过去一段时间的价格表现，选择动量最强的股票进行交易
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional, Union

# {{ AURA-X: Add - 导入BaseStrategy和DataFetcherManager，实现策略标准化. Approval: 寸止(ID:阶段2.3). }}
from src.strategy.strategies.base_strategy import BaseStrategy
from src.data.fetcher.data_fetcher_manager import DataFetcherManager

class MomentumStrategy(BaseStrategy):
    """
    动量策略类
    根据股票过去表现的动量选择股票
    """
    def __init__(self,
                 lookback_period=60,
                 holding_period=20,
                 top_n=10,
                 data_manager=None,
                 **kwargs):
        """
        初始化策略参数

        参数:
            lookback_period (int): 回看天数，用于计算动量
            holding_period (int): 持有天数，每隔多少天调仓一次
            top_n (int): 选择动量排名前几的股票
            data_manager (DataFetcherManager): 数据管理器
            **kwargs: 其他参数
        """
        # {{ AURA-X: Modify - 继承BaseStrategy，集成DataFetcherManager. Approval: 寸止(ID:阶段2.3). }}
        # 先设置属性，再调用父类初始化
        self.lookback_period = lookback_period
        self.holding_period = holding_period
        self.top_n = top_n

        # 集成数据管理器
        self.data_manager = data_manager or DataFetcherManager.get_instance()

        # 调用父类初始化
        super().__init__(name="股票动量策略", **kwargs)

        # 设置策略配置（使用BaseStrategy的属性）
        self.strategy_type = 'momentum'
        self.data_frequency = 'daily'

    def request_market_data(self, symbols, start_date, end_date, data_type='daily'):
        """
        请求市场数据的统一接口

        参数:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            data_type: 数据类型

        返回:
            DataFrame: 市场数据
        """
        try:
            # {{ AURA-X: Add - 统一市场数据获取接口，集成DataFetcherManager. Approval: 寸止(ID:阶段2.3). }}
            fetcher = self.data_manager.get_fetcher()

            # 获取市场数据
            market_data = fetcher.fetch_market_data(
                symbols=symbols,
                data_type=data_type,
                start_date=start_date,
                end_date=end_date,
                save=False,
                use_cache=True
            )

            self.logger.info(f"通过DataFetcherManager获取市场数据: {len(market_data)}条记录")
            return market_data

        except Exception as e:
            self.logger.error(f"市场数据获取失败: {e}")
            raise

    def calculate_momentum(self, data, date, lookback_date):
        """
        计算指定日期的动量
        
        参数:
            data (DataFrame): 股票价格数据
            date (str): 当前日期
            lookback_date (str): 回看日期
            
        返回:
            DataFrame: 股票的动量排名
        """
        # 获取当日和回看日的价格数据
        current_prices = data[data['trade_date'] == date][['ts_code', 'close']].set_index('ts_code')
        past_prices = data[data['trade_date'] == lookback_date][['ts_code', 'close']].set_index('ts_code')
        
        # 确保数据存在
        if current_prices.empty or past_prices.empty:
            return pd.DataFrame()
        
        # 合并数据
        prices = pd.merge(current_prices, past_prices, left_index=True, right_index=True, suffixes=('_current', '_past'))
        
        # 计算收益率
        prices['return'] = prices['close_current'] / prices['close_past'] - 1
        
        # 计算动量得分（按收益率排名）
        prices['momentum_score'] = prices['return'].rank(method='first', ascending=False)
        
        # 按动量得分排序
        prices = prices.sort_values('momentum_score')
        
        return prices.reset_index()
    
    def generate_signals(self, data):
        """
        生成交易信号
        
        参数:
            data (DataFrame): 包含日期、代码和收盘价的数据框
            
        返回:
            DataFrame: 每个日期持有的股票列表
        """
        # 获取所有交易日
        all_dates = sorted(data['trade_date'].unique())
        
        # 创建一个字典来存储每个日期的持仓
        holdings = {}
        
        # 当前持仓
        current_stocks = []
        
        # 上次调仓日期的索引
        last_rebalance_idx = -1
        
        # 遍历每个交易日
        for i, date in enumerate(all_dates):
            if i < self.lookback_period:
                # 数据不足以计算动量
                holdings[date] = []
                continue
            
            # 判断是否需要调仓
            if i - last_rebalance_idx >= self.holding_period or last_rebalance_idx == -1:
                # 获取回看日期
                lookback_idx = i - self.lookback_period
                lookback_date = all_dates[lookback_idx]
                
                # 计算动量
                momentum_data = self.calculate_momentum(data, date, lookback_date)
                
                # 选择动量排名靠前的股票
                if not momentum_data.empty:
                    current_stocks = momentum_data.head(self.top_n)['ts_code'].tolist()
                    last_rebalance_idx = i
            
            # 记录当日持仓
            holdings[date] = current_stocks
        
        # 转换为DataFrame
        holdings_df = pd.DataFrame([(date, stock) for date, stocks in holdings.items() for stock in stocks],
                                  columns=['trade_date', 'ts_code'])
        
        return holdings_df
    
    def backtest(self, data, initial_capital=1000000.0):
        """
        回测策略
        
        参数:
            data (DataFrame): 价格数据
            initial_capital (float): 初始资金
            
        返回:
            DataFrame: 包含仓位和资产价值的数据框
        """
        # 生成交易信号
        holdings_df = self.generate_signals(data)
        
        # 所有交易日
        all_dates = sorted(data['trade_date'].unique())
        
        # 创建回测结果DataFrame
        results = pd.DataFrame({'trade_date': all_dates})
        results = results.set_index('trade_date')
        
        # 初始化资金和持仓
        cash = initial_capital
        positions = {}
        
        # 上一个持仓日期
        last_holdings = []
        
        # 记录每日的资产总值
        portfolio_values = []
        
        # 遍历每个交易日
        for date in all_dates:
            # 当日持仓股票
            current_holdings = holdings_df[holdings_df['trade_date'] == date]['ts_code'].tolist()
            
            # 如果持仓发生变化，进行调仓
            if set(current_holdings) != set(last_holdings):
                # 卖出不在新持仓中的股票
                for stock in list(positions.keys()):
                    if stock not in current_holdings:
                        # 获取当日价格
                        price = data[(data['trade_date'] == date) & 
                                    (data['ts_code'] == stock)]['close'].values
                        
                        if len(price) > 0:
                            cash += positions[stock] * price[0]
                            del positions[stock]
                
                # 计算每只股票应分配的资金
                if current_holdings:
                    cash_per_stock = cash / len(current_holdings)
                    
                    # 买入新增的股票
                    for stock in current_holdings:
                        # 获取当日价格
                        price = data[(data['trade_date'] == date) & 
                                    (data['ts_code'] == stock)]['close'].values
                        
                        if len(price) > 0:
                            # 如果已持有该股票，则调整持仓
                            if stock in positions:
                                cash += positions[stock] * price[0] - cash_per_stock
                                positions[stock] = cash_per_stock / price[0]
                            else:
                                positions[stock] = cash_per_stock / price[0]
                                cash -= cash_per_stock
            
            # 计算当日资产总值
            portfolio_value = cash
            for stock, shares in positions.items():
                price = data[(data['trade_date'] == date) & 
                           (data['ts_code'] == stock)]['close'].values
                
                if len(price) > 0:
                    portfolio_value += shares * price[0]
            
            # 记录当日资产总值
            portfolio_values.append(portfolio_value)
            
            # 更新上一个持仓日期
            last_holdings = current_holdings
        
        # 添加资产总值到结果中
        results['portfolio_value'] = portfolio_values
        
        # 计算日收益率
        results['daily_return'] = results['portfolio_value'].pct_change()
        
        return results
    
    def show_performance(self, results):
        """
        显示策略性能指标
        
        参数:
            results (DataFrame): 回测结果数据框
        """
        # 计算年化收益率
        total_days = len(results)
        annual_return = (results['portfolio_value'].iloc[-1] / results['portfolio_value'].iloc[0] - 1) * (252 / total_days)
        
        # 计算最大回撤
        results['cummax'] = results['portfolio_value'].cummax()
        results['drawdown'] = (results['cummax'] - results['portfolio_value']) / results['cummax']
        max_drawdown = results['drawdown'].max()
        
        # 计算夏普比率
        risk_free_rate = 0.03 / 252  # 假设无风险年化收益率为3%
        excess_return = results['daily_return'] - risk_free_rate
        sharpe_ratio = excess_return.mean() / results['daily_return'].std() * np.sqrt(252)
        
        # 打印结果
        print(f"策略性能指标:")
        print(f"总收益率: {results['portfolio_value'].iloc[-1] / results['portfolio_value'].iloc[0] - 1:.2%}")
        print(f"年化收益率: {annual_return:.2%}")
        print(f"最大回撤: {max_drawdown:.2%}")
        print(f"夏普比率: {sharpe_ratio:.2f}")
        
        return {
            'total_return': results['portfolio_value'].iloc[-1] / results['portfolio_value'].iloc[0] - 1,
            'annual_return': annual_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio
        }
    
    def run(self, data, params=None, initial_capital=1000000.0):
        """
        运行策略
        
        参数:
            data (DataFrame): 价格数据
            params (dict): 策略参数字典
            initial_capital (float): 初始资金
            
        返回:
            dict: 回测结果和性能指标
        """
        # 更新策略参数
        if params:
            if 'lookback_period' in params:
                self.lookback_period = params['lookback_period']
            if 'holding_period' in params:
                self.holding_period = params['holding_period']
            if 'top_n' in params:
                self.top_n = params['top_n']
        
        # 运行回测
        results = self.backtest(data, initial_capital)
        
        # 计算性能指标
        performance = self.show_performance(results)
        
        return {
            'holdings': self.generate_signals(data),
            'results': results,
            'performance': performance
        }

    # {{ AURA-X: Add - 实现BaseStrategy的抽象方法. Approval: 寸止(ID:阶段2.3). }}
    def _generate_signals_impl(self, context: Dict[str, Any], data: pd.DataFrame) -> pd.DataFrame:
        """生成信号的具体实现"""
        try:
            # 动量策略基于历史价格数据生成信号
            signals = self.generate_signals(data)

            # 转换为标准信号格式
            if not signals.empty:
                # 获取当前日期的持仓
                current_date = context.get('current_date', '')
                if current_date:
                    current_holdings = signals[signals['trade_date'] == current_date]
                    if not current_holdings.empty:
                        signal_df = pd.DataFrame()
                        signal_df['ts_code'] = current_holdings['ts_code']
                        signal_df['signal'] = 1.0  # 买入信号
                        signal_df['weight'] = 1.0 / len(current_holdings)  # 等权重
                        signal_df['momentum_rank'] = range(1, len(current_holdings) + 1)
                        return signal_df

            return pd.DataFrame()

        except Exception as e:
            self.logger.error(f"生成动量信号失败: {e}")
            return pd.DataFrame()

    def _generate_weights_impl(self, context: Dict[str, Any], signals: pd.DataFrame) -> pd.DataFrame:
        """生成权重的具体实现"""
        if signals.empty:
            return pd.DataFrame()

        # 动量策略使用等权重
        weights = signals[['ts_code', 'weight']].copy()
        weights.set_index('ts_code', inplace=True)

        return weights

    def _generate_orders_impl(self, context: Dict[str, Any], weights: pd.DataFrame) -> List[Dict[str, Any]]:
        """生成订单的具体实现"""
        orders = []

        for symbol, row in weights.iterrows():
            if row['weight'] > 0:
                orders.append({
                    'symbol': symbol,
                    'side': 'buy',
                    'amount': row['weight'],
                    'type': 'market',
                    'strategy': 'momentum'
                })

        return orders

    def before_trading_start(self, context: Dict[str, Any]) -> None:
        """在每个交易日开始前调用"""
        self.logger.debug(f"动量策略交易日开始: {context.get('current_date', 'Unknown')}")

    def handle_data(self, context: Dict[str, Any], data: pd.DataFrame) -> Dict[str, Any]:
        """处理每个bar的数据，生成交易信号"""
        # 动量策略根据holding_period进行调仓
        current_date = context.get('current_date', '')

        # 检查是否需要调仓
        if self._should_rebalance(current_date, data):
            signals = self._generate_signals_impl(context, data)
            if not signals.empty:
                return {
                    'action': 'rebalance',
                    'selected_stocks': len(signals),
                    'avg_momentum': signals.get('momentum_rank', pd.Series()).mean()
                }

        return {'action': 'hold'}

    def _should_rebalance(self, current_date: str, data: pd.DataFrame) -> bool:
        """判断是否需要调仓"""
        # 简化的调仓逻辑，实际应该基于holding_period
        all_dates = sorted(data['trade_date'].unique())
        if current_date in all_dates:
            current_idx = all_dates.index(current_date)
            return current_idx % self.holding_period == 0
        return False

    def after_trading_end(self, context: Dict[str, Any]) -> None:
        """在每个交易日结束后调用"""
        self.logger.debug(f"动量策略交易日结束: {context.get('current_date', 'Unknown')}")

    def finalize(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """策略结束时调用"""
        return {
            'strategy_name': self.name,
            'parameters': self.get_parameters(),
            'strategy_type': 'momentum'
        }

    def get_parameters(self) -> Dict[str, Any]:
        """获取策略参数"""
        return {
            'lookback_period': self.lookback_period,
            'holding_period': self.holding_period,
            'top_n': self.top_n,
            'strategy_type': 'momentum'
        }

    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """设置策略参数"""
        if 'lookback_period' in parameters:
            self.lookback_period = parameters['lookback_period']
        if 'holding_period' in parameters:
            self.holding_period = parameters['holding_period']
        if 'top_n' in parameters:
            self.top_n = parameters['top_n']

        # 更新参数字典（使用BaseStrategy的属性）
        self.parameters.update({
            'lookback_period': self.lookback_period,
            'holding_period': self.holding_period,
            'top_n': self.top_n
        })

    def get_required_data(self) -> Dict[str, Any]:
        """获取策略所需的数据描述"""
        return {
            'market_data': {
                'frequency': 'daily',
                'fields': ['open', 'high', 'low', 'close', 'volume'],
                'lookback_periods': self.lookback_period + self.holding_period
            }
        }

# 注册策略到工厂
from src.strategy.strategies.base_strategy import StrategyFactory
try:
    StrategyFactory.register('momentum', MomentumStrategy)
except ValueError:
    # 策略已注册，忽略错误
    pass

# 示例用法
if __name__ == "__main__":
    # 创建模拟数据
    print("创建模拟数据...")
    
    # 模拟交易日期和股票
    dates = pd.date_range('2020-01-01', periods=120, freq='D').strftime('%Y%m%d')
    stocks = [f'00000{i}.SZ' for i in range(1, 21)]
    
    # 创建价格数据
    rows = []
    # 为每只股票生成一个基础价格
    base_prices = {stock: np.random.uniform(10, 100) for stock in stocks}
    
    for i, date in enumerate(dates):
        for stock in stocks:
            # 添加一些趋势和随机波动
            if i == 0:
                # 第一天使用基础价格
                price = base_prices[stock]
            else:
                # 之后的价格基于前一天的价格加上一些随机波动
                prev_price = data[data['ts_code'] == stock]['close'].iloc[-1]
                # 添加一些趋势（有些股票上涨趋势强，有些下跌趋势强）
                trend = np.random.normal(0.0005, 0.001) * (1 if stock < stocks[len(stocks)//2] else -1)
                # 添加随机波动
                random_change = np.random.normal(0, 0.01)
                price = prev_price * (1 + trend + random_change)
            
            row = {
                'trade_date': date,
                'ts_code': stock,
                'open': price * (1 + np.random.uniform(-0.005, 0.005)),
                'high': price * (1 + np.random.uniform(0, 0.01)),
                'low': price * (1 - np.random.uniform(0, 0.01)),
                'close': price,
                'volume': np.random.uniform(1000000, 10000000)
            }
            rows.append(row)
            
        if i == 0:
            # 转换第一天的数据为DataFrame
            data = pd.DataFrame(rows)
            rows = []
        else:
            # 添加新数据
            data = pd.concat([data, pd.DataFrame(rows)])
            rows = []
    
    # 重置索引
    data = data.reset_index(drop=True)
    
    # 创建策略实例
    strategy = MomentumStrategy(
        lookback_period=60,
        holding_period=20,
        top_n=5
    )
    
    # 运行策略
    print("运行回测...")
    results = strategy.run(data)
    
    # 打印最终结果
    print(f"\n回测完成，最终资产价值: {results['results']['portfolio_value'].iloc[-1]:.2f}")
    print(f"选出的股票示例 (最后一个交易日):")
    last_date = dates[-1]
    last_holdings = results['holdings'][results['holdings']['trade_date'] == last_date]
    print(last_holdings['ts_code'].tolist())
