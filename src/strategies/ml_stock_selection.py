#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习选股策略
基于您的完美数据平台，实现AI驱动的智能选股系统
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, accuracy_score
import warnings
warnings.filterwarnings('ignore')

class MLStockSelection:
    """
    机器学习选股策略
    使用多种机器学习算法进行智能选股
    """
    
    def __init__(self,
                 prediction_days=20,      # 预测天数
                 return_threshold=0.05,   # 收益率阈值
                 feature_window=60,       # 特征计算窗口
                 min_samples=100):        # 最小样本数
        """
        初始化机器学习选股策略
        """
        self.prediction_days = prediction_days
        self.return_threshold = return_threshold
        self.feature_window = feature_window
        self.min_samples = min_samples
        self.name = "机器学习选股策略"
        
        self.db_path = 'output/data/db/sqlite/quantification.db'
        self.models = {}
        self.scaler = StandardScaler()
        self.feature_names = []
    
    def get_training_stocks(self, limit=50):
        """
        获取用于训练的股票列表
        """
        conn = sqlite3.connect(self.db_path)
        
        sql = """
        WITH stock_stats AS (
            SELECT
                d.ts_code,
                s.name,
                COUNT(*) as trading_days,
                AVG(d.amount) as avg_amount
            FROM daily d
            JOIN stock_list s ON d.ts_code = s.ts_code
            WHERE d.vol > 0 AND d.amount > 0
            GROUP BY d.ts_code, s.name
            HAVING COUNT(*) >= ?
        )
        SELECT ts_code, name, trading_days, avg_amount
        FROM stock_stats
        WHERE avg_amount > 1000000  -- 日均成交额大于100万
        ORDER BY avg_amount DESC
        LIMIT ?
        """
        
        stocks = pd.read_sql(sql, conn, params=[self.feature_window + self.prediction_days + 20, limit])
        conn.close()
        
        return stocks['ts_code'].tolist()
    
    def calculate_technical_features(self, data):
        """
        计算技术面特征
        """
        df = data.copy()
        
        # 价格特征
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        # 移动平均特征
        for window in [5, 10, 20, 30]:
            df[f'ma_{window}'] = df['close'].rolling(window=window).mean()
            df[f'ma_ratio_{window}'] = df['close'] / df[f'ma_{window}']
        
        # 波动率特征
        for window in [5, 10, 20]:
            df[f'volatility_{window}'] = df['returns'].rolling(window=window).std()
        
        # 动量特征
        for window in [5, 10, 20]:
            df[f'momentum_{window}'] = df['close'] / df['close'].shift(window) - 1
        
        # 成交量特征
        df['volume_ma_20'] = df['vol'].rolling(window=20).mean()
        df['volume_ratio'] = df['vol'] / df['volume_ma_20']
        df['volume_price_trend'] = df['vol'].rolling(window=5).corr(df['close'].rolling(window=5))
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 布林带
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # MACD
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema_12 - ema_26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        return df
    
    def calculate_fundamental_features(self, ts_code):
        """
        计算基本面特征
        """
        conn = sqlite3.connect(self.db_path)
        
        # 获取最新财务数据
        sql = """
        WITH latest_financial AS (
            SELECT 
                i.ts_code,
                i.revenue,
                i.n_income,
                b.total_assets,
                b.total_liab,
                b.total_hldr_eqy_exc_min_int as equity,
                c.n_cashflow_act,
                ROW_NUMBER() OVER (PARTITION BY i.ts_code ORDER BY i.end_date DESC) as rn
            FROM income i
            LEFT JOIN balance b ON i.ts_code = b.ts_code AND i.end_date = b.end_date
            LEFT JOIN cash_flow c ON i.ts_code = c.ts_code AND i.end_date = c.end_date
            WHERE i.ts_code = ?
        )
        SELECT * FROM latest_financial WHERE rn = 1
        """
        
        financial = pd.read_sql(sql, conn, params=[ts_code])
        conn.close()
        
        if financial.empty:
            return {}
        
        row = financial.iloc[0]
        
        # 计算财务比率
        features = {}
        
        if row['equity'] and row['equity'] > 0:
            features['roe'] = row['n_income'] / row['equity'] if row['n_income'] else 0
        else:
            features['roe'] = 0
            
        if row['total_assets'] and row['total_assets'] > 0:
            features['roa'] = row['n_income'] / row['total_assets'] if row['n_income'] else 0
            features['debt_ratio'] = row['total_liab'] / row['total_assets'] if row['total_liab'] else 0
        else:
            features['roa'] = 0
            features['debt_ratio'] = 0
            
        if row['revenue'] and row['revenue'] > 0:
            features['profit_margin'] = row['n_income'] / row['revenue'] if row['n_income'] else 0
        else:
            features['profit_margin'] = 0
        
        features['cash_flow'] = row['n_cashflow_act'] if row['n_cashflow_act'] else 0
        
        return features
    
    def prepare_training_data(self, stocks):
        """
        准备训练数据
        """
        print("📊 准备机器学习训练数据...")
        
        all_features = []
        all_labels = []
        
        for i, stock in enumerate(stocks):
            print(f"  处理股票 {i+1}/{len(stocks)}: {stock}")
            
            try:
                # 获取股票数据
                conn = sqlite3.connect(self.db_path)
                sql = """
                SELECT trade_date, close, vol, amount
                FROM daily 
                WHERE ts_code = ?
                ORDER BY trade_date
                """
                
                data = pd.read_sql(sql, conn, params=[stock])
                conn.close()
                
                if len(data) < self.feature_window + self.prediction_days + 20:
                    continue
                
                # 计算技术特征
                data = self.calculate_technical_features(data)
                
                # 获取基本面特征
                fundamental_features = self.calculate_fundamental_features(stock)
                
                # 计算未来收益率标签
                data['future_return'] = data['close'].shift(-self.prediction_days) / data['close'] - 1
                data['label'] = (data['future_return'] > self.return_threshold).astype(int)
                
                # 提取特征和标签
                feature_cols = [col for col in data.columns if col not in 
                              ['trade_date', 'close', 'vol', 'amount', 'future_return', 'label']]
                
                for idx in range(self.feature_window, len(data) - self.prediction_days):
                    # 技术特征
                    tech_features = data.iloc[idx][feature_cols].values
                    
                    # 基本面特征
                    fund_features = list(fundamental_features.values())
                    
                    # 合并特征
                    combined_features = np.concatenate([tech_features, fund_features])
                    
                    # 检查是否有无效值
                    if not np.isnan(combined_features).any() and not np.isinf(combined_features).any():
                        all_features.append(combined_features)
                        all_labels.append(data.iloc[idx]['label'])
                
            except Exception as e:
                print(f"    处理失败: {e}")
                continue
        
        if not all_features:
            raise ValueError("没有有效的训练数据")
        
        # 记录特征名称
        if not self.feature_names:
            self.feature_names = feature_cols + list(fundamental_features.keys())
        
        X = np.array(all_features)
        y = np.array(all_labels)
        
        print(f"训练数据准备完成: {X.shape[0]}个样本, {X.shape[1]}个特征")
        print(f"正样本比例: {y.mean():.2%}")
        
        return X, y
    
    def train_models(self, X, y):
        """
        训练多个机器学习模型
        """
        print("🤖 训练机器学习模型...")
        
        # 数据标准化
        X_scaled = self.scaler.fit_transform(X)
        
        # 分割训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 定义模型
        models = {
            'RandomForest': RandomForestClassifier(
                n_estimators=100, 
                max_depth=10, 
                random_state=42,
                class_weight='balanced'
            ),
            'GradientBoosting': GradientBoostingClassifier(
                n_estimators=100, 
                max_depth=6, 
                random_state=42
            )
        }
        
        # 训练和评估模型
        for name, model in models.items():
            print(f"\n训练 {name} 模型...")
            
            # 训练模型
            model.fit(X_train, y_train)
            
            # 预测
            y_pred = model.predict(X_test)
            
            # 评估
            accuracy = accuracy_score(y_test, y_pred)
            cv_scores = cross_val_score(model, X_train, y_train, cv=5)
            
            print(f"  测试集准确率: {accuracy:.3f}")
            print(f"  交叉验证平均准确率: {cv_scores.mean():.3f} (+/- {cv_scores.std() * 2:.3f})")
            
            # 保存模型
            self.models[name] = model
            
            # 特征重要性
            if hasattr(model, 'feature_importances_'):
                feature_importance = pd.DataFrame({
                    'feature': self.feature_names,
                    'importance': model.feature_importances_
                }).sort_values('importance', ascending=False)
                
                print(f"  前5个重要特征:")
                for _, row in feature_importance.head(5).iterrows():
                    print(f"    {row['feature']}: {row['importance']:.3f}")
        
        return X_test, y_test
    
    def predict_stock_performance(self, ts_code):
        """
        预测单只股票的表现
        """
        try:
            # 获取股票数据
            conn = sqlite3.connect(self.db_path)
            sql = """
            SELECT trade_date, close, vol, amount
            FROM daily 
            WHERE ts_code = ?
            ORDER BY trade_date DESC
            LIMIT ?
            """
            
            data = pd.read_sql(sql, conn, params=[ts_code, self.feature_window + 10])
            conn.close()
            
            if len(data) < self.feature_window:
                return None
            
            # 按日期排序
            data = data.sort_values('trade_date').reset_index(drop=True)
            
            # 计算技术特征
            data = self.calculate_technical_features(data)
            
            # 获取基本面特征
            fundamental_features = self.calculate_fundamental_features(ts_code)
            
            # 提取最新特征
            feature_cols = [col for col in data.columns if col not in 
                          ['trade_date', 'close', 'vol', 'amount']]
            
            latest_tech_features = data.iloc[-1][feature_cols].values
            latest_fund_features = list(fundamental_features.values())
            
            # 合并特征
            combined_features = np.concatenate([latest_tech_features, latest_fund_features])
            
            # 检查无效值
            if np.isnan(combined_features).any() or np.isinf(combined_features).any():
                return None
            
            # 标准化
            features_scaled = self.scaler.transform([combined_features])
            
            # 预测
            predictions = {}
            for name, model in self.models.items():
                prob = model.predict_proba(features_scaled)[0]
                predictions[name] = {
                    'probability': prob[1],  # 正类概率
                    'prediction': model.predict(features_scaled)[0]
                }
            
            return predictions
            
        except Exception as e:
            return None
    
    def run_ml_stock_selection(self, candidate_limit=30):
        """
        运行机器学习选股
        """
        print("🧠 机器学习选股系统")
        print("基于您的完美数据平台")
        print("=" * 60)
        
        # 获取训练股票
        training_stocks = self.get_training_stocks(limit=20)
        
        if len(training_stocks) < 10:
            print("❌ 训练股票数量不足")
            return
        
        print(f"训练股票池: {len(training_stocks)}只")
        
        # 准备训练数据
        try:
            X, y = self.prepare_training_data(training_stocks)
        except Exception as e:
            print(f"❌ 训练数据准备失败: {e}")
            return
        
        if len(X) < self.min_samples:
            print(f"❌ 训练样本不足: {len(X)} < {self.min_samples}")
            return
        
        # 训练模型
        X_test, y_test = self.train_models(X, y)
        
        # 获取候选股票
        candidate_stocks = self.get_training_stocks(limit=candidate_limit)
        
        print(f"\n🎯 对{len(candidate_stocks)}只候选股票进行预测...")
        
        # 预测候选股票
        predictions = []
        
        for stock in candidate_stocks:
            pred = self.predict_stock_performance(stock)
            if pred:
                # 计算平均概率
                avg_prob = np.mean([p['probability'] for p in pred.values()])
                predictions.append({
                    'ts_code': stock,
                    'avg_probability': avg_prob,
                    'predictions': pred
                })
        
        # 排序并选择最佳股票
        predictions.sort(key=lambda x: x['avg_probability'], reverse=True)
        
        print(f"\n📈 机器学习选股结果 (前10只):")
        print("=" * 60)
        
        for i, pred in enumerate(predictions[:10]):
            print(f"{i+1:2d}. {pred['ts_code']}: 上涨概率 {pred['avg_probability']:.1%}")
            for model_name, model_pred in pred['predictions'].items():
                print(f"     {model_name}: {model_pred['probability']:.1%}")
            print()
        
        return predictions[:10]

def main():
    """
    主函数
    """
    ml_strategy = MLStockSelection(
        prediction_days=20,
        return_threshold=0.05,
        feature_window=60,
        min_samples=100
    )
    
    results = ml_strategy.run_ml_stock_selection(candidate_limit=25)

if __name__ == "__main__":
    main()
