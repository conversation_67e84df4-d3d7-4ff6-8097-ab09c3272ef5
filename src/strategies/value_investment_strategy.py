#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
价值投资策略
基于您的完美财务数据平台，实现巴菲特式价值投资策略
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False

# {{ AURA-X: Add - 导入BaseStrategy和DataFetcherManager，实现策略标准化. Approval: 寸止(ID:阶段2.2). }}
from src.strategy.strategies.base_strategy import BaseStrategy
from src.data.fetcher.data_fetcher_manager import DataFetcherManager

class ValueInvestmentStrategy(BaseStrategy):
    """
    价值投资策略
    基于财务指标筛选低估值、高质量的股票
    """
    
    def __init__(self,
                 pe_max=15,          # PE上限
                 pb_max=2,           # PB上限
                 roe_min=0.15,       # ROE下限
                 debt_ratio_max=0.6, # 资产负债率上限
                 revenue_growth_min=0.1,  # 营收增长率下限
                 profit_growth_min=0.1,   # 净利润增长率下限
                 stock_num=20,       # 持仓股票数量
                 rebalance_months=3, # 调仓周期（月）
                 data_manager=None,  # 数据管理器
                 **kwargs):
        """
        初始化策略参数
        """
        # {{ AURA-X: Modify - 继承BaseStrategy，集成DataFetcherManager. Approval: 寸止(ID:阶段2.2). }}
        # 先设置属性，再调用父类初始化
        self.pe_max = pe_max
        self.pb_max = pb_max
        self.roe_min = roe_min
        self.debt_ratio_max = debt_ratio_max
        self.revenue_growth_min = revenue_growth_min
        self.profit_growth_min = profit_growth_min
        self.stock_num = stock_num
        self.rebalance_months = rebalance_months

        # 集成数据管理器
        self.data_manager = data_manager or DataFetcherManager.get_instance()

        # 调用父类初始化
        super().__init__(name="价值投资策略", **kwargs)

        # 设置策略配置（使用BaseStrategy的属性）
        self.strategy_type = 'value_investment'
        self.data_frequency = 'quarterly'  # 财务数据通常是季度频率

        # 数据库连接（向后兼容）
        self.db_path = 'output/data/db/sqlite/quantification.db'

    def request_financial_data(self, symbols=None, end_date=None, report_type='annual'):
        """
        请求财务数据的统一接口

        参数:
            symbols: 股票代码列表（可选，None表示获取所有股票）
            end_date: 截止日期
            report_type: 报告类型

        返回:
            DataFrame: 财务数据
        """
        try:
            # {{ AURA-X: Modify - 获取完整财务数据，包含利润表、资产负债表、现金流量表. Approval: 寸止(ID:阶段2.2). }}
            fetcher = self.data_manager.get_fetcher()

            if symbols:
                # 获取多种财务报表数据
                all_financial_data = []

                # 获取利润表数据
                try:
                    income_data = fetcher.fetch_financial_data(
                        symbols=symbols,
                        report_type='income',
                        end_date=end_date,
                        save=False,
                        use_cache=True
                    )
                    if not income_data.empty:
                        all_financial_data.append(income_data)
                except Exception as e:
                    self.logger.warning(f"获取利润表数据失败: {e}")

                # 获取资产负债表数据
                try:
                    balance_data = fetcher.fetch_financial_data(
                        symbols=symbols,
                        report_type='balancesheet',
                        end_date=end_date,
                        save=False,
                        use_cache=True
                    )
                    if not balance_data.empty:
                        # 只保留资产负债表特有的字段
                        balance_cols = ['ts_code', 'end_date', 'total_assets', 'total_liab', 'total_hldr_eqy_exc_min_int']
                        balance_data = balance_data[balance_cols].copy()
                        all_financial_data.append(balance_data)
                except Exception as e:
                    self.logger.warning(f"获取资产负债表数据失败: {e}")

                # 获取现金流量表数据
                try:
                    cashflow_data = fetcher.fetch_financial_data(
                        symbols=symbols,
                        report_type='cashflow',
                        end_date=end_date,
                        save=False,
                        use_cache=True
                    )
                    if not cashflow_data.empty:
                        # 只保留现金流量表特有的字段
                        cashflow_cols = ['ts_code', 'end_date', 'n_cashflow_act']
                        cashflow_data = cashflow_data[cashflow_cols].copy()
                        all_financial_data.append(cashflow_data)
                except Exception as e:
                    self.logger.warning(f"获取现金流量表数据失败: {e}")

                # 获取市值数据
                try:
                    market_data = fetcher.fetch_market_data(
                        symbols=symbols,
                        data_type='daily_basic',
                        start_date=end_date,
                        end_date=end_date,
                        save=False,
                        use_cache=True
                    )
                    if not market_data.empty:
                        # 只保留市值相关字段
                        market_cols = ['ts_code', 'trade_date', 'total_mv', 'pe', 'pb']
                        available_cols = [col for col in market_cols if col in market_data.columns]
                        if available_cols:
                            market_data = market_data[available_cols].copy()
                            # 重命名trade_date为end_date以便合并
                            if 'trade_date' in market_data.columns:
                                market_data['end_date'] = market_data['trade_date']
                            all_financial_data.append(market_data)
                except Exception as e:
                    self.logger.warning(f"获取市值数据失败: {e}")

                # 合并所有财务数据
                if all_financial_data:
                    financial_data = all_financial_data[0]
                    for data in all_financial_data[1:]:
                        financial_data = pd.merge(
                            financial_data, data,
                            on=['ts_code', 'end_date'],
                            how='left'
                        )

                    self.logger.info(f"通过DataFetcherManager获取完整财务数据: {len(financial_data)}条记录")
                    return financial_data
                else:
                    self.logger.warning("未获取到任何财务数据，回退到原有方法")
                    return self.load_financial_data(end_date)
            else:
                # 如果没有指定symbols，回退到原有方法
                return self.load_financial_data(end_date)

        except Exception as e:
            self.logger.warning(f"统一数据获取失败，回退到原有方法: {e}")
            return self.load_financial_data(end_date)

    def load_financial_data(self, end_date=None):
        """
        加载财务数据
        
        参数:
            end_date (str): 截止日期 (YYYYMMDD)
            
        返回:
            DataFrame: 综合财务数据
        """
        conn = sqlite3.connect(self.db_path)
        
        # 获取最新的财务数据
        sql = """
        WITH latest_data AS (
            SELECT 
                i.ts_code,
                i.end_date,
                i.revenue,
                i.n_income,
                b.total_assets,
                b.total_liab,
                c.n_cashflow_act,
                ROW_NUMBER() OVER (PARTITION BY i.ts_code ORDER BY i.end_date DESC) as rn
            FROM income i
            LEFT JOIN balance b ON i.ts_code = b.ts_code AND i.end_date = b.end_date
            LEFT JOIN cash_flow c ON i.ts_code = c.ts_code AND i.end_date = c.end_date
            WHERE i.end_date IS NOT NULL
        )
        SELECT 
            ts_code,
            end_date,
            revenue,
            n_income,
            total_assets,
            total_liab,
            n_cashflow_act
        FROM latest_data 
        WHERE rn = 1
        """
        
        if end_date:
            sql = sql.replace("WHERE i.end_date IS NOT NULL", 
                            f"WHERE i.end_date IS NOT NULL AND i.end_date <= '{end_date}'")
        
        financial_data = pd.read_sql(sql, conn)
        
        # {{ AURA-X: Modify - 修复SQLite查询，处理缺少列的情况. Approval: 寸止(ID:阶段2.2). }}
        # 获取市值数据，处理可能缺少的列
        try:
            # 首先检查表结构
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(market_cap)")
            columns = [row[1] for row in cursor.fetchall()]

            # 构建动态SQL，只查询存在的列
            available_cols = ['ts_code', 'trade_date']
            optional_cols = ['total_mv', 'pe', 'pb', 'pe_ttm', 'pb_mrq']

            for col in optional_cols:
                if col in columns:
                    available_cols.append(col)

            if len(available_cols) > 2:  # 至少有ts_code, trade_date和一个数值列
                cols_str = ', '.join(available_cols)
                market_sql = f"""
                WITH latest_market AS (
                    SELECT
                        {cols_str},
                        ROW_NUMBER() OVER (PARTITION BY ts_code ORDER BY trade_date DESC) as rn
                    FROM market_cap
                    WHERE ts_code IS NOT NULL
                )
                SELECT {cols_str}
                FROM latest_market
                WHERE rn = 1
                """

                market_data = pd.read_sql(market_sql, conn)
            else:
                # 如果表结构不完整，创建空的DataFrame
                market_data = pd.DataFrame(columns=['ts_code', 'total_mv', 'pe', 'pb'])

        except Exception as market_error:
            self.logger.warning(f"获取市值数据失败: {market_error}")
            # 创建空的市值数据DataFrame
            market_data = pd.DataFrame(columns=['ts_code', 'total_mv', 'pe', 'pb'])
        conn.close()
        
        # 合并数据
        data = pd.merge(financial_data, market_data, on='ts_code', how='inner')
        
        return data
    
    def calculate_financial_metrics(self, data):
        """
        计算财务指标
        
        参数:
            data (DataFrame): 原始财务数据
            
        返回:
            DataFrame: 添加了计算指标的数据
        """
        df = data.copy()
        
        # 转换数据类型
        numeric_cols = ['revenue', 'n_income', 'total_assets', 'total_liab', 
                       'n_cashflow_act', 'total_mv', 'pe', 'pb']
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 计算ROE (净资产收益率)
        df['equity'] = df['total_assets'] - df['total_liab']
        df['roe'] = df['n_income'] / df['equity']
        
        # 计算资产负债率
        df['debt_ratio'] = df['total_liab'] / df['total_assets']
        
        # 计算净利润率
        df['profit_margin'] = df['n_income'] / df['revenue']
        
        # 计算现金流收益率
        df['cash_flow_yield'] = df['n_cashflow_act'] / df['total_mv']
        
        # 计算增长率（需要历史数据，这里简化处理）
        df['revenue_growth'] = 0.1  # 简化：假设平均增长率
        df['profit_growth'] = 0.1   # 简化：假设平均增长率
        
        return df
    
    def calculate_value_score(self, data):
        """
        计算价值评分
        
        参数:
            data (DataFrame): 财务指标数据
            
        返回:
            DataFrame: 添加了价值评分的数据
        """
        df = data.copy()
        
        # 初始化评分
        df['value_score'] = 0
        
        # PE评分 (越低越好)
        df['pe_score'] = np.where(df['pe'] <= self.pe_max, 
                                 (self.pe_max - df['pe']) / self.pe_max * 25, 0)
        
        # PB评分 (越低越好)
        df['pb_score'] = np.where(df['pb'] <= self.pb_max,
                                 (self.pb_max - df['pb']) / self.pb_max * 25, 0)
        
        # ROE评分 (越高越好)
        df['roe_score'] = np.where(df['roe'] >= self.roe_min,
                                  np.minimum(df['roe'] * 100, 30), 0)
        
        # 债务评分 (负债率越低越好)
        df['debt_score'] = np.where(df['debt_ratio'] <= self.debt_ratio_max,
                                   (self.debt_ratio_max - df['debt_ratio']) / self.debt_ratio_max * 20, 0)
        
        # 综合评分
        df['value_score'] = (df['pe_score'] + df['pb_score'] + 
                           df['roe_score'] + df['debt_score'])
        
        return df
    
    def screen_stocks(self, data):
        """
        筛选股票
        
        参数:
            data (DataFrame): 财务数据
            
        返回:
            DataFrame: 筛选后的股票
        """
        df = data.copy()
        
        # 基本筛选条件
        conditions = (
            (df['pe'] > 0) & (df['pe'] <= self.pe_max) &
            (df['pb'] > 0) & (df['pb'] <= self.pb_max) &
            (df['roe'] >= self.roe_min) &
            (df['debt_ratio'] <= self.debt_ratio_max) &
            (df['revenue'] > 0) &
            (df['n_income'] > 0) &
            (df['total_assets'] > 0)
        )
        
        # 应用筛选条件
        filtered_df = df[conditions].copy()
        
        # 计算价值评分
        filtered_df = self.calculate_value_score(filtered_df)
        
        # 按评分排序，选择前N只股票
        top_stocks = filtered_df.nlargest(self.stock_num, 'value_score')
        
        return top_stocks
    
    def backtest(self, start_date='20240101', end_date='20250701', initial_capital=1000000.0):
        """
        回测策略
        
        参数:
            start_date (str): 开始日期
            end_date (str): 结束日期
            initial_capital (float): 初始资金
            
        返回:
            dict: 回测结果
        """
        # 获取调仓日期列表
        rebalance_dates = self.get_rebalance_dates(start_date, end_date)
        
        # 初始化组合
        portfolio = {}
        portfolio_values = []
        holdings_history = []
        
        cash = initial_capital
        
        for date in rebalance_dates:
            print(f"调仓日期: {date}")
            
            # 获取当期财务数据
            financial_data = self.load_financial_data(date)
            
            if financial_data.empty:
                continue
            
            # 计算财务指标
            financial_data = self.calculate_financial_metrics(financial_data)
            
            # 筛选股票
            selected_stocks = self.screen_stocks(financial_data)
            
            if selected_stocks.empty:
                continue
            
            # 获取价格数据进行调仓
            portfolio_value = self.rebalance_portfolio(
                portfolio, selected_stocks, date, cash
            )
            
            portfolio_values.append({
                'date': date,
                'value': portfolio_value,
                'stocks': len(selected_stocks)
            })
            
            holdings_history.append({
                'date': date,
                'holdings': selected_stocks[['ts_code', 'value_score']].to_dict('records')
            })
        
        # 计算性能指标
        performance = self.calculate_performance(portfolio_values, initial_capital)
        
        return {
            'portfolio_values': portfolio_values,
            'holdings_history': holdings_history,
            'performance': performance,
            'final_value': portfolio_values[-1]['value'] if portfolio_values else initial_capital
        }
    
    def get_rebalance_dates(self, start_date, end_date):
        """
        获取调仓日期列表
        """
        # 简化：每季度调仓
        dates = []
        current_date = pd.to_datetime(start_date, format='%Y%m%d')
        end_dt = pd.to_datetime(end_date, format='%Y%m%d')
        
        while current_date <= end_dt:
            dates.append(current_date.strftime('%Y%m%d'))
            # 下一个调仓日期（3个月后）
            if current_date.month <= 9:
                current_date = current_date.replace(month=current_date.month + 3)
            else:
                current_date = current_date.replace(year=current_date.year + 1, 
                                                  month=current_date.month - 9)
        
        return dates
    
    def rebalance_portfolio(self, portfolio, selected_stocks, date, cash):
        """
        调仓操作
        """
        # 简化：等权重分配
        if len(selected_stocks) > 0:
            weight_per_stock = 1.0 / len(selected_stocks)
            total_value = cash  # 简化：假设全部资金投入
            
            for _, stock in selected_stocks.iterrows():
                portfolio[stock['ts_code']] = {
                    'weight': weight_per_stock,
                    'value': total_value * weight_per_stock,
                    'score': stock['value_score']
                }
        
        return cash  # 简化：返回总资金
    
    def calculate_performance(self, portfolio_values, initial_capital):
        """
        计算性能指标
        """
        if not portfolio_values:
            return {}
        
        values = [pv['value'] for pv in portfolio_values]
        final_value = values[-1]
        
        total_return = (final_value / initial_capital) - 1
        
        # 简化的性能指标
        return {
            'total_return': total_return,
            'annual_return': total_return,  # 简化
            'max_drawdown': 0.0,  # 简化
            'sharpe_ratio': 0.0,  # 简化
            'rebalance_count': len(portfolio_values)
        }
    
    def run(self, start_date='20240101', end_date='20250701', initial_capital=1000000.0):
        """
        运行策略
        """
        print(f"开始运行价值投资策略...")
        print(f"筛选条件: PE<={self.pe_max}, PB<={self.pb_max}, ROE>={self.roe_min}")
        
        result = self.backtest(start_date, end_date, initial_capital)
        
        print(f"\n策略回测完成:")
        print(f"总收益率: {result['performance']['total_return']:.2%}")
        print(f"调仓次数: {result['performance']['rebalance_count']}")
        
        return result

    # {{ AURA-X: Add - 实现BaseStrategy的抽象方法. Approval: 寸止(ID:阶段2.2). }}
    def _generate_signals_impl(self, context: Dict[str, Any], data: pd.DataFrame) -> pd.DataFrame:
        """生成信号的具体实现"""
        # 价值投资策略基于财务数据筛选，不是基于价格信号
        # 这里返回筛选结果作为信号
        try:
            # 获取财务数据
            financial_data = self.request_financial_data(
                symbols=context.get('universe', None),
                end_date=context.get('current_date', None)
            )

            if financial_data.empty:
                return pd.DataFrame()

            # 计算财务指标
            financial_data = self.calculate_financial_metrics(financial_data)

            # 筛选股票
            selected_stocks = self.screen_stocks(financial_data)

            # 转换为信号格式
            signals = pd.DataFrame()
            if not selected_stocks.empty:
                signals = selected_stocks[['ts_code', 'value_score']].copy()
                signals['signal'] = 1.0  # 买入信号
                signals['weight'] = 1.0 / len(selected_stocks)  # 等权重

            return signals

        except Exception as e:
            self.logger.error(f"生成价值投资信号失败: {e}")
            return pd.DataFrame()

    def _generate_weights_impl(self, context: Dict[str, Any], signals: pd.DataFrame) -> pd.DataFrame:
        """生成权重的具体实现"""
        if signals.empty:
            return pd.DataFrame()

        # 价值投资策略使用等权重
        weights = signals[['ts_code', 'weight']].copy()
        weights.set_index('ts_code', inplace=True)

        return weights

    def _generate_orders_impl(self, context: Dict[str, Any], weights: pd.DataFrame) -> List[Dict[str, Any]]:
        """生成订单的具体实现"""
        orders = []

        for symbol, row in weights.iterrows():
            if row['weight'] > 0:
                orders.append({
                    'symbol': symbol,
                    'side': 'buy',
                    'amount': row['weight'],
                    'type': 'market',
                    'strategy': 'value_investment'
                })

        return orders

    def before_trading_start(self, context: Dict[str, Any]) -> None:
        """在每个交易日开始前调用"""
        self.logger.debug(f"价值投资策略交易日开始: {context.get('current_date', 'Unknown')}")

    def handle_data(self, context: Dict[str, Any], data: pd.DataFrame) -> Dict[str, Any]:
        """处理每个bar的数据，生成交易信号"""
        # 价值投资策略通常不需要处理每个bar的数据
        # 只在调仓日期进行操作
        current_date = context.get('current_date', '')
        rebalance_dates = self.get_rebalance_dates('20240101', '20251231')

        if current_date in rebalance_dates:
            signals = self._generate_signals_impl(context, data)
            if not signals.empty:
                return {
                    'action': 'rebalance',
                    'selected_stocks': len(signals),
                    'avg_score': signals['value_score'].mean() if 'value_score' in signals.columns else 0
                }

        return {'action': 'hold'}

    def after_trading_end(self, context: Dict[str, Any]) -> None:
        """在每个交易日结束后调用"""
        self.logger.debug(f"价值投资策略交易日结束: {context.get('current_date', 'Unknown')}")

    def finalize(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """策略结束时调用"""
        return {
            'strategy_name': self.name,
            'parameters': self.get_parameters(),
            'strategy_type': 'value_investment'
        }

    def get_parameters(self) -> Dict[str, Any]:
        """获取策略参数"""
        return {
            'pe_max': self.pe_max,
            'pb_max': self.pb_max,
            'roe_min': self.roe_min,
            'debt_ratio_max': self.debt_ratio_max,
            'revenue_growth_min': self.revenue_growth_min,
            'profit_growth_min': self.profit_growth_min,
            'stock_num': self.stock_num,
            'rebalance_months': self.rebalance_months,
            'strategy_type': 'value_investment'
        }

    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """设置策略参数"""
        if 'pe_max' in parameters:
            self.pe_max = parameters['pe_max']
        if 'pb_max' in parameters:
            self.pb_max = parameters['pb_max']
        if 'roe_min' in parameters:
            self.roe_min = parameters['roe_min']
        if 'debt_ratio_max' in parameters:
            self.debt_ratio_max = parameters['debt_ratio_max']
        if 'revenue_growth_min' in parameters:
            self.revenue_growth_min = parameters['revenue_growth_min']
        if 'profit_growth_min' in parameters:
            self.profit_growth_min = parameters['profit_growth_min']
        if 'stock_num' in parameters:
            self.stock_num = parameters['stock_num']
        if 'rebalance_months' in parameters:
            self.rebalance_months = parameters['rebalance_months']

        # 更新参数字典（使用BaseStrategy的属性）
        self.parameters.update({
            'pe_max': self.pe_max,
            'pb_max': self.pb_max,
            'roe_min': self.roe_min,
            'debt_ratio_max': self.debt_ratio_max,
            'revenue_growth_min': self.revenue_growth_min,
            'profit_growth_min': self.profit_growth_min,
            'stock_num': self.stock_num,
            'rebalance_months': self.rebalance_months
        })

    def get_required_data(self) -> Dict[str, Any]:
        """获取策略所需的数据描述"""
        return {
            'financial_data': {
                'frequency': 'quarterly',
                'fields': ['revenue', 'n_income', 'total_assets', 'total_liab', 'n_cashflow_act'],
                'report_types': ['annual', 'quarterly']
            },
            'market_data': {
                'frequency': 'daily',
                'fields': ['total_mv', 'pe', 'pb'],
                'lookback_periods': 1
            }
        }

# 注册策略到工厂
from src.strategy.strategies.base_strategy import StrategyFactory
try:
    StrategyFactory.register('value_investment', ValueInvestmentStrategy)
except ValueError:
    # 策略已注册，忽略错误
    pass

# 示例用法
if __name__ == "__main__":
    strategy = ValueInvestmentStrategy()

    try:
        result = strategy.run()

        # 显示最新一期的选股结果
        if result['holdings_history']:
            latest_holdings = result['holdings_history'][-1]
            print(f"\n最新选股结果 ({latest_holdings['date']}):")
            for holding in latest_holdings['holdings'][:10]:  # 显示前10只
                print(f"  {holding['ts_code']}: 评分 {holding['value_score']:.2f}")

    except Exception as e:
        print(f"策略运行失败: {e}")
