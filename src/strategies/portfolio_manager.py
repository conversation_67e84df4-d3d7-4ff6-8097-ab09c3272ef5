#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
投资组合管理器
基于您的完美数据平台，实现多策略组合和风险管理
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False

class PortfolioManager:
    """
    投资组合管理器
    实现多策略组合、风险管理和资产配置
    """
    
    def __init__(self, initial_capital=1000000):
        self.db_path = 'output/data/db/sqlite/quantification.db'
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = {}
        self.portfolio_history = []
        self.risk_metrics = {}
    
    def get_quality_stocks(self, min_market_cap=100, limit=50):
        """
        获取高质量股票池
        
        参数:
            min_market_cap (float): 最小市值要求(亿元)
            limit (int): 股票数量限制
            
        返回:
            DataFrame: 高质量股票列表
        """
        conn = sqlite3.connect(self.db_path)
        
        sql = """
        WITH latest_financial AS (
            SELECT 
                i.ts_code,
                s.name,
                i.revenue,
                i.n_income,
                b.total_assets,
                b.total_liab,
                b.total_hldr_eqy_exc_min_int as equity,
                m.total_mv,
                ROW_NUMBER() OVER (PARTITION BY i.ts_code ORDER BY i.end_date DESC) as rn
            FROM income i
            LEFT JOIN balance b ON i.ts_code = b.ts_code AND i.end_date = b.end_date
            LEFT JOIN stock_list s ON i.ts_code = s.ts_code
            LEFT JOIN (
                SELECT ts_code, total_mv,
                ROW_NUMBER() OVER (PARTITION BY ts_code ORDER BY trade_date DESC) as mv_rn
                FROM market_cap WHERE total_mv IS NOT NULL
            ) m ON i.ts_code = m.ts_code AND m.mv_rn = 1
            WHERE i.revenue IS NOT NULL AND i.n_income IS NOT NULL
        )
        SELECT 
            ts_code, name, revenue, n_income, total_assets, 
            total_liab, equity, total_mv
        FROM latest_financial 
        WHERE rn = 1 
        AND total_mv >= ?
        AND revenue > 0 
        AND n_income > 0
        AND equity > 0
        ORDER BY total_mv DESC
        LIMIT ?
        """
        
        stocks = pd.read_sql(sql, conn, params=[min_market_cap * 10000, limit])
        conn.close()
        
        if stocks.empty:
            return stocks
        
        # 计算财务指标
        numeric_cols = ['revenue', 'n_income', 'total_assets', 'total_liab', 'equity', 'total_mv']
        for col in numeric_cols:
            stocks[col] = pd.to_numeric(stocks[col], errors='coerce')
        
        # 计算质量指标
        stocks['roe'] = stocks['n_income'] / stocks['equity']
        stocks['roa'] = stocks['n_income'] / stocks['total_assets']
        stocks['debt_ratio'] = stocks['total_liab'] / stocks['total_assets']
        stocks['market_cap_billion'] = stocks['total_mv'] / 10000
        
        # 质量筛选
        quality_filter = (
            (stocks['roe'] > 0.08) &  # ROE > 8%
            (stocks['roa'] > 0.03) &  # ROA > 3%
            (stocks['debt_ratio'] < 0.7) &  # 负债率 < 70%
            (stocks['market_cap_billion'] > min_market_cap)
        )
        
        return stocks[quality_filter].copy()
    
    def calculate_stock_momentum(self, ts_code, days=60):
        """
        计算股票动量指标
        
        参数:
            ts_code (str): 股票代码
            days (int): 计算天数
            
        返回:
            dict: 动量指标
        """
        conn = sqlite3.connect(self.db_path)
        
        sql = """
        SELECT trade_date, close, vol
        FROM daily 
        WHERE ts_code = ?
        ORDER BY trade_date DESC
        LIMIT ?
        """
        
        data = pd.read_sql(sql, conn, params=[ts_code, days])
        conn.close()
        
        if len(data) < 20:
            return None
        
        data = data.sort_values('trade_date').reset_index(drop=True)
        data['returns'] = data['close'].pct_change()
        
        # 计算动量指标
        momentum_20d = (data['close'].iloc[-1] / data['close'].iloc[-20] - 1) if len(data) >= 20 else 0
        momentum_5d = (data['close'].iloc[-1] / data['close'].iloc[-5] - 1) if len(data) >= 5 else 0
        
        # 计算波动率
        volatility = data['returns'].std() * np.sqrt(252)
        
        # 计算平均成交量
        avg_volume = data['vol'].mean()
        
        return {
            'momentum_20d': momentum_20d,
            'momentum_5d': momentum_5d,
            'volatility': volatility,
            'avg_volume': avg_volume
        }
    
    def create_multi_strategy_portfolio(self):
        """
        创建多策略投资组合
        
        返回:
            dict: 投资组合配置
        """
        print("📊 创建多策略投资组合...")
        
        # 获取高质量股票池
        quality_stocks = self.get_quality_stocks(min_market_cap=100, limit=100)
        
        if quality_stocks.empty:
            print("❌ 未找到符合条件的股票")
            return {}
        
        print(f"高质量股票池: {len(quality_stocks)}只")
        
        # 策略1: 价值策略 (40%资金)
        value_stocks = self.select_value_stocks(quality_stocks, allocation=0.4)
        
        # 策略2: 成长策略 (30%资金)
        growth_stocks = self.select_growth_stocks(quality_stocks, allocation=0.3)
        
        # 策略3: 动量策略 (20%资金)
        momentum_stocks = self.select_momentum_stocks(quality_stocks, allocation=0.2)
        
        # 策略4: 现金储备 (10%资金)
        cash_reserve = 0.1
        
        portfolio = {
            'value_strategy': value_stocks,
            'growth_strategy': growth_stocks,
            'momentum_strategy': momentum_stocks,
            'cash_reserve': cash_reserve,
            'total_stocks': len(set(
                list(value_stocks.keys()) + 
                list(growth_stocks.keys()) + 
                list(momentum_stocks.keys())
            ))
        }
        
        return portfolio
    
    def select_value_stocks(self, stocks, allocation=0.4, max_stocks=8):
        """
        选择价值股
        """
        # 价值评分：ROE高、负债率低、市值适中
        stocks['value_score'] = (
            stocks['roe'].rank(pct=True) * 0.4 +
            (1 - stocks['debt_ratio']).rank(pct=True) * 0.3 +
            (1 / stocks['market_cap_billion']).rank(pct=True) * 0.3
        )
        
        top_value = stocks.nlargest(max_stocks, 'value_score')
        
        # 等权重分配
        weight_per_stock = allocation / len(top_value)
        
        value_portfolio = {}
        for _, stock in top_value.iterrows():
            value_portfolio[stock['ts_code']] = {
                'name': stock['name'],
                'weight': weight_per_stock,
                'strategy': 'value',
                'score': stock['value_score'],
                'roe': stock['roe'],
                'market_cap': stock['market_cap_billion']
            }
        
        print(f"价值策略选股: {len(value_portfolio)}只")
        return value_portfolio
    
    def select_growth_stocks(self, stocks, allocation=0.3, max_stocks=6):
        """
        选择成长股
        """
        # 成长评分：ROE高、ROA高、市值中等
        stocks['growth_score'] = (
            stocks['roe'].rank(pct=True) * 0.5 +
            stocks['roa'].rank(pct=True) * 0.5
        )
        
        # 筛选ROE > 15%的股票
        growth_candidates = stocks[stocks['roe'] > 0.15]
        
        if growth_candidates.empty:
            growth_candidates = stocks.nlargest(max_stocks * 2, 'growth_score')
        
        top_growth = growth_candidates.nlargest(max_stocks, 'growth_score')
        
        weight_per_stock = allocation / len(top_growth)
        
        growth_portfolio = {}
        for _, stock in top_growth.iterrows():
            growth_portfolio[stock['ts_code']] = {
                'name': stock['name'],
                'weight': weight_per_stock,
                'strategy': 'growth',
                'score': stock['growth_score'],
                'roe': stock['roe'],
                'roa': stock['roa']
            }
        
        print(f"成长策略选股: {len(growth_portfolio)}只")
        return growth_portfolio
    
    def select_momentum_stocks(self, stocks, allocation=0.2, max_stocks=4):
        """
        选择动量股
        """
        momentum_data = []
        
        for _, stock in stocks.head(30).iterrows():  # 只测试前30只以节省时间
            momentum = self.calculate_stock_momentum(stock['ts_code'])
            if momentum:
                momentum_data.append({
                    'ts_code': stock['ts_code'],
                    'name': stock['name'],
                    'momentum_20d': momentum['momentum_20d'],
                    'volatility': momentum['volatility']
                })
        
        if not momentum_data:
            print("动量策略选股: 0只（数据不足）")
            return {}
        
        momentum_df = pd.DataFrame(momentum_data)
        
        # 动量评分：20日动量高、波动率适中
        momentum_df['momentum_score'] = (
            momentum_df['momentum_20d'].rank(pct=True) * 0.7 +
            (1 / momentum_df['volatility']).rank(pct=True) * 0.3
        )
        
        # 选择动量最强的股票
        top_momentum = momentum_df.nlargest(max_stocks, 'momentum_score')
        
        weight_per_stock = allocation / len(top_momentum)
        
        momentum_portfolio = {}
        for _, stock in top_momentum.iterrows():
            momentum_portfolio[stock['ts_code']] = {
                'name': stock['name'],
                'weight': weight_per_stock,
                'strategy': 'momentum',
                'score': stock['momentum_score'],
                'momentum_20d': stock['momentum_20d'],
                'volatility': stock['volatility']
            }
        
        print(f"动量策略选股: {len(momentum_portfolio)}只")
        return momentum_portfolio
    
    def calculate_portfolio_risk(self, portfolio):
        """
        计算投资组合风险指标
        """
        all_stocks = {}
        all_stocks.update(portfolio.get('value_strategy', {}))
        all_stocks.update(portfolio.get('growth_strategy', {}))
        all_stocks.update(portfolio.get('momentum_strategy', {}))
        
        if not all_stocks:
            return {}
        
        # 计算集中度风险
        weights = [stock['weight'] for stock in all_stocks.values()]
        concentration_risk = max(weights) if weights else 0
        
        # 计算策略分散度
        strategy_weights = {
            'value': sum([s['weight'] for s in portfolio.get('value_strategy', {}).values()]),
            'growth': sum([s['weight'] for s in portfolio.get('growth_strategy', {}).values()]),
            'momentum': sum([s['weight'] for s in portfolio.get('momentum_strategy', {}).values()]),
            'cash': portfolio.get('cash_reserve', 0)
        }
        
        return {
            'total_stocks': len(all_stocks),
            'max_single_weight': concentration_risk,
            'strategy_allocation': strategy_weights,
            'diversification_score': 1 - concentration_risk  # 简化的分散化评分
        }
    
    def generate_portfolio_report(self, portfolio):
        """
        生成投资组合报告
        """
        print("\n📋 投资组合配置报告")
        print("=" * 60)
        
        # 计算风险指标
        risk_metrics = self.calculate_portfolio_risk(portfolio)
        
        print(f"📊 组合概览:")
        print(f"总股票数: {risk_metrics.get('total_stocks', 0)}")
        print(f"最大单股权重: {risk_metrics.get('max_single_weight', 0):.1%}")
        print(f"分散化评分: {risk_metrics.get('diversification_score', 0):.2f}")
        print()
        
        print(f"🎯 策略配置:")
        strategy_allocation = risk_metrics.get('strategy_allocation', {})
        for strategy, weight in strategy_allocation.items():
            print(f"{strategy}: {weight:.1%}")
        print()
        
        # 详细持仓
        for strategy_name, strategy_stocks in portfolio.items():
            if strategy_name == 'cash_reserve' or strategy_name == 'total_stocks':
                continue
                
            print(f"📈 {strategy_name}:")
            if isinstance(strategy_stocks, dict):
                for ts_code, stock_info in strategy_stocks.items():
                    print(f"  {ts_code} {stock_info['name']}: {stock_info['weight']:.1%}")
            print()
        
        return risk_metrics
    
    def run_portfolio_management(self):
        """
        运行投资组合管理
        """
        print("🎯 多策略投资组合管理系统")
        print("基于您的完美数据平台")
        print("=" * 60)
        
        # 创建投资组合
        portfolio = self.create_multi_strategy_portfolio()
        
        if not portfolio:
            print("❌ 投资组合创建失败")
            return None
        
        # 生成报告
        risk_metrics = self.generate_portfolio_report(portfolio)
        
        print("💡 投资建议:")
        print("1. 定期重新平衡投资组合（建议每月）")
        print("2. 根据市场环境调整策略权重")
        print("3. 监控个股基本面变化")
        print("4. 控制单股权重不超过5%")
        
        return {
            'portfolio': portfolio,
            'risk_metrics': risk_metrics,
            'total_capital': self.initial_capital
        }

def main():
    """
    主函数
    """
    manager = PortfolioManager(initial_capital=1000000)
    result = manager.run_portfolio_management()

if __name__ == "__main__":
    main()
