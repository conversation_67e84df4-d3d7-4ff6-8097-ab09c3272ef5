#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多因子选股策略
基于多个因子的综合评分选择股票，进行定期调仓
"""

import pandas as pd
import numpy as np
from datetime import datetime
from src.strategy.strategies.base_strategy import BaseStrategy

class FactorStrategy(BaseStrategy):
    """
    多因子选股策略类
    结合多个因子进行选股和调仓
    """
    def __init__(self,
                 factors=['pe', 'pb', 'roe', 'growth'],
                 weights=[0.3, 0.3, 0.2, 0.2],
                 stock_num=10,
                 rebalance_days=20,
                 **kwargs):
        """
        初始化策略参数

        参数:
            factors (list): 使用的因子列表
            weights (list): 对应的因子权重
            stock_num (int): 持有的股票数量
            rebalance_days (int): 调仓周期（交易日）
            **kwargs: 其他参数
        """
        # 调用父类初始化
        super().__init__(name="多因子选股策略", **kwargs)

        self.factors = factors
        self.weights = weights
        self.stock_num = stock_num
        self.rebalance_days = rebalance_days

        # 验证参数
        if len(factors) != len(weights):
            raise ValueError("因子列表和权重列表长度必须相同")

        if abs(sum(weights) - 1.0) > 0.001:
            raise ValueError("因子权重之和必须为1")
    
    def calculate_factor_score(self, data, date):
        """
        计算指定日期的因子得分
        
        参数:
            data (DataFrame): 股票因子数据
            date (str): 日期
            
        返回:
            DataFrame: 股票的综合因子得分
        """
        # 筛选出特定日期的数据
        date_data = data[data['trade_date'] == date].copy()
        
        if date_data.empty:
            return pd.DataFrame()
        
        # 对每个因子进行标准化处理
        for factor in self.factors:
            if factor not in date_data.columns:
                raise ValueError(f"数据中不存在因子: {factor}")
            
            # 使用分位数标准化，避免极端值的影响
            date_data[f'{factor}_score'] = date_data[factor].rank(method='min', pct=True)
            
            # 有些因子需要反转（如PE、PB，数值越小越好）
            if factor in ['pe', 'pb']:
                date_data[f'{factor}_score'] = 1 - date_data[f'{factor}_score']
        
        # 计算综合得分
        date_data['total_score'] = 0
        for i, factor in enumerate(self.factors):
            date_data['total_score'] += date_data[f'{factor}_score'] * self.weights[i]
        
        # 按得分排序
        date_data = date_data.sort_values('total_score', ascending=False)
        
        return date_data
    
    def generate_signals(self, data):
        """
        生成交易信号
        
        参数:
            data (DataFrame): 包含日期、代码、收盘价和因子的数据框
            
        返回:
            DataFrame: 每个日期持有的股票列表
        """
        # 获取所有交易日
        all_dates = sorted(data['trade_date'].unique())
        
        # 创建一个字典来存储每个日期的持仓
        holdings = {}
        
        # 当前持仓
        current_stocks = []
        
        # 上次调仓日期的索引
        last_rebalance_idx = -1
        
        # 遍历每个交易日
        for i, date in enumerate(all_dates):
            # 判断是否需要调仓
            if i - last_rebalance_idx >= self.rebalance_days or last_rebalance_idx == -1:
                # 计算因子得分
                factor_data = self.calculate_factor_score(data, date)
                
                # 选择得分最高的N只股票
                if not factor_data.empty:
                    current_stocks = factor_data.head(self.stock_num)['ts_code'].tolist()
                    last_rebalance_idx = i
            
            # 记录当日持仓
            holdings[date] = current_stocks
        
        # 转换为DataFrame
        holdings_df = pd.DataFrame([(date, stock) for date, stocks in holdings.items() for stock in stocks],
                                  columns=['trade_date', 'ts_code'])
        
        return holdings_df
    
    def backtest(self, data, price_data, initial_capital=1000000.0):
        """
        回测策略
        
        参数:
            data (DataFrame): 因子数据
            price_data (DataFrame): 价格数据
            initial_capital (float): 初始资金
            
        返回:
            DataFrame: 包含仓位和资产价值的数据框
        """
        # 生成交易信号
        holdings_df = self.generate_signals(data)
        
        # 所有交易日
        all_dates = sorted(price_data['trade_date'].unique())
        
        # 创建回测结果DataFrame
        results = pd.DataFrame({'trade_date': all_dates})
        results = results.set_index('trade_date')
        
        # 初始化资金和持仓
        cash = initial_capital
        positions = {}
        
        # 上一个持仓日期
        last_holdings = []
        
        # 记录每日的资产总值
        portfolio_values = []
        
        # 遍历每个交易日
        for date in all_dates:
            # 当日持仓股票
            current_holdings = holdings_df[holdings_df['trade_date'] == date]['ts_code'].tolist()
            
            # 如果持仓发生变化，进行调仓
            if set(current_holdings) != set(last_holdings):
                # 卖出不在新持仓中的股票
                for stock in list(positions.keys()):
                    if stock not in current_holdings:
                        # 获取当日价格
                        price = price_data[(price_data['trade_date'] == date) & 
                                           (price_data['ts_code'] == stock)]['close'].values
                        
                        if len(price) > 0:
                            cash += positions[stock] * price[0]
                            del positions[stock]
                
                # 计算每只股票应分配的资金
                if current_holdings:
                    cash_per_stock = cash / len(current_holdings)
                    
                    # 买入新增的股票
                    for stock in current_holdings:
                        # 获取当日价格
                        price = price_data[(price_data['trade_date'] == date) & 
                                           (price_data['ts_code'] == stock)]['close'].values
                        
                        if len(price) > 0:
                            # 如果已持有该股票，则调整持仓
                            if stock in positions:
                                cash += positions[stock] * price[0] - cash_per_stock
                                positions[stock] = cash_per_stock / price[0]
                            else:
                                positions[stock] = cash_per_stock / price[0]
                                cash -= cash_per_stock
            
            # 计算当日资产总值
            portfolio_value = cash
            for stock, shares in positions.items():
                price = price_data[(price_data['trade_date'] == date) & 
                                  (price_data['ts_code'] == stock)]['close'].values
                
                if len(price) > 0:
                    portfolio_value += shares * price[0]
            
            # 记录当日资产总值
            portfolio_values.append(portfolio_value)
            
            # 更新上一个持仓日期
            last_holdings = current_holdings
        
        # 添加资产总值到结果中
        results['portfolio_value'] = portfolio_values
        
        # 计算日收益率
        results['daily_return'] = results['portfolio_value'].pct_change()
        
        return results
    
    def show_performance(self, results):
        """
        显示策略性能指标
        
        参数:
            results (DataFrame): 回测结果数据框
        """
        # 计算年化收益率
        total_days = len(results)
        annual_return = (results['portfolio_value'].iloc[-1] / results['portfolio_value'].iloc[0] - 1) * (252 / total_days)
        
        # 计算最大回撤
        results['cummax'] = results['portfolio_value'].cummax()
        results['drawdown'] = (results['cummax'] - results['portfolio_value']) / results['cummax']
        max_drawdown = results['drawdown'].max()
        
        # 计算夏普比率
        risk_free_rate = 0.03 / 252  # 假设无风险年化收益率为3%
        excess_return = results['daily_return'] - risk_free_rate
        sharpe_ratio = excess_return.mean() / results['daily_return'].std() * np.sqrt(252)
        
        # 打印结果
        print(f"策略性能指标:")
        print(f"总收益率: {results['portfolio_value'].iloc[-1] / results['portfolio_value'].iloc[0] - 1:.2%}")
        print(f"年化收益率: {annual_return:.2%}")
        print(f"最大回撤: {max_drawdown:.2%}")
        print(f"夏普比率: {sharpe_ratio:.2f}")
        
        return {
            'total_return': results['portfolio_value'].iloc[-1] / results['portfolio_value'].iloc[0] - 1,
            'annual_return': annual_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio
        }
    
    def run(self, factor_data, price_data, params=None, initial_capital=1000000.0):
        """
        运行策略
        
        参数:
            factor_data (DataFrame): 因子数据
            price_data (DataFrame): 价格数据
            params (dict): 策略参数字典
            initial_capital (float): 初始资金
            
        返回:
            dict: 回测结果和性能指标
        """
        # 更新策略参数
        if params:
            if 'factors' in params and 'weights' in params:
                self.factors = params['factors']
                self.weights = params['weights']
                
                if len(self.factors) != len(self.weights):
                    raise ValueError("因子列表和权重列表长度必须相同")
                
                if abs(sum(self.weights) - 1.0) > 0.001:
                    raise ValueError("因子权重之和必须为1")
            
            if 'stock_num' in params:
                self.stock_num = params['stock_num']
                
            if 'rebalance_days' in params:
                self.rebalance_days = params['rebalance_days']
        
        # 运行回测
        results = self.backtest(factor_data, price_data, initial_capital)
        
        # 计算性能指标
        performance = self.show_performance(results)
        
        return {
            'holdings': self.generate_signals(factor_data),
            'results': results,
            'performance': performance
        }

# 示例用法
if __name__ == "__main__":
    # 创建模拟数据
    print("创建模拟数据...")
    
    # 模拟交易日期和股票
    dates = pd.date_range('2020-01-01', periods=100, freq='D').strftime('%Y%m%d')
    stocks = [f'00000{i}.SZ' for i in range(1, 21)]
    
    # 创建因子数据
    rows = []
    for date in dates:
        for stock in stocks:
            # 随机生成因子值
            row = {
                'trade_date': date,
                'ts_code': stock,
                'pe': np.random.uniform(5, 30),
                'pb': np.random.uniform(0.5, 5),
                'roe': np.random.uniform(0.05, 0.3),
                'growth': np.random.uniform(-0.1, 0.5)
            }
            rows.append(row)
    
    factor_data = pd.DataFrame(rows)
    
    # 创建价格数据
    price_rows = []
    for date in dates:
        for stock in stocks:
            base_price = np.random.uniform(10, 100)
            price_row = {
                'trade_date': date,
                'ts_code': stock,
                'open': base_price * (1 + np.random.uniform(-0.02, 0.02)),
                'high': base_price * (1 + np.random.uniform(0, 0.05)),
                'low': base_price * (1 - np.random.uniform(0, 0.05)),
                'close': base_price * (1 + np.random.uniform(-0.02, 0.02)),
                'volume': np.random.uniform(1000000, 10000000)
            }
            price_rows.append(price_row)
    
    price_data = pd.DataFrame(price_rows)
    
    # 创建策略实例
    strategy = FactorStrategy(
        factors=['pe', 'pb', 'roe', 'growth'],
        weights=[0.3, 0.3, 0.2, 0.2],
        stock_num=5,
        rebalance_days=20
    )
    
    # 运行策略
    print("运行回测...")
    results = strategy.run(factor_data, price_data)
    
    # 打印最终结果
    print(f"\n回测完成，最终资产价值: {results['results']['portfolio_value'].iloc[-1]:.2f}")
    print(f"选出的股票示例 (最后一个交易日):")
    last_date = dates[-1]
    last_holdings = results['holdings'][results['holdings']['trade_date'] == last_date]
    print(last_holdings['ts_code'].tolist())
