#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版机器学习选股演示
基于您的完美数据平台，展示AI选股的核心功能
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, classification_report
import warnings
warnings.filterwarnings('ignore')

class SimpleMLDemo:
    """
    简化版机器学习选股演示
    """
    
    def __init__(self):
        self.db_path = 'output/data/db/sqlite/quantification.db'
        self.model = None
        self.scaler = StandardScaler()
        self.feature_names = []
    
    def get_stock_data_with_features(self, ts_code, days=120):
        """
        获取股票数据并计算特征
        """
        conn = sqlite3.connect(self.db_path)
        
        sql = """
        SELECT trade_date, open, high, low, close, vol, amount
        FROM daily 
        WHERE ts_code = ?
        ORDER BY trade_date DESC
        LIMIT ?
        """
        
        data = pd.read_sql(sql, conn, params=[ts_code, days])
        conn.close()
        
        if len(data) < 60:
            return None
        
        # 按日期排序
        data = data.sort_values('trade_date').reset_index(drop=True)
        
        # 转换数据类型
        for col in ['open', 'high', 'low', 'close', 'vol', 'amount']:
            data[col] = pd.to_numeric(data[col], errors='coerce')
        
        # 计算基础特征
        data['returns'] = data['close'].pct_change()
        data['high_low_ratio'] = data['high'] / data['low']
        data['volume_ratio'] = data['vol'] / data['vol'].rolling(20).mean()
        
        # 移动平均
        data['ma5'] = data['close'].rolling(5).mean()
        data['ma20'] = data['close'].rolling(20).mean()
        data['ma_ratio'] = data['ma5'] / data['ma20']
        
        # 波动率
        data['volatility'] = data['returns'].rolling(20).std()
        
        # RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        data['rsi'] = 100 - (100 / (1 + rs))
        
        # 价格动量
        data['momentum_5'] = data['close'] / data['close'].shift(5) - 1
        data['momentum_20'] = data['close'] / data['close'].shift(20) - 1
        
        return data
    
    def prepare_ml_dataset(self, stock_list, prediction_days=10):
        """
        准备机器学习数据集
        """
        print("📊 准备机器学习数据集...")
        
        all_features = []
        all_labels = []
        
        for i, stock in enumerate(stock_list):
            print(f"  处理股票 {i+1}/{len(stock_list)}: {stock}")
            
            try:
                data = self.get_stock_data_with_features(stock, days=100)
                
                if data is None or len(data) < 50:
                    continue
                
                # 计算未来收益标签
                data['future_return'] = data['close'].shift(-prediction_days) / data['close'] - 1
                data['label'] = (data['future_return'] > 0.05).astype(int)  # 5%收益阈值
                
                # 选择特征列
                feature_cols = ['returns', 'high_low_ratio', 'volume_ratio', 'ma_ratio', 
                              'volatility', 'rsi', 'momentum_5', 'momentum_20']
                
                # 提取有效数据
                valid_data = data.dropna()
                
                if len(valid_data) < 30:
                    continue
                
                # 去掉最后prediction_days行（没有标签）
                valid_data = valid_data.iloc[:-prediction_days]
                
                for _, row in valid_data.iterrows():
                    features = [row[col] for col in feature_cols]
                    
                    # 检查特征有效性
                    if not any(pd.isna(features)) and not any(np.isinf(features)):
                        all_features.append(features)
                        all_labels.append(row['label'])
                
                if not self.feature_names:
                    self.feature_names = feature_cols
                    
            except Exception as e:
                print(f"    处理失败: {e}")
                continue
        
        if not all_features:
            return None, None
        
        X = np.array(all_features)
        y = np.array(all_labels)
        
        print(f"数据集准备完成: {len(X)}个样本, {len(X[0])}个特征")
        print(f"正样本比例: {y.mean():.2%}")
        
        return X, y
    
    def train_model(self, X, y):
        """
        训练机器学习模型
        """
        print("🤖 训练随机森林模型...")
        
        # 数据标准化
        X_scaled = self.scaler.fit_transform(X)
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42
        )
        
        # 训练模型
        self.model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            class_weight='balanced'
        )
        
        self.model.fit(X_train, y_train)
        
        # 评估模型
        y_pred = self.model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"模型训练完成")
        print(f"测试集准确率: {accuracy:.3f}")
        
        # 特征重要性
        feature_importance = pd.DataFrame({
            'feature': self.feature_names,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print(f"\n特征重要性排序:")
        for _, row in feature_importance.iterrows():
            print(f"  {row['feature']}: {row['importance']:.3f}")
        
        return accuracy
    
    def predict_stocks(self, candidate_stocks):
        """
        预测候选股票
        """
        print(f"\n🎯 预测{len(candidate_stocks)}只候选股票...")
        
        predictions = []
        
        for stock in candidate_stocks:
            try:
                data = self.get_stock_data_with_features(stock, days=60)
                
                if data is None or len(data) < 30:
                    continue
                
                # 获取最新特征
                latest_data = data.iloc[-1]
                features = [latest_data[col] for col in self.feature_names]
                
                # 检查特征有效性
                if any(pd.isna(features)) or any(np.isinf(features)):
                    continue
                
                # 标准化并预测
                features_scaled = self.scaler.transform([features])
                probability = self.model.predict_proba(features_scaled)[0][1]
                prediction = self.model.predict(features_scaled)[0]
                
                predictions.append({
                    'ts_code': stock,
                    'probability': probability,
                    'prediction': prediction,
                    'current_price': latest_data['close']
                })
                
            except Exception as e:
                continue
        
        # 按概率排序
        predictions.sort(key=lambda x: x['probability'], reverse=True)
        
        return predictions
    
    def get_stock_fundamental_info(self, ts_code):
        """
        获取股票基本信息
        """
        conn = sqlite3.connect(self.db_path)
        
        # 获取股票名称
        name_sql = "SELECT name FROM stock_list WHERE ts_code = ?"
        name_result = pd.read_sql(name_sql, conn, params=[ts_code])
        name = name_result.iloc[0]['name'] if not name_result.empty else "未知"
        
        # 获取最新财务数据
        financial_sql = """
        SELECT revenue, n_income
        FROM income 
        WHERE ts_code = ?
        ORDER BY end_date DESC
        LIMIT 1
        """
        
        financial_result = pd.read_sql(financial_sql, conn, params=[ts_code])
        
        conn.close()
        
        info = {'name': name}
        if not financial_result.empty:
            row = financial_result.iloc[0]
            info['revenue'] = row['revenue'] if row['revenue'] else 0
            info['profit'] = row['n_income'] if row['n_income'] else 0
        else:
            info['revenue'] = 0
            info['profit'] = 0
        
        return info
    
    def run_ml_stock_selection(self):
        """
        运行机器学习选股系统
        """
        print("🧠 机器学习选股系统演示")
        print("基于您的完美数据平台")
        print("=" * 60)
        
        # 获取训练股票（成交额较大的股票）
        conn = sqlite3.connect(self.db_path)
        
        training_sql = """
        SELECT ts_code, AVG(amount) as avg_amount
        FROM daily 
        WHERE amount > 0
        GROUP BY ts_code
        ORDER BY avg_amount DESC
        LIMIT 15
        """
        
        training_stocks_df = pd.read_sql(training_sql, conn)
        training_stocks = training_stocks_df['ts_code'].tolist()
        
        # 获取候选股票
        candidate_sql = """
        SELECT ts_code, AVG(amount) as avg_amount
        FROM daily 
        WHERE amount > 0
        GROUP BY ts_code
        ORDER BY avg_amount DESC
        LIMIT 30
        """
        
        candidate_stocks_df = pd.read_sql(candidate_sql, conn)
        candidate_stocks = candidate_stocks_df['ts_code'].tolist()
        
        conn.close()
        
        print(f"训练股票池: {len(training_stocks)}只")
        print(f"候选股票池: {len(candidate_stocks)}只")
        
        # 准备训练数据
        X, y = self.prepare_ml_dataset(training_stocks)
        
        if X is None or len(X) < 50:
            print("❌ 训练数据不足")
            return
        
        # 训练模型
        accuracy = self.train_model(X, y)
        
        if accuracy < 0.5:
            print("⚠️ 模型准确率较低，结果仅供参考")
        
        # 预测候选股票
        predictions = self.predict_stocks(candidate_stocks)
        
        if not predictions:
            print("❌ 没有有效的预测结果")
            return
        
        # 显示结果
        print(f"\n📈 机器学习选股结果 (前10只):")
        print("=" * 80)
        print(f"{'排名':<4} {'股票代码':<12} {'股票名称':<12} {'上涨概率':<10} {'当前价格':<10} {'营收(万元)':<12}")
        print("-" * 80)
        
        for i, pred in enumerate(predictions[:10]):
            info = self.get_stock_fundamental_info(pred['ts_code'])
            revenue_wan = info['revenue'] / 10000 if info['revenue'] else 0
            
            print(f"{i+1:<4} {pred['ts_code']:<12} {info['name'][:8]:<12} "
                  f"{pred['probability']:.1%}{'':^6} {pred['current_price']:<10.2f} "
                  f"{revenue_wan:<12,.0f}")
        
        print("\n💡 投资建议:")
        high_prob_stocks = [p for p in predictions if p['probability'] > 0.6]
        if high_prob_stocks:
            print(f"1. 重点关注概率>60%的{len(high_prob_stocks)}只股票")
            print(f"2. 建议分散投资，单股权重不超过10%")
            print(f"3. 定期重新训练模型以适应市场变化")
        else:
            print("1. 当前市场环境下，建议谨慎投资")
            print("2. 可以考虑调整模型参数或增加训练数据")
        
        return predictions[:10]

def main():
    """
    主函数
    """
    ml_demo = SimpleMLDemo()
    results = ml_demo.run_ml_stock_selection()

if __name__ == "__main__":
    main()
