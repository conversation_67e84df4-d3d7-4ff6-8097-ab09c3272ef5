"""
简化版优化移动平均线策略
基于全A股回测结果进行参数优化，继承BaseStrategy

优化要点：
1. 调整移动平均线窗口：5/20 → 8/25
2. 增加信号确认机制：连续2天信号一致才执行
3. 添加成交量过滤：只在放量时执行交易
"""

import pandas as pd
import numpy as np
# {{ AURA-X: Add - 导入技术指标工具类，消除重复代码. Approval: 寸止(ID:重构阶段3). }}
from src.utils.technical_indicators import TechnicalIndicators
from src.strategies.base.moving_average_base import MovingAverageBase


class SimpleOptimizedMAStrategy(MovingAverageBase):
    """
    简化版优化移动平均线策略
    """
    
    def __init__(self, short_window=8, long_window=25, signal_confirm_days=2, volume_threshold=1.2, **kwargs):
        """
        初始化策略

        参数:
            short_window (int): 短期移动平均窗口，默认8天
            long_window (int): 长期移动平均窗口，默认25天
            signal_confirm_days (int): 信号确认天数，默认2天
            volume_threshold (float): 成交量阈值倍数，默认1.2倍
            **kwargs: 其他参数
        """
        # {{ AURA-X: Modify - 继承MovingAverageBase，启用优化功能. Approval: 寸止(ID:策略重复修复). }}
        # 调用父类初始化，启用成交量过滤和信号确认
        super().__init__(
            short_window=short_window,
            long_window=long_window,
            signal_confirm_days=signal_confirm_days,
            volume_threshold=volume_threshold,
            enable_volume_filter=True,
            enable_signal_confirmation=True,
            name=f"OptimizedMA_{short_window}_{long_window}",
            **kwargs
        )
    
    # {{ AURA-X: Remove - 删除重复的信号生成逻辑，使用基类实现. Approval: 寸止(ID:策略重复修复). }}
    # generate_signals方法已在基类中实现，包含成交量过滤和信号确认功能
    
    def backtest(self, data, initial_capital=1000000):
        """
        执行回测

        参数:
            data (DataFrame): 历史数据
            initial_capital (float): 初始资金

        返回:
            DataFrame: 回测结果
        """
        # {{ AURA-X: Modify - 使用基类的统一回测逻辑. Approval: 寸止(ID:策略重复修复). }}
        # 使用基类的回测方法
        return self.execute_backtest(data, initial_capital)


    
    def show_performance(self, positions):
        """
        显示策略性能指标
        
        参数:
            positions (DataFrame): 回测结果数据框
        """
        # 计算总收益率
        initial_value = positions['total'].iloc[0]
        final_value = positions['total'].iloc[-1]
        total_return = (final_value / initial_value) - 1
        
        # 计算年化收益率
        total_days = len(positions)
        annual_return = total_return * (252 / total_days)
        
        # 计算最大回撤
        positions['cummax'] = positions['total'].cummax()
        positions['drawdown'] = (positions['cummax'] - positions['total']) / positions['cummax']
        max_drawdown = positions['drawdown'].max()
        
        # 计算夏普比率（处理除零情况）
        returns_std = positions['returns'].std()
        if returns_std > 0:
            sharpe_ratio = positions['returns'].mean() / returns_std * np.sqrt(252)
        else:
            sharpe_ratio = 0.0
        
        # 计算交易次数（信号变化次数）
        signal_changes = (positions['signal'].diff() != 0).sum()
        
        # 计算胜率（盈利交易占比）
        trades = []
        entry_price = None
        for i, row in positions.iterrows():
            if row['signal'] == 1.0 and entry_price is None:
                entry_price = row['close']
            elif row['signal'] == 0.0 and entry_price is not None:
                exit_price = row['close']
                trade_return = (exit_price - entry_price) / entry_price
                trades.append(trade_return)
                entry_price = None
        
        win_rate = 0.0
        if trades:
            winning_trades = [t for t in trades if t > 0]
            win_rate = len(winning_trades) / len(trades)
        
        # 打印结果
        print(f"优化版策略性能指标:")
        print(f"总收益率: {total_return:.2%}")
        print(f"年化收益率: {annual_return:.2%}")
        print(f"最大回撤: {max_drawdown:.2%}")
        print(f"夏普比率: {sharpe_ratio:.2f}")
        print(f"交易次数: {signal_changes}")
        print(f"胜率: {win_rate:.2%}")
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'trades': signal_changes,
            'win_rate': win_rate
        }
    
    def run(self, data, initial_capital=1000000, start_date=None, end_date=None):
        """
        运行策略
        
        参数:
            data (DataFrame): 历史数据
            initial_capital (float): 初始资金
            start_date (str): 开始日期
            end_date (str): 结束日期
            
        返回:
            dict: 包含回测结果和性能指标的字典
        """
        # 数据预处理
        if start_date or end_date:
            if 'trade_date' in data.columns:
                if start_date:
                    data = data[data['trade_date'] >= start_date]
                if end_date:
                    data = data[data['trade_date'] <= end_date]
        
        # 执行回测
        positions = self.backtest(data, initial_capital)
        
        # 计算性能指标
        performance = self.show_performance(positions)
        
        return {
            'positions': positions,
            'performance': performance,
            'portfolio': {
                'initial_capital': initial_capital,
                'final_capital': positions['total'].iloc[-1]
            }
        }
