#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移工具
用于将数据从SQLite迁移到MySQL，提升大数据量处理性能
"""

import os
import sys
import time
import logging
from typing import Dict, List, Any, Optional
import pandas as pd
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

from src.data.storage.storage_factory import StorageFactory

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('database_migration.log')
    ]
)

logger = logging.getLogger(__name__)

class DatabaseMigrator:
    """数据库迁移器"""
    
    def __init__(self, mysql_config: Dict[str, Any] = None):
        """
        初始化迁移器
        
        参数:
            mysql_config: MySQL配置信息
        """
        # {{ AURA-X: Modify - 使用StorageFactory创建存储实例，遵循工厂模式. Approval: 寸止(ID:架构合规性修复). }}
        self.sqlite_adapter = StorageFactory.create('sqlite')

        # MySQL配置
        if mysql_config is None:
            mysql_config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': '',
                'database': 'quantification',
                'charset': 'utf8mb4'
            }

        self.mysql_adapter = StorageFactory.create('mysql', **mysql_config)
        self.migration_stats = {}
        
    def check_prerequisites(self) -> bool:
        """检查迁移前提条件"""
        logger.info("🔍 检查迁移前提条件...")
        
        try:
            # 检查SQLite连接
            if not self.sqlite_adapter.connect():
                logger.error("❌ SQLite连接失败")
                return False
            logger.info("✅ SQLite连接正常")
            
            # 检查MySQL连接
            if not self.mysql_adapter.connect():
                logger.error("❌ MySQL连接失败")
                return False
            logger.info("✅ MySQL连接正常")
            
            # 检查SQLite数据
            tables = self.sqlite_adapter.list_tables()
            if not tables:
                logger.warning("⚠️ SQLite中没有数据表")
                return False
            
            logger.info(f"✅ 发现 {len(tables)} 个数据表: {', '.join(tables)}")
            
            # 检查数据量
            total_rows = 0
            for table in tables:
                try:
                    count_query = f"SELECT COUNT(*) as count FROM {table}"
                    result = self.sqlite_adapter.query(count_query)
                    if not result.empty:
                        rows = result.iloc[0]['count']
                        total_rows += rows
                        logger.info(f"   {table}: {rows:,} 行")
                except Exception as e:
                    logger.warning(f"⚠️ 无法统计表 {table} 的行数: {e}")
            
            logger.info(f"✅ 总数据量: {total_rows:,} 行")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 前提条件检查失败: {e}")
            return False
    
    def get_table_schema(self, table_name: str) -> Dict[str, str]:
        """获取表结构并转换为MySQL兼容格式"""
        try:
            # 获取SQLite表结构
            pragma_query = f"PRAGMA table_info({table_name})"
            schema_info = self.sqlite_adapter.query(pragma_query)
            
            if schema_info.empty:
                return {}
            
            # 转换为MySQL兼容的数据类型
            type_mapping = {
                'INTEGER': 'BIGINT',
                'REAL': 'DOUBLE',
                'TEXT': 'TEXT',
                'BLOB': 'LONGBLOB',
                'NUMERIC': 'DECIMAL(20,8)',
                'FLOAT': 'DOUBLE',
                'VARCHAR': 'VARCHAR(255)',
                'DATE': 'DATE',
                'DATETIME': 'DATETIME',
                'TIMESTAMP': 'TIMESTAMP'
            }
            
            mysql_schema = {}
            primary_keys = []
            
            for _, row in schema_info.iterrows():
                column_name = row['name']
                sqlite_type = row['type'].upper()
                is_primary = row['pk'] == 1
                not_null = row['notnull'] == 1
                
                # 映射数据类型
                mysql_type = type_mapping.get(sqlite_type, 'TEXT')
                
                # 特殊处理
                if 'VARCHAR' in sqlite_type:
                    mysql_type = sqlite_type
                elif sqlite_type.startswith('DECIMAL'):
                    mysql_type = sqlite_type
                
                # 添加约束
                if not_null:
                    mysql_type += ' NOT NULL'
                
                if is_primary:
                    primary_keys.append(column_name)
                
                mysql_schema[column_name] = mysql_type
            
            # 添加主键约束
            if primary_keys:
                mysql_schema['_PRIMARY_KEY'] = f"PRIMARY KEY ({', '.join(primary_keys)})"
            
            return mysql_schema
            
        except Exception as e:
            logger.error(f"❌ 获取表结构失败 {table_name}: {e}")
            return {}
    
    def create_mysql_table(self, table_name: str, schema: Dict[str, str]) -> bool:
        """在MySQL中创建表"""
        try:
            # 检查表是否已存在
            if self.mysql_adapter.table_exists(table_name):
                logger.info(f"📋 MySQL表 {table_name} 已存在，跳过创建")
                return True
            
            # 创建表
            success = self.mysql_adapter.create_table(table_name, schema, if_not_exists=True)
            if success:
                logger.info(f"✅ MySQL表 {table_name} 创建成功")
            else:
                logger.error(f"❌ MySQL表 {table_name} 创建失败")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 创建MySQL表失败 {table_name}: {e}")
            return False
    
    def migrate_table_data(self, table_name: str, batch_size: int = 10000) -> bool:
        """迁移表数据"""
        logger.info(f"🚀 开始迁移表数据: {table_name}")
        
        try:
            # 获取总行数
            count_query = f"SELECT COUNT(*) as count FROM {table_name}"
            count_result = self.sqlite_adapter.query(count_query)
            total_rows = count_result.iloc[0]['count'] if not count_result.empty else 0
            
            if total_rows == 0:
                logger.info(f"⚠️ 表 {table_name} 为空，跳过迁移")
                return True
            
            logger.info(f"📊 表 {table_name} 总行数: {total_rows:,}")
            
            # 分批迁移数据
            migrated_rows = 0
            start_time = time.time()
            
            for offset in range(0, total_rows, batch_size):
                batch_start = time.time()
                
                # 从SQLite读取数据
                query = f"SELECT * FROM {table_name} LIMIT {batch_size} OFFSET {offset}"
                batch_data = self.sqlite_adapter.query(query)
                
                if batch_data.empty:
                    break
                
                # 写入MySQL
                self.mysql_adapter.save(batch_data, table_name, if_exists='append', index=False)
                
                migrated_rows += len(batch_data)
                batch_time = time.time() - batch_start
                
                # 计算进度和速度
                progress = (migrated_rows / total_rows) * 100
                speed = len(batch_data) / batch_time if batch_time > 0 else 0
                
                logger.info(f"📈 {table_name}: {migrated_rows:,}/{total_rows:,} ({progress:.1f}%) - {speed:.0f}行/秒")
            
            total_time = time.time() - start_time
            avg_speed = migrated_rows / total_time if total_time > 0 else 0
            
            # 验证迁移结果
            mysql_count_query = f"SELECT COUNT(*) as count FROM {table_name}"
            mysql_count_result = self.mysql_adapter.query(mysql_count_query)
            mysql_rows = mysql_count_result.iloc[0]['count'] if not mysql_count_result.empty else 0
            
            if mysql_rows == total_rows:
                logger.info(f"✅ 表 {table_name} 迁移成功: {migrated_rows:,}行, 耗时{total_time:.1f}秒, 平均速度{avg_speed:.0f}行/秒")
                self.migration_stats[table_name] = {
                    'rows': migrated_rows,
                    'time': total_time,
                    'speed': avg_speed,
                    'success': True
                }
                return True
            else:
                logger.error(f"❌ 表 {table_name} 数据验证失败: SQLite={total_rows}, MySQL={mysql_rows}")
                self.migration_stats[table_name] = {
                    'rows': migrated_rows,
                    'time': total_time,
                    'speed': avg_speed,
                    'success': False,
                    'error': f'数据不一致: SQLite={total_rows}, MySQL={mysql_rows}'
                }
                return False
                
        except Exception as e:
            logger.error(f"❌ 表 {table_name} 迁移失败: {e}")
            self.migration_stats[table_name] = {
                'rows': 0,
                'time': 0,
                'speed': 0,
                'success': False,
                'error': str(e)
            }
            return False
    
    def run_migration(self, tables: List[str] = None, batch_size: int = 10000) -> bool:
        """运行完整的数据库迁移"""
        logger.info("🚀 开始数据库迁移")
        
        # 检查前提条件
        if not self.check_prerequisites():
            logger.error("❌ 前提条件检查失败，迁移终止")
            return False
        
        # 获取要迁移的表
        if tables is None:
            tables = self.sqlite_adapter.list_tables()
        
        logger.info(f"📋 计划迁移 {len(tables)} 个表: {', '.join(tables)}")
        
        # 开始迁移
        migration_start = time.time()
        success_count = 0
        
        for table_name in tables:
            logger.info(f"🔄 处理表: {table_name}")
            
            try:
                # 获取表结构
                schema = self.get_table_schema(table_name)
                if not schema:
                    logger.error(f"❌ 无法获取表结构: {table_name}")
                    continue
                
                # 创建MySQL表
                if not self.create_mysql_table(table_name, schema):
                    logger.error(f"❌ 创建MySQL表失败: {table_name}")
                    continue
                
                # 迁移数据
                if self.migrate_table_data(table_name, batch_size):
                    success_count += 1
                
            except Exception as e:
                logger.error(f"❌ 处理表失败 {table_name}: {e}")
        
        # 生成迁移报告
        migration_time = time.time() - migration_start
        self.generate_migration_report(migration_time)
        
        success = success_count == len(tables)
        if success:
            logger.info(f"🎉 数据库迁移完成！成功迁移 {success_count}/{len(tables)} 个表")
        else:
            logger.error(f"❌ 数据库迁移部分失败：成功 {success_count}/{len(tables)} 个表")
        
        return success
    
    def generate_migration_report(self, total_time: float):
        """生成迁移报告"""
        logger.info("📋 生成迁移报告")
        
        print("\n" + "="*80)
        print("🗄️ 数据库迁移报告")
        print("="*80)
        
        total_rows = 0
        successful_tables = 0
        
        for table_name, stats in self.migration_stats.items():
            status = "✅ 成功" if stats['success'] else "❌ 失败"
            total_rows += stats['rows']
            if stats['success']:
                successful_tables += 1
            
            print(f"\n📋 表: {table_name}")
            print(f"   状态: {status}")
            print(f"   行数: {stats['rows']:,}")
            print(f"   耗时: {stats['time']:.1f}秒")
            print(f"   速度: {stats['speed']:.0f}行/秒")
            
            if not stats['success'] and 'error' in stats:
                print(f"   错误: {stats['error']}")
        
        avg_speed = total_rows / total_time if total_time > 0 else 0
        
        print(f"\n📊 总体统计:")
        print(f"   成功表数: {successful_tables}/{len(self.migration_stats)}")
        print(f"   总行数: {total_rows:,}")
        print(f"   总耗时: {total_time:.1f}秒")
        print(f"   平均速度: {avg_speed:.0f}行/秒")
        
        print("\n" + "="*80)

def main():
    """主函数"""
    print("🗄️ 数据库迁移工具")
    print("="*50)
    
    # MySQL配置
    mysql_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '',  # 请根据实际情况修改
        'database': 'quantification',
        'charset': 'utf8mb4'
    }
    
    migrator = DatabaseMigrator(mysql_config)
    
    try:
        # 运行迁移
        success = migrator.run_migration(batch_size=10000)
        
        if success:
            print("\n🎉 数据库迁移成功完成！")
            return 0
        else:
            print("\n❌ 数据库迁移失败！")
            return 1
            
    except Exception as e:
        logger.error(f"❌ 迁移过程中发生异常: {e}")
        print(f"\n❌ 迁移失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
