#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步数据获取器

{{ AURA-X: Add - 实现高性能异步数据获取器，集成异步TushareAdapter和异步批处理器. Source: 异步并发优化第一阶段 }}

集成所有异步组件，提供统一的高性能数据获取接口：
- 异步TushareAdapter
- 异步批处理器
- 异步自适应限流
- 性能监控和统计
"""

import asyncio
import time
import logging
import pandas as pd
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass

from src.data.sources.async_tushare_adapter import HighPerformanceAsyncTushareAdapter, AsyncBatchConfig
from src.data.fetcher.async_batch_processor import AsyncBatchProcessor, AsyncBatchResult
from src.data.storage.storage_interface import StorageInterface
from src.data.sources.data_source_interface import DataFetchError


@dataclass
class AsyncFetchConfig:
    """异步获取配置"""
    # TushareAdapter配置
    tushare_token: str
    tushare_timeout: float = 30.0
    tushare_max_retries: int = 3
    
    # 批处理配置
    batch_size: int = 50
    max_concurrent: int = 100
    
    # 性能监控
    enable_monitoring: bool = True
    log_interval: int = 100


class AsyncDataFetcher:
    """
    高性能异步数据获取器
    
    {{ AURA-X: Add - 集成异步组件的高性能数据获取器，预期提升3-5倍性能. Source: 异步并发优化第一阶段 }}
    """
    
    def __init__(
        self,
        config: AsyncFetchConfig,
        storage: Optional[StorageInterface] = None,
        logger: Optional[logging.Logger] = None
    ):
        """
        初始化异步数据获取器
        
        参数:
            config: 异步获取配置
            storage: 存储接口
            logger: 日志记录器
        """
        self.config = config
        self.storage = storage
        self.logger = logger or logging.getLogger(__name__)
        
        # 初始化异步TushareAdapter
        batch_config = AsyncBatchConfig(
            batch_size=config.batch_size,
            max_concurrent=config.max_concurrent,
            timeout=config.tushare_timeout,
            retry_count=config.tushare_max_retries
        )
        
        self.tushare_adapter = HighPerformanceAsyncTushareAdapter(
            token=config.tushare_token,
            batch_config=batch_config,
            timeout=config.tushare_timeout,
            max_retries=config.tushare_max_retries
        )
        
        # 性能统计
        self.stats = {
            'total_fetches': 0,
            'successful_fetches': 0,
            'failed_fetches': 0,
            'total_rows': 0,
            'total_time': 0.0,
            'avg_throughput': 0.0
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.tushare_adapter.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.tushare_adapter.disconnect()

    def __del__(self):
        """
        析构函数，确保异步资源正确清理
        """
        if hasattr(self, 'tushare_adapter') and self.tushare_adapter:
            # 尝试清理Tushare适配器
            try:
                if hasattr(self.tushare_adapter, 'session') and self.tushare_adapter.session and not self.tushare_adapter.session.closed:
                    # 尝试在当前事件循环中清理
                    try:
                        loop = asyncio.get_running_loop()
                        if loop.is_running():
                            loop.create_task(self._cleanup_resources())
                    except RuntimeError:
                        # 没有运行中的事件循环，创建新的循环来清理
                        try:
                            asyncio.run(self._cleanup_resources())
                        except Exception:
                            # 静默处理清理异常
                            pass
            except Exception:
                # 静默处理清理过程中的异常
                pass

    async def _cleanup_resources(self):
        """
        清理资源的内部方法
        """
        try:
            if self.tushare_adapter and hasattr(self.tushare_adapter, 'disconnect'):
                await self.tushare_adapter.disconnect()
        except Exception:
            # 静默处理清理过程中的异常
            pass
    
    async def fetch_market_data_async(
        self,
        symbols: List[str],
        data_type: str = 'daily',
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        fields: Optional[str] = None,
        save_to_storage: bool = True,
        **kwargs
    ) -> pd.DataFrame:
        """
        异步获取市场数据
        
        参数:
            symbols: 股票代码列表
            data_type: 数据类型
            start_date: 开始日期
            end_date: 结束日期
            fields: 字段列表
            save_to_storage: 是否保存到存储
            **kwargs: 其他参数
            
        返回:
            pd.DataFrame: 市场数据
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"🚀 开始异步获取市场数据: {len(symbols)}只股票, 类型={data_type}")
            
            # 使用异步TushareAdapter获取数据
            df = await self.tushare_adapter.get_market_data_async(
                symbols=symbols,
                data_type=data_type,
                start_date=start_date,
                end_date=end_date,
                fields=fields,
                **kwargs
            )
            
            # 保存到存储
            if save_to_storage and self.storage and not df.empty:
                await self._save_to_storage_async(df, data_type)
            
            # 更新统计信息
            fetch_time = time.time() - start_time
            self._update_stats(True, len(df), fetch_time)
            
            # 计算吞吐量
            throughput = len(df) / fetch_time if fetch_time > 0 else 0
            
            self.logger.info(f"✅ 异步市场数据获取完成: {len(df)}行, 耗时{fetch_time:.2f}s, 吞吐量{throughput:.1f}行/秒")
            
            return df
            
        except Exception as e:
            fetch_time = time.time() - start_time
            self._update_stats(False, 0, fetch_time)
            
            self.logger.error(f"❌ 异步市场数据获取失败: {e}")
            raise DataFetchError(f"异步获取市场数据失败: {e}")
    
    async def fetch_financial_data_async(
        self,
        symbols: List[str],
        report_type: str = 'income',
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        fields: Optional[str] = None,
        save_to_storage: bool = True,
        **kwargs
    ) -> pd.DataFrame:
        """
        异步获取财务数据
        
        参数:
            symbols: 股票代码列表
            report_type: 报表类型
            start_date: 开始日期
            end_date: 结束日期
            fields: 字段列表
            save_to_storage: 是否保存到存储
            **kwargs: 其他参数
            
        返回:
            pd.DataFrame: 财务数据
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"💰 开始异步获取财务数据: {len(symbols)}只股票, 类型={report_type}")
            
            # 使用异步TushareAdapter获取数据
            df = await self.tushare_adapter.get_financial_data_async(
                symbols=symbols,
                report_type=report_type,
                start_date=start_date,
                end_date=end_date,
                fields=fields,
                **kwargs
            )
            
            # 保存到存储
            if save_to_storage and self.storage and not df.empty:
                await self._save_financial_data_to_storage_async(df, report_type)
            
            # 更新统计信息
            fetch_time = time.time() - start_time
            self._update_stats(True, len(df), fetch_time)
            
            # 计算吞吐量
            throughput = len(df) / fetch_time if fetch_time > 0 else 0
            
            self.logger.info(f"✅ 异步财务数据获取完成: {len(df)}行, 耗时{fetch_time:.2f}s, 吞吐量{throughput:.1f}行/秒")
            
            return df
            
        except Exception as e:
            fetch_time = time.time() - start_time
            self._update_stats(False, 0, fetch_time)
            
            self.logger.error(f"❌ 异步财务数据获取失败: {e}")
            raise DataFetchError(f"异步获取财务数据失败: {e}")
    
    async def _save_to_storage_async(self, df: pd.DataFrame, data_type: str):
        """
        异步保存数据到存储

        参数:
            df: 要保存的数据
            data_type: 数据类型
        """
        try:
            # 如果存储接口支持异步
            if hasattr(self.storage, 'save_async'):
                await self.storage.save_async(df, data_type)
            else:
                # 在线程池中运行同步保存
                loop = asyncio.get_event_loop()
                # 使用专门的市场数据保存方法
                if hasattr(self.storage, 'save_market_data'):
                    await loop.run_in_executor(None, self.storage.save_market_data, df, data_type)
                else:
                    # 回退到通用保存方法，使用表名作为第一个参数
                    await loop.run_in_executor(None, self.storage.save, data_type, df)

            self.logger.debug(f"💾 数据已保存到存储: {len(df)}行, 类型={data_type}")

        except Exception as e:
            self.logger.error(f"❌ 保存数据到存储失败: {e}")
            # 不重新抛出异常，避免影响主要的数据获取流程
            pass

    async def _save_financial_data_to_storage_async(self, df: pd.DataFrame, report_type: str):
        """
        异步保存财务数据到存储

        参数:
            df: 要保存的财务数据
            report_type: 报表类型
        """
        try:
            # 如果存储接口支持异步
            if hasattr(self.storage, 'save_async'):
                await self.storage.save_async(df, report_type)
            else:
                # 在线程池中运行同步保存
                loop = asyncio.get_event_loop()
                # 使用专门的财务数据保存方法
                if hasattr(self.storage, 'save_financial_data'):
                    await loop.run_in_executor(None, self.storage.save_financial_data, df, report_type)
                else:
                    # 回退到通用保存方法，使用表名作为第一个参数
                    await loop.run_in_executor(None, self.storage.save, report_type, df)

            self.logger.debug(f"💾 财务数据已保存到存储: {len(df)}行, 类型={report_type}")

        except Exception as e:
            self.logger.error(f"❌ 保存财务数据到存储失败: {e}")
            # 不重新抛出异常，避免影响主要的数据获取流程
            pass
    
    def _update_stats(self, success: bool, rows: int, time_taken: float):
        """
        更新统计信息
        
        参数:
            success: 是否成功
            rows: 数据行数
            time_taken: 耗时
        """
        self.stats['total_fetches'] += 1
        if success:
            self.stats['successful_fetches'] += 1
            self.stats['total_rows'] += rows
        else:
            self.stats['failed_fetches'] += 1
        
        self.stats['total_time'] += time_taken
        
        # 计算平均吞吐量
        if self.stats['total_time'] > 0:
            self.stats['avg_throughput'] = self.stats['total_rows'] / self.stats['total_time']
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        返回:
            Dict[str, Any]: 性能统计数据
        """
        total_fetches = self.stats['total_fetches']
        
        stats = dict(self.stats)
        
        if total_fetches > 0:
            stats['success_rate'] = (self.stats['successful_fetches'] / total_fetches) * 100
            stats['failure_rate'] = (self.stats['failed_fetches'] / total_fetches) * 100
            stats['avg_fetch_time'] = self.stats['total_time'] / total_fetches
        else:
            stats['success_rate'] = 0.0
            stats['failure_rate'] = 0.0
            stats['avg_fetch_time'] = 0.0
        
        # 添加TushareAdapter统计
        stats['tushare_stats'] = self.tushare_adapter.get_performance_stats()
        
        return stats
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_fetches': 0,
            'successful_fetches': 0,
            'failed_fetches': 0,
            'total_rows': 0,
            'total_time': 0.0,
            'avg_throughput': 0.0
        }
        
        # 重置TushareAdapter统计
        self.tushare_adapter.reset_stats()
    
    # 同步接口包装器
    
    def fetch_market_data(
        self,
        symbols: List[str],
        data_type: str = 'daily',
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        fields: Optional[str] = None,
        save_to_storage: bool = True,
        **kwargs
    ) -> pd.DataFrame:
        """
        同步接口：获取市场数据
        
        内部调用异步方法，提供同步接口兼容性
        """
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                self.fetch_market_data_async(
                    symbols=symbols,
                    data_type=data_type,
                    start_date=start_date,
                    end_date=end_date,
                    fields=fields,
                    save_to_storage=save_to_storage,
                    **kwargs
                )
            )
        finally:
            loop.close()
    
    def fetch_financial_data(
        self,
        symbols: List[str],
        report_type: str = 'income',
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        fields: Optional[str] = None,
        save_to_storage: bool = True,
        **kwargs
    ) -> pd.DataFrame:
        """
        同步接口：获取财务数据
        
        内部调用异步方法，提供同步接口兼容性
        """
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                self.fetch_financial_data_async(
                    symbols=symbols,
                    report_type=report_type,
                    start_date=start_date,
                    end_date=end_date,
                    fields=fields,
                    save_to_storage=save_to_storage,
                    **kwargs
                )
            )
        finally:
            loop.close()
