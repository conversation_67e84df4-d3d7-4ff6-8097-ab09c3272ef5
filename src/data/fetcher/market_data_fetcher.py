"""
统一数据获取器
提供获取市场数据的统一接口，包括：
- 日线、分钟线等K线数据
- 交易日历
- 股票基本信息
- 行业分类
"""

import pandas as pd
import datetime
import logging
from typing import List, Dict, Any, Optional, Union, Tuple

from src.data.sources.data_source_interface import DataSourceInterface
from src.data.sources.data_source_factory import DataSourceFactory
from src.data.fetcher.data_fetcher import DataFetcher

class MarketData(DataFetcher):
    """
    市场数据获取类
    
    提供统一的接口获取各种市场数据，支持多种数据源
    """
    
    def __init__(self, data_source=None, use_cache=True, cache=None, cache_config=None):
        super().__init__(data_source=data_source, use_cache=use_cache, cache=cache, cache_config=cache_config)
        self.logger = logging.getLogger(__name__)
        if self.data_source is None:
            try:
                self.data_source = DataSourceFactory.create_tushare()
            except Exception as e:
                self.logger.error(f"创建默认数据源失败: {e}")
                raise ValueError(f"无法创建默认数据源: {e}")
        try:
            if hasattr(self.data_source, 'connect'):
                self.data_source.connect()
        except Exception as e:
            self.logger.error(f"连接数据源失败: {e}")
            raise ConnectionError(f"连接数据源失败: {e}")
    
    def get_price(self, symbol: Union[str, List[str]], 
                 start_date: Union[str, datetime.datetime],
                 end_date: Union[str, datetime.datetime],
                 fields: Optional[List[str]] = None,
                 frequency: str = 'daily',
                 adjust_flag: str = 'qfq') -> pd.DataFrame:
        """
        获取股票价格数据
        
        Args:
            symbol: 股票代码或代码列表
            start_date: 开始日期
            end_date: 结束日期
            fields: 需要的字段列表，默认为None表示全部字段
            frequency: 数据频率，daily=日线，min=分钟线
            adjust_flag: 复权类型，qfq=前复权，hfq=后复权，None=不复权
            
        Returns:
            pd.DataFrame: 价格数据
        """
        # 转换日期格式
        start_date_str = self._format_date(start_date)
        end_date_str = self._format_date(end_date)
        
        # 确保symbol是列表
        symbols = [symbol] if isinstance(symbol, str) else symbol
        
        result_df = pd.DataFrame()
        
        for sym in symbols:
            try:
                # 根据频率调用不同的接口
                if frequency == 'daily':
                    df = self.data_source.get_daily_bars(
                        ts_code=sym,
                        start_date=start_date_str,
                        end_date=end_date_str,
                        adjust=adjust_flag
                    )
                elif frequency.startswith('min'):
                    # 分钟线数据
                    df = self.data_source.get_minute_bars(
                        ts_code=sym,
                        start_date=start_date_str,
                        end_date=end_date_str,
                        freq=frequency
                    )
                else:
                    raise ValueError(f"不支持的数据频率: {frequency}")
                
                # 添加到结果集
                if not df.empty:
                    result_df = pd.concat([result_df, df], ignore_index=True)
                    
            except Exception as e:
                self.logger.error(f"获取 {sym} 价格数据失败: {e}")
                # 继续处理其他股票
        
        # 筛选字段
        if fields and not result_df.empty:
            available_fields = [f for f in fields if f in result_df.columns]
            if available_fields:
                result_df = result_df[['ts_code', 'trade_date'] + available_fields]
        
        return result_df
    
    def get_trading_calendar(self, start_date: Union[str, datetime.datetime],
                            end_date: Union[str, datetime.datetime]) -> List[datetime.datetime]:
        """
        获取交易日历
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[datetime.datetime]: 交易日列表
        """
        # 转换日期格式
        start_date_str = self._format_date(start_date)
        end_date_str = self._format_date(end_date)
        
        try:
            # 获取交易日历
            calendar_df = self.data_source.get_trade_calendar(
                start_date=start_date_str,
                end_date=end_date_str,
                is_open=1  # 只获取交易日
            )
            
            if not calendar_df.empty and 'cal_date' in calendar_df.columns:
                # 转换为datetime格式
                dates = pd.to_datetime(calendar_df['cal_date'])
                return dates.tolist()
            else:
                return []
                
        except Exception as e:
            self.logger.error(f"获取交易日历失败: {e}")
            # 返回空列表作为退路
            return []
    
    def get_stock_list(self, market: str = '') -> pd.DataFrame:
        """
        获取股票列表
        
        Args:
            market: 市场，如'主板'、'中小板'、'创业板'等，为空表示全部
            
        Returns:
            pd.DataFrame: 股票列表
        """
        try:
            stock_list = self.data_source.get_stock_basic(market=market)
            return stock_list
        except Exception as e:
            self.logger.error(f"获取股票列表失败: {e}")
            return pd.DataFrame()
    
    def get_index_constituents(self, index_code: str, 
                              date: Optional[Union[str, datetime.datetime]] = None) -> pd.DataFrame:
        """
        获取指数成分股
        
        Args:
            index_code: 指数代码，如'000300.SH'
            date: 查询日期，默认为最新
            
        Returns:
            pd.DataFrame: 成分股列表
        """
        date_str = self._format_date(date) if date else None
        
        try:
            constituents = self.data_source.get_index_weight(
                index_code=index_code,
                trade_date=date_str
            )
            return constituents
        except Exception as e:
            self.logger.error(f"获取指数 {index_code} 成分股失败: {e}")
            return pd.DataFrame()
    
    def get_industry_stocks(self, industry: str) -> pd.DataFrame:
        """
        获取行业股票列表
        
        Args:
            industry: 行业代码或名称
            
        Returns:
            pd.DataFrame: 行业股票列表
        """
        try:
            stocks = self.data_source.get_industry_classified(industry=industry)
            return stocks
        except Exception as e:
            self.logger.error(f"获取行业 {industry} 股票列表失败: {e}")
            return pd.DataFrame()
    
    def _format_date(self, date: Union[str, datetime.datetime]) -> str:
        """
        格式化日期为字符串
        
        Args:
            date: 日期对象或字符串
            
        Returns:
            str: 格式化后的日期字符串 (YYYYMMDD)
        """
        if isinstance(date, str):
            # 尝试解析日期字符串并转换格式
            try:
                date_obj = pd.to_datetime(date)
                return date_obj.strftime('%Y%m%d')
            except:
                # 如果已经是YYYYMMDD格式，直接返回
                if len(date) == 8 and date.isdigit():
                    return date
                raise ValueError(f"无效的日期格式: {date}")
        elif isinstance(date, datetime.datetime):
            return date.strftime('%Y%m%d')
        else:
            raise TypeError(f"日期必须是字符串或datetime对象，而不是 {type(date)}")
    
    def close(self):
        """关闭数据源连接"""
        try:
            self.data_source.disconnect()
        except Exception as e:
            self.logger.error(f"关闭数据源连接失败: {e}")
    
    def __del__(self):
        """析构函数，确保资源释放"""
        try:
            self.close()
        except:
            pass

