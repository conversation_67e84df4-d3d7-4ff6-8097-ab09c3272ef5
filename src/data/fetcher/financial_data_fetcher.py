"""
财务数据获取器
提供获取股票财务数据的统一接口，包括：
- 基本财务数据（资产负债表、利润表、现金流量表）
- 财务指标（ROE、ROA、毛利率等）
- 股东结构
- 分红信息
"""

import pandas as pd
import datetime
import logging
from typing import List, Dict, Optional, Union
from concurrent.futures import ThreadPoolExecutor

from src.data.sources.data_source_interface import DataSourceInterface
from src.data.sources.data_source_factory import DataSourceFactory
from src.data.storage.storage_factory import StorageFactory
from src.data.fetcher.data_fetcher import DataFetcher


# API性能优化配置 - 高级并发优化版本
API_PERFORMANCE_CONFIG = {
    # 基于excellent等级稳定运行的高级并发参数
    "income_statement": {
        "batch_size": 75,  # 从50提升到75（50%提升）
        "workers": 15,     # 从10提升到15（50%提升）
        "retry_count": 5,  # 保持重试次数
        "retry_interval": 5 # 保持重试间隔
    },
    "balance_sheet": {
        "batch_size": 75,  # 从50提升到75
        "workers": 15,     # 从10提升到15
        "retry_count": 5,
        "retry_interval": 5
    },
    "cash_flow": {
        "batch_size": 75,  # 从50提升到75
        "workers": 15,     # 从10提升到15
        "retry_count": 5,
        "retry_interval": 5
    },
    "financial_indicator": {
        "batch_size": 150, # 从100提升到150（50%提升）
        "workers": 22,     # 从15提升到22（约50%提升）
        "retry_count": 5,
        "retry_interval": 5
    },
    "express_report": {
        "batch_size": 75,  # 从50提升到75
        "workers": 15,     # 从10提升到15
        "retry_count": 5,
        "retry_interval": 5
    },
    # 默认配置 - 高级并发优化
    "default": {
        "batch_size": 75,  # 从50提升到75
        "workers": 15,     # 从10提升到15
        "retry_count": 5,
        "retry_interval": 5
    }
}


class FinancialData(DataFetcher):
    """
    财务数据获取类
    提供统一接口获取各种财务数据，支持多种数据源
    """
    
    def __init__(self, data_source=None, use_cache=True, cache=None, cache_config=None, performance_config=None):
        super().__init__(data_source=data_source, use_cache=use_cache, cache=cache, cache_config=cache_config)
        self.logger = logging.getLogger(__name__)
        self.performance_config = performance_config or API_PERFORMANCE_CONFIG
        # 数据源初始化逻辑保留
        if self.data_source is None:
            try:
                self.data_source = DataSourceFactory.create_tushare()
            except Exception as e:
                self.logger.error(f"创建默认数据源失败: {e}")
                raise ValueError(f"无法创建默认数据源: {e}")
        try:
            if hasattr(self.data_source, 'connect'):
                self.data_source.connect()
        except Exception as e:
            self.logger.error(f"连接数据源失败: {e}")
            raise ConnectionError(f"连接数据源失败: {e}")
    
    def _format_date(self, date: Union[str, datetime.datetime, datetime.date]) -> str:
        """
        标准化日期格式为YYYYMMDD
        
        参数:
            date: 日期对象
            
        返回:
            str: 格式化的日期字符串
        """
        if isinstance(date, str):
            # 移除分隔符
            date_str = date.replace('-', '').replace('/', '').replace('.', '')
            if len(date_str) == 8 and date_str.isdigit():
                return date_str
            
        if isinstance(date, (datetime.datetime, datetime.date)):
            return date.strftime('%Y%m%d')
            
        # 尝试解析日期字符串
        try:
            parsed_date = pd.to_datetime(date)
            return parsed_date.strftime('%Y%m%d')
        except:
            raise ValueError(f"无法解析日期格式: {date}")
    
    def _get_cache_key(self, func_name: str, *args, **kwargs) -> str:
        """
        生成缓存键
        
        参数:
            func_name: 函数名称
            *args, **kwargs: 函数参数
            
        返回:
            str: 缓存键
        """
        # 将参数转换为字符串
        args_str = '_'.join(str(arg) for arg in args)
        kwargs_str = '_'.join(f"{k}_{v}" for k, v in sorted(kwargs.items()))
        
        # 组合成缓存键
        return f"financial_{func_name}_{args_str}_{kwargs_str}"
    
    def _cached_call(self, func_name: str, call_func, *args, **kwargs):
        """
        带缓存的函数调用
        
        参数:
            func_name: 函数名称
            call_func: 要调用的函数
            *args, **kwargs: 函数参数
            
        返回:
            函数返回值
        """
        if not self.use_cache:
            return call_func(*args, **kwargs)
        
        # 生成缓存键
        cache_key = self._get_cache_key(func_name, *args, **kwargs)
        
        # 尝试从缓存获取
        cached_result = self.cache.get(cache_key)
        if cached_result is not None:
            self.logger.debug(f"从缓存获取 {func_name} 数据")
            return cached_result
        
        # 调用函数获取结果
        result = call_func(*args, **kwargs)
        
        # 保存到缓存
        if result is not None:
            self.cache.set(cache_key, result)
        
        return result
    
    def _get_api_config(self, api_name: str) -> dict:
        """
        获取指定API的性能配置
        
        参数:
            api_name: API名称
            
        返回:
            dict: 性能配置
        """
        return self.performance_config.get(api_name, self.performance_config['default'])
    
    def get_income_statement(self, symbol: str, period: str = 'annual',
                          start_date: Optional[Union[str, datetime.datetime]] = None,
                          end_date: Optional[Union[str, datetime.datetime]] = None,
                          report_type: str = 'report') -> pd.DataFrame:
        """
        获取利润表
        
        参数:
            symbol: 股票代码
            period: 报告期类型，'annual'=年报, 'quarterly'=季报
            start_date: 开始日期
            end_date: 结束日期
            report_type: 报告类型，'report'=原始报表, 'single'=单季报表
            
        返回:
            pd.DataFrame: 利润表数据
        """
        # 标准化日期
        if start_date:
            start_date = self._format_date(start_date)
        if end_date:
            end_date = self._format_date(end_date)
            
        def _get_income_statement():
            try:
                # 使用针对income_statement优化的配置
                config = self._get_api_config('income_statement')
                
                # 直接使用get_financial_data而不是单独的get_income_statement
                return self.data_source.get_financial_data(
                    api_name='income',
                    ts_code=symbol,
                    period=period,
                    start_date=start_date,
                    end_date=end_date,
                    report_type=report_type
                )
            except Exception as e:
                self.logger.error(f"获取 {symbol} 利润表失败: {e}")
                return pd.DataFrame()
        
        return self._cached_call('income_statement', _get_income_statement)
    
    def get_balance_sheet(self, symbol: str, period: str = 'annual',
                       start_date: Optional[Union[str, datetime.datetime]] = None,
                       end_date: Optional[Union[str, datetime.datetime]] = None,
                       report_type: str = 'report') -> pd.DataFrame:
        """
        获取资产负债表
        
        参数:
            symbol: 股票代码
            period: 报告期类型，'annual'=年报, 'quarterly'=季报
            start_date: 开始日期
            end_date: 结束日期
            report_type: 报告类型，'report'=原始报表, 'single'=单季报表
            
        返回:
            pd.DataFrame: 资产负债表数据
        """
        # 标准化日期
        if start_date:
            start_date = self._format_date(start_date)
        if end_date:
            end_date = self._format_date(end_date)
            
        def _get_balance_sheet():
            try:
                # 使用针对balance_sheet优化的配置
                config = self._get_api_config('balance_sheet')
                
                # 直接使用get_financial_data而不是单独的get_balance_sheet
                return self.data_source.get_financial_data(
                    api_name='balancesheet',
                    ts_code=symbol,
                    period=period,
                    start_date=start_date,
                    end_date=end_date,
                    report_type=report_type
                )
            except Exception as e:
                self.logger.error(f"获取 {symbol} 资产负债表失败: {e}")
                return pd.DataFrame()
        
        return self._cached_call('balance_sheet', _get_balance_sheet)
    
    def get_cash_flow(self, symbol: str, period: str = 'annual',
                   start_date: Optional[Union[str, datetime.datetime]] = None,
                   end_date: Optional[Union[str, datetime.datetime]] = None,
                   report_type: str = 'report') -> pd.DataFrame:
        """
        获取现金流量表
        
        参数:
            symbol: 股票代码
            period: 报告期类型，'annual'=年报, 'quarterly'=季报
            start_date: 开始日期
            end_date: 结束日期
            report_type: 报告类型，'report'=原始报表, 'single'=单季报表
            
        返回:
            pd.DataFrame: 现金流量表数据
        """
        # 标准化日期
        if start_date:
            start_date = self._format_date(start_date)
        if end_date:
            end_date = self._format_date(end_date)
            
        def _get_cash_flow():
            try:
                # 使用针对cash_flow优化的配置
                config = self._get_api_config('cash_flow')
                
                # 直接使用get_financial_data而不是单独的get_cash_flow
                return self.data_source.get_financial_data(
                    api_name='cashflow',
                    ts_code=symbol,
                    period=period,
                    start_date=start_date,
                    end_date=end_date,
                    report_type=report_type
                )
            except Exception as e:
                self.logger.error(f"获取 {symbol} 现金流量表失败: {e}")
                return pd.DataFrame()
        
        return self._cached_call('cash_flow', _get_cash_flow)
    
    def get_financial_indicator(self, symbol: str, period: str = 'annual',
                             start_date: Optional[Union[str, datetime.datetime]] = None,
                             end_date: Optional[Union[str, datetime.datetime]] = None,
                             **kwargs) -> pd.DataFrame:
        """
        获取财务指标数据
        
        参数:
            symbol: 股票代码
            period: 报告期类型，'annual'=年报, 'quarterly'=季报
            start_date: 开始日期
            end_date: 结束日期
            **kwargs: 其他参数
            
        返回:
            pd.DataFrame: 财务指标数据
        """
        if kwargs:
            raise TypeError(f"get_financial_indicator() 不支持参数: {', '.join(kwargs.keys())}")
        
        # 标准化日期
        if start_date:
            start_date = self._format_date(start_date)
        if end_date:
            end_date = self._format_date(end_date)
            
        def _get_financial_indicator():
            try:
                # 使用针对financial_indicator优化的配置
                config = self._get_api_config('financial_indicator')
                
                # 直接使用get_financial_data
                return self.data_source.get_financial_data(
                    api_name='fina_indicator',
                    ts_code=symbol,
                    period=period,
                    start_date=start_date,
                    end_date=end_date
                )
            except Exception as e:
                self.logger.error(f"获取 {symbol} 财务指标失败: {e}")
                return pd.DataFrame()
        
        return self._cached_call('financial_indicator', _get_financial_indicator)
    
    def get_dividend(self, symbol: str,
                  start_date: Optional[Union[str, datetime.datetime]] = None,
                  end_date: Optional[Union[str, datetime.datetime]] = None) -> pd.DataFrame:
        """
        获取分红数据
        
        参数:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            pd.DataFrame: 分红数据
        """
        # 标准化日期
        if start_date:
            start_date = self._format_date(start_date)
        if end_date:
            end_date = self._format_date(end_date)
            
        def _get_dividend():
            try:
                return self.data_source.get_dividend(
                    ts_code=symbol,
                    start_date=start_date,
                    end_date=end_date
                )
            except Exception as e:
                self.logger.error(f"获取 {symbol} 分红数据失败: {e}")
                return pd.DataFrame()
        
        return self._cached_call('dividend', _get_dividend)
    
    def get_top_holders(self, symbol: str,
                     date: Optional[Union[str, datetime.datetime]] = None,
                     top_n: int = 10) -> pd.DataFrame:
        """
        获取前N大股东数据
        
        参数:
            symbol: 股票代码
            date: 查询日期，默认为最新
            top_n: 返回前N大股东
            
        返回:
            pd.DataFrame: 股东数据
        """
        # 标准化日期
        if date:
            date_str = self._format_date(date)
        else:
            date_str = None
            
        def _get_top_holders():
            try:
                holders = self.data_source.get_top_holders(
                    ts_code=symbol,
                    trade_date=date_str
                )
                
                # 限制返回前N大股东
                if not holders.empty and len(holders) > top_n:
                    return holders.head(top_n)
                return holders
            except Exception as e:
                self.logger.error(f"获取 {symbol} 股东数据失败: {e}")
                return pd.DataFrame()
        
        return self._cached_call('top_holders', _get_top_holders)
    
    def get_industry_financials(self, industry: str, date: Optional[Union[str, datetime.datetime]] = None,
                             indicators: List[str] = None) -> pd.DataFrame:
        """
        获取行业财务数据对比
        
        参数:
            industry: 行业名称或代码
            date: 报告日期，默认为最新
            indicators: 要对比的财务指标列表，如['roe', 'roa', 'net_profit_margin']等
            
        返回:
            pd.DataFrame: 行业内公司的财务指标对比
        """
        # 标准化日期
        if date:
            date_str = self._format_date(date)
        else:
            date_str = None
            
        # 如果未指定指标，使用默认常用指标
        if indicators is None:
            indicators = ['roe', 'roa', 'grossprofit_margin', 'netprofit_margin', 'debt_to_assets']
            
        def _get_industry_financials():
            try:
                # 获取行业成分股
                stocks = self.data_source.get_industry_stocks(industry)
                if stocks.empty:
                    return pd.DataFrame()
                
                # 获取成分股的财务指标
                result_list = []
                for _, stock_info in stocks.iterrows():
                    stock_code = stock_info['ts_code']
                    stock_name = stock_info.get('name', stock_code)
                    
                    # 获取该股票的财务指标
                    indicators_data = self.get_financial_indicator(
                        symbol=stock_code,
                        period='annual',
                        end_date=date_str
                    )
                    
                    if not indicators_data.empty:
                        # 获取最新的财务指标数据
                        latest_data = indicators_data.iloc[0].to_dict()
                        latest_data['ts_code'] = stock_code
                        latest_data['name'] = stock_name
                        result_list.append(latest_data)
                
                if not result_list:
                    return pd.DataFrame()
                
                # 构建结果DataFrame
                result_df = pd.DataFrame(result_list)
                
                # 筛选需要的指标列
                valid_columns = ['ts_code', 'name', 'end_date'] + [col for col in indicators if col in result_df.columns]
                return result_df[valid_columns].sort_values('end_date', ascending=False)
                
            except Exception as e:
                self.logger.error(f"获取行业 {industry} 财务对比数据失败: {e}")
                return pd.DataFrame()
        
        return self._cached_call(f'industry_financials_{industry}', _get_industry_financials)
    
    def get_valuation_data(self, symbol: str,
                        start_date: Optional[Union[str, datetime.datetime]] = None,
                        end_date: Optional[Union[str, datetime.datetime]] = None) -> pd.DataFrame:
        """
        获取股票估值数据
        
        参数:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            pd.DataFrame: 估值数据，包括PE、PB、PS等
        """
        # 标准化日期
        if start_date:
            start_date = self._format_date(start_date)
        if end_date:
            end_date = self._format_date(end_date)
            
        def _get_valuation_data():
            try:
                return self.data_source.get_daily_basic(
                    ts_code=symbol,
                    start_date=start_date,
                    end_date=end_date
                )
            except Exception as e:
                self.logger.error(f"获取 {symbol} 估值数据失败: {e}")
                return pd.DataFrame()
        
        return self._cached_call('valuation', _get_valuation_data)
    
    def get_forecast(self, symbol: str,
                  start_date: Optional[Union[str, datetime.datetime]] = None,
                  end_date: Optional[Union[str, datetime.datetime]] = None) -> pd.DataFrame:
        """
        获取业绩预告
        
        参数:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            pd.DataFrame: 业绩预告数据
        """
        # 标准化日期
        if start_date:
            start_date = self._format_date(start_date)
        if end_date:
            end_date = self._format_date(end_date)
            
        def _get_forecast():
            try:
                return self.data_source.get_forecast(
                    ts_code=symbol,
                    start_date=start_date,
                    end_date=end_date
                )
            except Exception as e:
                self.logger.error(f"获取 {symbol} 业绩预告失败: {e}")
                return pd.DataFrame()
        
        return self._cached_call('forecast', _get_forecast)
    
    def get_express_report(self, symbol: str,
                        start_date: Optional[Union[str, datetime.datetime]] = None,
                        end_date: Optional[Union[str, datetime.datetime]] = None,
                        **kwargs) -> pd.DataFrame:
        """
        获取业绩快报数据
        
        参数:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            **kwargs: 其他参数
            
        返回:
            pd.DataFrame: 业绩快报数据
        """
        if kwargs:
            raise TypeError(f"get_express_report() 不支持参数: {', '.join(kwargs.keys())}")
        
        # 标准化日期
        if start_date:
            start_date = self._format_date(start_date)
        if end_date:
            end_date = self._format_date(end_date)
            
        def _get_express_report():
            try:
                # 使用针对express_report优化的配置
                config = self._get_api_config('express_report')
                
                # 直接使用get_financial_data
                return self.data_source.get_financial_data(
                    api_name='express',
                    ts_code=symbol,
                    start_date=start_date,
                    end_date=end_date
                )
            except Exception as e:
                self.logger.error(f"获取 {symbol} 业绩快报失败: {e}")
                return pd.DataFrame()
        
        return self._cached_call('express_report', _get_express_report)
    
    def get_financial_data_by_date(self, date: Union[str, datetime.datetime],
                                fields: List[str] = None) -> pd.DataFrame:
        """
        获取指定日期的所有股票财务数据
        
        参数:
            date: 日期
            fields: 需要的字段列表
            
        返回:
            pd.DataFrame: 财务数据
        """
        # 标准化日期
        date_str = self._format_date(date)
        
        def _get_financial_data_by_date():
            try:
                return self.data_source.get_financial_data_by_date(
                    trade_date=date_str,
                    fields=fields
                )
            except Exception as e:
                self.logger.error(f"获取 {date_str} 的财务数据失败: {e}")
                return pd.DataFrame()
        
        return self._cached_call(f'financial_data_{date_str}', _get_financial_data_by_date)
    
    def get_all_reports(self, symbol: str, year: int, quarter: int) -> Dict[str, pd.DataFrame]:
        """
        获取某一季度的所有财务报表
        
        参数:
            symbol: 股票代码
            year: 年份
            quarter: 季度 (1-4)
            
        返回:
            Dict[str, pd.DataFrame]: 包含资产负债表、利润表、现金流量表的字典
        """
        if quarter < 1 or quarter > 4:
            raise ValueError("季度必须是1-4之间的整数")
            
        # 构建日期字符串
        if quarter == 1:
            date_str = f"{year}0331"
        elif quarter == 2:
            date_str = f"{year}0630"
        elif quarter == 3:
            date_str = f"{year}0930"
        else:  # quarter == 4
            date_str = f"{year}1231"
            
        def _get_all_reports():
            try:
                # 获取各种财务报表
                balance = self.get_balance_sheet(symbol, end_date=date_str)
                income = self.get_income_statement(symbol, end_date=date_str)
                cash_flow = self.get_cash_flow(symbol, end_date=date_str)
                indicators = self.get_financial_indicator(symbol, end_date=date_str)
                
                # 返回结果字典
                return {
                    'balance_sheet': balance,
                    'income_statement': income,
                    'cash_flow': cash_flow,
                    'indicators': indicators
                }
            except Exception as e:
                self.logger.error(f"获取 {symbol} {year}年第{quarter}季度报表失败: {e}")
                return {
                    'balance_sheet': pd.DataFrame(),
                    'income_statement': pd.DataFrame(),
                    'cash_flow': pd.DataFrame(),
                    'indicators': pd.DataFrame()
                }
        
        return self._cached_call(f'all_reports_{symbol}_{year}Q{quarter}', _get_all_reports)
    
    def get_financial_data_batch(self, api_name: str, symbols: List[str], **kwargs) -> Dict[str, pd.DataFrame]:
        """
        批量获取财务数据
        
        参数:
            api_name: API名称，支持 'income_statement', 'balance_sheet', 'cash_flow', 'financial_indicator', 'express_report'
            symbols: 股票代码列表
            **kwargs: 其他参数，与单个获取函数参数一致
            
        返回:
            Dict[str, pd.DataFrame]: 以股票代码为键的数据字典
        """
        # 获取API对应的函数
        api_func_map = {
            'income_statement': self.get_income_statement,
            'balance_sheet': self.get_balance_sheet,
            'cash_flow': self.get_cash_flow,
            'financial_indicator': self.get_financial_indicator,
            'express_report': self.get_express_report
        }
        
        if api_name not in api_func_map:
            self.logger.error(f"不支持的API: {api_name}")
            return {}
        
        api_func = api_func_map[api_name]
        
        # 获取API的性能配置
        config = self._get_api_config(api_name)
        batch_size = config['batch_size']
        workers = config['workers']
        
        # 分批处理
        results = {}
        batches = [symbols[i:i+batch_size] for i in range(0, len(symbols), batch_size)]
        
        self.logger.info(f"开始批量获取 {api_name} 数据，共 {len(symbols)} 只股票，分 {len(batches)} 批处理")
        
        for batch_idx, batch in enumerate(batches):
            self.logger.info(f"处理 {api_name} 批次 {batch_idx+1}/{len(batches)}")
            
            # 并行获取每批数据
            with ThreadPoolExecutor(max_workers=workers) as executor:
                batch_results = {}
                
                future_to_symbol = {
                    executor.submit(api_func, symbol, **kwargs): symbol
                    for symbol in batch
                }
                
                for future in future_to_symbol:
                    symbol = future_to_symbol[future]
                    try:
                        df = future.result()
                        batch_results[symbol] = df
                        
                        if df is not None and not df.empty:
                            self.logger.debug(f"成功获取 {symbol} 的 {api_name} 数据，{len(df)}行")
                        else:
                            self.logger.warning(f"获取 {symbol} 的 {api_name} 数据为空")
                    except Exception as e:
                        self.logger.error(f"获取 {symbol} 的 {api_name} 数据时出错: {e}")
                        batch_results[symbol] = pd.DataFrame()
            
            # 合并批次结果
            results.update(batch_results)
        
        self.logger.info(f"完成批量获取 {api_name} 数据，成功获取 {sum(1 for df in results.values() if not df.empty)}/{len(symbols)} 只股票的数据")
        return results
    
    def clear_cache(self):
        """清除所有缓存数据"""
        if hasattr(self, 'cache'):
            self.cache.clear()
            self.logger.info("已清除所有缓存数据")
        else:
            self.logger.warning("缓存未初始化")

