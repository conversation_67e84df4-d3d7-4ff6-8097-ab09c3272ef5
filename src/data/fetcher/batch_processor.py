"""
批量处理器
用于处理大量数据的批量获取和处理
"""

import time
import logging
import concurrent.futures
from typing import List, Callable, Any, Dict, Optional, Tuple, TypeVar, Generic, Iterator, Union
import threading
from dataclasses import dataclass


T = TypeVar('T')  # 输入数据类型
R = TypeVar('R')  # 输出结果类型


@dataclass
class BatchResult(Generic[T, R]):
    """批量处理结果"""
    
    item: T  # 原始输入项
    result: Optional[R] = None  # 处理结果，如果成功
    error: Optional[Exception] = None  # 错误信息，如果失败
    success: bool = True  # 是否成功
    processing_time: float = 0.0  # 处理时间（秒）


class BatchProcessor(Generic[T, R]):
    """
    批量处理器
    
    用于高效处理大量数据，支持并行处理和错误处理
    """
    
    def __init__(self,
                 processor_func: Callable[[T], R],
                 batch_size: int = 38,      # 从25提升到38（50%提升）
                 max_workers: int = 75,     # 从50提升到75（50%提升）
                 timeout: Optional[float] = None,
                 on_item_success: Optional[Callable[[BatchResult], None]] = None,
                 on_item_error: Optional[Callable[[BatchResult], None]] = None,
                 on_batch_complete: Optional[Callable[[List[BatchResult]], None]] = None,
                 retry_failed: bool = False,
                 max_retries: int = 3,
                 retry_delay: float = 1.0,
                 use_thread_pool: bool = True):
        """
        初始化批量处理器
        
        参数:
            processor_func: 处理单个数据项的函数
            batch_size: 每批处理的数据量
            max_workers: 最大并行工作线程数
            timeout: 每个处理操作的超时时间（秒），None表示无限制
            on_item_success: 当单个项成功处理时的回调函数
            on_item_error: 当单个项处理失败时的回调函数
            on_batch_complete: 当一个批次完成处理时的回调函数
            retry_failed: 是否自动重试失败的项
            max_retries: 最大重试次数
            retry_delay: 重试间隔（秒）
            use_thread_pool: 是否使用线程池（如果False则串行处理）
        """
        self.processor_func = processor_func
        self.batch_size = batch_size
        self.max_workers = max_workers
        self.timeout = timeout
        self.on_item_success = on_item_success
        self.on_item_error = on_item_error
        self.on_batch_complete = on_batch_complete
        self.retry_failed = retry_failed
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.use_thread_pool = use_thread_pool
        
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()
        
        # 处理统计信息
        self.stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'retried': 0,
            'total_batches': 0,
            'total_time': 0.0
        }
    
    def process_batch(self, batch: List[T]) -> List[BatchResult[T, R]]:
        """
        处理一批数据
        
        参数:
            batch: 要处理的数据列表
            
        返回:
            List[BatchResult]: 处理结果列表
        """
        results: List[BatchResult[T, R]] = []
        
        # 将处理函数应用到整个批，如果处理函数支持批处理
        batch_supported = False
        try:
            if hasattr(self.processor_func, '__code__') and self.processor_func.__code__.co_argcount == 1:
                # 尝试直接将整个批次传递给处理函数
                param_hint = self.processor_func.__code__.co_varnames[0]
                # 如果函数是从另一个函数计算的，比如lambda或partial，那么它可能有不同的参数名
                # 我们假设它能够处理批量参数
                if param_hint in ['batch', 'items', 'list', 'data'] or not isinstance(batch[0], (int, float, str, bool)):
                    batch_supported = True
        except (AttributeError, IndexError):
            batch_supported = False

        if self.use_thread_pool and len(batch) > 1 and self.max_workers > 1 and not batch_supported:
            # 并行处理
            with concurrent.futures.ThreadPoolExecutor(max_workers=min(self.max_workers, len(batch))) as executor:
                # 提交所有任务
                future_to_item = {
                    executor.submit(self._process_item, item): item 
                    for item in batch
                }
                
                # 获取结果
                for future in concurrent.futures.as_completed(future_to_item):
                    item = future_to_item[future]
                    try:
                        result = future.result(timeout=self.timeout)
                        results.append(result)
                        
                        # 更新统计信息
                        with self._lock:
                            self.stats['total_processed'] += 1
                            if result.success:
                                self.stats['successful'] += 1
                            else:
                                self.stats['failed'] += 1
                        
                        # 回调
                        if result.success and self.on_item_success:
                            self.on_item_success(result)
                        elif not result.success and self.on_item_error:
                            self.on_item_error(result)
                            
                    except concurrent.futures.TimeoutError:
                        # 处理超时
                        error_result = BatchResult(
                            item=item,
                            error=TimeoutError(f"处理超时，超过 {self.timeout} 秒"),
                            success=False
                        )
                        results.append(error_result)
                        
                        # 更新统计信息
                        with self._lock:
                            self.stats['total_processed'] += 1
                            self.stats['failed'] += 1
                        
                        # 回调
                        if self.on_item_error:
                            self.on_item_error(error_result)
        elif batch_supported:
            # 批处理模式 - 处理整个批次
            try:
                start_time = time.time()
                batch_result = self.processor_func(batch)
                processing_time = time.time() - start_time
                
                # 如果返回的是列表，并且长度与批次相同，假设它是按项返回的结果
                if isinstance(batch_result, list) and len(batch_result) == len(batch):
                    for i, (item, res) in enumerate(zip(batch, batch_result)):
                        results.append(BatchResult(
                            item=item,
                            result=res,
                            success=True,
                            processing_time=processing_time / len(batch)
                        ))
                else:
                    # 否则假设它是一个整体结果
                    results = [BatchResult(
                        item=item,
                        result=batch_result,
                        success=True,
                        processing_time=processing_time / len(batch)
                    ) for item in batch]
                
                # 更新统计信息
                with self._lock:
                    self.stats['total_processed'] += len(batch)
                    self.stats['successful'] += len(batch)
                
                # 回调
                if self.on_item_success:
                    for result in results:
                        self.on_item_success(result)
                
            except Exception as e:
                # 批处理失败，回退到逐项处理
                self.logger.error(f"批处理失败: {str(e)}，回退到逐项处理")
                
                # 串行处理每个项
                for item in batch:
                    result = self._process_item(item)
                    results.append(result)
                    
                    # 更新统计信息
                    with self._lock:
                        self.stats['total_processed'] += 1
                        if result.success:
                            self.stats['successful'] += 1
                        else:
                            self.stats['failed'] += 1
                    
                    # 回调
                    if result.success and self.on_item_success:
                        self.on_item_success(result)
                    elif not result.success and self.on_item_error:
                        self.on_item_error(result)
        else:
            # 串行处理
            for item in batch:
                result = self._process_item(item)
                results.append(result)
                
                # 更新统计信息
                with self._lock:
                    self.stats['total_processed'] += 1
                    if result.success:
                        self.stats['successful'] += 1
                    else:
                        self.stats['failed'] += 1
                
                # 回调
                if result.success and self.on_item_success:
                    self.on_item_success(result)
                elif not result.success and self.on_item_error:
                    self.on_item_error(result)
        
        # 批次完成回调
        if self.on_batch_complete:
            self.on_batch_complete(results)
        
        # 更新批次统计
        with self._lock:
            self.stats['total_batches'] += 1
        
        return results

    # 移除增强版并发处理方法，回滚到原始版本

    # 移除智能批处理和增强批处理方法，回滚到原始版本
    
    def _process_item(self, item: T, retries: int = 0) -> BatchResult[T, R]:
        """
        处理单个数据项
        
        参数:
            item: 要处理的数据项
            retries: 当前重试次数
            
        返回:
            BatchResult: 处理结果
        """
        start_time = time.time()
        
        try:
            # 调用处理函数
            result = self.processor_func(item)
            
            processing_time = time.time() - start_time
            
            return BatchResult(
                item=item,
                result=result,
                success=True,
                processing_time=processing_time
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            
            # 判断是否重试
            if self.retry_failed and retries < self.max_retries:
                self.logger.warning(f"处理 {item} 失败: {str(e)}，将重试 ({retries + 1}/{self.max_retries})")
                
                # 更新重试统计
                with self._lock:
                    self.stats['retried'] += 1
                
                # 等待后重试
                time.sleep(self.retry_delay * (retries + 1))
                return self._process_item(item, retries + 1)
            else:
                self.logger.error(f"处理 {item} 失败: {str(e)}")
                
                return BatchResult(
                    item=item,
                    error=e,
                    success=False,
                    processing_time=processing_time
                )
    
    def process_all(self, data: Union[List[T], Iterator[T]]) -> List[BatchResult[T, R]]:
        """
        处理完整的数据列表或迭代器
        
        参数:
            data: 要处理的数据列表或迭代器
            
        返回:
            List[BatchResult]: 处理结果列表
        """
        if isinstance(data, (list, tuple)):
            return self.process(data)
        else:
            # 处理迭代器
            return self._process_iterator(data)
    
    def _process_iterator(self, iterator: Iterator[T]) -> List[BatchResult[T, R]]:
        """
        处理数据迭代器
        
        参数:
            iterator: 数据迭代器
            
        返回:
            List[BatchResult]: 处理结果列表
        """
        all_results = []
        batch = []
        
        # 收集一个批次的数据
        for item in iterator:
            batch.append(item)
            
            if len(batch) >= self.batch_size:
                # 处理当前批次
                batch_results = self.process_batch(batch)
                all_results.extend(batch_results)
                batch = []
                
        # 处理最后一个不满的批次
        if batch:
            batch_results = self.process_batch(batch)
            all_results.extend(batch_results)
            
        return all_results
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        返回:
            Dict[str, Any]: 统计信息字典
        """
        with self._lock:
            return dict(self.stats)
    
    def reset_stats(self):
        """
        重置统计信息
        """
        with self._lock:
            self.stats = {
                'total_processed': 0,
                'successful': 0,
                'failed': 0,
                'retried': 0,
                'total_batches': 0,
                'total_time': 0.0
            }

    def process(self,
                items: List[T],
                config: Optional[Dict[str, Any]] = None) -> List[BatchResult[T, R]]:
        """
        基础批处理方法（原始版本）

        Args:
            items: 要处理的数据项列表
            config: 处理配置（可选）

        Returns:
            List[BatchResult]: 处理结果列表
        """
        if not items:
            return []

        # 使用基础并发处理
        return self.process_concurrent(items)