"""
智能缓存管理器
实现高级缓存策略，包括智能键生成、版本管理和性能监控
"""

import hashlib
import json
import logging
import time
import threading
import pickle
from collections import defaultdict, OrderedDict
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Tuple, Callable
from dataclasses import dataclass
from enum import Enum
from src.data.storage.cache.cache_interface import CacheInterface
from src.data.storage.cache.cache_factory import CacheFactory


# {{ AURA-X: Add - 增强缓存策略和算法支持. Approval: 寸止(ID:最优实践优化). }}
class CacheStrategy(Enum):
    """缓存策略枚举"""
    LRU = "lru"              # 最近最少使用
    LFU = "lfu"              # 最少使用频率
    FIFO = "fifo"            # 先进先出
    ADAPTIVE = "adaptive"     # 自适应策略
    TIME_BASED = "time_based" # 基于时间的策略


class CachePriority(Enum):
    """缓存优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class CacheMetrics:
    """缓存指标数据类"""
    key: str
    access_count: int = 0
    last_access_time: float = 0
    creation_time: float = 0
    size_bytes: int = 0
    priority: CachePriority = CachePriority.NORMAL
    data_type: str = "unknown"

    def __post_init__(self):
        if self.creation_time == 0:
            self.creation_time = time.time()
        if self.last_access_time == 0:
            self.last_access_time = self.creation_time


@dataclass
class CacheConfig:
    """缓存配置数据类"""
    strategy: CacheStrategy = CacheStrategy.ADAPTIVE
    max_size: int = 10000
    default_ttl: int = 3600
    enable_compression: bool = False
    enable_encryption: bool = False
    enable_stats: bool = True
    preload_patterns: List[str] = None

    def __post_init__(self):
        if self.preload_patterns is None:
            self.preload_patterns = []


class SmartCacheManager:
    """
    增强的智能缓存管理器

    功能：
    1. 智能缓存键生成（包含时间范围、数据版本）
    2. 缓存命中率统计和监控
    3. 智能过期策略和多种缓存算法
    4. 缓存预热功能和预测性加载
    5. 数据版本管理和一致性保证
    6. 压缩和加密支持
    7. 自适应性能优化
    """

    def __init__(
        self,
        cache: CacheInterface,
        config: Optional[CacheConfig] = None
    ):
        """
        初始化智能缓存管理器

        Args:
            cache: 缓存接口实例
            config: 缓存配置
        """
        # {{ AURA-X: Modify - 增强初始化，支持多种缓存策略和配置. Approval: 寸止(ID:最优实践优化). }}
        self.cache = cache
        self.config = config or CacheConfig()
        self.logger = logging.getLogger(__name__)

        # 线程安全锁
        self._lock = threading.RLock()

        # 缓存指标跟踪
        self._metrics: Dict[str, CacheMetrics] = {}
        self._access_order = OrderedDict()  # LRU支持
        self._frequency_counter = defaultdict(int)  # LFU支持

        # 统计信息
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'evictions': 0,
            'compressions': 0,
            'decompressions': 0,
            'hit_rate': 0.0,
            'avg_access_time': 0.0,
            'cache_size': 0,
            'memory_usage': 0,
            'last_reset': time.time(),
            'strategy_switches': 0
        }

        # 缓存键前缀
        self.key_prefixes = {
            'market_data': 'mkt',
            'financial_data': 'fin',
            'reference_data': 'ref',
            'analysis_result': 'ana',
            'backtest_result': 'bt',
            'risk_metrics': 'risk',
            'portfolio_data': 'pf'
        }

        # 智能TTL策略
        self.ttl_strategies = {
            'market_data': 1800,      # 30分钟
            'financial_data': 86400,  # 24小时
            'reference_data': 604800, # 7天
            'analysis_result': 3600,  # 1小时
            'backtest_result': 7200,  # 2小时
            'risk_metrics': 900,      # 15分钟
            'portfolio_data': 300     # 5分钟
        }

        # 预热任务队列
        self._preload_queue = []
        self._preload_callbacks: List[Callable] = []

        # 性能监控
        self._performance_history = []
        self._last_cleanup_time = time.time()
        self._cleanup_interval = 300  # 5分钟清理一次

        # 初始化策略处理器
        self._strategy_handlers = {
            CacheStrategy.LRU: self._handle_lru_access,
            CacheStrategy.LFU: self._handle_lfu_access,
            CacheStrategy.FIFO: self._handle_fifo_access,
            CacheStrategy.ADAPTIVE: self._handle_adaptive_access,
            CacheStrategy.TIME_BASED: self._handle_time_based_access
        }

        self.logger.info(f"SmartCacheManager初始化完成: 策略={self.config.strategy.value}, 最大大小={self.config.max_size}")

    @classmethod
    def from_config(
        cls,
        cache_config: Dict[str, Any],
        default_ttl: int = 3600,
        enable_stats: bool = True
    ) -> 'SmartCacheManager':
        """
        从配置创建SmartCacheManager

        Args:
            cache_config: 缓存配置字典
            default_ttl: 默认过期时间（秒）
            enable_stats: 是否启用统计功能

        Returns:
            SmartCacheManager: 缓存管理器实例
        """
        # 使用缓存工厂创建缓存实例
        cache = CacheFactory.create_cache_from_app_config({'cache': cache_config})

        return cls(
            cache=cache,
            default_ttl=default_ttl,
            enable_stats=enable_stats
        )
    
    def generate_cache_key(
        self,
        data_type: str,
        symbols: Union[str, List[str]] = None,
        start_date: str = None,
        end_date: str = None,
        fields: List[str] = None,
        extra_params: Dict[str, Any] = None,
        version: str = "v1",
        priority: CachePriority = CachePriority.NORMAL,
        **kwargs
    ) -> str:
        """
        生成智能缓存键
        
        Args:
            data_type: 数据类型
            symbols: 股票代码或代码列表
            start_date: 开始日期
            end_date: 结束日期
            fields: 字段列表
            extra_params: 额外参数
            version: 数据版本
            
        Returns:
            str: 缓存键
        """
        # 构建键组件
        key_components = []
        
        # 添加前缀
        prefix = self.key_prefixes.get(data_type, 'data')
        key_components.append(prefix)
        
        # 添加版本
        key_components.append(version)
        
        # 添加数据类型
        key_components.append(data_type)
        
        # 处理股票代码
        if symbols:
            if isinstance(symbols, str):
                symbol_str = symbols
            else:
                # 对股票列表进行排序和哈希，确保一致性
                sorted_symbols = sorted(symbols)
                if len(sorted_symbols) > 10:
                    # 如果股票太多，使用哈希
                    symbol_hash = hashlib.md5(
                        ','.join(sorted_symbols).encode()
                    ).hexdigest()[:8]
                    symbol_str = f"batch_{len(sorted_symbols)}_{symbol_hash}"
                else:
                    symbol_str = ','.join(sorted_symbols)
            key_components.append(symbol_str)
        
        # 添加日期范围
        if start_date and end_date:
            key_components.append(f"{start_date}_{end_date}")
        elif start_date:
            key_components.append(f"from_{start_date}")
        elif end_date:
            key_components.append(f"to_{end_date}")
        
        # 添加字段
        if fields:
            fields_str = ','.join(sorted(fields))
            if len(fields_str) > 50:
                # 字段太多时使用哈希
                fields_hash = hashlib.md5(fields_str.encode()).hexdigest()[:8]
                key_components.append(f"fields_{fields_hash}")
            else:
                key_components.append(fields_str)
        
        # 添加额外参数
        if extra_params:
            # 将参数序列化并哈希
            params_str = json.dumps(extra_params, sort_keys=True)
            params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
            key_components.append(f"params_{params_hash}")
        
        # 组合键
        cache_key = ':'.join(key_components)
        
        # 确保键长度不超过限制
        if len(cache_key) > 200:
            # 如果键太长，使用哈希
            key_hash = hashlib.sha256(cache_key.encode()).hexdigest()[:32]
            cache_key = f"{prefix}:{version}:hash_{key_hash}"
        
        return cache_key

    def _calculate_data_size(self, data: Any) -> int:
        """计算数据大小（字节）"""
        # {{ AURA-X: Add - 数据大小计算，支持内存使用监控. Approval: 寸止(ID:最优实践优化). }}
        try:
            if hasattr(data, '__sizeof__'):
                return data.__sizeof__()
            else:
                # 使用pickle序列化估算大小
                return len(pickle.dumps(data))
        except Exception:
            # 如果无法计算，返回估算值
            return len(str(data)) * 2

    def _should_compress(self, data: Any, size_bytes: int) -> bool:
        """判断是否应该压缩数据"""
        if not self.config.enable_compression:
            return False

        # 大于1KB的数据考虑压缩
        return size_bytes > 1024

    def _compress_data(self, data: Any) -> bytes:
        """压缩数据"""
        import gzip
        serialized = pickle.dumps(data)
        compressed = gzip.compress(serialized)

        if self.config.enable_stats:
            self.stats['compressions'] += 1

        return compressed

    def _decompress_data(self, compressed_data: bytes) -> Any:
        """解压数据"""
        import gzip
        decompressed = gzip.decompress(compressed_data)
        data = pickle.loads(decompressed)

        if self.config.enable_stats:
            self.stats['decompressions'] += 1

        return data

    def _update_access_metrics(self, key: str, data_type: str = "unknown",
                              priority: CachePriority = CachePriority.NORMAL):
        """更新访问指标"""
        with self._lock:
            current_time = time.time()

            if key not in self._metrics:
                self._metrics[key] = CacheMetrics(
                    key=key,
                    data_type=data_type,
                    priority=priority
                )

            metrics = self._metrics[key]
            metrics.access_count += 1
            metrics.last_access_time = current_time

            # 更新策略相关的数据结构
            strategy_handler = self._strategy_handlers.get(self.config.strategy)
            if strategy_handler:
                strategy_handler(key, metrics)

    def _handle_lru_access(self, key: str, metrics: CacheMetrics):
        """处理LRU访问"""
        # 更新访问顺序
        if key in self._access_order:
            del self._access_order[key]
        self._access_order[key] = time.time()

    def _handle_lfu_access(self, key: str, metrics: CacheMetrics):
        """处理LFU访问"""
        self._frequency_counter[key] += 1

    def _handle_fifo_access(self, key: str, metrics: CacheMetrics):
        """处理FIFO访问"""
        # FIFO不需要特殊处理，按创建时间排序
        pass

    def _handle_adaptive_access(self, key: str, metrics: CacheMetrics):
        """处理自适应访问"""
        # 结合LRU和LFU策略
        self._handle_lru_access(key, metrics)
        self._handle_lfu_access(key, metrics)

        # 根据性能动态调整策略
        if len(self._performance_history) > 10:
            recent_hit_rate = sum(self._performance_history[-10:]) / 10
            if recent_hit_rate < 0.5:
                # 命中率低，可能需要调整策略
                self._consider_strategy_switch()

    def _handle_time_based_access(self, key: str, metrics: CacheMetrics):
        """处理基于时间的访问"""
        # 基于时间的策略主要在清理时处理
        pass

    def _consider_strategy_switch(self):
        """考虑策略切换"""
        if self.config.strategy == CacheStrategy.ADAPTIVE:
            # 在自适应模式下，可以动态切换底层策略
            current_time = time.time()
            if hasattr(self, '_last_strategy_switch'):
                if current_time - self._last_strategy_switch < 300:  # 5分钟内不重复切换
                    return

            self._last_strategy_switch = current_time
            self.stats['strategy_switches'] += 1
            self.logger.info("自适应策略触发策略评估")

    def get(self, key: str, default: Any = None, data_type: str = "unknown",
           priority: CachePriority = CachePriority.NORMAL) -> Any:
        """
        获取缓存数据（增强版）

        Args:
            key: 缓存键
            default: 默认值
            data_type: 数据类型
            priority: 缓存优先级

        Returns:
            Any: 缓存值或默认值
        """
        # {{ AURA-X: Modify - 增强get方法，支持访问统计和策略处理. Approval: 寸止(ID:最优实践优化). }}
        start_time = time.time()

        try:
            value = self.cache.get(key, default)

            # 更新访问指标
            if value is not default:
                self._update_access_metrics(key, data_type, priority)

                # 处理压缩数据
                if isinstance(value, bytes) and self.config.enable_compression:
                    try:
                        value = self._decompress_data(value)
                    except Exception as e:
                        self.logger.warning(f"解压缓存数据失败: {e}")
                        value = default

                if self.config.enable_stats:
                    self.stats['hits'] += 1
                    access_time = time.time() - start_time
                    self._update_avg_access_time(access_time)
            else:
                if self.config.enable_stats:
                    self.stats['misses'] += 1

            if self.config.enable_stats:
                self._update_hit_rate()

            return value

        except Exception as e:
            self.logger.error(f"获取缓存失败: {e}")
            if self.config.enable_stats:
                self.stats['misses'] += 1
                self._update_hit_rate()
            return default
    
    def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        data_type: str = "unknown",
        priority: CachePriority = CachePriority.NORMAL
    ) -> bool:
        """
        设置缓存数据（增强版）

        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒）
            data_type: 数据类型，用于智能TTL
            priority: 缓存优先级

        Returns:
            bool: 是否设置成功
        """
        # {{ AURA-X: Modify - 增强set方法，支持压缩、优先级和容量管理. Approval: 寸止(ID:最优实践优化). }}
        try:
            # 计算数据大小
            data_size = self._calculate_data_size(value)

            # 检查是否需要清理空间
            if self._should_evict_for_new_data(data_size, priority):
                self._evict_data_for_space(data_size, priority)

            # 处理数据压缩
            processed_value = value
            if self._should_compress(value, data_size):
                processed_value = self._compress_data(value)
                data_size = len(processed_value)  # 更新压缩后的大小

            # 智能TTL策略
            if ttl is None:
                ttl = self._calculate_smart_ttl(data_type, value)

            # 设置缓存
            success = self.cache.set(key, processed_value, ttl)

            if success:
                # 更新指标
                self._update_cache_metrics(key, data_type, priority, data_size)

                if self.config.enable_stats:
                    self.stats['sets'] += 1
                    self.stats['cache_size'] += 1
                    self.stats['memory_usage'] += data_size

            return success

        except Exception as e:
            self.logger.error(f"设置缓存失败: {e}")
            return False

    def _should_evict_for_new_data(self, data_size: int, priority: CachePriority) -> bool:
        """判断是否需要为新数据腾出空间"""
        current_size = len(self._metrics)

        # 检查数量限制
        if current_size >= self.config.max_size:
            return True

        # 检查内存使用（如果可以估算）
        memory_usage = self.stats.get('memory_usage', 0)
        if memory_usage > 0:
            # 假设最大内存使用为100MB
            max_memory = 100 * 1024 * 1024
            if memory_usage + data_size > max_memory:
                return True

        return False

    def _evict_data_for_space(self, needed_size: int, new_priority: CachePriority):
        """为新数据腾出空间"""
        with self._lock:
            evicted_count = 0
            evicted_size = 0

            # 根据策略选择要清理的数据
            candidates = self._get_eviction_candidates(new_priority)

            for key in candidates:
                if evicted_size >= needed_size and evicted_count >= 1:
                    break

                try:
                    # 删除缓存项
                    self.cache.delete(key)

                    # 更新统计
                    if key in self._metrics:
                        metrics = self._metrics[key]
                        evicted_size += metrics.size_bytes
                        del self._metrics[key]

                    # 清理策略相关数据
                    if key in self._access_order:
                        del self._access_order[key]
                    if key in self._frequency_counter:
                        del self._frequency_counter[key]

                    evicted_count += 1

                except Exception as e:
                    self.logger.warning(f"清理缓存项失败 {key}: {e}")

            if self.config.enable_stats:
                self.stats['evictions'] += evicted_count
                self.stats['cache_size'] -= evicted_count
                self.stats['memory_usage'] -= evicted_size

            if evicted_count > 0:
                self.logger.debug(f"为新数据清理了 {evicted_count} 个缓存项，释放 {evicted_size} 字节")

    def _get_eviction_candidates(self, new_priority: CachePriority) -> List[str]:
        """获取清理候选项"""
        candidates = []

        # 根据策略选择候选项
        if self.config.strategy == CacheStrategy.LRU:
            # LRU: 选择最久未访问的
            sorted_items = sorted(self._access_order.items(), key=lambda x: x[1])
            candidates = [key for key, _ in sorted_items]

        elif self.config.strategy == CacheStrategy.LFU:
            # LFU: 选择访问频率最低的
            sorted_items = sorted(self._frequency_counter.items(), key=lambda x: x[1])
            candidates = [key for key, _ in sorted_items]

        elif self.config.strategy == CacheStrategy.FIFO:
            # FIFO: 选择最早创建的
            sorted_metrics = sorted(self._metrics.items(), key=lambda x: x[1].creation_time)
            candidates = [key for key, _ in sorted_metrics]

        else:  # ADAPTIVE or TIME_BASED
            # 综合考虑多个因素
            candidates = self._get_adaptive_eviction_candidates(new_priority)

        return candidates

    def _get_adaptive_eviction_candidates(self, new_priority: CachePriority) -> List[str]:
        """获取自适应清理候选项"""
        scored_items = []
        current_time = time.time()

        for key, metrics in self._metrics.items():
            # 计算综合分数（分数越低越容易被清理）
            score = 0

            # 优先级因子（优先级低的更容易被清理）
            if metrics.priority.value < new_priority.value:
                score += (new_priority.value - metrics.priority.value) * 10

            # 访问频率因子
            access_frequency = self._frequency_counter.get(key, 0)
            score -= access_frequency * 2

            # 最近访问时间因子
            time_since_access = current_time - metrics.last_access_time
            score += time_since_access / 3600  # 每小时加1分

            # 数据大小因子（大数据更容易被清理）
            score += metrics.size_bytes / (1024 * 1024)  # 每MB加1分

            scored_items.append((key, score))

        # 按分数排序，分数高的优先清理
        scored_items.sort(key=lambda x: x[1], reverse=True)

        return [key for key, _ in scored_items]

    def _update_cache_metrics(self, key: str, data_type: str, priority: CachePriority, size_bytes: int):
        """更新缓存指标"""
        # {{ AURA-X: Add - 缓存指标更新和管理. Approval: 寸止(ID:最优实践优化). }}
        with self._lock:
            if key not in self._metrics:
                self._metrics[key] = CacheMetrics(
                    key=key,
                    data_type=data_type,
                    priority=priority,
                    size_bytes=size_bytes
                )
            else:
                # 更新现有指标
                metrics = self._metrics[key]
                metrics.data_type = data_type
                metrics.priority = priority
                metrics.size_bytes = size_bytes
                metrics.last_access_time = time.time()

    def _update_avg_access_time(self, access_time: float):
        """更新平均访问时间"""
        current_avg = self.stats['avg_access_time']
        total_requests = self.stats['hits'] + self.stats['misses']

        if total_requests > 0:
            self.stats['avg_access_time'] = (
                (current_avg * (total_requests - 1) + access_time) / total_requests
            )

    def _calculate_smart_ttl(self, data_type: str, value: Any) -> int:
        """
        计算智能TTL
        
        Args:
            data_type: 数据类型
            value: 缓存值
            
        Returns:
            int: TTL（秒）
        """
        # 基于数据类型的TTL策略
        ttl_strategies = {
            'stock_list': 24 * 3600,      # 股票列表：24小时
            'daily': 4 * 3600,            # 日线数据：4小时
            'minute': 5 * 60,             # 分钟数据：5分钟
            'financial': 12 * 3600,       # 财务数据：12小时
            'market_cap': 2 * 3600,       # 市值数据：2小时
            'analysis': 30 * 60,          # 分析结果：30分钟
        }
        
        base_ttl = ttl_strategies.get(data_type, self.config.default_ttl)
        
        # 基于数据大小的调整
        try:
            if hasattr(value, '__len__'):
                data_size = len(value)
                if data_size > 10000:
                    # 大数据集缓存时间更长
                    base_ttl = int(base_ttl * 1.5)
                elif data_size < 100:
                    # 小数据集缓存时间更短
                    base_ttl = int(base_ttl * 0.7)
        except:
            pass
        
        return base_ttl
    
    def delete(self, key: str) -> bool:
        """
        删除缓存
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否删除成功
        """
        try:
            success = self.cache.delete(key)
            
            if self.config.enable_stats and success:
                self.stats['deletes'] += 1
            
            return success
            
        except Exception as e:
            self.logger.error(f"删除缓存失败: {e}")
            return False
    
    def clear_by_pattern(self, pattern: str) -> int:
        """
        按模式清除缓存
        
        Args:
            pattern: 模式字符串
            
        Returns:
            int: 清除的缓存数量
        """
        # 注意：这需要缓存实现支持模式匹配
        # 这里提供一个基础实现
        try:
            if hasattr(self.cache, 'clear_by_pattern'):
                return self.cache.clear_by_pattern(pattern)
            else:
                self.logger.warning("缓存实现不支持模式清除")
                return 0
        except Exception as e:
            self.logger.error(f"按模式清除缓存失败: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = self.stats.copy()
        
        # 添加缓存实现的统计信息
        try:
            if hasattr(self.cache, 'get_stats'):
                cache_stats = self.cache.get_stats()
                stats.update(cache_stats)
        except:
            pass
        
        # 添加运行时间
        stats['uptime_seconds'] = time.time() - stats['last_reset']
        
        return stats
    
    def _update_hit_rate(self):
        """更新命中率"""
        total_requests = self.stats['hits'] + self.stats['misses']
        if total_requests > 0:
            self.stats['hit_rate'] = self.stats['hits'] / total_requests
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'hit_rate': 0.0,
            'last_reset': time.time()
        }
    
    def warmup_cache(
        self,
        warmup_data: List[Dict[str, Any]],
        batch_size: int = 100
    ) -> int:
        """
        缓存预热
        
        Args:
            warmup_data: 预热数据列表
            batch_size: 批处理大小
            
        Returns:
            int: 预热的缓存数量
        """
        warmed_count = 0
        
        try:
            for i in range(0, len(warmup_data), batch_size):
                batch = warmup_data[i:i + batch_size]
                
                for item in batch:
                    key = item.get('key')
                    value = item.get('value')
                    ttl = item.get('ttl')
                    data_type = item.get('data_type')
                    
                    if key and value is not None:
                        if self.set(key, value, ttl, data_type):
                            warmed_count += 1
                
                # 避免过快的批处理
                time.sleep(0.01)
            
            self.logger.info(f"缓存预热完成，预热了 {warmed_count} 个缓存项")
            
        except Exception as e:
            self.logger.error(f"缓存预热失败: {e}")
        
        return warmed_count

    # {{ AURA-X: Add - 新增高级缓存管理功能. Approval: 寸止(ID:最优实践优化). }}
    def register_preload_callback(self, callback: Callable[[str], Any]):
        """注册预加载回调函数"""
        if callback not in self._preload_callbacks:
            self._preload_callbacks.append(callback)
            self.logger.info("预加载回调已注册")

    def unregister_preload_callback(self, callback: Callable[[str], Any]):
        """取消注册预加载回调函数"""
        if callback in self._preload_callbacks:
            self._preload_callbacks.remove(callback)
            self.logger.info("预加载回调已取消注册")

    def get_cache_health(self) -> Dict[str, Any]:
        """获取缓存健康状态"""
        current_time = time.time()
        uptime = current_time - self.stats['last_reset']

        # 计算健康指标
        hit_rate = self.stats['hit_rate']
        avg_access_time = self.stats['avg_access_time']
        cache_size = len(self._metrics)
        memory_usage = self.stats.get('memory_usage', 0)

        # 健康评分（0-100）
        health_score = 100

        # 命中率影响（权重40%）
        if hit_rate < 0.3:
            health_score -= 40
        elif hit_rate < 0.6:
            health_score -= 20
        elif hit_rate < 0.8:
            health_score -= 10

        # 访问时间影响（权重30%）
        if avg_access_time > 0.1:
            health_score -= 30
        elif avg_access_time > 0.05:
            health_score -= 15
        elif avg_access_time > 0.01:
            health_score -= 5

        # 内存使用影响（权重20%）
        if cache_size > self.config.max_size * 0.9:
            health_score -= 20
        elif cache_size > self.config.max_size * 0.8:
            health_score -= 10

        # 清理频率影响（权重10%）
        eviction_rate = self.stats['evictions'] / max(self.stats['sets'], 1)
        if eviction_rate > 0.3:
            health_score -= 10
        elif eviction_rate > 0.1:
            health_score -= 5

        health_score = max(0, health_score)

        # 健康状态
        if health_score >= 80:
            health_status = "excellent"
        elif health_score >= 60:
            health_status = "good"
        elif health_score >= 40:
            health_status = "fair"
        elif health_score >= 20:
            health_status = "poor"
        else:
            health_status = "critical"

        return {
            'health_score': health_score,
            'health_status': health_status,
            'uptime': uptime,
            'hit_rate': hit_rate,
            'avg_access_time': avg_access_time,
            'cache_size': cache_size,
            'max_size': self.config.max_size,
            'memory_usage': memory_usage,
            'eviction_rate': eviction_rate,
            'recommendations': self._get_health_recommendations(health_score, hit_rate, avg_access_time, eviction_rate)
        }

    def _get_health_recommendations(self, health_score: float, hit_rate: float,
                                  avg_access_time: float, eviction_rate: float) -> List[str]:
        """获取健康改善建议"""
        recommendations = []

        if hit_rate < 0.5:
            recommendations.append("考虑增加缓存大小或调整TTL策略")

        if avg_access_time > 0.05:
            recommendations.append("考虑启用数据压缩或优化缓存键结构")

        if eviction_rate > 0.2:
            recommendations.append("缓存清理频繁，建议增加缓存容量")

        if health_score < 60:
            recommendations.append("考虑切换到自适应缓存策略")

        if len(self._metrics) > self.config.max_size * 0.9:
            recommendations.append("缓存接近容量上限，建议清理或扩容")

        return recommendations

    def optimize_performance(self) -> Dict[str, Any]:
        """自动性能优化"""
        optimization_results = {
            'actions_taken': [],
            'performance_improvement': 0,
            'recommendations': []
        }

        health = self.get_cache_health()

        # 自动优化策略
        if health['hit_rate'] < 0.4 and self.config.strategy != CacheStrategy.ADAPTIVE:
            # 切换到自适应策略
            old_strategy = self.config.strategy
            self.config.strategy = CacheStrategy.ADAPTIVE
            optimization_results['actions_taken'].append(f"策略从{old_strategy.value}切换到adaptive")

        # 清理过期和低价值缓存
        cleaned_count = self._cleanup_low_value_cache()
        if cleaned_count > 0:
            optimization_results['actions_taken'].append(f"清理了{cleaned_count}个低价值缓存项")

        # 预加载高价值数据
        if len(self.config.preload_patterns) > 0:
            preloaded = self._preload_high_value_data()
            if preloaded > 0:
                optimization_results['actions_taken'].append(f"预加载了{preloaded}个高价值缓存项")

        # 计算性能改善
        new_health = self.get_cache_health()
        optimization_results['performance_improvement'] = new_health['health_score'] - health['health_score']

        # 添加建议
        optimization_results['recommendations'] = new_health['recommendations']

        return optimization_results

    def _cleanup_low_value_cache(self) -> int:
        """清理低价值缓存"""
        cleaned_count = 0
        current_time = time.time()

        with self._lock:
            low_value_keys = []

            for key, metrics in self._metrics.items():
                # 识别低价值缓存项
                is_low_value = (
                    metrics.access_count <= 1 and  # 访问次数少
                    current_time - metrics.last_access_time > 3600 and  # 超过1小时未访问
                    metrics.priority == CachePriority.LOW  # 低优先级
                )

                if is_low_value:
                    low_value_keys.append(key)

            # 清理低价值缓存项
            for key in low_value_keys:
                try:
                    self.cache.delete(key)
                    if key in self._metrics:
                        del self._metrics[key]
                    if key in self._access_order:
                        del self._access_order[key]
                    if key in self._frequency_counter:
                        del self._frequency_counter[key]

                    cleaned_count += 1
                except Exception as e:
                    self.logger.warning(f"清理低价值缓存失败 {key}: {e}")

        return cleaned_count

    def _preload_high_value_data(self) -> int:
        """预加载高价值数据"""
        preloaded_count = 0

        # 基于访问模式预测需要预加载的数据
        for pattern in self.config.preload_patterns:
            try:
                # 调用预加载回调
                for callback in self._preload_callbacks:
                    try:
                        result = callback(pattern)
                        if result:
                            preloaded_count += 1
                    except Exception as e:
                        self.logger.warning(f"预加载回调执行失败: {e}")
            except Exception as e:
                self.logger.warning(f"预加载模式处理失败 {pattern}: {e}")

        return preloaded_count

    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        health = self.get_cache_health()
        stats = self.get_stats()

        # 计算性能指标
        total_operations = stats['hits'] + stats['misses'] + stats['sets'] + stats['deletes']
        operations_per_second = total_operations / max(health['uptime'], 1)

        return {
            'summary': {
                'health_score': health['health_score'],
                'health_status': health['health_status'],
                'hit_rate': health['hit_rate'],
                'operations_per_second': operations_per_second
            },
            'detailed_stats': stats,
            'health_analysis': health,
            'strategy_info': {
                'current_strategy': self.config.strategy.value,
                'strategy_switches': stats.get('strategy_switches', 0),
                'max_size': self.config.max_size,
                'compression_enabled': self.config.enable_compression
            },
            'recommendations': health['recommendations']
        }
