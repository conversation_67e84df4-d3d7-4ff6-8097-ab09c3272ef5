#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合数据获取器

{{ AURA-X: Add - 创建统一的HybridDataFetcher，支持同步和异步两种模式，保持接口兼容性. Source: 异步并发优化集成 }}

核心特性:
- 统一接口，支持同步/异步两种模式
- 自动模式选择和性能优化
- 完全向后兼容现有DataFetcher接口
- 深度集成异步优化组件
"""

import asyncio
import time
import logging
import threading
import concurrent.futures
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import pandas as pd

from src.data.fetcher.data_fetcher import DataFetcher
from src.data.fetcher.async_data_fetcher import AsyncDataFetcher, AsyncFetchConfig
from src.utils.config.config_factory import config_factory
from src.data.sources.data_source_interface import DataSourceInterface
from src.data.storage.storage_interface import StorageInterface
from src.data.storage.cache.cache_interface import CacheInterface
from src.data.processors.processor_interface import ProcessorInterface as DataProcessorInterface
from src.data.sources.data_source_factory import DataSourceFactory
from src.data.storage.storage_factory import StorageFactory
# {{ AURA-X: Add - 集成性能监控体系到数据获取流程. Approval: 寸止(ID:性能监控集成). }}
from src.monitoring.system_monitor import SystemMonitor


class AsyncEventLoopManager:
    """
    异步事件循环管理器

    解决事件循环冲突问题，提供稳定的异步执行环境
    """

    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化事件循环管理器

        参数:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        self._executor = None
        self._loop_thread = None
        self._loop = None
        self._shutdown = False
        self._lock = threading.Lock()
        self._loop_ready = threading.Event()

    def start(self):
        """启动事件循环管理器"""
        with self._lock:
            if self._executor is not None:
                return

            self.logger.info("🔄 启动异步事件循环管理器...")

            # 创建线程池执行器
            self._executor = concurrent.futures.ThreadPoolExecutor(
                max_workers=1,
                thread_name_prefix="AsyncEventLoop"
            )

            # 启动事件循环线程
            self._executor.submit(self._run_event_loop)

            # 等待事件循环启动
            if self._loop_ready.wait(timeout=10):
                self.logger.info("✅ 异步事件循环管理器启动成功")
            else:
                self.logger.error("❌ 异步事件循环管理器启动超时")
                self.shutdown()
                raise RuntimeError("事件循环管理器启动超时")

    def _run_event_loop(self):
        """在独立线程中运行事件循环"""
        try:
            # 创建新的事件循环
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)

            self.logger.info("🔄 事件循环线程启动")

            # 通知事件循环已准备就绪
            self._loop_ready.set()

            # 运行事件循环直到被关闭
            self._loop.run_forever()

        except Exception as e:
            self.logger.error(f"❌ 事件循环线程异常: {e}")
            raise
        finally:
            # 清理事件循环
            try:
                if self._loop and not self._loop.is_closed():
                    self._loop.close()
                self.logger.info("🧹 事件循环线程清理完成")
            except Exception as e:
                self.logger.error(f"❌ 事件循环清理异常: {e}")

    def run_async(self, coro):
        """
        在管理的事件循环中运行异步协程

        参数:
            coro: 异步协程

        返回:
            协程执行结果
        """
        if self._executor is None or self._shutdown or not self._loop:
            raise RuntimeError("事件循环管理器未启动或已关闭")

        # 在事件循环中执行协程
        future = asyncio.run_coroutine_threadsafe(coro, self._loop)
        return future.result()

    def shutdown(self):
        """关闭事件循环管理器"""
        with self._lock:
            if self._shutdown:
                return

            self.logger.info("🔄 关闭异步事件循环管理器...")
            self._shutdown = True

            try:
                # 停止事件循环
                if self._loop and not self._loop.is_closed():
                    self._loop.call_soon_threadsafe(self._loop.stop)

                # 关闭线程池
                if self._executor:
                    self._executor.shutdown(wait=True)

                self.logger.info("✅ 异步事件循环管理器关闭完成")

            except Exception as e:
                self.logger.error(f"❌ 关闭异步事件循环管理器异常: {e}")
            finally:
                self._executor = None
                self._loop = None


class HybridDataFetcher:
    """
    混合数据获取器
    
    {{ AURA-X: Add - 统一的数据获取器，支持同步和异步两种模式. Source: 异步并发优化集成 }}
    
    提供统一接口，内部根据配置自动选择最优的数据获取模式：
    - 同步模式：兼容现有代码，稳定可靠
    - 异步模式：高性能并发，适合大规模数据获取
    """
    
    def __init__(
        self,
        data_source: Optional[Union[DataSourceInterface, str]] = None,
        storage: Optional[Union[StorageInterface, str]] = None,
        calendar: Optional[Any] = None,
        cache: Optional[Union[CacheInterface, str]] = None,
        use_cache: bool = True,
        cache_config: Optional[dict] = None,
        processors: Optional[List[DataProcessorInterface]] = None,
        retry_config: Optional[Dict[str, Any]] = None,
        rate_limit_config: Optional[Dict[str, Any]] = None,
        batch_config: Optional[Dict[str, Any]] = None,
        # 新增异步相关配置
        async_mode: bool = False,
        auto_async: bool = True,
        async_threshold: int = 10,  # {{ AURA-X: Modify - 优化异步切换阈值，提升性能. Approval: 寸止(ID:异步优化). }} 超过此数量自动启用异步模式
        tushare_token: Optional[str] = None
    ):
        """
        初始化混合数据获取器
        
        参数:
            data_source: 数据源实例或类型名称
            storage: 存储实例或类型名称
            calendar: 交易日历实例
            cache: 缓存实例或类型名称
            use_cache: 是否使用缓存
            cache_config: 缓存配置
            processors: 数据处理器列表
            retry_config: 重试配置
            rate_limit_config: 频率限制配置
            batch_config: 批处理配置
            async_mode: 是否强制使用异步模式
            auto_async: 是否自动选择异步模式
            async_threshold: 自动启用异步模式的阈值
            tushare_token: Tushare API令牌
        """
        self.logger = logging.getLogger(__name__)

        # 存储配置参数
        self.async_mode = async_mode
        self.auto_async = auto_async
        self.async_threshold = async_threshold
        self.tushare_token = tushare_token

        # {{ AURA-X: Modify - 修复token传递问题，确保数据源正确获取token. Approval: 寸止(ID:阶段1.3). }}
        # 处理数据源创建，确保token正确传递
        if isinstance(data_source, str) and data_source == 'tushare' and tushare_token:
            from src.data.sources.data_source_factory import DataSourceFactory
            data_source = DataSourceFactory.create_tushare(token=tushare_token)

        # 初始化同步数据获取器
        self.sync_fetcher = DataFetcher(
            data_source=data_source,
            storage=storage,
            calendar=calendar,
            cache=cache,
            use_cache=use_cache,
            cache_config=cache_config,
            processors=processors,
            retry_config=retry_config,
            rate_limit_config=rate_limit_config,
            batch_config=batch_config
        )

        # 异步数据获取器（延迟初始化）
        self.async_fetcher = None
        self.event_loop_manager = None

        # 统计信息
        self.stats = {
            'sync_calls': 0,
            'async_calls': 0,
            'auto_async_triggers': 0,
            'total_records': 0,
            'total_time': 0.0,
            'total_time_sync': 0.0,
            'total_time_async': 0.0,
            'avg_throughput': 0.0
        }

        # {{ AURA-X: Add - 集成性能监控体系到数据获取流程. Approval: 寸止(ID:性能监控集成). }}
        # 性能监控器（可选）
        self.performance_monitor = None
        self.enable_monitoring = False

    @classmethod
    def from_app_config(
        cls,
        data_source: str = 'tushare',
        storage: str = 'sqlite',
        tushare_token: Optional[str] = None,
        use_cache: bool = True,
        auto_mode: bool = True,
        async_threshold: int = 10  # {{ AURA-X: Modify - 优化异步切换阈值，提升性能. Approval: 寸止(ID:异步优化). }}
    ) -> 'HybridDataFetcher':
        """
        从应用配置创建HybridDataFetcher

        Args:
            data_source: 数据源类型
            storage: 存储类型
            tushare_token: Tushare API token
            use_cache: 是否使用缓存
            auto_mode: 是否自动选择模式
            async_threshold: 异步模式阈值

        Returns:
            HybridDataFetcher: 配置好的数据获取器实例
        """
        # 获取应用配置
        try:
            app_config = config_factory.load_config('app')
        except Exception:
            # 如果加载失败，使用默认配置
            app_config = {}

        # 获取缓存配置
        cache_config = app_config.get('cache', {})

        # 确定缓存类型和配置
        if use_cache and cache_config:
            cache_strategy = cache_config.get('strategy', 'single')
            if cache_strategy == 'multi':
                # 使用多级缓存
                cache = 'multi'
            else:
                # 使用单级缓存，默认内存缓存
                cache = cache_config.get('type', 'memory')
        else:
            cache = None
            cache_config = None

        # 创建实例时需要传递tushare_token到数据源
        if data_source == 'tushare' and tushare_token:
            # 创建带token的Tushare数据源
            from src.data.sources.tushare_adapter import TushareAdapter
            data_source_instance = TushareAdapter(token=tushare_token)
        else:
            data_source_instance = data_source

        return cls(
            data_source=data_source_instance,
            storage=storage,
            cache=cache,
            cache_config=cache_config,
            tushare_token=tushare_token,
            use_cache=use_cache,
            auto_async=auto_mode,
            async_threshold=async_threshold
        )
        
        # 保存配置参数
        self.async_mode = async_mode
        self.auto_async = auto_async
        self.async_threshold = async_threshold
        self.tushare_token = tushare_token
        
        # 初始化同步数据获取器（始终可用）
        self.sync_fetcher = DataFetcher(
            data_source=data_source,
            storage=storage,
            calendar=calendar,
            cache=cache,
            use_cache=use_cache,
            cache_config=cache_config,
            processors=processors,
            retry_config=retry_config,
            rate_limit_config=rate_limit_config,
            batch_config=batch_config
        )

        # 设置Tushare token（如果提供）
        if self.tushare_token and hasattr(self.sync_fetcher.data_source, 'token'):
            self.sync_fetcher.data_source.token = self.tushare_token
        
        # 异步数据获取器（按需初始化）
        self.async_fetcher: Optional[AsyncDataFetcher] = None
        self._async_fetcher_initialized = False

        # 事件循环管理器
        self.loop_manager: Optional[AsyncEventLoopManager] = None

        # 性能统计
        self.stats = {
            'sync_calls': 0,
            'async_calls': 0,
            'auto_async_triggers': 0,
            'total_time_sync': 0.0,
            'total_time_async': 0.0,
            'total_records': 0
        }
        
        self.logger.info(f"HybridDataFetcher初始化完成: async_mode={async_mode}, auto_async={auto_async}")
    
    def _get_tushare_token(self) -> str:
        """获取Tushare API令牌"""
        if self.tushare_token:
            return self.tushare_token
        
        # 尝试从同步获取器的数据源获取token
        if hasattr(self.sync_fetcher.data_source, 'token'):
            return self.sync_fetcher.data_source.token
        
        # 尝试从配置文件获取
        try:
            from src.utils.config.config_factory import config_factory
            tushare_config = config_factory.get_tushare_config()
            api_config = tushare_config.get('api', {})
            return api_config.get('token', '')
        except Exception as e:
            self.logger.warning(f"无法获取Tushare token: {e}")
            return ''
    
    def _init_async_fetcher(self):
        """初始化异步数据获取器"""
        if self._async_fetcher_initialized:
            return
        
        try:
            # 获取Tushare token
            token = self._get_tushare_token()
            if not token:
                self.logger.warning("未找到Tushare token，异步模式可能无法正常工作")
            
            # 创建异步配置
            async_config = AsyncFetchConfig(
                tushare_token=token,
                batch_size=50,
                max_concurrent=100,
                tushare_timeout=30.0,
                tushare_max_retries=3,
                enable_monitoring=True
            )
            
            # 创建异步数据获取器
            self.async_fetcher = AsyncDataFetcher(
                config=async_config,
                storage=self.sync_fetcher.storage,
                logger=self.logger
            )

            # 初始化事件循环管理器
            if not hasattr(self, 'loop_manager'):
                self.loop_manager = None
            if not self.loop_manager:
                self.loop_manager = AsyncEventLoopManager(self.logger)
                self.loop_manager.start()

            # 使用事件循环管理器初始化异步连接
            async def init_connection():
                """异步连接初始化"""
                return await self.async_fetcher.tushare_adapter.connect()

            # 在管理的事件循环中执行连接
            connection_success = self.loop_manager.run_async(init_connection())

            if connection_success:
                self._async_fetcher_initialized = True
                self.logger.info("✅ 异步数据获取器初始化成功")
                self.logger.info("✅ 异步适配器连接验证成功")
            else:
                self.logger.error("❌ 异步适配器连接失败")
                self._async_fetcher_initialized = False

        except Exception as e:
            self.logger.error(f"❌ 异步数据获取器初始化失败: {e}")
            self.logger.error(f"异步初始化异常详情: {type(e).__name__}: {str(e)}")
            import traceback
            self.logger.error(f"异步初始化堆栈跟踪: {traceback.format_exc()}")
            self._async_fetcher_initialized = False

            # 清理失败的事件循环管理器
            if hasattr(self, 'loop_manager') and self.loop_manager:
                try:
                    self.loop_manager.shutdown()
                except Exception:
                    pass
                self.loop_manager = None
    
    def _should_use_async(self, symbols: Union[str, List[str]]) -> bool:
        """
        判断是否应该使用异步模式
        
        参数:
            symbols: 股票代码或代码列表
            
        返回:
            bool: 是否使用异步模式
        """
        # 强制异步模式
        if self.async_mode:
            return True
        
        # 禁用自动选择
        if not self.auto_async:
            return False
        
        # 根据股票数量自动选择
        if isinstance(symbols, str):
            symbol_count = 1
        else:
            symbol_count = len(symbols)
        
        should_async = symbol_count >= self.async_threshold
        
        if should_async:
            self.stats['auto_async_triggers'] += 1
            self.logger.info(f"自动启用异步模式: {symbol_count}只股票 >= {self.async_threshold}阈值")
        
        return should_async
    
    def fetch_market_data(
        self,
        symbols: Union[str, List[str]],
        data_type: str,
        start_date: Optional[Union[str, datetime]] = None,
        end_date: Optional[Union[str, datetime]] = None,
        fields: Optional[List[str]] = None,
        adjust: Optional[str] = None,
        save: bool = True,
        use_cache: Optional[bool] = None,
        enable_incremental: bool = True,
        **kwargs
    ) -> pd.DataFrame:
        """
        获取市场数据
        
        自动选择同步或异步模式，保持与DataFetcher完全相同的接口
        
        参数:
            symbols: 股票代码或代码列表
            data_type: 数据类型
            start_date: 开始日期
            end_date: 结束日期
            fields: 字段列表
            adjust: 复权类型
            save: 是否保存到存储
            use_cache: 是否使用缓存
            enable_incremental: 是否启用增量更新
            **kwargs: 其他参数
            
        返回:
            pd.DataFrame: 市场数据
        """
        start_time = time.time()

        # {{ AURA-X: Add - 集成性能监控体系到数据获取流程. Approval: 寸止(ID:性能监控集成). }}
        # 记录性能监控开始
        if self.enable_monitoring and self.performance_monitor:
            try:
                self.performance_monitor.record_operation_start('fetch_market_data', {
                    'symbols_count': len(symbols) if isinstance(symbols, list) else 1,
                    'data_type': data_type,
                    'start_date': str(start_date),
                    'end_date': str(end_date)
                })
            except Exception as e:
                self.logger.debug(f"性能监控记录失败: {e}")

        # 判断是否使用异步模式
        use_async = self._should_use_async(symbols)
        
        try:
            if use_async:
                # 使用异步模式
                self._init_async_fetcher()
                if self.async_fetcher:
                    self.stats['async_calls'] += 1
                    
                    # 转换参数格式
                    if isinstance(symbols, str):
                        symbols = [symbols]
                    
                    # 转换日期格式
                    start_date_str = self._format_date(start_date) if start_date else None
                    end_date_str = self._format_date(end_date) if end_date else None
                    
                    # 转换字段格式
                    fields_str = ','.join(fields) if fields else None
                    
                    # 使用事件循环管理器调用异步方法
                    async def fetch_data():
                        """异步数据获取"""
                        return await self.async_fetcher.fetch_market_data_async(
                            symbols=symbols,
                            data_type=data_type,
                            start_date=start_date_str,
                            end_date=end_date_str,
                            fields=fields_str,
                            save_to_storage=save,
                            **kwargs
                        )

                    # 在管理的事件循环中执行异步操作
                    result = self.loop_manager.run_async(fetch_data())
                    
                    fetch_time = time.time() - start_time
                    self.stats['total_time_async'] += fetch_time
                    
                    self.logger.info(f"✅ 异步模式获取完成: {len(result)}行, 耗时{fetch_time:.2f}s")
                    return result
                else:
                    self.logger.warning("异步获取器初始化失败，回退到同步模式")
                    use_async = False
            
            if not use_async:
                # 使用同步模式
                self.stats['sync_calls'] += 1
                
                result = self.sync_fetcher.fetch_market_data(
                    symbols=symbols,
                    data_type=data_type,
                    start_date=start_date,
                    end_date=end_date,
                    fields=fields,
                    adjust=adjust,
                    save=save,
                    use_cache=use_cache,
                    enable_incremental=enable_incremental,
                    **kwargs
                )
                
                fetch_time = time.time() - start_time
                self.stats['total_time_sync'] += fetch_time

                # {{ AURA-X: Add - 集成性能监控体系到数据获取流程. Approval: 寸止(ID:性能监控集成). }}
                # 记录性能监控结束
                if self.enable_monitoring and self.performance_monitor:
                    try:
                        operation_id = f"fetch_market_data_{int(start_time * 1000)}"
                        self.performance_monitor.record_operation_end(operation_id, {
                            'rows_fetched': len(result),
                            'duration': fetch_time,
                            'mode': 'sync',
                            'success': True
                        })
                    except Exception as e:
                        self.logger.debug(f"性能监控记录失败: {e}")

                self.logger.info(f"✅ 同步模式获取完成: {len(result)}行, 耗时{fetch_time:.2f}s")
                return result
                
        except Exception as e:
            self.logger.error(f"数据获取失败: {e}")
            raise
    
    def fetch_financial_data(
        self,
        symbols: Union[str, List[str]],
        report_type: str,
        start_date: Optional[Union[str, datetime]] = None,
        end_date: Optional[Union[str, datetime]] = None,
        fields: Optional[List[str]] = None,
        save: bool = True,
        use_cache: Optional[bool] = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        获取财务数据
        
        自动选择同步或异步模式，保持与DataFetcher完全相同的接口
        """
        start_time = time.time()
        
        # 判断是否使用异步模式
        use_async = self._should_use_async(symbols)
        
        try:
            if use_async:
                # 使用异步模式
                self._init_async_fetcher()
                if self.async_fetcher:
                    self.stats['async_calls'] += 1
                    
                    # 转换参数格式
                    if isinstance(symbols, str):
                        symbols = [symbols]
                    
                    # 转换日期格式
                    start_date_str = self._format_date(start_date) if start_date else None
                    end_date_str = self._format_date(end_date) if end_date else None
                    
                    # 转换字段格式
                    fields_str = ','.join(fields) if fields else None
                    
                    # 使用事件循环管理器调用异步方法
                    async def fetch_financial_data():
                        """异步财务数据获取"""
                        return await self.async_fetcher.fetch_financial_data_async(
                            symbols=symbols,
                            report_type=report_type,
                            start_date=start_date_str,
                            end_date=end_date_str,
                            fields=fields_str,
                            save_to_storage=save,
                            **kwargs
                        )

                    # 在管理的事件循环中执行异步操作
                    result = self.loop_manager.run_async(fetch_financial_data())
                    
                    fetch_time = time.time() - start_time
                    self.stats['total_time_async'] += fetch_time
                    
                    self.logger.info(f"✅ 异步模式财务数据获取完成: {len(result)}行, 耗时{fetch_time:.2f}s")
                    return result
                else:
                    self.logger.warning("异步获取器初始化失败，回退到同步模式")
                    use_async = False
            
            if not use_async:
                # 使用同步模式
                self.stats['sync_calls'] += 1
                
                result = self.sync_fetcher.fetch_financial_data(
                    symbols=symbols,
                    report_type=report_type,
                    start_date=start_date,
                    end_date=end_date,
                    fields=fields,
                    save=save,
                    use_cache=use_cache,
                    **kwargs
                )
                
                fetch_time = time.time() - start_time
                self.stats['total_time_sync'] += fetch_time
                
                self.logger.info(f"✅ 同步模式财务数据获取完成: {len(result)}行, 耗时{fetch_time:.2f}s")
                return result
                
        except Exception as e:
            self.logger.error(f"财务数据获取失败: {e}")
            raise
    
    def _format_date(self, date: Union[str, datetime]) -> str:
        """
        格式化日期
        
        参数:
            date: 日期对象或字符串
            
        返回:
            str: 格式化后的日期字符串 (YYYYMMDD)
        """
        if isinstance(date, str):
            # 如果已经是字符串，尝试标准化格式
            if len(date) == 8 and date.isdigit():
                return date
            elif len(date) == 10 and '-' in date:
                return date.replace('-', '')
            else:
                return date
        elif isinstance(date, datetime):
            return date.strftime('%Y%m%d')
        else:
            return str(date)

    # 代理其他DataFetcher方法，保持完全兼容性

    def fetch_reference_data(self, *args, **kwargs):
        """获取参考数据（代理到同步获取器）"""
        return self.sync_fetcher.fetch_reference_data(*args, **kwargs)

    def update_market_data(self, *args, **kwargs):
        """更新市场数据（代理到同步获取器）"""
        return self.sync_fetcher.update_market_data(*args, **kwargs)

    def get_cache(self):
        """获取缓存（代理到同步获取器）"""
        return self.sync_fetcher.get_cache()

    def set_cache(self, cache):
        """设置缓存（代理到同步获取器）"""
        self.sync_fetcher.set_cache(cache)

    def clear_cache(self):
        """清除缓存（代理到同步获取器）"""
        self.sync_fetcher.clear_cache()

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        返回:
            Dict[str, Any]: 性能统计数据
        """
        stats = dict(self.stats)

        # 计算平均时间
        if self.stats['sync_calls'] > 0:
            stats['avg_time_sync'] = self.stats['total_time_sync'] / self.stats['sync_calls']
        else:
            stats['avg_time_sync'] = 0.0

        if self.stats['async_calls'] > 0:
            stats['avg_time_async'] = self.stats['total_time_async'] / self.stats['async_calls']
        else:
            stats['avg_time_async'] = 0.0

        # 计算性能提升
        if stats['avg_time_sync'] > 0 and stats['avg_time_async'] > 0:
            stats['async_speedup'] = stats['avg_time_sync'] / stats['avg_time_async']
        else:
            stats['async_speedup'] = 1.0

        # 添加异步获取器统计
        if self.async_fetcher:
            stats['async_fetcher_stats'] = self.async_fetcher.get_performance_stats()

        # 添加同步获取器统计
        if hasattr(self.sync_fetcher, 'get_performance_stats'):
            stats['sync_fetcher_stats'] = self.sync_fetcher.get_performance_stats()

        return stats

    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'sync_calls': 0,
            'async_calls': 0,
            'auto_async_triggers': 0,
            'total_time_sync': 0.0,
            'total_time_async': 0.0,
            'total_records': 0
        }

        # 重置子获取器统计
        if self.async_fetcher:
            self.async_fetcher.reset_stats()

        if hasattr(self.sync_fetcher, 'reset_stats'):
            self.sync_fetcher.reset_stats()

    def set_async_mode(self, enabled: bool):
        """
        设置异步模式

        参数:
            enabled: 是否启用异步模式
        """
        self.async_mode = enabled
        self.logger.info(f"异步模式设置为: {enabled}")

    def set_auto_async(self, enabled: bool, threshold: int = None):
        """
        设置自动异步模式

        参数:
            enabled: 是否启用自动异步
            threshold: 自动启用异步的阈值
        """
        self.auto_async = enabled
        if threshold is not None:
            self.async_threshold = threshold

        self.logger.info(f"自动异步模式设置为: {enabled}, 阈值: {self.async_threshold}")

    def force_async_init(self):
        """强制初始化异步获取器"""
        self._init_async_fetcher()

    def enable_performance_monitoring(self, monitor_config: Optional[Dict[str, Any]] = None):
        """
        启用性能监控

        参数:
            monitor_config: 监控配置
        """
        # {{ AURA-X: Add - 集成性能监控体系到数据获取流程. Approval: 寸止(ID:性能监控集成). }}
        try:
            if not self.performance_monitor:
                self.performance_monitor = SystemMonitor()

            # 启动监控
            self.performance_monitor.start_monitoring()
            self.enable_monitoring = True

            self.logger.info("✅ 性能监控已启用")

        except Exception as e:
            self.logger.error(f"启用性能监控失败: {e}")
            self.enable_monitoring = False

    def disable_performance_monitoring(self):
        """禁用性能监控"""
        if self.performance_monitor:
            try:
                self.performance_monitor.stop_monitoring()
                self.enable_monitoring = False
                self.logger.info("✅ 性能监控已禁用")
            except Exception as e:
                self.logger.error(f"禁用性能监控失败: {e}")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标

        返回:
            Dict[str, Any]: 性能指标数据
        """
        if not self.performance_monitor or not self.enable_monitoring:
            return {}

        try:
            # 获取系统指标
            system_metrics = self.performance_monitor.get_current_metrics()

            # 获取数据库指标
            db_metrics = self.performance_monitor.get_database_metrics()

            # 合并指标
            return {
                'system': system_metrics,
                'database': db_metrics,
                'fetcher_stats': self.get_performance_stats(),
                'monitoring_enabled': self.enable_monitoring
            }

        except Exception as e:
            self.logger.error(f"获取性能指标失败: {e}")
            return {}
        return self._async_fetcher_initialized

    def is_async_available(self) -> bool:
        """检查异步模式是否可用"""
        # 安全检查：确保属性存在
        if not hasattr(self, '_async_fetcher_initialized'):
            self._async_fetcher_initialized = False

        if not self._async_fetcher_initialized:
            self._init_async_fetcher()
        return self._async_fetcher_initialized

    def get_mode_info(self) -> Dict[str, Any]:
        """
        获取当前模式信息

        返回:
            Dict[str, Any]: 模式信息
        """
        return {
            'async_mode': self.async_mode,
            'auto_async': self.auto_async,
            'async_threshold': self.async_threshold,
            'async_available': self.is_async_available(),
            'async_initialized': self._async_fetcher_initialized
        }

    # 支持异步上下文管理器

    async def __aenter__(self):
        """异步上下文管理器入口"""
        self._init_async_fetcher()
        if self.async_fetcher:
            await self.async_fetcher.__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.async_fetcher:
            await self.async_fetcher.__aexit__(exc_type, exc_val, exc_tb)

    def __del__(self):
        """
        析构函数，确保异步资源正确清理
        """
        try:
            # 清理事件循环管理器
            if hasattr(self, 'loop_manager') and self.loop_manager:
                try:
                    self.loop_manager.shutdown()
                except Exception:
                    pass

            # 清理异步获取器
            if hasattr(self, 'async_fetcher') and self.async_fetcher:
                try:
                    if hasattr(self.async_fetcher, 'tushare_adapter'):
                        adapter = self.async_fetcher.tushare_adapter
                        if hasattr(adapter, 'session') and adapter.session and not adapter.session.closed:
                            # 尝试在当前事件循环中清理
                            try:
                                loop = asyncio.get_running_loop()
                                if loop.is_running():
                                    loop.create_task(self._cleanup_async_resources())
                            except RuntimeError:
                                # 没有运行中的事件循环，创建新的循环来清理
                                try:
                                    asyncio.run(self._cleanup_async_resources())
                                except Exception:
                                    # 静默处理清理异常
                                    pass
                except Exception:
                    # 静默处理清理过程中的异常
                    pass
        except Exception:
            # 静默处理所有清理异常
            pass

    async def _cleanup_async_resources(self):
        """
        清理异步资源的内部方法
        """
        try:
            if self.async_fetcher and hasattr(self.async_fetcher, 'tushare_adapter'):
                adapter = self.async_fetcher.tushare_adapter
                if hasattr(adapter, 'disconnect'):
                    await adapter.disconnect()
        except Exception:
            # 静默处理清理过程中的异常
            pass

    async def cleanup_async(self):
        """
        公共的异步资源清理方法
        """
        await self._cleanup_async_resources()

    def cleanup_sync(self):
        """
        同步清理方法，供外部调用
        """
        try:
            if hasattr(self, 'async_fetcher') and self.async_fetcher:
                if hasattr(self.async_fetcher, 'tushare_adapter'):
                    adapter = self.async_fetcher.tushare_adapter
                    if hasattr(adapter, 'cleanup_sync'):
                        adapter.cleanup_sync()
        except Exception:
            # 静默处理清理过程中的异常
            pass

    # 异步方法接口

    async def fetch_market_data_async(
        self,
        symbols: List[str],
        data_type: str = 'daily',
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        fields: Optional[str] = None,
        save_to_storage: bool = True,
        **kwargs
    ) -> pd.DataFrame:
        """
        异步获取市场数据

        直接调用异步获取器，不进行模式判断
        """
        self._init_async_fetcher()
        if not self.async_fetcher:
            raise RuntimeError("异步获取器初始化失败")

        return await self.async_fetcher.fetch_market_data_async(
            symbols=symbols,
            data_type=data_type,
            start_date=start_date,
            end_date=end_date,
            fields=fields,
            save_to_storage=save_to_storage,
            **kwargs
        )

    async def fetch_financial_data_async(
        self,
        symbols: List[str],
        report_type: str = 'income',
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        fields: Optional[str] = None,
        save_to_storage: bool = True,
        **kwargs
    ) -> pd.DataFrame:
        """
        异步获取财务数据

        直接调用异步获取器，不进行模式判断
        """
        self._init_async_fetcher()
        if not self.async_fetcher:
            raise RuntimeError("异步获取器初始化失败")

        return await self.async_fetcher.fetch_financial_data_async(
            symbols=symbols,
            report_type=report_type,
            start_date=start_date,
            end_date=end_date,
            fields=fields,
            save_to_storage=save_to_storage,
            **kwargs
        )
