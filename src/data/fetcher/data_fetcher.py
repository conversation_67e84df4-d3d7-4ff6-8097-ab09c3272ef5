"""
统一数据获取器
负责整合多种数据源的数据获取能力，提供统一的API接口
"""
from typing import Dict, List, Optional, Union, Any, Tuple
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import inspect
import time
import os
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from src.data.sources.data_source_factory import DataSourceFactory
from src.data.sources.data_source_interface import DataSourceInterface
from src.data.processors.data_processor import DataProcessorInterface
from src.data.storage.storage_interface import StorageInterface
from src.data.storage.storage_factory import StorageFactory
from src.data.calendar.calendar_interface import CalendarInterface
from src.data.storage.cache.cache_interface import CacheInterface
from src.data.storage.storage_factory import StorageFactory
from src.data.fetcher.retry_handler import RetryHandler
from src.data.fetcher.rate_limiter import TokenBucketRateLimiter
from src.data.fetcher.batch_processor import BatchProcessor
from src.data.fetcher.incremental_updater import IncrementalUpdater
from src.data.fetcher.smart_cache_manager import SmartCacheManager

logger = logging.getLogger(__name__)

class DataFetcher:
    """统一数据获取器，作为数据获取的门面类，整合各种数据源和处理流程"""
    
    def __init__(
        self,
        data_source: Optional[Union[DataSourceInterface, str]] = None,
        storage: Optional[Union[StorageInterface, str]] = None,
        calendar: Optional[CalendarInterface] = None,
        cache: Optional[Union[CacheInterface, str]] = None,
        use_cache: bool = True,
        cache_config: Optional[dict] = None,
        processors: Optional[List[DataProcessorInterface]] = None,
        retry_config: Optional[Dict[str, Any]] = None,
        rate_limit_config: Optional[Dict[str, Any]] = None,
        batch_config: Optional[Dict[str, Any]] = None
    ):
        """
        初始化数据获取器
        
        Args:
            data_source: 数据源实例或数据源类型名称，如 'tushare', 'local_file', 'akshare'
            storage: 存储实例或存储类型名称，如 'sqlite', 'mysql', 'csv'
            calendar: 交易日历实例
            cache: 缓存实例或缓存类型名称，如 'memory', 'disk'
            use_cache: 是否使用缓存
            cache_config: 缓存配置，例如 {'ttl': 3600, 'clear': False}
            processors: 数据处理器列表
            retry_config: 重试配置，例如 {'max_retries': 3, 'retry_delay': 1}
            rate_limit_config: 频率限制配置，例如 {'calls': 500, 'period': 60}
            batch_config: 批处理配置，例如 {'batch_size': 100, 'parallelism': 5}
        """
        # 初始化数据源
        if isinstance(data_source, str):
            self.data_source = DataSourceFactory.create(data_source)
        else:
            self.data_source = data_source
            
        # 初始化存储
        if isinstance(storage, str):
            self.storage = StorageFactory.create(storage)
        else:
            self.storage = storage
            
        # 初始化缓存
        self.use_cache = use_cache
        if cache is not None:
            if isinstance(cache, str):
                # 如果是字符串，创建对应类型的缓存
                from src.data.storage.cache.cache_factory import CacheFactory

                if cache.lower() == 'multi':
                    # 多级缓存需要完整配置
                    if not cache_config:
                        raise ValueError("多级缓存需要提供cache_config配置")
                    self.cache = CacheFactory.create_cache('multi', cache_config)
                else:
                    # 单级缓存
                    config = cache_config if cache_config else {}
                    self.cache = CacheFactory.create_cache(cache.lower(), config)
            else:
                self.cache = cache
        elif cache_config is not None:
            # 从cache_config中提取缓存类型
            from src.data.storage.cache.cache_factory import CacheFactory

            cache_type = cache_config.get('type', 'memory')

            # 使用缓存工厂创建缓存
            try:
                self.cache = CacheFactory.create_cache(cache_type, cache_config)
            except Exception as e:
                self.logger.error(f"创建缓存失败: {e}")
                self.cache = None
        else:
            self.cache = None
            
        # 其他组件
        self.calendar = calendar
        self.processors = processors or []
        
        # 检测数据源是否实现了自己的重试机制和速率限制
        self._source_has_retry = self._check_source_has_retry()
        self._source_has_rate_limit = self._check_source_has_rate_limit()
        
        logger.info(f"数据源是否实现重试机制: {self._source_has_retry}")
        logger.info(f"数据源是否实现速率限制: {self._source_has_rate_limit}")
        
        # 初始化辅助工具，只在数据源未实现相应功能时才会使用
        if not self._source_has_retry:
            self.retry_handler = RetryHandler(**(retry_config or {}))
            logger.info(f"使用DataFetcher的重试机制")
        else:
            self.retry_handler = None
            logger.info(f"使用数据源的重试机制")
        
        # 为TokenBucketRateLimiter提供默认参数
        if not self._source_has_rate_limit:
            rate_limit_config = rate_limit_config or {}
            tokens_per_second = rate_limit_config.get('tokens_per_second') or 5.0  # 默认每秒5个请求
            bucket_size = rate_limit_config.get('bucket_size') or 20  # 默认20个令牌的桶
            self.rate_limiter = TokenBucketRateLimiter(tokens_per_second=tokens_per_second, bucket_size=bucket_size)
            logger.info(f"使用DataFetcher的速率限制")
        else:
            self.rate_limiter = None
            logger.info(f"使用数据源的速率限制")
        
        # 保存批处理配置，但不立即创建BatchProcessor实例
        self.batch_config = batch_config or {}
        self.batch_processor = None  # 将在需要时延迟初始化

        # 初始化智能增量更新器
        if self.storage:
            self.incremental_updater = IncrementalUpdater(self.storage)
        else:
            self.incremental_updater = None

        # 初始化智能缓存管理器
        if self.cache:
            # {{ AURA-X: Modify - 适配SmartCacheManager新的初始化方式. Approval: 寸止(ID:兼容性修复). }}
            from src.data.fetcher.smart_cache_manager import CacheConfig
            cache_ttl = cache_config.get('ttl', 3600) if cache_config else 3600
            config = CacheConfig(default_ttl=cache_ttl, enable_stats=True)
            self.smart_cache = SmartCacheManager(self.cache, config)
        else:
            self.smart_cache = None

        # 基础配置完成

    def _check_source_has_retry(self) -> bool:
        """
        检测数据源是否实现了自己的重试机制
        
        Returns:
            bool: 如果数据源实现了重试机制返回True，否则返回False
        """
        if self.data_source is None:
            return False
        
        # 检查数据源是否有retry_count或set_retry_count属性/方法
        has_retry_count = hasattr(self.data_source, 'retry_count')
        has_set_retry = hasattr(self.data_source, 'set_retry_count')
        
        # 检查源码中是否有retry相关实现
        if hasattr(self.data_source, '_call_api'):
            # 如果有_call_api方法，检查其实现是否包含retry逻辑
            source_code = inspect.getsource(self.data_source._call_api)
            if 'retry' in source_code.lower():
                return True
        
        return has_retry_count or has_set_retry
    
    def _check_source_has_rate_limit(self) -> bool:
        """
        检测数据源是否实现了自己的速率限制
        
        Returns:
            bool: 如果数据源实现了速率限制返回True，否则返回False
        """
        if self.data_source is None:
            return False
        
        # 检查数据源是否有rate_limit或相关属性/方法
        has_rate_limit = hasattr(self.data_source, 'rate_limit')
        has_rate_limit_methods = (
            hasattr(self.data_source, 'set_rate_limit') or
            hasattr(self.data_source, '_check_rate_limit') or
            hasattr(self.data_source, '_wait_for_rate_limit')
        )
        
        # 检查源码中是否有rate limit相关实现
        if hasattr(self.data_source, '_call_api'):
            source_code = inspect.getsource(self.data_source._call_api)
            if 'rate_limit' in source_code.lower() or 'wait' in source_code.lower():
                return True
        
        return has_rate_limit or has_rate_limit_methods
        
    def fetch_market_data(
        self,
        symbols: Union[str, List[str]],
        data_type: str,
        start_date: Optional[Union[str, datetime]] = None,
        end_date: Optional[Union[str, datetime]] = None,
        fields: Optional[List[str]] = None,
        adjust: Optional[str] = None,
        save: bool = True,
        use_cache: Optional[bool] = None,
        enable_incremental: bool = True,
        **kwargs
    ) -> pd.DataFrame:
        """
        获取市场数据，支持智能增量更新和缓存

        Args:
            symbols: 股票代码或代码列表
            data_type: 数据类型
            start_date: 开始日期
            end_date: 结束日期
            fields: 字段列表
            adjust: 复权类型
            save: 是否保存到存储
            use_cache: 是否使用缓存
            enable_incremental: 是否启用增量更新
            **kwargs: 其他参数

        Returns:
            pd.DataFrame: 获取的数据
        """
        import time
        import os

        # 检查参数
        if not self.data_source:
            raise ValueError("数据源未初始化")
        use_cache = use_cache if use_cache is not None else (self.cache is not None)
        if isinstance(symbols, str):
            symbols = [symbols]

        # 转换日期格式
        if start_date and isinstance(start_date, datetime):
            start_date = start_date.strftime('%Y%m%d')
        if end_date and isinstance(end_date, datetime):
            end_date = end_date.strftime('%Y%m%d')
        # 智能缓存检查（优先级最高）
        cache_key = None
        if use_cache and self.smart_cache:
            cache_key = self.smart_cache.generate_cache_key(
                data_type=data_type,
                symbols=symbols,
                start_date=start_date,
                end_date=end_date,
                fields=fields,
                extra_params={'adjust': adjust, **kwargs}
            )
            cached_data = self.smart_cache.get(cache_key)
            if cached_data is not None:
                logger.info(f"从智能缓存获取数据: {cache_key}")
                return cached_data

        # 智能增量更新检查（仅在缓存未命中时执行）
        symbols_to_fetch = symbols
        if enable_incremental and self.incremental_updater and start_date and end_date:
            try:
                # 获取需要更新的股票列表
                symbols_needing_update = self.incremental_updater.filter_symbols_needing_update(
                    symbols, data_type, start_date, end_date, min_completeness=0.95
                )

                if len(symbols_needing_update) < len(symbols):
                    logger.info(
                        f"增量更新：{len(symbols)} 支股票中，{len(symbols_needing_update)} 支需要更新"
                    )
                    symbols_to_fetch = symbols_needing_update

                    # 如果没有股票需要更新，从数据库获取现有数据并缓存
                    if not symbols_needing_update:
                        logger.info("所有股票数据都是最新的，从数据库获取")
                        if self.storage:
                            existing_data = self.storage.query(
                                table=data_type,
                                conditions={
                                    'ts_code': symbols,
                                    'trade_date': {'>=': start_date, '<=': end_date}
                                } if len(symbols) > 1 else {
                                    'ts_code': symbols[0],
                                    'trade_date': {'>=': start_date, '<=': end_date}
                                }
                            )

                            # 将从数据库获取的数据也缓存起来
                            if use_cache and self.smart_cache and cache_key and not existing_data.empty:
                                self.smart_cache.set(cache_key, existing_data, data_type=data_type)
                                logger.info(f"数据库数据已缓存: {cache_key}")

                            return existing_data if not existing_data.empty else pd.DataFrame()

            except Exception as e:
                logger.warning(f"增量更新检查失败，将获取全部数据: {e}")
                symbols_to_fetch = symbols
        # 如果没有股票需要获取，返回空DataFrame
        if not symbols_to_fetch:
            logger.info("没有股票需要获取数据")
            return pd.DataFrame()

        # 使用智能限流检测机制
        logger.info(f"数据获取: {len(symbols_to_fetch)}只股票, 类型: {data_type}")
        start_time = time.time()

        # 定义数据获取函数
        def fetch_func():
            return self.data_source.get_market_data(
                symbols=symbols_to_fetch,
                data_type=data_type,
                start_date=start_date,
                end_date=end_date,
                fields=fields,
                adjust=adjust,
                **kwargs
            )

        # 智能执行：根据数据源能力选择限流策略
        def execute_fetch():
            if self._source_has_retry and self._source_has_rate_limit:
                # 数据源已实现重试和速率限制，直接调用
                logger.debug("使用数据源内置的重试和限流机制")
                return fetch_func()
            elif self._source_has_retry:
                # 数据源已实现重试，只应用速率限制
                logger.debug("使用数据源重试 + DataFetcher限流")
                return self.rate_limiter.execute(fetch_func)
            elif self._source_has_rate_limit:
                # 数据源已实现速率限制，只应用重试
                logger.debug("使用DataFetcher重试 + 数据源限流")
                return self.retry_handler.execute(fetch_func)
            else:
                # 数据源都未实现，应用两者
                logger.debug("使用DataFetcher重试和限流")
                return self.retry_handler.execute(
                    lambda: self.rate_limiter.execute(fetch_func)
                )

        # 执行数据获取
        try:
            data = execute_fetch()
        except Exception as e:
            logger.error(f"数据获取失败: {e}")
            return pd.DataFrame()

        # 性能统计
        elapsed_time = time.time() - start_time
        total_rows = len(data) if not data.empty else 0

        if elapsed_time > 0:
            throughput = total_rows / elapsed_time
            logger.info(f"✅ 数据获取完成:")
            logger.info(f"   数据行数: {total_rows:,}")
            logger.info(f"   总耗时: {elapsed_time:.2f}秒")
            logger.info(f"   吞吐量: {throughput:.1f}行/秒")
        for processor in self.processors:
            data = processor.process(data)
        # 保存数据
        if save and self.storage:
            self.storage.save_market_data(data, data_type=data_type)

        # 智能缓存保存（使用之前生成的cache_key或重新生成）
        if use_cache and self.smart_cache and not data.empty:
            if cache_key is None:
                cache_key = self.smart_cache.generate_cache_key(
                    data_type=data_type,
                    symbols=symbols,
                    start_date=start_date,
                    end_date=end_date,
                    fields=fields,
                    extra_params={'adjust': adjust, **kwargs}
                )
            self.smart_cache.set(cache_key, data, data_type=data_type)
            logger.info(f"新获取数据已缓存: {cache_key}")

        return data
    
    def fetch_financial_data(
        self,
        symbols: Union[str, List[str]],
        report_type: str,
        start_date: Optional[Union[str, datetime]] = None,
        end_date: Optional[Union[str, datetime]] = None,
        fields: Optional[List[str]] = None,
        save: bool = True,
        use_cache: Optional[bool] = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        获取财务数据，自动补抓失败股票
        """
        # 检查参数
        if not self.data_source:
            raise ValueError("数据源未初始化")
        use_cache = use_cache if use_cache is not None else (self.cache is not None)
        if isinstance(symbols, str):
            symbols = [symbols]
        # 尝试从缓存获取
        if use_cache and self.cache:
            cache_key = self._generate_cache_key(
                "financial", symbols, report_type, start_date, end_date, fields, **kwargs
            )
            cached_data = self.cache.get(cache_key)
            if cached_data is not None:
                logger.info(f"从缓存获取数据: {cache_key}")
                return cached_data
        # 分批处理参数
        batch_size = 20
        batch_sleep = 8  # 每批之间sleep秒数
        per_req_sleep = 0.3  # 每只股票请求前sleep秒数
        max_retry = 2  # 最大补抓轮数（首轮+1次补抓）
        # {{ AURA-X: Modify - 扩展财务数据API映射，支持更多报表类型. Approval: 寸止(ID:财务数据修复). }}
        api_name_map = {
            'income': 'income',
            'balance': 'balancesheet',
            'cash_flow': 'cashflow',
            'balancesheet': 'balancesheet',
            'cashflow': 'cashflow',
            'annual': 'income',  # 年报默认使用利润表
            'quarterly': 'income',  # 季报默认使用利润表
            'fina_indicator': 'fina_indicator',  # 财务指标
            'express': 'express'  # 业绩快报
        }
        api_name = api_name_map.get(report_type, report_type)
        all_data = []
        failed_symbols = set(symbols)
        for retry_round in range(1, max_retry+1):
            if not failed_symbols:
                break
            logger.info(f"第{retry_round}轮抓取{api_name}数据，待抓取股票数: {len(failed_symbols)}")
            current_failed = set()
            total = len(failed_symbols)
            failed_symbols_list = list(failed_symbols)
            for batch_idx in range(0, total, batch_size):
                batch = failed_symbols_list[batch_idx:batch_idx+batch_size]
                batch_data = []
                for symbol in batch:
                    time.sleep(per_req_sleep)
                    try:
                        df = self.data_source.get_financial_data(
                            api_name=api_name,
                            ts_code=symbol,
                            start_date=start_date,
                            end_date=end_date,
                            **kwargs
                        )
                        if df is not None and not df.empty:
                            batch_data.append(df)
                        else:
                            current_failed.add(symbol)
                    except Exception as e:
                        logger.warning(f"获取{symbol}的{api_name}数据失败，已跳过: {e}")
                        current_failed.add(symbol)
                        continue
                if batch_data:
                    all_data.append(pd.concat(batch_data, ignore_index=True))
                logger.info(f"已处理{min(batch_idx+batch_size, total)}/{total}支股票的{api_name}数据（第{retry_round}轮）")
                if batch_idx + batch_size < total:
                    time.sleep(batch_sleep)
            failed_symbols = current_failed
        # 输出最终失败股票
        if failed_symbols:
            logger.warning(f"以下股票{api_name}数据抓取失败，建议后续人工补抓: {sorted(failed_symbols)}")
            try:
                os.makedirs('output', exist_ok=True)
                fail_path = f'output/failed_{api_name}_symbols.txt'
                with open(fail_path, 'w', encoding='utf-8') as f:
                    for symbol in sorted(failed_symbols):
                        f.write(symbol + '\n')
                logger.info(f"失败股票列表已输出到: {fail_path}")
            except Exception as e:
                logger.error(f"写入失败股票列表文件出错: {e}")
        if all_data:
            data = pd.concat(all_data, ignore_index=True)
        else:
            data = pd.DataFrame()
        for processor in self.processors:
            data = processor.process(data)
        if save and self.storage:
            self.storage.save_financial_data(data, report_type=report_type)
        if use_cache and self.cache:
            cache_key = self._generate_cache_key(
                "financial", symbols, report_type, start_date, end_date, fields, **kwargs
            )
            self.cache.set(cache_key, data)
        return data
    
    def fetch_reference_data(
        self,
        data_type: str,
        fields: Optional[List[str]] = None,
        conditions: Optional[Dict[str, Any]] = None,
        save: bool = True,
        use_cache: Optional[bool] = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        获取参考数据（如股票列表、交易日历等）
        
        Args:
            data_type: 数据类型，如 'stock_list', 'trade_calendar'
            fields: 字段列表
            conditions: 筛选条件
            save: 是否保存到存储
            use_cache: 是否使用缓存，如果为None则使用初始化时的设置
            **kwargs: 其他参数
            
        Returns:
            DataFrame: 获取的数据
        """
        # 检查参数
        if not self.data_source:
            raise ValueError("数据源未初始化")
            
        use_cache = use_cache if use_cache is not None else (self.cache is not None)
        
        # 尝试从缓存获取
        if use_cache and self.cache:
            cache_key = self._generate_cache_key(
                "reference", data_type, fields, conditions, **kwargs
            )
            cached_data = self.cache.get(cache_key)
            if cached_data is not None:
                logger.info(f"从缓存获取数据: {cache_key}")
                return cached_data
        
        # 定义数据获取函数
        def execute_fetch():
            # 根据数据源是否实现重试和速率限制，选择合适的数据获取方式
            fetch_func = lambda: self.data_source.get_reference_data(
                data_type=data_type,
                fields=fields,
                conditions=conditions,
                **kwargs
            )
            
            # 智能选择是否应用重试和速率限制
            if self._source_has_retry and self._source_has_rate_limit:
                # 数据源已实现重试和速率限制，直接调用
                return fetch_func()
            elif self._source_has_retry:
                # 数据源已实现重试，只应用速率限制
                return self.rate_limiter.execute(fetch_func)
            elif self._source_has_rate_limit:
                # 数据源已实现速率限制，只应用重试
                return self.retry_handler.execute(fetch_func)
            else:
                # 数据源都未实现，应用两者
                return self.retry_handler.execute(
                    lambda: self.rate_limiter.execute(fetch_func)
                )
        
        # 获取数据
        data = execute_fetch()
        
        # 应用处理器
        for processor in self.processors:
            data = processor.process(data)
            
        # 保存到存储
        if save and self.storage:
            self.storage.save_reference_data(data, data_type=data_type)
            
        # 保存到缓存
        if use_cache and self.cache:
            cache_key = self._generate_cache_key(
                "reference", data_type, fields, conditions, **kwargs
            )
            self.cache.set(cache_key, data)
            
        return data
        
    def load_from_storage(
        self,
        data_category: str,
        data_type: str,
        symbols: Optional[Union[str, List[str]]] = None,
        start_date: Optional[Union[str, datetime]] = None,
        end_date: Optional[Union[str, datetime]] = None,
        fields: Optional[List[str]] = None,
        conditions: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        从存储中加载数据
        
        Args:
            data_category: 数据类别，如 'market', 'financial', 'reference'
            data_type: 数据类型，如 'daily', 'income', 'stock_list'
            symbols: 股票代码或股票代码列表
            start_date: 起始日期
            end_date: 结束日期
            fields: 字段列表
            conditions: 筛选条件
            **kwargs: 其他参数
            
        Returns:
            DataFrame: 加载的数据
        """
        # 检查参数
        if not self.storage:
            raise ValueError("存储未初始化")
            
        # 标准化参数
        if isinstance(symbols, str):
            symbols = [symbols]
            
        # 根据数据类别选择加载方法
        if data_category == 'market':
            data = self.storage.load_market_data(
                symbols=symbols,
                data_type=data_type,
                start_date=start_date,
                end_date=end_date,
                fields=fields,
                **kwargs
            )
        elif data_category == 'financial':
            data = self.storage.load_financial_data(
                symbols=symbols,
                report_type=data_type,
                start_date=start_date,
                end_date=end_date,
                fields=fields,
                **kwargs
            )
        elif data_category == 'reference':
            data = self.storage.load_reference_data(
                data_type=data_type,
                fields=fields,
                conditions=conditions,
                **kwargs
            )
        else:
            raise ValueError(f"不支持的数据类别: {data_category}")
            
        return data
    
    def update_market_data(
        self,
        symbols: Union[str, List[str]],
        data_type: str,
        days: int = 30,
        end_date: Optional[Union[str, datetime]] = None,
        fields: Optional[List[str]] = None,
        adjust: Optional[str] = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        更新市场数据，通常用于增量更新最近的数据
        
        Args:
            symbols: 股票代码或股票代码列表
            data_type: 数据类型，如 'daily', 'minute', 'tick'
            days: 要更新的天数
            end_date: 结束日期，默认为今天
            fields: 字段列表
            adjust: 复权类型，如 'qfq', 'hfq', None
            **kwargs: 其他参数
            
        Returns:
            DataFrame: 更新后的数据
        """
        # 计算起始日期
        if end_date is None:
            end_date = datetime.now()
        elif isinstance(end_date, str):
            end_date = datetime.strptime(end_date, "%Y-%m-%d")
            
        start_date = end_date - timedelta(days=days)
        
        # 获取新数据
        new_data = self.fetch_market_data(
            symbols=symbols,
            data_type=data_type,
            start_date=start_date,
            end_date=end_date,
            fields=fields,
            adjust=adjust,
            save=True,
            **kwargs
        )
        
        return new_data
    
    def get_cache(self):
        return self.cache

    def set_cache(self, cache):
        self.cache = cache

    def clear_cache(self):
        if self.cache:
            self.cache.clear()

    def _generate_cache_key(self, *args, **kwargs) -> str:
        """
        生成缓存键
        
        Args:
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            str: 缓存键
        """
        # 统一参数化key生成
        key_parts = [str(arg) for arg in args]
        for k, v in sorted(kwargs.items()):
            if v is not None:
                key_parts.append(f"{k}={v}")
        return ":".join(key_parts)

    # 移除所有性能优化相关方法，回滚到原始版本

