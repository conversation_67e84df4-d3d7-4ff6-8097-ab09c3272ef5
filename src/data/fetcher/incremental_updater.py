"""
智能增量更新模块
实现基于已有数据的智能增量获取逻辑，避免重复获取数据
"""

import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional, Union, Any
from src.data.storage.storage_interface import StorageInterface


class IncrementalUpdater:
    """
    智能增量更新器
    
    功能：
    1. 检查数据库中已有的数据范围
    2. 计算需要获取的缺失时间段
    3. 实现数据完整性验证
    4. 支持断点续传功能
    """
    
    def __init__(self, storage: StorageInterface):
        """
        初始化增量更新器

        Args:
            storage: 存储接口实例
        """
        self.storage = storage
        self.logger = logging.getLogger(__name__)

    def _format_date_for_tushare(self, date_str: str) -> str:
        """
        将日期格式转换为Tushare格式

        Args:
            date_str: 日期字符串，支持多种格式

        Returns:
            str: Tushare格式日期 (YYYYMMDD)
        """
        try:
            # 如果已经是YYYYMMDD格式，直接返回
            if len(date_str) == 8 and date_str.isdigit():
                return date_str

            # 尝试解析YYYY-MM-DD格式
            if '-' in date_str:
                return date_str.replace('-', '')

            # 尝试其他格式
            for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%Y.%m.%d']:
                try:
                    dt = datetime.strptime(date_str, fmt)
                    return dt.strftime('%Y%m%d')
                except ValueError:
                    continue

            # 如果都不匹配，返回原字符串
            return date_str

        except Exception as e:
            self.logger.warning(f"日期格式转换失败: {date_str} -> {e}")
            return date_str
        
    def get_missing_date_ranges(
        self,
        symbols: List[str],
        data_type: str,
        start_date: str,
        end_date: str,
        table_name: Optional[str] = None
    ) -> Dict[str, List[Tuple[str, str]]]:
        """
        获取每个股票缺失的日期范围

        Args:
            symbols: 股票代码列表
            data_type: 数据类型
            start_date: 开始日期 (支持多种格式)
            end_date: 结束日期 (支持多种格式)
            table_name: 表名，如果为None则使用data_type

        Returns:
            Dict[str, List[Tuple[str, str]]]: 每个股票的缺失日期范围列表
        """
        table_name = table_name or data_type
        missing_ranges = {}

        # 转换日期格式
        start_date = self._format_date_for_tushare(start_date)
        end_date = self._format_date_for_tushare(end_date)
        
        try:
            # 检查表是否存在
            if not self.storage.table_exists(table_name):
                self.logger.info(f"表 {table_name} 不存在，需要获取全部数据")
                return {symbol: [(start_date, end_date)] for symbol in symbols}
            
            for symbol in symbols:
                try:
                    # 查询该股票的现有数据日期范围
                    existing_data = self.storage.query(
                        table=table_name,
                        conditions={'ts_code': symbol},
                        fields=['trade_date'],
                        order_by='trade_date'
                    )
                    
                    if existing_data.empty:
                        # 没有现有数据，需要获取全部
                        missing_ranges[symbol] = [(start_date, end_date)]
                    else:
                        # 计算缺失的日期范围
                        missing_ranges[symbol] = self._calculate_missing_ranges(
                            existing_data['trade_date'].tolist(),
                            start_date,
                            end_date
                        )
                        
                except Exception as e:
                    self.logger.warning(f"检查股票 {symbol} 的现有数据失败: {e}")
                    # 出错时保守处理，获取全部数据
                    missing_ranges[symbol] = [(start_date, end_date)]
                    
        except Exception as e:
            self.logger.error(f"获取缺失日期范围失败: {e}")
            # 出错时返回全部范围
            return {symbol: [(start_date, end_date)] for symbol in symbols}
            
        return missing_ranges
    
    def _calculate_missing_ranges(
        self,
        existing_dates: List[str],
        start_date: str,
        end_date: str
    ) -> List[Tuple[str, str]]:
        """
        计算缺失的日期范围
        
        Args:
            existing_dates: 已有的日期列表
            start_date: 目标开始日期
            end_date: 目标结束日期
            
        Returns:
            List[Tuple[str, str]]: 缺失的日期范围列表
        """
        if not existing_dates:
            return [(start_date, end_date)]
        
        # 转换为日期对象并排序
        existing_dates = sorted([
            datetime.strptime(str(date)[:8], '%Y%m%d') 
            for date in existing_dates
        ])
        
        start_dt = datetime.strptime(start_date, '%Y%m%d')
        end_dt = datetime.strptime(end_date, '%Y%m%d')
        
        missing_ranges = []
        
        # 检查开始日期之前的缺失
        if existing_dates[0] > start_dt:
            missing_end = existing_dates[0] - timedelta(days=1)
            missing_ranges.append((
                start_dt.strftime('%Y%m%d'),
                missing_end.strftime('%Y%m%d')
            ))
        
        # 检查中间的缺失
        for i in range(len(existing_dates) - 1):
            current_date = existing_dates[i]
            next_date = existing_dates[i + 1]
            
            # 如果两个日期之间有间隔（超过1天）
            if (next_date - current_date).days > 1:
                gap_start = current_date + timedelta(days=1)
                gap_end = next_date - timedelta(days=1)
                
                # 确保间隔在目标范围内
                if gap_start <= end_dt and gap_end >= start_dt:
                    actual_start = max(gap_start, start_dt)
                    actual_end = min(gap_end, end_dt)
                    missing_ranges.append((
                        actual_start.strftime('%Y%m%d'),
                        actual_end.strftime('%Y%m%d')
                    ))
        
        # 检查结束日期之后的缺失
        if existing_dates[-1] < end_dt:
            missing_start = existing_dates[-1] + timedelta(days=1)
            missing_ranges.append((
                missing_start.strftime('%Y%m%d'),
                end_dt.strftime('%Y%m%d')
            ))
        
        return missing_ranges
    
    def get_data_completeness_report(
        self,
        symbols: List[str],
        data_type: str,
        start_date: str,
        end_date: str,
        table_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取数据完整性报告
        
        Args:
            symbols: 股票代码列表
            data_type: 数据类型
            start_date: 开始日期
            end_date: 结束日期
            table_name: 表名
            
        Returns:
            Dict[str, Any]: 完整性报告
        """
        table_name = table_name or data_type

        # 转换日期格式
        start_date = self._format_date_for_tushare(start_date)
        end_date = self._format_date_for_tushare(end_date)

        report = {
            'total_symbols': len(symbols),
            'symbols_with_data': 0,
            'symbols_without_data': 0,
            'symbols_with_partial_data': 0,
            'overall_completeness': 0.0,
            'details': {}
        }
        
        try:
            if not self.storage.table_exists(table_name):
                report['symbols_without_data'] = len(symbols)
                return report
            
            for symbol in symbols:
                try:
                    existing_data = self.storage.query(
                        table=table_name,
                        conditions={'ts_code': symbol},
                        fields=['trade_date']
                    )
                    
                    if existing_data.empty:
                        report['symbols_without_data'] += 1
                        report['details'][symbol] = {
                            'status': 'no_data',
                            'completeness': 0.0,
                            'missing_ranges': [(start_date, end_date)]
                        }
                    else:
                        missing_ranges = self._calculate_missing_ranges(
                            existing_data['trade_date'].tolist(),
                            start_date,
                            end_date
                        )
                        
                        if not missing_ranges:
                            report['symbols_with_data'] += 1
                            status = 'complete'
                            completeness = 1.0
                        else:
                            report['symbols_with_partial_data'] += 1
                            status = 'partial'
                            # 简单的完整性计算（基于日期范围）
                            total_days = (datetime.strptime(end_date, '%Y%m%d') - 
                                        datetime.strptime(start_date, '%Y%m%d')).days + 1
                            missing_days = sum([
                                (datetime.strptime(end, '%Y%m%d') - 
                                 datetime.strptime(start, '%Y%m%d')).days + 1
                                for start, end in missing_ranges
                            ])
                            completeness = max(0.0, (total_days - missing_days) / total_days)
                        
                        report['details'][symbol] = {
                            'status': status,
                            'completeness': completeness,
                            'missing_ranges': missing_ranges,
                            'existing_records': len(existing_data)
                        }
                        
                except Exception as e:
                    self.logger.warning(f"检查股票 {symbol} 完整性失败: {e}")
                    report['details'][symbol] = {
                        'status': 'error',
                        'completeness': 0.0,
                        'error': str(e)
                    }
            
            # 计算总体完整性
            if report['total_symbols'] > 0:
                total_completeness = sum([
                    details.get('completeness', 0.0) 
                    for details in report['details'].values()
                ])
                report['overall_completeness'] = total_completeness / report['total_symbols']
                
        except Exception as e:
            self.logger.error(f"生成完整性报告失败: {e}")
            
        return report
    
    def filter_symbols_needing_update(
        self,
        symbols: List[str],
        data_type: str,
        start_date: str,
        end_date: str,
        min_completeness: float = 0.95,
        table_name: Optional[str] = None
    ) -> List[str]:
        """
        过滤出需要更新的股票列表

        Args:
            symbols: 股票代码列表
            data_type: 数据类型
            start_date: 开始日期 (支持多种格式)
            end_date: 结束日期 (支持多种格式)
            min_completeness: 最小完整性阈值
            table_name: 表名

        Returns:
            List[str]: 需要更新的股票代码列表
        """
        # 日期格式转换在get_data_completeness_report中处理
        report = self.get_data_completeness_report(
            symbols, data_type, start_date, end_date, table_name
        )
        
        symbols_needing_update = []
        for symbol, details in report['details'].items():
            completeness = details.get('completeness', 0.0)
            if completeness < min_completeness:
                symbols_needing_update.append(symbol)
        
        self.logger.info(
            f"在 {len(symbols)} 支股票中，{len(symbols_needing_update)} 支需要更新"
        )
        
        return symbols_needing_update
