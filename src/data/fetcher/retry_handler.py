"""
重试处理器
处理网络请求的重试逻辑
"""

import time
import random
import logging
import functools
from typing import Callable, Any, List, Optional, Type, Union, Tuple

# 导入标准库的calendar模块，使用绝对导入以避免混淆
import importlib
std_calendar = importlib.import_module('calendar')


class RetryHandler:
    """
    重试处理器
    
    处理网络请求的重试逻辑，支持自定义重试策略
    """
    
    def __init__(self, max_retries: int = 3, 
                 retry_delay: float = 1.0,
                 backoff_factor: float = 2.0,
                 jitter: float = 0.1,
                 retry_exceptions: List[Type[Exception]] = None,
                 retry_on_result: Callable[[Any], bool] = None):
        """
        初始化重试处理器
        
        参数:
            max_retries: 最大重试次数
            retry_delay: 初始重试延迟（秒）
            backoff_factor: 重试延迟的增长因子
            jitter: 随机抖动因子，防止同时重试
            retry_exceptions: 需要重试的异常类型列表
            retry_on_result: 根据结果判断是否需要重试的函数
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.backoff_factor = backoff_factor
        self.jitter = jitter
        
        # 默认重试的异常类型
        if retry_exceptions is None:
            import requests
            import urllib.error
            import socket
            self.retry_exceptions = [
                requests.exceptions.RequestException,
                requests.exceptions.ConnectionError,
                requests.exceptions.Timeout,
                urllib.error.URLError,
                urllib.error.HTTPError,
                socket.timeout,
                ConnectionError,
                TimeoutError
            ]
        else:
            self.retry_exceptions = retry_exceptions
        
        self.retry_on_result = retry_on_result
        self.logger = logging.getLogger(__name__)
    
    def __call__(self, func: Callable) -> Callable:
        """
        装饰器形式使用重试处理器
        
        参数:
            func: 要装饰的函数
            
        返回:
            装饰后的函数
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return self.execute(func, *args, **kwargs)
        return wrapper
    
    def execute(self, func: Callable, *args, **kwargs) -> Any:
        """
        执行函数并处理重试逻辑
        
        参数:
            func: 要执行的函数
            *args, **kwargs: 函数参数
            
        返回:
            函数的返回值
            
        异常:
            最后一次尝试的异常（如果全部失败）
        """
        last_exception = None
        delay = self.retry_delay
        
        for attempt in range(self.max_retries + 1):  # +1是因为第一次不算重试
            try:
                result = func(*args, **kwargs)
                
                # 检查结果是否需要重试
                if self.retry_on_result and self.retry_on_result(result):
                    if attempt < self.max_retries:
                        self.logger.warning(f"第 {attempt + 1} 次尝试返回需要重试的结果，将在 {delay:.2f} 秒后重试")
                        self._sleep(delay)
                        delay = self._get_next_delay(delay)
                        continue
                    else:
                        self.logger.warning(f"达到最大重试次数 {self.max_retries}，返回最后一次结果")
                
                return result
                
            except Exception as e:
                # 检查异常是否需要重试
                should_retry = False
                for retry_exc in self.retry_exceptions:
                    if isinstance(e, retry_exc):
                        should_retry = True
                        break
                
                if should_retry and attempt < self.max_retries:
                    last_exception = e
                    self.logger.warning(f"第 {attempt + 1} 次尝试失败: {str(e)}，将在 {delay:.2f} 秒后重试")
                    self._sleep(delay)
                    delay = self._get_next_delay(delay)
                else:
                    if attempt == self.max_retries:
                        self.logger.error(f"达到最大重试次数 {self.max_retries}，最后一次失败: {str(e)}")
                    raise e  # 重新抛出异常
        
        # 这里通常不会执行到，因为上面的循环会在成功时返回，
        # 或者在达到最大重试次数后抛出异常
        if last_exception:
            raise last_exception
    
    def _get_next_delay(self, current_delay: float) -> float:
        """
        计算下一次重试的延迟时间
        
        参数:
            current_delay: 当前延迟时间
            
        返回:
            下一次延迟时间
        """
        # 指数退避策略
        next_delay = current_delay * self.backoff_factor
        
        # 添加随机抖动
        if self.jitter > 0:
            jitter_amount = random.uniform(-self.jitter * next_delay, self.jitter * next_delay)
            next_delay += jitter_amount
        
        return max(0.001, next_delay)  # 确保延迟至少是一个很小的正数
    
    def _sleep(self, seconds: float):
        """
        睡眠指定的秒数
        
        参数:
            seconds: 睡眠时间（秒）
        """
        time.sleep(seconds)


def retry(max_retries: int = 3, 
          retry_delay: float = 1.0,
          backoff_factor: float = 2.0,
          jitter: float = 0.1,
          retry_exceptions: List[Type[Exception]] = None,
          retry_on_result: Callable[[Any], bool] = None):
    """
    重试装饰器的便捷函数
    
    参数:
        max_retries: 最大重试次数
        retry_delay: 初始重试延迟（秒）
        backoff_factor: 重试延迟的增长因子
        jitter: 随机抖动因子，防止同时重试
        retry_exceptions: 需要重试的异常类型列表
        retry_on_result: 根据结果判断是否需要重试的函数
        
    返回:
        装饰器函数
    """
    def decorator(func):
        handler = RetryHandler(
            max_retries=max_retries,
            retry_delay=retry_delay,
            backoff_factor=backoff_factor,
            jitter=jitter,
            retry_exceptions=retry_exceptions,
            retry_on_result=retry_on_result
        )
        return handler(func)
    
    return decorator

