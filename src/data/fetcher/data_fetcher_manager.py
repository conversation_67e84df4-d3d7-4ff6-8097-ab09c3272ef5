#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据获取器管理器

{{ AURA-X: Add - 创建统一的DataFetcherManager单例类，消除HybridDataFetcher重复初始化. Source: 代码整合优化阶段1.1 }}

核心特性:
- 线程安全的单例模式管理HybridDataFetcher实例
- 统一配置管理和全局访问接口
- 支持配置覆盖和缓存策略统一管理
- 完全兼容现有HybridDataFetcher接口
"""

import threading
import logging
import time
import hashlib
import json
from typing import Dict, Any, Optional, Union, Callable, List
from src.data.fetcher.hybrid_data_fetcher import HybridDataFetcher
from src.utils.config.config_factory import ConfigFactory


class DataFetcherManager:
    """
    增强的数据获取器管理器

    使用单例模式管理HybridDataFetcher实例，消除重复初始化，
    提供统一的配置管理和全局访问接口。

    新增特性:
    - 配置热重载和版本管理
    - 实例池管理和生命周期控制
    - 健康检查和自动恢复
    - 性能监控和统计分析
    """

    _instance = None
    _lock = threading.RLock()
    _fetcher = None
    _config_factory = None
    _logger = None

    # {{ AURA-X: Add - 增强配置管理和实例池功能. Approval: 寸止(ID:最优实践优化). }}
    # 新增管理功能
    _fetcher_pool: Dict[str, HybridDataFetcher] = {}
    _config_version = None
    _config_hash = None
    _last_health_check = 0
    _health_check_interval = 300  # 5分钟
    _performance_stats = {
        'total_requests': 0,
        'cache_hits': 0,
        'cache_misses': 0,
        'avg_response_time': 0,
        'error_count': 0,
        'last_error': None
    }
    _config_change_callbacks: List[Callable] = []
    
    def __new__(cls):
        """线程安全的单例实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(DataFetcherManager, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """初始化管理器（只执行一次）"""
        if self._initialized:
            return

        with self._lock:
            if self._initialized:
                return

            self._logger = logging.getLogger(__name__)
            self._config_factory = ConfigFactory()
            self._default_config = self._load_default_config()

            # {{ AURA-X: Add - 初始化增强功能. Approval: 寸止(ID:最优实践优化). }}
            # 计算配置哈希用于变更检测
            self._config_hash = self._calculate_config_hash(self._default_config)
            self._config_version = int(time.time())

            # 初始化性能统计
            self._performance_stats['start_time'] = time.time()

            self._initialized = True

            self._logger.info(f"DataFetcherManager单例初始化完成 (版本: {self._config_version})")
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        try:
            # 获取Tushare配置
            tushare_config = self._config_factory.get_tushare_config()
            api_config = tushare_config.get('api', {})
            token = api_config.get('token', '')
            
            # 加载应用配置获取缓存设置
            app_config = self._config_factory.load_config('app', 'config')
            cache_config = app_config.get('cache', {})
            
            # 构建默认配置
            default_config = {
                'data_source': 'tushare',
                'storage': 'sqlite',
                'tushare_token': token,
                'use_cache': True,
                'cache': cache_config.get('strategy', 'memory'),
                'cache_config': {
                    'ttl': cache_config.get('l1', {}).get('ttl', 3600)
                }
            }
            
            self._logger.info(f"默认配置加载成功: data_source={default_config['data_source']}, "
                            f"cache={default_config['cache']}, token={'已配置' if token else '未配置'}")
            
            return default_config
            
        except Exception as e:
            self._logger.error(f"加载默认配置失败: {e}")
            # 返回最小可用配置
            return {
                'data_source': 'tushare',
                'storage': 'sqlite',
                'use_cache': False
            }

    def _calculate_config_hash(self, config: Dict[str, Any]) -> str:
        """计算配置哈希值用于变更检测"""
        # {{ AURA-X: Add - 配置变更检测功能. Approval: 寸止(ID:最优实践优化). }}
        try:
            config_str = json.dumps(config, sort_keys=True, default=str)
            return hashlib.md5(config_str.encode()).hexdigest()
        except Exception as e:
            self._logger.warning(f"计算配置哈希失败: {e}")
            return str(hash(str(config)))

    def _check_config_changes(self) -> bool:
        """检查配置是否发生变更"""
        try:
            current_config = self._load_default_config()
            current_hash = self._calculate_config_hash(current_config)

            if current_hash != self._config_hash:
                self._logger.info("检测到配置变更，准备热重载")
                self._default_config = current_config
                self._config_hash = current_hash
                self._config_version = int(time.time())

                # 触发配置变更回调
                for callback in self._config_change_callbacks:
                    try:
                        callback(current_config)
                    except Exception as e:
                        self._logger.error(f"配置变更回调执行失败: {e}")

                return True
            return False
        except Exception as e:
            self._logger.error(f"配置变更检查失败: {e}")
            return False

    def _perform_health_check(self) -> Dict[str, Any]:
        """执行健康检查"""
        current_time = time.time()

        # 检查是否需要执行健康检查
        if current_time - self._last_health_check < self._health_check_interval:
            return {'status': 'skipped', 'reason': 'interval_not_reached'}

        self._last_health_check = current_time
        health_status = {
            'status': 'healthy',
            'timestamp': current_time,
            'checks': {}
        }

        try:
            # 检查配置变更
            config_changed = self._check_config_changes()
            health_status['checks']['config'] = {
                'status': 'changed' if config_changed else 'stable',
                'version': self._config_version,
                'hash': self._config_hash
            }

            # 检查实例状态
            if self._fetcher:
                try:
                    # 尝试获取统计信息来验证实例健康状态
                    stats = self._fetcher.get_stats() if hasattr(self._fetcher, 'get_stats') else {}
                    health_status['checks']['fetcher'] = {
                        'status': 'healthy',
                        'stats': stats
                    }
                except Exception as e:
                    health_status['checks']['fetcher'] = {
                        'status': 'unhealthy',
                        'error': str(e)
                    }
                    health_status['status'] = 'degraded'
            else:
                health_status['checks']['fetcher'] = {
                    'status': 'not_initialized'
                }

            # 检查实例池状态
            health_status['checks']['pool'] = {
                'size': len(self._fetcher_pool),
                'instances': list(self._fetcher_pool.keys())
            }

            return health_status

        except Exception as e:
            self._logger.error(f"健康检查执行失败: {e}")
            return {
                'status': 'error',
                'timestamp': current_time,
                'error': str(e)
            }

    def register_config_change_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """注册配置变更回调函数"""
        # {{ AURA-X: Add - 配置变更回调机制. Approval: 寸止(ID:最优实践优化). }}
        if callback not in self._config_change_callbacks:
            self._config_change_callbacks.append(callback)
            self._logger.info("配置变更回调已注册")

    def unregister_config_change_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """取消注册配置变更回调函数"""
        if callback in self._config_change_callbacks:
            self._config_change_callbacks.remove(callback)
            self._logger.info("配置变更回调已取消注册")

    def get_pool_status(self) -> Dict[str, Any]:
        """获取实例池状态"""
        with self._lock:
            return {
                'pool_size': len(self._fetcher_pool),
                'instances': {
                    key: {
                        'valid': self._validate_fetcher_instance(fetcher),
                        'type': type(fetcher).__name__
                    }
                    for key, fetcher in self._fetcher_pool.items()
                },
                'performance_stats': self._performance_stats.copy(),
                'config_version': self._config_version,
                'last_health_check': self._last_health_check
            }

    def cleanup_invalid_instances(self) -> int:
        """清理无效实例"""
        # {{ AURA-X: Add - 实例生命周期管理. Approval: 寸止(ID:最优实践优化). }}
        cleaned_count = 0
        with self._lock:
            invalid_keys = []

            for key, fetcher in self._fetcher_pool.items():
                if not self._validate_fetcher_instance(fetcher):
                    invalid_keys.append(key)

            for key in invalid_keys:
                del self._fetcher_pool[key]
                cleaned_count += 1
                self._logger.info(f"清理无效实例: {key}")

            # 如果默认实例被清理，重置引用
            if "default" in invalid_keys:
                self._fetcher = None

        return cleaned_count

    def force_reload_config(self) -> bool:
        """强制重新加载配置"""
        try:
            with self._lock:
                old_version = self._config_version
                self._default_config = self._load_default_config()
                self._config_hash = self._calculate_config_hash(self._default_config)
                self._config_version = int(time.time())

                # 清理所有实例以使用新配置
                self._fetcher_pool.clear()
                self._fetcher = None

                self._logger.info(f"配置强制重载完成: {old_version} -> {self._config_version}")

                # 触发配置变更回调
                for callback in self._config_change_callbacks:
                    try:
                        callback(self._default_config)
                    except Exception as e:
                        self._logger.error(f"配置变更回调执行失败: {e}")

                return True
        except Exception as e:
            self._logger.error(f"强制重载配置失败: {e}")
            return False

    @classmethod
    def get_instance(cls) -> 'DataFetcherManager':
        """获取管理器实例"""
        return cls()
    
    def get_fetcher(self, config_override: Optional[Dict[str, Any]] = None,
                   pool_key: Optional[str] = None) -> HybridDataFetcher:
        """
        获取HybridDataFetcher实例（增强版）

        参数:
            config_override: 配置覆盖参数
            pool_key: 实例池键名，用于管理多个实例

        返回:
            HybridDataFetcher: 数据获取器实例
        """
        # {{ AURA-X: Modify - 增强实例管理和健康检查. Approval: 寸止(ID:最优实践优化). }}
        start_time = time.time()

        with self._lock:
            # 执行健康检查
            health_status = self._perform_health_check()
            if health_status['status'] == 'error':
                self._logger.warning(f"健康检查失败: {health_status.get('error')}")

            # 确定实例键
            if pool_key:
                instance_key = pool_key
            elif config_override:
                # 为配置覆盖生成唯一键
                config_hash = self._calculate_config_hash(config_override)
                instance_key = f"override_{config_hash[:8]}"
            else:
                instance_key = "default"

            # 检查实例池中是否已存在
            if instance_key in self._fetcher_pool:
                existing_fetcher = self._fetcher_pool[instance_key]
                # 验证实例是否仍然有效
                if self._validate_fetcher_instance(existing_fetcher):
                    self._update_performance_stats('cache_hit', time.time() - start_time)
                    return existing_fetcher
                else:
                    # 移除无效实例
                    del self._fetcher_pool[instance_key]
                    self._logger.info(f"移除无效实例: {instance_key}")

            # 创建新实例
            config = self._default_config.copy()

            # 应用配置覆盖
            if config_override:
                config.update(config_override)
                self._logger.debug(f"应用配置覆盖: {config_override}")

            # 创建新的HybridDataFetcher实例
            try:
                new_fetcher = HybridDataFetcher(**config)

                # 默认启用性能监控
                if config.get('enable_monitoring', True):
                    new_fetcher.enable_performance_monitoring()

                # 存储到实例池
                self._fetcher_pool[instance_key] = new_fetcher

                # 更新默认实例引用
                if instance_key == "default":
                    self._fetcher = new_fetcher

                self._logger.info(f"HybridDataFetcher实例创建成功: key={instance_key}, cache={config.get('cache', 'none')}")
                self._update_performance_stats('cache_miss', time.time() - start_time)

                return new_fetcher

            except Exception as e:
                self._logger.error(f"创建HybridDataFetcher实例失败: {e}")
                self._update_performance_stats('error', time.time() - start_time, str(e))
                raise

    def _validate_fetcher_instance(self, fetcher: HybridDataFetcher) -> bool:
        """验证数据获取器实例是否有效"""
        try:
            # 简单的健康检查
            if not hasattr(fetcher, 'sync_fetcher'):
                return False

            # 检查是否有基本的获取能力
            if not hasattr(fetcher, 'fetch_market_data'):
                return False

            return True
        except Exception as e:
            self._logger.debug(f"实例验证失败: {e}")
            return False

    def _update_performance_stats(self, event_type: str, response_time: float, error: str = None):
        """更新性能统计"""
        self._performance_stats['total_requests'] += 1

        if event_type == 'cache_hit':
            self._performance_stats['cache_hits'] += 1
        elif event_type == 'cache_miss':
            self._performance_stats['cache_misses'] += 1
        elif event_type == 'error':
            self._performance_stats['error_count'] += 1
            self._performance_stats['last_error'] = error

        # 更新平均响应时间
        current_avg = self._performance_stats['avg_response_time']
        total_requests = self._performance_stats['total_requests']
        self._performance_stats['avg_response_time'] = (
            (current_avg * (total_requests - 1) + response_time) / total_requests
        )
    
    def get_fetcher_for_testing(self, disable_cache: bool = True) -> HybridDataFetcher:
        """
        获取用于测试的数据获取器实例
        
        参数:
            disable_cache: 是否禁用缓存
            
        返回:
            HybridDataFetcher: 测试用数据获取器实例
        """
        test_config = {
            'use_cache': not disable_cache,
            'cache': None if disable_cache else 'memory'
        }
        return self.get_fetcher(test_config)
    
    def get_fetcher_with_cache(self, cache_type: str = 'memory', 
                              cache_config: Optional[Dict[str, Any]] = None) -> HybridDataFetcher:
        """
        获取指定缓存类型的数据获取器实例
        
        参数:
            cache_type: 缓存类型 ('memory', 'redis', 'multi', 'disk')
            cache_config: 缓存配置
            
        返回:
            HybridDataFetcher: 配置了指定缓存的数据获取器实例
        """
        config = {
            'cache': cache_type,
            'use_cache': True
        }
        
        if cache_config:
            config['cache_config'] = cache_config
        
        return self.get_fetcher(config)
    
    def reset_fetcher(self, pool_key: Optional[str] = None) -> None:
        """重置数据获取器实例（用于配置更新后的重新初始化）"""
        # {{ AURA-X: Modify - 增强重置功能，支持实例池管理. Approval: 寸止(ID:最优实践优化). }}
        with self._lock:
            if pool_key:
                # 重置特定实例
                if pool_key in self._fetcher_pool:
                    del self._fetcher_pool[pool_key]
                    self._logger.info(f"重置实例: {pool_key}")

                    # 如果重置的是默认实例，清空引用
                    if pool_key == "default":
                        self._fetcher = None
            else:
                # 重置所有实例
                if self._fetcher_pool:
                    self._logger.info(f"重置所有实例: {len(self._fetcher_pool)} 个")
                    self._fetcher_pool.clear()

                if self._fetcher:
                    self._logger.info("重置默认HybridDataFetcher实例")
                    self._fetcher = None
    
    def get_stats(self, include_pool_stats: bool = True) -> Dict[str, Any]:
        """
        获取数据获取器统计信息（增强版）

        参数:
            include_pool_stats: 是否包含实例池统计

        返回:
            Dict[str, Any]: 统计信息
        """
        # {{ AURA-X: Modify - 增强统计信息，包含管理器级别的统计. Approval: 寸止(ID:最优实践优化). }}
        stats = {
            'manager_stats': self._performance_stats.copy(),
            'config_version': self._config_version,
            'uptime': time.time() - self._performance_stats.get('start_time', time.time())
        }

        # 默认实例统计
        if self._fetcher and hasattr(self._fetcher, 'get_stats'):
            stats['default_fetcher_stats'] = self._fetcher.get_stats()

        # 实例池统计
        if include_pool_stats:
            pool_stats = {}
            for key, fetcher in self._fetcher_pool.items():
                if hasattr(fetcher, 'get_stats'):
                    pool_stats[key] = fetcher.get_stats()
                else:
                    pool_stats[key] = {'status': 'no_stats_available'}

            stats['pool_stats'] = pool_stats
            stats['pool_summary'] = {
                'total_instances': len(self._fetcher_pool),
                'valid_instances': sum(1 for f in self._fetcher_pool.values()
                                     if self._validate_fetcher_instance(f))
            }

        return stats
    
    def update_config(self, new_config: Dict[str, Any]) -> None:
        """
        更新默认配置
        
        参数:
            new_config: 新的配置参数
        """
        with self._lock:
            self._default_config.update(new_config)
            self._logger.info(f"默认配置已更新: {new_config}")
            # 重置实例以应用新配置
            self.reset_fetcher()


# {{ AURA-X: Modify - 增强全局访问函数. Approval: 寸止(ID:最优实践优化). }}
# 全局实例访问函数
def get_data_fetcher(config_override: Optional[Dict[str, Any]] = None,
                    pool_key: Optional[str] = None) -> HybridDataFetcher:
    """
    获取全局数据获取器实例的便捷函数（增强版）

    参数:
        config_override: 配置覆盖参数
        pool_key: 实例池键名

    返回:
        HybridDataFetcher: 数据获取器实例
    """
    manager = DataFetcherManager.get_instance()
    return manager.get_fetcher(config_override, pool_key)


def get_test_data_fetcher(disable_cache: bool = True) -> HybridDataFetcher:
    """
    获取测试用数据获取器实例的便捷函数
    
    参数:
        disable_cache: 是否禁用缓存
        
    返回:
        HybridDataFetcher: 测试用数据获取器实例
    """
    manager = DataFetcherManager.get_instance()
    return manager.get_fetcher_for_testing(disable_cache)


def get_cached_data_fetcher(cache_type: str = 'memory',
                           cache_config: Optional[Dict[str, Any]] = None) -> HybridDataFetcher:
    """
    获取指定缓存类型的数据获取器实例的便捷函数

    参数:
        cache_type: 缓存类型
        cache_config: 缓存配置

    返回:
        HybridDataFetcher: 配置了指定缓存的数据获取器实例
    """
    manager = DataFetcherManager.get_instance()
    return manager.get_fetcher_with_cache(cache_type, cache_config)


# {{ AURA-X: Add - 新增管理功能的便捷函数. Approval: 寸止(ID:最优实践优化). }}
def get_manager_stats(include_pool: bool = True) -> Dict[str, Any]:
    """
    获取数据获取器管理器统计信息的便捷函数

    参数:
        include_pool: 是否包含实例池统计

    返回:
        Dict[str, Any]: 管理器统计信息
    """
    manager = DataFetcherManager.get_instance()
    return manager.get_stats(include_pool)


def cleanup_data_fetcher_instances() -> int:
    """
    清理无效数据获取器实例的便捷函数

    返回:
        int: 清理的实例数量
    """
    manager = DataFetcherManager.get_instance()
    return manager.cleanup_invalid_instances()


def reload_data_fetcher_config() -> bool:
    """
    重新加载数据获取器配置的便捷函数

    返回:
        bool: 是否重载成功
    """
    manager = DataFetcherManager.get_instance()
    return manager.force_reload_config()


def get_data_fetcher_pool_status() -> Dict[str, Any]:
    """
    获取数据获取器实例池状态的便捷函数

    返回:
        Dict[str, Any]: 实例池状态信息
    """
    manager = DataFetcherManager.get_instance()
    return manager.get_pool_status()


def register_data_fetcher_config_callback(callback: Callable[[Dict[str, Any]], None]):
    """
    注册数据获取器配置变更回调的便捷函数

    参数:
        callback: 配置变更回调函数
    """
    manager = DataFetcherManager.get_instance()
    manager.register_config_change_callback(callback)
