"""
数据获取模块
提供数据获取、批处理、重试和频率限制等功能
"""

# {{ AURA-X: Modify - 整合数据获取器接口，以HybridDataFetcher作为主要接口. Source: 代码重构阶段二功能整合 }}

# 主要数据获取器接口
from src.data.fetcher.hybrid_data_fetcher import HybridDataFetcher
from src.data.fetcher.data_fetcher_manager import (
    DataFetcherManager,
    get_data_fetcher,
    get_test_data_fetcher,
    get_cached_data_fetcher
)

# 底层组件（保留用于内部使用和测试）
from src.data.fetcher.data_fetcher import DataFetcher
from src.data.fetcher.async_data_fetcher import AsyncDataFetcher, AsyncFetchConfig

# 工具组件
from src.data.fetcher.batch_processor import BatchProcessor, BatchResult
from src.data.fetcher.retry_handler import RetryHandler, retry
from src.data.fetcher.rate_limiter import RateLimiter, TokenBucketRateLimiter, MultiRateLimiter

# 专用数据获取器（基于HybridDataFetcher构建）
from src.data.fetcher.market_data_fetcher import MarketData
from src.data.fetcher.financial_data_fetcher import FinancialData

__all__ = [
    # 主要接口
    'HybridDataFetcher',

    # 底层实现（用于内部使用）
    'DataFetcher',
    'AsyncDataFetcher',
    'AsyncFetchConfig',

    # 工具组件
    'BatchProcessor',
    'BatchResult',
    'RetryHandler',
    'retry',
    'RateLimiter',
    'TokenBucketRateLimiter',
    'MultiRateLimiter',

    # 专用获取器
    'MarketData',
    'FinancialData',
]
