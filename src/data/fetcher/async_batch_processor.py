#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步批处理器

{{ AURA-X: Add - 实现高性能异步批处理器，支持动态批量大小调整、并行批次处理、错误隔离机制. Source: 异步并发优化第一阶段 }}

核心特性:
- 动态批量大小调整
- 并行批次处理
- 错误隔离机制
- 流式数据处理
- 智能重试策略
"""

import asyncio
import time
import logging
from typing import List, Dict, Any, Optional, Callable, TypeVar, Generic, Union, AsyncIterator
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor
import random
from enum import Enum

T = TypeVar('T')  # 输入类型
R = TypeVar('R')  # 输出类型


class BatchStrategy(Enum):
    """批处理策略"""
    FIXED = "fixed"           # 固定批次大小
    ADAPTIVE = "adaptive"     # 自适应批次大小
    DYNAMIC = "dynamic"       # 动态批次大小


@dataclass
class AsyncBatchResult(Generic[T, R]):
    """异步批处理结果"""
    item: T
    result: Optional[R] = None
    success: bool = False
    error: Optional[str] = None
    processing_time: float = 0.0
    retry_count: int = 0
    batch_id: str = ""


@dataclass
class AsyncBatchConfig:
    """异步批处理配置"""
    batch_size: int = 50                    # 基础批次大小
    max_concurrent_batches: int = 10        # 最大并发批次数
    max_concurrent_items: int = 100         # 最大并发项目数
    strategy: BatchStrategy = BatchStrategy.ADAPTIVE  # 批处理策略
    
    # 自适应配置
    min_batch_size: int = 10               # 最小批次大小
    max_batch_size: int = 200              # 最大批次大小
    performance_threshold: float = 2.0      # 性能阈值（秒）
    
    # 重试配置
    max_retries: int = 3                   # 最大重试次数
    retry_delay: float = 1.0               # 重试延迟
    backoff_factor: float = 2.0            # 退避因子
    
    # 错误处理
    error_isolation: bool = True           # 错误隔离
    failure_threshold: float = 0.3         # 失败阈值（30%）
    
    # 性能监控
    enable_monitoring: bool = True         # 启用性能监控
    stats_interval: int = 100              # 统计间隔


class AsyncBatchProcessor(Generic[T, R]):
    """
    高性能异步批处理器
    
    {{ AURA-X: Add - 实现高性能异步批处理器，预期提升3-5倍性能. Source: 异步并发优化第一阶段 }}
    """
    
    def __init__(
        self,
        processor_func: Callable[[T], R],
        config: Optional[AsyncBatchConfig] = None,
        logger: Optional[logging.Logger] = None
    ):
        """
        初始化异步批处理器
        
        参数:
            processor_func: 处理函数
            config: 批处理配置
            logger: 日志记录器
        """
        self.processor_func = processor_func
        self.config = config or AsyncBatchConfig()
        self.logger = logger or logging.getLogger(__name__)
        
        # 性能统计
        self.stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'retried': 0,
            'total_batches': 0,
            'total_time': 0.0,
            'avg_batch_time': 0.0,
            'current_batch_size': self.config.batch_size
        }
        
        # 性能监控
        self.recent_batch_times = []
        self.recent_success_rates = []
        
        # 并发控制
        self.batch_semaphore = asyncio.Semaphore(self.config.max_concurrent_batches)
        self.item_semaphore = asyncio.Semaphore(self.config.max_concurrent_items)
    
    async def process_async(self, items: List[T]) -> List[AsyncBatchResult[T, R]]:
        """
        异步处理数据列表
        
        参数:
            items: 要处理的数据列表
            
        返回:
            List[AsyncBatchResult]: 处理结果列表
        """
        if not items:
            return []
        
        start_time = time.time()
        self.logger.info(f"🚀 开始异步批处理: {len(items)}个项目")
        
        # 动态调整批次大小
        current_batch_size = self._get_optimal_batch_size(len(items))
        
        # 创建批次
        batches = self._create_batches(items, current_batch_size)
        
        # 并发处理所有批次
        batch_tasks = []
        for batch_id, batch in enumerate(batches):
            task = self._process_batch_async(batch, f"batch_{batch_id}")
            batch_tasks.append(task)
        
        # 等待所有批次完成
        batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
        
        # 合并结果
        all_results = []
        for batch_result in batch_results:
            if isinstance(batch_result, Exception):
                self.logger.error(f"❌ 批次处理异常: {batch_result}")
                continue
            all_results.extend(batch_result)
        
        # 更新统计信息
        total_time = time.time() - start_time
        self._update_stats(all_results, total_time)
        
        # 性能报告
        successful = sum(1 for r in all_results if r.success)
        failed = len(all_results) - successful
        
        self.logger.info(f"✅ 异步批处理完成: 总计{len(all_results)}项, 成功{successful}项, 失败{failed}项, 耗时{total_time:.2f}s")
        
        return all_results
    
    async def _process_batch_async(
        self, 
        batch: List[T], 
        batch_id: str
    ) -> List[AsyncBatchResult[T, R]]:
        """
        异步处理单个批次
        
        参数:
            batch: 批次数据
            batch_id: 批次ID
            
        返回:
            List[AsyncBatchResult]: 批次处理结果
        """
        async with self.batch_semaphore:
            start_time = time.time()
            
            # 创建项目处理任务
            item_tasks = []
            for item in batch:
                task = self._process_item_async(item, batch_id)
                item_tasks.append(task)
            
            # 并发处理批次中的所有项目
            results = await asyncio.gather(*item_tasks, return_exceptions=True)
            
            # 处理异常结果
            batch_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    batch_results.append(AsyncBatchResult(
                        item=batch[i],
                        success=False,
                        error=str(result),
                        batch_id=batch_id
                    ))
                else:
                    batch_results.append(result)
            
            # 批次性能监控
            batch_time = time.time() - start_time
            success_rate = sum(1 for r in batch_results if r.success) / len(batch_results)
            
            self.logger.debug(f"📦 批次{batch_id}完成: {len(batch)}项, 成功率{success_rate:.1%}, 耗时{batch_time:.2f}s")
            
            # 更新性能监控数据
            if self.config.enable_monitoring:
                self.recent_batch_times.append(batch_time)
                self.recent_success_rates.append(success_rate)
                
                # 保持最近20次记录
                if len(self.recent_batch_times) > 20:
                    self.recent_batch_times.pop(0)
                    self.recent_success_rates.pop(0)
            
            return batch_results
    
    async def _process_item_async(
        self, 
        item: T, 
        batch_id: str, 
        retry_count: int = 0
    ) -> AsyncBatchResult[T, R]:
        """
        异步处理单个项目
        
        参数:
            item: 要处理的项目
            batch_id: 批次ID
            retry_count: 重试次数
            
        返回:
            AsyncBatchResult: 处理结果
        """
        async with self.item_semaphore:
            start_time = time.time()
            
            try:
                # 如果处理函数是异步的
                if asyncio.iscoroutinefunction(self.processor_func):
                    result = await self.processor_func(item)
                else:
                    # 在线程池中运行同步函数
                    loop = asyncio.get_event_loop()
                    with ThreadPoolExecutor(max_workers=1) as executor:
                        result = await loop.run_in_executor(executor, self.processor_func, item)
                
                processing_time = time.time() - start_time
                
                return AsyncBatchResult(
                    item=item,
                    result=result,
                    success=True,
                    processing_time=processing_time,
                    retry_count=retry_count,
                    batch_id=batch_id
                )
                
            except Exception as e:
                processing_time = time.time() - start_time
                
                # 重试逻辑
                if retry_count < self.config.max_retries:
                    retry_delay = self.config.retry_delay * (self.config.backoff_factor ** retry_count)
                    self.logger.warning(f"⚠️ 项目处理失败，{retry_delay:.1f}s后重试 ({retry_count+1}/{self.config.max_retries}): {e}")
                    
                    await asyncio.sleep(retry_delay)
                    return await self._process_item_async(item, batch_id, retry_count + 1)
                
                return AsyncBatchResult(
                    item=item,
                    success=False,
                    error=str(e),
                    processing_time=processing_time,
                    retry_count=retry_count,
                    batch_id=batch_id
                )
    
    def _get_optimal_batch_size(self, total_items: int) -> int:
        """
        获取最优批次大小
        
        参数:
            total_items: 总项目数
            
        返回:
            int: 最优批次大小
        """
        if self.config.strategy == BatchStrategy.FIXED:
            return self.config.batch_size
        
        elif self.config.strategy == BatchStrategy.ADAPTIVE:
            # 基于历史性能调整
            if len(self.recent_batch_times) >= 5:
                avg_time = sum(self.recent_batch_times) / len(self.recent_batch_times)
                avg_success_rate = sum(self.recent_success_rates) / len(self.recent_success_rates)
                
                current_size = self.stats['current_batch_size']
                
                # 如果性能良好，增加批次大小
                if avg_time < self.config.performance_threshold and avg_success_rate > 0.9:
                    new_size = min(current_size * 1.2, self.config.max_batch_size)
                # 如果性能较差，减少批次大小
                elif avg_time > self.config.performance_threshold * 1.5 or avg_success_rate < 0.8:
                    new_size = max(current_size * 0.8, self.config.min_batch_size)
                else:
                    new_size = current_size
                
                self.stats['current_batch_size'] = int(new_size)
                return int(new_size)
        
        elif self.config.strategy == BatchStrategy.DYNAMIC:
            # 基于总项目数动态调整
            if total_items <= 100:
                return min(20, total_items)
            elif total_items <= 1000:
                return min(50, total_items // 10)
            else:
                return min(100, total_items // 20)
        
        return self.config.batch_size
    
    def _create_batches(self, items: List[T], batch_size: int) -> List[List[T]]:
        """
        创建批次
        
        参数:
            items: 项目列表
            batch_size: 批次大小
            
        返回:
            List[List[T]]: 批次列表
        """
        batches = []
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            batches.append(batch)
        return batches
    
    def _update_stats(self, results: List[AsyncBatchResult[T, R]], total_time: float):
        """
        更新统计信息
        
        参数:
            results: 处理结果列表
            total_time: 总处理时间
        """
        successful = sum(1 for r in results if r.success)
        failed = len(results) - successful
        retried = sum(r.retry_count for r in results)
        
        self.stats['total_processed'] += len(results)
        self.stats['successful'] += successful
        self.stats['failed'] += failed
        self.stats['retried'] += retried
        self.stats['total_batches'] += 1
        self.stats['total_time'] += total_time
        
        if self.stats['total_batches'] > 0:
            self.stats['avg_batch_time'] = self.stats['total_time'] / self.stats['total_batches']
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        返回:
            Dict[str, Any]: 统计信息
        """
        total = self.stats['total_processed']
        if total == 0:
            return dict(self.stats)
        
        stats = dict(self.stats)
        stats['success_rate'] = (self.stats['successful'] / total) * 100
        stats['failure_rate'] = (self.stats['failed'] / total) * 100
        stats['avg_processing_time'] = self.stats['total_time'] / total
        
        return stats
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'retried': 0,
            'total_batches': 0,
            'total_time': 0.0,
            'avg_batch_time': 0.0,
            'current_batch_size': self.config.batch_size
        }
        self.recent_batch_times = []
        self.recent_success_rates = []
