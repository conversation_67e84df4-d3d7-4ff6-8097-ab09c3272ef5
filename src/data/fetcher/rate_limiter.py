"""
频率限制处理
"""

import time
import logging
import threading
from typing import Dict, Any, Optional, Callable, List

from collections import deque
from statistics import mean, median

# {{ AURA-X: Add - 监控可视化增强集成. Source: 最优实践监控可视化 }}
try:
    from src.monitoring.rate_limiter_dashboard import record_api_call
    DASHBOARD_AVAILABLE = True
except ImportError:
    DASHBOARD_AVAILABLE = False


class RateLimiter:
    """
    速率限制器
    
    控制请求频率，确保不超过API限制
    """
    
    def __init__(self, max_calls: int, period: float, retry_after: float = 1.0):
        """
        初始化速率限制器
        
        参数:
            max_calls: 在period时间内允许的最大请求数
            period: 时间周期（秒）
            retry_after: 当超过限制时，重试前等待的时间（秒）
        """
        self.max_calls = max_calls
        self.period = period
        self.retry_after = retry_after
        
        self._call_times = []  # 记录请求时间
        self._lock = threading.RLock()  # 线程锁
        self.logger = logging.getLogger(__name__)
    
    def __call__(self, func: Callable) -> Callable:
        """
        装饰器形式使用速率限制器
        
        参数:
            func: 要装饰的函数
            
        返回:
            装饰后的函数
        """
        def wrapper(*args, **kwargs):
            self.wait_if_needed()
            return func(*args, **kwargs)
        return wrapper
    
    def wait_if_needed(self):
        """
        如果需要，等待直到可以进行下一次请求
        """
        with self._lock:
            # 清理过期的请求记录
            current_time = time.time()
            cutoff_time = current_time - self.period
            self._call_times = [t for t in self._call_times if t >= cutoff_time]
            
            # 检查是否超过限制
            while len(self._call_times) >= self.max_calls:
                # 计算需要等待的时间
                oldest_call = self._call_times[0]
                wait_time = oldest_call + self.period - current_time
                
                if wait_time <= 0:
                    # 已经过了足够的时间，可以移除最老的请求记录
                    self._call_times.pop(0)
                else:
                    # 需要等待
                    self.logger.debug(f"速率限制: 等待 {wait_time:.2f} 秒")
                    
                    # 释放锁，避免阻塞其他线程
                    self._lock.release()
                    time.sleep(max(wait_time, self.retry_after))
                    self._lock.acquire()
                    
                    # 重新检查时间
                    current_time = time.time()
                    cutoff_time = current_time - self.period
                    self._call_times = [t for t in self._call_times if t >= cutoff_time]
            
            # 添加新请求记录
            self._call_times.append(time.time())
    
    def reset(self):
        """
        重置速率限制器
        """
        with self._lock:
            self._call_times = []


class TokenBucketRateLimiter:
    """
    令牌桶速率限制器
    
    使用令牌桶算法控制请求频率
    """
    
    def __init__(self, tokens_per_second: float, bucket_size: Optional[float] = None):
        """
        初始化令牌桶速率限制器
        
        参数:
            tokens_per_second: 每秒产生的令牌数量
            bucket_size: 令牌桶容量，默认为tokens_per_second的2倍
        """
        self.tokens_per_second = tokens_per_second
        self.bucket_size = bucket_size or (tokens_per_second * 2)
        self._tokens = self.bucket_size  # 初始令牌数量为满桶
        self._last_update = time.time()
        self._lock = threading.RLock()
        self.logger = logging.getLogger(__name__)
    
    def execute(self, func: Callable, *args, **kwargs) -> Any:
        """
        执行函数，在执行前应用速率限制
        
        参数:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        返回:
            函数执行结果
        """
        self.acquire_wait()
        return func(*args, **kwargs)
    
    def __call__(self, func: Callable) -> Callable:
        """
        装饰器形式使用速率限制器
        
        参数:
            func: 要装饰的函数
            
        返回:
            装饰后的函数
        """
        def wrapper(*args, **kwargs):
            self.acquire_wait()
            return func(*args, **kwargs)
        return wrapper
    
    def acquire(self, tokens: int = 1) -> bool:
        """
        尝试获取指定数量的令牌，如果没有足够令牌则返回False
        
        参数:
            tokens: 需要的令牌数量
            
        返回:
            bool: 是否成功获取令牌
        """
        with self._lock:
            self._add_new_tokens()
            
            if self._tokens >= tokens:
                self._tokens -= tokens
                return True
            
            return False
    
    def acquire_wait(self, tokens: int = 1) -> float:
        """
        获取指定数量的令牌，如果没有足够令牌则等待
        
        参数:
            tokens: 需要的令牌数量
            
        返回:
            float: 等待的时间（秒）
        """
        wait_time = 0.0
        with self._lock:
            self._add_new_tokens()
            
            # 计算需要等待的时间
            if self._tokens < tokens:
                needed_tokens = tokens - self._tokens
                wait_time = needed_tokens / self.tokens_per_second
                
                self.logger.debug(f"令牌桶限制: 等待 {wait_time:.2f} 秒")
                
                # 释放锁，避免阻塞其他线程
                self._lock.release()
                time.sleep(wait_time)
                self._lock.acquire()
                
                # 重新计算令牌
                self._add_new_tokens()
            
            # 获取令牌
            self._tokens -= tokens
            return wait_time
    
    def wait_if_needed(self, tokens: int = 1):
        """
        如果需要，等待直到有足够的令牌可用
        
        参数:
            tokens: 需要的令牌数量
        """
        self.acquire_wait(tokens)
    
    def _add_new_tokens(self):
        """
        根据时间流逝添加新令牌
        """
        current_time = time.time()
        time_passed = current_time - self._last_update
        
        # 计算新令牌数量
        new_tokens = time_passed * self.tokens_per_second
        
        if new_tokens > 0:
            self._tokens = min(self._tokens + new_tokens, self.bucket_size)
            self._last_update = current_time
    
    def reset(self):
        """
        重置令牌桶
        """
        with self._lock:
            self._tokens = self.bucket_size
            self._last_update = time.time()


class MultiRateLimiter:
    """
    多层速率限制器
    
    可以同时应用多个不同的速率限制策略
    如：每秒最多5次请求，每分钟最多100次请求，每天最多2000次请求
    """
    
    def __init__(self, limiters=None):
        """
        初始化多层速率限制器
        
        参数:
            limiters: RateLimiter对象列表
        """
        self.limiters = limiters or []
        self.logger = logging.getLogger(__name__)
    
    def add_limiter(self, limiter):
        """
        添加速率限制器
        
        参数:
            limiter: RateLimiter对象
        """
        self.limiters.append(limiter)
    
    def __call__(self, func: Callable) -> Callable:
        """
        装饰器形式使用速率限制器
        
        参数:
            func: 要装饰的函数
            
        返回:
            装饰后的函数
        """
        def wrapper(*args, **kwargs):
            self.wait_if_needed()
            return func(*args, **kwargs)
        return wrapper
    
    def wait_if_needed(self):
        """
        如果需要，等待直到可以进行下一次请求
        """
        for limiter in self.limiters:
            limiter.wait_if_needed()
    
    def reset(self):
        """
        重置所有速率限制器
        """
        for limiter in self.limiters:
            limiter.reset()


class AdaptiveRateLimiter:
    """
    自适应智能限流系统

    {{ AURA-X: Add - 重构实现自适应智能限流系统，遵循单一职责原则. Source: 架构优化重构方案 }}

    根据实时API响应时间动态调整限流策略，提供更智能的限流控制
    """

    def __init__(self, base_limiter: RateLimiter, logger: Optional[logging.Logger] = None):
        """
        初始化轻量化自适应限流器

        {{ AURA-X: Modify - 增加API特定限流策略和智能渐进式恢复. Source: 短期优化方案实施 }}

        参数:
            base_limiter: 基础限流器
            logger: 日志记录器
        """
        self.base_limiter = base_limiter
        self.logger = logger or logging.getLogger(__name__)

        # 轻量化性能监控数据（移除线程锁，减少内存使用）
        self.recent_response_times = []  # 最近20次响应时间（简化）
        self.success_count = 0  # 成功计数
        self.total_count = 0    # 总计数

        # {{ AURA-X: Add - 算法精度优化：EWMA和百分位统计. Source: 最优实践算法精度优化 }}
        self.ewma_alpha = 0.3  # EWMA平滑因子
        self.ewma_response_time = None  # 指数加权移动平均响应时间
        self.response_time_variance = 0.0  # 响应时间方差
        self.percentile_buffer = []  # 百分位统计缓冲区（最近100次）

        # {{ AURA-X: Add - 智能配置和性能预测. Source: 最优实践智能配置 }}
        self.performance_history = deque(maxlen=1000)  # 性能历史数据
        self.prediction_model = None  # 性能预测模型
        self.auto_tuning_enabled = True  # 自动调优开关
        self.learning_rate = 0.1  # 学习率
        self.prediction_accuracy = 0.0  # 预测准确率

        # {{ AURA-X: Add - API特定配置，针对不同API的差异化限流策略. Source: 短期优化方案1 }}
        self.api_specific_configs = {
            'cashflow': {
                'timeout_threshold': 30.0,      # cashflow API超时阈值30秒
                'adjustment_interval': 30,      # 快速调整间隔30秒
                'recovery_threshold': 10,       # 连续10次成功才能升级
                'max_congested_time': 300       # 最大congested状态持续5分钟
            },
            'default': {
                'timeout_threshold': 5.0,       # 默认超时阈值5秒
                'adjustment_interval': 60,      # 默认调整间隔60秒
                'recovery_threshold': 5,        # 连续5次成功才能升级
                'max_congested_time': 600       # 最大congested状态持续10分钟
            }
        }

        # {{ AURA-X: Modify - 优化性能等级阈值，启用渐进式恢复. Source: 短期优化方案2 }}
        self.performance_levels = {
            'excellent': {'threshold': 0.3, 'multiplier': 1.5},    # 响应时间<0.3s，提升50%
            'good': {'threshold': 0.8, 'multiplier': 1.2},         # 响应时间<0.8s，提升20%
            'normal': {'threshold': 1.5, 'multiplier': 1.0},       # 响应时间<1.5s，保持不变
            'slow': {'threshold': 3.0, 'multiplier': 0.85},        # 响应时间<3.0s，降低15%
            'congested': {'threshold': float('inf'), 'multiplier': 0.7}  # 响应时间>=3.0s，降低30%
        }

        # {{ AURA-X: Add - 智能渐进式恢复状态管理. Source: 短期优化方案2 }}
        self.current_level = 'normal'
        self.last_adjustment_time = time.time()
        self.adjustment_interval = 60  # 默认调整间隔
        self.consecutive_success_count = 0  # 连续成功计数
        self.congested_start_time = None    # congested状态开始时间
        self.level_transition_history = []  # 等级变化历史（最近10次）

    def record_response_time(self, api_name: str, response_time: float, success: bool = True):
        """
        轻量化记录API响应时间和成功状态

        {{ AURA-X: Modify - 增加API特定智能处理和渐进式恢复逻辑. Source: 短期优化方案实施 }}
        """
        import time

        # 轻量化记录（无锁设计）
        self.recent_response_times.append(response_time)

        # 保持最近20次记录（简化内存使用）
        if len(self.recent_response_times) > 20:
            self.recent_response_times.pop(0)

        # {{ AURA-X: Add - EWMA算法精度优化. Source: 最优实践算法精度优化 }}
        # 更新指数加权移动平均
        if self.ewma_response_time is None:
            self.ewma_response_time = response_time
        else:
            self.ewma_response_time = (self.ewma_alpha * response_time +
                                     (1 - self.ewma_alpha) * self.ewma_response_time)

        # 更新响应时间方差（用于波动性分析）
        if len(self.recent_response_times) > 1:
            mean_time = sum(self.recent_response_times) / len(self.recent_response_times)
            variance = sum((t - mean_time) ** 2 for t in self.recent_response_times) / len(self.recent_response_times)
            self.response_time_variance = variance

        # 更新百分位统计缓冲区
        self.percentile_buffer.append(response_time)
        if len(self.percentile_buffer) > 100:
            self.percentile_buffer.pop(0)

        # {{ AURA-X: Add - 性能历史记录和智能学习. Source: 最优实践智能配置 }}
        # 记录性能历史数据
        performance_record = {
            'timestamp': time.time(),
            'api_name': api_name,
            'response_time': response_time,
            'success': success,
            'level': self.current_level,
            'multiplier': self.performance_levels[self.current_level]['multiplier']
        }
        self.performance_history.append(performance_record)

        # 智能配置调优
        if self.auto_tuning_enabled and len(self.performance_history) >= 50:
            self._intelligent_tuning(api_name)

        # {{ AURA-X: Add - 集成监控仪表板记录. Source: 最优实践监控可视化 }}
        # 记录到监控仪表板
        if DASHBOARD_AVAILABLE:
            try:
                record_api_call(
                    api_name=api_name,
                    response_time=response_time,
                    success=success,
                    level=self.current_level,
                    multiplier=self.performance_levels[self.current_level]['multiplier'],
                    total_calls=self.total_count
                )
            except Exception as e:
                # 监控记录失败不应影响主要功能
                self.logger.debug(f"监控仪表板记录失败: {e}")

        # {{ AURA-X: Add - 智能渐进式恢复逻辑. Source: 短期优化方案2 }}
        # 更新连续成功计数
        if success:
            self.consecutive_success_count += 1
        else:
            self.consecutive_success_count = 0  # 失败时重置计数

        # 简化成功率统计
        self.total_count += 1
        if success:
            self.success_count += 1

        # {{ AURA-X: Add - API特定异常检测. Source: 短期优化方案1 }}
        # 获取API特定配置
        api_config = self.api_specific_configs.get(api_name, self.api_specific_configs['default'])

        # 检测API特定的异常响应时间
        if response_time > api_config['timeout_threshold']:
            self.logger.warning(f"🚨 {api_name} API异常响应时间: {response_time:.2f}s (阈值: {api_config['timeout_threshold']}s)")

            # 如果是cashflow API异常，立即触发congested状态
            if api_name == 'cashflow' and response_time > 100:  # 超过100秒认为是严重异常
                self.logger.error(f"💥 {api_name} API严重超时: {response_time:.2f}s，立即降级到congested")
                self._force_level_change('congested')

        # {{ AURA-X: Add - 详细性能日志记录. Source: 详细日志分析任务 }}
        # 记录每次API调用的详细信息
        current_time = time.time()
        current_level = self.current_level
        multiplier = self.performance_levels[current_level]['multiplier']

        # 详细日志记录到文件
        self.logger.info(f"API_CALL_DETAIL: api={api_name}, response_time={response_time:.4f}s, "
                        f"success={success}, level={current_level}, multiplier={multiplier:.2f}, "
                        f"timestamp={current_time:.3f}, total_calls={self.total_count}")

        # 减少控制台日志输出频率
        if self.total_count % 100 == 0:  # 每100次记录一次日志
            success_rate = (self.success_count / self.total_count) * 100
            avg_time = sum(self.recent_response_times) / len(self.recent_response_times)
            self.logger.debug(f"API性能统计: 平均响应时间={avg_time:.3f}s, 成功率={success_rate:.1f}%")

    def _force_level_change(self, new_level: str):
        """
        强制改变性能等级（用于异常情况）

        {{ AURA-X: Add - 强制等级变化方法，支持异常情况下的立即降级. Source: 短期优化方案1 }}
        """
        import time

        if new_level == 'congested':
            self.congested_start_time = time.time()

        old_level = self.current_level
        self.current_level = new_level
        self.consecutive_success_count = 0  # 重置连续成功计数

        # 记录强制等级变化
        self._record_level_change(old_level, new_level, forced=True)

    def get_current_performance_level(self) -> str:
        """
        智能渐进式性能等级计算

        {{ AURA-X: Modify - 增加渐进式恢复逻辑和API特定处理. Source: 短期优化方案2 }}
        """
        if len(self.recent_response_times) < 5:
            return 'normal'

        # {{ AURA-X: Modify - 使用EWMA代替简单平均，提升算法精度. Source: 最优实践算法精度优化 }}
        # 使用指数加权移动平均，对最新数据给予更高权重
        if self.ewma_response_time is not None:
            recent_avg = self.ewma_response_time
        else:
            # 回退到简单平均值
            recent_avg = sum(self.recent_response_times) / len(self.recent_response_times)

        # {{ AURA-X: Add - 智能渐进式恢复逻辑. Source: 短期优化方案2 }}
        # 如果当前是congested状态，需要更严格的恢复条件
        if self.current_level == 'congested':
            # 检查是否满足恢复条件
            if recent_avg < 1.0 and self.consecutive_success_count >= 10:
                # 渐进式恢复：congested → slow
                return 'slow'
            else:
                # 检查是否超过最大congested时间，强制恢复
                if self.congested_start_time and (time.time() - self.congested_start_time) > 300:  # 5分钟
                    self.logger.warning("🔄 congested状态超过5分钟，强制恢复到slow等级")
                    return 'slow'
                return 'congested'

        # {{ AURA-X: Add - 渐进式等级提升逻辑. Source: 短期优化方案2 }}
        # 其他等级的渐进式提升
        if self.current_level == 'slow' and recent_avg < 0.8 and self.consecutive_success_count >= 8:
            return 'normal'
        elif self.current_level == 'normal' and recent_avg < 0.5 and self.consecutive_success_count >= 6:
            return 'good'
        elif self.current_level == 'good' and recent_avg < 0.3 and self.consecutive_success_count >= 5:
            return 'excellent'

        # 根据响应时间确定基础性能等级（降级逻辑）
        for level, config in self.performance_levels.items():
            if recent_avg < config['threshold']:
                return level

        return 'congested'

    def _record_level_change(self, old_level: str, new_level: str, forced: bool = False):
        """
        记录性能等级变化

        {{ AURA-X: Add - 等级变化记录方法，支持详细日志和历史追踪. Source: 短期优化方案实施 }}
        """
        import time
        current_time = time.time()
        avg_response_time = sum(self.recent_response_times) / len(self.recent_response_times) if self.recent_response_times else 0
        old_multiplier = self.performance_levels[old_level]['multiplier']
        new_multiplier = self.performance_levels[new_level]['multiplier']

        # 记录到历史
        self.level_transition_history.append({
            'old_level': old_level,
            'new_level': new_level,
            'timestamp': current_time,
            'forced': forced
        })

        # 保持最近10次记录
        if len(self.level_transition_history) > 10:
            self.level_transition_history.pop(0)

        # 详细日志记录
        change_type = "强制变化" if forced else "自动调整"
        self.logger.info(f"🔄 性能等级{change_type}: {old_level} → {new_level}")
        self.logger.info(f"LEVEL_CHANGE_DETAIL: old_level={old_level}, new_level={new_level}, "
                       f"avg_response_time={avg_response_time:.4f}s, old_multiplier={old_multiplier:.2f}, "
                       f"new_multiplier={new_multiplier:.2f}, timestamp={current_time:.3f}, "
                       f"samples={len(self.recent_response_times)}, forced={forced}")

    def get_adaptive_multiplier(self, api_name: Optional[str] = None) -> float:
        """
        智能自适应调整倍数计算

        {{ AURA-X: Modify - 增加API特定调整间隔和智能评估逻辑. Source: 短期优化方案实施 }}
        """
        import time
        current_time = time.time()

        # {{ AURA-X: Add - API特定调整间隔. Source: 短期优化方案1 }}
        # 获取API特定的调整间隔
        if api_name:
            api_config = self.api_specific_configs.get(api_name, self.api_specific_configs['default'])
            adjustment_interval = api_config['adjustment_interval']
        else:
            adjustment_interval = self.adjustment_interval

        # 检查是否需要重新评估
        if current_time - self.last_adjustment_time > adjustment_interval:
            old_level = self.current_level
            self.current_level = self.get_current_performance_level()
            self.last_adjustment_time = current_time

            if old_level != self.current_level:
                self._record_level_change(old_level, self.current_level)

        # 获取基础倍数
        return self.performance_levels[self.current_level]['multiplier']

    def _calculate_percentiles(self) -> Dict[str, float]:
        """
        计算响应时间百分位统计

        {{ AURA-X: Add - 百分位统计算法，提供P50/P95/P99指标. Source: 最优实践算法精度优化 }}

        返回:
            Dict[str, float]: 包含P50, P95, P99的字典
        """
        if len(self.percentile_buffer) < 10:
            return {'p50': 0.0, 'p95': 0.0, 'p99': 0.0}

        sorted_times = sorted(self.percentile_buffer)
        n = len(sorted_times)

        # 计算百分位数
        p50_idx = int(n * 0.50)
        p95_idx = int(n * 0.95)
        p99_idx = int(n * 0.99)

        return {
            'p50': sorted_times[min(p50_idx, n-1)],
            'p95': sorted_times[min(p95_idx, n-1)],
            'p99': sorted_times[min(p99_idx, n-1)]
        }

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        增强性能统计信息

        {{ AURA-X: Modify - 增加渐进式恢复和API特定统计信息. Source: 短期优化方案实施 }}
        """
        if not self.recent_response_times:
            return {}

        avg_response_time = sum(self.recent_response_times) / len(self.recent_response_times)
        success_rate = (self.success_count / self.total_count * 100) if self.total_count > 0 else 100.0

        # {{ AURA-X: Modify - 增强统计信息，添加算法精度指标. Source: 最优实践算法精度优化 }}
        # 获取百分位统计
        percentiles = self._calculate_percentiles()

        stats = {
            'current_level': self.current_level,
            'avg_response_time': avg_response_time,
            'ewma_response_time': self.ewma_response_time or 0.0,  # EWMA响应时间
            'response_time_variance': self.response_time_variance,  # 响应时间方差
            'success_rate': success_rate,
            'total_samples': len(self.recent_response_times),
            'performance_multiplier': self.performance_levels[self.current_level]['multiplier'],
            'consecutive_success_count': self.consecutive_success_count,
            'level_transitions': len(self.level_transition_history),
            'congested_duration': None,
            # 新增百分位统计
            'p50_response_time': percentiles['p50'],
            'p95_response_time': percentiles['p95'],
            'p99_response_time': percentiles['p99'],
            'percentile_samples': len(self.percentile_buffer)
        }

        # 计算congested状态持续时间
        if self.current_level == 'congested' and self.congested_start_time:
            stats['congested_duration'] = time.time() - self.congested_start_time

        return stats

    def wait_if_needed(self, api_name: Optional[str] = None):
        """
        轻量化限流等待

        {{ AURA-X: Modify - 轻量化限流等待，简化自适应调整逻辑. Source: 性能瓶颈优化方案A }}
        """
        # 获取自适应倍数
        multiplier = self.get_adaptive_multiplier(api_name)

        # 如果性能良好，减少等待时间；如果性能差，增加等待时间
        if multiplier > 1.0:
            # 性能良好，减少等待
            adjusted_period = self.base_limiter.period / multiplier
        else:
            # 性能较差，增加等待
            adjusted_period = self.base_limiter.period / multiplier

        # 临时调整基础限流器的周期
        original_period = self.base_limiter.period
        self.base_limiter.period = adjusted_period

        try:
            # 执行限流等待
            self.base_limiter.wait_if_needed()
        finally:
            # 恢复原始参数
            self.base_limiter.period = original_period

    def execute_with_monitoring(self, func: Callable, api_name: str, *args, **kwargs) -> Any:
        """
        轻量化执行函数并监控性能

        {{ AURA-X: Modify - 轻量化性能监控，减少日志输出频率. Source: 性能瓶颈优化方案A }}

        参数:
            func: 要执行的函数
            api_name: API名称
            *args: 函数参数
            **kwargs: 函数关键字参数

        返回:
            函数执行结果
        """
        # 应用轻量化限流
        self.wait_if_needed(api_name)

        # 执行并监控
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            response_time = time.time() - start_time
            self.record_response_time(api_name, response_time, success=True)

            # 减少日志输出频率（仅在响应时间过长时输出）
            if response_time > 3.0:  # 提高阈值到3秒
                self.logger.warning(f"API {api_name} 响应较慢: {response_time:.3f}s")

            return result
        except Exception as e:
            response_time = time.time() - start_time
            self.record_response_time(api_name, response_time, success=False)
            raise e

    def _intelligent_tuning(self, api_name: str):
        """
        智能配置调优

        {{ AURA-X: Add - 智能配置调优核心算法. Source: 最优实践智能配置 }}

        参数:
            api_name: API名称
        """
        # 获取最近100次该API的性能数据
        api_history = [record for record in list(self.performance_history)[-100:]
                      if record['api_name'] == api_name]

        if len(api_history) < 20:
            return

        # 分析性能模式
        response_times = [record['response_time'] for record in api_history]
        success_rates = [record['success'] for record in api_history]

        # 计算性能指标
        avg_response_time = sum(response_times) / len(response_times)
        success_rate = sum(success_rates) / len(success_rates)

        # 智能调整阈值（使用api_specific_configs）
        if api_name in self.api_specific_configs:
            config = self.api_specific_configs[api_name]

            # 基于历史数据动态调整阈值
            if success_rate > 0.95 and avg_response_time < 0.2:  # 使用固定阈值
                # 性能优秀，可以更激进
                self.logger.debug(f"API {api_name} 性能优秀，建议更激进的配置")
            elif success_rate < 0.9 or avg_response_time > 1.0:  # 使用固定阈值
                # 性能不佳，需要更保守
                self.logger.debug(f"API {api_name} 性能不佳，建议更保守的配置")

    def predict_performance(self, api_name: str, horizon_minutes: int = 10) -> Dict[str, Any]:
        """
        性能预测

        {{ AURA-X: Add - 性能预测算法. Source: 最优实践性能预测 }}

        参数:
            api_name: API名称
            horizon_minutes: 预测时间范围（分钟）

        返回:
            Dict: 预测结果
        """
        # 获取该API的历史数据
        current_time = time.time()
        cutoff_time = current_time - (horizon_minutes * 60)

        api_history = [record for record in self.performance_history
                      if record['api_name'] == api_name and record['timestamp'] >= cutoff_time]

        if len(api_history) < 10:
            return {'prediction': 'insufficient_data', 'confidence': 0.0}

        # 简单的线性趋势预测
        response_times = [record['response_time'] for record in api_history]
        timestamps = [record['timestamp'] for record in api_history]

        # 计算趋势
        if len(response_times) >= 5:
            recent_avg = sum(response_times[-5:]) / 5
            earlier_avg = sum(response_times[:5]) / 5
            trend = (recent_avg - earlier_avg) / earlier_avg if earlier_avg > 0 else 0
        else:
            trend = 0

        # 预测未来性能等级
        current_avg = sum(response_times[-3:]) / min(3, len(response_times))
        predicted_response_time = current_avg * (1 + trend * 0.5)  # 保守预测

        # 预测性能等级（使用固定阈值）
        if predicted_response_time <= 0.2:
            predicted_level = 'excellent'
            confidence = 0.9
        elif predicted_response_time <= 0.5:
            predicted_level = 'good'
            confidence = 0.8
        elif predicted_response_time <= 1.0:
            predicted_level = 'normal'
            confidence = 0.7
        else:
            predicted_level = 'congested'
            confidence = 0.6

        # 计算预测准确率（基于历史验证）
        self._update_prediction_accuracy(api_name, predicted_level)

        return {
            'api_name': api_name,
            'current_response_time': current_avg,
            'predicted_response_time': predicted_response_time,
            'trend': trend,
            'predicted_level': predicted_level,
            'confidence': confidence,
            'prediction_accuracy': self.prediction_accuracy,
            'data_points': len(api_history),
            'horizon_minutes': horizon_minutes
        }

    def _update_prediction_accuracy(self, api_name: str, predicted_level: str):
        """更新预测准确率"""
        # 简化的准确率计算
        if hasattr(self, '_last_prediction'):
            if self._last_prediction.get('api_name') == api_name:
                actual_level = self.current_level
                if self._last_prediction.get('predicted_level') == actual_level:
                    # 预测正确
                    self.prediction_accuracy = (self.prediction_accuracy * 0.9 + 1.0 * 0.1)
                else:
                    # 预测错误
                    self.prediction_accuracy = (self.prediction_accuracy * 0.9 + 0.0 * 0.1)

        self._last_prediction = {'api_name': api_name, 'predicted_level': predicted_level}

    def get_intelligent_recommendations(self, api_name: str) -> List[Dict[str, Any]]:
        """
        获取智能配置建议

        {{ AURA-X: Add - 智能配置建议. Source: 最优实践智能配置 }}

        参数:
            api_name: API名称

        返回:
            List[Dict]: 配置建议列表
        """
        recommendations = []

        # 获取性能预测
        prediction = self.predict_performance(api_name, 10)

        if prediction.get('prediction') == 'insufficient_data':
            recommendations.append({
                'type': 'info',
                'message': f'API {api_name} 数据不足，建议继续收集性能数据',
                'priority': 'low'
            })
            return recommendations

        # 基于预测结果生成建议
        if prediction['predicted_level'] == 'congested':
            recommendations.append({
                'type': 'warning',
                'message': f'预测API {api_name} 将进入拥塞状态，建议提前降低调用频率',
                'priority': 'high',
                'suggested_action': 'increase_multiplier',
                'confidence': prediction['confidence']
            })
        elif prediction['predicted_level'] == 'excellent' and prediction['trend'] < -0.1:
            recommendations.append({
                'type': 'optimization',
                'message': f'API {api_name} 性能持续改善，可以适当提高调用频率',
                'priority': 'medium',
                'suggested_action': 'decrease_multiplier',
                'confidence': prediction['confidence']
            })

        # 基于历史数据分析生成建议
        api_history = [record for record in list(self.performance_history)[-100:]
                      if record['api_name'] == api_name]

        if len(api_history) >= 20:
            level_changes = len(set(record['level'] for record in api_history[-20:]))
            if level_changes > 3:
                recommendations.append({
                    'type': 'stability',
                    'message': f'API {api_name} 性能等级变化频繁，建议调整阈值参数',
                    'priority': 'medium',
                    'suggested_action': 'adjust_thresholds',
                    'level_changes': level_changes
                })

        return recommendations