"""
日历工厂
用于创建和管理不同市场的交易日历
"""

import logging
from typing import Dict, Type, Optional, Any

from src.data.calendar.calendar_interface import CalendarInterface
from src.data.calendar.china_calendar import ChinaCalendar
from src.data.calendar.us_calendar import USCalendar

class CalendarFactory:
    """
    日历工厂类
    负责创建和管理不同市场的交易日历
    """
    
    # 类变量，用于缓存已创建的日历实例
    _calendar_instances: Dict[str, CalendarInterface] = {}
    
    # 类变量，存储日历类型映射
    _calendar_types: Dict[str, Type[CalendarInterface]] = {
        'china': ChinaCalendar,
        'cn': ChinaCalendar,
        'A': ChinaCalendar,
        'us': USCalendar,
        'usa': USCalendar,
        'US': USCalendar,
    }
    
    @classmethod
    def register_calendar_type(cls, calendar_type: str, calendar_class: Type[CalendarInterface]) -> None:
        """
        注册日历类型
        
        参数:
            calendar_type: 日历类型名称
            calendar_class: 日历类
        """
        if calendar_type in cls._calendar_types:
            logging.warning(f"日历类型 '{calendar_type}' 已存在，将被覆盖")
        
        cls._calendar_types[calendar_type] = calendar_class
        logging.info(f"注册日历类型: {calendar_type}")
    
    @classmethod
    def create_calendar(cls, calendar_type: str, **kwargs) -> CalendarInterface:
        """
        创建日历实例
        
        参数:
            calendar_type: 日历类型名称
            **kwargs: 传递给日历构造函数的参数
            
        返回:
            CalendarInterface: 日历实例
            
        异常:
            ValueError: 未知的日历类型
        """
        # 转换为小写以避免大小写敏感
        calendar_type = calendar_type.lower()
        
        # 检查是否为已知日历类型
        if calendar_type not in cls._calendar_types:
            raise ValueError(f"未知的日历类型: {calendar_type}")
        
        # 创建日历实例
        calendar_class = cls._calendar_types[calendar_type]
        try:
            calendar = calendar_class(**kwargs)
            logging.info(f"创建日历: {calendar_type}")
            return calendar
        except Exception as e:
            logging.error(f"创建日历 {calendar_type} 失败: {e}")
            raise
    
    @classmethod
    def get_calendar(cls, calendar_type: str, **kwargs) -> CalendarInterface:
        """
        获取日历实例，如果不存在则创建
        
        参数:
            calendar_type: 日历类型名称
            **kwargs: 传递给日历构造函数的参数
            
        返回:
            CalendarInterface: 日历实例
        """
        # 转换为小写以避免大小写敏感
        calendar_type = calendar_type.lower()
        
        # 检查是否已有缓存的实例
        if calendar_type in cls._calendar_instances:
            return cls._calendar_instances[calendar_type]
        
        # 创建新实例并缓存
        calendar = cls.create_calendar(calendar_type, **kwargs)
        cls._calendar_instances[calendar_type] = calendar
        return calendar
    
    @classmethod
    def get_china_calendar(cls, **kwargs) -> ChinaCalendar:
        """
        获取中国A股交易日历
        
        参数:
            **kwargs: 传递给日历构造函数的参数
            
        返回:
            ChinaCalendar: 中国交易日历实例
        """
        return cls.get_calendar('china', **kwargs)
    
    @classmethod
    def get_us_calendar(cls, **kwargs) -> USCalendar:
        """
        获取美国交易日历
        
        参数:
            **kwargs: 传递给日历构造函数的参数
            
        返回:
            USCalendar: 美国交易日历实例
        """
        return cls.get_calendar('us', **kwargs)
    
    @classmethod
    def clear_cache(cls) -> None:
        """
        清除日历实例缓存
        """
        cls._calendar_instances.clear()
        logging.info("日历实例缓存已清除")

