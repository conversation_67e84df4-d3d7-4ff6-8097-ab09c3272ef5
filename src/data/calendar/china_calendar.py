"""
中国交易日历
提供A股市场交易日的相关功能
"""

import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime, date, time, timedelta
from typing import List, Optional, Union, Dict, Tuple

from src.data.calendar.calendar_interface import BaseCalendar
from src.data.sources.data_source_factory import DataSourceFactory

class ChinaCalendar(BaseCalendar):
    """
    中国A股交易日历
    提供A股市场交易日的查询和处理功能
    """
    
    def __init__(self, cache_file: str = None, update_cache: bool = False, name: str = "ChinaCalendar"):
        """
        初始化中国交易日历
        
        参数:
            cache_file: 缓存文件路径，默认为None
            update_cache: 是否更新缓存，默认为False
            name: 日历名称
        """
        super().__init__(name)
        self.logger = logging.getLogger(__name__)
        
        # 设置默认的缓存文件路径
        if cache_file is None:
            cache_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'output', 'data')
            if not os.path.exists(cache_dir):
                os.makedirs(cache_dir)
            cache_file = os.path.join(cache_dir, 'china_trading_calendar.csv')
        
        self.cache_file = cache_file
        
        # 交易时间配置
        self.morning_open = time(9, 30)    # 上午开盘时间
        self.morning_close = time(11, 30)  # 上午收盘时间
        self.afternoon_open = time(13, 0)  # 下午开盘时间
        self.afternoon_close = time(15, 0) # 下午收盘时间
        
        # 加载交易日历数据
        self._trading_days = self._load_trading_days(update_cache)
        
    def _load_trading_days(self, update_cache: bool = False) -> List[datetime]:
        """
        加载交易日历数据
        
        参数:
            update_cache: 是否强制更新缓存
            
        返回:
            List[datetime]: 交易日列表
        """
        # 如果缓存文件存在且不需要更新缓存，直接从缓存加载
        if os.path.exists(self.cache_file) and not update_cache:
            try:
                calendar_df = pd.read_csv(self.cache_file)
                if 'date' in calendar_df.columns:
                    calendar_df['date'] = pd.to_datetime(calendar_df['date'])
                    trading_days = calendar_df[calendar_df['is_trading_day'] == 1]['date'].tolist()
                    self.logger.info(f"从缓存加载了 {len(trading_days)} 个交易日")
                    return trading_days
            except Exception as e:
                self.logger.warning(f"从缓存加载交易日历失败: {e}, 将重新获取")
        
        # 从数据源获取交易日历
        self.logger.info("从数据源获取交易日历...")
        try:
            # 使用数据源工厂创建数据源
            data_source = DataSourceFactory.create_tushare()
            
            # 获取从2000年至今的交易日历
            start_date = "20000101"
            end_date = datetime.now().strftime("%Y%m%d")
            
            # 获取交易日历
            calendar_df = data_source.get_trade_calendar(start_date=start_date, end_date=end_date)
            
            if calendar_df is not None and not calendar_df.empty:
                # 重命名列以便统一处理
                if 'cal_date' in calendar_df.columns:
                    calendar_df = calendar_df.rename(columns={'cal_date': 'date'})
                
                # 转换日期格式
                calendar_df['date'] = pd.to_datetime(calendar_df['date'])
                
                # 确定是否为交易日的列名
                is_trading_col = None
                for col in ['is_open', 'is_trading_day']:
                    if col in calendar_df.columns:
                        is_trading_col = col
                        break
                
                if is_trading_col:
                    # 重命名交易日列
                    if is_trading_col != 'is_trading_day':
                        calendar_df = calendar_df.rename(columns={is_trading_col: 'is_trading_day'})
                    
                    # 保存到缓存
                    calendar_df.to_csv(self.cache_file, index=False)
                    self.logger.info(f"交易日历已保存到缓存: {self.cache_file}")
                    
                    # 提取交易日
                    trading_days = calendar_df[calendar_df['is_trading_day'] == 1]['date'].tolist()
                    self.logger.info(f"获取了 {len(trading_days)} 个交易日")
                    return trading_days
                else:
                    self.logger.error(f"交易日历数据缺少交易日标识列，列名: {calendar_df.columns.tolist()}")
            else:
                self.logger.error("获取交易日历失败，返回的数据为空")
        except Exception as e:
            self.logger.error(f"从数据源获取交易日历失败: {e}")
        
        # 如果获取失败但缓存存在，尝试从缓存加载
        if os.path.exists(self.cache_file):
            try:
                calendar_df = pd.read_csv(self.cache_file)
                calendar_df['date'] = pd.to_datetime(calendar_df['date'])
                trading_days = calendar_df[calendar_df['is_trading_day'] == 1]['date'].tolist()
                self.logger.warning(f"从数据源获取失败，使用缓存中的 {len(trading_days)} 个交易日")
                return trading_days
            except Exception as e:
                self.logger.error(f"从缓存加载交易日历失败: {e}")
        
        # 如果都失败了，返回空列表
        self.logger.error("无法获取交易日历数据，返回空列表")
        return []
    
    def get_trading_days(self, start_date: Union[str, datetime, date], 
                         end_date: Union[str, datetime, date]) -> List[datetime]:
        """
        获取指定日期范围内的交易日列表
        
        参数:
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            List[datetime]: 交易日列表
        """
        start = self._format_date(start_date)
        end = self._format_date(end_date)
        
        # 使用缓存避免重复计算
        cache_key = f"{start.strftime('%Y%m%d')}_{end.strftime('%Y%m%d')}"
        if cache_key in self._trading_days_cache:
            return self._trading_days_cache[cache_key]
        
        # 筛选日期范围内的交易日
        result = [d for d in self._trading_days if start <= d <= end]
        
        # 缓存结果
        self._trading_days_cache[cache_key] = result
        
        return result
    
    def is_trading_day(self, check_date: Union[str, datetime, date]) -> bool:
        """
        检查指定日期是否为交易日
        
        参数:
            check_date: 需要检查的日期
            
        返回:
            bool: 是否为交易日
        """
        date_to_check = self._format_date(check_date)
        return date_to_check in self._trading_days
    
    def get_previous_trading_day(self, reference_date: Union[str, datetime, date]) -> datetime:
        """
        获取指定日期的前一个交易日
        
        参数:
            reference_date: 参考日期
            
        返回:
            datetime: 前一个交易日
        """
        ref_date = self._format_date(reference_date)
        
        # 找到比参考日期小的最大交易日
        previous_days = [d for d in self._trading_days if d < ref_date]
        
        if not previous_days:
            raise ValueError(f"在 {ref_date} 之前没有交易日")
        
        return max(previous_days)
    
    def get_next_trading_day(self, reference_date: Union[str, datetime, date]) -> datetime:
        """
        获取指定日期的下一个交易日
        
        参数:
            reference_date: 参考日期
            
        返回:
            datetime: 下一个交易日
        """
        ref_date = self._format_date(reference_date)
        
        # 找到比参考日期大的最小交易日
        next_days = [d for d in self._trading_days if d > ref_date]
        
        if not next_days:
            raise ValueError(f"在 {ref_date} 之后没有交易日")
        
        return min(next_days)
    
    def get_n_trading_days_before(self, reference_date: Union[str, datetime, date], n: int) -> List[datetime]:
        """
        获取指定日期前N个交易日
        
        参数:
            reference_date: 参考日期
            n: 前N个交易日
            
        返回:
            List[datetime]: N个交易日组成的列表
        """
        if n <= 0:
            return []
            
        ref_date = self._format_date(reference_date)
        
        # 找到比参考日期小的交易日并排序
        previous_days = sorted([d for d in self._trading_days if d < ref_date], reverse=True)
        
        # 获取前N个
        return previous_days[:n]
    
    def get_n_trading_days_after(self, reference_date: Union[str, datetime, date], n: int) -> List[datetime]:
        """
        获取指定日期后N个交易日
        
        参数:
            reference_date: 参考日期
            n: 后N个交易日
            
        返回:
            List[datetime]: N个交易日组成的列表
        """
        if n <= 0:
            return []
            
        ref_date = self._format_date(reference_date)
        
        # 找到比参考日期大的交易日并排序
        next_days = sorted([d for d in self._trading_days if d > ref_date])
        
        # 获取前N个
        return next_days[:n]
    
    def get_trading_close_time(self, check_date: Union[str, datetime, date] = None) -> datetime:
        """
        获取指定交易日的收盘时间
        
        参数:
            check_date: 指定日期，默认为当天
            
        返回:
            datetime: 收盘时间
        """
        if check_date is None:
            check_date = datetime.now()
        
        date_obj = self._format_date(check_date)
        
        # 如果不是交易日，抛出异常
        if not self.is_trading_day(date_obj):
            raise ValueError(f"{date_obj.strftime('%Y-%m-%d')} 不是交易日")
        
        # 返回当天的收盘时间
        return datetime.combine(date_obj.date(), self.afternoon_close)
    
    def get_trading_open_time(self, check_date: Union[str, datetime, date] = None) -> datetime:
        """
        获取指定交易日的开盘时间
        
        参数:
            check_date: 指定日期，默认为当天
            
        返回:
            datetime: 开盘时间
        """
        if check_date is None:
            check_date = datetime.now()
        
        date_obj = self._format_date(check_date)
        
        # 如果不是交易日，抛出异常
        if not self.is_trading_day(date_obj):
            raise ValueError(f"{date_obj.strftime('%Y-%m-%d')} 不是交易日")
        
        # 返回当天的开盘时间
        return datetime.combine(date_obj.date(), self.morning_open)

