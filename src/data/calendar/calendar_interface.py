"""
日历接口
定义交易日历的基本操作和日期处理功能
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Union, Tuple
from datetime import datetime, date

class CalendarInterface(ABC):
    """
    交易日历接口抽象类
    提供获取交易日、检查是否为交易日等功能
    """
    
    @abstractmethod
    def get_trading_days(self, start_date: Union[str, datetime, date], 
                         end_date: Union[str, datetime, date]) -> List[datetime]:
        """
        获取指定日期范围内的交易日列表
        
        参数:
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            List[datetime]: 交易日列表
        """
        pass
    
    @abstractmethod
    def is_trading_day(self, check_date: Union[str, datetime, date]) -> bool:
        """
        检查指定日期是否为交易日
        
        参数:
            check_date: 需要检查的日期
            
        返回:
            bool: 是否为交易日
        """
        pass
    
    @abstractmethod
    def get_previous_trading_day(self, reference_date: Union[str, datetime, date]) -> datetime:
        """
        获取指定日期的前一个交易日
        
        参数:
            reference_date: 参考日期
            
        返回:
            datetime: 前一个交易日
        """
        pass
    
    @abstractmethod
    def get_next_trading_day(self, reference_date: Union[str, datetime, date]) -> datetime:
        """
        获取指定日期的下一个交易日
        
        参数:
            reference_date: 参考日期
            
        返回:
            datetime: 下一个交易日
        """
        pass
    
    @abstractmethod
    def get_n_trading_days_before(self, reference_date: Union[str, datetime, date], n: int) -> List[datetime]:
        """
        获取指定日期前N个交易日
        
        参数:
            reference_date: 参考日期
            n: 前N个交易日
            
        返回:
            List[datetime]: N个交易日组成的列表
        """
        pass
    
    @abstractmethod
    def get_n_trading_days_after(self, reference_date: Union[str, datetime, date], n: int) -> List[datetime]:
        """
        获取指定日期后N个交易日
        
        参数:
            reference_date: 参考日期
            n: 后N个交易日
            
        返回:
            List[datetime]: N个交易日组成的列表
        """
        pass
        
    @abstractmethod
    def get_trading_days_between(self, start_date: Union[str, datetime, date], 
                                end_date: Union[str, datetime, date]) -> List[datetime]:
        """
        获取两个日期之间的所有交易日
        
        参数:
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            List[datetime]: 交易日列表
        """
        pass
    
    @abstractmethod
    def count_trading_days(self, start_date: Union[str, datetime, date], 
                          end_date: Union[str, datetime, date]) -> int:
        """
        计算两个日期之间的交易日天数
        
        参数:
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            int: 交易日天数
        """
        pass
    
    @abstractmethod
    def get_trading_close_time(self, check_date: Union[str, datetime, date] = None) -> datetime:
        """
        获取指定交易日的收盘时间
        
        参数:
            check_date: 指定日期，默认为当天
            
        返回:
            datetime: 收盘时间
        """
        pass
    
    @abstractmethod
    def get_trading_open_time(self, check_date: Union[str, datetime, date] = None) -> datetime:
        """
        获取指定交易日的开盘时间
        
        参数:
            check_date: 指定日期，默认为当天
            
        返回:
            datetime: 开盘时间
        """
        pass
        
class BaseCalendar(CalendarInterface):
    """
    日历基类，提供通用实现
    """
    
    def __init__(self, name: str = "BaseCalendar"):
        """
        初始化日历
        
        参数:
            name: 日历名称
        """
        self.name = name
        self._trading_days_cache = {}  # 缓存交易日数据
    
    def _format_date(self, input_date: Union[str, datetime, date]) -> datetime:
        """
        将输入的日期格式化为datetime对象
        
        参数:
            input_date: 输入日期
            
        返回:
            datetime: 格式化后的日期
        """
        if isinstance(input_date, str):
            try:
                return datetime.strptime(input_date, "%Y%m%d")
            except ValueError:
                try:
                    return datetime.strptime(input_date, "%Y-%m-%d")
                except ValueError:
                    raise ValueError(f"无法解析日期字符串: {input_date}, 支持的格式: YYYYMMDD, YYYY-MM-DD")
        elif isinstance(input_date, date) and not isinstance(input_date, datetime):
            return datetime.combine(input_date, datetime.min.time())
        elif isinstance(input_date, datetime):
            return input_date
        else:
            raise TypeError(f"不支持的日期类型: {type(input_date)}")
    
    def count_trading_days(self, start_date: Union[str, datetime, date], 
                          end_date: Union[str, datetime, date]) -> int:
        """
        计算两个日期之间的交易日天数
        
        参数:
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            int: 交易日天数
        """
        trading_days = self.get_trading_days_between(start_date, end_date)
        return len(trading_days)
    
    def get_trading_days_between(self, start_date: Union[str, datetime, date], 
                                end_date: Union[str, datetime, date]) -> List[datetime]:
        """
        获取两个日期之间的所有交易日
        
        参数:
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            List[datetime]: 交易日列表
        """
        return self.get_trading_days(start_date, end_date)

# 为了与旧代码兼容，提供一个别名
TradingCalendarInterface = CalendarInterface

