"""
美国交易日历
提供美国股票市场交易日的相关功能
"""

import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime, date, time, timedelta
from typing import List, Optional, Union, Dict, Tuple

from src.data.calendar.calendar_interface import BaseCalendar
from src.data.sources.data_source_factory import DataSourceFactory

class USCalendar(BaseCalendar):
    """
    美国股票交易日历
    提供美国股票市场交易日的查询和处理功能
    """
    
    def __init__(self, cache_file: str = None, update_cache: bool = False, name: str = "USCalendar"):
        """
        初始化美国交易日历
        
        参数:
            cache_file: 缓存文件路径，默认为None
            update_cache: 是否更新缓存，默认为False
            name: 日历名称
        """
        super().__init__(name)
        self.logger = logging.getLogger(__name__)
        
        # 设置默认的缓存文件路径
        if cache_file is None:
            cache_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'output', 'data')
            if not os.path.exists(cache_dir):
                os.makedirs(cache_dir)
            cache_file = os.path.join(cache_dir, 'us_trading_calendar.csv')
        
        self.cache_file = cache_file
        
        # 交易时间配置（美国东部时间）
        self.morning_open = time(9, 30)    # 开盘时间
        self.afternoon_close = time(16, 0) # 收盘时间
        
        # 加载交易日历数据
        self._trading_days = self._load_trading_days(update_cache)
    
    def _generate_us_trading_days(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """
        生成美国交易日（简化版）
        
        参数:
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            List[datetime]: 交易日列表
        """
        # 生成所有日期
        all_days = []
        current = start_date
        while current <= end_date:
            all_days.append(current)
            current += timedelta(days=1)
        
        # 过滤周末和主要假日
        trading_days = []
        for day in all_days:
            # 跳过周末
            if day.weekday() >= 5:  # 5: Saturday, 6: Sunday
                continue
            
            # 判断是否为美国主要假日
            is_holiday = False
            
            # 新年 (1月1日)
            if day.month == 1 and day.day == 1:
                is_holiday = True
            
            # 马丁·路德·金日 (1月第三个周一)
            if day.month == 1 and day.weekday() == 0:  # Monday
                if 15 <= day.day <= 21:
                    is_holiday = True
            
            # 总统日 (2月第三个周一)
            if day.month == 2 and day.weekday() == 0:  # Monday
                if 15 <= day.day <= 21:
                    is_holiday = True
            
            # 耶稣受难日 (复活节前的周五) - 简化处理，视为3月或4月的某个周五
            # 这是个移动假日，实际计算比较复杂，这里简化处理
            
            # 阵亡将士纪念日 (5月最后一个周一)
            if day.month == 5 and day.weekday() == 0:  # Monday
                next_day = day + timedelta(days=7)
                if next_day.month > 5:
                    is_holiday = True
            
            # 独立日 (7月4日)
            if day.month == 7 and day.day == 4:
                is_holiday = True
            
            # 劳动节 (9月第一个周一)
            if day.month == 9 and day.weekday() == 0:  # Monday
                if day.day <= 7:
                    is_holiday = True
            
            # 感恩节 (11月第四个周四)
            if day.month == 11 and day.weekday() == 3:  # Thursday
                if 22 <= day.day <= 28:
                    is_holiday = True
            
            # 圣诞节 (12月25日)
            if day.month == 12 and day.day == 25:
                is_holiday = True
            
            # 如果不是假日，添加到交易日列表
            if not is_holiday:
                trading_days.append(day)
        
        return trading_days
    
    def _load_trading_days(self, update_cache: bool = False) -> List[datetime]:
        """
        加载交易日历数据
        
        参数:
            update_cache: 是否强制更新缓存
            
        返回:
            List[datetime]: 交易日列表
        """
        # 如果缓存文件存在且不需要更新缓存，直接从缓存加载
        if os.path.exists(self.cache_file) and not update_cache:
            try:
                calendar_df = pd.read_csv(self.cache_file)
                if 'date' in calendar_df.columns:
                    calendar_df['date'] = pd.to_datetime(calendar_df['date'])
                    trading_days = calendar_df[calendar_df['is_trading_day'] == 1]['date'].tolist()
                    self.logger.info(f"从缓存加载了 {len(trading_days)} 个交易日")
                    return trading_days
            except Exception as e:
                self.logger.warning(f"从缓存加载交易日历失败: {e}, 将生成新的交易日历")
        
        # 生成过去和未来几年的交易日历
        self.logger.info("正在生成美国交易日历...")
        try:
            # 生成从2000年至今后一年的交易日历
            start_date = datetime(2000, 1, 1)
            end_date = datetime.now() + timedelta(days=365)
            
            trading_days = self._generate_us_trading_days(start_date, end_date)
            
            # 保存到缓存
            if trading_days:
                calendar_df = pd.DataFrame({
                    'date': trading_days,
                    'is_trading_day': 1
                })
                calendar_df.to_csv(self.cache_file, index=False)
                self.logger.info(f"交易日历已保存到缓存: {self.cache_file}")
            
            self.logger.info(f"生成了 {len(trading_days)} 个交易日")
            return trading_days
        except Exception as e:
            self.logger.error(f"生成交易日历失败: {e}")
            return []
    
    def get_trading_days(self, start_date: Union[str, datetime, date], 
                         end_date: Union[str, datetime, date]) -> List[datetime]:
        """
        获取指定日期范围内的交易日列表
        
        参数:
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            List[datetime]: 交易日列表
        """
        start = self._format_date(start_date)
        end = self._format_date(end_date)
        
        # 使用缓存避免重复计算
        cache_key = f"{start.strftime('%Y%m%d')}_{end.strftime('%Y%m%d')}"
        if cache_key in self._trading_days_cache:
            return self._trading_days_cache[cache_key]
        
        # 筛选日期范围内的交易日
        result = [d for d in self._trading_days if start <= d <= end]
        
        # 缓存结果
        self._trading_days_cache[cache_key] = result
        
        return result
    
    def is_trading_day(self, check_date: Union[str, datetime, date]) -> bool:
        """
        检查指定日期是否为交易日
        
        参数:
            check_date: 需要检查的日期
            
        返回:
            bool: 是否为交易日
        """
        date_to_check = self._format_date(check_date)
        return date_to_check in self._trading_days
    
    def get_previous_trading_day(self, reference_date: Union[str, datetime, date]) -> datetime:
        """
        获取指定日期的前一个交易日
        
        参数:
            reference_date: 参考日期
            
        返回:
            datetime: 前一个交易日
        """
        ref_date = self._format_date(reference_date)
        
        # 找到比参考日期小的最大交易日
        previous_days = [d for d in self._trading_days if d < ref_date]
        
        if not previous_days:
            raise ValueError(f"在 {ref_date} 之前没有交易日")
        
        return max(previous_days)
    
    def get_next_trading_day(self, reference_date: Union[str, datetime, date]) -> datetime:
        """
        获取指定日期的下一个交易日
        
        参数:
            reference_date: 参考日期
            
        返回:
            datetime: 下一个交易日
        """
        ref_date = self._format_date(reference_date)
        
        # 找到比参考日期大的最小交易日
        next_days = [d for d in self._trading_days if d > ref_date]
        
        if not next_days:
            raise ValueError(f"在 {ref_date} 之后没有交易日")
        
        return min(next_days)
    
    def get_n_trading_days_before(self, reference_date: Union[str, datetime, date], n: int) -> List[datetime]:
        """
        获取指定日期前N个交易日
        
        参数:
            reference_date: 参考日期
            n: 前N个交易日
            
        返回:
            List[datetime]: N个交易日组成的列表
        """
        if n <= 0:
            return []
            
        ref_date = self._format_date(reference_date)
        
        # 找到比参考日期小的交易日并排序
        previous_days = sorted([d for d in self._trading_days if d < ref_date], reverse=True)
        
        # 获取前N个
        return previous_days[:n]
    
    def get_n_trading_days_after(self, reference_date: Union[str, datetime, date], n: int) -> List[datetime]:
        """
        获取指定日期后N个交易日
        
        参数:
            reference_date: 参考日期
            n: 后N个交易日
            
        返回:
            List[datetime]: N个交易日组成的列表
        """
        if n <= 0:
            return []
            
        ref_date = self._format_date(reference_date)
        
        # 找到比参考日期大的交易日并排序
        next_days = sorted([d for d in self._trading_days if d > ref_date])
        
        # 获取前N个
        return next_days[:n]
    
    def get_trading_close_time(self, check_date: Union[str, datetime, date] = None) -> datetime:
        """
        获取指定交易日的收盘时间
        
        参数:
            check_date: 指定日期，默认为当天
            
        返回:
            datetime: 收盘时间
        """
        if check_date is None:
            check_date = datetime.now()
        
        date_obj = self._format_date(check_date)
        
        # 如果不是交易日，抛出异常
        if not self.is_trading_day(date_obj):
            raise ValueError(f"{date_obj.strftime('%Y-%m-%d')} 不是交易日")
        
        # 返回当天的收盘时间
        return datetime.combine(date_obj.date(), self.afternoon_close)
    
    def get_trading_open_time(self, check_date: Union[str, datetime, date] = None) -> datetime:
        """
        获取指定交易日的开盘时间
        
        参数:
            check_date: 指定日期，默认为当天
            
        返回:
            datetime: 开盘时间
        """
        if check_date is None:
            check_date = datetime.now()
        
        date_obj = self._format_date(check_date)
        
        # 如果不是交易日，抛出异常
        if not self.is_trading_day(date_obj):
            raise ValueError(f"{date_obj.strftime('%Y-%m-%d')} 不是交易日")
        
        # 返回当天的开盘时间
        return datetime.combine(date_obj.date(), self.morning_open)

