"""
日历模块
提供交易日历和日期处理功能
"""

from src.data.calendar.calendar_factory import CalendarFactory
from src.data.calendar.calendar_interface import CalendarInterface, BaseCalendar
from src.data.calendar.china_calendar import ChinaCalendar
from src.data.calendar.us_calendar import USCalendar

# 导出便捷函数和类
__all__ = [
    'CalendarFactory',
    'CalendarInterface',
    'BaseCalendar',
    'ChinaCalendar',
    'USCalendar',
]

# 便捷函数
def get_china_calendar(**kwargs):
    """获取中国A股交易日历实例"""
    return CalendarFactory.get_china_calendar(**kwargs)

def get_us_calendar(**kwargs):
    """获取美国交易日历实例"""
    return CalendarFactory.get_us_calendar(**kwargs)

# 更新导出的便捷函数
__all__ += ['get_china_calendar', 'get_us_calendar']
