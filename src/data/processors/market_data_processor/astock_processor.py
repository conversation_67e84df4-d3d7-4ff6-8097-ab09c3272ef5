"""
A股市场数据处理器
"""

from typing import Dict, Any
import pandas as pd
import numpy as np
from src.data.processors.market_data_processor.base_market_processor import BaseMarketProcessor


class AStockProcessor(BaseMarketProcessor):
    """
    A股市场数据处理器
    专门处理中国A股市场的数据特性
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化A股处理器
        
        参数:
            config: 配置参数
        """
        default_config = {
            'handle_missing': True,
            'handle_outliers': True,
            'handle_duplicates': True,
            'calculate_returns': True,
            'add_indicators': True,
            'indicators': ['sma', 'turnover_rate', 'limit_up_down'],
            'handle_suspension': True,
            'handle_limit_up_down': True,
            'sma_window': 20
        }
        
        # 合并默认配置和用户配置
        if config:
            default_config.update(config)
            
        super().__init__(default_config)
    
    def process(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        处理A股市场数据
        
        参数:
            data: A股市场数据
            **kwargs: 额外参数
            
        返回:
            处理后的数据
        """
        # 调用基类处理方法
        result = super().process(data, **kwargs)
        
        # A股特有处理逻辑
        if self.config.get('handle_suspension', True):
            result = self._handle_suspension(result)
            
        if self.config.get('handle_limit_up_down', True):
            result = self._handle_limit_up_down(result)
        
        return result
    
    def _handle_suspension(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        处理停牌
        
        参数:
            data: A股市场数据
            
        返回:
            处理后的数据
        """
        result = data.copy()
        
        # 检查是否有停牌标志列
        if 'is_suspended' in result.columns:
            # 删除连续停牌超过30个交易日的数据
            if 'symbol' in result.columns and 'date' in result.columns:
                # 按股票分组，计算连续停牌天数
                result = result.sort_values(['symbol', 'date'])
                result['suspension_days'] = result.groupby('symbol')['is_suspended'].transform(
                    lambda x: x.astype(int).rolling(30, min_periods=1).sum()
                )
                
                # 删除连续停牌超过30天的数据
                result = result[result['suspension_days'] < 30]
                result = result.drop('suspension_days', axis=1)
        
        # 如果没有停牌标志列，尝试从成交量判断停牌
        elif 'volume' in result.columns:
            # 成交量为0可能表示停牌
            result['is_suspended'] = (result['volume'] == 0)
            
            # 同样处理连续停牌
            if 'symbol' in result.columns and 'date' in result.columns:
                result = result.sort_values(['symbol', 'date'])
                result['suspension_days'] = result.groupby('symbol')['is_suspended'].transform(
                    lambda x: x.astype(int).rolling(30, min_periods=1).sum()
                )
                
                # 删除连续停牌超过30天的数据
                result = result[result['suspension_days'] < 30]
                result = result.drop('suspension_days', axis=1)
        
        return result
    
    def _handle_limit_up_down(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        处理涨跌停
        
        参数:
            data: A股市场数据
            
        返回:
            处理后的数据
        """
        result = data.copy()
        
        # 检查是否有直接的涨跌停标志
        if all(col in result.columns for col in ['is_limit_up', 'is_limit_down', 'open']):
            # 如果数据中已有涨跌停标志，直接调整价格
            # 涨停情况：收盘价等于开盘价的1.1倍
            result.loc[result['is_limit_up'], 'close'] = result.loc[result['is_limit_up'], 'open'] * 1.1
            result.loc[result['is_limit_up'], 'high'] = result.loc[result['is_limit_up'], 'close']
            
            # 跌停情况：收盘价等于开盘价的0.9倍
            result.loc[result['is_limit_down'], 'close'] = result.loc[result['is_limit_down'], 'open'] * 0.9
            result.loc[result['is_limit_down'], 'low'] = result.loc[result['is_limit_down'], 'close']
        
        # 如果有前收盘价，计算涨跌幅并标记涨跌停
        elif all(col in result.columns for col in ['close', 'pre_close']):
            # 计算涨跌幅
            result['change_pct'] = (result['close'] / result['pre_close'] - 1) * 100
            
            # 标记涨跌停（一般A股是±10%，ST股是±5%）
            if 'is_st' in result.columns:
                # 对ST股区分处理
                result['is_limit_up'] = ((result['is_st'] & (result['change_pct'] > 4.9)) |
                                         (~result['is_st'] & (result['change_pct'] > 9.9)))
                result['is_limit_down'] = ((result['is_st'] & (result['change_pct'] < -4.9)) |
                                          (~result['is_st'] & (result['change_pct'] < -9.9)))
                
                # 调整ST股和非ST股的价格
                if 'open' in result.columns:
                    # ST股涨停：收盘价等于开盘价的1.05倍
                    st_limit_up = result['is_st'] & result['is_limit_up']
                    result.loc[st_limit_up, 'close'] = result.loc[st_limit_up, 'open'] * 1.05
                    result.loc[st_limit_up, 'high'] = result.loc[st_limit_up, 'close']
                    
                    # ST股跌停：收盘价等于开盘价的0.95倍
                    st_limit_down = result['is_st'] & result['is_limit_down']
                    result.loc[st_limit_down, 'close'] = result.loc[st_limit_down, 'open'] * 0.95
                    result.loc[st_limit_down, 'low'] = result.loc[st_limit_down, 'close']
                    
                    # 非ST股涨停：收盘价等于开盘价的1.1倍
                    non_st_limit_up = ~result['is_st'] & result['is_limit_up']
                    result.loc[non_st_limit_up, 'close'] = result.loc[non_st_limit_up, 'open'] * 1.1
                    result.loc[non_st_limit_up, 'high'] = result.loc[non_st_limit_up, 'close']
                    
                    # 非ST股跌停：收盘价等于开盘价的0.9倍
                    non_st_limit_down = ~result['is_st'] & result['is_limit_down']
                    result.loc[non_st_limit_down, 'close'] = result.loc[non_st_limit_down, 'open'] * 0.9
                    result.loc[non_st_limit_down, 'low'] = result.loc[non_st_limit_down, 'close']
            else:
                # 不区分ST股
                result['is_limit_up'] = (result['change_pct'] > 9.9)
                result['is_limit_down'] = (result['change_pct'] < -9.9)
                
                # 调整价格
                if 'open' in result.columns:
                    # 涨停情况：收盘价等于开盘价的1.1倍
                    result.loc[result['is_limit_up'], 'close'] = result.loc[result['is_limit_up'], 'open'] * 1.1
                    result.loc[result['is_limit_up'], 'high'] = result.loc[result['is_limit_up'], 'close']
                    
                    # 跌停情况：收盘价等于开盘价的0.9倍
                    result.loc[result['is_limit_down'], 'close'] = result.loc[result['is_limit_down'], 'open'] * 0.9
                    result.loc[result['is_limit_down'], 'low'] = result.loc[result['is_limit_down'], 'close']
        
        return result
    
    def _add_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        添加A股特有指标
        
        参数:
            data: A股市场数据
            
        返回:
            添加指标后的数据
        """
        result = super()._add_indicators(data)
        
        indicators = self.config.get('indicators', [])
        
        # 添加换手率
        if 'turnover_rate' in indicators and all(col in result.columns for col in ['volume', 'float_shares']):
            result['turnover_rate'] = result['volume'] / result['float_shares'] * 100
            
        # 添加北向资金持股比例
        if 'northbound_ratio' in indicators and all(col in result.columns for col in ['northbound_shares', 'total_shares']):
            result['northbound_ratio'] = result['northbound_shares'] / result['total_shares'] * 100
            
        # 添加杜邦分析指标（如果有财务数据）
        if 'dupont' in indicators and all(col in result.columns for col in ['net_profit', 'revenue', 'total_assets', 'total_equity']):
            # ROE = 净利润率 * 资产周转率 * 财务杠杆
            result['net_profit_margin'] = result['net_profit'] / result['revenue']  # 净利润率
            result['asset_turnover'] = result['revenue'] / result['total_assets']  # 资产周转率
            result['financial_leverage'] = result['total_assets'] / result['total_equity']  # 财务杠杆
            result['roe_dupont'] = result['net_profit_margin'] * result['asset_turnover'] * result['financial_leverage']  # ROE
        
        return result

