"""
市场数据处理基类
"""

from typing import Dict, List, Any, Optional, Union
import pandas as pd
import numpy as np
from src.data.processors.processor_interface import ProcessorInterface


class BaseMarketProcessor(ProcessorInterface):
    """
    市场数据处理基类
    提供通用的市场数据处理方法
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化市场数据处理器
        
        参数:
            config: 配置参数
        """
        default_config = {
            'handle_missing': True,
            'handle_outliers': True,
            'handle_duplicates': True,
            'calculate_returns': True,
            'add_indicators': False,
            'indicators': []
        }
        
        # 合并默认配置和用户配置
        if config:
            default_config.update(config)
            
        super().__init__(default_config)
    
    def process(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        处理市场数据
        
        参数:
            data: 市场数据
            **kwargs: 额外参数
            
        返回:
            处理后的数据
        """
        if data.empty:
            return data
            
        # 创建副本以避免修改原始数据
        result = data.copy()
        
        # 1. 数据清洗
        if self.config.get('handle_missing', True):
            result = self._handle_missing(result)
            
        if self.config.get('handle_outliers', True):
            result = self._handle_outliers(result)
            
        if self.config.get('handle_duplicates', True):
            result = result.drop_duplicates()
        
        # 2. 计算收益率
        if self.config.get('calculate_returns', True):
            result = self._calculate_returns(result)
        
        # 3. 添加技术指标
        if self.config.get('add_indicators', False):
            result = self._add_indicators(result)
            
        return result
    
    def _handle_missing(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        处理缺失值
        
        参数:
            data: 市场数据
            
        返回:
            处理后的数据
        """
        # 处理缺失值的默认实现
        result = data.copy()
        
        # 对于数值列，使用前向填充
        numeric_cols = result.select_dtypes(include=['number']).columns
        result[numeric_cols] = result[numeric_cols].ffill()
        
        # 对于分类列，使用众数填充
        categorical_cols = result.select_dtypes(include=['category', 'object']).columns
        for col in categorical_cols:
            if result[col].isnull().any():
                mode_value = result[col].mode()[0] if not result[col].mode().empty else None
                result[col] = result[col].fillna(mode_value)
                
        return result
    
    def _handle_outliers(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        处理异常值
        
        参数:
            data: 市场数据
            
        返回:
            处理后的数据
        """
        # 处理异常值的默认实现
        result = data.copy()
        
        # 对数值型列使用IQR方法处理异常值
        numeric_cols = result.select_dtypes(include=['number']).columns
        
        for col in numeric_cols:
            # 排除日期类数值列和ID类列
            if col.lower() in ['date', 'time', 'id', 'index', 'year', 'month', 'day']:
                continue
                
            # 计算IQR
            q1 = result[col].quantile(0.25)
            q3 = result[col].quantile(0.75)
            iqr = q3 - q1
            
            # 设置上下限
            lower_bound = q1 - 3 * iqr
            upper_bound = q3 + 3 * iqr
            
            # 在设置值之前进行类型转换
            col_dtype = result[col].dtype
            # 处理异常值
            # 在赋值前进行类型转换，避免类型不兼容警告
            result.loc[result[col] < lower_bound, col] = result.loc[result[col] < lower_bound, col].astype(float).apply(lambda x: float(lower_bound))
            result.loc[result[col] > upper_bound, col] = result.loc[result[col] > upper_bound, col].astype(float).apply(lambda x: float(upper_bound))
            
        return result
    
    def _calculate_returns(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算收益率
        
        参数:
            data: 市场数据
            
        返回:
            添加收益率列的数据
        """
        result = data.copy()
        
        # 如果有收盘价列，计算收益率
        if 'close' in result.columns:
            # 确保数据按日期排序
            if 'date' in result.columns:
                result = result.sort_values('date')
                
            # 计算收益率，明确指定fill_method=None
            if 'symbol' in result.columns:
                # 多资产情况，按资产分组计算
                result['return'] = result.groupby('symbol')['close'].pct_change(fill_method=None)
                # 计算对数收益率
                result['log_return'] = np.log(result['close'] / result.groupby('symbol')['close'].shift(1))
            else:
                # 单资产情况
                result['return'] = result['close'].pct_change(fill_method=None)
                result['log_return'] = np.log(result['close'] / result['close'].shift(1))
                
        return result
    
    def _add_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        添加技术指标
        
        参数:
            data: 市场数据
            
        返回:
            添加技术指标的数据
        """
        # 添加指标的默认实现，子类可以重写
        result = data.copy()
        
        # 默认添加20日简单移动平均线
        if 'close' in result.columns:
            result['sma_20'] = result['close'].rolling(window=20).mean()
        
        indicators = self.config.get('indicators', [])
        
        # 添加其他配置的指标
        # 示例：添加简单移动平均线（其他窗口大小）
        if 'sma' in indicators and 'close' in result.columns:
            window = self.config.get('sma_window', 20)
            # 避免重复计算sma_20
            if window != 20:
                result[f'sma_{window}'] = result['close'].rolling(window=window).mean()
            
        # 示例：添加布林带
        if 'bollinger_bands' in indicators and 'close' in result.columns:
            window = self.config.get('bb_window', 20)
            std_dev = self.config.get('bb_std_dev', 2)
            
            # 计算移动平均线，如果已经计算过则不重复计算
            if f'sma_{window}' not in result.columns:
                result[f'sma_{window}'] = result['close'].rolling(window=window).mean()
            
            # 计算标准差
            result[f'std_{window}'] = result['close'].rolling(window=window).std()
            
            # 计算上轨和下轨
            result[f'bb_upper_{window}'] = result[f'sma_{window}'] + (result[f'std_{window}'] * std_dev)
            result[f'bb_lower_{window}'] = result[f'sma_{window}'] - (result[f'std_{window}'] * std_dev)
            
        return result
        
    def clean(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        清洗市场数据
        
        参数:
            data: 市场数据
            **kwargs: 额外参数
            
        返回:
            清洗后的数据
            
        抛出:
            CleaningError: 清洗错误
        """
        result = data.copy()
        
        # 数据清洗处理
        if self.config.get('handle_missing', True):
            result = self._handle_missing(result)
            
        if self.config.get('handle_outliers', True):
            result = self._handle_outliers(result)
            
        if self.config.get('handle_duplicates', True):
            result = result.drop_duplicates()
            
        return result
        
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        转换市场数据
        
        参数:
            data: 市场数据
            **kwargs: 额外参数
            
        返回:
            转换后的数据
            
        抛出:
            TransformationError: 转换错误
        """
        result = data.copy()
        
        # 计算收益率
        if self.config.get('calculate_returns', True):
            result = self._calculate_returns(result)
        
        # 添加技术指标
        if self.config.get('add_indicators', False):
            result = self._add_indicators(result)
            
        return result

