"""
市场数据标准化器
- 提供针对市场数据的多种标准化方法
- 支持交易量、价格、收益率等指标的标准化
- 支持按股票分组标准化
- 支持按时间窗口标准化
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union, Optional, Callable, Tuple, Set
import logging

from .normalizer_interface import BaseNormalizer

class MarketDataNormalizer(BaseNormalizer):
    """
    市场数据标准化器，提供针对市场数据的标准化方法
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化市场数据标准化器
        
        Args:
            config: 标准化器配置
                - error_action: 错误处理方式，'raise'|'log'|'return'
                - default_method: 默认标准化方法
                - window_size: 滚动窗口大小（用于按时间窗口标准化）
                - group_by: 分组列，通常为股票代码列名
                - price_cols: 价格相关列
                - volume_cols: 交易量相关列
                - returns_cols: 收益率相关列
        """
        # 市场数据默认配置
        market_config = {
            'default_method': 'zscore',
            'window_size': 60,  # 默认60个交易日窗口
            'group_by': 'ts_code',  # 默认按股票代码分组
            'price_cols': ['open', 'high', 'low', 'close', 'adj_close'],
            'volume_cols': ['volume', 'amount'],
            'returns_cols': ['daily_return', 'pct_chg']
        }
        
        # 合并用户配置
        if config:
            for k, v in config.items():
                if isinstance(v, dict) and k in market_config and isinstance(market_config[k], dict):
                    market_config[k].update(v)
                else:
                    market_config[k] = v
        
        # 调用父类初始化
        super().__init__(market_config)
        
        # 添加市场数据特有的标准化方法
        self._add_market_data_normalizers()
    
    def _add_market_data_normalizers(self):
        """
        添加市场数据特有的标准化方法
        """
        # 成交量标准化 - 相对于平均日成交量
        self.add_normalizer(
            'volume_to_avg',
            self._volume_to_avg_normalize
        )
        
        # 价格标准化 - 相对于移动平均
        self.add_normalizer(
            'price_to_ma',
            self._price_to_ma_normalize
        )
        
        # 对数收益率标准化
        self.add_normalizer(
            'log_return',
            self._log_return_normalize
        )
        
        # 相对强弱指标(RSI)标准化
        self.add_normalizer(
            'rsi',
            self._rsi_normalize
        )
        
        # 波动率标准化
        self.add_normalizer(
            'volatility',
            self._volatility_normalize
        )
        
        # 滚动窗口Z-score标准化
        self.add_normalizer(
            'rolling_zscore',
            self._rolling_zscore_normalize
        )
        
        # 横截面Z-score标准化（同一时间点不同股票之间）
        self.add_normalizer(
            'cross_sectional_zscore',
            self._cross_sectional_zscore_normalize
        )
    
    def normalize(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        标准化市场数据
        
        Args:
            data: 要标准化的市场数据
            **kwargs: 标准化参数
                - method: 标准化方法
                - columns: 要标准化的列
                - by_group: 是否按股票分组标准化
                - window_size: 滚动窗口大小
                
        Returns:
            pd.DataFrame: 标准化后的数据
        """
        # 设置默认分组标准化（按股票代码分组）
        if 'by_group' not in kwargs and self.config['group_by'] in data.columns:
            kwargs['by_group'] = True
            kwargs['group_columns'] = [self.config['group_by']]
        
        # 调用父类的标准化方法
        return super().normalize(data, **kwargs)
    
    def _volume_to_avg_normalize(self, data: pd.DataFrame, columns: List[str] = None, **kwargs) -> pd.DataFrame:
        """
        成交量标准化 - 相对于平均日成交量
        
        Args:
            data: 要标准化的数据
            columns: 要标准化的列，默认为volume_cols中的列
            **kwargs:
                - window_size: 计算平均成交量的窗口大小
                
        Returns:
            pd.DataFrame: 标准化后的数据
        """
        if data is None or data.empty:
            return data
        
        result = data.copy()
        
        # 获取要标准化的列
        if not columns:
            columns = [col for col in self.config['volume_cols'] if col in data.columns]
        
        # 获取窗口大小
        window_size = kwargs.get('window_size', self.config['window_size'])
        
        # 按分组计算平均成交量并标准化
        group_by = kwargs.get('group_columns', [self.config['group_by']] if self.config['group_by'] in data.columns else None)
        
        if group_by and all(col in data.columns for col in group_by):
            # 按股票分组标准化
            for col in columns:
                if col in result.columns and pd.api.types.is_numeric_dtype(result[col]):
                    # 计算移动平均
                    result[f'{col}_avg'] = result.groupby(group_by)[col].transform(
                        lambda x: x.rolling(window=window_size, min_periods=1).mean()
                    )
                    
                    # 相对于平均值
                    result[col] = result[col] / result[f'{col}_avg']
                    result.drop(f'{col}_avg', axis=1, inplace=True)
        else:
            # 全局标准化
            for col in columns:
                if col in result.columns and pd.api.types.is_numeric_dtype(result[col]):
                    # 计算移动平均
                    result[f'{col}_avg'] = result[col].rolling(window=window_size, min_periods=1).mean()
                    
                    # 相对于平均值
                    result[col] = result[col] / result[f'{col}_avg']
                    result.drop(f'{col}_avg', axis=1, inplace=True)
        
        return result
    
    def _price_to_ma_normalize(self, data: pd.DataFrame, columns: List[str] = None, **kwargs) -> pd.DataFrame:
        """
        价格标准化 - 相对于移动平均
        
        Args:
            data: 要标准化的数据
            columns: 要标准化的列，默认为price_cols中的列
            **kwargs:
                - window_size: 移动平均窗口大小
                - ma_type: 移动平均类型，'sma'(简单移动平均)或'ema'(指数移动平均)
                
        Returns:
            pd.DataFrame: 标准化后的数据
        """
        if data is None or data.empty:
            return data
        
        result = data.copy()
        
        # 获取要标准化的列
        if not columns:
            columns = [col for col in self.config['price_cols'] if col in data.columns]
        
        # 获取窗口大小和移动平均类型
        window_size = kwargs.get('window_size', self.config['window_size'])
        ma_type = kwargs.get('ma_type', 'sma')
        
        # 按分组计算移动平均并标准化
        group_by = kwargs.get('group_columns', [self.config['group_by']] if self.config['group_by'] in data.columns else None)
        
        if group_by and all(col in data.columns for col in group_by):
            # 按股票分组标准化
            for col in columns:
                if col in result.columns and pd.api.types.is_numeric_dtype(result[col]):
                    # 计算移动平均
                    if ma_type == 'ema':
                        result[f'{col}_ma'] = result.groupby(group_by)[col].transform(
                            lambda x: x.ewm(span=window_size, min_periods=1).mean()
                        )
                    else:  # sma
                        result[f'{col}_ma'] = result.groupby(group_by)[col].transform(
                            lambda x: x.rolling(window=window_size, min_periods=1).mean()
                        )
                    
                    # 相对于移动平均
                    result[col] = result[col] / result[f'{col}_ma']
                    result.drop(f'{col}_ma', axis=1, inplace=True)
        else:
            # 全局标准化
            for col in columns:
                if col in result.columns and pd.api.types.is_numeric_dtype(result[col]):
                    # 计算移动平均
                    if ma_type == 'ema':
                        result[f'{col}_ma'] = result[col].ewm(span=window_size, min_periods=1).mean()
                    else:  # sma
                        result[f'{col}_ma'] = result[col].rolling(window=window_size, min_periods=1).mean()
                    
                    # 相对于移动平均
                    result[col] = result[col] / result[f'{col}_ma']
                    result.drop(f'{col}_ma', axis=1, inplace=True)
        
        return result
    
    def _log_return_normalize(self, data: pd.DataFrame, columns: List[str] = None, **kwargs) -> pd.DataFrame:
        """
        对数收益率标准化
        
        Args:
            data: 要标准化的数据
            columns: 要标准化的列，默认为returns_cols中的列
            **kwargs:
                - price_col: 价格列名，如果要根据价格计算收益率
                
        Returns:
            pd.DataFrame: 标准化后的数据
        """
        if data is None or data.empty:
            return data
        
        result = data.copy()
        
        # 获取要标准化的列
        returns_cols = [col for col in self.config['returns_cols'] if col in data.columns]
        price_col = kwargs.get('price_col', None)
        
        if not columns and not returns_cols and price_col and price_col in data.columns:
            # 如果没有指定列，也没有收益率列，但指定了价格列，则计算收益率
            group_by = kwargs.get('group_columns', [self.config['group_by']] if self.config['group_by'] in data.columns else None)
            
            if group_by and all(col in data.columns for col in group_by):
                # 按分组计算收益率
                result['daily_return'] = result.groupby(group_by)[price_col].pct_change()
            else:
                # 全局计算收益率
                result['daily_return'] = result[price_col].pct_change()
            
            columns = ['daily_return']
        elif not columns:
            columns = returns_cols
        
        # 计算对数收益率
        for col in columns:
            if col in result.columns and pd.api.types.is_numeric_dtype(result[col]):
                # 确保数据适合对数变换（收益率+1，避免负值）
                result[col] = np.log1p(result[col])
        
        return result
    
    def _rsi_normalize(self, data: pd.DataFrame, columns: List[str] = None, **kwargs) -> pd.DataFrame:
        """
        相对强弱指标(RSI)标准化
        
        Args:
            data: 要标准化的数据
            columns: 要标准化的列，默认为价格列
            **kwargs:
                - window_size: RSI计算窗口
                - output_col: 输出列名
                - scale: 是否缩放到[-1,1]范围，默认True
                
        Returns:
            pd.DataFrame: 标准化后的数据
        """
        if data is None or data.empty:
            return data
        
        result = data.copy()
        
        # 获取要计算RSI的列
        if not columns:
            columns = ['close'] if 'close' in data.columns else [col for col in self.config['price_cols'] if col in data.columns][:1]
        
        if not columns:
            return result
        
        # 获取RSI窗口大小和输出列名
        window_size = kwargs.get('window_size', 14)  # RSI默认14天
        output_col = kwargs.get('output_col', f'rsi_{window_size}')
        scale = kwargs.get('scale', True)
        
        # 按分组计算RSI
        group_by = kwargs.get('group_columns', [self.config['group_by']] if self.config['group_by'] in data.columns else None)
        
        for col in columns:
            if col in result.columns and pd.api.types.is_numeric_dtype(result[col]):
                if group_by and all(col in data.columns for col in group_by):
                    # 按分组计算RSI
                    for name, group in result.groupby(group_by):
                        # 计算价格变化
                        delta = group[col].diff()
                        
                        # 分离上涨和下跌
                        gain = delta.copy()
                        loss = delta.copy()
                        gain[gain < 0] = 0
                        loss[loss > 0] = 0
                        loss = abs(loss)
                        
                        # 计算平均涨跌幅
                        avg_gain = gain.rolling(window=window_size, min_periods=1).mean()
                        avg_loss = loss.rolling(window=window_size, min_periods=1).mean()
                        
                        # 计算相对强度
                        rs = avg_gain / (avg_loss + 1e-10)  # 避免除零
                        
                        # 计算RSI
                        group_rsi = 100 - (100 / (1 + rs))
                        
                        # 确保值在[0, 100]范围内
                        group_rsi = group_rsi.clip(0, 100)
                        
                        # 将结果写回对应的行
                        result.loc[group.index, output_col] = group_rsi
                else:
                    # 全局计算RSI
                    # 计算价格变化
                    delta = result[col].diff()
                    
                    # 分离上涨和下跌
                    gain = delta.copy()
                    loss = delta.copy()
                    gain[gain < 0] = 0
                    loss[loss > 0] = 0
                    loss = abs(loss)
                    
                    # 计算平均涨跌幅
                    avg_gain = gain.rolling(window=window_size, min_periods=1).mean()
                    avg_loss = loss.rolling(window=window_size, min_periods=1).mean()
                    
                    # 计算相对强度
                    rs = avg_gain / (avg_loss + 1e-10)  # 避免除零
                    
                    # 计算RSI
                    result[output_col] = 100 - (100 / (1 + rs))
                    
                    # 确保值在[0, 100]范围内
                    result[output_col] = result[output_col].clip(0, 100)
        
        # 标准化RSI到[-1,1]范围
        if scale and output_col in result.columns:
            result[output_col] = 2 * (result[output_col] / 100) - 1
        
        return result
    
    def _volatility_normalize(self, data: pd.DataFrame, columns: List[str] = None, **kwargs) -> pd.DataFrame:
        """
        波动率标准化
        
        Args:
            data: 要标准化的数据
            columns: 要标准化的列，默认为价格列
            **kwargs:
                - window_size: 波动率计算窗口
                - output_col: 输出列名
                - method: 波动率计算方法，'std'或'parkinson'
                
        Returns:
            pd.DataFrame: 标准化后的数据，添加波动率列
        """
        if data is None or data.empty:
            return data
        
        result = data.copy()
        
        # 获取要计算波动率的列
        if not columns:
            columns = ['close'] if 'close' in data.columns else [col for col in self.config['price_cols'] if col in data.columns][:1]
        
        if not columns:
            return result
        
        # 获取波动率窗口大小、输出列名和计算方法
        window_size = kwargs.get('window_size', self.config['window_size'])
        method = kwargs.get('method', 'std')
        
        # 按分组计算波动率
        group_by = kwargs.get('group_columns', [self.config['group_by']] if self.config['group_by'] in data.columns else None)
        
        for col in columns:
            if col in result.columns and pd.api.types.is_numeric_dtype(result[col]):
                output_col = kwargs.get('output_col', f'{col}_volatility')
                
                if method == 'parkinson' and 'high' in result.columns and 'low' in result.columns:
                    # 使用Parkinson波动率公式（基于高低价）
                    # 确保高低价都是正数，避免log计算问题
                    high_positive = result['high'] > 0
                    low_positive = result['low'] > 0
                    valid_rows = high_positive & low_positive
                    
                    if valid_rows.any():
                        log_hl_ratio = np.log(result.loc[valid_rows, 'high'] / result.loc[valid_rows, 'low'])
                        
                        if group_by and all(col_g in data.columns for col_g in group_by):
                            # 按分组计算Parkinson波动率
                            for name, group in result.groupby(group_by):
                                # 找出组内有效行
                                group_valid_rows = valid_rows.loc[group.index]
                                
                                if group_valid_rows.any():
                                    # 计算波动率
                                    group_log_hl = log_hl_ratio.loc[group.index[group_valid_rows]]
                                    volatility = np.sqrt(
                                        1 / (4 * np.log(2)) * 
                                        group_log_hl ** 2
                                    ).rolling(window=window_size, min_periods=1).mean()
                                    
                                    # 将结果写回对应的行
                                    result.loc[group.index[group_valid_rows], output_col] = volatility
                        else:
                            # 全局计算Parkinson波动率
                            volatility = np.sqrt(
                                1 / (4 * np.log(2)) * 
                                log_hl_ratio ** 2
                            ).rolling(window=window_size, min_periods=1).mean()
                            
                            # 将结果写回对应的行
                            result.loc[valid_rows, output_col] = volatility
                else:
                    # 使用标准差作为波动率
                    if group_by and all(col_g in data.columns for col_g in group_by):
                        # 按分组计算波动率
                        for name, group in result.groupby(group_by):
                            # 计算收益率
                            returns = group[col].pct_change()
                            
                            # 计算波动率
                            volatility = returns.rolling(window=window_size, min_periods=1).std()
                            
                            # 将结果写回对应的行，确保值非负
                            result.loc[group.index, output_col] = volatility.abs()
                    else:
                        # 全局计算波动率
                        # 计算收益率
                        returns = result[col].pct_change()
                        
                        # 计算波动率
                        volatility = returns.rolling(window=window_size, min_periods=1).std()
                        
                        # 确保值非负
                        result[output_col] = volatility.abs()
        
        return result
    
    def _rolling_zscore_normalize(self, data: pd.DataFrame, columns: List[str] = None, **kwargs) -> pd.DataFrame:
        """
        滚动窗口Z-score标准化
        
        Args:
            data: 要标准化的数据
            columns: 要标准化的列
            **kwargs:
                - window_size: 滚动窗口大小
                
        Returns:
            pd.DataFrame: 标准化后的数据
        """
        if data is None or data.empty:
            return data
        
        result = data.copy()
        
        # 获取要标准化的列
        if not columns:
            # 默认使用所有数值列
            columns = result.select_dtypes(include=['number']).columns.tolist()
        
        # 获取窗口大小
        window_size = kwargs.get('window_size', self.config['window_size'])
        
        # 按分组进行滚动Z-score标准化
        group_by = kwargs.get('group_columns', [self.config['group_by']] if self.config['group_by'] in data.columns else None)
        
        if group_by and all(col in data.columns for col in group_by):
            # 按分组标准化
            for col in columns:
                if col in result.columns and pd.api.types.is_numeric_dtype(result[col]):
                    # 计算滚动均值和标准差
                    rolling_mean = result.groupby(group_by)[col].transform(
                        lambda x: x.rolling(window=window_size, min_periods=1).mean()
                    )
                    rolling_std = result.groupby(group_by)[col].transform(
                        lambda x: x.rolling(window=window_size, min_periods=1).std()
                    )
                    
                    # 应用Z-score标准化
                    result[col] = (result[col] - rolling_mean) / (rolling_std + 1e-10)  # 避免除零
        else:
            # 全局标准化
            for col in columns:
                if col in result.columns and pd.api.types.is_numeric_dtype(result[col]):
                    # 计算滚动均值和标准差
                    rolling_mean = result[col].rolling(window=window_size, min_periods=1).mean()
                    rolling_std = result[col].rolling(window=window_size, min_periods=1).std()
                    
                    # 应用Z-score标准化
                    result[col] = (result[col] - rolling_mean) / (rolling_std + 1e-10)  # 避免除零
        
        return result
    
    def _cross_sectional_zscore_normalize(self, data: pd.DataFrame, columns: List[str] = None, **kwargs) -> pd.DataFrame:
        """
        横截面Z-score标准化（同一时间点不同股票之间）
        
        Args:
            data: 要标准化的数据
            columns: 要标准化的列
            **kwargs:
                - time_col: 时间列名，默认为'trade_date'
                
        Returns:
            pd.DataFrame: 标准化后的数据
        """
        if data is None or data.empty:
            return data
        
        # 创建数据副本，避免修改原始数据
        result = data.copy()
        
        # 获取要标准化的列
        if not columns:
            # 默认使用所有数值列
            columns = result.select_dtypes(include=['number']).columns.tolist()
        
        # 获取时间列名
        time_col = kwargs.get('time_col', 'trade_date')
        
        # 检查时间列是否存在
        if time_col not in result.columns:
            self.logger.warning(f"横截面标准化需要时间列'{time_col}'，但数据中不存在")
            return result
        
        # 手动实现按时间分组的标准化
        for col in columns:
            if col in result.columns and pd.api.types.is_numeric_dtype(result[col]):
                # 对每个时间点分别标准化
                for date, group in result.groupby(time_col):
                    # 确保分组中有足够的数据点
                    if len(group) > 1:
                        # 计算均值和标准差
                        mean = group[col].mean()
                        std = group[col].std()
                        
                        # 标准化，避免标准差为零的情况
                        if std > 0:
                            # 使用Z-score标准化: (x - μ) / σ
                            result.loc[group.index, col] = (group[col] - mean) / std
                        else:
                            # 如果标准差为零，说明所有值相等，设为零
                            result.loc[group.index, col] = 0
                            self.logger.warning(f"时间点 {date} 的列 {col} 标准差为零，无法进行标准化")
        
        return result 