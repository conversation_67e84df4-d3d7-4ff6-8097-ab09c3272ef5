"""
数据标准化器接口
- 定义数据标准化的基本方法和规则
- 提供标准化异常处理
- 支持多种标准化方法
"""

from abc import ABC, abstractmethod
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union, Optional, Callable, Tuple, Set
import logging

from src.data.processors.processor_interface import ProcessorInterface, TransformationError, CleaningError, ValidationError

class NormalizerInterface(ProcessorInterface, ABC):
    """
    数据标准化器接口，定义数据标准化的基本方法
    """
    
    @abstractmethod
    def normalize(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        标准化数据
        
        Args:
            data: 要标准化的数据
            **kwargs: 标准化参数
                - method: 标准化方法，'zscore', 'minmax', 'robust'等
                - columns: 要标准化的列
                - by_group: 是否按分组标准化
                - group_columns: 分组列
                
        Returns:
            pd.DataFrame: 标准化后的数据
            
        Raises:
            TransformationError: 标准化错误
        """
        pass
    
    @abstractmethod
    def add_normalizer(self, normalizer_name: str, normalizer_func: Callable, **kwargs) -> None:
        """
        添加标准化方法
        
        Args:
            normalizer_name: 标准化方法名称
            normalizer_func: 标准化函数，接受DataFrame和额外参数，返回DataFrame
            **kwargs: 标准化函数的额外参数
            
        Returns:
            None
        """
        pass
    
    @abstractmethod
    def remove_normalizer(self, normalizer_name: str) -> bool:
        """
        移除标准化方法
        
        Args:
            normalizer_name: 标准化方法名称
            
        Returns:
            bool: 是否成功移除
        """
        pass

class BaseNormalizer(NormalizerInterface):
    """
    基础标准化器，提供通用的数据标准化功能
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化基础标准化器
        
        Args:
            config: 标准化器配置
                - error_action: 错误处理方式，'raise'|'log'|'return'
                - normalizers: 预定义标准化方法列表
                - default_method: 默认标准化方法
                - default_columns: 默认要标准化的列
                - by_group: 是否按分组标准化
                - group_columns: 分组列
        """
        self.normalizers = {}  # 标准化方法字典
        self.results = {}  # 标准化结果
        self.errors = []  # 错误信息列表
        
        # 默认配置
        self.config = {
            'error_action': 'raise',  # 'raise', 'log', 'return'
            'normalizers': {},
            'default_method': 'zscore',
            'default_columns': None,  # 默认为None，表示所有数值列
            'by_group': False,
            'group_columns': []
        }
        
        # 更新配置
        if config:
            for key, value in config.items():
                if isinstance(value, dict) and key in self.config and isinstance(self.config[key], dict):
                    self.config[key].update(value)
                else:
                    self.config[key] = value
        
        # 设置日志
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化预定义标准化方法
        self._init_predefined_normalizers()
    
    def _init_predefined_normalizers(self) -> None:
        """
        初始化预定义的标准化方法
        """
        # Z-score标准化：(x - mean) / std
        self.add_normalizer(
            'zscore',
            self._zscore_normalize
        )
        
        # Min-max标准化：(x - min) / (max - min)
        self.add_normalizer(
            'minmax',
            self._minmax_normalize
        )
        
        # 鲁棒标准化：(x - median) / IQR
        self.add_normalizer(
            'robust',
            self._robust_normalize
        )
        
        # 分位数标准化：将数据转换为百分位数
        self.add_normalizer(
            'quantile',
            self._quantile_normalize
        )
        
        # 对数标准化：log(x + offset)
        self.add_normalizer(
            'log',
            self._log_normalize,
            offset=1.0
        )
        
        # 幂变换标准化：(x ^ lambda - 1) / lambda
        self.add_normalizer(
            'power',
            self._power_normalize,
            lambda_val=0.5  # 默认使用平方根变换
        )
    
    def normalize(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        标准化数据
        
        Args:
            data: 要标准化的数据
            **kwargs: 标准化参数
                - method: 标准化方法，'zscore', 'minmax', 'robust'等
                - columns: 要标准化的列
                - by_group: 是否按分组标准化
                - group_columns: 分组列
                
        Returns:
            pd.DataFrame: 标准化后的数据
            
        Raises:
            TransformationError: 标准化错误
        """
        if data is None or data.empty:
            if self.config['error_action'] == 'raise':
                raise TransformationError("数据为空")
            elif self.config['error_action'] == 'log':
                self.logger.error("数据为空")
            return data
        
        # 重置结果和错误
        self.results = {}
        self.errors = []
        
        # 创建数据副本，避免修改原始数据
        result_data = data.copy()
        
        try:
            # 获取标准化方法
            method = kwargs.get('method', self.config['default_method'])
            if method not in self.normalizers:
                raise TransformationError(f"未知的标准化方法: {method}")
            
            # 获取要标准化的列
            columns = kwargs.get('columns', self.config['default_columns'])
            if columns is None:
                # 默认标准化所有数值列
                columns = result_data.select_dtypes(include=['number']).columns.tolist()
            
            # 检查列是否存在
            missing_columns = [col for col in columns if col not in result_data.columns]
            if missing_columns:
                raise TransformationError(f"列不存在: {missing_columns}")
            
            # 获取是否按分组标准化
            by_group = kwargs.get('by_group', self.config['by_group'])
            group_columns = kwargs.get('group_columns', self.config['group_columns'])
            
            # 如果按分组标准化，检查分组列是否存在
            if by_group and group_columns:
                missing_group_columns = [col for col in group_columns if col not in result_data.columns]
                if missing_group_columns:
                    raise TransformationError(f"分组列不存在: {missing_group_columns}")
            
            # 执行标准化
            normalizer = self.normalizers[method]
            # 创建一个新的参数字典，避免修改原始kwargs
            normalizer_params = normalizer['params'].copy()
            
            # 添加其他参数，包括columns（确保传递给normalizer函数）
            for key, value in kwargs.items():
                if key != 'method':  # 只排除method
                    normalizer_params[key] = value
            
            if by_group and group_columns:
                # 按分组标准化
                grouped_data = []
                for name, group in result_data.groupby(group_columns):
                    try:
                        normalized_group = normalizer['func'](group, **normalizer_params)
                        grouped_data.append(normalized_group)
                    except Exception as e:
                        error_msg = f"分组 {name} 标准化失败: {str(e)}"
                        self.errors.append({
                            'group': name,
                            'message': error_msg
                        })
                        
                        # 处理错误
                        error_action = kwargs.get('error_action', self.config['error_action'])
                        if error_action == 'raise':
                            raise TransformationError(error_msg)
                        elif error_action == 'log':
                            self.logger.error(error_msg)
                        
                        # 如果不抛出异常，使用原始分组数据
                        grouped_data.append(group)
                
                if grouped_data:
                    result_data = pd.concat(grouped_data, ignore_index=False)
            else:
                # 全局标准化
                result_data = normalizer['func'](result_data, **normalizer_params)
            
            self.results = {
                'success': True,
                'method': method,
                'columns': columns,
                'by_group': by_group,
                'group_columns': group_columns
            }
            
            return result_data
            
        except Exception as e:
            error_msg = f"数据标准化失败: {str(e)}"
            self.errors.append({
                'message': error_msg
            })
            
            # 处理错误
            error_action = kwargs.get('error_action', self.config['error_action'])
            if error_action == 'raise':
                raise TransformationError(error_msg)
            elif error_action == 'log':
                self.logger.error(error_msg)
            
            return data
    
    def add_normalizer(self, normalizer_name: str, normalizer_func: Callable, **kwargs) -> None:
        """
        添加标准化方法
        
        Args:
            normalizer_name: 标准化方法名称
            normalizer_func: 标准化函数，接受DataFrame和额外参数，返回DataFrame
            **kwargs: 标准化函数的额外参数
            
        Returns:
            None
        """
        self.normalizers[normalizer_name] = {
            'func': normalizer_func,
            'params': kwargs
        }
    
    def remove_normalizer(self, normalizer_name: str) -> bool:
        """
        移除标准化方法
        
        Args:
            normalizer_name: 标准化方法名称
            
        Returns:
            bool: 是否成功移除
        """
        if normalizer_name in self.normalizers:
            del self.normalizers[normalizer_name]
            return True
        return False
    
    def get_normalizers(self) -> Dict[str, Dict]:
        """
        获取所有标准化方法
        
        Returns:
            Dict[str, Dict]: 标准化方法字典
        """
        return self.normalizers
    
    def get_results(self) -> Dict[str, Any]:
        """
        获取最近一次标准化结果
        
        Returns:
            Dict[str, Any]: 标准化结果
        """
        return {
            'success': len(self.errors) == 0,
            'results': self.results,
            'errors': self.errors
        }
    
    def process(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        处理数据（ProcessorInterface 接口实现）
        
        Args:
            data: 要处理的数据
            **kwargs: 额外参数
            
        Returns:
            pd.DataFrame: 处理后的数据
        """
        return self.normalize(data, **kwargs)
    
    def validate(self, data: pd.DataFrame, rules: Dict[str, Any] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        验证数据（ProcessorInterface 接口实现）
        
        Args:
            data: 要验证的数据
            rules: 验证规则
            
        Returns:
            Tuple[bool, Dict[str, Any]]: (是否有效, 验证结果详情)
            
        Raises:
            ValidationError: 验证错误
        """
        # 标准化器主要负责标准化，验证功能相对简单
        # 这里只检查数据是否为空和是否有数值列
        if data is None or data.empty:
            return False, {'error': '数据为空'}
        
        # 检查是否有数值列
        numeric_columns = data.select_dtypes(include=['number']).columns.tolist()
        if not numeric_columns:
            return False, {'error': '数据中没有数值列，无法进行标准化'}
        
        return True, {'numeric_columns': numeric_columns}
    
    def clean(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        清洗数据（ProcessorInterface 接口实现）
        
        Args:
            data: 要清洗的数据
            **kwargs: 清洗参数
            
        Returns:
            pd.DataFrame: 清洗后的数据
            
        Raises:
            CleaningError: 清洗错误
        """
        # 标准化器主要负责标准化，清洗功能相对简单
        # 这里只处理缺失值和极端值
        if data is None or data.empty:
            return data
        
        result = data.copy()
        
        try:
            # 处理缺失值
            if kwargs.get('handle_missing', True):
                strategy = kwargs.get('missing_strategy', 'mean')
                numeric_columns = result.select_dtypes(include=['number']).columns.tolist()
                
                for col in numeric_columns:
                    if result[col].isnull().any():
                        if strategy == 'mean':
                            result[col] = result[col].fillna(result[col].mean())
                        elif strategy == 'median':
                            result[col] = result[col].fillna(result[col].median())
                        elif strategy == 'mode':
                            result[col] = result[col].fillna(result[col].mode()[0])
                        elif strategy == 'drop':
                            result = result.dropna(subset=[col])
            
            # 处理极端值
            if kwargs.get('handle_outliers', False):
                method = kwargs.get('outlier_method', 'zscore')
                threshold = kwargs.get('outlier_threshold', 3.0)
                numeric_columns = result.select_dtypes(include=['number']).columns.tolist()
                
                if method == 'zscore':
                    for col in numeric_columns:
                        mean = result[col].mean()
                        std = result[col].std()
                        if std > 0:
                            z_scores = abs((result[col] - mean) / std)
                            result.loc[z_scores > threshold, col] = np.nan
                
                elif method == 'iqr':
                    for col in numeric_columns:
                        q1 = result[col].quantile(0.25)
                        q3 = result[col].quantile(0.75)
                        iqr = q3 - q1
                        if iqr > 0:
                            lower_bound = q1 - threshold * iqr
                            upper_bound = q3 + threshold * iqr
                            result.loc[(result[col] < lower_bound) | (result[col] > upper_bound), col] = np.nan
            
            return result
            
        except Exception as e:
            if kwargs.get('error_action', self.config['error_action']) == 'raise':
                raise CleaningError(f"数据清洗失败: {str(e)}")
            self.logger.error(f"数据清洗失败: {str(e)}")
            return data
    
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        转换数据（ProcessorInterface 接口实现）
        
        Args:
            data: 要转换的数据
            **kwargs: 转换参数
            
        Returns:
            pd.DataFrame: 转换后的数据
            
        Raises:
            TransformationError: 转换错误
        """
        # 对于标准化器，transform方法就是normalize方法
        return self.normalize(data, **kwargs)
    
    # 标准化方法实现
    def _zscore_normalize(self, data: pd.DataFrame, columns: List[str] = None, **kwargs) -> pd.DataFrame:
        """
        Z-score标准化：(x - mean) / std
        
        Args:
            data: 要标准化的数据
            columns: 要标准化的列
            **kwargs: 额外参数
            
        Returns:
            pd.DataFrame: 标准化后的数据
        """
        if not columns:
            columns = data.select_dtypes(include=['number']).columns.tolist()
        
        result = data.copy()
        
        for col in columns:
            if col in result.columns and pd.api.types.is_numeric_dtype(result[col]):
                mean = result[col].mean()
                std = result[col].std()
                if std > 0:  # 避免除以零
                    result.loc[:, col] = (result[col] - mean) / std
                else:
                    # 如果标准差为零，所有值相等，设为零或保持不变
                    if kwargs.get('zero_variance', 'set_zero') == 'set_zero':
                        result.loc[:, col] = 0
        
        return result
    
    def _minmax_normalize(self, data: pd.DataFrame, columns: List[str] = None, **kwargs) -> pd.DataFrame:
        """
        Min-max标准化：(x - min) / (max - min)
        
        Args:
            data: 要标准化的数据
            columns: 要标准化的列
            **kwargs: 额外参数
                - feature_range: 目标范围，默认为(0, 1)
            
        Returns:
            pd.DataFrame: 标准化后的数据
        """
        if not columns:
            columns = data.select_dtypes(include=['number']).columns.tolist()
        
        result = data.copy()
        feature_range = kwargs.get('feature_range', (0, 1))
        min_range, max_range = feature_range
        
        for col in columns:
            if col in result.columns and pd.api.types.is_numeric_dtype(result[col]):
                min_val = result[col].min()
                max_val = result[col].max()
                if max_val > min_val:  # 避免除以零
                    result.loc[:, col] = min_range + (result[col] - min_val) * (max_range - min_range) / (max_val - min_val)
                else:
                    # 如果所有值相等，设为范围的中点或保持不变
                    if kwargs.get('zero_range', 'set_midpoint') == 'set_midpoint':
                        result.loc[:, col] = (min_range + max_range) / 2
        
        return result
    
    def _robust_normalize(self, data: pd.DataFrame, columns: List[str] = None, **kwargs) -> pd.DataFrame:
        """
        鲁棒标准化：(x - median) / IQR
        
        Args:
            data: 要标准化的数据
            columns: 要标准化的列
            **kwargs: 额外参数
            
        Returns:
            pd.DataFrame: 标准化后的数据
        """
        if not columns:
            columns = data.select_dtypes(include=['number']).columns.tolist()
        
        result = data.copy()
        
        for col in columns:
            if col in result.columns and pd.api.types.is_numeric_dtype(result[col]):
                median = result[col].median()
                q1 = result[col].quantile(0.25)
                q3 = result[col].quantile(0.75)
                iqr = q3 - q1
                if iqr > 0:  # 避免除以零
                    result.loc[:, col] = (result[col] - median) / iqr
                else:
                    # 如果IQR为零，所有值相等或分布非常集中，设为零或保持不变
                    if kwargs.get('zero_iqr', 'set_zero') == 'set_zero':
                        result.loc[:, col] = 0
        
        return result
    
    def _quantile_normalize(self, data: pd.DataFrame, columns: List[str] = None, **kwargs) -> pd.DataFrame:
        """
        分位数标准化：将数据转换为百分位数
        
        Args:
            data: 要标准化的数据
            columns: 要标准化的列
            **kwargs: 额外参数
            
        Returns:
            pd.DataFrame: 标准化后的数据
        """
        if not columns:
            columns = data.select_dtypes(include=['number']).columns.tolist()
        
        result = data.copy()
        
        for col in columns:
            if col in result.columns and pd.api.types.is_numeric_dtype(result[col]):
                # 计算每个值的分位数（0-1之间）
                result.loc[:, col] = result[col].rank(pct=True)
        
        return result
    
    def _log_normalize(self, data: pd.DataFrame, columns: List[str] = None, **kwargs) -> pd.DataFrame:
        """
        对数标准化：log(x + offset)
        
        Args:
            data: 要标准化的数据
            columns: 要标准化的列
            **kwargs: 额外参数
                - offset: 偏移量，防止取log(0)，默认为1.0
                - base: 对数的底，默认为自然对数e
            
        Returns:
            pd.DataFrame: 标准化后的数据
        """
        if not columns:
            columns = data.select_dtypes(include=['number']).columns.tolist()
        
        result = data.copy()
        offset = kwargs.get('offset', 1.0)
        base = kwargs.get('base', None)  # None表示自然对数
        
        for col in columns:
            if col in result.columns and pd.api.types.is_numeric_dtype(result[col]):
                # 检查数据是否都为非负
                if (result[col] + offset <= 0).any():
                    # 找到最小值，并调整偏移量确保所有值为正
                    min_val = result[col].min()
                    if min_val < 0:
                        new_offset = abs(min_val) + offset
                        self.logger.warning(f"列 {col} 包含负值，偏移量从 {offset} 调整为 {new_offset}")
                        offset = new_offset
                
                # 应用对数变换
                if base is None:
                    # 自然对数
                    result.loc[:, col] = np.log(result[col] + offset)
                else:
                    # 指定底的对数
                    result.loc[:, col] = np.log(result[col] + offset) / np.log(base)
        
        return result
    
    def _power_normalize(self, data: pd.DataFrame, columns: List[str] = None, **kwargs) -> pd.DataFrame:
        """
        幂变换标准化：(x ^ lambda - 1) / lambda  (Box-Cox变换)
        
        Args:
            data: 要标准化的数据
            columns: 要标准化的列
            **kwargs: 额外参数
                - lambda_val: 幂参数，默认为0.5（平方根变换）
                - offset: 偏移量，防止负值，默认为0.0
            
        Returns:
            pd.DataFrame: 标准化后的数据
        """
        if not columns:
            columns = data.select_dtypes(include=['number']).columns.tolist()
        
        result = data.copy()
        lambda_val = kwargs.get('lambda_val', 0.5)
        offset = kwargs.get('offset', 0.0)
        
        for col in columns:
            if col in result.columns and pd.api.types.is_numeric_dtype(result[col]):
                # 检查数据是否都为非负
                if (result[col] + offset <= 0).any():
                    # 找到最小值，并调整偏移量确保所有值为正
                    min_val = result[col].min()
                    if min_val < 0:
                        new_offset = abs(min_val) + 1.0
                        self.logger.warning(f"列 {col} 包含负值，偏移量从 {offset} 调整为 {new_offset}")
                        offset = new_offset
                
                # 应用幂变换
                if abs(lambda_val) < 1e-10:
                    # 如果lambda接近0，使用对数变换
                    result.loc[:, col] = np.log(result[col] + offset)
                else:
                    # Box-Cox变换
                    result.loc[:, col] = ((result[col] + offset) ** lambda_val - 1) / lambda_val
        
        return result

