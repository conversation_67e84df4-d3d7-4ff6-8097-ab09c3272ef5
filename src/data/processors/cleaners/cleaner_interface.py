"""
数据清洗器接口
- 定义数据清洗流程和方法
- 支持异常检测和数据修复
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Union, Optional, Tuple, Set
import pandas as pd
import numpy as np
import datetime
import logging
from ..processor_interface import ProcessorInterface, ValidationError, CleaningError

class CleanerInterface(ProcessorInterface):
    """
    数据清洗器接口，继承自处理器接口
    所有数据清洗器必须实现这个接口
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化清洗器
        
        Args:
            config: 清洗器配置
        """
        super().__init__(config)
        
        # 记录清洗过程中的变更
        self.changes = {
            'missing_values': 0,
            'outliers': 0,
            'duplicates': 0,
            'errors': 0
        }
    
    def process(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        处理数据的主方法
        
        Args:
            data: 输入数据
            **kwargs: 额外参数
            
        Returns:
            处理后的数据
            
        Raises:
            CleaningError: 清洗错误
        """
        if data.empty:
            return data
        
        try:
            # 重置变更计数
            self.changes = {
                'missing_values': 0,
                'outliers': 0,
                'duplicates': 0,
                'errors': 0
            }
            
            # 记录原始数据统计
            self.log_stats(data, stage="清洗前")
            
            # 1. 验证数据
            is_valid, validation_results = self.validate(data)
            if not is_valid:
                self.logger.warning(f"数据验证不通过: {validation_results}")
            
            # 2. 清洗数据
            cleaned_data = self.clean(data, **kwargs)
            
            # 3. 转换数据
            result = self.transform(cleaned_data, **kwargs)
            
            # 记录处理后数据统计
            self.log_stats(result, stage="清洗后")
            
            # 记录变更总结
            self.logger.info(f"数据清洗完成，变更统计: {self.changes}")
            
            return result
        except Exception as e:
            self.logger.error(f"数据处理失败: {str(e)}")
            raise CleaningError(f"数据处理失败: {str(e)}")
    
    def validate(self, data: pd.DataFrame, rules: Dict[str, Any] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        验证数据
        
        Args:
            data: 要验证的数据
            rules: 验证规则
            
        Returns:
            (是否有效, 验证结果详情)
            
        Raises:
            ValidationError: 验证错误
        """
        # 基本验证规则，子类可以扩展
        validation_results = {
            'is_valid': True,
            'missing_values': {},
            'missing_columns': [],
            'incorrect_type_columns': [],
            'details': {}
        }
        
        try:
            # 检查是否为空
            if data.empty:
                validation_results['is_valid'] = False
                validation_results['details']['empty'] = "数据为空"
                return False, validation_results
            
            # 检查必要的列是否存在
            required_columns = self.config.get('required_columns', [])
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                validation_results['is_valid'] = False
                validation_results['missing_columns'] = missing_columns
                validation_results['details']['missing_columns'] = f"缺少必要列: {missing_columns}"
            
            # 检查每列的缺失值
            missing_values = data.isnull().sum()
            validation_results['missing_values'] = missing_values.to_dict()
            
            # 检查列的数据类型
            expected_types = self.config.get('column_types', {})
            incorrect_type_columns = []
            
            for col, expected_type in expected_types.items():
                if col not in data.columns:
                    continue
                    
                is_correct = False
                if expected_type == 'numeric':
                    is_correct = pd.api.types.is_numeric_dtype(data[col])
                elif expected_type == 'datetime':
                    is_correct = pd.api.types.is_datetime64_dtype(data[col])
                elif expected_type == 'string':
                    is_correct = pd.api.types.is_string_dtype(data[col])
                elif expected_type == 'boolean':
                    is_correct = pd.api.types.is_bool_dtype(data[col])
                
                if not is_correct:
                    incorrect_type_columns.append(col)
            
            if incorrect_type_columns:
                validation_results['is_valid'] = False
                validation_results['incorrect_type_columns'] = incorrect_type_columns
                validation_results['details']['incorrect_type_columns'] = f"列数据类型不符: {incorrect_type_columns}"
            
            # 执行自定义验证规则
            if rules:
                custom_validation = self._validate_custom_rules(data, rules)
                validation_results['custom_validation'] = custom_validation
                if not custom_validation['is_valid']:
                    validation_results['is_valid'] = False
                    validation_results['details']['custom_rules'] = custom_validation['details']
            
            return validation_results['is_valid'], validation_results
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {str(e)}")
            raise ValidationError(f"数据验证失败: {str(e)}")
    
    def _validate_custom_rules(self, data: pd.DataFrame, rules: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行自定义验证规则
        
        Args:
            data: 要验证的数据
            rules: 验证规则
            
        Returns:
            验证结果
        """
        result = {
            'is_valid': True,
            'details': {}
        }
        
        try:
            for rule_name, rule_config in rules.items():
                rule_type = rule_config.get('type')
                
                # 1. 范围检查
                if rule_type == 'range':
                    column = rule_config.get('column')
                    min_value = rule_config.get('min')
                    max_value = rule_config.get('max')
                    
                    if column not in data.columns:
                        continue
                    
                    if not pd.api.types.is_numeric_dtype(data[column]):
                        continue
                    
                    # 检查范围
                    if min_value is not None and data[column].min() < min_value:
                        result['is_valid'] = False
                        result['details'][f"{rule_name}_min"] = f"列 {column} 的最小值 {data[column].min()} 小于 {min_value}"
                    
                    if max_value is not None and data[column].max() > max_value:
                        result['is_valid'] = False
                        result['details'][f"{rule_name}_max"] = f"列 {column} 的最大值 {data[column].max()} 大于 {max_value}"
                
                # 2. 唯一性检查
                elif rule_type == 'unique':
                    column = rule_config.get('column')
                    
                    if column not in data.columns:
                        continue
                    
                    if data[column].duplicated().any():
                        result['is_valid'] = False
                        dup_count = data[column].duplicated().sum()
                        result['details'][f"{rule_name}"] = f"列 {column} 存在 {dup_count} 个重复值"
                
                # 3. 模式检查
                elif rule_type == 'pattern':
                    column = rule_config.get('column')
                    pattern = rule_config.get('pattern')
                    
                    if column not in data.columns or not pattern:
                        continue
                    
                    # 转换为字符串列后检查模式
                    str_col = data[column].astype(str)
                    invalid_pattern = ~str_col.str.match(pattern)
                    if invalid_pattern.any():
                        result['is_valid'] = False
                        invalid_count = invalid_pattern.sum()
                        result['details'][f"{rule_name}"] = f"列 {column} 中 {invalid_count} 个值不符合模式 {pattern}"
                
                # 4. 值列表检查
                elif rule_type == 'values':
                    column = rule_config.get('column')
                    allowed_values = rule_config.get('values', [])
                    
                    if column not in data.columns or not allowed_values:
                        continue
                    
                    invalid_values = ~data[column].isin(allowed_values)
                    if invalid_values.any():
                        result['is_valid'] = False
                        invalid_count = invalid_values.sum()
                        result['details'][f"{rule_name}"] = f"列 {column} 中 {invalid_count} 个值不在允许的值列表中"
                
                # 5. 必填检查
                elif rule_type == 'required':
                    column = rule_config.get('column')
                    
                    if column not in data.columns:
                        continue
                    
                    missing_count = data[column].isnull().sum()
                    if missing_count > 0:
                        result['is_valid'] = False
                        result['details'][f"{rule_name}"] = f"列 {column} 中存在 {missing_count} 个缺失值"
                
                # 6. 依赖列检查
                elif rule_type == 'dependency':
                    column = rule_config.get('column')
                    depends_on = rule_config.get('depends_on')
                    
                    if column not in data.columns or depends_on not in data.columns:
                        continue
                    
                    # 如果主列有值，依赖列也必须有值
                    invalid_deps = data[column].notnull() & data[depends_on].isnull()
                    if invalid_deps.any():
                        result['is_valid'] = False
                        invalid_count = invalid_deps.sum()
                        result['details'][f"{rule_name}"] = f"列 {column} 有值但其依赖列 {depends_on} 缺失的行数: {invalid_count}"
            
            return result
            
        except Exception as e:
            self.logger.error(f"执行自定义验证规则失败: {str(e)}")
            result['is_valid'] = False
            result['details']['error'] = f"验证规则执行错误: {str(e)}"
            return result
    
    def clean(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        清洗数据
        
        Args:
            data: 要清洗的数据
            **kwargs: 清洗参数
            
        Returns:
            清洗后的数据
            
        Raises:
            CleaningError: 清洗错误
        """
        if data.empty:
            return data
            
        result = data.copy()
        
        try:
            # 1. 处理重复行
            if kwargs.get('remove_duplicates', True):
                duplicates_count = result.duplicated().sum()
                if duplicates_count > 0:
                    dup_subset = kwargs.get('duplicates_subset')
                    dup_keep = kwargs.get('duplicates_keep', 'first')
                    
                    old_count = len(result)
                    result = result.drop_duplicates(subset=dup_subset, keep=dup_keep)
                    removed_count = old_count - len(result)
                    
                    self.changes['duplicates'] = removed_count
                    self.logger.info(f"已删除 {removed_count} 行重复数据")
            
            # 2. 处理缺失值
            missing_strategies = kwargs.get('missing_strategies')
            if missing_strategies:
                # 记录处理前的缺失值数量
                missing_before = result.isnull().sum().sum()
                
                # 应用缺失值处理策略
                result = self.handle_missing_values(result, missing_strategies)
                
                # 计算处理的缺失值数量
                missing_after = result.isnull().sum().sum()
                self.changes['missing_values'] = missing_before - missing_after
                self.logger.info(f"已处理 {self.changes['missing_values']} 个缺失值")
            
            # 3. 处理异常值
            if kwargs.get('handle_outliers', False):
                method = kwargs.get('outlier_method', 'zscore')
                threshold = kwargs.get('outlier_threshold', 3.0)
                columns = kwargs.get('outlier_columns')
                
                # 记录处理前的行数
                rows_before = len(result)
                
                # 应用异常值处理
                result = self.handle_outliers(result, method=method, threshold=threshold, columns=columns)
                
                # 需要再次处理缺失值（异常值处理会将异常替换为NaN）
                if kwargs.get('outlier_replace_with_nan', True) and kwargs.get('outlier_fill_method'):
                    # 仅处理由异常值处理引入的NaN
                    fill_method = kwargs.get('outlier_fill_method')
                    
                    if fill_method == 'drop':
                        result = result.dropna()
                    elif fill_method == 'mean':
                        for col in result.select_dtypes(include=['number']).columns:
                            result[col] = result[col].fillna(result[col].mean())
                    elif fill_method == 'median':
                        for col in result.select_dtypes(include=['number']).columns:
                            result[col] = result[col].fillna(result[col].median())
                
                # 计算处理的异常值数量
                rows_after = len(result)
                self.changes['outliers'] = rows_before - rows_after
                self.logger.info(f"已处理 {self.changes['outliers']} 行异常值数据")
            
            # 4. 处理数据一致性错误
            self._clean_data_consistency(result, **kwargs)
            
            return result
            
        except Exception as e:
            self.logger.error(f"数据清洗失败: {str(e)}")
            raise CleaningError(f"数据清洗失败: {str(e)}")
    
    def _clean_data_consistency(self, data: pd.DataFrame, **kwargs) -> None:
        """
        处理数据一致性错误
        
        Args:
            data: 要处理的数据
            **kwargs: 参数
        """
        # 默认实现为空，子类应重写此方法添加特定数据类型的一致性处理
        pass
    
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        转换数据
        
        Args:
            data: 要转换的数据
            **kwargs: 转换参数
            
        Returns:
            转换后的数据
            
        Raises:
            TransformationError: 转换错误
        """
        # 基本实现，子类应重写此方法添加特定数据类型的转换逻辑
        return data
    
    def get_changes(self) -> Dict[str, int]:
        """
        获取清洗过程中的变更统计
        
        Returns:
            变更统计
        """
        return self.changes


class BaseCleaner(CleanerInterface):
    """
    基础数据清洗器，提供通用的清洗功能
    特定类型的清洗器应该继承这个类并扩展功能
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化基础清洗器
        
        Args:
            config: 清洗器配置
        """
        super().__init__(config)
        
        # 设置默认配置
        default_config = {
            'required_columns': [],
            'column_types': {},
            'missing_strategies': {
                '_default_': 'drop'
            },
            'outlier_method': 'zscore',
            'outlier_threshold': 3.0,
            'remove_duplicates': True,
            'duplicates_subset': None,
            'duplicates_keep': 'first'
        }
        
        # 合并默认配置和传入的配置
        if not self.config:
            self.config = {}
            
        for key, value in default_config.items():
            if key not in self.config:
                self.config[key] = value
    
    def clean(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        清洗数据，应用通用清洗逻辑
        
        Args:
            data: 要清洗的数据
            **kwargs: 清洗参数
            
        Returns:
            清洗后的数据
            
        Raises:
            CleaningError: 清洗错误
        """
        # 1. 应用基类的通用清洗逻辑
        result = super().clean(data, **kwargs)
        
        # 2. 执行数据类型转换
        type_mapping = self.config.get('column_types', {})
        if type_mapping:
            converted_mapping = {}
            for col, type_name in type_mapping.items():
                if type_name == 'numeric':
                    converted_mapping[col] = 'float'
                elif type_name == 'integer':
                    converted_mapping[col] = 'int'
                elif type_name == 'string':
                    converted_mapping[col] = 'str'
                elif type_name == 'boolean':
                    converted_mapping[col] = 'bool'
                elif type_name == 'datetime':
                    converted_mapping[col] = 'datetime'
            
            if converted_mapping:
                result = self.convert_types(result, converted_mapping)
        
        # 3. 应用数据过滤条件
        filter_conditions = kwargs.get('filter_conditions')
        if filter_conditions:
            result = self.filter_data(result, filter_conditions)
        
        return result
    
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        转换数据，应用通用转换逻辑
        
        Args:
            data: 要转换的数据
            **kwargs: 转换参数
            
        Returns:
            转换后的数据
            
        Raises:
            TransformationError: 转换错误
        """
        result = data.copy()
        
        # 1. 如果需要标准化数据
        if kwargs.get('normalize', False):
            normalize_method = kwargs.get('normalize_method', 'zscore')
            normalize_columns = kwargs.get('normalize_columns')
            result = self.normalize(result, method=normalize_method, columns=normalize_columns)
        
        # 2. 应用排序
        sort_by = kwargs.get('sort_by')
        if sort_by:
            ascending = kwargs.get('ascending', True)
            result = result.sort_values(by=sort_by, ascending=ascending)
        
        # 3. 应用列重命名
        rename_map = kwargs.get('rename_columns')
        if rename_map:
            result = result.rename(columns=rename_map)
        
        # 4. 选择/过滤列
        select_columns = kwargs.get('select_columns')
        if select_columns:
            # 只保留存在的列
            valid_columns = [col for col in select_columns if col in result.columns]
            result = result[valid_columns]
        
        # 5. 重置索引
        if kwargs.get('reset_index', False):
            result = result.reset_index(drop=True)
        
        return result

