"""
财务数据转换器
- 专用于财务数据的计算和转换
- 计算财务比率和指标
- 计算增长率和趋势
- 进行同比环比分析
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union, Optional, Tuple, Set
import logging
from datetime import datetime, timedelta

from .transformer_interface import BaseTransformer

class FinancialDataTransformer(BaseTransformer):
    """
    财务数据转换器，用于财务指标计算和数据转换
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化财务数据转换器
        
        Args:
            config: 转换器配置，除了基础转换器的配置外，还可以包含：
                - report_type: 财务报表类型，'balance'(资产负债表)、'income'(利润表)、'cashflow'(现金流量表)
                - calc_growth: 是否计算增长率
                - calc_ratios: 是否计算财务比率
                - calc_ttm: 是否计算TTM（Trailing Twelve Months）指标
                - calc_common_size: 是否计算同比数据（Common Size）
                - ratio_precision: 比率计算精度，默认4位小数
        """
        # 默认财务数据配置
        financial_transform_config = {
            'report_type': None,  # 可以是'balance', 'income', 'cashflow'
            'calc_growth': True,
            'calc_ratios': True,
            'calc_ttm': True,
            'calc_common_size': True,
            'ratio_precision': 4
        }
        
        # 合并传入的配置
        if config:
            for key, value in config.items():
                if isinstance(value, dict) and key in financial_transform_config and isinstance(financial_transform_config[key], dict):
                    financial_transform_config[key].update(value)
                else:
                    financial_transform_config[key] = value
        
        # 调用父类初始化
        super().__init__(financial_transform_config)
        
        # 添加财务数据特有的转换方法
        self._add_financial_transforms()
    
    def _add_financial_transforms(self):
        """
        添加财务数据特有的转换方法
        """
        # 计算财务比率
        if self.config.get('calc_ratios', True):
            self.add_transform(
                'calculate_financial_ratios',
                self._calculate_financial_ratios
            )
        
        # 计算增长率
        if self.config.get('calc_growth', True):
            self.add_transform(
                'calculate_growth_rates',
                self._calculate_growth_rates
            )
        
        # 计算同比数据（Common Size）
        if self.config.get('calc_common_size', True):
            self.add_transform(
                'calculate_common_size',
                self._calculate_common_size
            )
        
        # 计算TTM指标
        if self.config.get('calc_ttm', True):
            self.add_transform(
                'calculate_ttm',
                self._calculate_ttm
            )
        
        # 根据报表类型添加特定的转换
        report_type = self.config.get('report_type')
        if report_type == 'balance':
            self.add_transform(
                'calculate_asset_structure',
                self._calculate_asset_structure
            )
        elif report_type == 'income':
            self.add_transform(
                'calculate_profit_margins',
                self._calculate_profit_margins
            )
        elif report_type == 'cashflow':
            self.add_transform(
                'calculate_cash_flow_ratios',
                self._calculate_cash_flow_ratios
            )
    
    def _calculate_financial_ratios(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算财务比率
        
        Args:
            data: 财务数据
            
        Returns:
            pd.DataFrame: 添加了财务比率的数据
        """
        result = data.copy()
        report_type = self.config.get('report_type')
        precision = self.config.get('ratio_precision', 4)
        
        try:
            if report_type == 'balance':
                # 资产负债比率
                if all(col in result.columns for col in ['total_assets', 'total_liab']):
                    result['debt_to_assets'] = round(result['total_liab'] / result['total_assets'], precision)
                
                # 流动比率
                if all(col in result.columns for col in ['total_cur_assets', 'total_cur_liab']):
                    result['current_ratio'] = round(result['total_cur_assets'] / result['total_cur_liab'], precision)
                
                # 速动比率
                if all(col in result.columns for col in ['total_cur_assets', 'inventories', 'total_cur_liab']):
                    result['quick_ratio'] = round((result['total_cur_assets'] - result['inventories']) / result['total_cur_liab'], precision)
                
                # 权益乘数
                if all(col in result.columns for col in ['total_assets', 'total_hldr_eqy_exc_min_int']):
                    result['equity_multiplier'] = round(result['total_assets'] / result['total_hldr_eqy_exc_min_int'], precision)
                
            elif report_type == 'income':
                # 毛利率
                if all(col in result.columns for col in ['revenue', 'oper_cost']):
                    result['gross_margin'] = round((result['revenue'] - result['oper_cost']) / result['revenue'], precision)
                
                # 净利率
                if all(col in result.columns for col in ['revenue', 'n_income']):
                    result['net_margin'] = round(result['n_income'] / result['revenue'], precision)
                
                # 营业利润率
                if all(col in result.columns for col in ['revenue', 'operate_profit']):
                    result['operating_margin'] = round(result['operate_profit'] / result['revenue'], precision)
                
                # 费用率
                if all(col in result.columns for col in ['revenue', 'sell_exp', 'admin_exp', 'fin_exp']):
                    result['expense_ratio'] = round((result['sell_exp'] + result['admin_exp'] + result['fin_exp']) / result['revenue'], precision)
                
            elif report_type == 'cashflow':
                # 经营现金流与净利润比
                if all(col in result.columns for col in ['n_cashflow_act', 'n_income']):
                    result['cf_to_profit'] = round(result['n_cashflow_act'] / result['n_income'], precision)
                
                # 经营现金流与营业收入比
                if all(col in result.columns for col in ['n_cashflow_act', 'revenue']):
                    result['cf_to_revenue'] = round(result['n_cashflow_act'] / result['revenue'], precision)
                
                # 自由现金流
                if all(col in result.columns for col in ['n_cashflow_act', 'c_pay_acq_const_fiolta']):
                    result['free_cash_flow'] = result['n_cashflow_act'] - result['c_pay_acq_const_fiolta']
                
            # 跨表指标计算
            # 这些指标需要多个报表的数据，可能需要预先合并报表数据
            
            # 处理无穷值和NaN
            for col in result.columns:
                if pd.api.types.is_numeric_dtype(result[col]):
                    result[col] = result[col].replace([np.inf, -np.inf], np.nan)
                    
        except Exception as e:
            self.logger.error(f"计算财务比率时出错: {str(e)}")
            
        return result
    
    def _calculate_growth_rates(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算增长率（同比、环比）
        
        Args:
            data: 财务数据
            
        Returns:
            pd.DataFrame: 添加了增长率的数据
        """
        result = data.copy()
        precision = self.config.get('ratio_precision', 4)
        
        try:
            # 确保数据按公司代码和报告期排序
            required_columns = ['ts_code', 'end_date']
            if not all(col in result.columns for col in required_columns):
                self.logger.warning("缺少计算增长率所需的列")
                return result
            
            # 确保end_date是日期类型
            if not pd.api.types.is_datetime64_dtype(result['end_date']):
                result['end_date'] = pd.to_datetime(result['end_date'])
            
            # 对每个公司分别计算增长率
            growth_dfs = []
            
            for ts_code, group in result.groupby('ts_code'):
                # 按报告期排序
                group = group.sort_values('end_date').reset_index(drop=True)
                
                # 获取需要计算增长率的数值列
                numeric_cols = []
                for col in group.columns:
                    if col not in ['ts_code', 'end_date', 'ann_date', 'report_type'] and pd.api.types.is_numeric_dtype(group[col]):
                        numeric_cols.append(col)
                
                # 没有数值列则跳过
                if not numeric_cols:
                    growth_dfs.append(group)
                    continue
                
                # 计算同比增长率（YoY）和环比增长率（QoQ）
                group_with_yoy = group.copy()
                for col in numeric_cols:
                    # 确保不为0的除数，同时避免计算一些不需要增长率的累计数据
                    if col in ['report_type', 'comp_type', 'end_type', 'seq', 'update_flag']:
                        continue
                    
                    # 计算环比增长率 (QoQ)
                    qoq_col = f"{col}_qoq"
                    group_with_yoy[qoq_col] = group_with_yoy[col].pct_change() * 100
                    
                    # 计算同比增长率 (YoY)
                    # 使用基于位置的方法来计算同比增长率，而不是基于分组
                    # 第5期对应第1期：(第5期值 - 第1期值) / 第1期值
                    yoy_col = f"{col}_yoy"
                    for i in range(len(group_with_yoy)):
                        if i >= 4:  # 有足够的历史数据计算同比
                            current_value = group_with_yoy.loc[i, col]
                            previous_year_value = group_with_yoy.loc[i-4, col]
                            
                            if previous_year_value != 0:  # 避免除以零
                                yoy_growth = (current_value - previous_year_value) / previous_year_value * 100
                                group_with_yoy.loc[i, yoy_col] = round(yoy_growth, precision)
                    
                    # 保留指定精度
                    if qoq_col in group_with_yoy.columns:
                        group_with_yoy[qoq_col] = round(group_with_yoy[qoq_col], precision)
                
                growth_dfs.append(group_with_yoy)
            
            if growth_dfs:
                result = pd.concat(growth_dfs, ignore_index=True)
                
        except Exception as e:
            self.logger.error(f"计算增长率时出错: {str(e)}")
            
        return result
    
    def _calculate_common_size(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算同比数据（Common Size Analysis）
        - 资产负债表：占总资产百分比
        - 利润表：占营业收入百分比
        - 现金流量表：占经营活动现金流百分比
        
        Args:
            data: 财务数据
            
        Returns:
            pd.DataFrame: 添加了同比数据的DataFrame
        """
        result = data.copy()
        report_type = self.config.get('report_type')
        precision = self.config.get('ratio_precision', 4)
        
        try:
            if report_type == 'balance':
                # 资产负债表同比分析，以总资产为基准
                if 'total_assets' in result.columns:
                    base_col = 'total_assets'
                    # 获取资产类科目
                    asset_cols = [col for col in result.columns if pd.api.types.is_numeric_dtype(result[col]) and 
                                  col not in ['ts_code', 'end_date', 'ann_date', 'report_type']]
                    
                    for col in asset_cols:
                        if col != base_col:
                            result[f"{col}_pct"] = round(result[col] / result[base_col] * 100, precision)
                
            elif report_type == 'income':
                # 利润表同比分析，以营业收入为基准
                if 'revenue' in result.columns:
                    base_col = 'revenue'
                    # 获取利润表科目
                    income_cols = [col for col in result.columns if pd.api.types.is_numeric_dtype(result[col]) and 
                                   col not in ['ts_code', 'end_date', 'ann_date', 'report_type']]
                    
                    for col in income_cols:
                        if col != base_col:
                            result[f"{col}_pct"] = round(result[col] / result[base_col] * 100, precision)
                
            elif report_type == 'cashflow':
                # 现金流量表同比分析，以经营活动现金流为基准
                if 'n_cashflow_act' in result.columns:
                    base_col = 'n_cashflow_act'
                    # 获取现金流科目
                    cf_cols = [col for col in result.columns if pd.api.types.is_numeric_dtype(result[col]) and 
                              col not in ['ts_code', 'end_date', 'ann_date', 'report_type']]
                    
                    for col in cf_cols:
                        if col != base_col:
                            # 避免分母为0或接近0
                            valid_rows = result[base_col].abs() > 1e-10
                            if valid_rows.any():
                                result.loc[valid_rows, f"{col}_pct"] = round(
                                    result.loc[valid_rows, col] / result.loc[valid_rows, base_col] * 100, 
                                    precision
                                )
            
            # 处理无穷值和NaN
            for col in result.columns:
                if col.endswith('_pct') and pd.api.types.is_numeric_dtype(result[col]):
                    result[col] = result[col].replace([np.inf, -np.inf], np.nan)
                    
        except Exception as e:
            self.logger.error(f"计算同比数据时出错: {str(e)}")
            
        return result
    
    def _calculate_ttm(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算TTM（Trailing Twelve Months）指标
        
        Args:
            data: 财务数据
            
        Returns:
            pd.DataFrame: 添加了TTM指标的数据
        """
        result = data.copy()
        report_type = self.config.get('report_type')
        
        try:
            # TTM主要适用于利润表和现金流量表
            if report_type in ['income', 'cashflow']:
                required_columns = ['ts_code', 'end_date']
                if not all(col in result.columns for col in required_columns):
                    self.logger.warning("缺少计算TTM所需的列")
                    return result
                
                # 确保end_date是日期类型
                if not pd.api.types.is_datetime64_dtype(result['end_date']):
                    result['end_date'] = pd.to_datetime(result['end_date'])
                
                # 按公司分组计算TTM
                ttm_dfs = []
                
                for ts_code, group in result.groupby('ts_code'):
                    # 按报告期排序
                    group = group.sort_values('end_date')
                    
                    # 获取需要计算TTM的数值列
                    numeric_cols = []
                    for col in group.columns:
                        if col not in ['ts_code', 'end_date', 'ann_date', 'report_type'] and pd.api.types.is_numeric_dtype(group[col]):
                            # 排除不需要TTM的累计字段
                            if col not in ['report_type', 'comp_type', 'end_type', 'seq', 'update_flag']:
                                numeric_cols.append(col)
                    
                    # 计算TTM
                    group_with_ttm = group.copy()
                    for col in numeric_cols:
                        ttm_col = f"{col}_ttm"
                        
                        # 使用rolling窗口计算TTM
                        # 对于季度数据，需要取过去4个季度的数据
                        group_with_ttm[ttm_col] = group_with_ttm[col].rolling(window=4, min_periods=4).sum()
                        
                    ttm_dfs.append(group_with_ttm)
                
                if ttm_dfs:
                    result = pd.concat(ttm_dfs, ignore_index=True)
                
        except Exception as e:
            self.logger.error(f"计算TTM指标时出错: {str(e)}")
            
        return result
    
    def _calculate_asset_structure(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算资产结构分析指标
        
        Args:
            data: 资产负债表数据
            
        Returns:
            pd.DataFrame: 添加了资产结构指标的数据
        """
        result = data.copy()
        precision = self.config.get('ratio_precision', 4)
        
        try:
            # 计算资产结构比率
            
            # 流动资产占比
            if all(col in result.columns for col in ['total_cur_assets', 'total_assets']):
                result['current_assets_ratio'] = round(result['total_cur_assets'] / result['total_assets'] * 100, precision)
            
            # 非流动资产占比
            if all(col in result.columns for col in ['total_nca', 'total_assets']):
                result['non_current_assets_ratio'] = round(result['total_nca'] / result['total_assets'] * 100, precision)
            
            # 货币资金占比
            if all(col in result.columns for col in ['money_cap', 'total_assets']):
                result['cash_ratio'] = round(result['money_cap'] / result['total_assets'] * 100, precision)
            
            # 应收账款占比
            if all(col in result.columns for col in ['accounts_receiv', 'total_assets']):
                result['accounts_receivable_ratio'] = round(result['accounts_receiv'] / result['total_assets'] * 100, precision)
            
            # 存货占比
            if all(col in result.columns for col in ['inventories', 'total_assets']):
                result['inventory_ratio'] = round(result['inventories'] / result['total_assets'] * 100, precision)
            
            # 固定资产占比
            if all(col in result.columns for col in ['fix_assets', 'total_assets']):
                result['fixed_assets_ratio'] = round(result['fix_assets'] / result['total_assets'] * 100, precision)
            
            # 流动负债占比
            if all(col in result.columns for col in ['total_cur_liab', 'total_liab']):
                result['current_liab_ratio'] = round(result['total_cur_liab'] / result['total_liab'] * 100, precision)
            
            # 非流动负债占比
            if all(col in result.columns for col in ['total_ncl', 'total_liab']):
                result['non_current_liab_ratio'] = round(result['total_ncl'] / result['total_liab'] * 100, precision)
            
            # 处理无穷值和NaN
            for col in result.columns:
                if col.endswith('_ratio') and pd.api.types.is_numeric_dtype(result[col]):
                    result[col] = result[col].replace([np.inf, -np.inf], np.nan)
                    
        except Exception as e:
            self.logger.error(f"计算资产结构指标时出错: {str(e)}")
            
        return result
    
    def _calculate_profit_margins(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算利润率指标
        
        Args:
            data: 利润表数据
            
        Returns:
            pd.DataFrame: 添加了利润率指标的数据
        """
        result = data.copy()
        precision = self.config.get('ratio_precision', 4)
        
        try:
            # 计算利润率指标
            
            # 毛利率 = (营业收入 - 营业成本) / 营业收入
            if all(col in result.columns for col in ['revenue', 'oper_cost']):
                result['gross_profit_margin'] = round((result['revenue'] - result['oper_cost']) / result['revenue'] * 100, precision)
            
            # EBIT（息税前利润）= 利润总额 + 财务费用
            if all(col in result.columns for col in ['total_profit', 'fin_exp']):
                result['ebit'] = result['total_profit'] + result['fin_exp']
            
            # EBITDA（息税折旧摊销前利润）= EBIT + 折旧 + 摊销
            if all(col in result.columns for col in ['ebit', 'depreciation_exp', 'amortization_exp']):
                result['ebitda'] = result['ebit'] + result['depreciation_exp'] + result['amortization_exp']
            elif 'ebit' in result.columns:
                # 如果没有单独的折旧摊销数据，可以使用近似值
                result['ebitda'] = result['ebit'] * 1.15  # 近似值，实际应该使用真实数据
            
            # EBIT利润率
            if all(col in result.columns for col in ['ebit', 'revenue']):
                result['ebit_margin'] = round(result['ebit'] / result['revenue'] * 100, precision)
            
            # EBITDA利润率
            if all(col in result.columns for col in ['ebitda', 'revenue']):
                result['ebitda_margin'] = round(result['ebitda'] / result['revenue'] * 100, precision)
            
            # 销售净利率 = 净利润 / 营业收入
            if all(col in result.columns for col in ['n_income', 'revenue']):
                result['net_profit_margin'] = round(result['n_income'] / result['revenue'] * 100, precision)
            
            # 销售费用率
            if all(col in result.columns for col in ['sell_exp', 'revenue']):
                result['selling_expense_ratio'] = round(result['sell_exp'] / result['revenue'] * 100, precision)
            
            # 管理费用率
            if all(col in result.columns for col in ['admin_exp', 'revenue']):
                result['admin_expense_ratio'] = round(result['admin_exp'] / result['revenue'] * 100, precision)
            
            # 财务费用率
            if all(col in result.columns for col in ['fin_exp', 'revenue']):
                result['financial_expense_ratio'] = round(result['fin_exp'] / result['revenue'] * 100, precision)
            
            # 所得税率
            if all(col in result.columns for col in ['income_tax', 'total_profit']):
                # 避免分母为0
                valid_rows = result['total_profit'].abs() > 1e-10
                if valid_rows.any():
                    result.loc[valid_rows, 'effective_tax_rate'] = round(
                        result.loc[valid_rows, 'income_tax'] / result.loc[valid_rows, 'total_profit'] * 100, 
                        precision
                    )
            
            # 处理无穷值和NaN
            for col in result.columns:
                if '_margin' in col or '_ratio' in col or '_rate' in col:
                    if pd.api.types.is_numeric_dtype(result[col]):
                        result[col] = result[col].replace([np.inf, -np.inf], np.nan)
                        
        except Exception as e:
            self.logger.error(f"计算利润率指标时出错: {str(e)}")
            
        return result
    
    def _calculate_cash_flow_ratios(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算现金流相关比率
        
        Args:
            data: 现金流量表数据
            
        Returns:
            pd.DataFrame: 添加了现金流比率的数据
        """
        result = data.copy()
        precision = self.config.get('ratio_precision', 4)
        
        try:
            # 计算现金流比率
            
            # 经营现金流量比率 = 经营活动现金流量净额 / 流动负债
            if all(col in result.columns for col in ['n_cashflow_act', 'total_cur_liab']):
                # 避免分母为0
                valid_rows = result['total_cur_liab'].abs() > 1e-10
                if valid_rows.any():
                    result.loc[valid_rows, 'operating_cash_flow_ratio'] = round(
                        result.loc[valid_rows, 'n_cashflow_act'] / result.loc[valid_rows, 'total_cur_liab'] * 100, 
                        precision
                    )
            
            # 现金流量覆盖率 = 经营活动现金流量净额 / (资本支出 + 存货增加额 + 现金股利)
            # 简化版：经营活动现金流量净额 / 投资活动现金流出
            if all(col in result.columns for col in ['n_cashflow_act', 'c_pay_acq_const_fiolta']):
                # 避免分母为0
                valid_rows = result['c_pay_acq_const_fiolta'].abs() > 1e-10
                if valid_rows.any():
                    result.loc[valid_rows, 'cash_flow_coverage_ratio'] = round(
                        result.loc[valid_rows, 'n_cashflow_act'] / result.loc[valid_rows, 'c_pay_acq_const_fiolta'] * 100, 
                        precision
                    )
            
            # 现金流量与销售收入比率 = 经营活动现金流量净额 / 销售收入
            if all(col in result.columns for col in ['n_cashflow_act', 'revenue']):
                # 避免分母为0
                valid_rows = result['revenue'].abs() > 1e-10
                if valid_rows.any():
                    result.loc[valid_rows, 'cash_flow_to_revenue_ratio'] = round(
                        result.loc[valid_rows, 'n_cashflow_act'] / result.loc[valid_rows, 'revenue'] * 100, 
                        precision
                    )
            
            # 现金流量与净利润比率 = 经营活动现金流量净额 / 净利润
            if all(col in result.columns for col in ['n_cashflow_act', 'n_income']):
                # 避免分母为0
                valid_rows = result['n_income'].abs() > 1e-10
                if valid_rows.any():
                    result.loc[valid_rows, 'cash_flow_to_profit_ratio'] = round(
                        result.loc[valid_rows, 'n_cashflow_act'] / result.loc[valid_rows, 'n_income'] * 100, 
                        precision
                    )
            
            # 自由现金流 = 经营活动现金流量净额 - 资本支出
            if all(col in result.columns for col in ['n_cashflow_act', 'c_pay_acq_const_fiolta']):
                result['free_cash_flow'] = result['n_cashflow_act'] - result['c_pay_acq_const_fiolta']
            
            # 处理无穷值和NaN
            for col in result.columns:
                if '_ratio' in col and pd.api.types.is_numeric_dtype(result[col]):
                    result[col] = result[col].replace([np.inf, -np.inf], np.nan)
                    
        except Exception as e:
            self.logger.error(f"计算现金流比率时出错: {str(e)}")
            
        return result

    def validate(self, data: pd.DataFrame, rules: Dict[str, Any] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        验证财务数据
        
        Args:
            data: 要验证的财务数据
            rules: 验证规则
            
        Returns:
            Tuple[bool, Dict[str, Any]]: (是否有效, 验证结果详情)
            
        Raises:
            ValidationError: 验证错误
        """
        # 默认验证规则
        default_rules = {
            'required_columns': [],  # 必需的列
            'column_types': {},      # 列的数据类型
            'value_ranges': {},      # 数值范围检查
            'non_negative_columns': [] # 非负值列
        }
        
        # 根据报表类型设置默认规则
        report_type = self.config.get('report_type')
        if report_type == 'balance':
            default_rules['required_columns'] = ['ts_code', 'end_date', 'total_assets', 'total_liab']
            default_rules['non_negative_columns'] = ['total_assets', 'total_cur_assets', 'total_nca', 'total_liab']
        elif report_type == 'income':
            default_rules['required_columns'] = ['ts_code', 'end_date', 'revenue', 'oper_cost']
            default_rules['non_negative_columns'] = ['revenue', 'oper_cost']
        elif report_type == 'cashflow':
            default_rules['required_columns'] = ['ts_code', 'end_date', 'n_cashflow_act']
        
        # 使用传入的规则覆盖默认规则
        if rules:
            for key, value in rules.items():
                if isinstance(value, dict) and key in default_rules and isinstance(default_rules[key], dict):
                    default_rules[key].update(value)
                else:
                    default_rules[key] = value
        
        validation_results = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        try:
            # 检查必需列
            required_columns = default_rules.get('required_columns', [])
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                validation_results['valid'] = False
                validation_results['errors'].append({
                    'type': 'missing_columns',
                    'message': f"缺少必需列: {missing_columns}"
                })
            
            # 检查列数据类型
            column_types = default_rules.get('column_types', {})
            for col, expected_type in column_types.items():
                if col in data.columns:
                    # 检查数据类型
                    actual_type = data[col].dtype
                    type_valid = False
                    
                    if expected_type == 'numeric' and pd.api.types.is_numeric_dtype(actual_type):
                        type_valid = True
                    elif expected_type == 'datetime' and pd.api.types.is_datetime64_dtype(actual_type):
                        type_valid = True
                    elif expected_type == 'string' and pd.api.types.is_string_dtype(actual_type):
                        type_valid = True
                    
                    if not type_valid:
                        validation_results['valid'] = False
                        validation_results['errors'].append({
                            'type': 'invalid_column_type',
                            'message': f"列 {col} 的数据类型应为 {expected_type}，实际为 {actual_type}"
                        })
            
            # 检查数值范围
            value_ranges = default_rules.get('value_ranges', {})
            for col, range_info in value_ranges.items():
                if col in data.columns and pd.api.types.is_numeric_dtype(data[col]):
                    min_value = range_info.get('min')
                    max_value = range_info.get('max')
                    
                    if min_value is not None and (data[col] < min_value).any():
                        invalid_count = (data[col] < min_value).sum()
                        validation_results['warnings'].append({
                            'type': 'below_min_value',
                            'message': f"列 {col} 有 {invalid_count} 个值小于最小值 {min_value}"
                        })
                    
                    if max_value is not None and (data[col] > max_value).any():
                        invalid_count = (data[col] > max_value).sum()
                        validation_results['warnings'].append({
                            'type': 'above_max_value',
                            'message': f"列 {col} 有 {invalid_count} 个值大于最大值 {max_value}"
                        })
            
            # 检查非负值列
            non_negative_columns = default_rules.get('non_negative_columns', [])
            for col in non_negative_columns:
                if col in data.columns and pd.api.types.is_numeric_dtype(data[col]):
                    negative_count = (data[col] < 0).sum()
                    if negative_count > 0:
                        validation_results['warnings'].append({
                            'type': 'negative_values',
                            'message': f"列 {col} 有 {negative_count} 个负值"
                        })
            
            # 设置验证状态
            validation_results['valid'] = len(validation_results['errors']) == 0
            
            return validation_results['valid'], validation_results
            
        except Exception as e:
            self.logger.error(f"财务数据验证失败: {str(e)}")
            validation_results['valid'] = False
            validation_results['errors'].append({
                'type': 'validation_exception',
                'message': f"验证过程出错: {str(e)}"
            })
            return False, validation_results
    
    def clean(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        清洗财务数据
        
        Args:
            data: 要清洗的财务数据
            **kwargs: 清洗参数
                - handle_missing: 是否处理缺失值
                - handle_outliers: 是否处理异常值
                - outlier_method: 异常值处理方法
                - outlier_threshold: 异常值阈值
                - missing_strategies: 缺失值处理策略
            
        Returns:
            pd.DataFrame: 清洗后的数据
            
        Raises:
            CleaningError: 清洗错误
        """
        if data is None or data.empty:
            return data
        
        result = data.copy()
        
        try:
            # 获取清洗参数
            handle_missing = kwargs.get('handle_missing', True)
            handle_outliers = kwargs.get('handle_outliers', True)
            outlier_method = kwargs.get('outlier_method', 'zscore')
            outlier_threshold = kwargs.get('outlier_threshold', 3.0)
            
            # 处理缺失值
            if handle_missing:
                # 设置缺失值处理策略
                missing_strategies = kwargs.get('missing_strategies', {})
                
                # 根据报表类型设置默认缺失值处理策略
                report_type = self.config.get('report_type')
                default_strategies = {'_default_': 'drop'}  # 默认策略：删除缺失值行
                
                if report_type == 'balance':
                    # 资产负债表关键字段不允许缺失
                    critical_cols = ['total_assets', 'total_liab']
                    for col in critical_cols:
                        if col in result.columns:
                            default_strategies[col] = 'drop'
                
                elif report_type == 'income':
                    # 利润表关键字段不允许缺失
                    critical_cols = ['revenue', 'n_income']
                    for col in critical_cols:
                        if col in result.columns:
                            default_strategies[col] = 'drop'
                
                elif report_type == 'cashflow':
                    # 现金流量表关键字段不允许缺失
                    critical_cols = ['n_cashflow_act']
                    for col in critical_cols:
                        if col in result.columns:
                            default_strategies[col] = 'drop'
                
                # 合并默认策略和传入的策略
                if missing_strategies:
                    default_strategies.update(missing_strategies)
                
                # 处理缺失值
                result = self.handle_missing_values(result, default_strategies)
            
            # 处理异常值
            if handle_outliers:
                # 根据报表类型确定需要处理异常值的列
                report_type = self.config.get('report_type')
                outlier_columns = []
                
                if report_type == 'balance':
                    # 资产负债表异常值列
                    possible_cols = ['total_assets', 'total_cur_assets', 'total_liab', 'total_hldr_eqy_exc_min_int']
                    outlier_columns = [col for col in possible_cols if col in result.columns]
                
                elif report_type == 'income':
                    # 利润表异常值列
                    possible_cols = ['revenue', 'oper_cost', 'operate_profit', 'n_income']
                    outlier_columns = [col for col in possible_cols if col in result.columns]
                
                elif report_type == 'cashflow':
                    # 现金流量表异常值列
                    possible_cols = ['n_cashflow_act', 'n_cashflow_inv_act', 'n_cash_flows_fnc_act']
                    outlier_columns = [col for col in possible_cols if col in result.columns]
                
                # 处理异常值
                if outlier_columns:
                    result = self.handle_outliers(
                        result, 
                        method=outlier_method,
                        threshold=outlier_threshold,
                        columns=outlier_columns
                    )
            
            return result
            
        except Exception as e:
            self.logger.error(f"财务数据清洗失败: {str(e)}")
            raise CleaningError(f"财务数据清洗失败: {str(e)}") 