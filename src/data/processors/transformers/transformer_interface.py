"""
数据转换器接口
- 定义数据转换的基本方法和规则
- 提供转换异常处理
- 支持转换规则组合
"""

from abc import ABC, abstractmethod
import pandas as pd
from typing import Dict, List, Any, Union, Optional, Callable, Tuple, Set
import logging

from src.data.processors.processor_interface import ProcessorInterface, TransformationError

class TransformerInterface(ProcessorInterface, ABC):
    """
    数据转换器接口，定义数据转换的基本方法
    """
    
    @abstractmethod
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        转换数据
        
        Args:
            data: 要转换的数据
            **kwargs: 额外参数
            
        Returns:
            pd.DataFrame: 转换后的数据
            
        Raises:
            TransformationError: 转换错误
        """
        pass
    
    @abstractmethod
    def add_transform(self, transform_name: str, transform_func: Callable, **kwargs) -> None:
        """
        添加转换方法
        
        Args:
            transform_name: 转换方法名称
            transform_func: 转换函数，接受DataFrame和额外参数，返回DataFrame
            **kwargs: 转换函数的额外参数
            
        Returns:
            None
        """
        pass
    
    @abstractmethod
    def remove_transform(self, transform_name: str) -> bool:
        """
        移除转换方法
        
        Args:
            transform_name: 转换方法名称
            
        Returns:
            bool: 是否成功移除
        """
        pass

class BaseTransformer(TransformerInterface):
    """
    基础转换器，提供通用的数据转换功能
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化基础转换器
        
        Args:
            config: 转换器配置
                - error_action: 错误处理方式，'raise'|'log'|'return'
                - transforms: 预定义转换方法列表
        """
        self.transforms = {}  # 转换方法字典
        self.results = {}  # 转换结果
        self.errors = []  # 错误信息列表
        
        # 默认配置
        self.config = {
            'error_action': 'raise',  # 'raise', 'log', 'return'
            'transforms': {}
        }
        
        # 更新配置
        if config:
            for key, value in config.items():
                if isinstance(value, dict) and key in self.config and isinstance(self.config[key], dict):
                    self.config[key].update(value)
                else:
                    self.config[key] = value
        
        # 设置日志
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化预定义转换方法
        self._init_predefined_transforms()
    
    def _init_predefined_transforms(self) -> None:
        """
        初始化预定义的转换方法
        """
        # 子类中实现具体的预定义转换方法
        pass
    
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        转换数据
        
        Args:
            data: 要转换的数据
            **kwargs: 额外参数
                - transforms: 指定要运行的转换方法，如果为空则运行所有转换方法
                - error_action: 覆盖配置中的error_action
                
        Returns:
            pd.DataFrame: 转换后的数据
            
        Raises:
            TransformationError: 转换错误（如果error_action='raise'）
        """
        if data is None or data.empty:
            if self.config['error_action'] == 'raise':
                raise TransformationError("数据为空")
            elif self.config['error_action'] == 'log':
                self.logger.error("数据为空")
            return data
        
        # 重置结果和错误
        self.results = {}
        self.errors = []
        
        # 创建数据副本，避免修改原始数据
        result_data = data.copy()
        
        # 获取要运行的转换方法
        transforms_to_run = kwargs.get('transforms', list(self.transforms.keys()))
        
        # 运行每个转换方法
        for transform_name in transforms_to_run:
            if transform_name in self.transforms:
                transform = self.transforms[transform_name]
                try:
                    result_data = transform['func'](result_data, **transform['params'])
                    self.results[transform_name] = {
                        'success': True,
                        'message': f"转换 {transform_name} 成功"
                    }
                except Exception as e:
                    error_msg = f"转换 {transform_name} 执行错误: {str(e)}"
                    self.errors.append({
                        'transform': transform_name,
                        'message': error_msg
                    })
                    self.results[transform_name] = {
                        'success': False,
                        'message': error_msg
                    }
                    
                    # 处理错误
                    error_action = kwargs.get('error_action', self.config['error_action'])
                    if error_action == 'raise':
                        raise TransformationError(error_msg)
                    elif error_action == 'log':
                        self.logger.error(error_msg)
        
        return result_data
    
    def add_transform(self, transform_name: str, transform_func: Callable, **kwargs) -> None:
        """
        添加转换方法
        
        Args:
            transform_name: 转换方法名称
            transform_func: 转换函数，接受DataFrame和额外参数，返回DataFrame
            **kwargs: 转换函数的额外参数
            
        Returns:
            None
        """
        self.transforms[transform_name] = {
            'func': transform_func,
            'params': kwargs
        }
    
    def remove_transform(self, transform_name: str) -> bool:
        """
        移除转换方法
        
        Args:
            transform_name: 转换方法名称
            
        Returns:
            bool: 是否成功移除
        """
        if transform_name in self.transforms:
            del self.transforms[transform_name]
            return True
        return False
    
    def get_transforms(self) -> Dict[str, Dict]:
        """
        获取所有转换方法
        
        Returns:
            Dict[str, Dict]: 转换方法字典
        """
        return self.transforms
    
    def get_results(self) -> Dict[str, Any]:
        """
        获取最近一次转换结果
        
        Returns:
            Dict[str, Any]: 转换结果
        """
        return {
            'success': len(self.errors) == 0,
            'results': self.results,
            'errors': self.errors
        }
    
    def process(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        处理数据（ProcessorInterface 接口实现）
        
        Args:
            data: 要处理的数据
            **kwargs: 额外参数
            
        Returns:
            pd.DataFrame: 处理后的数据
        """
        return self.transform(data, **kwargs) 