"""
数据处理器接口
定义数据处理组件的标准接口
"""

from abc import ABC, abstractmethod
from typing import Any, Optional, Dict, List, Union, Tuple
import pandas as pd
import numpy as np
import datetime
import logging

# 配置日志记录器
logger = logging.getLogger(__name__)

class ProcessorException(Exception):
    """处理器异常类"""
    pass

class ValidationError(ProcessorException):
    """数据验证错误"""
    pass

class CleaningError(ProcessorException):
    """数据清洗错误"""
    pass

class TransformationError(ProcessorException):
    """数据转换错误"""
    pass

class ProcessorInterface(ABC):
    """
    数据处理器接口
    
    所有数据处理组件的基类
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化处理器
        
        Args:
            config: 处理器配置
        """
        self.config = config or {}
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def process(self, *args, **kwargs) -> Any:
        """
        处理数据的核心方法
        
        参数:
            *args: 位置参数
            **kwargs: 关键字参数
            
        返回:
            处理后的数据
        """
        pass
    
    def __call__(self, *args, **kwargs) -> Any:
        """
        使处理器可调用
        
        参数:
            *args: 位置参数
            **kwargs: 关键字参数
            
        返回:
            处理后的数据
        """
        return self.process(*args, **kwargs)
    
    def validate(self, data: pd.DataFrame, rules: Optional[Dict[str, Any]] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        验证数据是否符合处理器要求
        
        参数:
            data: 待验证的数据
            rules: 验证规则
            
        返回:
            Tuple[bool, Dict[str, Any]]: (是否通过验证, 验证结果详情)
        """
        # 基本验证 - 检查是否为DataFrame且非空
        if not isinstance(data, pd.DataFrame):
            return False, {"error": "输入必须是pandas DataFrame"}
        
        if data.empty:
            return False, {"error": "输入DataFrame不能为空"}
        
        # 默认实现仅执行基本检查
        return True, {"message": "基本验证通过"}
    
    def filter_data(self, data: pd.DataFrame, filters: Dict[str, Any]) -> pd.DataFrame:
        """
        过滤数据
        
        参数:
            data: 待过滤的数据
            filters: 过滤条件
            
        返回:
            pd.DataFrame: 过滤后的数据
        """
        try:
            filtered_data = data.copy()
            
            for column, condition in filters.items():
                if column not in filtered_data.columns:
                    continue
                    
                if isinstance(condition, (list, tuple)):
                    filtered_data = filtered_data[filtered_data[column].isin(condition)]
                elif isinstance(condition, dict):
                    if 'min' in condition:
                        filtered_data = filtered_data[filtered_data[column] >= condition['min']]
                    if 'max' in condition:
                        filtered_data = filtered_data[filtered_data[column] <= condition['max']]
                    if 'eq' in condition:
                        filtered_data = filtered_data[filtered_data[column] == condition['eq']]
                    if 'neq' in condition:
                        filtered_data = filtered_data[filtered_data[column] != condition['neq']]
                else:
                    filtered_data = filtered_data[filtered_data[column] == condition]
                    
            return filtered_data
            
        except Exception as e:
            raise ProcessorException(f"数据过滤失败: {str(e)}")
    
    @abstractmethod
    def clean(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        清洗数据
        
        Args:
            data: 要清洗的数据
            **kwargs: 清洗参数
            
        Returns:
            清洗后的数据
            
        Raises:
            CleaningError: 清洗错误
        """
        pass
    
    @abstractmethod
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        转换数据
        
        Args:
            data: 要转换的数据
            **kwargs: 转换参数
            
        Returns:
            转换后的数据
            
        Raises:
            TransformationError: 转换错误
        """
        pass
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置处理器配置
        
        Args:
            config: 新的配置字典
        """
        self.config = config
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取处理器配置
        
        Returns:
            当前配置字典
        """
        return self.config
    
    def log_stats(self, data: pd.DataFrame, stage: str = None) -> None:
        """
        记录数据统计信息
        
        Args:
            data: 数据
            stage: 处理阶段名称
        """
        if stage:
            self.logger.info(f"--- {stage} 阶段统计 ---")
        
        stats = {
            '行数': len(data),
            '列数': len(data.columns),
            '列': list(data.columns),
            '数据类型': {col: str(dtype) for col, dtype in data.dtypes.items()},
            '缺失值': data.isnull().sum().to_dict()
        }
        
        # 记录数值列统计
        numeric_columns = data.select_dtypes(include=['number']).columns
        if len(numeric_columns) > 0:
            stats['数值统计'] = {
                col: {
                    'min': data[col].min(),
                    'max': data[col].max(),
                    'mean': data[col].mean(),
                    'std': data[col].std()
                } for col in numeric_columns
            }
        
        self.logger.info(f"数据统计: {stats}")
    
    def handle_missing_values(self, data: pd.DataFrame, strategies: Dict[str, Union[str, Dict[str, Any]]] = None) -> pd.DataFrame:
        """
        处理缺失值
        
        Args:
            data: 包含缺失值的数据
            strategies: 处理策略，可以是每列对应的策略或全局策略
                支持的策略: 'drop'(删除), 'fill_mean'(均值填充), 'fill_median'(中位数填充), 
                         'fill_mode'(众数填充), 'fill_value'(指定值填充)
                
        Returns:
            处理后的数据
            
        Raises:
            CleaningError: 清洗错误
        """
        if data.empty:
            return data
            
        result = data.copy()
        
        try:
            if strategies is None:
                strategies = {}
                
            # 获取全局默认策略
            default_strategy = strategies.get('_default_', 'drop')
            
            # 处理每列
            for column in result.columns:
                # 如果该列没有缺失值，跳过
                if not result[column].isnull().any():
                    continue
                    
                # 获取该列的处理策略
                if column in strategies:
                    strategy = strategies[column]
                else:
                    strategy = default_strategy
                
                # 根据策略处理
                if strategy == 'drop':
                    # 删除包含缺失值的行
                    result = result.dropna(subset=[column])
                elif strategy == 'fill_mean':
                    # 均值填充，仅适用于数值列
                    if pd.api.types.is_numeric_dtype(result[column]):
                        result[column] = result[column].fillna(result[column].mean())
                elif strategy == 'fill_median':
                    # 中位数填充，仅适用于数值列
                    if pd.api.types.is_numeric_dtype(result[column]):
                        result[column] = result[column].fillna(result[column].median())
                elif strategy == 'fill_mode':
                    # 众数填充
                    mode_value = result[column].mode()
                    if not mode_value.empty:
                        result[column] = result[column].fillna(mode_value[0])
                elif isinstance(strategy, dict) and strategy.get('method') == 'fill_value':
                    # 指定值填充
                    fill_value = strategy.get('value')
                    result[column] = result[column].fillna(fill_value)
                else:
                    # 不支持的策略
                    self.logger.warning(f"不支持的缺失值处理策略: {strategy}，跳过处理列 {column}")
            
            return result
        except Exception as e:
            self.logger.error(f"处理缺失值失败: {str(e)}")
            raise CleaningError(f"处理缺失值失败: {str(e)}")
    
    def handle_outliers(self, data: pd.DataFrame, method: str = 'zscore', 
                        threshold: float = 3.0, columns: List[str] = None) -> pd.DataFrame:
        """
        处理异常值
        
        Args:
            data: 数据
            method: 异常值检测方法，支持 'zscore', 'iqr', 'percentile', 'winsorize'
            threshold: 异常值阈值
            columns: 要处理的列，如果为None则处理所有数值列
            
        Returns:
            处理后的数据
            
        Raises:
            CleaningError: 清洗错误
        """
        if data.empty:
            return data
            
        result = data.copy()
        
        try:
            # 确定要处理的列
            if columns is None:
                columns = result.select_dtypes(include=['number']).columns
            else:
                # 过滤出数值类型的列
                columns = [col for col in columns if col in result.columns and pd.api.types.is_numeric_dtype(result[col])]
            
            # 对每列应用异常值处理
            for column in columns:
                if method == 'zscore':
                    # Z-score方法：标准差的倍数
                    mean = result[column].mean()
                    std = result[column].std()
                    outliers = np.abs((result[column] - mean) / std) > threshold
                    result.loc[outliers, column] = np.nan
                
                elif method == 'iqr':
                    # IQR方法：四分位距
                    q1 = result[column].quantile(0.25)
                    q3 = result[column].quantile(0.75)
                    iqr = q3 - q1
                    lower_bound = q1 - threshold * iqr
                    upper_bound = q3 + threshold * iqr
                    outliers = (result[column] < lower_bound) | (result[column] > upper_bound)
                    result.loc[outliers, column] = np.nan
                
                elif method == 'percentile':
                    # 百分位方法
                    lower_bound = result[column].quantile(threshold / 100)
                    upper_bound = result[column].quantile(1 - threshold / 100)
                    outliers = (result[column] < lower_bound) | (result[column] > upper_bound)
                    result.loc[outliers, column] = np.nan
                
                elif method == 'winsorize':
                    # Winsorize方法：将异常值替换为边界值
                    lower_bound = result[column].quantile(threshold / 100)
                    upper_bound = result[column].quantile(1 - threshold / 100)
                    result[column] = result[column].clip(lower=lower_bound, upper=upper_bound)
                
                else:
                    # 不支持的方法
                    self.logger.warning(f"不支持的异常值处理方法: {method}，跳过处理列 {column}")
            
            return result
        except Exception as e:
            self.logger.error(f"处理异常值失败: {str(e)}")
            raise CleaningError(f"处理异常值失败: {str(e)}")
    
    def normalize(self, data: pd.DataFrame, method: str = 'zscore', 
                 columns: List[str] = None) -> pd.DataFrame:
        """
        数据标准化
        
        Args:
            data: 数据
            method: 标准化方法，支持 'zscore', 'minmax', 'robust'
            columns: 要标准化的列，如果为None则标准化所有数值列
            
        Returns:
            标准化后的数据
            
        Raises:
            TransformationError: 转换错误
        """
        if data.empty:
            return data
            
        result = data.copy()
        
        try:
            # 确定要处理的列
            if columns is None:
                columns = result.select_dtypes(include=['number']).columns
            else:
                # 过滤出数值类型的列
                columns = [col for col in columns if col in result.columns and pd.api.types.is_numeric_dtype(result[col])]
            
            # 对每列应用标准化
            for column in columns:
                if method == 'zscore':
                    # Z-score标准化：(x - mean) / std
                    mean = result[column].mean()
                    std = result[column].std()
                    if std == 0:  # 避免除以零
                        result[column] = 0
                    else:
                        result[column] = (result[column] - mean) / std
                
                elif method == 'minmax':
                    # Min-max标准化：(x - min) / (max - min)
                    min_val = result[column].min()
                    max_val = result[column].max()
                    if max_val == min_val:  # 避免除以零
                        result[column] = 0.5
                    else:
                        result[column] = (result[column] - min_val) / (max_val - min_val)
                
                elif method == 'robust':
                    # 鲁棒标准化：(x - median) / IQR
                    median = result[column].median()
                    q1 = result[column].quantile(0.25)
                    q3 = result[column].quantile(0.75)
                    iqr = q3 - q1
                    if iqr == 0:  # 避免除以零
                        result[column] = 0
                    else:
                        result[column] = (result[column] - median) / iqr
                
                else:
                    # 不支持的方法
                    self.logger.warning(f"不支持的标准化方法: {method}，跳过处理列 {column}")
            
            return result
        except Exception as e:
            self.logger.error(f"数据标准化失败: {str(e)}")
            raise TransformationError(f"数据标准化失败: {str(e)}")
    
    def convert_types(self, data: pd.DataFrame, type_mapping: Dict[str, str]) -> pd.DataFrame:
        """
        转换数据类型
        
        Args:
            data: 数据
            type_mapping: 列名到数据类型的映射
                支持的类型: 'int', 'float', 'str', 'bool', 'datetime'
                
        Returns:
            转换后的数据
            
        Raises:
            TransformationError: 转换错误
        """
        if data.empty or not type_mapping:
            return data
            
        result = data.copy()
        
        try:
            for column, type_str in type_mapping.items():
                if column not in result.columns:
                    self.logger.warning(f"列 {column} 不存在，跳过类型转换")
                    continue
                
                if type_str == 'int':
                    result[column] = pd.to_numeric(result[column], errors='coerce').astype('Int64')
                elif type_str == 'float':
                    result[column] = pd.to_numeric(result[column], errors='coerce')
                elif type_str == 'str':
                    result[column] = result[column].astype(str)
                elif type_str == 'bool':
                    result[column] = result[column].astype(bool)
                elif type_str == 'datetime':
                    result[column] = pd.to_datetime(result[column], errors='coerce')
                else:
                    self.logger.warning(f"不支持的数据类型: {type_str}，跳过列 {column} 的类型转换")
            
            return result
        except Exception as e:
            self.logger.error(f"数据类型转换失败: {str(e)}")
            raise TransformationError(f"数据类型转换失败: {str(e)}")


class DataFrameProcessor(ProcessorInterface):
    """
    DataFrame处理器基类
    
    专门用于处理pandas DataFrame的处理器基类
    """
    
    @abstractmethod
    def process(self, df: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        处理DataFrame
        
        参数:
            df: 输入的DataFrame
            **kwargs: 处理参数
            
        返回:
            pd.DataFrame: 处理后的DataFrame
        """
        pass
    
    @abstractmethod
    def clean(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        清洗DataFrame
        
        Args:
            data: 要清洗的DataFrame
            **kwargs: 清洗参数
            
        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        pass
    
    @abstractmethod
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        转换DataFrame
        
        Args:
            data: 要转换的DataFrame
            **kwargs: 转换参数
            
        Returns:
            pd.DataFrame: 转换后的DataFrame
        """
        pass


class SequentialProcessor(ProcessorInterface):
    """
    顺序处理器
    
    按顺序执行多个处理器
    """
    
    def __init__(self, processors: List[ProcessorInterface] = None):
        """
        初始化顺序处理器
        
        参数:
            processors: 要按顺序执行的处理器列表
        """
        self.processors = processors or []
    
    def add_processor(self, processor: ProcessorInterface) -> 'SequentialProcessor':
        """
        添加处理器
        
        参数:
            processor: 要添加的处理器
            
        返回:
            SequentialProcessor: 处理器自身，支持链式调用
        """
        self.processors.append(processor)
        return self
    
    def process(self, data: Any, **kwargs) -> Any:
        """
        顺序执行所有处理器
        
        参数:
            data: 输入数据
            **kwargs: 额外参数
            
        返回:
            处理后的数据
        """
        result = data
        for processor in self.processors:
            result = processor.process(result, **kwargs)
        return result
    
    def clean(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        顺序执行所有处理器的clean方法
        
        Args:
            data: 要清洗的数据
            **kwargs: 清洗参数
            
        Returns:
            清洗后的数据
        """
        result = data
        for processor in self.processors:
            result = processor.clean(result, **kwargs)
        return result
    
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        顺序执行所有处理器的transform方法
        
        Args:
            data: 要转换的数据
            **kwargs: 转换参数
            
        Returns:
            转换后的数据
        """
        result = data
        for processor in self.processors:
            result = processor.transform(result, **kwargs)
        return result


class ConditionalProcessor(ProcessorInterface):
    """
    条件处理器
    
    根据条件选择不同的处理器
    """
    
    def __init__(self, condition_func: callable, 
                true_processor: Optional[ProcessorInterface] = None,
                false_processor: Optional[ProcessorInterface] = None):
        """
        初始化条件处理器
        
        参数:
            condition_func: 条件函数，接收输入数据，返回布尔值
            true_processor: 条件为True时执行的处理器
            false_processor: 条件为False时执行的处理器
        """
        self.condition_func = condition_func
        self.true_processor = true_processor
        self.false_processor = false_processor
    
    def process(self, data: Any, **kwargs) -> Any:
        """
        根据条件选择处理器
        
        参数:
            data: 输入数据
            **kwargs: 额外参数
            
        返回:
            处理后的数据
        """
        if self.condition_func(data):
            if self.true_processor:
                return self.true_processor.process(data, **kwargs)
            return data
        else:
            if self.false_processor:
                return self.false_processor.process(data, **kwargs)
            return data
    
    def clean(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        根据条件选择处理器执行clean方法
        
        Args:
            data: 要清洗的数据
            **kwargs: 清洗参数
            
        Returns:
            清洗后的数据
        """
        if self.condition_func(data):
            if self.true_processor:
                return self.true_processor.clean(data, **kwargs)
            return data
        else:
            if self.false_processor:
                return self.false_processor.clean(data, **kwargs)
            return data
    
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        根据条件选择处理器执行transform方法
        
        Args:
            data: 要转换的数据
            **kwargs: 转换参数
            
        Returns:
            转换后的数据
        """
        if self.condition_func(data):
            if self.true_processor:
                return self.true_processor.transform(data, **kwargs)
            return data
        else:
            if self.false_processor:
                return self.false_processor.transform(data, **kwargs)
            return data


class ParallelProcessor(ProcessorInterface):
    """
    并行处理器
    
    并行应用多个处理器，然后合并结果
    """
    
    def __init__(self, processors: List[ProcessorInterface], merger: Optional[callable] = None):
        """
        初始化并行处理器
        
        参数:
            processors: 要并行执行的处理器列表
            merger: 合并结果的函数，如果为None则返回结果列表
        """
        self.processors = processors
        self.merger = merger
    
    def process(self, data: Any, **kwargs) -> Any:
        """
        并行应用所有处理器
        
        参数:
            data: 输入数据
            **kwargs: 额外参数
            
        返回:
            处理后的数据
        """
        results = [processor.process(data, **kwargs) for processor in self.processors]
        
        if self.merger is not None:
            return self.merger(results)
        return results
    
    def clean(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        并行执行所有处理器的clean方法，然后合并结果
        
        Args:
            data: 要清洗的数据
            **kwargs: 清洗参数
            
        Returns:
            清洗后的数据
        """
        results = [processor.clean(data, **kwargs) for processor in self.processors]
        
        if self.merger is not None:
            return self.merger(results)
        # 如果没有合并函数，默认返回第一个处理器的结果
        return results[0] if results else data
    
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        并行执行所有处理器的transform方法，然后合并结果
        
        Args:
            data: 要转换的数据
            **kwargs: 转换参数
            
        Returns:
            转换后的数据
        """
        results = [processor.transform(data, **kwargs) for processor in self.processors]
        
        if self.merger is not None:
            return self.merger(results)
        # 如果没有合并函数，默认返回第一个处理器的结果
        return results[0] if results else data


# 为了与旧代码兼容，提供一个别名
DataProcessorInterface = ProcessorInterface

