"""
数据合并处理器
提供多种数据源的合并、对齐和连接功能
"""

import pandas as pd
import numpy as np
import logging
from typing import List, Dict, Any, Optional, Union, Tuple, Callable

from src.data.processors.processor_interface import ProcessorInterface


class DataMerger(ProcessorInterface):
    """
    数据合并处理器
    
    支持不同数据源的数据合并、对齐和连接功能
    """
    
    def __init__(self, on: Union[str, List[str]] = None,
                how: str = 'inner',
                time_column: str = None,
                reindex: bool = False,
                fill_method: Optional[str] = None):
        """
        初始化数据合并处理器
        
        参数:
            on: 连接列名(或列名列表)，若为None则自动检测
            how: 连接方式，'inner', 'outer', 'left', 'right'之一
            time_column: 时间列名，用于时间序列数据的排序和对齐
            reindex: 是否需要重新索引
            fill_method: 缺失值填充方法，如'ffill'（前向填充）, 'bfill'（后向填充）
        """
        self.on = on
        self.how = how
        self.time_column = time_column
        self.reindex = reindex
        self.fill_method = fill_method
        self.logger = logging.getLogger(__name__)
        
    def process(self, *dataframes, **kwargs) -> pd.DataFrame:
        """
        合并多个DataFrame
        
        参数:
            *dataframes: 待合并的DataFrame
            **kwargs: 附加参数
                - on: 覆盖构造函数中的on参数
                - how: 覆盖构造函数中的how参数
                - time_column: 覆盖构造函数中的time_column参数
                - reindex: 覆盖构造函数中的reindex参数
                - fill_method: 覆盖构造函数中的fill_method参数
        
        返回:
            pd.DataFrame: 合并后的DataFrame
        """
        if len(dataframes) == 0:
            self.logger.warning("没有提供任何数据帧，无法执行合并")
            return pd.DataFrame()
            
        if len(dataframes) == 1:
            self.logger.info("只提供了一个数据帧，直接返回")
            return dataframes[0]
            
        # 获取参数，优先使用调用时传入的参数
        on = kwargs.get('on', self.on)
        how = kwargs.get('how', self.how)
        time_column = kwargs.get('time_column', self.time_column)
        reindex = kwargs.get('reindex', self.reindex)
        fill_method = kwargs.get('fill_method', self.fill_method)
        
        # 记录开始合并
        n_dataframes = len(dataframes)
        total_rows = sum(len(df) for df in dataframes)
        self.logger.info(f"开始合并 {n_dataframes} 个数据帧，总计 {total_rows} 行数据")
        
        # 检查数据帧有效性
        valid_dfs = [df for df in dataframes if df is not None and not df.empty]
        if len(valid_dfs) < len(dataframes):
            self.logger.warning(f"过滤掉 {len(dataframes) - len(valid_dfs)} 个无效的数据帧")
            
        if len(valid_dfs) == 0:
            self.logger.warning("没有有效的数据帧，无法执行合并")
            return pd.DataFrame()
            
        if len(valid_dfs) == 1:
            self.logger.info("只有一个有效数据帧，直接返回")
            return valid_dfs[0]
        
        # 如果是时间序列数据
        if time_column is not None:
            return self._merge_time_series(valid_dfs, time_column, how, on, reindex, fill_method)
        
        # 尝试检测连接列
        if on is None:
            on = self._detect_join_columns(valid_dfs)
            self.logger.info(f"自动检测到连接列: {on}")
            
        # 执行合并
        try:
            result = self._merge_dataframes(valid_dfs, on, how)
            
            # 填充缺失值（如果指定）
            if fill_method is not None and not result.empty:
                result = result.fillna(method=fill_method)
                
            self.logger.info(f"合并完成，结果数据帧包含 {len(result)} 行")
            return result
            
        except Exception as e:
            self.logger.error(f"合并数据帧时出错: {str(e)}")
            raise
    
    def _merge_dataframes(self, dataframes: List[pd.DataFrame], on: Union[str, List[str]], how: str) -> pd.DataFrame:
        """
        合并数据帧的核心函数
        
        参数:
            dataframes: 待合并的数据帧列表
            on: 连接列
            how: 连接方式
            
        返回:
            pd.DataFrame: 合并结果
        """
        result = dataframes[0]
        suffix_base = '_df'
        
        for i, df in enumerate(dataframes[1:], start=1):
            suffix = f"{suffix_base}{i}"
            
            # 处理列名冲突
            overlapping_columns = set(result.columns) & set(df.columns)
            if on is not None:
                if isinstance(on, list):
                    overlapping_columns = overlapping_columns - set(on)
                else:
                    overlapping_columns = overlapping_columns - {on}
                    
            if overlapping_columns:
                self.logger.info(f"合并时发现 {len(overlapping_columns)} 个列名冲突")
                
            # 执行合并
            try:
                result = pd.merge(
                    result, df,
                    on=on,
                    how=how,
                    suffixes=('', suffix)
                )
            except Exception as e:
                self.logger.error(f"在合并第 {i} 个数据帧时出错: {str(e)}")
                raise
                
        return result
    
    def _merge_time_series(self, dataframes: List[pd.DataFrame], 
                         time_column: str, 
                         how: str, 
                         on: Union[str, List[str]],
                         reindex: bool,
                         fill_method: Optional[str]) -> pd.DataFrame:
        """
        合并时间序列数据帧
        
        参数:
            dataframes: 待合并的数据帧列表
            time_column: 时间列名
            how: 连接方式
            on: 连接列（如果不是时间列）
            reindex: 是否重新索引
            fill_method: 缺失值填充方法
            
        返回:
            pd.DataFrame: 合并结果
        """
        # 检查每个数据帧是否都包含时间列
        for i, df in enumerate(dataframes):
            if time_column not in df.columns:
                self.logger.warning(f"第 {i} 个数据帧不包含时间列 '{time_column}'")
                return self._merge_dataframes(dataframes, on, how)
        
        # 检查是否可以使用TimeSeriesMerger的merge_time_series方法
        if hasattr(self, 'merge_time_series') and callable(getattr(self, 'merge_time_series')):
            # 时间序列合并器可以直接调用其merge_time_series方法
            return self.merge_time_series(dataframes, fill_method=fill_method)
        
        # 如果时间列是连接列，且未指定其他连接列
        join_on = on if on is not None else time_column
        
        # 先合并数据
        result = self._merge_dataframes(dataframes, join_on, how)
        
        # 是否需要重新索引
        if reindex and not result.empty:
            try:
                # 确保时间列是日期时间类型
                result[time_column] = pd.to_datetime(result[time_column])
                
                # 设置时间索引
                result.set_index(time_column, inplace=True)
                
                # 按时间排序
                result.sort_index(inplace=True)
                
                # 填充缺失值
                if fill_method is not None:
                    if fill_method == 'ffill':
                        result = result.ffill()
                    elif fill_method == 'bfill':
                        result = result.bfill()
                    else:
                        # 兼容旧格式，但发出警告
                        self.logger.warning("DataFrame.fillna with 'method' is deprecated, using ffill/bfill instead")
                        result = result.fillna(method=fill_method)
                    
                self.logger.info(f"已将结果按 '{time_column}' 重新索引并排序")
            except Exception as e:
                self.logger.error(f"重新索引时出错: {str(e)}")
        
        return result
    
    def _detect_join_columns(self, dataframes: List[pd.DataFrame]) -> Union[str, List[str]]:
        """
        自动检测适合连接的列
        
        参数:
            dataframes: 数据帧列表
            
        返回:
            Union[str, List[str]]: 检测到的连接列名或列名列表
        """
        # 获取所有数据帧共有的列
        common_columns = set(dataframes[0].columns)
        for df in dataframes[1:]:
            common_columns &= set(df.columns)
            
        if not common_columns:
            self.logger.warning("没有找到共有列，无法自动检测连接列")
            return None
            
        # 优先考虑特定名称的列（常见的主键列名）
        priority_columns = ['id', 'code', 'symbol', 'ts_code', 'date', 'time', 'datetime', 'timestamp']
        for col in priority_columns:
            if col in common_columns:
                return col
                
        # 如果没有找到优先列，尝试根据列的唯一值比例来判断
        potential_join_columns = []
        for col in common_columns:
            unique_ratios = []
            for df in dataframes:
                if df[col].isna().any():
                    unique_ratios.append(0)  # 忽略包含空值的列
                    continue
                    
                unique_ratio = len(df[col].unique()) / len(df)
                unique_ratios.append(unique_ratio)
                
            # 计算平均唯一值比例
            avg_unique_ratio = sum(unique_ratios) / len(unique_ratios) if unique_ratios else 0
            
            # 高唯一值比例的列更可能是主键/ID列
            if 0.1 <= avg_unique_ratio <= 1.0:
                potential_join_columns.append((col, avg_unique_ratio))
                
        # 按唯一值比例排序，优先选择唯一值比例较高的列
        potential_join_columns.sort(key=lambda x: x[1], reverse=True)
        
        if potential_join_columns:
            # 返回唯一值比例最高的列
            return potential_join_columns[0][0]
        
        # 如果无法确定，返回第一个共有列
        return list(common_columns)[0]
    
    
class TimeSeriesMerger(DataMerger):
    """
    时间序列数据合并处理器
    
    专为时间序列数据设计的合并处理器
    """
    
    def __init__(self, time_column: str = 'date',
                how: str = 'outer',
                reindex: bool = True,
                fill_method: str = 'ffill',
                resample: Optional[str] = None):
        """
        初始化时间序列数据合并处理器
        
        参数:
            time_column: 时间列名
            how: 连接方式，'inner', 'outer', 'left', 'right'之一
            reindex: 是否需要重新索引（默认为True）
            fill_method: 缺失值填充方法（默认为前向填充）
            resample: 重采样频率，如'D'（日）, 'W'（周）, 'M'（月）
        """
        super().__init__(
            on=time_column,
            how=how,
            time_column=time_column, 
            reindex=reindex,
            fill_method=fill_method
        )
        self.resample = resample
    
    def merge_time_series(self, dataframes: List[pd.DataFrame], **kwargs) -> pd.DataFrame:
        """
        合并时间序列数据
        
        Args:
            dataframes: 时间序列数据列表
            **kwargs: 额外参数
        
        Returns:
            合并后的时间序列数据
        """
        if not dataframes:
            return pd.DataFrame()
        
        # 确保所有数据都有索引
        dfs = []
        for df in dataframes:
            df_copy = df.copy()
            if not isinstance(df_copy.index, pd.DatetimeIndex):
                if self.time_column in df_copy.columns:
                    # 如果有时间列，设置为索引
                    df_copy.set_index(self.time_column, inplace=True)
                df_copy.index = pd.to_datetime(df_copy.index)
            dfs.append(df_copy)
        
        # 合并所有数据
        result = pd.concat(dfs, axis=1, join='outer')
        
        # 填充缺失值
        method = kwargs.get('fill_method', self.fill_method)
        if method:
            if method == 'ffill':
                result = result.ffill()
            elif method == 'bfill':
                result = result.bfill()
            elif method == 'zero':
                result = result.fillna(0)
            elif method == 'value' and 'value' in kwargs:
                result = result.fillna(kwargs['value'])
            else:
                # 兼容旧格式，但发出警告
                self.logger.warning("DataFrame.fillna with 'method' is deprecated, using ffill/bfill instead")
                result = result.fillna(method=method)
        
        # 如果需要重采样
        resample_freq = kwargs.get('resample', self.resample)
        if resample_freq:
            result = result.resample(resample_freq).agg(self._get_resample_agg_funcs(result))
        
        return result
    
    def fill_missing_values(self, data: pd.DataFrame, method: str = 'ffill', value = None) -> pd.DataFrame:
        """
        填充缺失值
        
        Args:
            data: 包含缺失值的数据
            method: 填充方法，'ffill', 'bfill', 'zero', 'value'
            value: 当method='value'时使用的填充值
            
        Returns:
            填充后的数据
        """
        result = data.copy()
        
        if method == 'ffill':
            result = result.ffill()
        elif method == 'bfill':
            result = result.bfill()
        elif method == 'zero':
            result = result.fillna(0)
        elif method == 'value':
            result = result.fillna(value)
        else:
            # 兼容旧格式，但发出警告
            self.logger.warning("DataFrame.fillna with 'method' is deprecated, using ffill/bfill instead")
            result = result.fillna(method=method)
        
        return result
    
    def interpolate_data(self, data: pd.DataFrame, method: str = 'linear') -> pd.DataFrame:
        """
        插值填充缺失值
        
        Args:
            data: 包含缺失值的数据
            method: 插值方法，'linear', 'cubic', 'spline'等
            
        Returns:
            插值填充后的数据
        """
        return data.interpolate(method=method)
    
    def reindex_time_series(self, data: pd.DataFrame, new_index) -> pd.DataFrame:
        """
        重索引时间序列
        
        Args:
            data: 时间序列数据
            new_index: 新索引
            
        Returns:
            重索引后的数据
        """
        return data.reindex(new_index)
    
    def merge_with_custom_function(self, dataframes: List[pd.DataFrame], custom_func: callable) -> pd.DataFrame:
        """
        使用自定义函数合并数据
        
        Args:
            dataframes: 数据列表
            custom_func: 自定义合并函数
            
        Returns:
            合并后的数据
        """
        return custom_func(dataframes)
        
    def clean(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        清洗时间序列数据
        
        Args:
            data: 要清洗的数据
            **kwargs: 清洗参数
            
        Returns:
            清洗后的数据
        """
        # 基本实现：确保时间列是日期时间类型
        if data is None or data.empty:
            return data
            
        if self.time_column in data.columns:
            try:
                data = data.copy()
                data[self.time_column] = pd.to_datetime(data[self.time_column])
                self.logger.info(f"已将 '{self.time_column}' 列转换为日期时间类型")
            except Exception as e:
                self.logger.warning(f"转换时间列时出错: {str(e)}")
                
        return data
    
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        转换时间序列数据
        
        Args:
            data: 要转换的数据
            **kwargs: 转换参数
            
        Returns:
            转换后的数据
        """
        # 基本实现：如果需要可以进行时间序列相关的转换
        if data is None or data.empty:
            return data
            
        # 如果需要重新索引
        if self.reindex and self.time_column in data.columns:
            try:
                data = data.set_index(self.time_column).sort_index()
                self.logger.info(f"已将 '{self.time_column}' 设置为索引并排序")
            except Exception as e:
                self.logger.warning(f"设置索引时出错: {str(e)}")
                
        # 如果需要重采样
        if self.resample and isinstance(data.index, pd.DatetimeIndex):
            try:
                data = data.resample(self.resample).agg(self._get_resample_agg_funcs(data))
                self.logger.info(f"已按 '{self.resample}' 频率重采样数据")
            except Exception as e:
                self.logger.warning(f"重采样时出错: {str(e)}")
                
        return data
        
    def process(self, *dataframes, **kwargs) -> pd.DataFrame:
        """
        合并时间序列数据
        
        参数:
            *dataframes: 待合并的DataFrame
            **kwargs: 附加参数
                - resample: 覆盖构造函数中的resample参数
                
        返回:
            pd.DataFrame: 合并后的DataFrame
        """
        if len(dataframes) == 0:
            self.logger.warning("没有提供任何数据帧，无法执行合并")
            return pd.DataFrame()
            
        if len(dataframes) == 1:
            self.logger.info("只提供了一个数据帧，直接返回")
            return dataframes[0]
        
        # 时间序列合并器直接使用merge_time_series方法
        # 首先检查数据是否已经包含时间列或索引
        time_column = self.time_column
        dfs_to_merge = []
        
        for df in dataframes:
            if df is None or df.empty:
                continue
                
            df_copy = df.copy()
            
            # 如果数据已经有DatetimeIndex，直接加入
            if isinstance(df_copy.index, pd.DatetimeIndex):
                dfs_to_merge.append(df_copy)
                continue
                
            # 如果数据包含时间列，转换为时间类型
            if time_column in df_copy.columns:
                # 转换为日期时间类型
                df_copy[time_column] = pd.to_datetime(df_copy[time_column])
                dfs_to_merge.append(df_copy)
            else:
                self.logger.warning(f"数据帧缺少时间列 '{time_column}'，尝试普通合并")
                # 如果缺少时间列，回退到父类方法处理
                return super().process(*dataframes, **kwargs)
        
        if not dfs_to_merge:
            self.logger.warning("没有有效的数据帧，无法执行合并")
            return pd.DataFrame()
            
        if len(dfs_to_merge) == 1:
            self.logger.info("只有一个有效数据帧，直接返回")
            return dfs_to_merge[0]
        
        # 获取重采样参数
        resample = kwargs.pop('resample', self.resample)
        
        # 调用时间序列合并方法
        result = self.merge_time_series(dfs_to_merge, 
                                      fill_method=self.fill_method, 
                                      resample=resample, 
                                      **kwargs)
        
        self.logger.info(f"时间序列合并完成，结果数据帧包含 {len(result)} 行")
        return result
    
    def _get_resample_agg_funcs(self, df: pd.DataFrame) -> Dict[str, Union[str, Callable]]:
        """
        根据数据类型获取适当的重采样聚合函数
        
        参数:
            df: 数据帧
            
        返回:
            Dict[str, Union[str, Callable]]: 列名到聚合函数的映射
        """
        agg_funcs = {}
        
        for col in df.columns:
            # 检查数据类型
            dtype = df[col].dtype
            
            # 数值型列
            if np.issubdtype(dtype, np.number):
                # 常见的OHLC列
                if col.lower() in ['open', 'first']:
                    agg_funcs[col] = 'first'
                elif col.lower() in ['high', 'max']:
                    agg_funcs[col] = 'max'
                elif col.lower() in ['low', 'min']:
                    agg_funcs[col] = 'min'
                elif col.lower() in ['close', 'last']:
                    agg_funcs[col] = 'last'
                elif col.lower() in ['volume', 'amount', 'vol', 'amt']:
                    agg_funcs[col] = 'sum'
                else:
                    # 其他数值列默认取平均
                    agg_funcs[col] = 'mean'
            # 字符串或分类列
            elif np.issubdtype(dtype, np.object_) or str(dtype) == 'category':
                # 字符串列通常取最后一个有效值
                agg_funcs[col] = 'last'
            # 日期时间列
            elif np.issubdtype(dtype, np.datetime64):
                agg_funcs[col] = 'last'
            # 布尔列
            elif np.issubdtype(dtype, np.bool_):
                agg_funcs[col] = lambda x: any(x)
            # 其他类型
            else:
                agg_funcs[col] = 'last'
                
        return agg_funcs
    
    
class PanelDataMerger(DataMerger):
    """
    面板数据合并处理器
    
    用于合并多个实体的时间序列数据，例如多只股票的历史价格
    """
    
    def __init__(self, entity_column: str = 'ts_code',
                time_column: str = 'trade_date',
                how: str = 'outer',
                reindex: bool = True,
                fill_method: str = 'ffill',
                pivot: bool = True):
        """
        初始化面板数据合并处理器
        
        参数:
            entity_column: 实体列名（如股票代码）
            time_column: 时间列名
            how: 连接方式
            reindex: 是否重新索引
            fill_method: 缺失值填充方法
            pivot: 是否需要转为宽表格式（每个实体一列）
        """
        super().__init__(
            on=[entity_column, time_column] if pivot else time_column,
            how=how,
            time_column=time_column,
            reindex=reindex,
            fill_method=fill_method
        )
        self.entity_column = entity_column
        self.pivot = pivot
    
    def merge_panel_data(self, dataframes: List[pd.DataFrame], 
                          on: Union[str, List[str]] = None, 
                          how: str = None, 
                          **kwargs) -> pd.DataFrame:
        """
        合并面板数据
        
        Args:
            dataframes: 面板数据列表
            on: 连接列，如果为None则使用初始化时指定的on参数
            how: 连接方式，如果为None则使用初始化时指定的how参数
            **kwargs: 额外参数
                - pivot: 是否转为宽表格式
                - value_column: 做透视表时使用的值列
                
        Returns:
            合并后的面板数据
        """
        if not dataframes:
            return pd.DataFrame()
            
        # 获取参数，优先使用调用时传入的参数
        on_cols = on if on is not None else (self.on if not self.pivot else [self.entity_column, self.time_column])
        merge_how = how if how is not None else self.how
        pivot = kwargs.get('pivot', self.pivot)
        
        # 使用pandas merge方法合并
        result = dataframes[0].copy()
        for i, df in enumerate(dataframes[1:], 1):
            try:
                result = pd.merge(result, df, on=on_cols, how=merge_how)
            except Exception as e:
                self.logger.error(f"合并第 {i} 个数据帧时出错: {str(e)}")
                raise
                
        # 如果需要转为宽表格式
        if pivot:
            value_column = kwargs.get('value_column')
            try:
                if value_column and value_column in result.columns:
                    result = self._pivot_single_value(result, value_column)
                else:
                    result = self._pivot_multiple_values(result)
            except Exception as e:
                self.logger.error(f"透视数据时出错: {str(e)}")
                
        # 填充缺失值
        fill_method = kwargs.get('fill_method', self.fill_method)
        if fill_method and not result.empty:
            if fill_method == 'ffill':
                result = result.ffill()
            elif fill_method == 'bfill':
                result = result.bfill()
            elif fill_method == 'zero':
                result = result.fillna(0)
            elif fill_method == 'value' and 'value' in kwargs:
                result = result.fillna(kwargs['value'])
            else:
                # 兼容旧格式，但发出警告
                self.logger.warning("DataFrame.fillna with 'method' is deprecated, using ffill/bfill instead")
                result = result.fillna(method=fill_method)
                
        return result
        
    def clean(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        清洗面板数据
        
        Args:
            data: 要清洗的数据
            **kwargs: 清洗参数
            
        Returns:
            清洗后的数据
        """
        # 基本实现：确保时间列和实体列的有效性
        if data is None or data.empty:
            return data
            
        # 复制数据以避免修改原始数据
        data = data.copy()
            
        # 确保时间列是日期时间类型
        if self.time_column in data.columns:
            try:
                data[self.time_column] = pd.to_datetime(data[self.time_column])
                self.logger.info(f"已将 '{self.time_column}' 列转换为日期时间类型")
            except Exception as e:
                self.logger.warning(f"转换时间列时出错: {str(e)}")
                
        # 确保实体列没有缺失值
        if self.entity_column in data.columns:
            missing = data[self.entity_column].isnull().sum()
            if missing > 0:
                self.logger.warning(f"实体列 '{self.entity_column}' 有 {missing} 个缺失值")
                data = data.dropna(subset=[self.entity_column])
                self.logger.info(f"已删除实体列中有缺失值的 {missing} 行")
                
        return data
    
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        转换面板数据
        
        Args:
            data: 要转换的数据
            **kwargs: 转换参数
            
        Returns:
            转换后的数据
        """
        # 基本实现：如果需要可以进行面板数据相关的转换
        if data is None or data.empty:
            return data
            
        # 如果需要透视
        if self.pivot and self.entity_column in data.columns:
            try:
                # 获取要透视的值列
                value_column = kwargs.get('value_column')
                
                if value_column and value_column in data.columns:
                    result = self._pivot_single_value(data, value_column)
                else:
                    result = self._pivot_multiple_values(data)
                    
                self.logger.info(f"已将数据透视为宽表格式，从 {len(data)} 行转换为 {len(result)} 行")
                return result
            except Exception as e:
                self.logger.warning(f"透视数据时出错: {str(e)}")
                
        # 排序
        try:
            sort_columns = []
            if self.entity_column in data.columns:
                sort_columns.append(self.entity_column)
            if self.time_column in data.columns:
                sort_columns.append(self.time_column)
                
            if sort_columns:
                data = data.sort_values(sort_columns)
                self.logger.info(f"已按 {sort_columns} 排序数据")
        except Exception as e:
            self.logger.warning(f"排序数据时出错: {str(e)}")
                
        return data
        
    def process(self, *dataframes, **kwargs) -> pd.DataFrame:
        """
        合并面板数据
        
        参数:
            *dataframes: 待合并的DataFrame
            **kwargs: 附加参数
                - pivot: 覆盖构造函数中的pivot参数
                - value_column: 数据透视表的值列（当pivot=True时使用）
                
        返回:
            pd.DataFrame: 合并后的DataFrame
        """
        if len(dataframes) == 0:
            self.logger.warning("没有提供任何数据帧，无法执行合并")
            return pd.DataFrame()
            
        if len(dataframes) == 1:
            self.logger.info("只提供了一个数据帧，直接返回")
            return dataframes[0]
            
        # 检查数据帧是否有实体列和时间列
        entity_col = self.entity_column
        time_col = self.time_column
        
        dfs_to_merge = []
        for df in dataframes:
            if df is None or df.empty:
                continue
                
            df_copy = df.copy()
            
            # 确保时间列和实体列存在
            if entity_col in df_copy.columns and time_col in df_copy.columns:
                # 确保时间列是日期时间类型
                df_copy[time_col] = pd.to_datetime(df_copy[time_col])
                dfs_to_merge.append(df_copy)
            else:
                self.logger.warning(f"数据帧缺少必需的列: {entity_col}或{time_col}，尝试普通合并")
                # 如果缺少必要列，回退到父类方法处理
                return super().process(*dataframes, **kwargs)
                
        if not dfs_to_merge:
            self.logger.warning("没有有效的数据帧，无法执行合并")
            return pd.DataFrame()
            
        if len(dfs_to_merge) == 1:
            self.logger.info("只有一个有效数据帧，直接返回")
            return dfs_to_merge[0]
        
        # 获取参数，优先使用调用时传入的参数
        pivot = kwargs.pop('pivot', self.pivot)
        value_column = kwargs.pop('value_column', None)
        
        # 调用面板数据合并方法
        result = self.merge_panel_data(
            dfs_to_merge,
            pivot=pivot,
            value_column=value_column,
            fill_method=self.fill_method,
            **kwargs
        )
        
        self.logger.info(f"面板数据合并完成，结果数据帧包含 {len(result)} 行")
        return result
    
    def _pivot_single_value(self, df: pd.DataFrame, value_column: str) -> pd.DataFrame:
        """
        将单个值列透视为宽表格式
        
        参数:
            df: 数据帧
            value_column: 值列名
            
        返回:
            pd.DataFrame: 透视后的数据帧
        """
        # 使用数据透视表
        pivoted = pd.pivot_table(
            df,
            values=value_column,
            index=self.time_column,
            columns=self.entity_column,
            aggfunc='first'  # 使用第一个值（假设每个实体在每个时间点只有一个值）
        )
        
        # 填充缺失值
        if self.fill_method is not None:
            pivoted = pivoted.fillna(method=self.fill_method)
            
        return pivoted
    
    def _pivot_multiple_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        将多个值列透视为宽表格式
        
        参数:
            df: 数据帧
            
        返回:
            pd.DataFrame: 透视后的数据帧
        """
        # 确定要透视的值列
        entity_col = self.entity_column
        time_col = self.time_column
        
        # 排除实体列和时间列
        value_columns = [col for col in df.columns if col != entity_col and col != time_col]
        
        # 按时间和实体分组，然后使用first聚合
        # 这种方法比pivot_table更灵活，可以处理多个值列
        result = df.set_index([time_col, entity_col])[value_columns].unstack(entity_col)
        
        # 填充缺失值
        if self.fill_method is not None:
            result = result.fillna(method=self.fill_method)
            
        return result

