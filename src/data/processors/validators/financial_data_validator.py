"""
财务数据验证器
- 专用于验证财务报表数据
- 提供针对财务数据的验证规则
- 支持财务指标一致性检查
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union, Optional, Tuple, Set
import logging
from datetime import datetime, timedelta

from .validator_interface import BaseValidator
from src.data.processors.processor_interface import CleaningError, TransformationError

class FinancialDataValidator(BaseValidator):
    """
    财务数据验证器，验证资产负债表、利润表、现金流量表等财务数据
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化财务数据验证器
        
        Args:
            config: 验证器配置，除了基础验证器的配置外，还可以包含：
                - report_type: 报表类型，'balance'(资产负债表)、'income'(利润表)、'cashflow'(现金流量表)
                - check_formula: 是否检查财务公式
                - check_yoy_change: 是否检查同比变化
                - max_yoy_change: 最大同比变化百分比
                - check_duplicate: 是否检查重复数据
                - check_outlier: 是否检查异常值
                - outlier_method: 异常值检测方法，'zscore'、'iqr'等
                - outlier_threshold: 异常值阈值
        """
        # 默认财务数据配置
        financial_data_config = {
            'required_columns': ['ts_code', 'ann_date', 'end_date', 'report_type'],
            'column_types': {
                'ts_code': 'string',
                'ann_date': 'datetime',
                'end_date': 'datetime',
                'report_type': 'numeric'
            },
            'report_type': None,  # 可以是'balance', 'income', 'cashflow'
            'check_formula': True,
            'check_yoy_change': True,
            'max_yoy_change': 1000.0,  # 默认1000%
            'check_duplicate': True,
            'check_outlier': True,
            'outlier_method': 'zscore',
            'outlier_threshold': 3.0
        }
        
        # 合并传入的配置
        if config:
            # 合并嵌套字典
            for key, value in config.items():
                if isinstance(value, dict) and key in financial_data_config and isinstance(financial_data_config[key], dict):
                    financial_data_config[key].update(value)
                else:
                    financial_data_config[key] = value
        
        # 根据报表类型更新配置
        report_type = financial_data_config.get('report_type')
        if report_type:
            financial_data_config = self._update_config_by_report_type(financial_data_config, report_type)
        
        # 调用父类初始化
        super().__init__(financial_data_config)
        
        # 添加财务数据特有的验证规则
        self._add_financial_data_rules()
    
    def _update_config_by_report_type(self, config: Dict[str, Any], report_type: str) -> Dict[str, Any]:
        """
        根据报表类型更新配置
        
        Args:
            config: 原始配置
            report_type: 报表类型
            
        Returns:
            Dict[str, Any]: 更新后的配置
        """
        if report_type == 'balance':
            # 资产负债表必须列
            balance_columns = [
                'total_assets', 'total_liab', 'total_hldr_eqy_exc_min_int'
            ]
            config['required_columns'].extend(balance_columns)
            for col in balance_columns:
                config['column_types'][col] = 'numeric'
                
        elif report_type == 'income':
            # 利润表必须列
            income_columns = [
                'revenue', 'total_cogs', 'operate_profit', 'n_income'
            ]
            config['required_columns'].extend(income_columns)
            for col in income_columns:
                config['column_types'][col] = 'numeric'
                
        elif report_type == 'cashflow':
            # 现金流量表必须列
            cashflow_columns = [
                'n_cashflow_act', 'n_cashflow_inv_act', 'n_cash_flows_fnc_act', 'c_cash_equ_end_period'
            ]
            config['required_columns'].extend(cashflow_columns)
            for col in cashflow_columns:
                config['column_types'][col] = 'numeric'
        
        return config
    
    def _add_financial_data_rules(self):
        """
        添加财务数据特有的验证规则
        """
        # 检查重复数据
        if self.config.get('check_duplicate', True):
            self.add_rule(
                'check_duplicate_data',
                self._check_duplicate_data
            )
        
        # 检查财务公式
        if self.config.get('check_formula', True):
            report_type = self.config.get('report_type')
            if report_type == 'balance':
                self.add_rule(
                    'check_balance_formula',
                    self._check_balance_formula
                )
            elif report_type == 'income':
                self.add_rule(
                    'check_income_formula',
                    self._check_income_formula
                )
            elif report_type == 'cashflow':
                self.add_rule(
                    'check_cashflow_formula',
                    self._check_cashflow_formula
                )
        
        # 检查同比变化
        if self.config.get('check_yoy_change', True):
            self.add_rule(
                'check_yoy_change',
                self._check_yoy_change,
                max_change=self.config.get('max_yoy_change', 1000.0)
            )
        
        # 检查异常值
        if self.config.get('check_outlier', True):
            self.add_rule(
                'check_outlier',
                self._check_outlier,
                method=self.config.get('outlier_method', 'zscore'),
                threshold=self.config.get('outlier_threshold', 3.0)
            )
    
    def _check_duplicate_data(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        检查是否存在重复的财务数据
        
        Args:
            data: 财务数据
            
        Returns:
            Tuple[bool, str]: (是否通过, 错误信息)
        """
        required_columns = ['ts_code', 'end_date', 'report_type']
        if not all(col in data.columns for col in required_columns):
            return True, "数据中缺少必要列，无法检查重复数据"
        
        # 检查重复数据
        duplicate_keys = ['ts_code', 'end_date', 'report_type']
        duplicates = data[data.duplicated(subset=duplicate_keys, keep=False)]
        
        duplicate_count = len(duplicates)
        if duplicate_count > 0:
            duplicate_groups = duplicates.groupby(duplicate_keys).size().reset_index(name='count')
            duplicate_groups = duplicate_groups[duplicate_groups['count'] > 1]
            return False, f"发现 {len(duplicate_groups)} 组重复的财务数据，共 {duplicate_count} 条记录"
        
        return True, "没有发现重复的财务数据"
    
    def _check_balance_formula(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        检查资产负债表公式是否满足
        资产 = 负债 + 所有者权益
        
        Args:
            data: 财务数据
            
        Returns:
            Tuple[bool, str]: (是否通过, 错误信息)
        """
        required_columns = ['total_assets', 'total_liab', 'total_hldr_eqy_exc_min_int']
        if not all(col in data.columns for col in required_columns):
            return True, "数据中缺少必要列，无法检查资产负债表公式"
        
        # 创建副本，避免修改原始数据
        check_data = data.copy()
        
        # 计算误差
        check_data.loc[:, 'balance_error'] = abs(check_data['total_assets'] - (check_data['total_liab'] + check_data['total_hldr_eqy_exc_min_int']))
        check_data.loc[:, 'balance_error_pct'] = check_data['balance_error'] / check_data['total_assets'] * 100
        
        # 允许1%的误差
        violations = check_data[check_data['balance_error_pct'] > 1.0].dropna()
        
        violation_count = len(violations)
        if violation_count > 0:
            return False, f"发现 {violation_count} 条记录不满足资产负债表公式（误差>1%）"
        
        return True, "所有记录都满足资产负债表公式"
    
    def _check_income_formula(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        检查利润表公式是否满足
        营业利润 = 营业收入 - 营业成本 - 营业税金及附加 - 销售费用 - 管理费用 - 财务费用 - 资产减值损失 + 公允价值变动收益 + 投资收益
        
        Args:
            data: 财务数据
            
        Returns:
            Tuple[bool, str]: (是否通过, 错误信息)
        """
        # 简化版检查：营业利润应该小于营业收入
        if 'operate_profit' in data.columns and 'revenue' in data.columns:
            violations = data[data['operate_profit'] > data['revenue']].dropna()
            
            violation_count = len(violations)
            if violation_count > 0:
                return False, f"发现 {violation_count} 条记录的营业利润大于营业收入，可能数据有误"
        
        # 净利润应该小于营业利润（一般情况）
        if 'n_income' in data.columns and 'operate_profit' in data.columns:
            # 排除营业利润为负的情况
            profit_data = data[data['operate_profit'] > 0]
            violations = profit_data[profit_data['n_income'] > profit_data['operate_profit'] * 1.5].dropna()
            
            violation_count = len(violations)
            if violation_count > 0:
                return False, f"发现 {violation_count} 条记录的净利润远大于营业利润，可能数据有误"
        
        return True, "所有记录都满足利润表基本规则"
    
    def _check_cashflow_formula(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        检查现金流量表公式是否满足
        期末现金及现金等价物余额 = 期初现金及现金等价物余额 + 经营活动产生的现金流量净额 + 投资活动产生的现金流量净额 + 筹资活动产生的现金流量净额 + 汇率变动对现金的影响
        
        Args:
            data: 财务数据
            
        Returns:
            Tuple[bool, str]: (是否通过, 错误信息)
        """
        required_columns = [
            'c_cash_equ_beg_period', 'c_cash_equ_end_period', 
            'n_cashflow_act', 'n_cashflow_inv_act', 'n_cash_flows_fnc_act'
        ]
        
        # 检查是否有足够的列来验证公式
        available_columns = [col for col in required_columns if col in data.columns]
        if len(available_columns) < 3:  # 至少需要3列才能进行基本验证
            return True, "数据中缺少必要列，无法检查现金流量表公式"
        
        # 创建副本，避免修改原始数据
        check_data = data.copy()
        
        # 如果有期初和期末现金余额，检查期末余额不应为负
        if 'c_cash_equ_end_period' in check_data.columns:
            violations = check_data[check_data['c_cash_equ_end_period'] < 0].dropna()
            
            violation_count = len(violations)
            if violation_count > 0:
                return False, f"发现 {violation_count} 条记录的期末现金余额为负，可能数据有误"
        
        # 如果有期初和期末现金余额，以及三大现金流量，进行完整公式验证
        if all(col in check_data.columns for col in required_columns):
            # 计算理论期末现金
            check_data.loc[:, 'expected_end_cash'] = check_data['c_cash_equ_beg_period'] + check_data['n_cashflow_act'] + \
                                         check_data['n_cashflow_inv_act'] + check_data['n_cash_flows_fnc_act']
            
            # 计算误差
            check_data.loc[:, 'cash_error'] = abs(check_data['c_cash_equ_end_period'] - check_data['expected_end_cash'])
            check_data.loc[:, 'cash_error_pct'] = check_data['cash_error'] / check_data['c_cash_equ_end_period'] * 100
            
            # 允许5%的误差（考虑汇率变动等因素）
            violations = check_data[check_data['cash_error_pct'] > 5.0].dropna()
            
            violation_count = len(violations)
            if violation_count > 0:
                return False, f"发现 {violation_count} 条记录不满足现金流量表公式（误差>5%）"
        
        return True, "所有记录都满足现金流量表基本规则"
    
    def _check_yoy_change(self, data: pd.DataFrame, max_change: float = 1000.0) -> Tuple[bool, str]:
        """
        检查关键财务指标的同比变化是否异常
        
        Args:
            data: 财务数据
            max_change: 最大允许的同比变化百分比
            
        Returns:
            Tuple[bool, str]: (是否通过, 错误信息)
        """
        required_columns = ['ts_code', 'end_date']
        if not all(col in data.columns for col in required_columns):
            return True, "数据中缺少必要列，无法检查同比变化"
        
        # 确保end_date是日期类型
        if not pd.api.types.is_datetime64_dtype(data['end_date']):
            try:
                data['end_date'] = pd.to_datetime(data['end_date'])
            except:
                return False, "财务报告日期转换失败，无法检查同比变化"
        
        # 获取所有数值型列（排除日期、字符串等）
        numeric_columns = []
        for col in data.columns:
            if col not in ['ts_code', 'end_date', 'ann_date', 'report_type'] and pd.api.types.is_numeric_dtype(data[col]):
                numeric_columns.append(col)
                
        if not numeric_columns:
            return True, "数据中没有数值型列，无法检查同比变化"
        
        # 检查同比变化
        violations = []
        
        for ts_code in data['ts_code'].unique():
            stock_data = data[data['ts_code'] == ts_code].sort_values('end_date')
            
            # 计算每个指标的同比变化
            for col in numeric_columns:
                stock_data[f'{col}_yoy'] = stock_data[col].pct_change(periods=4) * 100  # 假设是季度数据，同比为4期
                
                # 检查变化是否超过阈值
                abnormal = stock_data[abs(stock_data[f'{col}_yoy']) > max_change].dropna()
                for _, row in abnormal.iterrows():
                    violations.append({
                        'ts_code': ts_code,
                        'end_date': row['end_date'],
                        'indicator': col,
                        'yoy_change': row[f'{col}_yoy']
                    })
        
        if violations:
            return False, f"发现 {len(violations)} 条同比变化异常的指标，超过 {max_change}%"
        
        return True, "所有同比变化都在合理范围内"
    
    def _check_outlier(self, data: pd.DataFrame, method: str = 'zscore', threshold: float = 3.0) -> Tuple[bool, str]:
        """
        检查财务数据中的异常值
        
        Args:
            data: 财务数据
            method: 异常值检测方法，'zscore', 'iqr'等
            threshold: 异常值阈值
            
        Returns:
            Tuple[bool, str]: (是否通过, 错误信息)
        """
        # 获取所有数值型列（排除日期、字符串等）
        numeric_columns = []
        for col in data.columns:
            if col not in ['ts_code', 'end_date', 'ann_date', 'report_type'] and pd.api.types.is_numeric_dtype(data[col]):
                numeric_columns.append(col)
                
        if not numeric_columns:
            return True, "数据中没有数值型列，无法检查异常值"
        
        # 检查异常值
        outliers = []
        
        if method == 'zscore':
            # Z-score方法
            for col in numeric_columns:
                # 计算Z-score
                mean = data[col].mean()
                std = data[col].std()
                if std == 0:
                    continue
                
                z_scores = abs((data[col] - mean) / std)
                
                # 找出异常值
                outlier_rows = data[z_scores > threshold].dropna()
                for _, row in outlier_rows.iterrows():
                    outliers.append({
                        'ts_code': row['ts_code'] if 'ts_code' in row else None,
                        'end_date': row['end_date'] if 'end_date' in row else None,
                        'indicator': col,
                        'value': row[col],
                        'zscore': (row[col] - mean) / std
                    })
                    
        elif method == 'iqr':
            # IQR方法
            for col in numeric_columns:
                # 计算IQR
                q1 = data[col].quantile(0.25)
                q3 = data[col].quantile(0.75)
                iqr = q3 - q1
                if iqr == 0:
                    continue
                
                # 找出异常值
                lower_bound = q1 - threshold * iqr
                upper_bound = q3 + threshold * iqr
                outlier_rows = data[(data[col] < lower_bound) | (data[col] > upper_bound)].dropna()
                
                for _, row in outlier_rows.iterrows():
                    outliers.append({
                        'ts_code': row['ts_code'] if 'ts_code' in row else None,
                        'end_date': row['end_date'] if 'end_date' in row else None,
                        'indicator': col,
                        'value': row[col]
                    })
        
        if outliers:
            return False, f"发现 {len(outliers)} 个异常值"
        
        return True, "没有发现异常值"

    def clean(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        清洗财务数据
        
        Args:
            data: 要清洗的财务数据
            **kwargs: 清洗参数
            
        Returns:
            pd.DataFrame: 清洗后的数据
            
        Raises:
            CleaningError: 清洗错误
        """
        # 财务数据验证器主要负责验证，清洗功能较为简单
        # 可能的清洗内容：移除重复数据、处理异常值
        if data is None or data.empty:
            return data
        
        result = data.copy()
        
        try:
            # 移除重复数据
            if self.config.get('check_duplicate', True):
                duplicate_keys = ['ts_code', 'end_date', 'report_type']
                if all(col in result.columns for col in duplicate_keys):
                    result = result.drop_duplicates(subset=duplicate_keys, keep='first')
            
            # 处理异常值
            if self.config.get('check_outlier', True):
                method = self.config.get('outlier_method', 'zscore')
                threshold = self.config.get('outlier_threshold', 3.0)
                
                # 获取数值列
                numeric_columns = []
                for col in result.columns:
                    if col not in ['ts_code', 'end_date', 'ann_date', 'report_type'] and pd.api.types.is_numeric_dtype(result[col]):
                        numeric_columns.append(col)
                
                # 处理异常值
                if method == 'zscore':
                    for col in numeric_columns:
                        mean = result[col].mean()
                        std = result[col].std()
                        if std == 0:
                            continue
                        
                        # 计算Z-score
                        z_scores = abs((result[col] - mean) / std)
                        
                        # 将异常值设为NaN
                        result.loc[z_scores > threshold, col] = np.nan
                
                elif method == 'iqr':
                    for col in numeric_columns:
                        q1 = result[col].quantile(0.25)
                        q3 = result[col].quantile(0.75)
                        iqr = q3 - q1
                        if iqr == 0:
                            continue
                        
                        # 计算边界
                        lower_bound = q1 - threshold * iqr
                        upper_bound = q3 + threshold * iqr
                        
                        # 将异常值设为NaN
                        result.loc[(result[col] < lower_bound) | (result[col] > upper_bound), col] = np.nan
            
            return result
            
        except Exception as e:
            self.logger.error(f"财务数据清洗失败: {str(e)}")
            raise CleaningError(f"财务数据清洗失败: {str(e)}")
    
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        转换财务数据
        
        Args:
            data: 要转换的财务数据
            **kwargs: 转换参数
            
        Returns:
            pd.DataFrame: 转换后的数据
            
        Raises:
            TransformationError: 转换错误
        """
        # 财务数据验证器主要负责验证，转换功能有限
        # 可能的转换内容：标准化字段名、转换数据类型
        if data is None or data.empty:
            return data
        
        result = data.copy()
        
        try:
            # 确保日期列为日期类型
            date_columns = ['ann_date', 'end_date']
            for col in date_columns:
                if col in result.columns and not pd.api.types.is_datetime64_dtype(result[col]):
                    try:
                        result[col] = pd.to_datetime(result[col])
                    except:
                        self.logger.warning(f"无法将列 {col} 转换为日期类型")
            
            # 确保代码列为字符串类型
            if 'ts_code' in result.columns and not pd.api.types.is_string_dtype(result[col]):
                result['ts_code'] = result['ts_code'].astype(str)
            
            # 确保报表类型为整数
            if 'report_type' in result.columns and pd.api.types.is_numeric_dtype(result['report_type']):
                result['report_type'] = result['report_type'].astype(int)
            
            return result
            
        except Exception as e:
            self.logger.error(f"财务数据转换失败: {str(e)}")
            raise TransformationError(f"财务数据转换失败: {str(e)}") 