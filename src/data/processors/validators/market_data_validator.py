"""
市场数据验证器
- 专用于验证股票、指数等市场数据
- 提供针对市场数据的验证规则
- 支持A股特有的验证规则
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union, Optional, Tuple, Set
import logging
from datetime import datetime, timedelta

from .validator_interface import BaseValidator

class MarketDataValidator(BaseValidator):
    """
    市场数据验证器，验证股票、指数等市场数据
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化市场数据验证器
        
        Args:
            config: 验证器配置，除了基础验证器的配置外，还可以包含：
                - price_limits: 价格检查限制，包含min_value和max_value
                - volume_limits: 成交量检查限制，包含min_value和max_value
                - check_price_sequence: 是否检查价格序列 (high >= open, high >= close, low <= open, low <= close)
                - check_date_continuity: 是否检查日期连续性
                - max_price_change: 单日最大价格变动百分比
                - market: 市场类型，'A'(A股市场)、'HK'(港股市场)、'US'(美股市场)
        """
        # 默认市场数据配置
        market_data_config = {
            'required_columns': ['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'volume'],
            'column_types': {
                'ts_code': 'string',
                'trade_date': 'datetime',
                'open': 'numeric',
                'high': 'numeric',
                'low': 'numeric',
                'close': 'numeric',
                'volume': 'numeric'
            },
            'price_limits': {
                'min_value': 0,
                'max_value': 10000
            },
            'volume_limits': {
                'min_value': 0,
                'max_value': None  # 无上限
            },
            'check_price_sequence': True,
            'check_date_continuity': True,
            'max_price_change': 30.0,  # 默认30%
            'market': 'A'  # 默认A股市场
        }
        
        # 合并传入的配置
        if config:
            # 合并嵌套字典
            for key, value in config.items():
                if isinstance(value, dict) and key in market_data_config and isinstance(market_data_config[key], dict):
                    market_data_config[key].update(value)
                else:
                    market_data_config[key] = value
        
        # 调用父类初始化
        super().__init__(market_data_config)
        
        # 添加市场数据特有的验证规则
        self._add_market_data_rules()
    
    def _add_market_data_rules(self):
        """
        添加市场数据特有的验证规则
        """
        # 检查价格范围
        if 'price_limits' in self.config:
            self.add_rule(
                'check_price_range',
                self._check_price_range,
                **self.config['price_limits']
            )
        
        # 检查成交量范围
        if 'volume_limits' in self.config:
            self.add_rule(
                'check_volume_range',
                self._check_volume_range,
                **self.config['volume_limits']
            )
        
        # 检查价格序列关系
        if self.config.get('check_price_sequence', True):
            self.add_rule(
                'check_price_sequence',
                self._check_price_sequence
            )
        
        # 检查日期连续性
        if self.config.get('check_date_continuity', True):
            self.add_rule(
                'check_date_continuity',
                self._check_date_continuity
            )
        
        # 检查价格异常变动
        if 'max_price_change' in self.config:
            self.add_rule(
                'check_price_change',
                self._check_price_change,
                max_change=self.config['max_price_change']
            )
        
        # 根据市场类型添加特殊规则
        market = self.config.get('market', 'A')
        if market == 'A':
            # A股特有规则
            self.add_rule(
                'check_a_stock_price_limit',
                self._check_a_stock_price_limit
            )
    
    def _check_price_range(self, data: pd.DataFrame, min_value: float = 0, max_value: float = None) -> Tuple[bool, str]:
        """
        检查价格是否在合理范围内
        
        Args:
            data: 市场数据
            min_value: 最小价格
            max_value: 最大价格，None表示无上限
            
        Returns:
            Tuple[bool, str]: (是否通过, 错误信息)
        """
        price_columns = ['open', 'high', 'low', 'close']
        price_columns = [col for col in price_columns if col in data.columns]
        
        # 检查最小值
        min_violations = pd.DataFrame()
        for col in price_columns:
            min_violations = pd.concat([min_violations, data[data[col] < min_value]])
        
        # 检查最大值（如果有设置）
        max_violations = pd.DataFrame()
        if max_value is not None:
            for col in price_columns:
                max_violations = pd.concat([max_violations, data[data[col] > max_value]])
        
        min_violation_count = len(min_violations.drop_duplicates())
        max_violation_count = len(max_violations.drop_duplicates())
        
        if min_violation_count > 0 or max_violation_count > 0:
            return False, f"发现 {min_violation_count} 条记录价格小于 {min_value}，{max_violation_count} 条记录价格大于 {max_value if max_value else '∞'}"
        
        return True, "所有价格都在合理范围内"
    
    def _check_volume_range(self, data: pd.DataFrame, min_value: float = 0, max_value: float = None) -> Tuple[bool, str]:
        """
        检查成交量是否在合理范围内
        
        Args:
            data: 市场数据
            min_value: 最小成交量
            max_value: 最大成交量，None表示无上限
            
        Returns:
            Tuple[bool, str]: (是否通过, 错误信息)
        """
        if 'volume' not in data.columns:
            return True, "数据中没有成交量列"
        
        # 检查最小值
        min_violations = data[data['volume'] < min_value]
        
        # 检查最大值（如果有设置）
        max_violations = pd.DataFrame()
        if max_value is not None:
            max_violations = data[data['volume'] > max_value]
        
        min_violation_count = len(min_violations)
        max_violation_count = len(max_violations)
        
        if min_violation_count > 0 or max_violation_count > 0:
            return False, f"发现 {min_violation_count} 条记录成交量小于 {min_value}，{max_violation_count} 条记录成交量大于 {max_value if max_value else '∞'}"
        
        return True, "所有成交量都在合理范围内"
    
    def _check_price_sequence(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        检查价格序列关系是否合理（high >= open, high >= close, low <= open, low <= close）
        
        Args:
            data: 市场数据
            
        Returns:
            Tuple[bool, str]: (是否通过, 错误信息)
        """
        required_columns = ['open', 'high', 'low', 'close']
        if not all(col in data.columns for col in required_columns):
            return True, "数据中缺少价格列，无法检查价格序列关系"
        
        # 检查价格序列关系
        violations = data[
            (data['high'] < data['open']) | 
            (data['high'] < data['close']) | 
            (data['low'] > data['open']) | 
            (data['low'] > data['close'])
        ]
        
        violation_count = len(violations)
        if violation_count > 0:
            return False, f"发现 {violation_count} 条记录的价格序列关系不合理"
        
        return True, "所有价格序列关系都合理"
    
    def _check_date_continuity(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        检查交易日期是否连续（考虑非交易日）
        
        Args:
            data: 市场数据
            
        Returns:
            Tuple[bool, str]: (是否通过, 错误信息)
        """
        if 'trade_date' not in data.columns or 'ts_code' not in data.columns:
            return True, "数据中缺少交易日期或股票代码列，无法检查日期连续性"
        
        # 确保trade_date是日期类型
        if not pd.api.types.is_datetime64_dtype(data['trade_date']):
            try:
                data['trade_date'] = pd.to_datetime(data['trade_date'])
            except:
                return False, "交易日期转换失败，无法检查日期连续性"
        
        # 按股票代码分组检查日期连续性
        date_gaps = []
        for code, group in data.groupby('ts_code'):
            # 日期排序
            sorted_dates = group['trade_date'].sort_values().reset_index(drop=True)
            if len(sorted_dates) <= 1:
                continue
            
            # 计算相邻日期的间隔（以天为单位）
            date_diffs = sorted_dates.diff().dt.days.dropna()
            
            # 检查间隔是否异常（大于5个自然日）
            abnormal_gaps = date_diffs[date_diffs > 5]
            if not abnormal_gaps.empty:
                for idx in abnormal_gaps.index:
                    date_gaps.append({
                        'ts_code': code,
                        'start_date': sorted_dates[idx-1].strftime('%Y-%m-%d'),
                        'end_date': sorted_dates[idx].strftime('%Y-%m-%d'),
                        'gap_days': date_diffs[idx]
                    })
        
        if date_gaps:
            return False, f"发现 {len(date_gaps)} 处日期异常间隔，可能缺少交易日数据"
        
        return True, "所有交易日期连续性正常"
    
    def _check_price_change(self, data: pd.DataFrame, max_change: float = 30.0) -> Tuple[bool, str]:
        """
        检查价格变动是否异常
        
        Args:
            data: 市场数据
            max_change: 最大允许的价格变动百分比
            
        Returns:
            Tuple[bool, str]: (是否通过, 错误信息)
        """
        required_columns = ['close', 'pre_close', 'ts_code']
        if not all(col in data.columns for col in required_columns):
            # 尝试计算pre_close
            if 'close' in data.columns and 'ts_code' in data.columns and len(data) > 1:
                data = data.sort_values(['ts_code', 'trade_date'])
                data['pre_close'] = data.groupby('ts_code')['close'].shift(1)
            else:
                return True, "数据中缺少必要列，无法检查价格变动"
        
        # 计算价格变动百分比
        data['price_change_pct'] = (data['close'] / data['pre_close'] - 1) * 100
        
        # 检查异常变动
        violations = data[abs(data['price_change_pct']) > max_change].dropna()
        
        violation_count = len(violations)
        if violation_count > 0:
            return False, f"发现 {violation_count} 条记录的价格变动超过 {max_change}%"
        
        return True, "所有价格变动都在合理范围内"
    
    def _check_a_stock_price_limit(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        检查A股涨跌停限制
        
        Args:
            data: 市场数据
            
        Returns:
            Tuple[bool, str]: (是否通过, 错误信息)
        """
        required_columns = ['close', 'pre_close', 'ts_code']
        if not all(col in data.columns for col in required_columns):
            return True, "数据中缺少必要列，无法检查涨跌停限制"
        
        # 计算价格变动百分比
        if 'price_change_pct' not in data.columns:
            data['price_change_pct'] = (data['close'] / data['pre_close'] - 1) * 100
        
        # A股涨跌停规则：普通股票 ±10%，ST股票 ±5%
        violations = []
        
        # 检查普通股票
        normal_stocks = data[~data['ts_code'].str.contains('ST', case=False, na=False)]
        normal_violations = normal_stocks[abs(normal_stocks['price_change_pct']) > 10.2]  # 允许小误差
        
        # 检查ST股票
        st_stocks = data[data['ts_code'].str.contains('ST', case=False, na=False)]
        st_violations = st_stocks[abs(st_stocks['price_change_pct']) > 5.2]  # 允许小误差
        
        violations = pd.concat([normal_violations, st_violations])
        
        violation_count = len(violations)
        if violation_count > 0:
            return False, f"发现 {violation_count} 条记录违反A股涨跌停限制"
        
        return True, "所有记录都符合A股涨跌停限制" 