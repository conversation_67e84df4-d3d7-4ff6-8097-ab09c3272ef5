"""
数据验证器接口
- 定义数据验证的基本方法和规则
- 提供验证异常处理
- 支持验证规则组合
"""

from abc import ABC, abstractmethod
import pandas as pd
from typing import Dict, List, Any, Union, Optional, Callable, Tuple, Set
import logging

from src.data.processors.processor_interface import ProcessorInterface, ValidationError

class ValidatorInterface(ProcessorInterface, ABC):
    """
    数据验证器接口，定义数据验证的基本方法
    """
    
    @abstractmethod
    def validate(self, data: pd.DataFrame, **kwargs) -> Tuple[bool, Dict[str, Any]]:
        """
        验证数据的有效性
        
        Args:
            data: 要验证的数据
            **kwargs: 额外参数
            
        Returns:
            Tuple[bool, Dict[str, Any]]: (验证是否通过, 验证结果详情)
            
        Raises:
            ValidationError: 验证错误
        """
        pass
    
    @abstractmethod
    def add_rule(self, rule_name: str, rule_func: Callable, **kwargs) -> None:
        """
        添加验证规则
        
        Args:
            rule_name: 规则名称
            rule_func: 规则函数，接受DataFrame和额外参数，返回(bool, str)表示(是否通过, 错误信息)
            **kwargs: 规则函数的额外参数
            
        Returns:
            None
        """
        pass
    
    @abstractmethod
    def remove_rule(self, rule_name: str) -> bool:
        """
        移除验证规则
        
        Args:
            rule_name: 规则名称
            
        Returns:
            bool: 是否成功移除
        """
        pass

class BaseValidator(ValidatorInterface):
    """
    基础验证器，提供通用的数据验证功能
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化基础验证器
        
        Args:
            config: 验证器配置
                - required_columns: 必须包含的列
                - column_types: 列的数据类型
                - error_action: 错误处理方式，'raise'|'log'|'return'
                - rules: 预定义规则列表
        """
        self.rules = {}  # 验证规则字典
        self.results = {}  # 验证结果
        self.errors = []  # 错误信息列表
        
        # 默认配置
        self.config = {
            'required_columns': [],
            'column_types': {},
            'error_action': 'raise',  # 'raise', 'log', 'return'
            'rules': {}
        }
        
        # 更新配置
        if config:
            self.config.update(config)
        
        # 设置日志
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化预定义规则
        self._init_predefined_rules()
    
    def _init_predefined_rules(self) -> None:
        """
        初始化预定义的验证规则
        """
        # 检查必须列
        if self.config['required_columns']:
            self.add_rule(
                'check_required_columns',
                self._check_required_columns,
                columns=self.config['required_columns']
            )
        
        # 检查列类型
        if self.config['column_types']:
            self.add_rule(
                'check_column_types',
                self._check_column_types,
                column_types=self.config['column_types']
            )
    
    def validate(self, data: pd.DataFrame, **kwargs) -> Tuple[bool, Dict[str, Any]]:
        """
        验证数据的有效性
        
        Args:
            data: 要验证的数据
            **kwargs: 额外参数
                - rules: 指定要运行的规则，如果为空则运行所有规则
                - error_action: 覆盖配置中的error_action
                
        Returns:
            Tuple[bool, Dict[str, Any]]: (验证是否通过, 验证结果详情)
            
        Raises:
            ValidationError: 验证错误（如果error_action='raise'）
        """
        if data is None or data.empty:
            if self.config['error_action'] == 'raise':
                raise ValidationError("数据为空")
            elif self.config['error_action'] == 'log':
                self.logger.error("数据为空")
            return False, {"error": "数据为空"}
        
        # 重置结果和错误
        self.results = {}
        self.errors = []
        
        # 获取要运行的规则
        rules_to_run = kwargs.get('rules', list(self.rules.keys()))
        
        # 运行每个规则
        all_passed = True
        for rule_name in rules_to_run:
            if rule_name in self.rules:
                rule = self.rules[rule_name]
                try:
                    passed, message = rule['func'](data, **rule['params'])
                    self.results[rule_name] = {
                        'passed': passed,
                        'message': message
                    }
                    if not passed:
                        all_passed = False
                        self.errors.append({
                            'rule': rule_name,
                            'message': message
                        })
                except Exception as e:
                    all_passed = False
                    error_msg = f"规则 {rule_name} 执行错误: {str(e)}"
                    self.errors.append({
                        'rule': rule_name,
                        'message': error_msg
                    })
                    self.results[rule_name] = {
                        'passed': False,
                        'message': error_msg
                    }
        
        # 处理错误
        error_action = kwargs.get('error_action', self.config['error_action'])
        if not all_passed:
            error_message = f"数据验证失败: {self.errors}"
            if error_action == 'raise':
                raise ValidationError(error_message)
            elif error_action == 'log':
                self.logger.error(error_message)
        
        return all_passed, {
            'passed': all_passed,
            'results': self.results,
            'errors': self.errors
        }
    
    def add_rule(self, rule_name: str, rule_func: Callable, **kwargs) -> None:
        """
        添加验证规则
        
        Args:
            rule_name: 规则名称
            rule_func: 规则函数，接受DataFrame和额外参数，返回(bool, str)表示(是否通过, 错误信息)
            **kwargs: 规则函数的额外参数
            
        Returns:
            None
        """
        self.rules[rule_name] = {
            'func': rule_func,
            'params': kwargs
        }
    
    def remove_rule(self, rule_name: str) -> bool:
        """
        移除验证规则
        
        Args:
            rule_name: 规则名称
            
        Returns:
            bool: 是否成功移除
        """
        if rule_name in self.rules:
            del self.rules[rule_name]
            return True
        return False
    
    def get_rules(self) -> Dict[str, Dict]:
        """
        获取所有验证规则
        
        Returns:
            Dict[str, Dict]: 规则字典
        """
        return self.rules
    
    def get_results(self) -> Dict[str, Any]:
        """
        获取最近一次验证结果
        
        Returns:
            Dict[str, Any]: 验证结果
        """
        return {
            'passed': len(self.errors) == 0,
            'results': self.results,
            'errors': self.errors
        }
    
    def process(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        实现ProcessorInterface的process方法，验证数据并返回
        
        Args:
            data: 要处理的数据
            **kwargs: 额外参数
            
        Returns:
            pd.DataFrame: 验证后的数据（不做修改）
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        passed, results = self.validate(data, **kwargs)
        return data
    
    def _check_required_columns(self, data: pd.DataFrame, columns: List[str]) -> Tuple[bool, str]:
        """
        检查数据是否包含所有必须的列
        
        Args:
            data: 要验证的数据
            columns: 必须包含的列名列表
            
        Returns:
            Tuple[bool, str]: (是否通过, 错误信息)
        """
        missing_columns = [col for col in columns if col not in data.columns]
        if missing_columns:
            return False, f"缺少必须的列: {missing_columns}"
        return True, "所有必须的列都存在"
    
    def _check_column_types(self, data: pd.DataFrame, column_types: Dict[str, str]) -> Tuple[bool, str]:
        """
        检查数据列的类型是否符合要求
        
        Args:
            data: 要验证的数据
            column_types: 列名到类型的映射，类型可以是'numeric', 'string', 'datetime', 'boolean'
            
        Returns:
            Tuple[bool, str]: (是否通过, 错误信息)
        """
        type_errors = []
        
        for col, expected_type in column_types.items():
            if col not in data.columns:
                continue  # 跳过不存在的列
                
            if expected_type == 'numeric':
                if not pd.api.types.is_numeric_dtype(data[col]):
                    type_errors.append(f"列 '{col}' 应为数值类型，但实际为 {data[col].dtype}")
            
            elif expected_type == 'string':
                if not pd.api.types.is_string_dtype(data[col]) and not pd.api.types.is_object_dtype(data[col]):
                    type_errors.append(f"列 '{col}' 应为字符串类型，但实际为 {data[col].dtype}")
            
            elif expected_type == 'datetime':
                if not pd.api.types.is_datetime64_dtype(data[col]):
                    type_errors.append(f"列 '{col}' 应为日期时间类型，但实际为 {data[col].dtype}")
            
            elif expected_type == 'boolean':
                if not pd.api.types.is_bool_dtype(data[col]):
                    type_errors.append(f"列 '{col}' 应为布尔类型，但实际为 {data[col].dtype}")
        
        if type_errors:
            return False, f"列类型错误: {'; '.join(type_errors)}"
        return True, "所有列类型正确" 