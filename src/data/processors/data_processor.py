"""
基本数据处理器实现
这个模块提供了一个简单的数据处理器实现，用于处理数据获取后的数据处理流程
"""
import pandas as pd
import numpy as np
from typing import Any, Dict, List, Optional, Union
import logging

from src.data.processors.processor_interface import ProcessorInterface

# 配置日志记录器
logger = logging.getLogger(__name__)

class DataProcessor(ProcessorInterface):
    """
    基本数据处理器实现
    
    实现了ProcessorInterface接口中定义的数据处理方法
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化数据处理器
        
        Args:
            config: 处理器配置
        """
        super().__init__(config)
        # 设置默认配置
        self.config = config or {
            "clean_missing_values": True,
            "handle_outliers": False,
            "normalize_data": False,
            "remove_duplicates": True,
            "add_indicators": False
        }
    
    def process(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        处理数据的核心方法
        
        Args:
            data: 要处理的数据
            **kwargs: 额外参数
            
        Returns:
            pd.DataFrame: 处理后的数据
        """
        if not isinstance(data, pd.DataFrame):
            logger.warning("输入数据不是DataFrame，无法处理")
            return data
            
        if data.empty:
            logger.warning("输入数据为空DataFrame")
            return data
            
        # 记录处理前的统计信息
        if self.config.get("log_stats", False):
            self.log_stats(data, "处理前")
            
        # 执行数据清洗
        cleaned_data = self.clean(data, **kwargs)
        
        # 执行数据转换
        transformed_data = self.transform(cleaned_data, **kwargs)
        
        # 记录处理后的统计信息
        if self.config.get("log_stats", False):
            self.log_stats(transformed_data, "处理后")
            
        return transformed_data
    
    def clean(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        清洗数据
        
        Args:
            data: 要清洗的数据
            **kwargs: 清洗参数
            
        Returns:
            pd.DataFrame: 清洗后的数据
        """
        cleaned_data = data.copy()
        
        # 处理缺失值
        if self.config.get("clean_missing_values", True):
            missing_value_strategies = kwargs.get("missing_value_strategies") or self.config.get("missing_value_strategies")
            if missing_value_strategies:
                cleaned_data = self.handle_missing_values(cleaned_data, missing_value_strategies)
        
        # 移除重复行
        if self.config.get("remove_duplicates", True):
            subset = kwargs.get("duplicate_subset") or self.config.get("duplicate_subset")
            keep = kwargs.get("duplicate_keep", "first") or self.config.get("duplicate_keep", "first")
            cleaned_data = cleaned_data.drop_duplicates(subset=subset, keep=keep)
            
        # 处理异常值
        if self.config.get("handle_outliers", False):
            outlier_method = kwargs.get("outlier_method") or self.config.get("outlier_method", "zscore")
            outlier_threshold = kwargs.get("outlier_threshold") or self.config.get("outlier_threshold", 3.0)
            outlier_columns = kwargs.get("outlier_columns") or self.config.get("outlier_columns")
            cleaned_data = self.handle_outliers(cleaned_data, method=outlier_method, 
                                           threshold=outlier_threshold, columns=outlier_columns)
            
        return cleaned_data
    
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        转换数据
        
        Args:
            data: 要转换的数据
            **kwargs: 转换参数
            
        Returns:
            pd.DataFrame: 转换后的数据
        """
        transformed_data = data.copy()
        
        # 数据归一化
        if self.config.get("normalize_data", False):
            normalize_method = kwargs.get("normalize_method") or self.config.get("normalize_method", "zscore")
            normalize_columns = kwargs.get("normalize_columns") or self.config.get("normalize_columns")
            transformed_data = self.normalize(transformed_data, method=normalize_method, columns=normalize_columns)
            
        # 类型转换
        type_mapping = kwargs.get("type_mapping") or self.config.get("type_mapping")
        if type_mapping:
            transformed_data = self.convert_types(transformed_data, type_mapping)
            
        # 添加额外指标
        if self.config.get("add_indicators", False):
            transformed_data = self._add_indicators(transformed_data, **kwargs)
            
        return transformed_data
    
    def _add_indicators(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        添加额外指标
        
        Args:
            data: 要处理的数据
            **kwargs: 额外参数
            
        Returns:
            pd.DataFrame: 添加指标后的数据
        """
        result = data.copy()
        
        # 根据配置添加指标
        indicators = kwargs.get("indicators") or self.config.get("indicators", [])
        
        for indicator in indicators:
            indicator_type = indicator.get("type")
            if indicator_type == "mov_avg":
                # 添加移动平均
                column = indicator.get("column")
                window = indicator.get("window", 5)
                name = indicator.get("name", f"{column}_ma{window}")
                if column in result.columns:
                    result[name] = result[column].rolling(window=window).mean()
            elif indicator_type == "pct_change":
                # 添加百分比变化
                column = indicator.get("column")
                periods = indicator.get("periods", 1)
                name = indicator.get("name", f"{column}_pct{periods}")
                if column in result.columns:
                    result[name] = result[column].pct_change(periods=periods)
            elif indicator_type == "rolling_std":
                # 添加滚动标准差
                column = indicator.get("column")
                window = indicator.get("window", 5)
                name = indicator.get("name", f"{column}_std{window}")
                if column in result.columns:
                    result[name] = result[column].rolling(window=window).std()
                    
        return result

# 为了方便DataFetcher测试，提供一个别名DataProcessorInterface
DataProcessorInterface = ProcessorInterface 