"""
AkShare数据适配器：连接AkShare数据源获取A股数据
- 封装AkShare API接口调用
- 处理数据格式转换
- 实现数据缓存和失败重试
- 提供数据过滤和预处理功能
"""

import os
import time
import pandas as pd
import numpy as np
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Union, Any, Tuple
# {{ AURA-X: Add - 导入统一错误处理器，消除重复代码. Approval: 寸止(ID:重构阶段3). }}
from src.utils.error_handling import DataFetchErrorHandler, retry_on_error, RetryStrategy

from .data_source_interface import DataSourceInterface, ConnectionError, DataFetchError, AuthenticationError, RateLimitError
from .data_source_interface import DataSourceException

class AkShareError(DataFetchError):
    """AkShare 数据获取错误"""
    pass

class AKShareAdapter(DataSourceInterface):
    """
    AkShare数据适配器类，实现AkShare的数据获取和处理
    """
    
    def __init__(self, timeout: int = 10, max_retry: int = 3, cache_dir: str = None, **kwargs):
        """
        初始化AkShare适配器
        
        参数：
            timeout: 请求超时时间（秒）
            max_retry: 最大重试次数
            cache_dir: 缓存目录，若为None则使用"~/.cache/quantification/akshare"
            **kwargs: 其他参数
        """
        # 配置日志记录器
        self.logger = logging.getLogger('AKShareAdapter')
        
        self.timeout = timeout
        self.max_retry = max_retry
        self.api = None
        
        # 设置缓存目录
        if cache_dir is None:
            home_dir = os.path.expanduser("~")
            self.cache_dir = os.path.join(home_dir, ".cache", "quantification", "akshare")
        else:
            self.cache_dir = cache_dir
            
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # 初始化API状态
        self.api_ready = False
    
    def connect(self) -> bool:
        """
        连接到AkShare数据源
        
        返回：
            bool: 是否连接成功
            
        异常：
            ConnectionError: 连接失败时抛出
        """
        try:
            import akshare as ak
            self.api = ak
            self.api_ready = True
            self.logger.info("AkShare库连接成功")
            return True
        except ImportError:
            self.logger.error("未安装akshare模块，请使用pip install akshare安装")
            raise ConnectionError("未安装akshare模块，请使用pip install akshare安装")
        except Exception as e:
            self.logger.error(f"AkShare库连接失败: {e}")
            raise ConnectionError(f"连接AkShare失败: {str(e)}")
    
    def disconnect(self) -> bool:
        """
        断开AkShare数据源连接
        
        返回：
            bool: 是否断开成功
        """
        self.api = None
        self.api_ready = False
        return True
    
    def is_connected(self) -> bool:
        """
        检查数据源连接状态
        
        返回：
            bool: 是否已连接
        """
        return self.api_ready and self.api is not None
    
    def get_data(self, api_name=None, **kwargs):
        """
        获取AkShare数据，带重试机制
        
        参数:
            api_name: API名称，如'stock_zh_a_hist'、'tool_trade_date_hist_sina'等
            **kwargs: API调用的具体参数
            
        返回:
            pd.DataFrame: 查询结果数据框
        """
        # 检查连接状态
        if not self.is_connected():
            if not self.connect():
                raise DataSourceException("未连接到数据源")
        
        # {{ AURA-X: Modify - 使用统一错误处理器，消除重复的重试代码. Approval: 寸止(ID:重构阶段3). }}
        # 创建数据获取错误处理器
        error_handler = DataFetchErrorHandler(
            max_retries=self.max_retry,
            base_delay=2.0,  # 基础延迟2秒
            strategy=RetryStrategy.LINEAR,  # 使用线性增长策略
            logger=self.logger
        )

        # 使用错误处理器执行API调用
        return error_handler.execute_with_retry(self._execute_akshare_api, api_name, **kwargs)

    def _execute_akshare_api(self, api_name: str, **kwargs) -> Any:
        """
        执行单次AkShare API调用（内部方法）

        参数：
            api_name: API名称
            **kwargs: API参数

        返回：
            API返回的数据
        """
        # 调用AkShare API
        self.logger.debug(f"调用API: {api_name}, 参数: {kwargs}")

        # 检查API方法是否存在
        if not hasattr(self.api, api_name):
            raise ValueError(f"无效的API名称: {api_name}")

        # 获取API方法
        api_method = getattr(self.api, api_name)

        # 调用API方法
        data = api_method(**kwargs)

        return data
    
    def get_trade_calendar(self, exchange: str = 'SSE', start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        获取交易日历
        
        参数:
            exchange: 交易所代码，默认为'SSE'（上海证券交易所）
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
            
        返回:
            pd.DataFrame: 交易日历数据，包含字段:
                - cal_date: 日历日期
                - is_open: 是否交易 0休市 1交易
        """
        try:
            # 获取历史交易日
            df = self.get_data('tool_trade_date_hist_sina')
            
            # 处理结果
            if isinstance(df, pd.DataFrame) and not df.empty:
                # 转换列名和格式
                df = df.rename(columns={'trade_date': 'cal_date'})
                # 添加is_open列，全部为1（交易日）
                df['is_open'] = 1
                
                # 过滤日期范围
                if start_date:
                    start_date = self._normalize_date(start_date)
                    df = df[df['cal_date'] >= start_date]
                if end_date:
                    end_date = self._normalize_date(end_date)
                    df = df[df['cal_date'] <= end_date]
                
                # 获取所有日期并标记非交易日
                if start_date and end_date:
                    # 创建从开始到结束的日期范围
                    all_dates = pd.date_range(start=start_date, end=end_date, freq='D')
                    all_dates_df = pd.DataFrame({'cal_date': all_dates.strftime('%Y%m%d')})
                    
                    # 标记交易日
                    merged_df = pd.merge(all_dates_df, df, on='cal_date', how='left')
                    merged_df['is_open'] = merged_df['is_open'].fillna(0).astype(int)
                    
                    return merged_df
                
                return df
            else:
                self.logger.warning("获取交易日历返回空数据")
                return pd.DataFrame(columns=['cal_date', 'is_open'])
                
        except Exception as e:
            self.logger.error(f"获取交易日历失败: {e}")
            raise DataFetchError(f"获取交易日历失败: {e}")
    
    def get_stock_basic(self) -> pd.DataFrame:
        """
        获取股票基本信息
        
        返回:
            pd.DataFrame: 股票基本信息
        """
        try:
            # 使用AkShare的stock_info_a_code_name函数获取A股代码和名称
            df = self.get_data('stock_info_a_code_name')
            return df if isinstance(df, pd.DataFrame) else pd.DataFrame()
        except Exception as e:
            self.logger.error(f"获取股票基本信息失败: {e}")
            raise DataFetchError(f"获取股票基本信息失败: {e}")
    
    def get_daily_bars(self, ts_code: str = None, trade_date: str = None, 
                    start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        获取股票日线数据
        
        参数:
            ts_code: 股票代码，例如'000001.SZ'
            trade_date: 交易日期，格式YYYYMMDD，二选一
            start_date: 开始日期，格式YYYYMMDD，与trade_date二选一
            end_date: 结束日期，格式YYYYMMDD，可选
            
        返回:
            pd.DataFrame: 日线数据，包含字段:
                - ts_code: 股票代码
                - trade_date: 交易日期
                - open: 开盘价
                - high: 最高价
                - low: 最低价
                - close: 收盘价
                - vol: 成交量
                - amount: 成交额
                - ...其他字段
        """
        try:
            # 从ts_code中提取股票代码和交易所
            if ts_code:
                # 转换tushare格式的股票代码为AkShare格式
                code = ts_code.split('.')[0]
                market = 'sh' if ts_code.endswith('.SH') else 'sz'
                
                # 获取股票日线数据
                df = self.get_data('stock_zh_a_hist', symbol=code, period="daily", 
                              start_date=start_date, end_date=end_date, 
                              adjust="qfq")
                
                # 处理结果
                if isinstance(df, pd.DataFrame) and not df.empty:
                    # 转换列名为tushare格式
                    df = df.rename(columns={
                        '日期': 'trade_date',
                        '开盘': 'open',
                        '收盘': 'close',
                        '最高': 'high',
                        '最低': 'low',
                        '成交量': 'vol',
                        '成交额': 'amount',
                        '涨跌幅': 'pct_chg',
                        '涨跌额': 'change',
                        '换手率': 'turnover_rate'
                    })
                    
                    # 添加股票代码列
                    df['ts_code'] = ts_code
                    
                    # 格式化日期为YYYYMMDD格式
                    df['trade_date'] = df['trade_date'].str.replace('-', '')
                    
                    # 过滤日期范围
                    if trade_date:
                        df = df[df['trade_date'] == trade_date]
                    
                    return df
                else:
                    self.logger.warning(f"获取股票日线数据返回空数据: {ts_code}")
                    return pd.DataFrame()
            else:
                self.logger.warning("未提供股票代码，无法获取数据")
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"获取股票日线数据失败: {e}")
            raise DataFetchError(f"获取股票日线数据失败: {e}")
            
    def get_index_data(self, ts_code: str = None, trade_date: str = None, 
                      start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        获取指数数据
        
        参数:
            ts_code: 指数代码，例如'000300.SH'
            trade_date: 交易日期，格式YYYYMMDD，二选一
            start_date: 开始日期，格式YYYYMMDD，与trade_date二选一
            end_date: 结束日期，格式YYYYMMDD，可选
            
        返回:
            pd.DataFrame: 指数数据，包含字段:
                - ts_code: 指数代码
                - trade_date: 交易日期
                - open: 开盘点位
                - high: 最高点位
                - low: 最低点位
                - close: 收盘点位
                - vol: 成交量
                - amount: 成交额
                - ...其他字段
        """
        try:
            # 从ts_code中提取指数代码和类型
            if ts_code:
                code = ts_code.split('.')[0]
                
                # 针对不同类型的指数使用不同的API
                if ts_code.endswith('.SH'):
                    if code == '000001':  # 上证指数
                        df = self.get_data('stock_zh_index_daily', symbol="sh000001")
                    elif code == '000300':  # 沪深300
                        df = self.get_data('stock_zh_index_daily', symbol="sh000300")
                    else:
                        df = self.get_data('stock_zh_index_daily', symbol=f"sh{code}")
                elif ts_code.endswith('.SZ'):
                    if code == '399001':  # 深证成指
                        df = self.get_data('stock_zh_index_daily', symbol="sz399001")
                    elif code == '399006':  # 创业板指
                        df = self.get_data('stock_zh_index_daily', symbol="sz399006")
                    else:
                        df = self.get_data('stock_zh_index_daily', symbol=f"sz{code}")
                else:
                    self.logger.warning(f"不支持的指数代码格式: {ts_code}")
                    return pd.DataFrame()
                
                # 处理结果
                if isinstance(df, pd.DataFrame) and not df.empty:
                    # 转换列名为tushare格式
                    df = df.rename(columns={
                        '日期': 'trade_date',
                        '开盘': 'open',
                        '收盘': 'close',
                        '最高': 'high',
                        '最低': 'low',
                        '成交量': 'vol',
                        '成交额': 'amount',
                        '涨跌幅': 'pct_chg',
                        '涨跌额': 'change'
                    })
                    
                    # 添加指数代码列
                    df['ts_code'] = ts_code
                    
                    # 格式化日期为YYYYMMDD格式
                    df['trade_date'] = df['trade_date'].str.replace('-', '')
                    
                    # 过滤日期范围
                    if trade_date:
                        df = df[df['trade_date'] == trade_date]
                    elif start_date or end_date:
                        if start_date:
                            df = df[df['trade_date'] >= start_date]
                        if end_date:
                            df = df[df['trade_date'] <= end_date]
                    
                    return df
                else:
                    self.logger.warning(f"获取指数数据返回空数据: {ts_code}")
                    return pd.DataFrame()
            else:
                self.logger.warning("未提供指数代码，无法获取数据")
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"获取指数数据失败: {e}")
            raise DataFetchError(f"获取指数数据失败: {e}")
    
    def _normalize_code(self, code: str) -> str:
        """
        标准化股票代码
        
        参数:
            code: 原始股票代码
            
        返回:
            str: 标准化后的股票代码
        """
        if not code:
            return code
        
        # 移除所有空白字符
        code = code.strip()
        
        # 判断是否是6位数字
        if code.isdigit() and len(code) == 6:
            # 上交所股票以6开头，深交所股票以0或3开头
            if code.startswith('6'):
                return f"{code}.SH"
            elif code.startswith('0') or code.startswith('3'):
                return f"{code}.SZ"
            elif code.startswith('8') or code.startswith('4'):
                return f"{code}.BJ"  # 北交所
            elif code.startswith('5') or code.startswith('1'):
                return f"{code}.SH"  # 上交所基金
            # 如果不能确定交易所，默认上交所
            return f"{code}.SH"
        
        # 已经是标准格式的代码直接返回
        if '.' in code and (code.endswith('.SH') or code.endswith('.SZ') or code.endswith('.BJ')):
            return code
        
        return code
    
    def _normalize_date(self, date_obj: Union[str, datetime]) -> str:
        """
        标准化日期格式为YYYYMMDD
        
        参数:
            date_obj: 日期对象，可以是字符串或datetime
            
        返回:
            str: 标准化的日期字符串
        """
        if isinstance(date_obj, str):
            # 尝试解析字符串
            try:
                # 尝试YYYYMMDD格式
                if len(date_obj) == 8 and date_obj.isdigit():
                    # 已经是YYYYMMDD格式
                    return date_obj
                
                # 尝试YYYY-MM-DD格式
                if len(date_obj) == 10 and date_obj[4] == '-' and date_obj[7] == '-':
                    return date_obj.replace('-', '')
                
                # 其他格式尝试使用datetime解析
                date_obj = datetime.strptime(date_obj, "%Y-%m-%d")
            except ValueError:
                # 如果解析失败，直接返回原字符串
                return date_obj
        
        if isinstance(date_obj, datetime):
            return date_obj.strftime("%Y%m%d")
        
        # 如果不是字符串也不是datetime，转成字符串返回
        return str(date_obj) 