#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能异步Tushare数据适配器

{{ AURA-X: Add - 实现高性能异步TushareAdapter，支持异步HTTP调用、连接池管理、智能重试机制. Source: 异步并发优化第一阶段 }}

实现异步并发优化，预期提升3-5倍性能：
- 异步HTTP客户端和连接池管理
- 智能批处理和并发控制
- 自适应限流和重试机制
- 流式数据处理
"""

import asyncio
import aiohttp
import time
import logging
import json
import pandas as pd
from typing import List, Dict, Any, Optional, Union, Callable, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import random
from datetime import datetime, timedelta

from src.data.sources.data_source_interface import DataSourceInterface, DataSourceException, DataFetchError, RateLimitError
from src.data.fetcher.rate_limiter import AdaptiveRateLimiter, RateLimiter


@dataclass
class AsyncBatchConfig:
    """异步批处理配置"""
    batch_size: int = 50           # 批次大小
    max_concurrent: int = 100      # 最大并发数
    timeout: float = 30.0          # 请求超时时间
    retry_count: int = 3           # 重试次数
    retry_delay: float = 1.0       # 重试延迟
    backoff_factor: float = 2.0    # 退避因子


@dataclass
class AsyncResult:
    """异步请求结果"""
    symbol: str
    data: pd.DataFrame
    success: bool
    error: Optional[str] = None
    response_time: float = 0.0
    retry_count: int = 0


class AsyncAdaptiveRateLimiter:
    """
    异步自适应限流器
    
    {{ AURA-X: Add - 异步版本的自适应限流器，支持异步等待和监控. Source: 异步并发优化第一阶段 }}
    """
    
    def __init__(self, base_limiter: RateLimiter, logger: Optional[logging.Logger] = None):
        """
        初始化异步自适应限流器
        
        参数:
            base_limiter: 基础限流器
            logger: 日志记录器
        """
        self.base_limiter = base_limiter
        self.logger = logger or logging.getLogger(__name__)
        
        # 异步锁 - 延迟初始化，避免事件循环问题
        self._lock = None
        
        # 性能监控数据
        self.recent_response_times = []
        self.success_count = 0
        self.total_count = 0
        
        # 性能等级配置
        self.performance_levels = {
            'excellent': {'threshold': 0.3, 'multiplier': 1.5},
            'good': {'threshold': 0.8, 'multiplier': 1.2},
            'normal': {'threshold': 1.5, 'multiplier': 1.0},
            'slow': {'threshold': 3.0, 'multiplier': 0.85},
            'congested': {'threshold': float('inf'), 'multiplier': 0.7}
        }
        
        self.current_level = 'normal'
        self.last_adjustment_time = time.time()
        self.consecutive_success_count = 0

    def _get_lock(self):
        """安全地获取异步锁，支持延迟初始化"""
        if self._lock is None:
            try:
                # 尝试在当前事件循环中创建锁
                self._lock = asyncio.Lock()
            except RuntimeError:
                # 如果没有事件循环，创建一个临时的
                import threading
                self._lock_fallback = threading.Lock()
                return self._lock_fallback
        return self._lock
    
    async def record_response_time(self, api_name: str, response_time: float, success: bool = True):
        """
        异步记录API响应时间
        
        参数:
            api_name: API名称
            response_time: 响应时间
            success: 是否成功
        """
        lock = self._get_lock()
        if hasattr(lock, '__aenter__'):
            # 异步锁
            async with lock:
                self.recent_response_times.append(response_time)
        else:
            # 同步锁回退
            with lock:
                self.recent_response_times.append(response_time)
            
            # 保持最近20次记录
            if len(self.recent_response_times) > 20:
                self.recent_response_times.pop(0)
            
            # 更新统计
            self.total_count += 1
            if success:
                self.success_count += 1
                self.consecutive_success_count += 1
            else:
                self.consecutive_success_count = 0
    
    async def get_adaptive_multiplier(self, api_name: Optional[str] = None) -> float:
        """
        获取自适应调整倍数
        
        参数:
            api_name: API名称
            
        返回:
            float: 调整倍数
        """
        current_time = time.time()
        
        # 检查是否需要重新评估
        if current_time - self.last_adjustment_time > 60:  # 60秒调整间隔
            lock = self._get_lock()
            if hasattr(lock, '__aenter__'):
                # 异步锁
                async with lock:
                    old_level = self.current_level
                    self.current_level = await self._get_current_performance_level()
                    self.last_adjustment_time = current_time

                    if old_level != self.current_level:
                        self.logger.info(f"🔄 性能等级调整: {old_level} → {self.current_level}")
            else:
                # 同步锁回退
                with lock:
                    old_level = self.current_level
                    # 同步版本的性能等级获取
                    self.current_level = self._get_current_performance_level_sync()
                    self.last_adjustment_time = current_time

                    if old_level != self.current_level:
                        self.logger.info(f"🔄 性能等级调整: {old_level} → {self.current_level}")
        
        return self.performance_levels[self.current_level]['multiplier']
    
    async def _get_current_performance_level(self) -> str:
        """
        计算当前性能等级
        
        返回:
            str: 性能等级
        """
        if len(self.recent_response_times) < 5:
            return 'normal'
        
        # 计算平均响应时间
        recent_avg = sum(self.recent_response_times) / len(self.recent_response_times)
        
        # 渐进式恢复逻辑
        if self.current_level == 'congested':
            if recent_avg < 1.0 and self.consecutive_success_count >= 10:
                return 'slow'
            return 'congested'
        
        # 根据响应时间确定等级
        for level, config in self.performance_levels.items():
            if recent_avg < config['threshold']:
                return level
        
        return 'congested'

    def _get_current_performance_level_sync(self) -> str:
        """
        同步版本：根据最近的响应时间确定当前性能等级

        返回:
            str: 性能等级
        """
        if not self.recent_response_times:
            return 'normal'

        # 计算最近响应时间的平均值
        recent_avg = sum(self.recent_response_times[-10:]) / min(len(self.recent_response_times), 10)

        # 根据响应时间确定等级
        for level, config in self.performance_levels.items():
            if recent_avg < config['threshold']:
                return level

        return 'congested'
    
    async def wait_if_needed(self, api_name: Optional[str] = None):
        """
        异步限流等待
        
        参数:
            api_name: API名称
        """
        # 获取自适应倍数
        multiplier = await self.get_adaptive_multiplier(api_name)
        
        # 计算调整后的等待时间
        adjusted_period = self.base_limiter.period / multiplier
        
        # 计算需要等待的时间
        current_time = time.time()
        lock = self._get_lock()
        if hasattr(lock, '__aenter__'):
            # 异步锁
            async with lock:
                # 清理过期的请求记录
                cutoff_time = current_time - adjusted_period
                self.base_limiter._call_times = [
                    t for t in self.base_limiter._call_times if t >= cutoff_time
                ]

                # 检查是否需要等待
                if len(self.base_limiter._call_times) >= self.base_limiter.max_calls:
                    oldest_call = self.base_limiter._call_times[0]
                    wait_time = oldest_call + adjusted_period - current_time

                    if wait_time > 0:
                        self.logger.debug(f"异步限流等待: {wait_time:.2f}秒")
                        await asyncio.sleep(wait_time)

                # 记录新请求
                self.base_limiter._call_times.append(time.time())
        else:
            # 同步锁回退
            with lock:
                # 清理过期的请求记录
                cutoff_time = current_time - adjusted_period
                self.base_limiter._call_times = [
                    t for t in self.base_limiter._call_times if t >= cutoff_time
                ]

                # 检查是否需要等待
                if len(self.base_limiter._call_times) >= self.base_limiter.max_calls:
                    oldest_call = self.base_limiter._call_times[0]
                    wait_time = oldest_call + adjusted_period - current_time

                    if wait_time > 0:
                        self.logger.debug(f"同步限流等待: {wait_time:.2f}秒")
                        import time as time_module
                        time_module.sleep(wait_time)

                # 记录新请求
                self.base_limiter._call_times.append(time.time())


class HighPerformanceAsyncTushareAdapter(DataSourceInterface):
    """
    高性能异步Tushare数据适配器
    
    {{ AURA-X: Add - 实现高性能异步TushareAdapter，预期提升3-5倍性能. Source: 异步并发优化第一阶段 }}
    
    核心特性:
    - 异步HTTP客户端和连接池管理
    - 智能批处理和并发控制  
    - 自适应限流和重试机制
    - 流式数据处理
    """
    
    def __init__(
        self,
        token: str,
        batch_config: Optional[AsyncBatchConfig] = None,
        timeout: float = 30.0,
        max_retries: int = 3
    ):
        """
        初始化高性能异步Tushare适配器
        
        参数:
            token: Tushare API令牌
            batch_config: 批处理配置
            timeout: 请求超时时间
            max_retries: 最大重试次数
        """
        self.token = token
        self.timeout = timeout
        self.max_retries = max_retries
        
        # 批处理配置
        self.batch_config = batch_config or AsyncBatchConfig()
        
        # 日志记录器
        self.logger = logging.getLogger('AsyncTushareAdapter')
        
        # 异步HTTP会话
        self.session: Optional[aiohttp.ClientSession] = None

        # 连接状态
        self.connected = False
        self.pro = None

        # 初始化自适应限流器
        base_limiter = RateLimiter(max_calls=800, period=60.0, retry_after=1.0)
        self.adaptive_limiter = AsyncAdaptiveRateLimiter(base_limiter, self.logger)
        
        # API端点配置
        self.api_base_url = "http://api.waditu.com"
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_response_time': 0.0,
            'concurrent_requests': 0
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()
    
    async def connect(self) -> bool:
        """
        建立异步连接

        返回:
            bool: 是否连接成功
        """
        try:
            # 创建连接器配置
            connector = aiohttp.TCPConnector(
                limit=self.batch_config.max_concurrent,  # 总连接池大小
                limit_per_host=50,                       # 每个主机的连接数
                ttl_dns_cache=300,                       # DNS缓存TTL
                use_dns_cache=True,                      # 启用DNS缓存
                keepalive_timeout=300,                   # Keep-Alive超时
                enable_cleanup_closed=True               # 启用清理已关闭连接
            )

            # 创建超时配置
            timeout = aiohttp.ClientTimeout(
                total=self.timeout,
                connect=10.0,
                sock_read=self.timeout
            )

            # 创建会话
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    'User-Agent': 'HighPerformanceAsyncTushareAdapter/1.0',
                    'Connection': 'keep-alive'
                }
            )

            # 初始化Tushare客户端（在线程池中执行）
            if not self.token:
                self.logger.error("❌ Tushare token未设置")
                return False

            self.logger.info(f"🔄 开始初始化Tushare客户端，token: {self.token[:10]}...")

            # 获取当前运行的事件循环
            current_loop = asyncio.get_running_loop()
            success = await current_loop.run_in_executor(None, self._init_tushare_client)

            if success:
                self.logger.info(f"✅ 异步HTTP连接池已初始化: 最大并发={self.batch_config.max_concurrent}")
                self.logger.info("✅ Tushare客户端初始化成功")
                self.logger.info("✅ 异步适配器连接完成")
                # 确保连接状态正确设置
                self.connected = True
                return True
            else:
                self.logger.error("❌ Tushare客户端初始化失败")
                # 清理会话
                if self.session:
                    await self.session.close()
                    self.session = None
                self.connected = False
                return False

        except Exception as e:
            self.logger.error(f"❌ 异步连接初始化失败: {e}")
            return False

    def _init_tushare_client(self) -> bool:
        """在线程池中初始化Tushare客户端"""
        try:
            import tushare as ts

            # 设置token
            if not self.token:
                self.logger.error("Tushare token为空")
                return False

            ts.set_token(self.token)
            self.pro = ts.pro_api()

            # 测试连接
            test_df = self.pro.stock_basic(list_status='L', limit=1)
            success = test_df is not None and not test_df.empty

            if success:
                self.logger.info(f"✅ Tushare客户端初始化成功，token: {self.token[:10]}...")
            else:
                self.logger.error("❌ Tushare API测试调用返回空数据")

            return success
        except Exception as e:
            self.logger.error(f"❌ Tushare客户端初始化失败: {e}")
            return False
    
    async def disconnect(self) -> bool:
        """
        断开异步连接

        返回:
            bool: 是否断开成功
        """
        try:
            if self.session:
                await self.session.close()
                self.session = None
            self.connected = False
            self.logger.info("✅ 异步连接已关闭")
            return True
        except Exception as e:
            self.logger.error(f"❌ 断开异步连接失败: {e}")
            return False

    def __del__(self):
        """
        析构函数，确保会话正确关闭
        """
        if hasattr(self, 'session') and self.session and not self.session.closed:
            # 尝试同步清理方法
            self._force_cleanup_session()

    def _force_cleanup_session(self):
        """
        强制清理会话的同步方法
        """
        try:
            if hasattr(self, 'session') and self.session and not self.session.closed:
                # 方法1：尝试在当前事件循环中清理
                try:
                    loop = asyncio.get_running_loop()
                    if loop.is_running():
                        # 创建一个任务来关闭会话
                        loop.create_task(self._cleanup_session())
                        return
                except RuntimeError:
                    pass

                # 方法2：创建新的事件循环来清理
                try:
                    asyncio.run(self._cleanup_session())
                    return
                except Exception:
                    pass

                # 方法3：强制同步关闭连接器
                try:
                    if hasattr(self.session, '_connector') and self.session._connector:
                        self.session._connector.close()
                except Exception:
                    pass

                # 方法4：直接设置会话为已关闭状态
                try:
                    if hasattr(self.session, '_closed'):
                        self.session._closed = True
                    self.session = None
                except Exception:
                    pass
        except Exception:
            # 静默处理所有异常
            pass

    async def _cleanup_session(self):
        """
        清理会话的内部方法
        """
        try:
            if self.session and not self.session.closed:
                await self.session.close()
                self.session = None
        except Exception:
            # 静默处理清理过程中的异常
            pass

    def cleanup_sync(self):
        """
        同步清理方法，供外部调用
        """
        self._force_cleanup_session()
    
    def is_connected(self) -> bool:
        """
        检查连接状态

        返回:
            bool: 是否已连接
        """
        connected = (self.session is not None and
                    not self.session.closed and
                    hasattr(self, 'pro') and
                    self.pro is not None and
                    hasattr(self, 'connected') and
                    self.connected)

        if not connected:
            self.logger.debug(f"连接状态检查: session={self.session is not None}, "
                            f"closed={self.session.closed if self.session else 'N/A'}, "
                            f"pro={hasattr(self, 'pro')}, "
                            f"connected={getattr(self, 'connected', False)}")

        return connected

    async def _make_api_request(
        self,
        api_name: str,
        params: Dict[str, Any],
        retry_count: int = 0
    ) -> pd.DataFrame:
        """
        执行单个异步API请求

        参数:
            api_name: API名称
            params: 请求参数
            retry_count: 当前重试次数

        返回:
            pd.DataFrame: API响应数据
        """
        if not self.is_connected():
            raise DataSourceException("未连接到数据源")

        # 应用自适应限流
        await self.adaptive_limiter.wait_if_needed(api_name)

        start_time = time.time()

        try:
            self.stats['total_requests'] += 1
            self.stats['concurrent_requests'] += 1

            # 获取当前运行的事件循环，确保线程池执行器在正确的循环中
            current_loop = asyncio.get_running_loop()
            df = await current_loop.run_in_executor(None, self._execute_tushare_api, api_name, params)

            response_time = time.time() - start_time
            self.stats['total_response_time'] += response_time

            # 记录成功的响应时间
            await self.adaptive_limiter.record_response_time(api_name, response_time, True)

            self.stats['successful_requests'] += 1
            self.logger.debug(f"✅ {api_name} API调用成功: {len(df)}行, {response_time:.3f}s")

            return df

        except (RateLimitError, DataFetchError):
            # 记录失败的响应时间
            response_time = time.time() - start_time
            await self.adaptive_limiter.record_response_time(api_name, response_time, False)
            raise

        except Exception as e:
            response_time = time.time() - start_time
            await self.adaptive_limiter.record_response_time(api_name, response_time, False)

            # 重试逻辑
            if retry_count < self.max_retries:
                retry_delay = self.batch_config.retry_delay * (self.batch_config.backoff_factor ** retry_count)
                self.logger.warning(f"⚠️ {api_name} API调用失败，{retry_delay:.1f}s后重试 ({retry_count+1}/{self.max_retries}): {e}")

                await asyncio.sleep(retry_delay)
                return await self._make_api_request(api_name, params, retry_count + 1)

            self.stats['failed_requests'] += 1
            raise DataFetchError(f"API调用失败: {e}")

    async def _make_api_request_safe(self, api_name: str, params: Dict[str, Any], current_loop: asyncio.AbstractEventLoop, retry_count: int = 0) -> pd.DataFrame:
        """
        安全的API请求方法，避免事件循环冲突

        参数:
            api_name: API名称
            params: 请求参数
            current_loop: 当前事件循环
            retry_count: 重试次数

        返回:
            pd.DataFrame: API响应数据
        """
        # 应用自适应限流
        await self.adaptive_limiter.wait_if_needed(api_name)

        start_time = time.time()

        try:
            self.stats['total_requests'] += 1
            self.stats['concurrent_requests'] += 1

            # 🔧 确保在指定的事件循环中执行
            if current_loop != asyncio.get_running_loop():
                self.logger.warning(f"⚠️ 事件循环不匹配，使用当前循环")
                current_loop = asyncio.get_running_loop()

            df = await current_loop.run_in_executor(None, self._execute_tushare_api, api_name, params)

            response_time = time.time() - start_time
            self.stats['total_response_time'] += response_time

            # 记录成功的响应时间
            await self.adaptive_limiter.record_response_time(api_name, response_time, True)

            self.stats['successful_requests'] += 1
            self.logger.debug(f"✅ {api_name} API调用成功: {len(df)}行, {response_time:.3f}s")

            return df

        except (RateLimitError, DataFetchError):
            # 记录失败的响应时间
            response_time = time.time() - start_time
            await self.adaptive_limiter.record_response_time(api_name, response_time, False)
            raise

        except Exception as e:
            response_time = time.time() - start_time
            await self.adaptive_limiter.record_response_time(api_name, response_time, False)

            # 重试逻辑
            if retry_count < self.max_retries:
                retry_delay = self.batch_config.retry_delay * (self.batch_config.backoff_factor ** retry_count)
                self.logger.warning(f"⚠️ {api_name} API调用失败，{retry_delay:.1f}s后重试 ({retry_count+1}/{self.max_retries}): {e}")

                await asyncio.sleep(retry_delay)
                return await self._make_api_request_safe(api_name, params, current_loop, retry_count + 1)

            self.stats['failed_requests'] += 1
            raise DataFetchError(f"API调用失败: {e}")

    async def _make_api_request_fallback(self, api_name: str, params: Dict[str, Any]) -> pd.DataFrame:
        """
        回退的API请求方法，用于处理事件循环冲突

        参数:
            api_name: API名称
            params: 请求参数

        返回:
            pd.DataFrame: API响应数据
        """
        try:
            # 🔧 使用同步方式作为回退
            import concurrent.futures

            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(self._execute_tushare_api, api_name, params)
                df = future.result(timeout=60)  # 🔧 延长超时至60秒，提高成功率

            self.logger.debug(f"✅ {api_name} 回退API调用成功: {len(df)}行")
            return df

        except Exception as e:
            self.logger.error(f"❌ {api_name} 回退API调用失败: {e}")
            raise DataFetchError(f"回退API调用失败: {e}")

        finally:
            self.stats['concurrent_requests'] -= 1

    def _execute_tushare_api(self, api_name: str, params: Dict[str, Any]) -> pd.DataFrame:
        """
        在线程池中执行Tushare API调用

        参数:
            api_name: API名称
            params: 请求参数

        返回:
            pd.DataFrame: API响应数据
        """
        try:
            # 根据API名称调用相应的方法
            if api_name == 'daily':
                return self.pro.daily(**params)
            elif api_name == 'daily_basic':
                return self.pro.daily_basic(**params)
            elif api_name == 'income':
                return self.pro.income(**params)
            elif api_name == 'balancesheet':
                return self.pro.balancesheet(**params)
            elif api_name == 'cashflow':
                return self.pro.cashflow(**params)
            elif api_name == 'stock_basic':
                return self.pro.stock_basic(**params)
            elif api_name == 'trade_cal':
                return self.pro.trade_cal(**params)
            else:
                # 通用API调用
                api_method = getattr(self.pro, api_name, None)
                if api_method:
                    return api_method(**params)
                else:
                    raise DataFetchError(f"不支持的API: {api_name}")

        except Exception as e:
            # 检查是否是限流错误
            error_msg = str(e)
            if '每分钟最多访问' in error_msg or 'too many requests' in error_msg.lower():
                raise RateLimitError(f"API限流: {error_msg}")

            raise DataFetchError(f"Tushare API调用失败: {e}")

    async def _create_isolated_task_pool(self, symbols: List[str], api_name: str, base_params: Dict[str, Any], semaphore: asyncio.Semaphore) -> List[AsyncResult]:
        """
        创建隔离的任务池，避免事件循环冲突

        参数:
            symbols: 股票代码列表
            api_name: API名称
            base_params: 基础参数
            semaphore: 并发控制信号量

        返回:
            List[AsyncResult]: 异步结果列表
        """
        async def fetch_single_stock_isolated(symbol: str) -> AsyncResult:
            """隔离的单只股票数据获取"""
            async with semaphore:
                start_time = time.time()
                try:
                    # 🔧 确保在当前事件循环中执行
                    current_loop = asyncio.get_running_loop()

                    # 准备参数
                    params = base_params.copy()
                    params['ts_code'] = symbol

                    # 🔧 使用当前循环执行API请求，避免跨循环调用
                    df = await self._make_api_request_safe(api_name, params, current_loop)

                    response_time = time.time() - start_time
                    return AsyncResult(
                        symbol=symbol,
                        data=df,
                        success=True,
                        response_time=response_time
                    )

                except Exception as e:
                    response_time = time.time() - start_time
                    error_msg = str(e)

                    # 🔧 特殊处理事件循环冲突错误
                    if "attached to a different loop" in error_msg:
                        self.logger.warning(f"⚠️ {symbol}事件循环冲突，尝试重新获取")
                        try:
                            # 重新尝试获取，使用更安全的方式
                            df = await self._make_api_request_fallback(api_name, params)
                            response_time = time.time() - start_time
                            return AsyncResult(
                                symbol=symbol,
                                data=df,
                                success=True,
                                response_time=response_time
                            )
                        except Exception as retry_e:
                            self.logger.error(f"❌ {symbol}重试失败: {retry_e}")
                            error_msg = str(retry_e)
                    else:
                        self.logger.error(f"❌ 获取{symbol}数据失败: {e}")

                    return AsyncResult(
                        symbol=symbol,
                        data=pd.DataFrame(),
                        success=False,
                        error=error_msg,
                        response_time=response_time
                    )

        # 🔧 任务池隔离机制：创建独立的任务执行环境
        results = []

        # 将symbols分成更小的组，每组使用独立的任务池
        group_size = min(8, len(symbols))  # 进一步减小组大小，增强隔离效果

        for i in range(0, len(symbols), group_size):
            group_symbols = symbols[i:i + group_size]

            try:
                # 🔧 关键修复：确保任务在当前事件循环中正确创建
                current_loop = asyncio.get_running_loop()

                # 🔧 使用更安全的任务创建方式，避免事件循环冲突
                group_tasks = []
                for symbol in group_symbols:
                    # 直接在当前循环中创建任务，避免跨循环引用
                    task = current_loop.create_task(fetch_single_stock_isolated(symbol))
                    group_tasks.append(task)

                # 🔧 使用wait替代gather，提供更好的异常处理
                # 🔧 优化超时设置：根据批次大小动态调整超时时间
                dynamic_timeout = min(60, max(45, len(group_symbols) * 2))  # 45-60秒动态超时
                try:
                    done, pending = await asyncio.wait(group_tasks, return_when=asyncio.ALL_COMPLETED, timeout=dynamic_timeout)

                    # 处理完成的任务
                    group_results = []
                    for task in done:
                        try:
                            result = await task
                            group_results.append(result)
                        except Exception as e:
                            # 获取任务对应的股票代码
                            task_index = group_tasks.index(task) if task in group_tasks else 0
                            symbol = group_symbols[task_index] if task_index < len(group_symbols) else "unknown"
                            self.logger.error(f"❌ 任务执行异常 {symbol}: {e}")
                            group_results.append(AsyncResult(
                                symbol=symbol,
                                data=pd.DataFrame(),
                                success=False,
                                error=str(e),
                                response_time=0.0
                            ))

                    # 🔧 处理超时的任务：添加重试机制
                    timeout_symbols = []
                    for task in pending:
                        task.cancel()
                        task_index = group_tasks.index(task) if task in group_tasks else 0
                        symbol = group_symbols[task_index] if task_index < len(group_symbols) else "unknown"
                        timeout_symbols.append(symbol)
                        self.logger.warning(f"⚠️ 任务超时取消 {symbol}，将尝试单独重试")

                    # 🔧 对超时的股票进行单独重试
                    for symbol in timeout_symbols:
                        try:
                            self.logger.info(f"🔄 单独重试超时股票: {symbol}")
                            params = base_params.copy()
                            params['ts_code'] = symbol

                            # 使用更长的超时时间进行重试
                            retry_start = time.time()
                            df = await asyncio.wait_for(
                                self._make_api_request_fallback(api_name, params),
                                timeout=90  # 90秒超时重试
                            )
                            retry_time = time.time() - retry_start

                            group_results.append(AsyncResult(
                                symbol=symbol,
                                data=df,
                                success=True,
                                response_time=retry_time,
                                retry_count=1
                            ))
                            self.logger.info(f"✅ {symbol} 重试成功: {len(df)}行, {retry_time:.2f}s")

                        except Exception as retry_e:
                            self.logger.error(f"❌ {symbol} 重试失败: {retry_e}")
                            group_results.append(AsyncResult(
                                symbol=symbol,
                                data=pd.DataFrame(),
                                success=False,
                                error=f"超时重试失败: {str(retry_e)}",
                                response_time=dynamic_timeout,
                                retry_count=1
                            ))

                    # 将结果添加到总结果列表
                    results.extend(group_results)

                except Exception as wait_error:
                    self.logger.error(f"❌ wait执行失败: {wait_error}")
                    # 取消所有任务
                    for task in group_tasks:
                        if not task.done():
                            task.cancel()

                    # 为整个组创建失败结果
                    for symbol in group_symbols:
                        results.append(AsyncResult(
                            symbol=symbol,
                            data=pd.DataFrame(),
                            success=False,
                            error=str(wait_error),
                            response_time=0.0
                        ))

                # 🔧 清理任务对象引用
                del group_tasks

            except Exception as e:
                self.logger.error(f"❌ 任务池执行失败: {e}")
                # 为整个组创建失败结果
                for symbol in group_symbols:
                    results.append(AsyncResult(
                        symbol=symbol,
                        data=pd.DataFrame(),
                        success=False,
                        error=str(e),
                        response_time=0.0
                    ))

            # 组间休息，让事件循环完全清理
            if i + group_size < len(symbols):
                await asyncio.sleep(0.02)  # 增加休息时间

                # 🔧 强制垃圾回收：每5组进行一次
                if (i // group_size + 1) % 5 == 0:
                    import gc
                    collected = gc.collect()
                    if collected > 0:
                        self.logger.debug(f"🧹 任务池垃圾回收清理了 {collected} 个对象")

        return results

    async def fetch_multiple_stocks_async(
        self,
        symbols: List[str],
        api_name: str,
        base_params: Dict[str, Any],
        semaphore: Optional[asyncio.Semaphore] = None
    ) -> List[AsyncResult]:
        """
        异步并发获取多只股票数据

        参数:
            symbols: 股票代码列表
            api_name: API名称
            base_params: 基础参数
            semaphore: 并发控制信号量

        返回:
            List[AsyncResult]: 异步结果列表
        """
        if not semaphore:
            semaphore = asyncio.Semaphore(self.batch_config.max_concurrent)

        # 执行并发请求，使用任务池隔离机制
        self.logger.info(f"🚀 开始异步并发获取 {len(symbols)} 只股票的 {api_name} 数据")

        try:
            # 🔧 使用隔离的任务池机制
            results = await self._create_isolated_task_pool(symbols, api_name, base_params, semaphore)

        except Exception as e:
            self.logger.error(f"❌ 任务池执行失败: {e}")
            # 创建失败结果
            results = [AsyncResult(
                symbol=symbol,
                data=pd.DataFrame(),
                success=False,
                error=str(e),
                response_time=0.0
            ) for symbol in symbols]

        # 统计结果
        successful = sum(1 for r in results if r.success)
        failed = len(results) - successful
        avg_time = sum(r.response_time for r in results) / len(results) if results else 0

        self.logger.info(f"✅ 异步并发获取完成: 成功{successful}只, 失败{failed}只, 平均耗时{avg_time:.3f}s")

        return results

    async def get_market_data_async(
        self,
        symbols: List[str],
        data_type: str = 'daily',
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        fields: Optional[str] = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        异步获取市场数据

        参数:
            symbols: 股票代码列表
            data_type: 数据类型 ('daily', 'daily_basic', 'weekly', 'monthly')
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            fields: 字段列表
            **kwargs: 其他参数

        返回:
            pd.DataFrame: 合并后的市场数据
        """
        # API名称映射
        api_name_map = {
            'daily': 'daily',
            'daily_basic': 'daily_basic',
            'weekly': 'weekly',
            'monthly': 'monthly'
        }

        api_name = api_name_map.get(data_type, 'daily')

        # 准备基础参数
        base_params = {}
        if start_date:
            base_params['start_date'] = start_date
        if end_date:
            base_params['end_date'] = end_date
        if fields:
            base_params['fields'] = fields
        base_params.update(kwargs)

        # 🔧 分阶段处理架构：根据股票数量决定是否分阶段
        if len(symbols) > 500:
            self.logger.info(f"🎯 启用分阶段处理架构: {len(symbols)}只股票 > 500阈值")

            # 将股票分为多个阶段，每个阶段最多500只股票
            stage_size = 500
            total_stages = (len(symbols) + stage_size - 1) // stage_size
            all_results = []

            for stage_num in range(1, total_stages + 1):
                start_idx = (stage_num - 1) * stage_size
                end_idx = min(start_idx + stage_size, len(symbols))
                stage_symbols = symbols[start_idx:end_idx]

                self.logger.info(f"🚀 开始处理阶段 {stage_num}/{total_stages}: {len(stage_symbols)}只股票")

                try:
                    # 🔧 修复：调用底层批处理方法处理当前阶段
                    stage_df = await self._execute_original_batch_logic(
                        stage_symbols, data_type, start_date, end_date, fields, **kwargs
                    )

                    # 将DataFrame转换为AsyncResult格式
                    if not stage_df.empty:
                        # 按股票代码分组，为每只股票创建AsyncResult
                        for symbol in stage_symbols:
                            symbol_data = stage_df[stage_df['ts_code'] == symbol] if 'ts_code' in stage_df.columns else pd.DataFrame()
                            all_results.append(AsyncResult(
                                symbol=symbol,
                                data=symbol_data,
                                success=not symbol_data.empty,
                                response_time=0.0
                            ))
                    else:
                        # 创建失败结果
                        for symbol in stage_symbols:
                            all_results.append(AsyncResult(
                                symbol=symbol,
                                data=pd.DataFrame(),
                                success=False,
                                error="阶段处理失败",
                                response_time=0.0
                            ))

                    # 🔧 阶段间深度重置：完全清理事件循环状态
                    if stage_num < total_stages:
                        self.logger.info(f"🔄 阶段 {stage_num} 完成，进行阶段间深度重置...")

                        # 强制垃圾回收
                        import gc
                        collected = gc.collect()
                        self.logger.info(f"🧹 阶段间垃圾回收清理了 {collected} 个对象")

                        # 更长的休息时间，让系统完全恢复
                        await asyncio.sleep(3.0)

                        self.logger.info(f"✅ 阶段 {stage_num} 处理完成，准备开始阶段 {stage_num + 1}")

                except Exception as e:
                    self.logger.error(f"❌ 阶段 {stage_num} 处理失败: {e}")
                    # 为整个阶段创建失败结果
                    for symbol in stage_symbols:
                        all_results.append(AsyncResult(
                            symbol=symbol,
                            data=pd.DataFrame(),
                            success=False,
                            error=str(e),
                            response_time=0.0
                        ))
                    continue

            # 合并所有阶段的结果
            all_data = []
            for result in all_results:
                if result.success and not result.data.empty:
                    all_data.append(result.data)

            if all_data:
                combined_df = pd.concat(all_data, ignore_index=True)
                self.logger.info(f"📊 分阶段处理完成: 总计{len(combined_df)}行数据")
                return combined_df
            else:
                self.logger.warning("⚠️ 分阶段处理未获取到任何数据")
                return pd.DataFrame()

        # 🔧 单阶段处理：股票数量<=500时使用原有逻辑
        self.logger.info(f"🎯 使用单阶段处理: {len(symbols)}只股票 <= 500阈值")

        # 直接使用原有的批处理逻辑（单阶段处理）
        return await self._execute_original_batch_logic(symbols, data_type, start_date, end_date, fields, **kwargs)

    async def _execute_original_batch_logic(
        self,
        symbols: List[str],
        data_type: str,
        start_date: str,
        end_date: str,
        fields: Optional[List[str]] = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        执行原有的批处理逻辑（单阶段处理的底层实现）

        参数:
            symbols: 股票代码列表
            data_type: 数据类型
            start_date: 开始日期
            end_date: 结束日期
            fields: 字段列表
            **kwargs: 其他参数

        返回:
            pd.DataFrame: 合并后的数据
        """
        # 🔧 API名称映射：将数据类型映射到对应的API名称
        api_name_map = {
            'daily': 'daily',
            'daily_basic': 'daily_basic',
            'market_cap': 'daily_basic',  # 市值数据使用daily_basic接口
            'adj_factor': 'adj_factor',
            'suspend': 'suspend',
            'limit_list': 'limit_list',
            'moneyflow': 'moneyflow',
            'stk_limit': 'stk_limit',
            'block_trade': 'block_trade',
            'stk_holdernumber': 'stk_holdernumber',
            'top10_holders': 'top10_holders',
            'top10_floatholders': 'top10_floatholders'
        }

        api_name = api_name_map.get(data_type, data_type)
        self.logger.info(f"🔧 API名称映射: {data_type} -> {api_name}")

        # 🔧 构建base_params：将方法参数转换为API调用参数
        base_params = {
            'start_date': start_date,
            'end_date': end_date
        }

        # 添加字段参数
        if fields:
            base_params['fields'] = ','.join(fields)

        # 添加其他kwargs参数
        for key, value in kwargs.items():
            if value is not None:
                base_params[key] = value

        self.logger.info(f"🔧 构建base_params: {base_params}")

        # 🔧 智能批处理：根据股票数量和稳定性需求动态调整批次大小
        batch_size = min(self.batch_config.batch_size, len(symbols))

        # 🔧 系统状态检查：根据当前事件循环状态调整批次大小
        try:
            current_loop = asyncio.get_running_loop()
            all_tasks = asyncio.all_tasks(current_loop)
            pending_tasks = [task for task in all_tasks if not task.done()]

            # 如果当前有大量待处理任务，进一步减小批次大小
            if len(pending_tasks) > 100:
                self.logger.warning(f"⚠️ 检测到大量待处理任务({len(pending_tasks)})，减小批次大小")
                batch_size = max(10, batch_size // 2)
            elif len(pending_tasks) > 50:
                self.logger.info(f"📊 检测到较多待处理任务({len(pending_tasks)})，适度减小批次大小")
                batch_size = max(15, int(batch_size * 0.8))

        except RuntimeError:
            self.logger.warning("⚠️ 无法获取事件循环状态，使用默认批次大小")

        # 大规模稳定性优化：更保守的批次大小策略
        if len(symbols) > 1000:
            batch_size = max(15, batch_size // 3)  # 大量股票时更小批次
        elif len(symbols) > 500:
            batch_size = max(25, batch_size // 2)  # 中等规模时减小批次
        elif len(symbols) > 200:
            batch_size = max(35, batch_size // 1.5)  # 较多股票时适度减小
        elif len(symbols) < 100:
            batch_size = min(50, len(symbols))    # 少量股票时适度增大

        self.logger.info(f"🎯 智能批处理: {len(symbols)}只股票, 批次大小={batch_size} (大规模稳定性优化)")

        # 分批处理
        all_results = []
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(symbols) - 1) // batch_size + 1

            self.logger.info(f"📦 处理批次 {batch_num}/{total_batches}: {len(batch_symbols)}只股票")

            try:
                # 异步获取批次数据，添加超时保护
                batch_results = await asyncio.wait_for(
                    self.fetch_multiple_stocks_async(batch_symbols, api_name, base_params),
                    timeout=self.batch_config.timeout * 2  # 给批次更多时间
                )
                all_results.extend(batch_results)

            except asyncio.TimeoutError:
                self.logger.warning(f"⚠️ 批次 {batch_num} 超时，跳过该批次")
                # 创建失败结果
                failed_results = [AsyncResult(
                    symbol=symbol,
                    data=pd.DataFrame(),
                    success=False,
                    error="批次超时",
                    response_time=0.0
                ) for symbol in batch_symbols]
                all_results.extend(failed_results)

            except Exception as e:
                self.logger.error(f"❌ 批次 {batch_num} 处理失败: {e}")
                # 创建失败结果
                failed_results = [AsyncResult(
                    symbol=symbol,
                    data=pd.DataFrame(),
                    success=False,
                    error=str(e),
                    response_time=0.0
                ) for symbol in batch_symbols]
                all_results.extend(failed_results)

            # 🔧 智能批次间隔：根据性能动态调整
            if i + batch_size < len(symbols):
                # 根据当前性能等级调整间隔时间
                current_level = getattr(self.adaptive_limiter, 'current_level', 'normal')
                if current_level == 'excellent':
                    interval = 0.2  # 性能优秀时减少间隔
                elif current_level == 'good':
                    interval = 0.3  # 性能良好时标准间隔
                elif current_level == 'normal':
                    interval = 0.4  # 性能一般时增加间隔
                else:
                    interval = 0.6  # 性能差时大幅增加间隔

                self.logger.debug(f"📊 批次间隔调整: {interval}s (性能等级: {current_level})")
                await asyncio.sleep(interval)

                # 🔧 大规模稳定性优化：更频繁的中期休息
                if batch_num % 5 == 0:  # 每5个批次休息一次（原来是10个）
                    self.logger.info(f"🔄 批次 {batch_num} 完成，进行中期休息...")
                    await asyncio.sleep(1.5)  # 增加休息时间

                    # 强制垃圾回收，清理可能的循环引用
                    import gc
                    gc.collect()

                    # 🔧 超大规模稳定性优化：最频繁事件循环重置机制
                    if batch_num % 5 == 0 and batch_num > 0:  # 每5个批次进行深度重置（原来10个）
                        self.logger.info(f"🔄 批次 {batch_num} 完成，进行最频繁事件循环深度重置...")

                        # 强制垃圾回收，清理事件循环相关对象
                        import weakref

                        # 清理弱引用和循环引用
                        collected = gc.collect()
                        self.logger.info(f"🧹 垃圾回收清理了 {collected} 个对象")

                        # 检查事件循环任务数量
                        try:
                            current_loop = asyncio.get_running_loop()
                            all_tasks = asyncio.all_tasks(current_loop)
                            pending_tasks = [task for task in all_tasks if not task.done()]
                            self.logger.info(f"📊 当前事件循环状态: 总任务{len(all_tasks)}, 待处理{len(pending_tasks)}")

                            # 🔧 更积极的任务清理策略
                            if len(pending_tasks) > 30:  # 降低阈值（原来50）
                                self.logger.warning(f"⚠️ 检测到大量待处理任务({len(pending_tasks)})，延长休息时间")
                                await asyncio.sleep(5.0)  # 增加休息时间（原来3.0）
                            elif len(pending_tasks) > 10:  # 新增中等阈值
                                self.logger.info(f"📊 检测到较多待处理任务({len(pending_tasks)})，适度休息")
                                await asyncio.sleep(2.0)

                            # 让事件循环有时间处理内部清理
                            await asyncio.sleep(0.2)  # 增加清理时间

                        except RuntimeError as e:
                            self.logger.error(f"❌ 事件循环状态检查异常: {e}")

                    # 🔧 事件循环健康检查：确保当前循环状态正常
                    try:
                        current_loop = asyncio.get_running_loop()
                        if current_loop.is_closed():
                            self.logger.error("❌ 检测到事件循环已关闭，停止处理")
                            break
                    except RuntimeError as e:
                        self.logger.error(f"❌ 事件循环状态异常: {e}")
                        break

        # 合并成功的结果
        successful_dfs = [result.data for result in all_results if result.success and not result.data.empty]

        if not successful_dfs:
            self.logger.warning("⚠️ 没有成功获取到任何数据")
            return pd.DataFrame()

        # 合并所有数据
        combined_df = pd.concat(successful_dfs, ignore_index=True)

        # 统计信息
        total_symbols = len(symbols)
        successful_symbols = sum(1 for r in all_results if r.success)
        failed_symbols = total_symbols - successful_symbols

        self.logger.info(f"📊 数据获取完成: 总计{len(combined_df)}行, 成功{successful_symbols}只股票, 失败{failed_symbols}只股票")

        return combined_df

    async def get_financial_data_async(
        self,
        symbols: List[str],
        report_type: str = 'income',
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        fields: Optional[str] = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        异步获取财务数据

        参数:
            symbols: 股票代码列表
            report_type: 报表类型 ('income', 'balancesheet', 'cashflow')
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            fields: 字段列表
            **kwargs: 其他参数

        返回:
            pd.DataFrame: 合并后的财务数据
        """
        # API名称映射
        api_name_map = {
            'income': 'income',
            'balancesheet': 'balancesheet',
            'cashflow': 'cashflow'
        }

        api_name = api_name_map.get(report_type, 'income')

        # 准备基础参数
        base_params = {}
        if start_date:
            base_params['start_date'] = start_date
        if end_date:
            base_params['end_date'] = end_date
        if fields:
            base_params['fields'] = fields
        base_params.update(kwargs)

        # 🔧 财务数据智能批次大小：针对200次/分钟限流优化
        # 200次/分钟 = 3.33次/秒，为了安全起见使用2.5次/秒
        # 考虑到并发和网络延迟，使用更保守的批次大小
        if len(symbols) > 500:
            batch_size = 8   # 大规模数据：更小批次，更长间隔
        elif len(symbols) > 200:
            batch_size = 12  # 中等规模：平衡批次大小
        else:
            batch_size = 15  # 小规模：稍大批次，提高效率

        self.logger.info(f"💰 获取财务数据: {len(symbols)}只股票, 类型={report_type}")

        # 分批处理
        all_results = []
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(symbols) - 1) // batch_size + 1

            self.logger.info(f"📦 处理财务数据批次 {batch_num}/{total_batches}")

            # 🔧 财务数据智能重试机制：专门处理限流问题
            retry_count = 0
            max_retries = 2  # 财务数据最多重试2次
            batch_success = False

            while retry_count <= max_retries and not batch_success:
                try:
                    # 异步获取批次数据，添加超时保护
                    batch_results = await asyncio.wait_for(
                        self.fetch_multiple_stocks_async(batch_symbols, api_name, base_params),
                        timeout=self.batch_config.timeout * 3  # 财务数据给更多时间
                    )
                    all_results.extend(batch_results)
                    batch_success = True

                    if retry_count > 0:
                        self.logger.info(f"✅ 财务数据批次 {batch_num} 重试成功 (第{retry_count}次重试)")

                except asyncio.TimeoutError:
                    retry_count += 1
                    if retry_count <= max_retries:
                        wait_time = retry_count * 5  # 递增等待时间
                        self.logger.warning(f"⚠️ 财务数据批次 {batch_num} 超时，{wait_time}秒后进行第{retry_count}次重试...")
                        await asyncio.sleep(wait_time)
                    else:
                        self.logger.error(f"❌ 财务数据批次 {batch_num} 超时，已达最大重试次数")
                        # 创建失败结果
                        failed_results = [AsyncResult(
                            symbol=symbol,
                            data=pd.DataFrame(),
                            success=False,
                            error="财务数据批次超时（已重试）",
                            response_time=0.0
                        ) for symbol in batch_symbols]
                        all_results.extend(failed_results)

                except Exception as e:
                    error_msg = str(e)
                    # 检查是否是限流错误
                    if '每分钟最多访问' in error_msg or 'too many requests' in error_msg.lower():
                        retry_count += 1
                        if retry_count <= max_retries:
                            # 限流错误：等待更长时间
                            wait_time = 15 + (retry_count * 10)  # 15秒起，递增10秒
                            self.logger.warning(f"🚨 财务数据批次 {batch_num} 遇到限流，{wait_time}秒后进行第{retry_count}次重试...")
                            await asyncio.sleep(wait_time)
                        else:
                            self.logger.error(f"❌ 财务数据批次 {batch_num} 限流重试失败，已达最大重试次数")
                            # 创建失败结果
                            failed_results = [AsyncResult(
                                symbol=symbol,
                                data=pd.DataFrame(),
                                success=False,
                                error=f"财务数据限流（已重试）: {error_msg}",
                                response_time=0.0
                            ) for symbol in batch_symbols]
                            all_results.extend(failed_results)
                    else:
                        # 非限流错误：不重试
                        self.logger.error(f"❌ 财务数据批次 {batch_num} 处理失败: {e}")
                        failed_results = [AsyncResult(
                            symbol=symbol,
                            data=pd.DataFrame(),
                            success=False,
                            error=str(e),
                            response_time=0.0
                        ) for symbol in batch_symbols]
                        all_results.extend(failed_results)
                        break  # 非限流错误直接退出重试循环

            # 🔧 财务数据智能限流策略：针对200次/分钟优化
            if i + batch_size < len(symbols):
                # 动态计算休息时间：确保不超过200次/分钟
                # 每个批次平均API调用次数 = batch_size
                # 目标：每分钟不超过180次（留20次缓冲）
                calls_per_batch = batch_size
                target_calls_per_minute = 180
                min_interval_seconds = (calls_per_batch * 60) / target_calls_per_minute

                # 实际休息时间：最小间隔 + 安全缓冲
                actual_sleep = max(min_interval_seconds, 2.0)  # 至少2秒

                self.logger.info(f"💤 财务数据批次间隔: {actual_sleep:.1f}秒 (批次大小={batch_size}, 目标限流=180次/分钟)")
                await asyncio.sleep(actual_sleep)

                # 🔧 财务数据稳定性优化：更频繁的中期休息
                if batch_num % 2 == 0:  # 每2个批次休息一次，更频繁
                    self.logger.info(f"🔄 财务数据批次 {batch_num} 完成，进行中期休息...")
                    await asyncio.sleep(5.0)  # 更长的中期休息

                    # 强制垃圾回收，清理可能的循环引用
                    import gc
                    gc.collect()

                    # 🔧 财务数据智能重置机制：更频繁的深度重置
                    if batch_num % 5 == 0 and batch_num > 0:  # 每5个批次进行深度重置
                        self.logger.info(f"🔄 财务数据批次 {batch_num} 完成，进行智能深度重置...")

                        # 强制垃圾回收，清理事件循环相关对象
                        collected = gc.collect()
                        self.logger.info(f"🧹 财务数据垃圾回收清理了 {collected} 个对象")

                        # 检查事件循环任务数量
                        try:
                            current_loop = asyncio.get_running_loop()
                            all_tasks = asyncio.all_tasks(current_loop)
                            pending_tasks = [task for task in all_tasks if not task.done()]
                            self.logger.info(f"📊 财务数据事件循环状态: 总任务{len(all_tasks)}, 待处理{len(pending_tasks)}")

                            # 财务数据更严格的任务数量控制
                            if len(pending_tasks) > 20:  # 财务数据阈值更低
                                self.logger.warning(f"⚠️ 财务数据检测到过多待处理任务({len(pending_tasks)})，延长休息时间")
                                await asyncio.sleep(8.0)  # 更长的休息时间

                            # 🔧 财务数据限流恢复策略：给API服务器更多恢复时间
                            self.logger.info("💤 财务数据深度重置休息，等待API限流恢复...")
                            await asyncio.sleep(10.0)  # 给API服务器充分的恢复时间

                            # 让事件循环有时间处理内部清理
                            await asyncio.sleep(0.2)

                        except RuntimeError as e:
                            self.logger.error(f"❌ 财务数据事件循环状态检查异常: {e}")

                    # 🔧 事件循环健康检查：确保当前循环状态正常
                    try:
                        current_loop = asyncio.get_running_loop()
                        if current_loop.is_closed():
                            self.logger.error("❌ 检测到财务数据事件循环已关闭，停止处理")
                            break
                    except RuntimeError as e:
                        self.logger.error(f"❌ 财务数据事件循环状态异常: {e}")
                        break

        # 合并成功的结果
        successful_dfs = [result.data for result in all_results if result.success and not result.data.empty]

        if not successful_dfs:
            self.logger.warning("⚠️ 没有成功获取到任何财务数据")
            return pd.DataFrame()

        combined_df = pd.concat(successful_dfs, ignore_index=True)

        # 统计信息
        successful_symbols = sum(1 for r in all_results if r.success)
        failed_symbols = len(symbols) - successful_symbols

        self.logger.info(f"💰 财务数据获取完成: 总计{len(combined_df)}行, 成功{successful_symbols}只股票, 失败{failed_symbols}只股票")

        return combined_df

    # 实现DataSourceInterface接口方法

    def get_market_data(
        self,
        symbols: Union[str, List[str]],
        data_type: str = 'daily',
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        fields: Optional[List[str]] = None,
        adjust: Optional[str] = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        同步接口：获取市场数据

        这是DataSourceInterface的同步接口实现，内部调用异步方法
        """
        # 转换参数
        if isinstance(symbols, str):
            symbols = [symbols]

        fields_str = ','.join(fields) if fields else None

        # 运行异步方法
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                self.get_market_data_async(
                    symbols=symbols,
                    data_type=data_type,
                    start_date=start_date,
                    end_date=end_date,
                    fields=fields_str,
                    **kwargs
                )
            )
        finally:
            loop.close()

    def get_financial_data(
        self,
        symbols: Union[str, List[str]],
        report_type: str = 'income',
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        fields: Optional[List[str]] = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        同步接口：获取财务数据

        这是DataSourceInterface的同步接口实现，内部调用异步方法
        """
        # 转换参数
        if isinstance(symbols, str):
            symbols = [symbols]

        fields_str = ','.join(fields) if fields else None

        # 运行异步方法
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                self.get_financial_data_async(
                    symbols=symbols,
                    report_type=report_type,
                    start_date=start_date,
                    end_date=end_date,
                    fields=fields_str,
                    **kwargs
                )
            )
        finally:
            loop.close()

    def get_reference_data(
        self,
        data_type: str,
        fields: Optional[List[str]] = None,
        conditions: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        同步接口：获取参考数据

        参数:
            data_type: 数据类型 ('stock_basic', 'trade_cal', etc.)
            fields: 字段列表
            conditions: 筛选条件
            **kwargs: 其他参数

        返回:
            pd.DataFrame: 参考数据
        """
        # 准备参数
        params = {}
        if fields:
            params['fields'] = ','.join(fields)
        if conditions:
            params.update(conditions)
        params.update(kwargs)

        # 运行异步方法
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                self._make_api_request(data_type, params)
            )
        finally:
            loop.close()

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        返回:
            Dict[str, Any]: 性能统计数据
        """
        total_requests = self.stats['total_requests']
        if total_requests == 0:
            return {
                'total_requests': 0,
                'success_rate': 0.0,
                'avg_response_time': 0.0,
                'concurrent_requests': 0
            }

        return {
            'total_requests': total_requests,
            'successful_requests': self.stats['successful_requests'],
            'failed_requests': self.stats['failed_requests'],
            'success_rate': (self.stats['successful_requests'] / total_requests) * 100,
            'avg_response_time': self.stats['total_response_time'] / total_requests,
            'concurrent_requests': self.stats['concurrent_requests'],
            'adaptive_limiter_stats': {
                'current_level': self.adaptive_limiter.current_level,
                'total_samples': len(self.adaptive_limiter.recent_response_times),
                'success_rate': (
                    self.adaptive_limiter.success_count /
                    self.adaptive_limiter.total_count * 100
                ) if self.adaptive_limiter.total_count > 0 else 0.0
            }
        }

    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_response_time': 0.0,
            'concurrent_requests': 0
        }

        # 重置自适应限流器统计
        self.adaptive_limiter.recent_response_times = []
        self.adaptive_limiter.success_count = 0
        self.adaptive_limiter.total_count = 0

    # 实现DataSourceInterface的抽象方法

    def get_data(self, **kwargs) -> pd.DataFrame:
        """
        获取数据的通用方法（同步接口）

        参数:
            **kwargs: 查询参数

        返回:
            pd.DataFrame: 获取的数据
        """
        api_name = kwargs.get('api_name', 'daily')

        # 检查是否已有运行中的事件循环
        try:
            # 尝试获取当前运行的事件循环
            current_loop = asyncio.get_running_loop()
            # 如果有运行中的循环，创建任务
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(self._run_async_in_thread, api_name, kwargs)
                return future.result()
        except RuntimeError:
            # 没有运行中的事件循环，创建新的
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(
                    self._make_api_request(api_name, kwargs)
                )
            finally:
                loop.close()
                asyncio.set_event_loop(None)

    def _run_async_in_thread(self, api_name: str, kwargs: Dict[str, Any]) -> pd.DataFrame:
        """在新线程中运行异步方法"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                self._make_api_request(api_name, kwargs)
            )
        finally:
            loop.close()

    def get_trade_calendar(
        self,
        exchange: str = None,
        start_date: str = None,
        end_date: str = None
    ) -> pd.DataFrame:
        """
        获取交易日历

        参数:
            exchange: 交易所代码
            start_date: 开始日期
            end_date: 结束日期

        返回:
            pd.DataFrame: 交易日历数据
        """
        params = {}
        if exchange:
            params['exchange'] = exchange
        if start_date:
            params['start_date'] = start_date
        if end_date:
            params['end_date'] = end_date

        return self.get_reference_data('trade_cal', conditions=params)

    def get_daily_bars(
        self,
        ts_code: str = None,
        trade_date: str = None,
        start_date: str = None,
        end_date: str = None
    ) -> pd.DataFrame:
        """
        获取股票日线数据

        参数:
            ts_code: 股票代码
            trade_date: 交易日期
            start_date: 开始日期
            end_date: 结束日期

        返回:
            pd.DataFrame: 日线数据
        """
        if ts_code:
            symbols = [ts_code]
        else:
            symbols = []

        return self.get_market_data(
            symbols=symbols,
            data_type='daily',
            start_date=start_date,
            end_date=end_date
        )

    def get_index_data(
        self,
        ts_code: str = None,
        trade_date: str = None,
        start_date: str = None,
        end_date: str = None
    ) -> pd.DataFrame:
        """
        获取指数数据

        参数:
            ts_code: 指数代码
            trade_date: 交易日期
            start_date: 开始日期
            end_date: 结束日期

        返回:
            pd.DataFrame: 指数数据
        """
        params = {}
        if ts_code:
            params['ts_code'] = ts_code
        if trade_date:
            params['trade_date'] = trade_date
        if start_date:
            params['start_date'] = start_date
        if end_date:
            params['end_date'] = end_date

        return self.get_reference_data('index_daily', conditions=params)
