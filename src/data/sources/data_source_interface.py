"""
数据源接口抽象类：定义所有数据源接口的通用规范
- 提供统一的数据获取方法
- 定义数据源连接和断开方法
- 支持数据请求参数标准化
- 提供数据源状态查询功能
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, TypeVar, Generic
import pandas as pd

# 泛型参数定义
T = TypeVar('T')

class DataSourceException(Exception):
    """数据源异常的基类"""
    pass

class ConnectionError(DataSourceException):
    """数据源连接异常"""
    pass

class DataFetchError(DataSourceException):
    """数据获取异常"""
    pass

class AuthenticationError(DataSourceException):
    """身份验证异常"""
    pass

class RateLimitError(DataSourceException):
    """请求频率限制异常"""
    pass

class ConfigurationError(DataSourceException):
    """配置错误异常"""
    pass

class DataSourceInterface(ABC):
    """
    数据源接口抽象类，所有数据源适配器必须实现此接口
    定义了数据源的基本操作，包括连接、断开、数据获取等
    """
    
    @abstractmethod
    def connect(self) -> bool:
        """
        连接到数据源
        
        返回：
            bool: 是否连接成功
            
        异常：
            ConnectionError: 连接失败时抛出
            AuthenticationError: 身份验证失败时抛出
            ConfigurationError: 配置错误时抛出
        """
        pass
    
    @abstractmethod
    def disconnect(self) -> bool:
        """
        断开数据源连接
        
        返回：
            bool: 是否断开成功
            
        异常：
            ConnectionError: 断开连接失败时抛出
        """
        pass
    
    @abstractmethod
    def get_data(self, **kwargs) -> pd.DataFrame:
        """
        获取数据的通用方法
        
        参数：
            **kwargs: 查询参数，根据具体数据源确定
            
        返回：
            pd.DataFrame: 获取的数据
            
        异常：
            DataFetchError: 数据获取失败时抛出
            RateLimitError: 超过请求限制时抛出
            ValueError: 参数无效时抛出
        """
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """
        检查数据源连接状态
        
        返回：
            bool: 是否已连接
        """
        pass
    
    @abstractmethod
    def get_trade_calendar(self, exchange: str = None, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        获取交易日历
        
        参数:
            exchange: 交易所代码，例如'SSE'（上海证券交易所）
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
            
        返回:
            pd.DataFrame: 交易日历数据，包含字段:
                - cal_date: 日历日期
                - is_open: 是否交易 0休市 1交易
        """
        pass
    
    @abstractmethod
    def get_daily_bars(self, ts_code: str = None, trade_date: str = None, 
                     start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        获取股票日线数据
        
        参数:
            ts_code: 股票代码，例如'000001.SZ'
            trade_date: 交易日期，格式YYYYMMDD，二选一
            start_date: 开始日期，格式YYYYMMDD，与trade_date二选一
            end_date: 结束日期，格式YYYYMMDD，可选
            
        返回:
            pd.DataFrame: 日线数据，包含字段:
                - ts_code: 股票代码
                - trade_date: 交易日期
                - open: 开盘价
                - high: 最高价
                - low: 最低价
                - close: 收盘价
                - vol: 成交量
                - amount: 成交额
                - ...其他字段
        """
        pass
    
    @abstractmethod
    def get_index_data(self, ts_code: str = None, trade_date: str = None, 
                      start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        获取指数数据
        
        参数:
            ts_code: 指数代码，例如'000300.SH'
            trade_date: 交易日期，格式YYYYMMDD，二选一
            start_date: 开始日期，格式YYYYMMDD，与trade_date二选一
            end_date: 结束日期，格式YYYYMMDD，可选
            
        返回:
            pd.DataFrame: 指数数据，包含字段:
                - ts_code: 指数代码
                - trade_date: 交易日期
                - open: 开盘点位
                - high: 最高点位
                - low: 最低点位
                - close: 收盘点位
                - vol: 成交量
                - amount: 成交额
                - ...其他字段
        """
        pass
    
    def validate_params(self, required_params: List[str], provided_params: Dict[str, Any]) -> bool:
        """
        验证查询参数是否包含所有必需参数
        
        参数：
            required_params: 必需的参数列表
            provided_params: 提供的参数字典
            
        返回：
            bool: 参数是否有效
            
        异常：
            ValueError: 缺少必需参数时抛出
        """
        missing_params = [param for param in required_params if param not in provided_params]
        if missing_params:
            raise ValueError(f"缺少必需参数: {', '.join(missing_params)}")
        return True
    
    def validate_date_format(self, date_str: str, format: str = "%Y%m%d") -> bool:
        """
        验证日期字符串格式是否正确
        
        参数：
            date_str: 日期字符串
            format: 预期的日期格式，默认为"%Y%m%d"
            
        返回：
            bool: 格式是否正确
            
        异常：
            ValueError: 日期格式不正确时抛出
        """
        from datetime import datetime
        try:
            datetime.strptime(date_str, format)
            return True
        except ValueError:
            raise ValueError(f"日期格式不正确: {date_str}，应为{format}格式")
    
    def check_not_future_date(self, date_str: str, format: str = "%Y%m%d") -> bool:
        """
        检查日期是否不是未来日期
        
        参数：
            date_str: 日期字符串
            format: 日期格式，默认为"%Y%m%d"
            
        返回：
            bool: 是否不是未来日期
            
        异常：
            ValueError: 日期是未来日期时抛出
        """
        from datetime import datetime
        date = datetime.strptime(date_str, format)
        if date > datetime.now():
            raise ValueError(f"日期不能是未来日期: {date_str}")
        return True
    
    def format_date(self, date_str: str, input_format: str = None, output_format: str = "%Y%m%d") -> str:
        """
        格式化日期字符串
        
        参数：
            date_str: 日期字符串
            input_format: 输入日期格式，如果为None则自动检测
            output_format: 输出日期格式，默认为"%Y%m%d"
            
        返回：
            str: 格式化后的日期字符串
            
        异常：
            ValueError: 无法解析日期时抛出
        """
        from datetime import datetime
        
        # 自动检测日期格式
        if input_format is None:
            formats = ["%Y-%m-%d", "%Y/%m/%d", "%Y%m%d", "%Y.%m.%d"]
            for fmt in formats:
                try:
                    date = datetime.strptime(date_str, fmt)
                    return date.strftime(output_format)
                except ValueError:
                    continue
            raise ValueError(f"无法解析日期: {date_str}")
        else:
            try:
                date = datetime.strptime(date_str, input_format)
                return date.strftime(output_format)
            except ValueError:
                raise ValueError(f"无法解析日期: {date_str}，格式为{input_format}")
