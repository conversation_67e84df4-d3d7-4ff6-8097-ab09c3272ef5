"""
本地文件数据源适配器
- 支持读取多种格式文件（CSV, Excel, Parquet等）
- 支持数据筛选和转换
- 支持批量读取和缓存
"""

import os
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple, Callable
import logging
from datetime import datetime
import glob
from functools import lru_cache

from src.data.sources.data_source_interface import DataSourceInterface
# {{ AURA-X: Modify - 修复导入路径，使用绝对导入. Approval: 寸止(ID:深度架构复查修复). }}
from src.utils.config.config_factory import ConfigFactory
from src.utils.temporary.file_utils import FileUtils

class FileError(Exception):
    """文件错误基类"""
    pass

class FileNotFoundError(FileError):
    """文件不存在错误"""
    pass

class FileFormatError(FileError):
    """文件格式错误"""
    pass

class LocalFileAdapter(DataSourceInterface):
    """
    本地文件数据源适配器，用于从本地文件系统读取数据
    
    支持的文件格式:
    - CSV
    - Excel (xls, xlsx)
    - Parquet
    - JSON
    - HDF5
    - SQLite数据库文件
    """
    
    def __init__(self, base_dir: str = None, config_file: str = 'data_source', env: str = None):
        """
        初始化本地文件数据源适配器
        
        参数:
            base_dir: 基础数据目录，默认从配置文件获取
            config_file: 配置文件名，默认为'data_source'
            env: 环境名称，用于加载不同环境配置
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.connected = False
        
        # 获取项目根目录作为配置目录 - 修复配置目录路径
        config_dir = os.path.abspath(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'config'))
        
        # 加载配置
        self.config_factory = ConfigFactory()
        try:
            self.config = self.config_factory.load_config(config_file, config_dir)
            self.logger.info(f"已加载配置: {config_file}")
        except Exception as e:
            self.logger.warning(f"找不到配置文件: {config_file}，使用默认配置。错误: {str(e)}")
            self.config = {}
        
        # 获取本地文件配置
        self.file_config = self.config.get('local_file', {})
        
        # 设置基础目录
        if base_dir:
            self.base_dir = base_dir
        else:
            self.base_dir = self.file_config.get('base_path', 'data')
            
        self.logger.info(f"设置基础目录: {self.base_dir}")
        
        # 确保目录存在
        os.makedirs(self.base_dir, exist_ok=True)
        
        # 文件路径映射
        self.path_mapping = self.file_config.get('paths', {})
        self.format_config = self.file_config.get('format', {})
        
        # 缓存配置
        self.use_cache = self.file_config.get('use_cache', True)
        self.cache_ttl = self.file_config.get('cache_ttl', 3600)  # 默认缓存1小时
        self.cache = {}
        
        # 初始化文件格式处理器
        self._init_format_handlers()
        
        # 连接标志
        self.connected = True
        
        self.logger.info(f"LocalFileAdapter初始化完成，基础目录: {self.base_dir}")
    
    def _init_format_handlers(self):
        """初始化各种文件格式的处理方法"""
        self.format_handlers = {
            'csv': self._read_csv,
            'excel': self._read_excel,
            'xls': self._read_excel,
            'xlsx': self._read_excel,
            'parquet': self._read_parquet,
            'json': self._read_json,
            'hdf': self._read_hdf,
            'hdf5': self._read_hdf,
            'h5': self._read_hdf,
            'sqlite': self._read_sqlite,
            'db': self._read_sqlite
        }
    
    def connect(self, **kwargs):
        """
        连接到本地文件系统
        
        参数:
            **kwargs: 可选参数，用于覆盖默认配置
        
        返回:
            bool: 连接是否成功
            
        异常:
            FileError: 连接失败时抛出
        """
        try:
            # 更新配置
            if kwargs:
                for key, value in kwargs.items():
                    if key == 'base_dir' and value:
                        self.base_dir = value
                        os.makedirs(self.base_dir, exist_ok=True)
            
            # 检查基础目录是否存在
            if not os.path.exists(self.base_dir):
                raise FileNotFoundError(f"基础数据目录不存在: {self.base_dir}")
            
            self.connected = True
            self.logger.info(f"已连接到本地文件数据源: {self.base_dir}")
            return True
        except Exception as e:
            self.connected = False
            self.logger.error(f"连接本地文件数据源失败: {str(e)}")
            raise FileError(f"连接本地文件数据源失败: {str(e)}")
    
    def disconnect(self):
        """
        断开与本地文件系统的连接
        
        返回:
            bool: 是否成功断开连接
        """
        self.connected = False
        self.logger.info("已断开与本地文件数据源的连接")
        return True
    
    def is_connected(self) -> bool:
        """
        检查是否已连接到本地文件系统
        
        返回:
            bool: 是否已连接
        """
        return self.connected
    
    def get_data(self, 
                 data_type: str, 
                 params: Dict[str, Any] = None, 
                 fields: List[str] = None, 
                 filters: Dict[str, Any] = None,
                 start_date: str = None,
                 end_date: str = None,
                 **kwargs) -> pd.DataFrame:
        """
        获取指定类型的数据
        
        参数:
            data_type: 数据类型，对应配置中的键或直接的文件路径
            params: 额外的查询参数
            fields: 需要返回的字段列表
            filters: 过滤条件
            start_date: 开始日期（如适用）
            end_date: 结束日期（如适用）
            **kwargs: 额外参数
        
        返回:
            pd.DataFrame: 查询结果数据
            
        异常:
            FileError: 数据获取失败时抛出
        """
        if not self.is_connected():
            self.connect()
        
        try:
            params = params or {}
            
            # 生成缓存键
            cache_key = f"{data_type}_{str(params)}_{str(fields)}_{str(filters)}_{start_date}_{end_date}"
            
            # 检查缓存
            if self.use_cache and cache_key in self.cache:
                cache_entry = self.cache[cache_key]
                if datetime.now().timestamp() - cache_entry['timestamp'] < self.cache_ttl:
                    return cache_entry['data']
            
            # 获取文件路径
            file_path = self._get_file_path(data_type, params)
            
            # 读取数据
            df = self._read_file(file_path, params)
            
            # 应用日期过滤
            if df is not None and df.shape[0] > 0:
                df = self._apply_date_filter(df, start_date, end_date)
                
                # 应用字段过滤
                if fields:
                    df = self._select_fields(df, fields)
                
                # 应用条件过滤
                if filters:
                    df = self._apply_filters(df, filters)
            
            # 更新缓存
            if self.use_cache:
                self.cache[cache_key] = {
                    'data': df,
                    'timestamp': datetime.now().timestamp()
                }
            
            return df
        except Exception as e:
            self.logger.error(f"获取数据失败: {str(e)}")
            raise FileError(f"获取数据失败: {str(e)}")
    
    def _get_file_path(self, data_type: str, params: Dict[str, Any] = None) -> str:
        """
        获取文件路径
        
        参数:
            data_type: 数据类型
            params: 参数字典
        
        返回:
            str: 文件路径或匹配文件列表
        
        异常:
            FileError: 无法确定文件路径时抛出
        """
        params = params or {}
        
        # 检查是否直接提供了文件路径
        if os.path.isfile(data_type):
            return data_type
        elif data_type.endswith(('.csv', '.xlsx', '.xls', '.parquet', '.json', '.hdf', '.hdf5', '.h5', '.sqlite', '.db')):
            if os.path.isabs(data_type):
                return data_type
            else:
                file_path = os.path.join(self.base_dir, data_type)
                if os.path.isfile(file_path):
                    return file_path
        
        # 从配置中获取文件路径模板
        path_templates = self.path_mapping.get(data_type, [])
        if not path_templates:
            # 如果没有模板，尝试直接使用数据类型作为相对路径
            default_path = os.path.join(self.base_dir, data_type)
            if os.path.exists(default_path):
                return default_path
            else:
                raise FileError(f"未找到数据类型对应的文件路径配置: {data_type}")
        
        # 如果提供了ts_code参数，优先处理
        if 'ts_code' in params:
            ts_code = params['ts_code']
            # 检查是否直接提供了文件名
            if ts_code.endswith(('.csv', '.xlsx', '.xls', '.parquet', '.json', '.hdf', '.hdf5', '.h5', '.sqlite', '.db')):
                # 检查文件是否存在于配置的目录中
                for path_template in path_templates:
                    dir_path = os.path.dirname(os.path.join(self.base_dir, path_template))
                    file_path = os.path.join(dir_path, ts_code)
                    if os.path.isfile(file_path):
                        return file_path
                
                # 尝试直接在base_dir下查找
                file_path = os.path.join(self.base_dir, ts_code)
                if os.path.isfile(file_path):
                    return file_path
                
                # 如果请求的文件不存在，应该抛出异常
                raise FileNotFoundError(f"文件不存在: {ts_code}")
            
            # 如果ts_code不是文件名，尝试在路径模板中替换
            for path_template in path_templates:
                if '{ts_code}' in path_template:
                    file_path = path_template.replace('{ts_code}', ts_code)
                    file_path = os.path.join(self.base_dir, file_path)
                    if os.path.isfile(file_path):
                        return file_path
        
        # 检查是否需要返回所有匹配的文件（通配符模式）
        all_files = []
        # 处理每个路径模板
        for path_template in path_templates:
            # 替换路径中的参数
            template = path_template
            for key, value in params.items():
                template = template.replace(f"{{{key}}}", str(value))
            
            # 如果有通配符，查找匹配的文件
            if '*' in template:
                matching_files = glob.glob(os.path.join(self.base_dir, template))
                if matching_files:
                    all_files.extend(matching_files)
            else:
                # 直接检查文件是否存在
                file_path = os.path.join(self.base_dir, template)
                if os.path.isfile(file_path):
                    all_files.append(file_path)
        
        # 如果找到了匹配的文件
        if all_files:
            # 如果是通配符路径且没有指定特定文件，返回所有匹配的文件
            if len(all_files) > 1 and not params.get('ts_code'):
                return all_files
            else:
                # 否则返回最新的文件
                return max(all_files, key=os.path.getmtime)
        
        # 如果所有模板都不匹配，尝试使用通配符查找匹配的文件
        for path_template in path_templates:
            dir_name = os.path.dirname(os.path.join(self.base_dir, path_template))
            if os.path.isdir(dir_name):
                for ext in ['.csv', '.xlsx', '.xls', '.parquet', '.json', '.hdf', '.hdf5', '.h5', '.sqlite', '.db']:
                    pattern = os.path.join(dir_name, f"*{ext}")
                    matching_files = glob.glob(pattern)
                    if matching_files:
                        if len(matching_files) > 1 and not params.get('ts_code'):
                            return matching_files
                        else:
                            return max(matching_files, key=os.path.getmtime)
        
        raise FileNotFoundError(f"未找到匹配的文件: {data_type}")
    
    def _read_file(self, file_path: str, params: Dict[str, Any] = None) -> pd.DataFrame:
        """
        读取文件内容
        
        参数:
            file_path: 文件路径或文件路径列表
            params: 读取参数
        
        返回:
            pd.DataFrame: 读取的数据
            
        异常:
            FileNotFoundError: 文件不存在时抛出
            FileFormatError: 文件格式不支持或解析出错时抛出
        """
        params = params or {}
        
        # 如果是文件路径列表，读取所有文件并合并
        if isinstance(file_path, list):
            if not file_path:
                raise FileNotFoundError("未找到匹配的文件")
                
            dfs = []
            for path in file_path:
                if not os.path.exists(path):
                    self.logger.warning(f"文件不存在: {path}")
                    continue
                    
                try:
                    df = self._read_single_file(path, params)
                    if df is not None and not df.empty:
                        dfs.append(df)
                except Exception as e:
                    self.logger.warning(f"读取文件失败: {path}, 错误: {str(e)}")
                    
            if not dfs:
                raise FileNotFoundError("所有文件读取失败")
                
            # 合并数据框
            return pd.concat(dfs, ignore_index=True)
        else:
            return self._read_single_file(file_path, params)
            
    def _read_single_file(self, file_path: str, params: Dict[str, Any] = None) -> pd.DataFrame:
        """读取单个文件"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 获取文件扩展名
        _, ext = os.path.splitext(file_path)
        ext = ext.lstrip('.').lower()
        
        # 调用对应的格式处理函数
        handler = self.format_handlers.get(ext)
        if not handler:
            raise FileFormatError(f"不支持的文件格式: {ext}")
        
        return handler(file_path, params or {})
    
    def _read_csv(self, file_path: str, params: Dict[str, Any]) -> pd.DataFrame:
        """读取CSV文件"""
        read_params = {
            'encoding': params.get('encoding', 'utf-8'),
            'sep': params.get('sep', ','),
            'header': params.get('header', 0),
            'index_col': params.get('index_col', None),
            'parse_dates': params.get('parse_dates', [])
        }
        
        if 'date_parser' in params:
            read_params['date_parser'] = params['date_parser']
        
        return pd.read_csv(file_path, **read_params)
    
    def _read_excel(self, file_path: str, params: Dict[str, Any]) -> pd.DataFrame:
        """读取Excel文件"""
        read_params = {
            'sheet_name': params.get('sheet_name', 0),
            'header': params.get('header', 0),
            'index_col': params.get('index_col', None),
            'parse_dates': params.get('parse_dates', [])
        }
        
        return pd.read_excel(file_path, **read_params)
    
    def _read_parquet(self, file_path: str, params: Dict[str, Any]) -> pd.DataFrame:
        """读取Parquet文件"""
        read_params = {
            'columns': params.get('columns', None),
            'engine': params.get('engine', 'auto')
        }
        
        return pd.read_parquet(file_path, **read_params)
    
    def _read_json(self, file_path: str, params: Dict[str, Any]) -> pd.DataFrame:
        """读取JSON文件"""
        read_params = {
            'orient': params.get('orient', 'columns'),
            'lines': params.get('lines', False),
            'encoding': params.get('encoding', 'utf-8')
        }
        
        return pd.read_json(file_path, **read_params)
    
    def _read_hdf(self, file_path: str, params: Dict[str, Any]) -> pd.DataFrame:
        """读取HDF5文件"""
        key = params.get('key', None)
        if not key:
            raise ValueError("读取HDF5文件需要指定'key'参数")
            
        read_params = {
            'key': key,
            'where': params.get('where', None),
            'columns': params.get('columns', None)
        }
        
        return pd.read_hdf(file_path, **read_params)
    
    def _read_sqlite(self, file_path: str, params: Dict[str, Any]) -> pd.DataFrame:
        """读取SQLite数据库文件"""
        sql_query = params.get('sql', None)
        table_name = params.get('table', None)
        
        if not sql_query and not table_name:
            raise ValueError("读取SQLite文件需要指定'sql'或'table'参数")
        
        import sqlite3
        
        conn = sqlite3.connect(file_path)
        
        if sql_query:
            return pd.read_sql_query(sql_query, conn)
        else:
            return pd.read_sql_table(table_name, conn)
    
    def _apply_date_filter(self, df: pd.DataFrame, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        应用日期过滤
        
        参数:
            df: 数据框
            start_date: 开始日期
            end_date: 结束日期
        
        返回:
            pd.DataFrame: 过滤后的数据框
        """
        if df.empty:
            return df
            
        # 寻找日期列
        date_cols = [col for col in df.columns if any(date_term in col.lower() for date_term in 
                    ['date', 'time', 'day', 'month', 'year', 'dt', '日期', '时间', '日'])]
        
        if not date_cols:
            return df
            
        date_col = date_cols[0]  # 使用第一个匹配的日期列
        
        # 确保日期列是datetime类型
        if df[date_col].dtype != 'datetime64[ns]':
            try:
                df[date_col] = pd.to_datetime(df[date_col])
            except:
                self.logger.warning(f"无法将列 {date_col} 转换为日期类型，跳过日期过滤")
                return df
        
        # 应用过滤
        if start_date:
            start_date = pd.to_datetime(start_date)
            df = df[df[date_col] >= start_date]
            
        if end_date:
            end_date = pd.to_datetime(end_date)
            df = df[df[date_col] <= end_date]
            
        return df
    
    def _select_fields(self, df: pd.DataFrame, fields: List[str]) -> pd.DataFrame:
        """
        选择指定字段
        
        参数:
            df: 数据框
            fields: 字段列表
        
        返回:
            pd.DataFrame: 选择指定字段后的数据框
        """
        if not fields:
            return df
            
        # 确保所有字段存在
        existing_fields = [f for f in fields if f in df.columns]
        if len(existing_fields) < len(fields):
            missing = set(fields) - set(existing_fields)
            self.logger.warning(f"以下字段不存在: {missing}")
            
        return df[existing_fields] if existing_fields else df
    
    def _apply_filters(self, df: pd.DataFrame, filters: Dict[str, Any]) -> pd.DataFrame:
        """
        应用过滤条件
        
        参数:
            df: 数据框
            filters: 过滤条件字典，格式如 
                  {'column': value} 
                  {'column': {'op': '>', 'value': 100}}
                  {'column': {'>': 100}}
        
        返回:
            pd.DataFrame: 过滤后的数据框
        """
        if not filters:
            return df
            
        result_df = df.copy()
        
        for col, condition in filters.items():
            if col not in result_df.columns:
                self.logger.warning(f"过滤列不存在: {col}")
                continue
                
            # 简单相等过滤
            if not isinstance(condition, dict):
                result_df = result_df[result_df[col] == condition]
                continue
                
            # 新格式: {'column': {'op': '>', 'value': 100}}
            if 'op' in condition and 'value' in condition:
                op = condition['op']
                value = condition['value']
            # 旧格式: {'column': {'>': 100}}
            else:
                # 找到操作符和值
                for op_key, value in condition.items():
                    op = op_key
                    break
                else:
                    self.logger.warning(f"无效的过滤条件: {condition}")
                    continue
            
            if value is None:
                continue
                
            if op == '=' or op == '==':
                result_df = result_df[result_df[col] == value]
            elif op == '!=':
                result_df = result_df[result_df[col] != value]
            elif op == '>':
                result_df = result_df[result_df[col] > value]
            elif op == '>=':
                result_df = result_df[result_df[col] >= value]
            elif op == '<':
                result_df = result_df[result_df[col] < value]
            elif op == '<=':
                result_df = result_df[result_df[col] <= value]
            elif op == 'in':
                result_df = result_df[result_df[col].isin(value)]
            elif op == 'not in':
                result_df = result_df[~result_df[col].isin(value)]
            elif op == 'contains':
                if isinstance(value, str):
                    result_df = result_df[result_df[col].astype(str).str.contains(value)]
            elif op == 'starts_with':
                if isinstance(value, str):
                    result_df = result_df[result_df[col].astype(str).str.startswith(value)]
            elif op == 'ends_with':
                if isinstance(value, str):
                    result_df = result_df[result_df[col].astype(str).str.endswith(value)]
            else:
                self.logger.warning(f"不支持的操作符: {op}")
                
        return result_df
        
    def get_fields(self, data_type: str, params: Dict[str, Any] = None) -> List[str]:
        """
        获取数据字段列表
        
        参数:
            data_type: 数据类型
            params: 查询参数
            
        返回:
            List[str]: 字段列表
        """
        try:
            # 获取少量数据样本来获取字段列表
            df = self.get_data(data_type, params, None, None, None, None, limit=1)
            return df.columns.tolist() if df is not None and not df.empty else []
        except Exception as e:
            self.logger.error(f"获取字段列表失败: {str(e)}")
            return []
    
    def check_connectivity(self) -> bool:
        """
        检查数据源连接状态
        
        返回:
            bool: 是否已连接
        """
        # 检查基础目录是否存在
        if not os.path.exists(self.base_dir):
            self.connected = False
            return False
            
        # 检查是否有权限
        try:
            test_file = os.path.join(self.base_dir, '.connectivity_test')
            with open(test_file, 'w') as f:
                f.write('test')
            FileUtils.remove_file(test_file)
            self.connected = True
            return True
        except Exception as e:
            self.logger.error(f"连接检查失败: {str(e)}")
            self.connected = False
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取数据源状态
        
        返回:
            Dict[str, Any]: 状态信息
        """
        total_space = 0
        used_space = 0
        file_count = 0
        
        try:
            for root, dirs, files in os.walk(self.base_dir):
                file_count += len(files)
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.exists(file_path):
                        used_space += os.path.getsize(file_path)
        except:
            pass
        
        return {
            'connected': self.is_connected(),
            'base_dir': self.base_dir,
            'base_path': self.base_dir,  # 添加 base_path 字段兼容测试
            'file_count': file_count,
            'used_space_mb': round(used_space / (1024 * 1024), 2),
            'last_check': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'available_types': ['daily', 'weekly', 'monthly', 'index', 'fundamental']  # 添加可用数据类型
        }
        
    def clear_cache(self):
        """
        清除数据缓存
        
        返回:
            bool: 是否成功清除缓存
        """
        self.cache = {}
        self.logger.info("数据缓存已清除")
        return True

