"""
Tushare数据适配器：连接Tushare数据源获取A股数据
- 封装Tushare API接口调用
- 处理数据格式转换
- 实现数据缓存和失败重试
- 提供数据过滤和预处理功能
"""

import os
import time
import pickle
import random
import hashlib
from typing import Dict, Any, List
import pandas as pd
from datetime import datetime, timedelta
import tushare as ts
import logging
import threading
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from collections import deque
from statistics import mean, median
# {{ AURA-X: Add - 导入统一错误处理器，消除重复代码. Approval: 寸止(ID:重构阶段3). }}
from src.utils.error_handling import DataFetchErrorHandler, retry_on_error, RetryStrategy, ErrorCategory

from .data_source_interface import DataSourceInterface, ConnectionError, DataFetchError, AuthenticationError, RateLimitError


class TushareErrorHandler(DataFetchErrorHandler):
    """Tushare专用错误处理器"""

    def _build_error_mapping(self):
        """构建Tushare特定的错误分类映射"""
        base_mapping = super()._build_error_mapping()
        base_mapping.update({
            RateLimitError: ErrorCategory.RATE_LIMIT,
            AuthenticationError: ErrorCategory.AUTHENTICATION,
        })
        return base_mapping

    def should_retry(self, exception: Exception, attempt: int) -> bool:
        """判断Tushare错误是否应该重试"""
        error_str = str(exception).lower()

        # 认证错误不重试
        if isinstance(exception, AuthenticationError):
            return False

        # 频率限制错误可以重试
        if any(keyword in error_str for keyword in ['频率', 'rate', 'limit', '限制']):
            return True

        # 网络相关错误可以重试
        if any(keyword in error_str for keyword in ['network', 'connection', 'timeout', '网络', '连接', '超时']):
            return True

        # 调用父类方法
        return super().should_retry(exception, attempt)

    def calculate_delay(self, attempt: int, error_category=None) -> float:
        """计算Tushare特定的重试延迟"""
        base_delay = super().calculate_delay(attempt, error_category)

        # 对于限流错误，使用更长的延迟
        if error_category == ErrorCategory.RATE_LIMIT:
            base_delay *= 3  # 限流错误延迟增加3倍

        return base_delay
from .data_source_interface import DataSourceException
from src.utils.temporary.file_utils import FileUtils
from ..fetcher.rate_limiter import RateLimiter, AdaptiveRateLimiter

class TushareError(DataFetchError):
    """Tushare 数据获取错误"""
    pass


class TushareAdapter(DataSourceInterface):
    """
    Tushare数据适配器类，实现Tushare的数据获取和处理
    """
    
    def __init__(self, token: str = None, timeout: int = 30, cache_dir: str = None, retry_count: int = 3, retry_interval: int = 5):
        """
        初始化Tushare适配器

        参数：
            token: Tushare API令牌
            timeout: 连接超时时间（秒）
            cache_dir: 缓存目录，若为None则使用"~/.cache/quantification/tushare"
            retry_count: 失败重试次数
            retry_interval: 重试间隔（秒）
        """
        self.token = token
        self.timeout = timeout
        self.pro = None

        # 配置日志记录器
        self.logger = logging.getLogger('TushareAdapter')

        # {{ AURA-X: Add - 配置详细性能日志记录. Source: 详细日志分析任务 }}
        # 配置性能分析日志记录器
        self.perf_logger = logging.getLogger('TushareAdapter.Performance')
        self.perf_logger.setLevel(logging.DEBUG)

        # 确保logs目录存在
        import os
        os.makedirs('logs', exist_ok=True)

        # 添加性能日志文件处理器
        if not self.perf_logger.handlers:
            perf_handler = logging.FileHandler('logs/performance_analysis.log', mode='a', encoding='utf-8')
            perf_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            perf_handler.setFormatter(perf_formatter)
            self.perf_logger.addHandler(perf_handler)

        # {{ AURA-X: Add - 初始化自适应智能限流系统（重构版本）. Source: 架构优化重构方案 }}
        # 创建基础限流器
        base_limiter = RateLimiter(max_calls=800, period=60.0, retry_after=1.0)
        # 初始化自适应限流器
        self.adaptive_limiter = AdaptiveRateLimiter(base_limiter, self.logger)

        # {{ AURA-X: Restore - 恢复HTTP连接池优化到最优配置状态. Source: HTTP连接池优化性能基准测试完成 }}
        # 恢复HTTP连接池优化
        self._init_http_session()
        
        # 设置缓存目录
        if cache_dir is None:
            home_dir = os.path.expanduser("~")
            self.cache_dir = os.path.join(home_dir, ".cache", "quantification", "tushare")
        else:
            self.cache_dir = cache_dir
            
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # 设置重试参数
        self.retry_count = retry_count
        self.retry_interval = retry_interval
        
        # {{ AURA-X: Modify - 增强限流配置，支持不同接口的限流策略. Source: Tushare API限流分析 }}
        # 初始化API调用频率限制
        self.rate_limit = {
            'limit': 800,       # 每分钟最大请求数（通用）
            'window': 60,       # 时间窗口（秒）
            'requests': [],     # 请求时间戳列表
            'period': 60,       # 周期（秒）
            'api_limits': {     # 特定API的限流配置
                'daily_basic': 200,  # 市值数据接口限制为每分钟200次
                'daily': 800,        # 日线数据接口限制为每分钟800次
                'income': 500,       # 利润表接口限制
                'balancesheet': 500, # 资产负债表接口限制
                'cashflow': 500      # 现金流量表接口限制
            },
            'api_requests': {}  # 按API分类的请求时间戳
        }
        
        # 初始化统计信息
        self.stats = {
            'api_calls': {
                'total': 0,
                'success': 0,
                'failure': 0,
                'retry': 0,
                'rate_limited': 0,
                'avg_wait_time': 0.0
            }
        }
        
        # API映射
        self._api_map = {
            # 行情数据
            'daily': 'daily',
            'weekly': 'weekly',
            'monthly': 'monthly',
            'adj_factor': 'adj_factor',
            'suspend': 'suspend',
            'daily_basic': 'daily_basic',
            'moneyflow': 'moneyflow',
            
            # 指数数据
            'index_daily': 'index_daily',
            'index_weekly': 'index_weekly',
            'index_monthly': 'index_monthly',
            'index_weight': 'index_weight',
            'index_dailybasic': 'index_dailybasic',
            
            # 财务数据
            'income': 'income',
            'balancesheet': 'balancesheet',
            'cashflow': 'cashflow',
            'express': 'express',
            'fina_indicator': 'fina_indicator',
            
            # 基本信息
            'stock_basic': 'stock_basic',
            'namechange': 'namechange',
            'hs_const': 'hs_const',
            'stock_company': 'stock_company',
            
            # 市场参考数据
            'trade_cal': 'trade_cal',
            'stk_limit': 'stk_limit',
            'stk_holder_number': 'stk_holder_number',
        }
        
        # 添加线程锁
        self._lock = threading.Lock()
        
    def set_retry_count(self, retry_count: int) -> None:
        """
        设置API调用重试次数
        
        参数:
            retry_count: 重试次数
        """
        self.logger.debug(f"设置重试次数: {retry_count}")
        self.retry_count = retry_count
    
    def set_retry_interval(self, retry_interval: int) -> None:
        """
        设置API调用重试间隔
        
        参数:
            retry_interval: 重试间隔（秒）
        """
        self.logger.debug(f"设置重试间隔: {retry_interval}秒")
        self.retry_interval = retry_interval
        
    def connect(self) -> bool:
        """
        连接到Tushare数据源
        
        返回：
            bool: 是否连接成功
            
        异常：
            ConnectionError: 连接失败时抛出
            AuthenticationError: 身份验证失败时抛出
        """
        if self.token is None:
            raise AuthenticationError("Tushare token未设置，请在初始化时提供token")
        
        try:
            # {{ AURA-X: Modify - 先创建连接，再应用HTTP会话优化. Source: HTTP连接池验证测试 }}
            # 使用更长的超时时间创建连接
            self.pro = ts.pro_api(self.token, timeout=max(60, self.timeout))

            # {{ AURA-X: Restore - 恢复HTTP会话优化到最优配置状态. Source: HTTP连接池优化性能基准测试完成 }}
            # 恢复HTTP会话优化
            self._patch_tushare_session()

            # 检查连接是否成功
            if self.pro is None:
                raise ConnectionError("无法创建Tushare API连接")

            # 测试连接是否成功
            try:
                _ = self.pro.trade_cal(exchange='SSE', start_date='20200101', end_date='20200110')
                self.logger.info("Tushare API连接成功，HTTP连接池已激活")
                return True
            except Exception as e:
                self.logger.error(f"Tushare API连接测试失败: {e}")
                if 'token' in str(e).lower() or 'auth' in str(e).lower():
                    raise AuthenticationError(f"Tushare认证失败: {str(e)}")
                else:
                    raise ConnectionError(f"连接Tushare失败: {str(e)}")
        except Exception as e:
            self.logger.error(f"创建Tushare API连接失败: {e}")
            if 'token' in str(e).lower() or 'auth' in str(e).lower():
                raise AuthenticationError(f"Tushare认证失败: {str(e)}")
            else:
                raise ConnectionError(f"连接Tushare失败: {str(e)}")
        
    def disconnect(self) -> bool:
        """
        断开Tushare数据源连接

        返回：
            bool: 是否断开成功
        """
        # {{ AURA-X: Modify - 在断开连接时关闭HTTP会话. Source: HTTP连接池优化方案 }}
        self.close_session()
        self.pro = None
        return True
        
    def get_data(self, api_name=None, **kwargs):
        """
        获取Tushare数据，带缓存和重试机制
        
        参数:
            api_name: API名称，如'daily'、'stock_basic'等
            **kwargs: API调用的具体参数
            
        返回:
            pd.DataFrame: 查询结果数据框
        """
        # 检查连接状态
        if not self.is_connected():
            self.logger.warning("数据源未连接，尝试重新连接")
            if not self.connect():
                raise DataSourceException("未连接到数据源")
        
        # {{ AURA-X: Modify - 使用新的按API分类的限流检查. Source: Tushare API限流分析 }}
        # 检查API调用频率限制
        wait_time = self._check_rate_limit(api_name)
        if wait_time > 0:
            self.logger.info(f"API {api_name} 限流等待 {wait_time:.2f} 秒")
            time.sleep(wait_time)
        
        # 初始化重试次数和退避时间
        retries = 0
        base_backoff_time = self.retry_interval
        max_backoff_time = 300  # 最大退避时间增加到5分钟
        
        # 添加重试日志
        self.logger.debug(f"准备调用API: {api_name}, 参数: {kwargs}, 最大重试次数: {self.retry_count}")
        
        # {{ AURA-X: Modify - 使用统一错误处理器，消除重复的重试代码. Approval: 寸止(ID:重构阶段3). }}
        # 创建Tushare专用错误处理器
        error_handler = TushareErrorHandler(
            max_retries=self.retry_count,
            base_delay=base_backoff_time,
            strategy=RetryStrategy.EXPONENTIAL,
            logger=self.logger
        )

        # 使用错误处理器执行API调用
        return error_handler.execute_with_retry(self._execute_api_call, api_name, **kwargs)

    def _execute_api_call(self, api_name: str, **kwargs) -> pd.DataFrame:
        """
        执行单次API调用（内部方法）

        参数：
            api_name: API名称
            **kwargs: API参数

        返回：
            pd.DataFrame: 数据
        """
        # 调用Tushare API
        self.logger.debug(f"调用API: {api_name}, 参数: {kwargs}")
        # 确保pro对象存在
        if self.pro is None:
            self.logger.warning("API客户端为空，尝试重新连接")
            if not self.connect():
                raise ConnectionError("无法连接到Tushare API")
        # {{ AURA-X: Modify - 使用_call_api方法调用API，支持按API分类的限流. Source: Tushare API限流分析 }}
        # 调用API
        if not hasattr(self.pro, api_name):
            raise ValueError(f"无效的API名称: {api_name}")
        api_method = getattr(self.pro, api_name)
        data = self._call_api(api_method, api_name, **kwargs)

        # {{ AURA-X: Add - 记录API请求到相应的请求列表. Source: Tushare API限流分析 }}
        # 记录API请求（已在_call_api中处理，这里无需重复）

        return data

    def is_connected(self) -> bool:
        """
        检查Tushare连接状态
        
        返回：
            bool: 是否已连接
        """
        return self.pro is not None
    
    def get_stock_basic(self, fields: str = None, **kwargs) -> pd.DataFrame:
        """
        获取股票基本信息
        
        参数：
            fields: 字段列表，逗号分隔
            **kwargs: 其他查询参数
            
        返回：
            pd.DataFrame: 股票基本信息
        """
        params = {'fields': fields} if fields else {}
        params.update(kwargs)
        return self.get_data('stock_basic', **params)
    
    def get_trade_calendar(self, exchange: str = 'SSE', start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        获取交易日历
        
        参数:
            exchange: 交易所代码，默认为'SSE'（上海证券交易所）
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
            
        返回:
            pd.DataFrame: 交易日历数据，包含字段:
                - cal_date: 日历日期
                - is_open: 是否交易 0休市 1交易
        """
        try:
            # 转换日期格式为Tushare API所需的YYYYMMDD格式
            if start_date:
                start_date = self._format_date_for_tushare(start_date)
            if end_date:
                end_date = self._format_date_for_tushare(end_date)

            # 确保已连接
            if not self.is_connected():
                self.logger.warning("数据源未连接，尝试重新连接")
                if not self.connect():
                    raise ConnectionError("无法连接到Tushare API")

            # 确保pro对象存在
            if self.pro is None:
                self.logger.warning("API客户端为空，尝试重新连接")
                if not self.connect():
                    raise ConnectionError("无法连接到Tushare API")

            # 调用Tushare API
            df = self.pro.trade_cal(exchange=exchange,
                              start_date=start_date,
                              end_date=end_date)
            
            # 检查结果
            if df is None or df.empty:
                self.logger.warning("获取交易日历返回空数据")
                return pd.DataFrame(columns=['cal_date', 'is_open'])
            
            return df
            
        except Exception as e:
            self.logger.error(f"获取交易日历失败: {e}")
            raise DataFetchError(f"获取交易日历失败: {e}")
            
    def get_daily_bars(self, ts_code: str = None, trade_date: str = None,
                      start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        获取股票日线数据
        
        参数:
            ts_code: 股票代码，例如'000001.SZ'
            trade_date: 交易日期，格式YYYYMMDD，二选一
            start_date: 开始日期，格式YYYYMMDD，与trade_date二选一
            end_date: 结束日期，格式YYYYMMDD，可选
            
        返回:
            pd.DataFrame: 日线数据，包含字段:
                - ts_code: 股票代码
                - trade_date: 交易日期
                - open: 开盘价
                - high: 最高价
                - low: 最低价
                - close: 收盘价
                - vol: 成交量
                - amount: 成交额
                - ...其他字段
        """
        # 转换日期格式为Tushare API所需的YYYYMMDD格式
        if trade_date:
            trade_date = self._format_date_for_tushare(trade_date)
        if start_date:
            start_date = self._format_date_for_tushare(start_date)
        if end_date:
            end_date = self._format_date_for_tushare(end_date)

        retry_count = 0
        max_retries = self.retry_count
        while retry_count <= max_retries:
            try:
                # 确保已连接
                if not self.is_connected():
                    self.logger.warning("数据源未连接，尝试重新连接")
                    if not self.connect():
                        raise ConnectionError("无法连接到Tushare API")
                
                # 确保pro对象存在
                if self.pro is None:
                    self.logger.warning("API客户端为空，尝试重新连接")
                    if not self.connect():
                        raise ConnectionError("无法连接到Tushare API")
                
                # {{ AURA-X: Modify - 传递API名称以支持分类限流. Source: Tushare API限流分析 }}
                # 调用带超时参数的API
                if trade_date:
                    result = self._call_api(
                        self.pro.daily,
                        'daily',  # 传递API名称
                        ts_code=ts_code,
                        trade_date=trade_date
                    )
                else:
                    result = self._call_api(
                        self.pro.daily,
                        'daily',  # 传递API名称
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date
                    )
                
                # 检查结果
                if result is None or result.empty:
                    self.logger.warning(f"获取日线数据返回空数据: {ts_code if ts_code else ''} "
                                   f"{trade_date if trade_date else start_date + ' to ' + end_date if start_date and end_date else ''}")
                    return pd.DataFrame()
                
                return result
                
            except Exception as e:
                retry_count += 1
                error_msg = f"获取日线数据失败: {e}"
                self.logger.error(error_msg)
                
                # 检查是否是连接错误
                error_str = str(e).lower()
                is_connection_error = any(conn_err in error_str for conn_err in 
                                     ['connection', 'timeout', 'broken', 'reset', 'refused', 'aborted'])
                
                # 如果是连接错误，添加更长的延迟
                wait_time = self.retry_interval * (2 if is_connection_error else 1)
                
                if retry_count <= max_retries:
                    self.logger.warning(f"第{retry_count}次重试获取日线数据，等待{wait_time}秒")
                    time.sleep(wait_time)
                else:
                    raise DataFetchError(error_msg)
    
    def get_index_data(self, ts_code: str = None, trade_date: str = None, 
                     start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        获取指数数据
        
        参数:
            ts_code: 指数代码，例如'000300.SH'
            trade_date: 交易日期，格式YYYYMMDD，二选一
            start_date: 开始日期，格式YYYYMMDD，与trade_date二选一
            end_date: 结束日期，格式YYYYMMDD，可选
            
        返回:
            pd.DataFrame: 指数数据，包含字段:
                - ts_code: 指数代码
                - trade_date: 交易日期
                - open: 开盘点位
                - high: 最高点位
                - low: 最低点位
                - close: 收盘点位
                - vol: 成交量
                - amount: 成交额
                - ...其他字段
        """
        try:
            # 确保已连接
            if not self.is_connected():
                self.logger.warning("数据源未连接，尝试重新连接")
                if not self.connect():
                    raise ConnectionError("无法连接到Tushare API")
            
            # 确保pro对象存在
            if self.pro is None:
                self.logger.warning("API客户端为空，尝试重新连接")
                if not self.connect():
                    raise ConnectionError("无法连接到Tushare API")
            
            # 调用Tushare API
            if trade_date:
                df = self.pro.index_daily(ts_code=ts_code, trade_date=trade_date)
            else:
                df = self.pro.index_daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
            
            # 检查结果
            if df is None or df.empty:
                self.logger.warning(f"获取指数数据返回空数据: {ts_code if ts_code else ''} "
                               f"{trade_date if trade_date else start_date + ' to ' + end_date if start_date and end_date else ''}")
                return pd.DataFrame()
            
            return df
            
        except Exception as e:
            self.logger.error(f"获取指数数据失败: {e}")
            raise DataFetchError(f"获取指数数据失败: {e}")
    
    def get_financial_data(self, api_name: str, ts_code: str, period: str = None, 
                         start_date: str = None, end_date: str = None, **kwargs) -> pd.DataFrame:
        """
        获取财务数据
        
        参数：
            api_name: 财务API名称，如'income', 'balancesheet', 'cashflow'
            ts_code: 股票代码
            period: 报告期
            start_date: 公告开始日期
            end_date: 公告结束日期
            **kwargs: 其他查询参数
            
        返回：
            pd.DataFrame: 财务数据
        """
        params = {'ts_code': ts_code}
        if period:
            params['period'] = period
        if start_date:
            params['start_date'] = start_date
        if end_date:
            params['end_date'] = end_date
        params.update(kwargs)
        
        return self.get_data(api_name, **params)

    def _format_date_for_tushare(self, date_str: str) -> str:
        """
        将日期字符串转换为Tushare API所需的YYYYMMDD格式

        参数:
            date_str: 日期字符串，支持YYYY-MM-DD或YYYYMMDD格式

        返回:
            str: YYYYMMDD格式的日期字符串
        """
        if not date_str:
            return date_str

        # 如果已经是YYYYMMDD格式，直接返回
        if len(date_str) == 8 and date_str.isdigit():
            return date_str

        # 处理YYYY-MM-DD格式
        if len(date_str) == 10 and date_str.count('-') == 2:
            return date_str.replace('-', '')

        # 尝试解析其他格式
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            return date_obj.strftime('%Y%m%d')
        except ValueError:
            try:
                date_obj = datetime.strptime(date_str, '%Y/%m/%d')
                return date_obj.strftime('%Y%m%d')
            except ValueError:
                self.logger.warning(f"无法解析日期格式: {date_str}，保持原格式")
                return date_str

    def _build_cache_key(self, api_name, **kwargs):
        """
        根据API名称和参数构建缓存键
        
        参数:
            api_name: API名称
            **kwargs: API调用参数
            
        返回:
            str: 缓存键
        """
        # 处理API名称映射
        if api_name in self._api_map:
            api_name = self._api_map[api_name]
            
        # 将参数转换为排序后的字符串
        params_str = ''.join(f"{k}:{v}" for k, v in sorted(kwargs.items()) if v is not None)
        
        # 构建缓存键
        cache_key = f"{api_name}_{params_str}"
        
        # 使用MD5生成固定长度的键，避免文件名过长
        return hashlib.md5(cache_key.encode()).hexdigest()
        
    def _check_rate_limit(self, api_name=None):
        """
        检查API调用频率限制，如果超过限制则返回需要等待的时间

        {{ AURA-X: Modify - 简化限流检查，自适应逻辑已移至AdaptiveRateLimiter. Source: 架构优化重构方案 }}

        参数:
            api_name: API名称，用于特定接口的限流检查

        返回:
            float: 需要等待的时间（秒）
        """
        with self._lock:
            now = time.time()

            # 清理超过时间窗口的请求记录
            self.rate_limit['requests'] = [t for t in self.rate_limit['requests'] if now - t < self.rate_limit['window']]

            # {{ AURA-X: Modify - 简化API特定限流检查. Source: 架构优化重构方案 }}
            # 获取当前API的限流配置
            if api_name and api_name in self.rate_limit['api_limits']:
                api_limit = self.rate_limit['api_limits'][api_name]

                # 初始化API特定的请求记录
                if api_name not in self.rate_limit['api_requests']:
                    self.rate_limit['api_requests'][api_name] = []
                # 清理API特定的请求记录
                self.rate_limit['api_requests'][api_name] = [
                    t for t in self.rate_limit['api_requests'][api_name]
                    if now - t < self.rate_limit['window']
                ]
                current_api_requests = len(self.rate_limit['api_requests'][api_name])
                limit_threshold = int(api_limit * 0.85)

                self.logger.debug(f"API {api_name} 限流检查: {current_api_requests}/{api_limit} (阈值: {limit_threshold})")

                # 检查API特定限制
                if current_api_requests >= api_limit:
                    # 已达到限制，需要等待
                    oldest_request = min(self.rate_limit['api_requests'][api_name]) if self.rate_limit['api_requests'][api_name] else now - self.rate_limit['window']
                    wait_time = self.rate_limit['window'] - (now - oldest_request) + 1
                    self.logger.warning(f"API {api_name} 达到限制({current_api_requests}/{api_limit})，等待 {wait_time:.2f} 秒")
                    return wait_time
                elif current_api_requests >= limit_threshold:
                    # 接近限制，使用平缓延迟
                    ratio = current_api_requests / api_limit
                    delay = 0.3 * ((ratio - 0.85) / 0.15) ** 2
                    delay = max(delay, 0.01)
                    self.logger.info(f"API {api_name} 接近限制({current_api_requests}/{api_limit})，延迟 {delay:.2f} 秒")
                    return delay

            # 检查通用限流
            current_requests = len(self.rate_limit['requests'])
            limit_threshold = int(self.rate_limit['limit'] * 0.85)

            if current_requests >= self.rate_limit['limit']:
                # 已达到限制，需要等待
                oldest_request = min(self.rate_limit['requests']) if self.rate_limit['requests'] else now - self.rate_limit['window']
                wait_time = self.rate_limit['window'] - (now - oldest_request) + 1
                self.logger.warning(f"通用API限制({current_requests}/{self.rate_limit['limit']})，等待 {wait_time:.2f} 秒")
                return wait_time
            elif current_requests >= limit_threshold:
                # 接近限制，使用平缓延迟
                ratio = current_requests / self.rate_limit['limit']
                delay = 0.3 * ((ratio - 0.85) / 0.15) ** 2
                delay = max(delay, 0.01)
                self.logger.info(f"通用API接近限制({current_requests}/{self.rate_limit['limit']})，延迟 {delay:.2f} 秒")
                return delay

            # 未达到限制，无需等待
            return 0
    
    def set_rate_limit(self, limit: int, period: int = 60) -> None:
        """
        设置请求频率限制
        
        参数：
            limit: 指定周期内的最大请求数
            period: 限制周期（秒）
        """
        self.rate_limit['limit'] = limit
        self.rate_limit['period'] = period

    def check_connectivity(self) -> bool:
        """
        检查与数据源的连接状态
        
        返回：
            bool: 如果连接正常返回True，否则返回False
        """
        if not self.is_connected():
            return False
            
        try:
            # 尝试执行一个简单查询来验证连接
            _ = self.pro.trade_cal(exchange='SSE', start_date='20200101', end_date='20200102')
            return True
        except Exception:
            return False
    
    def get_fields(self, data_type: str) -> List[str]:
        """
        获取指定数据类型的字段列表
        
        参数：
            data_type: 数据类型，例如'stock_basic', 'daily'等
            
        返回：
            List[str]: 字段名列表
        """
        if not self.is_connected():
            try:
                self.connect()
            except Exception as e:
                raise ConnectionError(f"获取字段列表前连接失败: {str(e)}")
                
        # 执行一个小的查询，从返回结果中获取字段列表
        try:
            # 根据数据类型选择适当的API和参数
            if data_type == 'stock_basic':
                data = self.get_stock_basic(limit=1)
            elif data_type in ['daily', 'weekly', 'monthly']:
                # 获取任意一支股票的最近数据
                data = self.get_data(api_name=data_type, limit=1)
            elif 'index' in data_type:
                # 获取指数数据
                data = self.get_data(api_name=data_type, limit=1)
            else:
                # 尝试直接使用数据类型作为API名称
                data = self.get_data(api_name=data_type, limit=1)
                
            # 如果成功获取到数据，返回字段列表
            if data is not None and isinstance(data, pd.DataFrame) and not data.empty:
                return list(data.columns)
            return []
        except Exception as e:
            raise DataFetchError(f"获取字段列表失败: {str(e)}")
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取数据源状态信息
        
        返回：
            Dict[str, Any]: 包含数据源状态的字典
        """
        status = {
            'name': 'Tushare',
            'connected': self.is_connected(),
            'cache_dir': self.cache_dir,
            'rate_limit': {
                'limit': self.rate_limit['limit'],
                'period': self.rate_limit['period'],
                'current_requests': len(self.rate_limit['requests'])
            },
            # {{ AURA-X: Add - 添加自适应限流性能统计. Source: 中期性能优化方案 }}
            'adaptive_performance': self.adaptive_limiter.get_performance_stats(),
            'last_error': None,  # 初始化为None
            'cache_size': 0  # 初始化缓存大小
        }
        
        # 计算缓存大小
        try:
            cache_size = 0
            for file in os.listdir(self.cache_dir):
                file_path = os.path.join(self.cache_dir, file)
                if os.path.isfile(file_path):
                    cache_size += os.path.getsize(file_path)
            status['cache_size'] = cache_size
        except Exception as e:
            status['last_error'] = f"计算缓存大小失败: {str(e)}"
        
        # 如果已连接，尝试获取更多信息
        if self.is_connected():
            try:
                # 获取一些基本信息作为状态数据
                trade_cal = self.pro.trade_cal(exchange='SSE', 
                                             start_date=(datetime.now() - timedelta(days=7)).strftime('%Y%m%d'),
                                             end_date=datetime.now().strftime('%Y%m%d'))
                status['latest_trade_date'] = trade_cal['cal_date'].max() if not trade_cal.empty else None
                status['api_ready'] = True
            except Exception as e:
                status['api_ready'] = False
                status['last_error'] = f"API调用失败: {str(e)}"
        
        return status

    def log_adaptive_performance(self):
        """
        输出自适应限流性能报告

        {{ AURA-X: Add - 添加自适应限流性能报告方法. Source: 中期性能优化方案 }}
        """
        stats = self.adaptive_limiter.get_performance_stats()
        if stats:
            self.logger.info("🚀 自适应智能限流性能报告:")
            self.logger.info(f"   当前性能等级: {stats['current_level']}")
            self.logger.info(f"   平均响应时间: {stats['avg_response_time']:.3f}s")
            self.logger.info(f"   中位响应时间: {stats['median_response_time']:.3f}s")
            self.logger.info(f"   API成功率: {stats['success_rate']:.1f}%")
            self.logger.info(f"   性能调整倍数: {stats['performance_multiplier']:.2f}x")
            self.logger.info(f"   监控样本数: {stats['total_samples']}")
        else:
            self.logger.info("🚀 自适应智能限流系统已启动，正在收集性能数据...")

    def _wait_for_rate_limit(self, api_name=None):
        """
        等待直到满足API调用频率限制，返回等待的时间（秒）

        {{ AURA-X: Modify - 支持按API分类的限流等待. Source: Tushare API限流分析 }}

        参数:
            api_name: API名称，用于特定接口的限流检查
        """
        wait_time = self._check_rate_limit(api_name)

        if wait_time > 0:
            self.logger.info(f"限流等待 {wait_time:.2f} 秒 (API: {api_name or '通用'})")
            time.sleep(wait_time)

        # 记录新的调用
        with self._lock:
            now = time.time()
            self.rate_limit['requests'].append(now)

            # 如果是特定API，也记录到API特定的请求列表
            if api_name and api_name in self.rate_limit['api_limits']:
                if api_name not in self.rate_limit['api_requests']:
                    self.rate_limit['api_requests'][api_name] = []
                self.rate_limit['api_requests'][api_name].append(now)

        return wait_time

    def _call_api(self, func, api_name=None, *args, **kwargs):
        """
        调用Tushare API，处理限流和重试

        {{ AURA-X: Modify - 使用重构后的自适应智能限流系统. Source: 架构优化重构方案 }}

        参数:
            func: API函数
            api_name: API名称，用于特定接口的限流检查
            *args: 位置参数
            **kwargs: 关键字参数

        返回:
            API调用结果

        异常:
            RateLimitError: 达到频率限制时抛出
            DataFetchError: 数据获取失败时抛出
        """
        # {{ AURA-X: Modify - 使用重构后的自适应智能限流系统. Source: 架构优化重构方案 }}
        # 如果未提供API名称，尝试从函数名推断
        if api_name is None and hasattr(func, '__name__'):
            api_name = func.__name__ or 'unknown_api'

        retry_count = 0
        while retry_count <= self.retry_count:
            try:
                # 记录API调用统计
                self.stats['api_calls']['total'] += 1

                # {{ AURA-X: Restore - 恢复轻量化自适应智能限流系统为最终方案. Source: 最终对照测试完成 }}
                # 使用轻量化自适应限流器执行API调用
                result = self.adaptive_limiter.execute_with_monitoring(func, api_name, *args, **kwargs)

                # 更新成功统计
                self.stats['api_calls']['success'] += 1
                return result

            except Exception as e:

                self.stats['api_calls']['failure'] += 1

                if retry_count < self.retry_count:
                    retry_count += 1
                    self.stats['api_calls']['retry'] += 1
                    self.logger.warning(f"API调用失败，正在进行第{retry_count}次重试: {str(e)}")
                    time.sleep(self.retry_interval)
                else:
                    self.logger.error(f"API调用失败，已达到最大重试次数: {str(e)}")
                    raise TushareError(f"获取数据失败: {str(e)}")

    def get_reference_data(self, data_type: str, fields: List[str] = None, conditions: Dict[str, Any] = None, **kwargs) -> pd.DataFrame:
        """
        获取参考数据（如股票列表、交易日历等）
        
        参数:
            data_type: 数据类型，如 'stock_list', 'trade_calendar'
            fields: 字段列表
            conditions: 筛选条件
            **kwargs: 其他参数
            
        返回:
            pd.DataFrame: 参考数据
            
        异常:
            ValueError: 不支持的数据类型
        """
        self.logger.info(f"获取参考数据: {data_type}")
        
        # 转换字段列表为字符串（如果提供）
        fields_str = None
        if fields:
            fields_str = ','.join(fields)
        
        # 根据数据类型调用相应的方法
        if data_type == 'stock_list':
            # 获取股票列表
            df = self.get_stock_basic(fields=fields_str, **kwargs)
            
            # 应用条件筛选（如果有）
            if conditions and isinstance(conditions, dict) and not df.empty:
                for field, value in conditions.items():
                    if field in df.columns:
                        df = df[df[field] == value]
            
            return df
            
        elif data_type == 'trade_calendar':
            # 获取交易日历
            exchange = kwargs.get('exchange', 'SSE')
            start_date = kwargs.get('start_date')
            end_date = kwargs.get('end_date')
            
            return self.get_trade_calendar(exchange=exchange, start_date=start_date, end_date=end_date)
            
        elif data_type == 'index_list':
            # 获取指数列表
            params = {'market': kwargs.get('market', '')}
            if fields_str:
                params['fields'] = fields_str
                
            return self.get_data('index_basic', **params)
            
        elif data_type == 'concept_list':
            # 获取概念列表
            return self.get_data('concept', **kwargs)
            
        elif data_type == 'industry_list':
            # 获取行业列表
            return self.get_data('stock_industry', **kwargs)
            
        else:
            error_msg = f"不支持的参考数据类型: {data_type}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

    def get_market_data(self, symbols, data_type='daily', start_date=None, end_date=None, **kwargs):
        """
        获取市场行情数据
        
        参数:
            symbols: 股票代码列表或字符串
            data_type: 数据类型, 如 'daily', 'weekly', 'monthly'
            start_date: 开始日期, 格式: YYYYMMDD
            end_date: 结束日期, 格式: YYYYMMDD
            **kwargs: 其他参数
            
        返回:
            pd.DataFrame: 市场数据
            
        异常:
            DataFetchError: 数据获取失败时抛出
        """
        self.logger.info(f"获取市场数据: {data_type}, {len(symbols) if isinstance(symbols, list) else 1}支股票, 时间范围: {start_date}-{end_date}")
        
        # 确保连接
        if not self.is_connected():
            self.logger.warning("数据源未连接，尝试重新连接")
            self.connect()
            
        # 将单个代码转换为列表
        if not isinstance(symbols, list):
            symbols = [symbols]
            
        # 限制批量查询的股票数量，避免请求过大
        batch_size = 50
        result_dfs = []
        
        # 批量查询
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i+batch_size]
            self.logger.debug(f"处理批次 {i//batch_size + 1}/{(len(symbols)-1)//batch_size + 1}, 股票数量: {len(batch_symbols)}")
            
            # 根据数据类型调用相应API
            if data_type == 'daily':
                # 日线数据
                for symbol in batch_symbols:
                    try:
                        df = self.get_daily_bars(ts_code=symbol, start_date=start_date, end_date=end_date)
                        if not df.empty:
                            result_dfs.append(df)
                    except Exception as e:
                        self.logger.warning(f"获取股票 {symbol} 日线数据失败: {e}")
                        
            elif data_type == 'weekly':
                # 周线数据
                for symbol in batch_symbols:
                    try:
                        api_name = 'weekly'
                        df = self.get_data(api_name, ts_code=symbol, start_date=start_date, end_date=end_date)
                        if not df.empty:
                            result_dfs.append(df)
                    except Exception as e:
                        self.logger.warning(f"获取股票 {symbol} 周线数据失败: {e}")
                        
            elif data_type == 'monthly':
                # 月线数据
                for symbol in batch_symbols:
                    try:
                        api_name = 'monthly'
                        df = self.get_data(api_name, ts_code=symbol, start_date=start_date, end_date=end_date)
                        if not df.empty:
                            result_dfs.append(df)
                    except Exception as e:
                        self.logger.warning(f"获取股票 {symbol} 月线数据失败: {e}")
                        
            elif data_type == 'adj_factor':
                # 复权因子
                for symbol in batch_symbols:
                    try:
                        api_name = 'adj_factor'
                        df = self.get_data(api_name, ts_code=symbol, start_date=start_date, end_date=end_date)
                        if not df.empty:
                            result_dfs.append(df)
                    except Exception as e:
                        self.logger.warning(f"获取股票 {symbol} 复权因子失败: {e}")
                        
            elif data_type == 'daily_basic':
                # 每日指标
                for symbol in batch_symbols:
                    try:
                        api_name = 'daily_basic'
                        df = self.get_data(api_name, ts_code=symbol, start_date=start_date, end_date=end_date)
                        if not df.empty:
                            result_dfs.append(df)
                    except Exception as e:
                        self.logger.warning(f"获取股票 {symbol} 每日指标失败: {e}")
                        
            elif data_type == 'market_cap':
                # 市值数据 (从daily_basic提取市值相关字段)
                for symbol in batch_symbols:
                    try:
                        api_name = 'daily_basic'
                        df = self.get_data(api_name, ts_code=symbol, start_date=start_date, end_date=end_date)
                        if not df.empty:
                            # 提取市值相关字段
                            market_cap_fields = ['ts_code', 'trade_date', 'total_mv', 'circ_mv', 'total_share', 'float_share', 'free_share']
                            available_fields = [field for field in market_cap_fields if field in df.columns]
                            result_dfs.append(df[available_fields])
                    except Exception as e:
                        self.logger.warning(f"获取股票 {symbol} 市值数据失败: {e}")
            else:
                raise ValueError(f"不支持的市场数据类型: {data_type}")
            
            # 避免频繁请求，添加适当延迟
            if i + batch_size < len(symbols):
                time.sleep(0.5)
                
        # 合并结果
        if result_dfs:
            result = pd.concat(result_dfs, ignore_index=True)
            self.logger.info(f"成功获取 {len(result)} 条 {data_type} 数据")
            return result
        else:
            self.logger.warning(f"未获取到任何 {data_type} 数据")
            return pd.DataFrame()

    def _init_http_session(self):
        """
        初始化HTTP会话和连接池

        {{ AURA-X: Restore - 恢复HTTP连接池优化功能. Source: HTTP连接池优化对照实验完成 }}
        """
        # 创建会话对象
        self.session = requests.Session()

        # 配置重试策略
        retry_strategy = Retry(
            total=3,  # 总重试次数
            backoff_factor=1.0,  # 退避因子
            status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的HTTP状态码
            allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"]
        )

        # 配置HTTP适配器（连接池）- 最优配置
        adapter = HTTPAdapter(
            pool_connections=3,   # 最优连接池大小，完美匹配API限流特性
            pool_maxsize=10,      # 最优最大连接数，避免资源竞争
            max_retries=retry_strategy,
            pool_block=False      # 非阻塞模式
        )

        # 为HTTP和HTTPS协议挂载适配器
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # 配置会话超时
        self.session.timeout = (self.timeout, self.timeout * 2)  # (连接超时, 读取超时)

        # 配置Keep-Alive
        self.session.headers.update({
            'Connection': 'keep-alive',
            'Keep-Alive': 'timeout=300, max=500',  # 最优配置：长连接保持，减少重建开销
            'User-Agent': 'TushareAdapter/1.0 (Quantification Platform)'
        })

        self.logger.info("HTTP连接池已初始化: 连接池大小=3, 最大连接数=10, Keep-Alive=300s")

    def _patch_tushare_session(self):
        """
        将优化的HTTP会话注入到Tushare客户端中

        {{ AURA-X: Restore - 恢复Monkey Patching逻辑. Source: HTTP连接池优化对照实验完成 }}
        """
        if not self.pro:
            self.logger.warning("Tushare API客户端未初始化，跳过HTTP会话注入")
            return

        try:
            # 保存原始的requests方法
            if not hasattr(self, '_original_requests_post'):
                self._original_requests_post = requests.post
                self._original_requests_get = requests.get
                self.logger.debug("已保存原始requests方法")

            # 创建计数器用于调试
            self._http_call_count = 0

            # 创建使用我们会话的包装函数
            def session_post(url, **kwargs):
                self._http_call_count += 1
                self.logger.debug(f"HTTP连接池调用 #{self._http_call_count}: POST {url}")
                try:
                    return self.session.post(url, **kwargs)
                except Exception as e:
                    self.logger.error(f"HTTP连接池调用失败: {e}")
                    # 回退到原始方法
                    return self._original_requests_post(url, **kwargs)

            def session_get(url, **kwargs):
                self._http_call_count += 1
                self.logger.debug(f"HTTP连接池调用 #{self._http_call_count}: GET {url}")
                try:
                    return self.session.get(url, **kwargs)
                except Exception as e:
                    self.logger.error(f"HTTP连接池调用失败: {e}")
                    # 回退到原始方法
                    return self._original_requests_get(url, **kwargs)

            # 替换requests的方法
            requests.post = session_post
            requests.get = session_get

            self.logger.info("✅ HTTP连接池已成功注入Tushare客户端")

        except Exception as e:
            self.logger.error(f"❌ HTTP会话注入失败: {e}")
            # 确保在失败时恢复原始方法
            if hasattr(self, '_original_requests_post'):
                requests.post = self._original_requests_post
                requests.get = self._original_requests_get

    def close_session(self):
        """
        关闭HTTP会话和连接池，恢复原始requests方法

        {{ AURA-X: Modify - 增强会话关闭逻辑，确保正确恢复原始方法. Source: HTTP连接池优化问题修复 }}
        """
        try:
            # 恢复原始的requests方法
            if hasattr(self, '_original_requests_post'):
                requests.post = self._original_requests_post
                requests.get = self._original_requests_get
                delattr(self, '_original_requests_post')
                delattr(self, '_original_requests_get')
                self.logger.info("✅ 已恢复原始requests方法")

            # 关闭HTTP会话
            if hasattr(self, 'session') and self.session:
                self.session.close()
                self.logger.info("✅ HTTP会话已关闭")

            # 输出HTTP调用统计
            if hasattr(self, '_http_call_count'):
                self.logger.info(f"📊 HTTP连接池总调用次数: {self._http_call_count}")

        except Exception as e:
            self.logger.error(f"❌ 关闭会话时发生错误: {e}")

