"""
数据源工厂：负责创建不同类型的数据源适配器实例
- 支持各种市场数据源
- 支持本地文件数据源
- 支持网络数据源
- 提供统一的数据源创建接口
"""

from typing import Dict, Any
import importlib
import os

from .data_source_interface import DataSourceInterface
from src.utils.config.config_factory import ConfigFactory


class DataSourceFactory:
    """
    数据源工厂类，用于创建各种数据源适配器实例
    """
    
    # 数据源适配器类型映射
    _adapters = {
        # A股数据源
        'tushare': 'src.data.sources.tushare_adapter.TushareAdapter',
        'tushare_async': 'src.data.sources.async_tushare_adapter.HighPerformanceAsyncTushareAdapter',
        'akshare': 'data.sources.akshare_adapter.AKShareAdapter',
        'eastmoney': 'data.sources.eastmoney_adapter.EastMoneyAdapter',
        'goldminer': 'data.sources.goldminer_adapter.GoldMinerAdapter',

        # 美股/全球数据源
        'yahoo': 'data.sources.yahoo_adapter.YahooAdapter',
        'alpha_vantage': 'data.sources.alpha_vantage_adapter.AlphaVantageAdapter',

        # 本地文件数据源
        'local_file': 'data.sources.local_file_adapter.LocalFileAdapter',
        'csv': 'data.sources.local_file_adapter.LocalFileAdapter',  # 别名

        # 网络数据源
        'web': 'data.sources.web_adapter.WebAdapter',
        'api': 'data.sources.api_adapter.ApiAdapter',
        
        # 模拟数据源（测试用）
        'mock': 'data.sources.mock_adapter.MockAdapter',
        'random': 'data.sources.random_adapter.RandomAdapter'
    }
    
    # 配置工厂实例
    _config_factory = ConfigFactory()
    
    # 项目根目录 - 修复配置目录路径
    _config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), 'config')
    
    # 配置文件路径
    _config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
                               'config', 'data_source.yaml')
    
    @classmethod
    def create(cls, source_type: str, config: Dict[str, Any] = None) -> DataSourceInterface:
        """
        创建指定类型的数据源适配器实例
        
        参数：
            source_type: 数据源类型，如'tushare', 'yahoo', 'local_file'等
            config: 数据源配置参数
            
        返回：
            DataSourceInterface: 数据源适配器实例
            
        异常：
            ValueError: 数据源类型不支持时抛出
            ImportError: 无法导入对应模块时抛出
        """
        if source_type not in cls._adapters:
            supported = ', '.join(cls._adapters.keys())
            raise ValueError(f"不支持的数据源类型: {source_type}，支持的类型包括: {supported}")
        
        # 导入适配器类
        module_path = cls._adapters[source_type]
        module_name, class_name = module_path.rsplit('.', 1)
        
        try:
            module = importlib.import_module(module_name)
            adapter_class = getattr(module, class_name)
        except (ImportError, AttributeError) as e:
            raise ImportError(f"无法导入数据源适配器 {module_path}: {str(e)}")
        
        # 创建适配器实例
        if config is None:
            config = {}
            
        return adapter_class(**config)
    
    @classmethod
    def create_from_config(cls, config: Dict[str, Any]) -> DataSourceInterface:
        """
        从配置字典创建数据源适配器实例
        
        参数：
            config: 配置字典，必须包含'type'字段
            
        返回：
            DataSourceInterface: 数据源适配器实例
            
        异常：
            ValueError: 配置不正确时抛出
        """
        if 'type' not in config:
            raise ValueError("配置必须包含'type'字段")
        
        source_type = config.pop('type')
        return cls.create(source_type, config)
    
    @classmethod
    def load_from_config(cls, source_type: str, config_name: str = 'data_source', env: str = None) -> Dict[str, Any]:
        """
        从配置文件加载数据源配置
        
        参数：
            source_type: 数据源类型，如'tushare', 'local_file'等
            config_name: 配置文件名称，默认为'data_source'
            env: 环境名称
            
        返回：
            Dict[str, Any]: 配置字典
            
        异常：
            ValueError: 配置不存在时抛出
        """
        # 使用ConfigFactory加载配置
        try:
            config = cls._config_factory.load_config(config_name, cls._config_dir)
            
            # 检查指定的数据源配置是否存在
            if source_type not in config:
                raise ValueError(f"配置文件中不存在数据源类型: {source_type}")
                
            return config[source_type]
        except FileNotFoundError as e:
            raise ValueError(f"配置文件不存在: {str(e)}")
    
    @classmethod
    def create_tushare(cls, token: str = None, timeout: int = None, **kwargs) -> DataSourceInterface:
        """
        创建Tushare数据源适配器
        
        参数：
            token: Tushare API令牌，如果为None则从配置文件加载
            timeout: 连接超时时间（秒）
            **kwargs: 其他配置参数
            
        返回：
            DataSourceInterface: Tushare数据源适配器实例
        """
        # 尝试从配置文件加载
        try:
            config = cls.load_from_config('tushare')
            api_config = config.get('api', {})
            
            # 如果未提供token，则使用配置文件中的token
            if token is None and 'token' in api_config:
                token = api_config['token']
                
            # 如果未提供timeout，则使用配置文件中的timeout
            if timeout is None and 'timeout' in api_config:
                timeout = api_config['timeout']
                
        except Exception:
            # 如果配置文件加载失败，使用提供的参数或默认值
            pass
            
        # 构建配置字典
        adapter_config = {'token': token}
        if timeout is not None:
            adapter_config['timeout'] = timeout
            
        adapter_config.update(kwargs)
        return cls.create('tushare', adapter_config)

    @classmethod
    def create_async_tushare(cls, token: str = None, **kwargs) -> DataSourceInterface:
        """
        创建异步Tushare数据源适配器

        参数：
            token: Tushare API令牌
            **kwargs: 其他配置参数

        返回：
            DataSourceInterface: 异步Tushare数据源适配器实例
        """
        # 尝试从配置文件加载
        try:
            config = cls.load_from_config('tushare')

            # 如果未提供token，则使用配置文件中的token
            if token is None and 'token' in config:
                token = config['token']

        except Exception:
            # 如果配置文件加载失败，使用提供的参数或默认值
            pass

        adapter_config = {'token': token} if token else {}
        adapter_config.update(kwargs)
        return cls.create('tushare_async', adapter_config)
    
    @classmethod
    def create_local_file(cls, data_path: str = None, **kwargs) -> DataSourceInterface:
        """
        创建本地文件数据源适配器
        
        参数：
            data_path: 数据文件路径
            **kwargs: 其他配置参数
            
        返回：
            DataSourceInterface: 本地文件数据源适配器实例
        """
        # 尝试从配置文件加载
        try:
            config = cls.load_from_config('local_file')
            
            # 如果未提供data_path，则使用配置文件中的base_path
            if data_path is None and 'base_path' in config:
                data_path = config['base_path']
                
        except Exception:
            # 如果配置文件加载失败，使用提供的参数或默认值
            pass
            
        adapter_config = {'data_path': data_path} if data_path else {}
        adapter_config.update(kwargs)
        return cls.create('local_file', adapter_config)
    
    @classmethod
    def create_goldminer(cls, username: str = None, password: str = None, token: str = None, **kwargs) -> DataSourceInterface:
        """
        创建掘金量化数据源适配器
        
        参数：
            username: 用户名，如果为None则从配置文件加载
            password: 密码，如果为None则从配置文件加载
            token: API令牌，如果为None则从配置文件加载
            **kwargs: 其他配置参数
            
        返回：
            DataSourceInterface: 掘金量化数据源适配器实例
        """
        # 尝试从配置文件加载
        try:
            config = cls.load_from_config('goldminer')
            
            # 如果未提供参数，则使用配置文件中的参数
            if username is None and 'username' in config:
                username = config['username']
                
            if password is None and 'password' in config:
                password = config['password']
                
            if token is None and 'token' in config:
                token = config['token']
                
        except Exception:
            # 如果配置文件加载失败，使用提供的参数或默认值
            pass
            
        adapter_config = {'username': username, 'password': password, 'token': token}
        adapter_config.update(kwargs)
        return cls.create('goldminer', adapter_config)
    
    @classmethod
    def create_akshare(cls, timeout: int = None, max_retry: int = None, **kwargs) -> DataSourceInterface:
        """
        创建AkShare数据源适配器
        
        参数：
            timeout: 连接超时时间（秒）
            max_retry: 最大重试次数
            **kwargs: 其他配置参数
            
        返回：
            DataSourceInterface: AkShare数据源适配器实例
        """
        # 构建配置字典
        adapter_config = {}
        if timeout is not None:
            adapter_config['timeout'] = timeout
        if max_retry is not None:
            adapter_config['max_retry'] = max_retry
            
        adapter_config.update(kwargs)
        return cls.create('akshare', adapter_config)
    
    @classmethod
    def register_adapter(cls, adapter_type: str, module_path: str) -> None:
        """
        注册新的数据源适配器类型
        
        参数：
            adapter_type: 适配器类型名称
            module_path: 模块路径，格式为'package.module.Class'
            
        异常：
            ValueError: 适配器类型已存在时抛出
        """
        if adapter_type in cls._adapters:
            raise ValueError(f"适配器类型'{adapter_type}'已存在")
        
        cls._adapters[adapter_type] = module_path
    
    @classmethod
    def get_available_adapters(cls) -> Dict[str, str]:
        """
        获取所有可用的适配器类型
        
        返回：
            Dict[str, str]: 适配器类型到模块路径的映射
        """
        return cls._adapters.copy()
    
    @classmethod
    def _load_config(cls, source_type: str) -> Dict[str, Any]:
        """
        从配置文件加载数据源配置
        
        参数:
            source_type: 数据源类型
            
        返回:
            数据源配置字典
        """
        import logging
        logger = logging.getLogger(__name__)
        
        try:
            # {{ AURA-X: Modify - 修复导入路径，使用绝对导入. Approval: 寸止(ID:架构一致性修复). }}
            # 使用ConfigFactory加载配置
            from src.utils.config.config_factory import config_factory
            
            if source_type == 'tushare':
                # 对于tushare，直接使用专用方法
                config = config_factory.get_tushare_config()
                if 'api' in config:
                    # 提取api下的配置到顶层
                    api_config = config.pop('api', {})
                    for key, value in api_config.items():
                        config[key] = value
                return config
            else:
                # 对于其他数据源，先加载完整配置
                full_config = config_factory.load_yaml('data_source.yaml')
                if not full_config:
                    logger.warning(f"配置文件加载失败或为空")
                    return {}
                    
                # 获取特定数据源的配置
                if source_type in full_config:
                    return full_config[source_type]
                else:
                    logger.warning(f"配置中未找到数据源类型: {source_type}")
                    return {}
                    
        except Exception as e:
            logger.error(f"加载配置时出错: {e}")
            import traceback
            traceback.print_exc()
            return {} 