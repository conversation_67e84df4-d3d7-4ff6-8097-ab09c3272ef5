#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分布式数据库工厂

{{ AURA-X: Add - 创建分布式数据库管理器工厂，提供统一的创建和管理接口. Approval: 寸止(ID:分布式数据库完善). }}

提供分布式数据库管理器的创建、配置和管理功能：
- 单例模式管理分布式数据库实例
- 支持多种配置方式（文件、字典、环境变量）
- 提供便捷的数据库操作接口
- 集成健康检查和故障转移
"""

import os
import json
import logging
import threading
from typing import Dict, Any, Optional, Union
from pathlib import Path

from src.data.storage.distributed_db_manager import DistributedDatabaseManager
from src.utils.logging.logger_factory import get_logger


class DistributedDatabaseFactory:
    """分布式数据库工厂类"""
    
    _instance = None
    _lock = threading.Lock()
    _managers: Dict[str, DistributedDatabaseManager] = {}
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.logger = get_logger(__name__)
            self._initialized = True
    
    @classmethod
    def create_manager(cls, 
                      name: str = "default",
                      config_file: Optional[str] = None,
                      config_dict: Optional[Dict[str, Any]] = None,
                      auto_start_health_check: bool = True) -> DistributedDatabaseManager:
        """
        创建分布式数据库管理器
        
        参数:
            name: 管理器名称
            config_file: 配置文件路径
            config_dict: 配置字典
            auto_start_health_check: 是否自动启动健康检查
            
        返回:
            DistributedDatabaseManager: 分布式数据库管理器实例
        """
        factory = cls()
        
        with cls._lock:
            if name in cls._managers:
                factory.logger.info(f"返回已存在的分布式数据库管理器: {name}")
                return cls._managers[name]
            
            # 创建新的管理器
            manager = DistributedDatabaseManager(logger=factory.logger)
            
            # 加载配置
            if config_file:
                if not os.path.exists(config_file):
                    raise FileNotFoundError(f"配置文件不存在: {config_file}")
                manager.load_config(config_file)
            elif config_dict:
                factory._load_config_from_dict(manager, config_dict)
            else:
                # 尝试加载默认配置
                default_config = "config/distributed_database.json"
                if os.path.exists(default_config):
                    manager.load_config(default_config)
                else:
                    factory.logger.warning("未找到配置文件，使用空配置创建管理器")
            
            # 启动健康检查（仅在有事件循环时）
            if auto_start_health_check:
                try:
                    import asyncio
                    # 检查是否有运行中的事件循环
                    try:
                        asyncio.get_running_loop()
                        manager.start_health_check()
                    except RuntimeError:
                        # 没有运行中的事件循环，跳过健康检查
                        factory.logger.info("没有运行中的事件循环，跳过健康检查启动")
                except ImportError:
                    factory.logger.warning("asyncio不可用，跳过健康检查")
            
            cls._managers[name] = manager
            factory.logger.info(f"✅ 创建分布式数据库管理器: {name}")
            
            return manager
    
    @classmethod
    def get_manager(cls, name: str = "default") -> Optional[DistributedDatabaseManager]:
        """
        获取分布式数据库管理器
        
        参数:
            name: 管理器名称
            
        返回:
            Optional[DistributedDatabaseManager]: 管理器实例，不存在则返回None
        """
        return cls._managers.get(name)
    
    @classmethod
    def get_or_create_manager(cls, 
                             name: str = "default",
                             config_file: Optional[str] = None,
                             **kwargs) -> DistributedDatabaseManager:
        """
        获取或创建分布式数据库管理器
        
        参数:
            name: 管理器名称
            config_file: 配置文件路径
            **kwargs: 其他创建参数
            
        返回:
            DistributedDatabaseManager: 管理器实例
        """
        manager = cls.get_manager(name)
        if manager is None:
            manager = cls.create_manager(name, config_file, **kwargs)
        return manager
    
    @classmethod
    def close_manager(cls, name: str = "default"):
        """
        关闭分布式数据库管理器
        
        参数:
            name: 管理器名称
        """
        with cls._lock:
            if name in cls._managers:
                manager = cls._managers[name]
                manager.close()
                del cls._managers[name]
                
                factory = cls()
                factory.logger.info(f"✅ 关闭分布式数据库管理器: {name}")
    
    @classmethod
    def close_all_managers(cls):
        """关闭所有分布式数据库管理器"""
        with cls._lock:
            for name in list(cls._managers.keys()):
                cls.close_manager(name)
    
    @classmethod
    def list_managers(cls) -> Dict[str, Dict[str, Any]]:
        """
        列出所有管理器信息
        
        返回:
            Dict[str, Dict[str, Any]]: 管理器信息字典
        """
        result = {}
        for name, manager in cls._managers.items():
            stats = manager.get_cluster_stats()
            result[name] = {
                'name': name,
                'total_nodes': stats['total_nodes'],
                'online_nodes': stats['online_nodes'],
                'master_nodes': stats['master_nodes'],
                'replica_nodes': stats['replica_nodes']
            }
        return result
    
    def _load_config_from_dict(self, manager: DistributedDatabaseManager, config: Dict[str, Any]):
        """从字典加载配置"""
        try:
            # 加载节点配置
            for node_config in config.get('nodes', []):
                from src.data.storage.distributed_db_manager import NodeRole
                
                manager.add_node(
                    node_id=node_config['node_id'],
                    host=node_config['host'],
                    port=node_config['port'],
                    database=node_config['database'],
                    username=node_config['username'],
                    password=node_config['password'],
                    role=NodeRole(node_config['role']),
                    weight=node_config.get('weight', 1),
                    db_type=node_config.get('db_type', 'mysql')
                )
            
            # 加载其他配置
            if 'failover' in config:
                failover_config = config['failover']
                manager.health_check_interval = failover_config.get('health_check_interval', 30)
                manager.max_error_count = failover_config.get('max_error_count', 3)
            
            if 'sharding' in config:
                sharding_config = config['sharding']
                manager.virtual_nodes = sharding_config.get('virtual_nodes_per_physical', 150)
            
            self.logger.info("✅ 从字典加载配置成功")
            
        except Exception as e:
            self.logger.error(f"从字典加载配置失败: {e}")
            raise


# 便捷函数
def get_distributed_db_manager(name: str = "default", 
                              config_file: Optional[str] = None) -> DistributedDatabaseManager:
    """
    获取分布式数据库管理器的便捷函数
    
    参数:
        name: 管理器名称
        config_file: 配置文件路径
        
    返回:
        DistributedDatabaseManager: 管理器实例
    """
    return DistributedDatabaseFactory.get_or_create_manager(name, config_file)


def create_test_distributed_db_manager() -> DistributedDatabaseManager:
    """
    创建测试用的分布式数据库管理器
    
    返回:
        DistributedDatabaseManager: 测试管理器实例
    """
    test_config = {
        "nodes": [
            {
                "node_id": "test_sqlite",
                "host": "localhost",
                "port": 0,
                "database": ":memory:",
                "username": "",
                "password": "",
                "role": "master",
                "weight": 10,
                "db_type": "sqlite"
            }
        ],
        "failover": {
            "health_check_interval": 10,
            "max_error_count": 2
        }
    }
    
    return DistributedDatabaseFactory.create_manager(
        name="test",
        config_dict=test_config,
        auto_start_health_check=False
    )


def close_all_distributed_db_managers():
    """关闭所有分布式数据库管理器的便捷函数"""
    DistributedDatabaseFactory.close_all_managers()
