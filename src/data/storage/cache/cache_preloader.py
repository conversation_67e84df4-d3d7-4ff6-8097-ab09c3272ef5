"""
缓存预热器
基于配置自动预热热门股票数据到多级缓存中
"""

import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

from src.data.fetcher.hybrid_data_fetcher import HybridDataFetcher
from src.utils.config.config_factory import config_factory


class CachePreloader:
    """
    缓存预热器
    
    根据配置文件中的预热设置，自动将热门股票数据加载到多级缓存中，
    提高后续查询的响应速度。
    """
    
    def __init__(
        self,
        fetcher: Optional[HybridDataFetcher] = None,
        tushare_token: Optional[str] = None
    ):
        """
        初始化缓存预热器
        
        Args:
            fetcher: 数据获取器实例，如果为None则自动创建
            tushare_token: Tushare API token
        """
        self.logger = logging.getLogger(__name__)
        self.tushare_token = tushare_token
        
        # 获取应用配置
        try:
            self.app_config = config_factory.load_config('app')
        except Exception:
            self.app_config = {}
        
        # 获取缓存预热配置
        cache_config = self.app_config.get('cache', {})
        self.preload_config = cache_config.get('preload', {})
        
        # 创建或使用提供的数据获取器
        if fetcher:
            self.fetcher = fetcher
        else:
            self.fetcher = self._create_fetcher()
        
        # 统计信息
        self.stats = {
            'preload_start_time': None,
            'preload_end_time': None,
            'total_symbols': 0,
            'successful_symbols': 0,
            'failed_symbols': 0,
            'total_records': 0,
            'total_time': 0.0,
            'avg_throughput': 0.0,
            'cache_stats_before': None,
            'cache_stats_after': None
        }
    
    def _create_fetcher(self) -> HybridDataFetcher:
        """创建数据获取器"""
        try:
            return HybridDataFetcher.from_app_config(
                data_source='tushare',
                storage='sqlite',
                tushare_token=self.tushare_token,
                use_cache=True,
                auto_mode=False
            )
        except Exception as e:
            self.logger.error(f"创建数据获取器失败: {e}")
            raise
    
    def is_preload_enabled(self) -> bool:
        """检查是否启用缓存预热"""
        return self.preload_config.get('enabled', False)
    
    def get_preload_symbols(self) -> List[str]:
        """获取预热股票列表"""
        symbols = self.preload_config.get('symbols', [])
        
        # 如果配置为空，使用默认热门股票
        if not symbols:
            symbols = [
                '000001.SZ', '000002.SZ', '000858.SZ', '000063.SZ',
                '600000.SH', '600036.SH', '600519.SH', '600276.SH',
                '002415.SZ', '300014.SZ', '688009.SH'
            ]
            self.logger.info("使用默认热门股票列表进行预热")
        
        return symbols
    
    def get_preload_data_types(self) -> List[str]:
        """获取预热数据类型"""
        return self.preload_config.get('data_types', ['daily'])
    
    def get_preload_date_range(self) -> tuple:
        """获取预热数据时间范围"""
        # 默认预热最近3个月的数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=90)
        
        return start_date.strftime('%Y%m%d'), end_date.strftime('%Y%m%d')
    
    def preload_cache(self, force: bool = False) -> Dict[str, Any]:
        """
        执行缓存预热
        
        Args:
            force: 是否强制预热（忽略enabled配置）
            
        Returns:
            Dict[str, Any]: 预热结果统计
        """
        if not force and not self.is_preload_enabled():
            self.logger.info("缓存预热未启用，跳过预热")
            return {'status': 'skipped', 'reason': 'not_enabled'}
        
        self.logger.info("开始缓存预热...")
        self.stats['preload_start_time'] = time.time()
        
        try:
            # 获取预热配置
            symbols = self.get_preload_symbols()
            data_types = self.get_preload_data_types()
            start_date, end_date = self.get_preload_date_range()
            
            self.stats['total_symbols'] = len(symbols)
            
            self.logger.info(f"预热配置: {len(symbols)}只股票, {len(data_types)}种数据类型, {start_date}-{end_date}")
            
            # 获取预热前的缓存统计
            if hasattr(self.fetcher.sync_fetcher, 'smart_cache') and self.fetcher.sync_fetcher.smart_cache:
                self.stats['cache_stats_before'] = self.fetcher.sync_fetcher.smart_cache.get_stats()
            
            # 执行预热
            for data_type in data_types:
                self.logger.info(f"预热数据类型: {data_type}")
                
                try:
                    start_time = time.time()
                    
                    data = self.fetcher.fetch_market_data(
                        symbols=symbols,
                        data_type=data_type,
                        start_date=start_date,
                        end_date=end_date,
                        use_cache=True,
                        save=False  # 不保存到数据库，只缓存
                    )
                    
                    end_time = time.time()
                    duration = end_time - start_time
                    records = len(data) if not data.empty else 0
                    
                    self.stats['total_records'] += records
                    self.stats['total_time'] += duration
                    self.stats['successful_symbols'] += len(symbols)
                    
                    self.logger.info(f"预热{data_type}数据完成: {records:,}条记录, 耗时{duration:.3f}秒")
                    
                except Exception as e:
                    self.logger.error(f"预热{data_type}数据失败: {e}")
                    self.stats['failed_symbols'] += len(symbols)
            
            # 获取预热后的缓存统计
            if hasattr(self.fetcher.sync_fetcher, 'smart_cache') and self.fetcher.sync_fetcher.smart_cache:
                self.stats['cache_stats_after'] = self.fetcher.sync_fetcher.smart_cache.get_stats()
            
            # 计算平均吞吐量
            if self.stats['total_time'] > 0:
                self.stats['avg_throughput'] = self.stats['total_records'] / self.stats['total_time']
            
            self.stats['preload_end_time'] = time.time()
            
            self.logger.info("缓存预热完成")
            return self._generate_preload_report()
            
        except Exception as e:
            self.logger.error(f"缓存预热失败: {e}")
            self.stats['preload_end_time'] = time.time()
            return {'status': 'failed', 'error': str(e), 'stats': self.stats}
    
    def _generate_preload_report(self) -> Dict[str, Any]:
        """生成预热报告"""
        total_duration = self.stats['preload_end_time'] - self.stats['preload_start_time']
        
        report = {
            'status': 'completed',
            'summary': {
                'total_symbols': self.stats['total_symbols'],
                'successful_symbols': self.stats['successful_symbols'],
                'failed_symbols': self.stats['failed_symbols'],
                'total_records': self.stats['total_records'],
                'total_time': total_duration,
                'avg_throughput': self.stats['avg_throughput']
            }
        }
        
        # 添加缓存统计对比
        if self.stats['cache_stats_before'] and self.stats['cache_stats_after']:
            before = self.stats['cache_stats_before']
            after = self.stats['cache_stats_after']
            
            report['cache_improvement'] = {
                'l1_cache_size_increase': after.get('l1_cache_size', 0) - before.get('l1_cache_size', 0),
                'l2_cache_size_increase': after.get('l2_cache_size', 0) - before.get('l2_cache_size', 0),
                'l3_cache_size_increase': after.get('l3_cache_size', 0) - before.get('l3_cache_size', 0),
                'total_sets_increase': (
                    after.get('l1_sets', 0) + after.get('l2_sets', 0) + after.get('l3_sets', 0)
                ) - (
                    before.get('l1_sets', 0) + before.get('l2_sets', 0) + before.get('l3_sets', 0)
                )
            }
        
        return report
    
    def get_preload_status(self) -> Dict[str, Any]:
        """获取预热状态"""
        return {
            'enabled': self.is_preload_enabled(),
            'symbols_count': len(self.get_preload_symbols()),
            'data_types': self.get_preload_data_types(),
            'date_range': self.get_preload_date_range(),
            'last_preload_stats': self.stats if self.stats['preload_start_time'] else None
        }
    
    def clear_preloaded_cache(self) -> bool:
        """清空预热的缓存"""
        try:
            if hasattr(self.fetcher.sync_fetcher, 'smart_cache') and self.fetcher.sync_fetcher.smart_cache:
                cache = self.fetcher.sync_fetcher.smart_cache.cache
                if hasattr(cache, 'clear'):
                    cache.clear()
                    self.logger.info("预热缓存已清空")
                    return True
            
            self.logger.warning("无法清空缓存：缓存实例不可用")
            return False
            
        except Exception as e:
            self.logger.error(f"清空预热缓存失败: {e}")
            return False
    
    def schedule_preload(self) -> None:
        """
        计划任务预热（未来扩展）
        
        可以集成到定时任务系统中，按照配置的schedule定期执行预热
        """
        schedule = self.preload_config.get('schedule')
        if schedule:
            self.logger.info(f"计划任务预热配置: {schedule}")
            # TODO: 集成到定时任务系统
        else:
            self.logger.info("未配置计划任务预热")
