"""
缓存工厂
根据配置创建合适的缓存实例
"""

import logging
from typing import Dict, Any, Optional

from src.data.storage.cache.cache_interface import CacheInterface
from src.data.storage.cache.memory_cache import MemoryCache
from src.data.storage.cache.disk_cache import DiskCache
from src.data.storage.cache.redis_cache import RedisCache
from src.data.storage.cache.multi_level_cache import MultiLevelCache


class CacheFactory:
    """
    缓存工厂类
    
    根据配置创建不同类型的缓存实例：
    - memory: 内存缓存
    - disk: 磁盘缓存
    - redis: Redis缓存
    - multi: 多级缓存
    """
    
    @staticmethod
    def create_cache(
        cache_type: str,
        config: Optional[Dict[str, Any]] = None
    ) -> CacheInterface:
        """
        创建缓存实例
        
        参数:
            cache_type: 缓存类型 ('memory', 'disk', 'redis', 'multi')
            config: 缓存配置
            
        返回:
            CacheInterface: 缓存实例
            
        异常:
            ValueError: 不支持的缓存类型
            RuntimeError: 缓存创建失败
        """
        logger = logging.getLogger(__name__)
        config = config or {}
        
        try:
            if cache_type.lower() == 'memory':
                return CacheFactory._create_memory_cache(config)
            elif cache_type.lower() == 'disk':
                return CacheFactory._create_disk_cache(config)
            elif cache_type.lower() == 'redis':
                return CacheFactory._create_redis_cache(config)
            elif cache_type.lower() == 'multi':
                return CacheFactory._create_multi_level_cache(config)
            else:
                raise ValueError(f"不支持的缓存类型: {cache_type}")
                
        except Exception as e:
            logger.error(f"创建{cache_type}缓存失败: {e}")
            raise RuntimeError(f"缓存创建失败: {e}")
    
    @staticmethod
    def _create_memory_cache(config: Dict[str, Any]) -> MemoryCache:
        """创建内存缓存"""
        return MemoryCache(
            max_size=config.get('max_size', 1000),
            default_expires=config.get('ttl', 3600)
        )
    
    @staticmethod
    def _create_disk_cache(config: Dict[str, Any]) -> DiskCache:
        """创建磁盘缓存"""
        # 将max_size_mb转换为字节
        max_size_mb = config.get('max_size_mb', 1024)
        max_size_bytes = max_size_mb * 1024 * 1024 if max_size_mb else None

        return DiskCache(
            cache_dir=config.get('cache_dir', 'data/cache'),
            default_expires=config.get('ttl', 86400),
            max_size=max_size_bytes
        )
    
    @staticmethod
    def _create_redis_cache(config: Dict[str, Any]) -> RedisCache:
        """创建Redis缓存"""
        return RedisCache(
            host=config.get('host', 'localhost'),
            port=config.get('port', 6379),
            db=config.get('db', 0),
            password=config.get('password'),
            default_expires=config.get('ttl', 3600),
            key_prefix=config.get('key_prefix', 'quantification:cache:'),
            connection_pool_size=config.get('connection_pool_size', 10),
            socket_timeout=config.get('socket_timeout', 5.0),
            serialization=config.get('serialization', 'json')
        )
    
    @staticmethod
    def _create_multi_level_cache(config: Dict[str, Any]) -> MultiLevelCache:
        """创建多级缓存"""
        return MultiLevelCache(
            l1_config=config.get('l1', {}),
            l2_config=config.get('l2', {}),
            l3_config=config.get('l3', {}),
            enable_l1=config.get('l1', {}).get('enabled', True),
            enable_l2=config.get('l2', {}).get('enabled', True),
            enable_l3=config.get('l3', {}).get('enabled', True),
            enable_write_back=config.get('penetration', {}).get('write_back', True),
            enable_write_through=config.get('write_through', True)
        )
    
    @staticmethod
    def create_cache_from_app_config(app_config: Dict[str, Any]) -> CacheInterface:
        """
        从应用配置创建缓存
        
        参数:
            app_config: 应用配置字典
            
        返回:
            CacheInterface: 缓存实例
        """
        logger = logging.getLogger(__name__)
        
        # 获取缓存配置
        cache_config = app_config.get('cache', {})
        
        # 确定缓存策略
        strategy = cache_config.get('strategy', 'single')
        
        if strategy == 'multi':
            # 多级缓存
            logger.info("创建多级缓存")
            return CacheFactory.create_cache('multi', cache_config)
        else:
            # 单级缓存，默认使用内存缓存
            cache_type = cache_config.get('type', 'memory')
            logger.info(f"创建单级缓存: {cache_type}")
            return CacheFactory.create_cache(cache_type, cache_config)
    
    @staticmethod
    def get_cache_info(cache: CacheInterface) -> Dict[str, Any]:
        """
        获取缓存信息
        
        参数:
            cache: 缓存实例
            
        返回:
            Dict[str, Any]: 缓存信息
        """
        info = {
            'type': cache.__class__.__name__,
            'module': cache.__class__.__module__
        }
        
        # 获取统计信息
        try:
            if hasattr(cache, 'get_stats'):
                stats = cache.get_stats()
                info.update(stats)
        except Exception as e:
            info['stats_error'] = str(e)
        
        return info
    
    @staticmethod
    def validate_cache_config(cache_type: str, config: Dict[str, Any]) -> bool:
        """
        验证缓存配置
        
        参数:
            cache_type: 缓存类型
            config: 缓存配置
            
        返回:
            bool: 配置是否有效
        """
        logger = logging.getLogger(__name__)
        
        try:
            if cache_type.lower() == 'memory':
                # 验证内存缓存配置
                max_size = config.get('max_size', 1000)
                if not isinstance(max_size, int) or max_size <= 0:
                    logger.error("内存缓存max_size必须是正整数")
                    return False
                    
            elif cache_type.lower() == 'disk':
                # 验证磁盘缓存配置
                cache_dir = config.get('cache_dir', 'data/cache')
                if not isinstance(cache_dir, str):
                    logger.error("磁盘缓存cache_dir必须是字符串")
                    return False
                    
            elif cache_type.lower() == 'redis':
                # 验证Redis缓存配置
                host = config.get('host', 'localhost')
                port = config.get('port', 6379)
                if not isinstance(host, str):
                    logger.error("Redis缓存host必须是字符串")
                    return False
                if not isinstance(port, int) or port <= 0 or port > 65535:
                    logger.error("Redis缓存port必须是有效端口号")
                    return False
                    
            elif cache_type.lower() == 'multi':
                # 验证多级缓存配置
                l1_config = config.get('l1', {})
                l2_config = config.get('l2', {})
                l3_config = config.get('l3', {})
                
                # 至少需要启用一个缓存级别
                l1_enabled = l1_config.get('enabled', True)
                l2_enabled = l2_config.get('enabled', True)
                l3_enabled = l3_config.get('enabled', True)
                
                if not (l1_enabled or l2_enabled or l3_enabled):
                    logger.error("多级缓存至少需要启用一个缓存级别")
                    return False
                    
            else:
                logger.error(f"不支持的缓存类型: {cache_type}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证缓存配置失败: {e}")
            return False
    
    @staticmethod
    def create_default_cache() -> CacheInterface:
        """
        创建默认缓存实例
        
        返回:
            CacheInterface: 默认内存缓存实例
        """
        logger = logging.getLogger(__name__)
        logger.info("创建默认内存缓存")
        
        return MemoryCache(
            max_size=1000,
            default_expires=3600
        )
    
    @staticmethod
    def test_cache_connection(cache: CacheInterface) -> bool:
        """
        测试缓存连接
        
        参数:
            cache: 缓存实例
            
        返回:
            bool: 连接是否正常
        """
        logger = logging.getLogger(__name__)
        
        try:
            # 测试基本操作
            test_key = "cache_factory_test"
            test_value = "test_value"
            
            # 设置测试值
            if not cache.set(test_key, test_value, 60):
                logger.error("缓存设置操作失败")
                return False
            
            # 获取测试值
            retrieved_value = cache.get(test_key)
            if retrieved_value != test_value:
                logger.error(f"缓存获取操作失败: 期望{test_value}, 实际{retrieved_value}")
                return False
            
            # 删除测试值
            if not cache.delete(test_key):
                logger.error("缓存删除操作失败")
                return False
            
            # 验证删除成功
            if cache.exists(test_key):
                logger.error("缓存删除验证失败")
                return False
            
            logger.info("缓存连接测试成功")
            return True
            
        except Exception as e:
            logger.error(f"缓存连接测试失败: {e}")
            return False
