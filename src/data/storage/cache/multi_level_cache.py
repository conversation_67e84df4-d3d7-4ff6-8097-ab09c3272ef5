"""
多级缓存管理器
实现L1(内存)+L2(Redis)+L3(磁盘)三级缓存架构
"""

import logging
import threading
import time
from typing import Any, Dict, List, Optional, Union

from src.data.storage.cache.cache_interface import CacheInterface
from src.data.storage.cache.memory_cache import MemoryCache
from src.data.storage.cache.redis_cache import RedisCache
from src.data.storage.cache.disk_cache import DiskCache


class MultiLevelCache(CacheInterface):
    """
    多级缓存管理器
    
    实现三级缓存架构：
    - L1: 内存缓存 (最热数据，最快访问)
    - L2: Redis缓存 (热数据，分布式共享)
    - L3: 磁盘缓存 (冷数据，持久化存储)
    
    缓存策略：
    - 读取：L1 -> L2 -> L3 -> 数据源
    - 写入：同时写入所有级别（可配置）
    - 回写：下级缓存命中时回写到上级缓存
    """
    
    def __init__(
        self,
        l1_config: Optional[Dict] = None,
        l2_config: Optional[Dict] = None,
        l3_config: Optional[Dict] = None,
        enable_l1: bool = True,
        enable_l2: bool = True,
        enable_l3: bool = True,
        enable_write_back: bool = True,
        enable_write_through: bool = True
    ):
        """
        初始化多级缓存
        
        参数:
            l1_config: L1缓存配置
            l2_config: L2缓存配置
            l3_config: L3缓存配置
            enable_l1: 是否启用L1缓存
            enable_l2: 是否启用L2缓存
            enable_l3: 是否启用L3缓存
            enable_write_back: 是否启用回写策略
            enable_write_through: 是否启用写穿策略
        """
        self.logger = logging.getLogger(__name__)
        
        # 缓存级别配置
        self.enable_l1 = enable_l1
        self.enable_l2 = enable_l2
        self.enable_l3 = enable_l3
        self.enable_write_back = enable_write_back
        self.enable_write_through = enable_write_through
        
        # 初始化各级缓存
        self.l1_cache = None
        self.l2_cache = None
        self.l3_cache = None
        
        # 统计信息
        self._stats = {
            'l1_hits': 0, 'l1_misses': 0, 'l1_sets': 0,
            'l2_hits': 0, 'l2_misses': 0, 'l2_sets': 0,
            'l3_hits': 0, 'l3_misses': 0, 'l3_sets': 0,
            'write_backs': 0, 'errors': 0
        }
        self._stats_lock = threading.RLock()
        
        # 初始化L1缓存 (内存)
        if self.enable_l1:
            try:
                l1_config = l1_config or {}
                self.l1_cache = MemoryCache(
                    max_size=l1_config.get('max_size', 1000),
                    default_expires=l1_config.get('ttl', 1800)
                )
                self.logger.info("L1内存缓存初始化成功")
            except Exception as e:
                self.logger.error(f"L1内存缓存初始化失败: {e}")
                self.enable_l1 = False
        
        # 初始化L2缓存 (Redis)
        if self.enable_l2:
            try:
                l2_config = l2_config or {}
                self.l2_cache = RedisCache(
                    host=l2_config.get('host', 'localhost'),
                    port=l2_config.get('port', 6379),
                    db=l2_config.get('db', 0),
                    password=l2_config.get('password'),
                    default_expires=l2_config.get('ttl', 3600),
                    key_prefix=l2_config.get('key_prefix', 'quantification:cache:'),
                    connection_pool_size=l2_config.get('connection_pool_size', 10),
                    socket_timeout=l2_config.get('socket_timeout', 5.0),
                    serialization=l2_config.get('serialization', 'json')
                )
                self.logger.info("L2 Redis缓存初始化成功")
            except Exception as e:
                self.logger.error(f"L2 Redis缓存初始化失败: {e}")
                self.enable_l2 = False
        
        # 初始化L3缓存 (磁盘)
        if self.enable_l3:
            try:
                l3_config = l3_config or {}
                # 将max_size_mb转换为字节
                max_size_mb = l3_config.get('max_size_mb', 1024)
                max_size_bytes = max_size_mb * 1024 * 1024 if max_size_mb else None

                self.l3_cache = DiskCache(
                    cache_dir=l3_config.get('cache_dir', 'data/cache'),
                    default_expires=l3_config.get('ttl', 86400),
                    max_size=max_size_bytes
                )
                self.logger.info("L3磁盘缓存初始化成功")
            except Exception as e:
                self.logger.error(f"L3磁盘缓存初始化失败: {e}")
                self.enable_l3 = False
        
        # 检查至少有一个缓存级别可用
        if not (self.enable_l1 or self.enable_l2 or self.enable_l3):
            raise RuntimeError("所有缓存级别都初始化失败，多级缓存不可用")
        
        self.logger.info(f"多级缓存初始化完成: L1={self.enable_l1}, L2={self.enable_l2}, L3={self.enable_l3}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        多级缓存获取
        
        查找顺序: L1 -> L2 -> L3
        如果在下级缓存命中，会回写到上级缓存
        """
        try:
            # L1缓存查找
            if self.enable_l1 and self.l1_cache:
                value = self.l1_cache.get(key)
                if value is not None:
                    with self._stats_lock:
                        self._stats['l1_hits'] += 1
                    return value
                else:
                    with self._stats_lock:
                        self._stats['l1_misses'] += 1
            
            # L2缓存查找
            if self.enable_l2 and self.l2_cache:
                value = self.l2_cache.get(key)
                if value is not None:
                    with self._stats_lock:
                        self._stats['l2_hits'] += 1
                    
                    # 回写到L1缓存
                    if self.enable_write_back and self.enable_l1 and self.l1_cache:
                        try:
                            self.l1_cache.set(key, value)
                            with self._stats_lock:
                                self._stats['write_backs'] += 1
                        except Exception as e:
                            self.logger.warning(f"L2->L1回写失败 [{key}]: {e}")
                    
                    return value
                else:
                    with self._stats_lock:
                        self._stats['l2_misses'] += 1
            
            # L3缓存查找
            if self.enable_l3 and self.l3_cache:
                value = self.l3_cache.get(key)
                if value is not None:
                    with self._stats_lock:
                        self._stats['l3_hits'] += 1
                    
                    # 回写到L2和L1缓存
                    if self.enable_write_back:
                        if self.enable_l2 and self.l2_cache:
                            try:
                                self.l2_cache.set(key, value)
                                with self._stats_lock:
                                    self._stats['write_backs'] += 1
                            except Exception as e:
                                self.logger.warning(f"L3->L2回写失败 [{key}]: {e}")
                        
                        if self.enable_l1 and self.l1_cache:
                            try:
                                self.l1_cache.set(key, value)
                                with self._stats_lock:
                                    self._stats['write_backs'] += 1
                            except Exception as e:
                                self.logger.warning(f"L3->L1回写失败 [{key}]: {e}")
                    
                    return value
                else:
                    with self._stats_lock:
                        self._stats['l3_misses'] += 1
            
            # 所有缓存级别都未命中
            return default
            
        except Exception as e:
            self.logger.error(f"多级缓存获取失败 [{key}]: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
            return default
    
    def set(self, key: str, value: Any, expires: Optional[int] = None) -> bool:
        """
        多级缓存设置
        
        写入策略: 同时写入所有启用的缓存级别
        """
        success_count = 0
        total_count = 0
        
        try:
            # 写入L1缓存
            if self.enable_l1 and self.l1_cache:
                total_count += 1
                try:
                    if self.l1_cache.set(key, value, expires):
                        success_count += 1
                        with self._stats_lock:
                            self._stats['l1_sets'] += 1
                except Exception as e:
                    self.logger.warning(f"L1缓存写入失败 [{key}]: {e}")
            
            # 写入L2缓存
            if self.enable_l2 and self.l2_cache:
                total_count += 1
                try:
                    if self.l2_cache.set(key, value, expires):
                        success_count += 1
                        with self._stats_lock:
                            self._stats['l2_sets'] += 1
                except Exception as e:
                    self.logger.warning(f"L2缓存写入失败 [{key}]: {e}")
            
            # 写入L3缓存
            if self.enable_l3 and self.l3_cache:
                total_count += 1
                try:
                    if self.l3_cache.set(key, value, expires):
                        success_count += 1
                        with self._stats_lock:
                            self._stats['l3_sets'] += 1
                except Exception as e:
                    self.logger.warning(f"L3缓存写入失败 [{key}]: {e}")
            
            # 如果至少有一个级别写入成功，则认为操作成功
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"多级缓存设置失败 [{key}]: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
            return False

    def delete(self, key: str) -> bool:
        """
        多级缓存删除

        从所有启用的缓存级别中删除指定键
        """
        success_count = 0
        total_count = 0

        try:
            # 从L1缓存删除
            if self.enable_l1 and self.l1_cache:
                total_count += 1
                try:
                    if self.l1_cache.delete(key):
                        success_count += 1
                except Exception as e:
                    self.logger.warning(f"L1缓存删除失败 [{key}]: {e}")

            # 从L2缓存删除
            if self.enable_l2 and self.l2_cache:
                total_count += 1
                try:
                    if self.l2_cache.delete(key):
                        success_count += 1
                except Exception as e:
                    self.logger.warning(f"L2缓存删除失败 [{key}]: {e}")

            # 从L3缓存删除
            if self.enable_l3 and self.l3_cache:
                total_count += 1
                try:
                    if self.l3_cache.delete(key):
                        success_count += 1
                except Exception as e:
                    self.logger.warning(f"L3缓存删除失败 [{key}]: {e}")

            return success_count > 0

        except Exception as e:
            self.logger.error(f"多级缓存删除失败 [{key}]: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
            return False

    def exists(self, key: str) -> bool:
        """
        检查缓存是否存在

        按L1 -> L2 -> L3顺序检查
        """
        try:
            # 检查L1缓存
            if self.enable_l1 and self.l1_cache:
                if self.l1_cache.exists(key):
                    return True

            # 检查L2缓存
            if self.enable_l2 and self.l2_cache:
                if self.l2_cache.exists(key):
                    return True

            # 检查L3缓存
            if self.enable_l3 and self.l3_cache:
                if self.l3_cache.exists(key):
                    return True

            return False

        except Exception as e:
            self.logger.error(f"多级缓存存在性检查失败 [{key}]: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
            return False

    def clear(self) -> bool:
        """
        清空所有缓存级别
        """
        success_count = 0
        total_count = 0

        try:
            # 清空L1缓存
            if self.enable_l1 and self.l1_cache:
                total_count += 1
                try:
                    if self.l1_cache.clear():
                        success_count += 1
                        self.logger.info("L1缓存已清空")
                except Exception as e:
                    self.logger.warning(f"L1缓存清空失败: {e}")

            # 清空L2缓存
            if self.enable_l2 and self.l2_cache:
                total_count += 1
                try:
                    if self.l2_cache.clear():
                        success_count += 1
                        self.logger.info("L2缓存已清空")
                except Exception as e:
                    self.logger.warning(f"L2缓存清空失败: {e}")

            # 清空L3缓存
            if self.enable_l3 and self.l3_cache:
                total_count += 1
                try:
                    if self.l3_cache.clear():
                        success_count += 1
                        self.logger.info("L3缓存已清空")
                except Exception as e:
                    self.logger.warning(f"L3缓存清空失败: {e}")

            return success_count > 0

        except Exception as e:
            self.logger.error(f"多级缓存清空失败: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
            return False

    def get_stats(self) -> Dict[str, Any]:
        """
        获取多级缓存统计信息
        """
        with self._stats_lock:
            # 计算总体统计
            total_hits = self._stats['l1_hits'] + self._stats['l2_hits'] + self._stats['l3_hits']
            total_misses = self._stats['l1_misses'] + self._stats['l2_misses'] + self._stats['l3_misses']
            total_requests = total_hits + total_misses
            overall_hit_rate = total_hits / total_requests if total_requests > 0 else 0.0

            # 各级缓存命中率
            l1_requests = self._stats['l1_hits'] + self._stats['l1_misses']
            l1_hit_rate = self._stats['l1_hits'] / l1_requests if l1_requests > 0 else 0.0

            l2_requests = self._stats['l2_hits'] + self._stats['l2_misses']
            l2_hit_rate = self._stats['l2_hits'] / l2_requests if l2_requests > 0 else 0.0

            l3_requests = self._stats['l3_hits'] + self._stats['l3_misses']
            l3_hit_rate = self._stats['l3_hits'] / l3_requests if l3_requests > 0 else 0.0

            stats = {
                # 总体统计
                'total_hits': total_hits,
                'total_misses': total_misses,
                'total_requests': total_requests,
                'overall_hit_rate': overall_hit_rate,
                'write_backs': self._stats['write_backs'],
                'errors': self._stats['errors'],

                # L1统计
                'l1_enabled': self.enable_l1,
                'l1_hits': self._stats['l1_hits'],
                'l1_misses': self._stats['l1_misses'],
                'l1_sets': self._stats['l1_sets'],
                'l1_hit_rate': l1_hit_rate,

                # L2统计
                'l2_enabled': self.enable_l2,
                'l2_hits': self._stats['l2_hits'],
                'l2_misses': self._stats['l2_misses'],
                'l2_sets': self._stats['l2_sets'],
                'l2_hit_rate': l2_hit_rate,

                # L3统计
                'l3_enabled': self.enable_l3,
                'l3_hits': self._stats['l3_hits'],
                'l3_misses': self._stats['l3_misses'],
                'l3_sets': self._stats['l3_sets'],
                'l3_hit_rate': l3_hit_rate,

                # 配置信息
                'write_back_enabled': self.enable_write_back,
                'write_through_enabled': self.enable_write_through
            }

            # 获取各级缓存的详细统计
            try:
                if self.enable_l1 and self.l1_cache:
                    l1_stats = self.l1_cache.get_stats()
                    stats['l1_cache_size'] = l1_stats.get('cache_size', 0)

                if self.enable_l2 and self.l2_cache:
                    l2_stats = self.l2_cache.get_stats()
                    stats['l2_cache_size'] = l2_stats.get('cache_size', 0)
                    stats['l2_memory_usage'] = l2_stats.get('memory_usage', 'Unknown')

                if self.enable_l3 and self.l3_cache:
                    l3_stats = self.l3_cache.get_stats()
                    stats['l3_cache_size'] = l3_stats.get('cache_size', 0)

            except Exception as e:
                self.logger.warning(f"获取详细缓存统计失败: {e}")

            return stats

    def reset_stats(self) -> None:
        """重置统计信息"""
        with self._stats_lock:
            self._stats = {
                'l1_hits': 0, 'l1_misses': 0, 'l1_sets': 0,
                'l2_hits': 0, 'l2_misses': 0, 'l2_sets': 0,
                'l3_hits': 0, 'l3_misses': 0, 'l3_sets': 0,
                'write_backs': 0, 'errors': 0
            }

        # 重置各级缓存的统计
        try:
            if self.enable_l1 and self.l1_cache and hasattr(self.l1_cache, 'reset_stats'):
                self.l1_cache.reset_stats()
            if self.enable_l2 and self.l2_cache and hasattr(self.l2_cache, 'reset_stats'):
                self.l2_cache.reset_stats()
            if self.enable_l3 and self.l3_cache and hasattr(self.l3_cache, 'reset_stats'):
                self.l3_cache.reset_stats()
        except Exception as e:
            self.logger.warning(f"重置缓存统计失败: {e}")

    def close(self) -> None:
        """关闭所有缓存连接"""
        try:
            if self.enable_l1 and self.l1_cache and hasattr(self.l1_cache, 'close'):
                self.l1_cache.close()
            if self.enable_l2 and self.l2_cache and hasattr(self.l2_cache, 'close'):
                self.l2_cache.close()
            if self.enable_l3 and self.l3_cache and hasattr(self.l3_cache, 'close'):
                self.l3_cache.close()
            self.logger.info("多级缓存连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭多级缓存连接失败: {e}")

    def get_many(self, keys: List[str], default: Any = None) -> Dict[str, Any]:
        """
        批量获取多个缓存值

        参数:
            keys: 缓存键列表
            default: 默认值，当某个缓存不存在时使用

        返回:
            Dict[str, Any]: 键值对字典
        """
        result = {}

        try:
            # 对每个键执行多级缓存查找
            for key in keys:
                result[key] = self.get(key, default)

            return result

        except Exception as e:
            self.logger.error(f"多级缓存批量获取失败: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
            # 返回所有键的默认值
            return {key: default for key in keys}

    def set_many(self, mapping: Dict[str, Any], expires: Optional[int] = None) -> bool:
        """
        批量设置多个缓存值

        参数:
            mapping: 键值对字典
            expires: 过期时间(秒)，None表示使用默认过期时间

        返回:
            bool: 设置是否全部成功
        """
        try:
            if not mapping:
                return True

            success_count = 0
            total_count = len(mapping)

            # 对每个键值对执行多级缓存设置
            for key, value in mapping.items():
                if self.set(key, value, expires):
                    success_count += 1

            # 如果至少有一半成功，则认为操作成功
            return success_count >= total_count / 2

        except Exception as e:
            self.logger.error(f"多级缓存批量设置失败: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
            return False

    def delete_many(self, keys: List[str]) -> int:
        """
        批量删除多个缓存

        参数:
            keys: 缓存键列表

        返回:
            int: 成功删除的缓存数量
        """
        try:
            if not keys:
                return 0

            success_count = 0

            # 对每个键执行多级缓存删除
            for key in keys:
                if self.delete(key):
                    success_count += 1

            return success_count

        except Exception as e:
            self.logger.error(f"多级缓存批量删除失败: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
            return 0

    def incr(self, key: str, delta: int = 1) -> int:
        """
        增加缓存的值

        参数:
            key: 缓存键
            delta: 增加的数值

        返回:
            int: 增加后的值

        说明:
            如果缓存不存在，则创建缓存并设置为delta
            如果缓存值不是整数，则抛出ValueError
        """
        try:
            # 优先使用L2 Redis缓存的原子性增加操作
            if self.enable_l2 and self.l2_cache and hasattr(self.l2_cache, 'incr'):
                try:
                    result = self.l2_cache.incr(key, delta)

                    # 同步到L1缓存
                    if self.enable_l1 and self.l1_cache:
                        try:
                            self.l1_cache.set(key, result)
                        except Exception as e:
                            self.logger.warning(f"L2->L1同步失败 [{key}]: {e}")

                    return result
                except Exception as e:
                    self.logger.warning(f"L2缓存增加操作失败 [{key}]: {e}")

            # 回退到获取-修改-设置模式
            current_value = self.get(key, 0)

            if not isinstance(current_value, int):
                if current_value == 0:  # 默认值
                    current_value = 0
                else:
                    raise ValueError(f"无法对非整数值执行增加操作: {current_value}")

            new_value = current_value + delta

            if self.set(key, new_value):
                return new_value
            else:
                raise RuntimeError("设置新值失败")

        except Exception as e:
            self.logger.error(f"多级缓存增加操作失败 [{key}]: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
            raise

    def __del__(self):
        """析构函数"""
        self.close()
