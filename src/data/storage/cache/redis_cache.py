"""
Redis缓存
基于Redis的分布式缓存实现
"""

import json
import pickle
import time
import logging
import threading
from typing import Any, Dict, List, Optional, Union
import redis
from redis.exceptions import ConnectionError, TimeoutError, RedisError

from src.data.storage.cache.cache_interface import CacheInterface


class RedisCache(CacheInterface):
    """
    Redis缓存实现
    
    使用Redis存储缓存数据，支持分布式访问、持久化、高性能
    """
    
    def __init__(
        self,
        host: str = 'localhost',
        port: int = 6379,
        db: int = 0,
        password: Optional[str] = None,
        default_expires: Optional[int] = None,
        key_prefix: str = 'quantification:cache:',
        connection_pool_size: int = 10,
        socket_timeout: float = 5.0,
        socket_connect_timeout: float = 5.0,
        decode_responses: bool = True,
        serialization: str = 'json'  # 'json' or 'pickle'
    ):
        """
        初始化Redis缓存
        
        参数:
            host: Redis服务器地址
            port: Redis服务器端口
            db: Redis数据库编号
            password: Redis密码
            default_expires: 默认过期时间(秒)，None表示永不过期
            key_prefix: 缓存键前缀
            connection_pool_size: 连接池大小
            socket_timeout: Socket超时时间
            socket_connect_timeout: Socket连接超时时间
            decode_responses: 是否解码响应
            serialization: 序列化方式 ('json' 或 'pickle')
        """
        self.host = host
        self.port = port
        self.db = db
        self.password = password
        self.default_expires = default_expires
        self.key_prefix = key_prefix
        self.serialization = serialization
        self.logger = logging.getLogger(__name__)
        
        # 创建连接池
        self.pool = redis.ConnectionPool(
            host=host,
            port=port,
            db=db,
            password=password,
            max_connections=connection_pool_size,
            socket_timeout=socket_timeout,
            socket_connect_timeout=socket_connect_timeout,
            decode_responses=decode_responses,
            retry_on_timeout=True
        )
        
        # 创建Redis客户端
        self.redis_client = redis.Redis(connection_pool=self.pool)
        
        # 统计信息
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0
        }
        self._stats_lock = threading.RLock()
        
        # 测试连接
        self._test_connection()
    
    def _test_connection(self) -> bool:
        """测试Redis连接"""
        try:
            self.redis_client.ping()
            self.logger.info(f"Redis连接成功: {self.host}:{self.port}/{self.db}")
            return True
        except Exception as e:
            self.logger.error(f"Redis连接失败: {e}")
            raise ConnectionError(f"无法连接到Redis服务器: {e}")
    
    def _make_key(self, key: str) -> str:
        """生成完整的缓存键"""
        return f"{self.key_prefix}{key}"
    
    def _serialize(self, value: Any) -> Union[str, bytes]:
        """序列化值"""
        try:
            if self.serialization == 'json':
                # {{ AURA-X: Modify - 修复DataFrame序列化问题，避免转换为字符串. Approval: 寸止(ID:数据获取修复). }}
                # 对于pandas DataFrame，强制使用pickle序列化
                import pandas as pd
                if isinstance(value, pd.DataFrame):
                    self.logger.debug("检测到DataFrame，使用pickle序列化")
                    return pickle.dumps(value)
                else:
                    return json.dumps(value, ensure_ascii=False, default=str)
            elif self.serialization == 'pickle':
                return pickle.dumps(value)
            else:
                raise ValueError(f"不支持的序列化方式: {self.serialization}")
        except Exception as e:
            self.logger.error(f"序列化失败: {e}")
            raise
    
    def _deserialize(self, value: Union[str, bytes]) -> Any:
        """反序列化值"""
        try:
            if value is None:
                return None

            # {{ AURA-X: Modify - 修复反序列化逻辑，支持混合序列化格式. Approval: 寸止(ID:数据获取修复). }}
            # 首先尝试pickle反序列化（用于DataFrame）
            if isinstance(value, bytes):
                try:
                    return pickle.loads(value)
                except Exception as pickle_error:
                    # 如果pickle失败，尝试JSON
                    try:
                        value = value.decode('utf-8')
                        return json.loads(value)
                    except Exception as json_error:
                        self.logger.warning(f"pickle和json反序列化都失败: pickle={pickle_error}, json={json_error}")
                        raise pickle_error

            if self.serialization == 'json':
                if isinstance(value, bytes):
                    value = value.decode('utf-8')
                return json.loads(value)
            elif self.serialization == 'pickle':
                if isinstance(value, str):
                    value = value.encode('utf-8')
                return pickle.loads(value)
            else:
                raise ValueError(f"不支持的序列化方式: {self.serialization}")
        except Exception as e:
            self.logger.error(f"反序列化失败: {e}")
            raise
    
    def set(self, key: str, value: Any, expires: Optional[int] = None) -> bool:
        """
        设置缓存
        
        参数:
            key: 缓存键
            value: 缓存值
            expires: 过期时间(秒)，None表示使用默认过期时间
            
        返回:
            bool: 设置是否成功
        """
        try:
            redis_key = self._make_key(key)
            serialized_value = self._serialize(value)
            
            # 确定过期时间
            ex = expires if expires is not None else self.default_expires
            
            result = self.redis_client.set(redis_key, serialized_value, ex=ex)
            
            with self._stats_lock:
                self._stats['sets'] += 1
            
            return bool(result)
        except Exception as e:
            self.logger.error(f"设置Redis缓存失败 [{key}]: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取缓存
        
        参数:
            key: 缓存键
            default: 默认值，当缓存不存在或已过期时返回
            
        返回:
            Any: 缓存值或默认值
        """
        try:
            redis_key = self._make_key(key)
            value = self.redis_client.get(redis_key)
            
            if value is None:
                with self._stats_lock:
                    self._stats['misses'] += 1
                return default
            
            deserialized_value = self._deserialize(value)
            
            with self._stats_lock:
                self._stats['hits'] += 1
            
            return deserialized_value
        except Exception as e:
            self.logger.error(f"获取Redis缓存失败 [{key}]: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
                self._stats['misses'] += 1
            return default
    
    def delete(self, key: str) -> bool:
        """
        删除缓存
        
        参数:
            key: 缓存键
            
        返回:
            bool: 删除是否成功
        """
        try:
            redis_key = self._make_key(key)
            result = self.redis_client.delete(redis_key)
            
            with self._stats_lock:
                self._stats['deletes'] += 1
            
            return result > 0
        except Exception as e:
            self.logger.error(f"删除Redis缓存失败 [{key}]: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
            return False
    
    def exists(self, key: str) -> bool:
        """
        检查缓存是否存在
        
        参数:
            key: 缓存键
            
        返回:
            bool: 缓存是否存在且未过期
        """
        try:
            redis_key = self._make_key(key)
            return bool(self.redis_client.exists(redis_key))
        except Exception as e:
            self.logger.error(f"检查Redis缓存存在性失败 [{key}]: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
            return False
    
    def clear(self) -> bool:
        """
        清空所有缓存（仅清空带前缀的键）
        
        返回:
            bool: 清空是否成功
        """
        try:
            # 使用SCAN命令查找所有带前缀的键
            pattern = f"{self.key_prefix}*"
            keys = []
            
            for key in self.redis_client.scan_iter(match=pattern):
                keys.append(key)
            
            if keys:
                result = self.redis_client.delete(*keys)
                self.logger.info(f"清空Redis缓存: 删除了{result}个键")
                return True
            else:
                self.logger.info("Redis缓存为空，无需清空")
                return True
        except Exception as e:
            self.logger.error(f"清空Redis缓存失败: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
            return False

    def get_many(self, keys: List[str], default: Any = None) -> Dict[str, Any]:
        """
        批量获取多个缓存值

        参数:
            keys: 缓存键列表
            default: 默认值，当某个缓存不存在时使用

        返回:
            Dict[str, Any]: 键值对字典
        """
        try:
            if not keys:
                return {}

            # 生成Redis键
            redis_keys = [self._make_key(key) for key in keys]

            # 批量获取
            values = self.redis_client.mget(redis_keys)

            result = {}
            for i, key in enumerate(keys):
                value = values[i]
                if value is None:
                    result[key] = default
                    with self._stats_lock:
                        self._stats['misses'] += 1
                else:
                    try:
                        result[key] = self._deserialize(value)
                        with self._stats_lock:
                            self._stats['hits'] += 1
                    except Exception as e:
                        self.logger.error(f"反序列化失败 [{key}]: {e}")
                        result[key] = default
                        with self._stats_lock:
                            self._stats['misses'] += 1
                            self._stats['errors'] += 1

            return result
        except Exception as e:
            self.logger.error(f"批量获取Redis缓存失败: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
            # 返回所有键的默认值
            return {key: default for key in keys}

    def set_many(self, mapping: Dict[str, Any], expires: Optional[int] = None) -> bool:
        """
        批量设置多个缓存值

        参数:
            mapping: 键值对字典
            expires: 过期时间(秒)，None表示使用默认过期时间

        返回:
            bool: 设置是否全部成功
        """
        try:
            if not mapping:
                return True

            # 使用管道批量操作
            pipe = self.redis_client.pipeline()

            # 确定过期时间
            ex = expires if expires is not None else self.default_expires

            for key, value in mapping.items():
                redis_key = self._make_key(key)
                serialized_value = self._serialize(value)
                pipe.set(redis_key, serialized_value, ex=ex)

            # 执行批量操作
            results = pipe.execute()

            # 检查所有操作是否成功
            success = all(results)

            with self._stats_lock:
                self._stats['sets'] += len(mapping)

            return success
        except Exception as e:
            self.logger.error(f"批量设置Redis缓存失败: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
            return False

    def delete_many(self, keys: List[str]) -> int:
        """
        批量删除多个缓存

        参数:
            keys: 缓存键列表

        返回:
            int: 成功删除的缓存数量
        """
        try:
            if not keys:
                return 0

            # 生成Redis键
            redis_keys = [self._make_key(key) for key in keys]

            # 批量删除
            result = self.redis_client.delete(*redis_keys)

            with self._stats_lock:
                self._stats['deletes'] += result

            return result
        except Exception as e:
            self.logger.error(f"批量删除Redis缓存失败: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
            return 0

    def incr(self, key: str, delta: int = 1) -> int:
        """
        增加缓存的值

        参数:
            key: 缓存键
            delta: 增加的数值

        返回:
            int: 增加后的值

        说明:
            如果缓存不存在，则创建缓存并设置为delta
            如果缓存值不是整数，则抛出ValueError
        """
        try:
            redis_key = self._make_key(key)

            # Redis的INCRBY命令原子性地增加值
            result = self.redis_client.incrby(redis_key, delta)

            # 如果设置了默认过期时间，需要设置过期时间
            if self.default_expires is not None:
                self.redis_client.expire(redis_key, self.default_expires)

            return result
        except Exception as e:
            self.logger.error(f"增加Redis缓存值失败 [{key}]: {e}")
            with self._stats_lock:
                self._stats['errors'] += 1
            raise

    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息

        返回:
            Dict[str, Any]: 统计信息，包括命中率、缓存数量等
        """
        with self._stats_lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0.0

            # 获取Redis服务器信息
            try:
                info = self.redis_client.info()
                memory_usage = info.get('used_memory_human', 'Unknown')
                connected_clients = info.get('connected_clients', 'Unknown')

                # 获取缓存键数量（带前缀的）
                pattern = f"{self.key_prefix}*"
                cache_size = 0
                for _ in self.redis_client.scan_iter(match=pattern):
                    cache_size += 1

            except Exception as e:
                self.logger.error(f"获取Redis服务器信息失败: {e}")
                memory_usage = 'Unknown'
                connected_clients = 'Unknown'
                cache_size = 'Unknown'

            return {
                'hits': self._stats['hits'],
                'misses': self._stats['misses'],
                'sets': self._stats['sets'],
                'deletes': self._stats['deletes'],
                'errors': self._stats['errors'],
                'hit_rate': hit_rate,
                'cache_size': cache_size,
                'memory_usage': memory_usage,
                'connected_clients': connected_clients,
                'redis_host': f"{self.host}:{self.port}",
                'redis_db': self.db,
                'key_prefix': self.key_prefix,
                'serialization': self.serialization
            }

    def reset_stats(self) -> None:
        """重置统计信息"""
        with self._stats_lock:
            self._stats = {
                'hits': 0,
                'misses': 0,
                'sets': 0,
                'deletes': 0,
                'errors': 0
            }

    def close(self) -> None:
        """关闭Redis连接"""
        try:
            if hasattr(self, 'redis_client'):
                self.redis_client.close()
            if hasattr(self, 'pool'):
                self.pool.disconnect()
            self.logger.info("Redis连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭Redis连接失败: {e}")

    def __del__(self):
        """析构函数，确保连接被关闭"""
        self.close()
