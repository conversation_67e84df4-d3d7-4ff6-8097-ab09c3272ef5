"""
内存缓存
基于Python字典实现的内存缓存系统
"""

import time
import threading
from typing import Any, Dict, List, Optional, Tuple
import logging

from src.data.storage.cache.cache_interface import CacheInterface


class MemoryCache(CacheInterface):
    """
    内存缓存实现
    
    使用Python字典存储缓存数据，支持过期时间，线程安全
    """
    
    def __init__(self, default_expires: Optional[int] = None, expire_seconds: Optional[int] = None, max_size: Optional[int] = None):
        """
        初始化内存缓存

        参数:
            default_expires: 默认过期时间(秒)，None表示永不过期
            expire_seconds: default_expires的别名，方便调用
            max_size: 最大缓存条目数，None表示无限制
        """
        # 使用expire_seconds覆盖default_expires（如果提供）
        if expire_seconds is not None:
            default_expires = expire_seconds

        # 缓存数据格式: {key: (value, expires_at)}
        # expires_at为None表示永不过期，否则是过期的时间戳
        self._cache: Dict[str, Tuple[Any, Optional[float]]] = {}
        self._default_expires = default_expires
        self._max_size = max_size
        self._lock = threading.RLock()  # 可重入锁用于线程安全

        # 统计信息
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0
        }

        self.logger = logging.getLogger(__name__)
    
    def set(self, key: str, value: Any, expires: Optional[int] = None) -> bool:
        """
        设置缓存
        
        参数:
            key: 缓存键
            value: 缓存值
            expires: 过期时间(秒)，None表示使用默认过期时间
            
        返回:
            bool: 设置是否成功
        """
        # 确定过期时间
        expires_at = None
        if expires is not None:
            expires_at = time.time() + expires
        elif self._default_expires is not None:
            expires_at = time.time() + self._default_expires
        
        with self._lock:
            try:
                self._cache[key] = (value, expires_at)
                self._stats['sets'] += 1
                return True
            except Exception as e:
                self.logger.error(f"设置缓存失败: {str(e)}")
                return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取缓存
        
        参数:
            key: 缓存键
            default: 默认值，当缓存不存在或已过期时返回
            
        返回:
            Any: 缓存值或默认值
        """
        with self._lock:
            try:
                if key not in self._cache:
                    self._stats['misses'] += 1
                    return default
                
                value, expires_at = self._cache[key]
                
                # 检查是否过期
                if expires_at is not None and time.time() > expires_at:
                    # 过期了，删除并返回默认值
                    del self._cache[key]
                    self._stats['misses'] += 1
                    return default
                
                self._stats['hits'] += 1
                return value
            except Exception as e:
                self.logger.error(f"获取缓存失败: {str(e)}")
                return default
    
    def delete(self, key: str) -> bool:
        """
        删除缓存
        
        参数:
            key: 缓存键
            
        返回:
            bool: 删除是否成功
        """
        with self._lock:
            try:
                if key in self._cache:
                    del self._cache[key]
                    self._stats['deletes'] += 1
                    return True
                return False
            except Exception as e:
                self.logger.error(f"删除缓存失败: {str(e)}")
                return False
    
    def exists(self, key: str) -> bool:
        """
        检查缓存是否存在
        
        参数:
            key: 缓存键
            
        返回:
            bool: 缓存是否存在且未过期
        """
        with self._lock:
            try:
                if key not in self._cache:
                    return False
                
                _, expires_at = self._cache[key]
                
                # 检查是否过期
                if expires_at is not None and time.time() > expires_at:
                    # 过期了，删除并返回False
                    del self._cache[key]
                    return False
                
                return True
            except Exception as e:
                self.logger.error(f"检查缓存是否存在失败: {str(e)}")
                return False
    
    def clear(self) -> bool:
        """
        清空所有缓存
        
        返回:
            bool: 清空是否成功
        """
        with self._lock:
            try:
                self._cache.clear()
                return True
            except Exception as e:
                self.logger.error(f"清空缓存失败: {str(e)}")
                return False
    
    def get_many(self, keys: List[str], default: Any = None) -> Dict[str, Any]:
        """
        批量获取多个缓存值
        
        参数:
            keys: 缓存键列表
            default: 默认值，当某个缓存不存在时使用
            
        返回:
            Dict[str, Any]: 键值对字典
        """
        result = {}
        with self._lock:
            for key in keys:
                result[key] = self.get(key, default)
        return result
    
    def set_many(self, mapping: Dict[str, Any], expires: Optional[int] = None) -> bool:
        """
        批量设置多个缓存值
        
        参数:
            mapping: 键值对字典
            expires: 过期时间(秒)，None表示使用默认过期时间
            
        返回:
            bool: 设置是否全部成功
        """
        with self._lock:
            try:
                for key, value in mapping.items():
                    self.set(key, value, expires)
                return True
            except Exception as e:
                self.logger.error(f"批量设置缓存失败: {str(e)}")
                return False
    
    def delete_many(self, keys: List[str]) -> int:
        """
        批量删除多个缓存
        
        参数:
            keys: 缓存键列表
            
        返回:
            int: 成功删除的缓存数量
        """
        count = 0
        with self._lock:
            for key in keys:
                if self.delete(key):
                    count += 1
        return count
    
    def incr(self, key: str, delta: int = 1) -> int:
        """
        增加缓存的值
        
        参数:
            key: 缓存键
            delta: 增加的数值
            
        返回:
            int: 增加后的值
            
        说明:
            如果缓存不存在，则创建缓存并设置为delta
            如果缓存值不是整数，则抛出ValueError
        """
        with self._lock:
            try:
                # 获取当前值和过期时间
                if key in self._cache:
                    value, expires_at = self._cache[key]
                    
                    # 检查是否过期
                    if expires_at is not None and time.time() > expires_at:
                        # 过期了，创建新值
                        self._cache[key] = (delta, expires_at)
                        return delta
                    
                    # 检查值是否为整数
                    if not isinstance(value, int):
                        raise ValueError(f"无法对非整数值执行增加操作: {value}")
                    
                    # 更新值
                    new_value = value + delta
                    self._cache[key] = (new_value, expires_at)
                    return new_value
                else:
                    # 缓存不存在，创建新值
                    expires_at = None
                    if self._default_expires is not None:
                        expires_at = time.time() + self._default_expires
                    
                    self._cache[key] = (delta, expires_at)
                    return delta
            except Exception as e:
                self.logger.error(f"增加缓存值失败: {str(e)}")
                raise
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        返回:
            Dict[str, Any]: 统计信息，如缓存数量、命中率等
        """
        with self._lock:
            # 计算命中率
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = 0
            if total_requests > 0:
                hit_rate = self._stats['hits'] / total_requests
            
            # 清理过期的缓存并计算当前缓存数量
            current_time = time.time()
            active_items = 0
            expired_items = 0
            
            for key, (_, expires_at) in list(self._cache.items()):
                if expires_at is not None and current_time > expires_at:
                    expired_items += 1
                    del self._cache[key]
                else:
                    active_items += 1
            
            return {
                'cache_size': active_items,
                'expired_items': expired_items,
                'hit_rate': hit_rate,
                'hits': self._stats['hits'],
                'misses': self._stats['misses'],
                'sets': self._stats['sets'],
                'deletes': self._stats['deletes']
            }

