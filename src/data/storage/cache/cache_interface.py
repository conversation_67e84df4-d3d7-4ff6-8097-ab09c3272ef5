"""
缓存接口
"""

from abc import ABC, abstractmethod
from typing import Any, Optional, List, Dict, Union
import datetime


class CacheInterface(ABC):
    """
    缓存接口抽象类
    定义了所有缓存实现需要支持的方法
    """
    
    @abstractmethod
    def set(self, key: str, value: Any, expires: Optional[int] = None) -> bool:
        """
        设置缓存
        
        参数:
            key: 缓存键
            value: 缓存值
            expires: 过期时间(秒)，None表示永不过期
            
        返回:
            bool: 设置是否成功
        """
        pass
    
    @abstractmethod
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取缓存
        
        参数:
            key: 缓存键
            default: 默认值，当缓存不存在或已过期时返回
            
        返回:
            Any: 缓存值或默认值
        """
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """
        删除缓存
        
        参数:
            key: 缓存键
            
        返回:
            bool: 删除是否成功
        """
        pass
    
    @abstractmethod
    def exists(self, key: str) -> bool:
        """
        检查缓存是否存在
        
        参数:
            key: 缓存键
            
        返回:
            bool: 缓存是否存在且未过期
        """
        pass
    
    @abstractmethod
    def clear(self) -> bool:
        """
        清空所有缓存
        
        返回:
            bool: 清空是否成功
        """
        pass
    
    @abstractmethod
    def get_many(self, keys: List[str], default: Any = None) -> Dict[str, Any]:
        """
        批量获取多个缓存值
        
        参数:
            keys: 缓存键列表
            default: 默认值，当某个缓存不存在时使用
            
        返回:
            Dict[str, Any]: 键值对字典
        """
        pass
    
    @abstractmethod
    def set_many(self, mapping: Dict[str, Any], expires: Optional[int] = None) -> bool:
        """
        批量设置多个缓存值
        
        参数:
            mapping: 键值对字典
            expires: 过期时间(秒)，None表示永不过期
            
        返回:
            bool: 设置是否全部成功
        """
        pass
    
    @abstractmethod
    def delete_many(self, keys: List[str]) -> int:
        """
        批量删除多个缓存
        
        参数:
            keys: 缓存键列表
            
        返回:
            int: 成功删除的缓存数量
        """
        pass
    
    @abstractmethod
    def incr(self, key: str, delta: int = 1) -> int:
        """
        增加缓存的值
        
        参数:
            key: 缓存键
            delta: 增加的数值
            
        返回:
            int: 增加后的值
            
        说明:
            如果缓存不存在，则创建缓存并设置为delta
            如果缓存值不是整数，则抛出ValueError
        """
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        返回:
            Dict[str, Any]: 统计信息，如缓存数量、命中率等
        """
        pass

