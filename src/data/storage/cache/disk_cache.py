"""
磁盘缓存
基于文件系统的持久化缓存实现
"""

import os
import pickle
import time
import logging
import shutil
import hashlib
import threading
from typing import Any, Dict, List, Optional, Tuple, Set

from src.data.storage.cache.cache_interface import CacheInterface
from src.utils.temporary.file_utils import FileUtils


class DiskCache(CacheInterface):
    """
    磁盘缓存实现
    
    使用文件系统存储缓存数据，支持过期时间，通过pickle序列化
    """
    
    def __init__(self, cache_dir: str, default_expires: Optional[int] = None, 
                 max_size: Optional[int] = None, clean_interval: int = 60 * 60):
        """
        初始化磁盘缓存
        
        参数:
            cache_dir: 缓存目录，不存在则创建
            default_expires: 默认过期时间(秒)，None表示永不过期
            max_size: 最大缓存容量(字节)，None表示无限制
            clean_interval: 清理过期缓存的间隔时间(秒)
        """
        self._cache_dir = os.path.abspath(cache_dir)
        self._default_expires = default_expires
        self._max_size = max_size
        self._clean_interval = clean_interval
        self._lock = threading.RLock()
        
        # 确保缓存目录存在
        os.makedirs(self._cache_dir, exist_ok=True)
        
        # 缓存元数据文件
        self._meta_file = os.path.join(self._cache_dir, '.cache_meta')
        
        # 加载缓存元数据（如果存在）
        self._meta = self._load_meta()
        
        # 最后一次清理时间
        self._last_clean_time = time.time()
        
        # 统计信息
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0
        }
        
        self.logger = logging.getLogger(__name__)
        
        # 初始清理
        self._maybe_clean()
    
    def _load_meta(self) -> Dict:
        """加载缓存元数据"""
        meta = {
            'keys': {},  # 格式: {key: (filename, expires_at, size)}
            'total_size': 0
        }
        
        if os.path.exists(self._meta_file):
            try:
                with open(self._meta_file, 'rb') as f:
                    loaded_meta = pickle.load(f)
                    if isinstance(loaded_meta, dict) and 'keys' in loaded_meta:
                        meta = loaded_meta
            except Exception as e:
                self.logger.error(f"加载缓存元数据失败: {str(e)}")
        
        return meta
    
    def _save_meta(self):
        """保存缓存元数据"""
        try:
            with open(self._meta_file, 'wb') as f:
                pickle.dump(self._meta, f)
        except Exception as e:
            self.logger.error(f"保存缓存元数据失败: {str(e)}")
    
    def _key_to_path(self, key: str) -> str:
        """将键转换为文件路径"""
        # 使用哈希是为了避免文件名无效字符和路径过长
        hashed = hashlib.md5(key.encode('utf-8')).hexdigest()
        return os.path.join(self._cache_dir, hashed)
    
    def _maybe_clean(self):
        """根据需要清理过期缓存"""
        current_time = time.time()
        
        # 检查是否需要清理
        if current_time - self._last_clean_time < self._clean_interval:
            return
        
        with self._lock:
            self._last_clean_time = current_time
            
            # 清理过期缓存
            expired_keys = []
            for key, (filename, expires_at, _) in self._meta['keys'].items():
                if expires_at is not None and current_time > expires_at:
                    expired_keys.append(key)
            
            for key in expired_keys:
                self.delete(key)
            
            # 如果超过最大容量，删除最旧的缓存
            if self._max_size is not None and self._meta['total_size'] > self._max_size:
                # 按过期时间排序，优先删除将要过期的
                items = [(k, v[1]) for k, v in self._meta['keys'].items()]
                items.sort(key=lambda x: float('inf') if x[1] is None else x[1])
                
                # 删除直到满足大小要求
                for key, _ in items:
                    if self._meta['total_size'] <= self._max_size:
                        break
                    self.delete(key)
    
    def set(self, key: str, value: Any, expires: Optional[int] = None) -> bool:
        """
        设置缓存
        
        参数:
            key: 缓存键
            value: 缓存值
            expires: 过期时间(秒)，None表示使用默认过期时间
            
        返回:
            bool: 设置是否成功
        """
        self._maybe_clean()
        
        # 确定过期时间
        expires_at = None
        if expires is not None:
            expires_at = time.time() + expires
        elif self._default_expires is not None:
            expires_at = time.time() + self._default_expires
        
        with self._lock:
            try:
                # 先删除旧缓存
                self.delete(key)
                
                # 获取文件路径
                file_path = self._key_to_path(key)
                
                # 序列化并保存
                with open(file_path, 'wb') as f:
                    pickle.dump(value, f)
                
                # 获取文件大小
                size = os.path.getsize(file_path)
                
                # 更新元数据
                self._meta['keys'][key] = (file_path, expires_at, size)
                self._meta['total_size'] += size
                self._save_meta()
                
                self._stats['sets'] += 1
                return True
            except Exception as e:
                self.logger.error(f"设置缓存失败: {str(e)}")
                return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取缓存
        
        参数:
            key: 缓存键
            default: 默认值，当缓存不存在或已过期时返回
            
        返回:
            Any: 缓存值或默认值
        """
        with self._lock:
            try:
                # 检查键是否存在于元数据中
                if key not in self._meta['keys']:
                    self._stats['misses'] += 1
                    return default
                
                file_path, expires_at, _ = self._meta['keys'][key]
                
                # 检查是否过期
                if expires_at is not None and time.time() > expires_at:
                    # 过期了，删除并返回默认值
                    self.delete(key)
                    self._stats['misses'] += 1
                    return default
                
                # 检查文件是否存在
                if not os.path.exists(file_path):
                    # 文件不存在，更新元数据
                    del self._meta['keys'][key]
                    self._save_meta()
                    self._stats['misses'] += 1
                    return default
                
                # 加载文件
                with open(file_path, 'rb') as f:
                    value = pickle.load(f)
                
                self._stats['hits'] += 1
                return value
            except Exception as e:
                self.logger.error(f"获取缓存失败: {str(e)}")
                return default
    
    def delete(self, key: str) -> bool:
        """
        删除缓存
        
        参数:
            key: 缓存键
            
        返回:
            bool: 删除是否成功
        """
        with self._lock:
            try:
                if key in self._meta['keys']:
                    file_path, _, size = self._meta['keys'][key]
                    
                    # 删除文件
                    FileUtils.remove_file(file_path)
                    
                    # 更新元数据
                    del self._meta['keys'][key]
                    self._meta['total_size'] -= size
                    self._save_meta()
                    
                    self._stats['deletes'] += 1
                    return True
                return False
            except Exception as e:
                self.logger.error(f"删除缓存失败: {str(e)}")
                return False
    
    def exists(self, key: str) -> bool:
        """
        检查缓存是否存在
        
        参数:
            key: 缓存键
            
        返回:
            bool: 缓存是否存在且未过期
        """
        with self._lock:
            try:
                # 检查键是否存在于元数据中
                if key not in self._meta['keys']:
                    return False
                
                file_path, expires_at, _ = self._meta['keys'][key]
                
                # 检查是否过期
                if expires_at is not None and time.time() > expires_at:
                    # 过期了，删除并返回False
                    self.delete(key)
                    return False
                
                # 检查文件是否存在
                if not os.path.exists(file_path):
                    # 文件不存在，更新元数据
                    del self._meta['keys'][key]
                    self._save_meta()
                    return False
                
                return True
            except Exception as e:
                self.logger.error(f"检查缓存是否存在失败: {str(e)}")
                return False
    
    def clear(self) -> bool:
        """
        清空所有缓存
        
        返回:
            bool: 清空是否成功
        """
        with self._lock:
            try:
                # 删除所有缓存文件
                for key, (file_path, _, _) in self._meta['keys'].items():
                    FileUtils.remove_file(file_path)
                
                # 重置元数据
                self._meta = {'keys': {}, 'total_size': 0}
                self._save_meta()
                return True
            except Exception as e:
                self.logger.error(f"清空缓存失败: {str(e)}")
                return False
    
    def get_many(self, keys: List[str], default: Any = None) -> Dict[str, Any]:
        """
        批量获取多个缓存值
        
        参数:
            keys: 缓存键列表
            default: 默认值，当某个缓存不存在时使用
            
        返回:
            Dict[str, Any]: 键值对字典
        """
        result = {}
        with self._lock:
            for key in keys:
                result[key] = self.get(key, default)
        return result
    
    def set_many(self, mapping: Dict[str, Any], expires: Optional[int] = None) -> bool:
        """
        批量设置多个缓存值
        
        参数:
            mapping: 键值对字典
            expires: 过期时间(秒)，None表示使用默认过期时间
            
        返回:
            bool: 设置是否全部成功
        """
        success = True
        with self._lock:
            for key, value in mapping.items():
                if not self.set(key, value, expires):
                    success = False
        return success
    
    def delete_many(self, keys: List[str]) -> int:
        """
        批量删除多个缓存
        
        参数:
            keys: 缓存键列表
            
        返回:
            int: 成功删除的缓存数量
        """
        count = 0
        with self._lock:
            for key in keys:
                if self.delete(key):
                    count += 1
        return count
    
    def incr(self, key: str, delta: int = 1) -> int:
        """
        增加缓存的值
        
        参数:
            key: 缓存键
            delta: 增加的数值
            
        返回:
            int: 增加后的值
            
        说明:
            如果缓存不存在，则创建缓存并设置为delta
            如果缓存值不是整数，则抛出ValueError
        """
        with self._lock:
            try:
                # 获取当前值
                if self.exists(key):
                    value = self.get(key)
                    
                    # 检查值是否为整数
                    if not isinstance(value, int):
                        raise ValueError(f"无法对非整数值执行增加操作: {value}")
                    
                    # 更新值
                    new_value = value + delta
                    
                    # 保持原有的过期时间
                    _, expires_at, _ = self._meta['keys'][key]
                    expires = None
                    if expires_at is not None:
                        expires = int(expires_at - time.time())
                        if expires <= 0:  # 已过期
                            expires = self._default_expires
                    
                    self.set(key, new_value, expires)
                    return new_value
                else:
                    # 缓存不存在，创建新值
                    self.set(key, delta)
                    return delta
            except Exception as e:
                self.logger.error(f"增加缓存值失败: {str(e)}")
                raise
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        返回:
            Dict[str, Any]: 统计信息，如缓存数量、命中率等
        """
        with self._lock:
            # 计算命中率
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = 0
            if total_requests > 0:
                hit_rate = self._stats['hits'] / total_requests
            
            # 清理过期的缓存
            self._maybe_clean()
            
            return {
                'cache_size': len(self._meta['keys']),
                'total_size_bytes': self._meta['total_size'],
                'hit_rate': hit_rate,
                'hits': self._stats['hits'],
                'misses': self._stats['misses'],
                'sets': self._stats['sets'],
                'deletes': self._stats['deletes'],
                'cache_dir': self._cache_dir
            }

