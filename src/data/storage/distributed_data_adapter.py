#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分布式数据访问适配器
- 提供统一的分布式数据访问接口
- 支持自动分片和路由
- 实现读写分离和负载均衡
- 支持事务和一致性保证
"""

import pandas as pd
import numpy as np
import logging
import time
import threading
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass
from contextlib import contextmanager
import hashlib

from src.utils.logging.logger_factory import get_logger
from src.data.storage.storage_interface import StorageInterface
from src.data.storage.distributed_db_manager import DistributedDatabaseManager, NodeRole
from src.data.storage.batch_processor import BatchProcessor
from src.data.storage.data_sync_manager import DataSyncManager
from src.data.storage.distributed_transaction_manager import DistributedTransactionManager, TransactionOperation
from src.data.storage.cluster_monitor import ClusterMonitor
from src.data.storage.ops_monitoring_dashboard import OpsMonitoringDashboard
from src.data.storage.automated_ops_manager import AutomatedOpsManager

@dataclass
class ShardingRule:
    """分片规则"""
    table_name: str
    shard_key: str  # 分片键字段
    shard_count: int  # 分片数量
    strategy: str = "hash"  # 分片策略: hash, range, directory

class DistributedDataAdapter(StorageInterface):
    """分布式数据访问适配器"""
    
    def __init__(
        self, 
        config_file: str = None,
        logger: Optional[logging.Logger] = None
    ):
        """
        初始化分布式数据适配器
        
        参数:
            config_file: 配置文件路径
            logger: 日志记录器
        """
        self.logger = logger or get_logger(__name__)
        
        # 分布式数据库管理器
        self.db_manager = DistributedDatabaseManager(config_file, logger)
        
        # 分片规则
        self.sharding_rules: Dict[str, ShardingRule] = {}
        
        # 批量处理器缓存
        self.batch_processors: Dict[str, BatchProcessor] = {}
        
        # 事务管理
        self.active_transactions: Dict[str, Dict[str, Any]] = {}
        self._transaction_lock = threading.RLock()

        # 数据同步管理器
        self.sync_manager = DataSyncManager(self.db_manager)

        # 分布式事务管理器
        self.transaction_manager = DistributedTransactionManager(self.db_manager)

        # 集群监控管理器
        self.cluster_monitor = ClusterMonitor(self.db_manager)

        # 运维监控仪表板
        self.ops_dashboard = OpsMonitoringDashboard(self)

        # 自动化运维管理器
        self.ops_manager = AutomatedOpsManager(self)

        # 默认分片规则
        self._setup_default_sharding_rules()

        # 如果没有节点，创建默认SQLite节点
        self._ensure_default_nodes()

        self.logger.info("✅ 分布式数据适配器初始化完成")
    
    def _setup_default_sharding_rules(self):
        """设置默认分片规则"""
        # 市场数据按股票代码分片
        self.add_sharding_rule("daily", "ts_code", 4)
        self.add_sharding_rule("daily_basic", "ts_code", 4)
        self.add_sharding_rule("weekly", "ts_code", 4)
        self.add_sharding_rule("monthly", "ts_code", 4)
        
        # 财务数据按股票代码分片
        self.add_sharding_rule("income", "ts_code", 2)
        self.add_sharding_rule("balancesheet", "ts_code", 2)
        self.add_sharding_rule("cashflow", "ts_code", 2)
        
        # 参考数据不分片（小表）
        self.add_sharding_rule("stock_list", "ts_code", 1)
    
    def add_sharding_rule(
        self, 
        table_name: str, 
        shard_key: str, 
        shard_count: int,
        strategy: str = "hash"
    ):
        """
        添加分片规则
        
        参数:
            table_name: 表名
            shard_key: 分片键
            shard_count: 分片数量
            strategy: 分片策略
        """
        self.sharding_rules[table_name] = ShardingRule(
            table_name=table_name,
            shard_key=shard_key,
            shard_count=shard_count,
            strategy=strategy
        )
        self.logger.debug(f"添加分片规则: {table_name} -> {shard_key} ({shard_count}分片)")

    def _ensure_default_nodes(self):
        """确保有默认节点可用"""
        stats = self.db_manager.get_cluster_stats()

        if stats['total_nodes'] == 0:
            # 没有节点时，创建默认SQLite节点
            self.logger.info("没有配置节点，创建默认SQLite节点")

            success = self.db_manager.add_node(
                node_id='default_sqlite',
                host='localhost',
                port=0,
                database='output/data/db/sqlite/distributed_default.db',
                username='',
                password='',
                role=NodeRole.MASTER,
                weight=10,
                db_type='sqlite'
            )

            if success:
                self.logger.info("✅ 默认SQLite节点创建成功")
            else:
                self.logger.error("❌ 默认SQLite节点创建失败")
    
    def _get_shard_key_value(self, table_name: str, data: Union[pd.DataFrame, Dict[str, Any]]) -> str:
        """
        获取分片键值
        
        参数:
            table_name: 表名
            data: 数据
            
        返回:
            str: 分片键值
        """
        if table_name not in self.sharding_rules:
            return "default"
        
        shard_rule = self.sharding_rules[table_name]
        shard_key = shard_rule.shard_key
        
        if isinstance(data, pd.DataFrame):
            if shard_key in data.columns and not data.empty:
                # 使用第一行的分片键值
                return str(data[shard_key].iloc[0])
        elif isinstance(data, dict):
            if shard_key in data:
                return str(data[shard_key])
        
        return "default"
    
    def _get_batch_processor(self, node_pool_name: str) -> BatchProcessor:
        """获取批量处理器"""
        if node_pool_name not in self.batch_processors:
            self.batch_processors[node_pool_name] = BatchProcessor(node_pool_name)
        return self.batch_processors[node_pool_name]
    
    def connect(self, **kwargs) -> bool:
        """
        连接到分布式数据库
        
        返回:
            bool: 连接是否成功
        """
        try:
            # 检查是否有可用节点
            stats = self.db_manager.get_cluster_stats()
            if stats['online_nodes'] == 0:
                self.logger.error("没有可用的数据库节点")
                return False
            
            self.logger.info(f"✅ 连接到分布式数据库集群: {stats['online_nodes']} 个在线节点")
            return True
        except Exception as e:
            self.logger.error(f"连接分布式数据库失败: {e}")
            return False
    
    def disconnect(self) -> bool:
        """
        断开分布式数据库连接
        
        返回:
            bool: 是否成功断开
        """
        try:
            self.db_manager.close()
            self.batch_processors.clear()
            self.logger.info("✅ 已断开分布式数据库连接")
            return True
        except Exception as e:
            self.logger.error(f"断开分布式数据库连接失败: {e}")
            return False
    
    def is_connected(self) -> bool:
        """
        检查是否已连接
        
        返回:
            bool: 是否已连接
        """
        stats = self.db_manager.get_cluster_stats()
        return stats['online_nodes'] > 0
    
    def save(
        self, 
        table: str, 
        data: Union[pd.DataFrame, List[Dict[str, Any]], Dict[str, Any]], 
        if_exists: str = 'append', 
        index: bool = False, 
        **kwargs
    ) -> int:
        """
        保存数据到分布式数据库
        
        参数:
            table: 表名
            data: 要保存的数据
            if_exists: 如果表已存在的处理方式
            index: 是否保存索引
            **kwargs: 额外参数
            
        返回:
            int: 受影响的行数
        """
        try:
            # 数据预处理
            if isinstance(data, dict):
                data = pd.DataFrame([data])
            elif isinstance(data, list):
                data = pd.DataFrame(data)
            
            if not isinstance(data, pd.DataFrame) or data.empty:
                return 0
            
            # 获取分片键值
            shard_key_value = self._get_shard_key_value(table, data)
            
            # 获取写入节点
            write_node = self.db_manager.get_write_node(shard_key_value)
            if not write_node:
                raise Exception("没有可用的写入节点")
            
            # 使用批量处理器保存数据
            batch_processor = self._get_batch_processor(write_node.pool_name)
            
            # 根据表名选择保存策略
            if table in ['daily', 'daily_basic', 'weekly', 'monthly']:
                # 市场数据使用去重插入
                stats = batch_processor.bulk_insert(
                    data=data,
                    table_name=table,
                    if_exists=if_exists,
                    index=index
                )
            else:
                # 其他数据使用普通插入
                stats = batch_processor.bulk_insert(
                    data=data,
                    table_name=table,
                    if_exists=if_exists,
                    index=index
                )
            
            self.logger.debug(f"保存数据到表 {table}: {stats.processed_records} 条记录")
            return stats.processed_records
            
        except Exception as e:
            self.logger.error(f"保存数据失败: {table}, 错误: {e}")
            raise
    
    def query(
        self, 
        table: str, 
        conditions: Dict[str, Any] = None, 
        fields: List[str] = None, 
        order_by: str = None, 
        limit: int = None, 
        offset: int = None,
        as_dict: bool = False, 
        **kwargs
    ) -> Union[pd.DataFrame, List[Dict[str, Any]]]:
        """
        从分布式数据库查询数据
        
        参数:
            table: 表名
            conditions: 查询条件
            fields: 要返回的字段
            order_by: 排序字段
            limit: 限制返回行数
            offset: 偏移量
            as_dict: 是否返回字典列表
            **kwargs: 额外参数
            
        返回:
            Union[pd.DataFrame, List[Dict[str, Any]]]: 查询结果
        """
        try:
            # 构建SQL查询
            fields_str = "*" if not fields else ", ".join(fields)
            sql = f"SELECT {fields_str} FROM {table}"
            params = {}

            # 添加WHERE条件
            if conditions:
                where_clauses = []
                for key, value in conditions.items():
                    if value is None:
                        where_clauses.append(f"{key} IS NULL")
                    else:
                        param_name = f"param_{key}"
                        where_clauses.append(f"{key} = :{param_name}")
                        params[param_name] = value

                if where_clauses:
                    sql += " WHERE " + " AND ".join(where_clauses)
            
            # 添加ORDER BY
            if order_by:
                sql += f" ORDER BY {order_by}"
            
            # 添加LIMIT和OFFSET
            if limit:
                sql += f" LIMIT {limit}"
            if offset:
                sql += f" OFFSET {offset}"
            
            # 确定分片键值
            shard_key_value = None
            if conditions and table in self.sharding_rules:
                shard_rule = self.sharding_rules[table]
                if shard_rule.shard_key in conditions:
                    shard_key_value = str(conditions[shard_rule.shard_key])
            
            # 获取读取节点
            read_node = self.db_manager.get_read_node(shard_key_value)
            if not read_node:
                raise Exception("没有可用的读取节点")
            
            # 执行查询
            result = self.db_manager.execute_read(sql, params, shard_key_value)
            
            if as_dict:
                return [dict(row) for row in result.fetchall()]
            else:
                # 转换为DataFrame
                rows = result.fetchall()
                if not rows:
                    return pd.DataFrame()

                # 获取列名
                if hasattr(result, 'keys'):
                    columns = list(result.keys())
                elif hasattr(rows[0], 'keys'):
                    columns = list(rows[0].keys())
                else:
                    columns = [f'col_{i}' for i in range(len(rows[0]))]

                # 转换数据
                if hasattr(rows[0], '_asdict'):
                    # 如果是命名元组
                    data = [list(row) for row in rows]
                elif hasattr(rows[0], 'keys'):
                    # 如果是字典类型
                    data = [list(row.values()) for row in rows]
                else:
                    # 普通元组
                    data = [list(row) for row in rows]

                return pd.DataFrame(data, columns=columns)
                
        except Exception as e:
            self.logger.error(f"查询数据失败: {table}, 错误: {e}")
            raise
    
    def update(
        self, 
        table: str, 
        data: Union[Dict[str, Any], pd.Series], 
        conditions: Dict[str, Any] = None
    ) -> int:
        """
        更新分布式数据库中的数据
        
        参数:
            table: 表名
            data: 要更新的数据
            conditions: 更新条件
            
        返回:
            int: 受影响的行数
        """
        try:
            if isinstance(data, pd.Series):
                data = data.to_dict()
            
            if not data:
                return 0
            
            # 构建UPDATE SQL
            set_clauses = []
            params = []
            
            for key, value in data.items():
                set_clauses.append(f"{key} = %s")
                params.append(value)
            
            sql = f"UPDATE {table} SET " + ", ".join(set_clauses)
            
            # 添加WHERE条件
            if conditions:
                where_clauses = []
                for key, value in conditions.items():
                    if value is None:
                        where_clauses.append(f"{key} IS NULL")
                    else:
                        where_clauses.append(f"{key} = %s")
                        params.append(value)
                
                if where_clauses:
                    sql += " WHERE " + " AND ".join(where_clauses)
            
            # 确定分片键值
            shard_key_value = self._get_shard_key_value(table, data)
            
            # 执行更新
            result = self.db_manager.execute_write(sql, tuple(params), shard_key_value)
            return result.rowcount
            
        except Exception as e:
            self.logger.error(f"更新数据失败: {table}, 错误: {e}")
            raise
    
    def delete(self, table: str, conditions: Dict[str, Any] = None) -> int:
        """
        从分布式数据库删除数据
        
        参数:
            table: 表名
            conditions: 删除条件
            
        返回:
            int: 受影响的行数
        """
        try:
            sql = f"DELETE FROM {table}"
            params = []
            
            # 添加WHERE条件
            if conditions:
                where_clauses = []
                for key, value in conditions.items():
                    if value is None:
                        where_clauses.append(f"{key} IS NULL")
                    else:
                        where_clauses.append(f"{key} = %s")
                        params.append(value)
                
                if where_clauses:
                    sql += " WHERE " + " AND ".join(where_clauses)
            
            # 确定分片键值
            shard_key_value = None
            if conditions and table in self.sharding_rules:
                shard_rule = self.sharding_rules[table]
                if shard_rule.shard_key in conditions:
                    shard_key_value = str(conditions[shard_rule.shard_key])
            
            # 执行删除
            result = self.db_manager.execute_write(sql, tuple(params), shard_key_value)
            return result.rowcount
            
        except Exception as e:
            self.logger.error(f"删除数据失败: {table}, 错误: {e}")
            raise
    
    def get_cluster_stats(self) -> Dict[str, Any]:
        """获取集群统计信息"""
        stats = self.db_manager.get_cluster_stats()
        
        # 添加批量处理统计
        batch_stats = {}
        for pool_name, processor in self.batch_processors.items():
            batch_stats[pool_name] = processor.get_stats()
        
        stats['batch_processing'] = batch_stats
        stats['sharding_rules'] = {
            table: {
                'shard_key': rule.shard_key,
                'shard_count': rule.shard_count,
                'strategy': rule.strategy
            }
            for table, rule in self.sharding_rules.items()
        }
        
        return stats
    
    # 实现其他StorageInterface方法的存根
    def begin_transaction(self) -> bool:
        """开始分布式事务（简化实现）"""
        return True
    
    def commit_transaction(self) -> bool:
        """提交分布式事务（简化实现）"""
        return True
    
    def rollback_transaction(self) -> bool:
        """回滚分布式事务（简化实现）"""
        return True
    
    def execute_in_transaction(self, func: Callable, *args, **kwargs) -> Any:
        """在分布式事务中执行函数（简化实现）"""
        return func(*args, **kwargs)
    
    def execute(self, sql: str, params: tuple = None) -> int:
        """执行SQL语句"""
        result = self.db_manager.execute_write(sql, params)
        return result.rowcount if hasattr(result, 'rowcount') else 0
    
    def table_exists(self, table: str) -> bool:
        """检查表是否存在（简化实现）"""
        try:
            self.query(table, limit=1)
            return True
        except:
            return False

    # 数据同步相关方法

    def start_auto_sync(self):
        """启动自动数据同步"""
        if hasattr(self, 'sync_manager'):
            self.sync_manager.start_auto_sync()

    def stop_auto_sync(self):
        """停止自动数据同步"""
        if hasattr(self, 'sync_manager'):
            self.sync_manager.stop_auto_sync()

    def sync_table(self, table_name: str, sync_type: str = "incremental") -> str:
        """同步指定表"""
        if not hasattr(self, 'sync_manager'):
            raise Exception("数据同步管理器未初始化")

        from src.data.storage.data_sync_manager import SyncType

        sync_type_enum = SyncType.INCREMENTAL
        if sync_type.lower() == "full":
            sync_type_enum = SyncType.FULL
        elif sync_type.lower() == "repair":
            sync_type_enum = SyncType.REPAIR

        return self.sync_manager.force_sync_table(table_name, sync_type_enum)

    def check_data_consistency(self, table_name: str) -> Dict[str, Any]:
        """检查数据一致性"""
        if not hasattr(self, 'sync_manager'):
            raise Exception("数据同步管理器未初始化")

        return self.sync_manager.check_data_consistency(table_name)

    def get_sync_stats(self) -> Dict[str, Any]:
        """获取同步统计"""
        if not hasattr(self, 'sync_manager'):
            return {}

        stats = self.sync_manager.get_sync_stats()
        return {
            'total_tasks': stats.total_tasks,
            'pending_tasks': stats.pending_tasks,
            'running_tasks': stats.running_tasks,
            'success_tasks': stats.success_tasks,
            'failed_tasks': stats.failed_tasks,
            'total_records_synced': stats.total_records_synced,
            'avg_sync_time': stats.avg_sync_time
        }

    # 分布式事务相关方法

    def begin_distributed_transaction(
        self,
        operations: List[Dict[str, Any]],
        timeout: Optional[float] = None
    ) -> str:
        """
        开始分布式事务

        参数:
            operations: 事务操作列表，每个操作包含:
                - operation_type: INSERT/UPDATE/DELETE
                - table_name: 表名
                - data: 数据字典
                - conditions: 条件字典(UPDATE/DELETE时使用)
            timeout: 事务超时时间

        返回:
            str: 事务ID
        """
        if not hasattr(self, 'transaction_manager'):
            raise Exception("分布式事务管理器未初始化")

        # 转换操作格式
        tx_operations = []
        for op in operations:
            tx_op = TransactionOperation(
                operation_id=f"op_{len(tx_operations)}",
                operation_type=op['operation_type'],
                table_name=op['table_name'],
                data=op.get('data', {}),
                conditions=op.get('conditions'),
                node_id=op.get('node_id')
            )
            tx_operations.append(tx_op)

        return self.transaction_manager.begin_transaction(tx_operations, timeout)

    def commit_distributed_transaction(self, transaction_id: str) -> bool:
        """
        提交分布式事务

        参数:
            transaction_id: 事务ID

        返回:
            bool: 是否成功提交
        """
        if not hasattr(self, 'transaction_manager'):
            raise Exception("分布式事务管理器未初始化")

        return self.transaction_manager.commit_transaction(transaction_id)

    def abort_distributed_transaction(self, transaction_id: str) -> bool:
        """
        中止分布式事务

        参数:
            transaction_id: 事务ID

        返回:
            bool: 是否成功中止
        """
        if not hasattr(self, 'transaction_manager'):
            raise Exception("分布式事务管理器未初始化")

        return self.transaction_manager.abort_transaction(transaction_id)

    def get_transaction_status(self, transaction_id: str) -> Optional[Dict[str, Any]]:
        """
        获取事务状态

        参数:
            transaction_id: 事务ID

        返回:
            Optional[Dict[str, Any]]: 事务状态信息
        """
        if not hasattr(self, 'transaction_manager'):
            return None

        return self.transaction_manager.get_transaction_status(transaction_id)

    def get_active_transactions(self) -> List[Dict[str, Any]]:
        """
        获取所有活跃事务

        返回:
            List[Dict[str, Any]]: 活跃事务列表
        """
        if not hasattr(self, 'transaction_manager'):
            return []

        return self.transaction_manager.get_active_transactions()

    def start_transaction_cleanup(self):
        """启动事务清理线程"""
        if hasattr(self, 'transaction_manager'):
            self.transaction_manager.start_cleanup_thread()

    def stop_transaction_cleanup(self):
        """停止事务清理线程"""
        if hasattr(self, 'transaction_manager'):
            self.transaction_manager.stop_cleanup_thread()

    # 集群监控相关方法

    def start_cluster_monitoring(self):
        """启动集群监控"""
        if hasattr(self, 'cluster_monitor'):
            self.cluster_monitor.start_monitoring()

    def stop_cluster_monitoring(self):
        """停止集群监控"""
        if hasattr(self, 'cluster_monitor'):
            self.cluster_monitor.stop_monitoring()

    def get_cluster_status(self) -> Dict[str, Any]:
        """
        获取集群状态

        返回:
            Dict[str, Any]: 集群状态信息
        """
        if not hasattr(self, 'cluster_monitor'):
            return {}

        return self.cluster_monitor.get_cluster_status()

    def get_performance_metrics(self, duration: int = 3600) -> Dict[str, Any]:
        """
        获取性能指标

        参数:
            duration: 时间范围(秒)

        返回:
            Dict[str, Any]: 性能指标数据
        """
        if not hasattr(self, 'cluster_monitor'):
            return {}

        return self.cluster_monitor.get_performance_metrics(duration)

    def get_alert_summary(self) -> Dict[str, Any]:
        """
        获取告警摘要

        返回:
            Dict[str, Any]: 告警摘要信息
        """
        if not hasattr(self, 'cluster_monitor'):
            return {}

        return self.cluster_monitor.get_alert_summary()

    def add_alert_callback(self, callback):
        """
        添加告警回调函数

        参数:
            callback: 告警回调函数
        """
        if hasattr(self, 'cluster_monitor'):
            self.cluster_monitor.add_alert_callback(callback)

    # 运维监控仪表板相关方法

    def start_ops_monitoring(self):
        """启动运维监控仪表板"""
        if hasattr(self, 'ops_dashboard'):
            self.ops_dashboard.start_ops_monitoring()

    def stop_ops_monitoring(self):
        """停止运维监控仪表板"""
        if hasattr(self, 'ops_dashboard'):
            self.ops_dashboard.stop_ops_monitoring()

    def get_ops_dashboard_data(self) -> Dict[str, Any]:
        """
        获取运维仪表板数据

        返回:
            Dict[str, Any]: 仪表板数据
        """
        if not hasattr(self, 'ops_dashboard'):
            return {}

        return self.ops_dashboard.get_dashboard_data()

    def get_capacity_report(self) -> Dict[str, Any]:
        """
        获取容量规划报告

        返回:
            Dict[str, Any]: 容量报告
        """
        if not hasattr(self, 'ops_dashboard'):
            return {}

        return self.ops_dashboard.get_capacity_report()

    def enable_auto_optimization(self, enabled: bool = True):
        """
        启用/禁用自动优化

        参数:
            enabled: 是否启用
        """
        if hasattr(self, 'ops_dashboard'):
            self.ops_dashboard.enable_auto_optimization(enabled)

    # 自动化运维相关方法

    def start_automation(self):
        """启动自动化运维"""
        if hasattr(self, 'ops_manager'):
            self.ops_manager.start_automation()

    def stop_automation(self):
        """停止自动化运维"""
        if hasattr(self, 'ops_manager'):
            self.ops_manager.stop_automation()

    def get_automation_status(self) -> Dict[str, Any]:
        """
        获取自动化运维状态

        返回:
            Dict[str, Any]: 自动化状态
        """
        if not hasattr(self, 'ops_manager'):
            return {}

        return self.ops_manager.get_automation_status()

    def add_automation_rule(self, rule_name: str, rule_config: Dict[str, Any]):
        """
        添加自动化规则

        参数:
            rule_name: 规则名称
            rule_config: 规则配置
        """
        if hasattr(self, 'ops_manager'):
            self.ops_manager.add_automation_rule(rule_name, rule_config)

    def enable_maintenance_task(self, task_id: str, enabled: bool = True):
        """
        启用/禁用维护任务

        参数:
            task_id: 任务ID
            enabled: 是否启用
        """
        if hasattr(self, 'ops_manager'):
            self.ops_manager.enable_maintenance_task(task_id, enabled)

    @contextmanager
    def distributed_transaction(self, operations: List[Dict[str, Any]], timeout: Optional[float] = None):
        """
        分布式事务上下文管理器

        参数:
            operations: 事务操作列表
            timeout: 事务超时时间

        使用示例:
            with adapter.distributed_transaction([
                {'operation_type': 'INSERT', 'table_name': 'daily', 'data': {...}},
                {'operation_type': 'UPDATE', 'table_name': 'daily_basic', 'data': {...}, 'conditions': {...}}
            ]) as tx_id:
                # 事务会自动提交
                pass
        """
        transaction_id = None
        try:
            # 开始事务
            transaction_id = self.begin_distributed_transaction(operations, timeout)
            self.logger.info(f"开始分布式事务上下文: {transaction_id}")

            yield transaction_id

            # 自动提交事务
            success = self.commit_distributed_transaction(transaction_id)
            if not success:
                raise Exception(f"分布式事务提交失败: {transaction_id}")

            self.logger.info(f"✅ 分布式事务上下文提交成功: {transaction_id}")

        except Exception as e:
            # 自动中止事务
            if transaction_id:
                self.abort_distributed_transaction(transaction_id)
                self.logger.error(f"❌ 分布式事务上下文中止: {transaction_id}, 错误: {e}")
            raise

    def disconnect(self):
        """断开连接"""
        try:
            # 关闭自动化运维管理器
            if hasattr(self, 'ops_manager'):
                self.ops_manager.close()

            # 关闭运维监控仪表板
            if hasattr(self, 'ops_dashboard'):
                self.ops_dashboard.close()

            # 关闭集群监控管理器
            if hasattr(self, 'cluster_monitor'):
                self.cluster_monitor.close()

            # 关闭分布式事务管理器
            if hasattr(self, 'transaction_manager'):
                self.transaction_manager.close()

            # 关闭数据同步管理器
            if hasattr(self, 'sync_manager'):
                self.sync_manager.close()

            # 关闭批量处理器
            for processor in self.batch_processors.values():
                processor.close()
            self.batch_processors.clear()

            # 关闭数据库管理器
            self.db_manager.close()

            self.logger.info("✅ 分布式数据适配器已断开连接")

        except Exception as e:
            self.logger.error(f"断开连接失败: {e}")
            raise
