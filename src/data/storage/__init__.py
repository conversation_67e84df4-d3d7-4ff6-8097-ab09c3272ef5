"""
数据存储模块包

包含各种存储适配器，如SQLite、MySQL等
"""

# 导出主要的存储类和接口
from .storage_interface import StorageInterface
from .storage_factory import StorageFactory
from .sqlite_adapter import SQLiteAdapter
from .mysql_adapter import MySQLAdapter
from .connection_pool_manager import get_pool_manager, ConnectionPoolConfig
# {{ AURA-X: Add - 导出分布式数据库相关类. Approval: 寸止(ID:分布式数据库完善). }}
from .distributed_db_manager import DistributedDatabaseManager, NodeRole, NodeStatus
from .distributed_db_factory import (
    DistributedDatabaseFactory,
    get_distributed_db_manager,
    create_test_distributed_db_manager,
    close_all_distributed_db_managers
)
from .distributed_operations import (
    DistributedDataOperations,
    DistributedQueryResult,
    get_distributed_operations
)

__all__ = [
    'StorageInterface',
    'StorageFactory',
    'SQLiteAdapter',
    'MySQLAdapter',
    'get_pool_manager',
    'ConnectionPoolConfig',
    'DistributedDatabaseManager',
    'NodeRole',
    'NodeStatus',
    'DistributedDatabaseFactory',
    'get_distributed_db_manager',
    'create_test_distributed_db_manager',
    'close_all_distributed_db_managers',
    'DistributedDataOperations',
    'DistributedQueryResult',
    'get_distributed_operations'
]
