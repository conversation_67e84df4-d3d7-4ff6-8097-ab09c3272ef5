"""
存储工厂：负责创建不同类型的存储适配器实例
- 支持关系型数据库存储
- 支持时序数据库存储
- 支持键值存储
- 支持文件存储
- 提供统一的存储创建接口
"""

from typing import Dict, Any, Optional, Union, Type
import importlib
from .storage_interface import StorageInterface
import os
from src.data.storage.cache.memory_cache import MemoryCache
from src.data.storage.cache.disk_cache import DiskCache

class StorageFactory:
    """
    存储工厂类，用于创建各种存储适配器实例
    """
    
    # 存储适配器类型映射
    _adapters = {
        # 关系型数据库
        'sqlite': 'src.data.storage.sqlite_adapter.SQLiteAdapter',
        'mysql': 'src.data.storage.mysql_adapter.MySQLAdapter',

        # 时序数据库
        'influxdb': 'src.data.storage.influxdb_adapter.InfluxDBAdapter',

        # 键值存储
        'redis': 'src.data.storage.redis_adapter.RedisAdapter',

        # 文件存储
        'csv': 'src.data.storage.file_adapters.csv_adapter.CSVAdapter',
        'parquet': 'src.data.storage.file_adapters.parquet_adapter.ParquetAdapter',

        # 复合存储
        'composite': 'src.data.storage.composite_storage.CompositeStorage',

        # {{ AURA-X: Add - 集成分布式存储到工厂. Approval: 寸止(ID:分布式集成). }}
        # 分布式存储
        'distributed': 'src.data.storage.distributed_db_factory.get_distributed_db_manager'
    }
    
    # 缓存类型映射（新增）
    _cache_types: Dict[str, Type[Any]] = {
        'memory': None,  # 稍后动态导入
        'disk': None,
    }
    # 缓存实例缓存（新增）
    _cache_instances: Dict[str, Any] = {}
    # 默认缓存目录（新增）
    _default_cache_dir = os.path.join(os.path.expanduser('~'), '.quantification', 'cache')

    @classmethod
    def create(cls, storage_type: str, config: Dict[str, Any] = None) -> StorageInterface:
        """
        创建指定类型的存储适配器实例
        
        参数：
            storage_type: 存储类型，如'sqlite', 'mysql', 'redis'等
            config: 存储配置参数
            
        返回：
            StorageInterface: 存储适配器实例
            
        异常：
            ValueError: 存储类型不支持时抛出
            ImportError: 无法导入对应模块时抛出
        """
        if storage_type not in cls._adapters:
            supported = ', '.join(cls._adapters.keys())
            raise ValueError(f"不支持的存储类型: {storage_type}，支持的类型包括: {supported}")
        
        # 导入适配器类
        module_path = cls._adapters[storage_type]
        module_name, class_name = module_path.rsplit('.', 1)
        
        try:
            module = importlib.import_module(module_name)
            adapter_class = getattr(module, class_name)
        except (ImportError, AttributeError) as e:
            raise ImportError(f"无法导入存储适配器 {module_path}: {str(e)}")
        
        # 创建适配器实例
        if config is None:
            config = {}
            
        return adapter_class(**config)
    
    @classmethod
    def create_from_config(cls, config: Dict[str, Any]) -> StorageInterface:
        """
        从配置字典创建存储适配器实例
        
        参数：
            config: 配置字典，必须包含'type'字段
            
        返回：
            StorageInterface: 存储适配器实例
            
        异常：
            ValueError: 配置不正确时抛出
        """
        if 'type' not in config:
            raise ValueError("配置必须包含'type'字段")
        
        storage_type = config.pop('type')
        return cls.create(storage_type, config)
    
    @classmethod
    def create_composite(cls, config: Dict[str, Any]) -> StorageInterface:
        """
        创建复合存储实例
        
        参数：
            config: 复合存储配置
            
        返回：
            StorageInterface: 复合存储实例
        """
        return cls.create('composite', config)
    
    @classmethod
    def register_adapter(cls, adapter_type: str, module_path: str) -> None:
        """
        注册新的存储适配器类型
        
        参数：
            adapter_type: 适配器类型名称
            module_path: 模块路径，格式为'package.module.Class'
            
        异常：
            ValueError: 适配器类型已存在时抛出
        """
        if adapter_type in cls._adapters:
            raise ValueError(f"适配器类型'{adapter_type}'已存在")
        
        cls._adapters[adapter_type] = module_path
    
    @classmethod
    def get_available_adapters(cls) -> Dict[str, str]:
        """
        获取所有可用的适配器类型
        
        返回：
            Dict[str, str]: 适配器类型到模块路径的映射
        """
        return cls._adapters.copy()

    @classmethod
    def create_cache(cls, cache_type: str, name: str, **kwargs) -> Any:
        """
        创建指定类型的缓存实例
        """
        cache_type = cache_type.lower()
        if cache_type not in cls._cache_types:
            raise ValueError(f"不支持的缓存类型: {cache_type}")
        # 动态导入缓存类
        if cls._cache_types[cache_type] is None:
            if cache_type == 'memory':
                cls._cache_types['memory'] = MemoryCache
            elif cache_type == 'disk':
                cls._cache_types['disk'] = DiskCache
        cache_class = cls._cache_types[cache_type]
        cache = cache_class(**kwargs)
        cls._cache_instances[name] = cache
        return cache

    @classmethod
    def get_cache(cls, name: str) -> Optional[Any]:
        """
        获取已创建的缓存实例
        """
        return cls._cache_instances.get(name)

    @classmethod
    def register_cache_type(cls, name: str, cache_class: Type[Any]):
        """
        注册新的缓存类型
        """
        cls._cache_types[name.lower()] = cache_class

    @classmethod
    def clear_all_caches(cls):
        """
        清空所有缓存实例中的数据
        """
        for cache in cls._cache_instances.values():
            cache.clear()

    @classmethod
    def remove_cache(cls, name: str) -> bool:
        """
        移除缓存实例
        """
        if name in cls._cache_instances:
            del cls._cache_instances[name]
            return True
        return False

    @classmethod
    def get_cache_stats(cls) -> Dict[str, Dict[str, Any]]:
        """
        获取所有缓存实例的统计信息
        """
        stats = {}
        for name, cache in cls._cache_instances.items():
            stats[name] = cache.get_stats()
        return stats

