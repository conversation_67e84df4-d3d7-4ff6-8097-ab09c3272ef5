"""
存储接口抽象类：定义所有存储适配器的标准接口
- 提供统一的数据存取方法
- 支持连接管理和状态查询
- 定义事务操作接口
- 提供数据迁移和备份功能
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple, Union, Set, TypeVar, Generic, Callable
import pandas as pd

# 泛型参数定义
T = TypeVar('T')
K = TypeVar('K')
V = TypeVar('V')

class StorageException(Exception):
    """存储异常的基类"""
    pass

class ConnectionError(StorageException):
    """连接异常"""
    pass

class QueryError(StorageException):
    """查询异常"""
    pass

class TransactionError(StorageException):
    """事务异常"""
    pass

class DataError(StorageException):
    """数据异常"""
    pass

class StorageInterface(ABC):
    """
    存储接口抽象类，所有存储适配器必须实现此接口
    定义了存储的基本操作，包括连接、断开、增删改查、事务等
    """
    
    @abstractmethod
    def connect(self, **connection_params) -> bool:
        """
        连接到存储系统
        
        参数：
            **connection_params: 连接参数，可能包括主机、端口、用户名、密码等
            
        返回：
            bool: 是否连接成功
            
        异常：
            ConnectionError: 连接失败时抛出
        """
        pass
    
    @abstractmethod
    def disconnect(self) -> bool:
        """
        断开存储系统连接
        
        返回：
            bool: 是否断开成功
            
        异常：
            ConnectionError: 断开连接失败时抛出
        """
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """
        检查存储连接状态
        
        返回：
            bool: 是否已连接
        """
        pass
    
    @abstractmethod
    def save(self, data: Union[pd.DataFrame, List[Dict[str, Any]], Dict[str, Any]], 
             table_name: str, **kwargs) -> bool:
        """
        保存数据到存储
        
        参数：
            data: 要保存的数据，可以是DataFrame、字典列表或单个字典
            table_name: 表名/集合名
            **kwargs: 额外参数，可能包括：
                if_exists: 如果表存在的处理方式 ('fail', 'replace', 'append')
                index: 是否将DataFrame的索引写入
                dtype: 列数据类型
                
        返回：
            bool: 是否保存成功
            
        异常：
            DataError: 数据格式错误或保存失败时抛出
            ConnectionError: 连接丢失时抛出
        """
        pass
    
    @abstractmethod
    def query(self, table_name: str, condition: Dict[str, Any] = None, 
              fields: List[str] = None, **kwargs) -> pd.DataFrame:
        """
        从存储查询数据
        
        参数：
            table_name: 表名/集合名
            condition: 查询条件，键值对字典
            fields: 要查询的字段列表，为None时查询所有字段
            **kwargs: 额外参数，可能包括：
                limit: 结果数量限制
                offset: 结果偏移量
                order_by: 排序字段
                asc: 是否升序排序
                
        返回：
            pd.DataFrame: 查询结果
            
        异常：
            QueryError: 查询失败时抛出
            ConnectionError: 连接丢失时抛出
        """
        pass
    
    @abstractmethod
    def update(self, table_name: str, condition: Dict[str, Any], 
               update_values: Dict[str, Any], **kwargs) -> int:
        """
        更新存储中的数据
        
        参数：
            table_name: 表名/集合名
            condition: 更新条件，键值对字典
            update_values: 要更新的值，键值对字典
            **kwargs: 额外参数
            
        返回：
            int: 更新的记录数
            
        异常：
            QueryError: 更新失败时抛出
            ConnectionError: 连接丢失时抛出
        """
        pass
    
    @abstractmethod
    def delete(self, table_name: str, condition: Dict[str, Any], **kwargs) -> int:
        """
        从存储中删除数据
        
        参数：
            table_name: 表名/集合名
            condition: 删除条件，键值对字典
            **kwargs: 额外参数
            
        返回：
            int: 删除的记录数
            
        异常：
            QueryError: 删除失败时抛出
            ConnectionError: 连接丢失时抛出
        """
        pass
    
    @abstractmethod
    def begin_transaction(self):
        """
        开始事务
        
        异常：
            TransactionError: 事务开始失败时抛出
            ConnectionError: 连接丢失时抛出
        """
        pass
    
    @abstractmethod
    def commit_transaction(self):
        """
        提交事务
        
        异常：
            TransactionError: 事务提交失败时抛出
            ConnectionError: 连接丢失时抛出
        """
        pass
    
    @abstractmethod
    def rollback_transaction(self):
        """
        回滚事务
        
        异常：
            TransactionError: 事务回滚失败时抛出
            ConnectionError: 连接丢失时抛出
        """
        pass
    
    def execute_in_transaction(self, func: Callable[[], T]) -> T:
        """
        在事务中执行函数
        
        参数：
            func: 要在事务中执行的函数
            
        返回：
            函数的返回值
            
        异常：
            TransactionError: 事务执行失败时抛出
            ConnectionError: 连接丢失时抛出
        """
        try:
            self.begin_transaction()
            result = func()
            self.commit_transaction()
            return result
        except Exception as e:
            self.rollback_transaction()
            raise TransactionError(f"事务执行失败: {str(e)}") from e
    
    def table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在
        
        参数：
            table_name: 表名/集合名
            
        返回：
            bool: 表是否存在
            
        异常：
            QueryError: 查询失败时抛出
            ConnectionError: 连接丢失时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def create_table(self, table_name: str, schema: Dict[str, str], 
                     primary_keys: List[str] = None, **kwargs) -> bool:
        """
        创建表
        
        参数：
            table_name: 表名/集合名
            schema: 表结构，字段名到类型的映射
            primary_keys: 主键列表
            **kwargs: 额外参数
            
        返回：
            bool: 是否创建成功
            
        异常：
            QueryError: 创建失败时抛出
            ConnectionError: 连接丢失时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def drop_table(self, table_name: str, **kwargs) -> bool:
        """
        删除表
        
        参数：
            table_name: 表名/集合名
            **kwargs: 额外参数
            
        返回：
            bool: 是否删除成功
            
        异常：
            QueryError: 删除失败时抛出
            ConnectionError: 连接丢失时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def backup(self, path: str, tables: List[str] = None) -> bool:
        """
        备份数据到指定路径
        
        参数：
            path: 备份文件路径
            tables: 要备份的表列表，为None时备份所有表
            
        返回：
            bool: 是否备份成功
            
        异常：
            DataError: 备份失败时抛出
            ConnectionError: 连接丢失时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def restore(self, path: str, tables: List[str] = None) -> bool:
        """
        从指定路径恢复数据
        
        参数：
            path: 备份文件路径
            tables: 要恢复的表列表，为None时恢复所有表
            
        返回：
            bool: 是否恢复成功
            
        异常：
            DataError: 恢复失败时抛出
            ConnectionError: 连接丢失时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def get_table_schema(self, table_name: str) -> Dict[str, str]:
        """
        获取表结构
        
        参数：
            table_name: 表名/集合名
            
        返回：
            Dict[str, str]: 表结构，字段名到类型的映射
            
        异常：
            QueryError: 查询失败时抛出
            ConnectionError: 连接丢失时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def get_table_stats(self, table_name: str) -> Dict[str, Any]:
        """
        获取表统计信息
        
        参数：
            table_name: 表名/集合名
            
        返回：
            Dict[str, Any]: 表统计信息，如行数、大小等
            
        异常：
            QueryError: 查询失败时抛出
            ConnectionError: 连接丢失时抛出
        """
        raise NotImplementedError("子类必须实现此方法")
