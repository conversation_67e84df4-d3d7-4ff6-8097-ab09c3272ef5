#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集群监控管理器
- 实时监控集群状态和性能
- 提供智能告警和异常检测
- 支持性能指标收集和分析
"""

import time
import threading
import json
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from collections import deque
import statistics

from src.utils.logging.logger_factory import get_logger
from src.data.storage.distributed_db_manager import DistributedDatabaseManager, NodeRole

class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"      # 计数器
    GAUGE = "gauge"          # 仪表盘
    HISTOGRAM = "histogram"  # 直方图
    TIMER = "timer"          # 计时器

@dataclass
class MetricPoint:
    """指标数据点"""
    timestamp: float
    value: float
    labels: Dict[str, str] = None

@dataclass
class Alert:
    """告警信息"""
    alert_id: str
    level: AlertLevel
    title: str
    message: str
    node_id: Optional[str]
    metric_name: Optional[str]
    threshold: Optional[float]
    current_value: Optional[float]
    timestamp: float
    resolved: bool = False
    resolved_at: Optional[float] = None

@dataclass
class NodeMetrics:
    """节点指标"""
    node_id: str
    role: str
    status: str
    connection_count: int = 0
    query_count: int = 0
    error_count: int = 0
    avg_response_time: float = 0.0
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    disk_usage: float = 0.0
    last_update: float = 0.0

@dataclass
class ClusterMetrics:
    """集群指标"""
    total_nodes: int
    online_nodes: int
    offline_nodes: int
    master_nodes: int
    replica_nodes: int
    total_connections: int
    total_queries: int
    total_errors: int
    avg_cluster_response_time: float
    cluster_health_score: float
    timestamp: float

class ClusterMonitor:
    """集群监控管理器"""
    
    def __init__(self, db_manager: DistributedDatabaseManager):
        self.db_manager = db_manager
        self.logger = get_logger(__name__)
        
        # 监控配置
        self.monitor_interval = 10  # 监控间隔(秒)
        self.metric_retention = 3600  # 指标保留时间(秒)
        self.alert_retention = 86400  # 告警保留时间(秒)
        
        # 数据存储
        self.metrics_history: Dict[str, deque] = {}  # 指标历史数据
        self.node_metrics: Dict[str, NodeMetrics] = {}  # 节点指标
        self.cluster_metrics_history: deque = deque(maxlen=360)  # 集群指标历史
        self.active_alerts: Dict[str, Alert] = {}  # 活跃告警
        self.alert_history: deque = deque(maxlen=1000)  # 告警历史
        
        # 告警规则
        self.alert_rules = self._setup_default_alert_rules()
        
        # 告警回调
        self.alert_callbacks: List[Callable[[Alert], None]] = []
        
        # 监控线程
        self.monitor_thread = None
        self.monitoring = False
        self.monitor_lock = threading.RLock()
        
        self.logger.info("✅ 集群监控管理器初始化完成")
    
    def _setup_default_alert_rules(self) -> Dict[str, Dict[str, Any]]:
        """设置默认告警规则"""
        return {
            'node_offline': {
                'metric': 'node_status',
                'condition': 'equals',
                'threshold': 'offline',
                'level': AlertLevel.CRITICAL,
                'message': '节点离线'
            },
            'high_error_rate': {
                'metric': 'error_rate',
                'condition': 'greater_than',
                'threshold': 0.05,  # 5%错误率
                'level': AlertLevel.ERROR,
                'message': '错误率过高'
            },
            'slow_response': {
                'metric': 'avg_response_time',
                'condition': 'greater_than',
                'threshold': 1000,  # 1秒
                'level': AlertLevel.WARNING,
                'message': '响应时间过慢'
            },
            'high_cpu_usage': {
                'metric': 'cpu_usage',
                'condition': 'greater_than',
                'threshold': 80,  # 80%
                'level': AlertLevel.WARNING,
                'message': 'CPU使用率过高'
            },
            'high_memory_usage': {
                'metric': 'memory_usage',
                'condition': 'greater_than',
                'threshold': 85,  # 85%
                'level': AlertLevel.WARNING,
                'message': '内存使用率过高'
            },
            'cluster_health_low': {
                'metric': 'cluster_health_score',
                'condition': 'less_than',
                'threshold': 70,  # 70分
                'level': AlertLevel.ERROR,
                'message': '集群健康度过低'
            }
        }
    
    def start_monitoring(self):
        """启动监控"""
        if self.monitoring:
            self.logger.warning("监控已在运行")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_worker, daemon=True)
        self.monitor_thread.start()
        self.logger.info("✅ 集群监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        self.logger.info("✅ 集群监控已停止")
    
    def _monitor_worker(self):
        """监控工作线程"""
        while self.monitoring:
            try:
                # 收集指标
                self._collect_metrics()
                
                # 检查告警
                self._check_alerts()
                
                # 清理过期数据
                self._cleanup_expired_data()
                
                # 等待下次监控
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                self.logger.error(f"监控工作线程错误: {e}")
                time.sleep(5)
    
    def _collect_metrics(self):
        """收集指标数据"""
        current_time = time.time()
        
        # 收集节点指标
        total_connections = 0
        total_queries = 0
        total_errors = 0
        response_times = []
        online_count = 0
        
        for node_id, node in self.db_manager.nodes.items():
            try:
                # 收集节点基础指标
                node_metrics = self._collect_node_metrics(node_id, node)
                self.node_metrics[node_id] = node_metrics
                
                # 累计集群指标
                if node_metrics.status == 'online':
                    online_count += 1
                    total_connections += node_metrics.connection_count
                    total_queries += node_metrics.query_count
                    total_errors += node_metrics.error_count
                    if node_metrics.avg_response_time > 0:
                        response_times.append(node_metrics.avg_response_time)
                
            except Exception as e:
                self.logger.error(f"收集节点指标失败: {node_id}, 错误: {e}")
        
        # 计算集群指标
        cluster_metrics = ClusterMetrics(
            total_nodes=len(self.db_manager.nodes),
            online_nodes=online_count,
            offline_nodes=len(self.db_manager.nodes) - online_count,
            master_nodes=len([n for n in self.db_manager.nodes.values() if n.role == NodeRole.MASTER]),
            replica_nodes=len([n for n in self.db_manager.nodes.values() if n.role == NodeRole.REPLICA]),
            total_connections=total_connections,
            total_queries=total_queries,
            total_errors=total_errors,
            avg_cluster_response_time=statistics.mean(response_times) if response_times else 0,
            cluster_health_score=self._calculate_health_score(),
            timestamp=current_time
        )
        
        # 保存集群指标历史
        with self.monitor_lock:
            self.cluster_metrics_history.append(cluster_metrics)
    
    def _collect_node_metrics(self, node_id: str, node) -> NodeMetrics:
        """收集单个节点指标"""
        current_time = time.time()
        
        # 基础指标
        metrics = NodeMetrics(
            node_id=node_id,
            role=node.role.value,
            status='online' if self._check_node_health(node_id) else 'offline',
            last_update=current_time
        )
        
        # 尝试获取连接池统计
        try:
            if hasattr(self.db_manager, 'pool_manager') and node.pool_name:
                pool_stats = self.db_manager.pool_manager.get_pool_stats(node.pool_name)
                if pool_stats:
                    metrics.connection_count = pool_stats.get('active_connections', 0)
        except Exception as e:
            self.logger.debug(f"获取连接池统计失败: {node_id}, {e}")
        
        # 模拟其他指标（在实际应用中应该从真实源获取）
        metrics.query_count = getattr(node, 'query_count', 0)
        metrics.error_count = getattr(node, 'error_count', 0)
        metrics.avg_response_time = getattr(node, 'avg_response_time', 0)
        metrics.cpu_usage = self._get_simulated_cpu_usage(node_id)
        metrics.memory_usage = self._get_simulated_memory_usage(node_id)
        metrics.disk_usage = self._get_simulated_disk_usage(node_id)
        
        return metrics
    
    def _check_node_health(self, node_id: str) -> bool:
        """检查节点健康状态"""
        try:
            node = self.db_manager.nodes.get(node_id)
            if not node or not node.pool_name:
                return False

            # 尝试获取连接测试健康状态
            from sqlalchemy import text

            with self.db_manager.pool_manager.get_connection(node.pool_name) as conn:
                # 执行简单查询测试连接
                if 'mysql' in node_id:
                    conn.execute(text("SELECT 1"))
                else:
                    conn.execute(text("SELECT 1"))
                return True

        except Exception as e:
            self.logger.debug(f"节点健康检查失败: {node_id}, {e}")
            return False
    
    def _get_simulated_cpu_usage(self, node_id: str) -> float:
        """获取模拟CPU使用率"""
        import random
        # 模拟CPU使用率，实际应用中应该从系统获取
        base_usage = 20 + hash(node_id) % 30
        variation = random.uniform(-5, 15)
        return max(0, min(100, base_usage + variation))
    
    def _get_simulated_memory_usage(self, node_id: str) -> float:
        """获取模拟内存使用率"""
        import random
        # 模拟内存使用率
        base_usage = 30 + hash(node_id) % 40
        variation = random.uniform(-10, 20)
        return max(0, min(100, base_usage + variation))
    
    def _get_simulated_disk_usage(self, node_id: str) -> float:
        """获取模拟磁盘使用率"""
        import random
        # 模拟磁盘使用率
        base_usage = 40 + hash(node_id) % 30
        variation = random.uniform(-5, 10)
        return max(0, min(100, base_usage + variation))
    
    def _calculate_health_score(self) -> float:
        """计算集群健康分数"""
        if not self.node_metrics:
            return 0.0
        
        total_score = 0
        node_count = 0
        
        for metrics in self.node_metrics.values():
            node_score = 100
            
            # 节点在线状态
            if metrics.status != 'online':
                node_score -= 50
            
            # CPU使用率影响
            if metrics.cpu_usage > 80:
                node_score -= 20
            elif metrics.cpu_usage > 60:
                node_score -= 10
            
            # 内存使用率影响
            if metrics.memory_usage > 85:
                node_score -= 20
            elif metrics.memory_usage > 70:
                node_score -= 10
            
            # 响应时间影响
            if metrics.avg_response_time > 1000:
                node_score -= 15
            elif metrics.avg_response_time > 500:
                node_score -= 8
            
            # 错误率影响
            if metrics.error_count > 0:
                error_rate = metrics.error_count / max(metrics.query_count, 1)
                if error_rate > 0.05:
                    node_score -= 25
                elif error_rate > 0.01:
                    node_score -= 10
            
            total_score += max(0, node_score)
            node_count += 1
        
        return total_score / node_count if node_count > 0 else 0
    
    def _check_alerts(self):
        """检查告警条件"""
        current_time = time.time()
        
        # 检查节点级别告警
        for node_id, metrics in self.node_metrics.items():
            self._check_node_alerts(node_id, metrics, current_time)
        
        # 检查集群级别告警
        if self.cluster_metrics_history:
            latest_cluster_metrics = self.cluster_metrics_history[-1]
            self._check_cluster_alerts(latest_cluster_metrics, current_time)
    
    def _check_node_alerts(self, node_id: str, metrics: NodeMetrics, current_time: float):
        """检查节点告警"""
        # 节点离线告警
        if metrics.status != 'online':
            self._trigger_alert(
                f"node_offline_{node_id}",
                AlertLevel.CRITICAL,
                f"节点离线: {node_id}",
                f"节点 {node_id} 当前状态为离线",
                node_id,
                "node_status",
                None,
                None,
                current_time
            )
        else:
            self._resolve_alert(f"node_offline_{node_id}", current_time)
        
        # CPU使用率告警
        if metrics.cpu_usage > 80:
            self._trigger_alert(
                f"high_cpu_{node_id}",
                AlertLevel.WARNING,
                f"CPU使用率过高: {node_id}",
                f"节点 {node_id} CPU使用率为 {metrics.cpu_usage:.1f}%",
                node_id,
                "cpu_usage",
                80,
                metrics.cpu_usage,
                current_time
            )
        else:
            self._resolve_alert(f"high_cpu_{node_id}", current_time)
        
        # 内存使用率告警
        if metrics.memory_usage > 85:
            self._trigger_alert(
                f"high_memory_{node_id}",
                AlertLevel.WARNING,
                f"内存使用率过高: {node_id}",
                f"节点 {node_id} 内存使用率为 {metrics.memory_usage:.1f}%",
                node_id,
                "memory_usage",
                85,
                metrics.memory_usage,
                current_time
            )
        else:
            self._resolve_alert(f"high_memory_{node_id}", current_time)
    
    def _check_cluster_alerts(self, cluster_metrics: ClusterMetrics, current_time: float):
        """检查集群告警"""
        # 集群健康度告警
        if cluster_metrics.cluster_health_score < 70:
            self._trigger_alert(
                "cluster_health_low",
                AlertLevel.ERROR,
                "集群健康度过低",
                f"集群健康分数为 {cluster_metrics.cluster_health_score:.1f}",
                None,
                "cluster_health_score",
                70,
                cluster_metrics.cluster_health_score,
                current_time
            )
        else:
            self._resolve_alert("cluster_health_low", current_time)
        
        # 节点离线数量告警
        if cluster_metrics.offline_nodes > 0:
            self._trigger_alert(
                "nodes_offline",
                AlertLevel.ERROR,
                "有节点离线",
                f"当前有 {cluster_metrics.offline_nodes} 个节点离线",
                None,
                "offline_nodes",
                0,
                cluster_metrics.offline_nodes,
                current_time
            )
        else:
            self._resolve_alert("nodes_offline", current_time)
    
    def _trigger_alert(
        self, 
        alert_id: str, 
        level: AlertLevel, 
        title: str, 
        message: str,
        node_id: Optional[str],
        metric_name: Optional[str],
        threshold: Optional[float],
        current_value: Optional[float],
        timestamp: float
    ):
        """触发告警"""
        # 检查是否已存在相同告警
        if alert_id in self.active_alerts and not self.active_alerts[alert_id].resolved:
            return
        
        alert = Alert(
            alert_id=alert_id,
            level=level,
            title=title,
            message=message,
            node_id=node_id,
            metric_name=metric_name,
            threshold=threshold,
            current_value=current_value,
            timestamp=timestamp
        )
        
        with self.monitor_lock:
            self.active_alerts[alert_id] = alert
            self.alert_history.append(alert)
        
        # 调用告警回调
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                self.logger.error(f"告警回调执行失败: {e}")
        
        self.logger.warning(f"🚨 告警触发: {title} - {message}")
    
    def _resolve_alert(self, alert_id: str, timestamp: float):
        """解决告警"""
        with self.monitor_lock:
            if alert_id in self.active_alerts and not self.active_alerts[alert_id].resolved:
                alert = self.active_alerts[alert_id]
                alert.resolved = True
                alert.resolved_at = timestamp
                self.logger.info(f"✅ 告警解决: {alert.title}")
    
    def _cleanup_expired_data(self):
        """清理过期数据"""
        current_time = time.time()
        
        # 清理过期指标
        for metric_name, points in list(self.metrics_history.items()):
            while points and current_time - points[0].timestamp > self.metric_retention:
                points.popleft()
        
        # 清理已解决的过期告警
        with self.monitor_lock:
            expired_alerts = [
                alert_id for alert_id, alert in self.active_alerts.items()
                if alert.resolved and current_time - alert.resolved_at > 3600  # 1小时后清理已解决告警
            ]
            for alert_id in expired_alerts:
                del self.active_alerts[alert_id]
    
    def add_alert_callback(self, callback: Callable[[Alert], None]):
        """添加告警回调函数"""
        self.alert_callbacks.append(callback)
    
    def get_cluster_status(self) -> Dict[str, Any]:
        """获取集群状态"""
        with self.monitor_lock:
            latest_metrics = self.cluster_metrics_history[-1] if self.cluster_metrics_history else None
            
            return {
                'cluster_metrics': asdict(latest_metrics) if latest_metrics else None,
                'node_metrics': {node_id: asdict(metrics) for node_id, metrics in self.node_metrics.items()},
                'active_alerts': {alert_id: asdict(alert) for alert_id, alert in self.active_alerts.items()},
                'monitoring_status': self.monitoring,
                'last_update': latest_metrics.timestamp if latest_metrics else None
            }
    
    def get_performance_metrics(self, duration: int = 3600) -> Dict[str, Any]:
        """获取性能指标"""
        current_time = time.time()
        start_time = current_time - duration
        
        with self.monitor_lock:
            # 过滤时间范围内的集群指标
            filtered_metrics = [
                m for m in self.cluster_metrics_history
                if m.timestamp >= start_time
            ]
            
            if not filtered_metrics:
                return {}
            
            # 计算性能统计
            response_times = [m.avg_cluster_response_time for m in filtered_metrics if m.avg_cluster_response_time > 0]
            health_scores = [m.cluster_health_score for m in filtered_metrics]
            
            return {
                'duration': duration,
                'sample_count': len(filtered_metrics),
                'avg_response_time': statistics.mean(response_times) if response_times else 0,
                'max_response_time': max(response_times) if response_times else 0,
                'min_response_time': min(response_times) if response_times else 0,
                'avg_health_score': statistics.mean(health_scores) if health_scores else 0,
                'min_health_score': min(health_scores) if health_scores else 0,
                'total_queries': sum(m.total_queries for m in filtered_metrics),
                'total_errors': sum(m.total_errors for m in filtered_metrics),
                'error_rate': sum(m.total_errors for m in filtered_metrics) / max(sum(m.total_queries for m in filtered_metrics), 1),
                'metrics_timeline': [asdict(m) for m in filtered_metrics]
            }
    
    def get_alert_summary(self) -> Dict[str, Any]:
        """获取告警摘要"""
        with self.monitor_lock:
            active_by_level = {}
            for alert in self.active_alerts.values():
                if not alert.resolved:
                    level = alert.level.value
                    active_by_level[level] = active_by_level.get(level, 0) + 1
            
            recent_alerts = [
                asdict(alert) for alert in list(self.alert_history)[-10:]
            ]
            
            return {
                'active_alerts_count': len([a for a in self.active_alerts.values() if not a.resolved]),
                'active_by_level': active_by_level,
                'recent_alerts': recent_alerts,
                'total_alerts_today': len([
                    a for a in self.alert_history
                    if time.time() - a.timestamp < 86400
                ])
            }
    
    def close(self):
        """关闭监控管理器"""
        self.stop_monitoring()
        self.logger.info("✅ 集群监控管理器已关闭")
