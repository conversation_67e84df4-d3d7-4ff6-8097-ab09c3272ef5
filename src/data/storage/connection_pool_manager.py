#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能数据库连接池管理器
- 支持SQLite和MySQL的连接池管理
- 实现连接复用和自动回收
- 提供性能监控和统计
"""

import asyncio
import threading
import time
import logging
from typing import Dict, Any, Optional, Union
from contextlib import contextmanager, asynccontextmanager
from dataclasses import dataclass
import sqlite3
import pymysql
from sqlalchemy import create_engine, pool, event
from sqlalchemy.pool import QueuePool, StaticPool

from src.utils.logging.logger_factory import get_logger

@dataclass
class ConnectionPoolConfig:
    """连接池配置"""
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600
    pool_pre_ping: bool = True
    echo: bool = False

@dataclass
class PoolStats:
    """连接池统计信息"""
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    pool_hits: int = 0
    pool_misses: int = 0
    connection_errors: int = 0
    avg_connection_time: float = 0.0

class ConnectionPoolManager:
    """高性能数据库连接池管理器"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化连接池管理器
        
        参数:
            logger: 日志记录器
        """
        self.logger = logger or get_logger(__name__)
        self._pools: Dict[str, Any] = {}
        self._stats: Dict[str, PoolStats] = {}
        self._lock = threading.RLock()
        
    def create_sqlite_pool(
        self, 
        db_path: str, 
        pool_name: str = "default_sqlite",
        config: Optional[ConnectionPoolConfig] = None
    ) -> str:
        """
        创建SQLite连接池
        
        参数:
            db_path: 数据库文件路径
            pool_name: 连接池名称
            config: 连接池配置
            
        返回:
            str: 连接池名称
        """
        if config is None:
            config = ConnectionPoolConfig(
                pool_size=5,
                max_overflow=10,
                pool_timeout=30,
                pool_recycle=3600
            )
        
        with self._lock:
            # SQLite连接字符串
            connection_string = f"sqlite:///{db_path}"
            
            # 创建SQLite引擎，使用StaticPool确保线程安全
            # SQLite使用StaticPool时不支持pool_size等参数
            engine = create_engine(
                connection_string,
                poolclass=StaticPool,
                pool_pre_ping=config.pool_pre_ping,
                echo=config.echo,
                connect_args={
                    'check_same_thread': False,
                    'timeout': 30,
                    'isolation_level': None  # 自动提交模式
                }
            )
            
            # 设置SQLite优化参数
            @event.listens_for(engine, "connect")
            def set_sqlite_pragma(dbapi_connection, connection_record):
                cursor = dbapi_connection.cursor()
                # 性能优化设置
                cursor.execute("PRAGMA journal_mode=WAL")
                cursor.execute("PRAGMA synchronous=NORMAL")
                cursor.execute("PRAGMA cache_size=-64000")  # 64MB缓存
                cursor.execute("PRAGMA temp_store=MEMORY")
                cursor.execute("PRAGMA mmap_size=268435456")  # 256MB内存映射
                cursor.close()
            
            self._pools[pool_name] = engine
            self._stats[pool_name] = PoolStats()
            
            self.logger.info(f"✅ SQLite连接池创建成功: {pool_name} -> {db_path}")
            return pool_name
    
    def create_mysql_pool(
        self,
        host: str,
        port: int,
        user: str,
        password: str,
        database: str,
        pool_name: str = "default_mysql",
        config: Optional[ConnectionPoolConfig] = None
    ) -> str:
        """
        创建MySQL连接池
        
        参数:
            host: 主机地址
            port: 端口号
            user: 用户名
            password: 密码
            database: 数据库名
            pool_name: 连接池名称
            config: 连接池配置
            
        返回:
            str: 连接池名称
        """
        if config is None:
            config = ConnectionPoolConfig(
                pool_size=20,
                max_overflow=50,
                pool_timeout=30,
                pool_recycle=3600
            )
        
        with self._lock:
            # MySQL连接字符串
            connection_string = f"mysql+pymysql://{user}:{password}@{host}:{port}/{database}?charset=utf8mb4"
            
            # 创建MySQL引擎
            engine = create_engine(
                connection_string,
                poolclass=QueuePool,
                pool_size=config.pool_size,
                max_overflow=config.max_overflow,
                pool_timeout=config.pool_timeout,
                pool_recycle=config.pool_recycle,
                pool_pre_ping=config.pool_pre_ping,
                echo=config.echo,
                connect_args={
                    'autocommit': False,
                    'connect_timeout': 10,
                    'read_timeout': 30,
                    'write_timeout': 30,
                    'charset': 'utf8mb4'
                }
            )
            
            self._pools[pool_name] = engine
            self._stats[pool_name] = PoolStats()
            
            self.logger.info(f"✅ MySQL连接池创建成功: {pool_name} -> {host}:{port}/{database}")
            return pool_name
    
    @contextmanager
    def get_connection(self, pool_name: str):
        """
        获取数据库连接（同步版本）
        
        参数:
            pool_name: 连接池名称
            
        返回:
            数据库连接对象
        """
        if pool_name not in self._pools:
            raise ValueError(f"连接池不存在: {pool_name}")
        
        engine = self._pools[pool_name]
        stats = self._stats[pool_name]
        
        start_time = time.time()
        connection = None
        
        try:
            # 获取连接
            connection = engine.connect()
            
            # 更新统计信息
            connection_time = time.time() - start_time
            stats.pool_hits += 1
            stats.active_connections += 1
            stats.avg_connection_time = (
                (stats.avg_connection_time * (stats.pool_hits - 1) + connection_time) / stats.pool_hits
            )
            
            yield connection
            
        except Exception as e:
            stats.connection_errors += 1
            self.logger.error(f"获取数据库连接失败: {e}")
            raise
        finally:
            if connection:
                connection.close()
                stats.active_connections -= 1
    
    def get_pool_stats(self, pool_name: str) -> Optional[PoolStats]:
        """
        获取连接池统计信息
        
        参数:
            pool_name: 连接池名称
            
        返回:
            PoolStats: 统计信息
        """
        if pool_name not in self._stats:
            return None
        
        stats = self._stats[pool_name]
        
        # 更新实时统计
        if pool_name in self._pools:
            engine = self._pools[pool_name]
            pool = engine.pool

            # 不同类型的连接池有不同的方法
            try:
                if hasattr(pool, 'size'):
                    stats.total_connections = pool.size()
                if hasattr(pool, 'checkedin'):
                    stats.idle_connections = pool.checkedin()
            except Exception:
                # SQLite StaticPool可能不支持这些方法
                pass
            
        return stats
    
    def close_pool(self, pool_name: str) -> bool:
        """
        关闭连接池
        
        参数:
            pool_name: 连接池名称
            
        返回:
            bool: 是否成功关闭
        """
        with self._lock:
            if pool_name in self._pools:
                engine = self._pools[pool_name]
                engine.dispose()
                del self._pools[pool_name]
                del self._stats[pool_name]
                
                self.logger.info(f"✅ 连接池已关闭: {pool_name}")
                return True
            
            return False
    
    def close_all_pools(self):
        """关闭所有连接池"""
        with self._lock:
            for pool_name in list(self._pools.keys()):
                self.close_pool(pool_name)
    
    def get_all_stats(self) -> Dict[str, PoolStats]:
        """获取所有连接池的统计信息"""
        return {name: self.get_pool_stats(name) for name in self._pools.keys()}

# 全局连接池管理器实例
_global_pool_manager = None
_pool_manager_lock = threading.Lock()

def get_pool_manager() -> ConnectionPoolManager:
    """获取全局连接池管理器实例（单例模式）"""
    global _global_pool_manager
    
    if _global_pool_manager is None:
        with _pool_manager_lock:
            if _global_pool_manager is None:
                _global_pool_manager = ConnectionPoolManager()
    
    return _global_pool_manager
