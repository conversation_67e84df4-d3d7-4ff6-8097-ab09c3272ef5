#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分布式存储工厂类
- 提供统一的分布式存储创建接口
- 支持多种存储后端的自动切换
- 实现存储适配器的生命周期管理
"""

import os
import logging
from typing import Dict, Any, Optional, Union
from enum import Enum

from src.utils.logging.logger_factory import get_logger
from src.data.storage.storage_interface import StorageInterface
from src.data.storage.sqlite_adapter import SQLiteAdapter
from src.data.storage.distributed_data_adapter import DistributedDataAdapter

class StorageType(Enum):
    """存储类型枚举"""
    SQLITE = "sqlite"
    DISTRIBUTED = "distributed"
    MYSQL = "mysql"
    HYBRID = "hybrid"

class DistributedStorageFactory:
    """分布式存储工厂类"""
    
    _instances: Dict[str, StorageInterface] = {}
    _logger = get_logger(__name__)
    
    @classmethod
    def create_storage(
        cls,
        storage_type: Union[str, StorageType],
        config: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> StorageInterface:
        """
        创建存储适配器实例
        
        参数:
            storage_type: 存储类型
            config: 配置参数
            **kwargs: 额外参数
            
        返回:
            StorageInterface: 存储适配器实例
        """
        if isinstance(storage_type, str):
            storage_type = StorageType(storage_type.lower())
        
        # 生成实例键
        instance_key = cls._generate_instance_key(storage_type, config, **kwargs)
        
        # 检查是否已存在实例
        if instance_key in cls._instances:
            cls._logger.debug(f"返回已存在的存储实例: {instance_key}")
            return cls._instances[instance_key]
        
        # 创建新实例
        try:
            if storage_type == StorageType.SQLITE:
                instance = cls._create_sqlite_storage(config, **kwargs)
            elif storage_type == StorageType.DISTRIBUTED:
                instance = cls._create_distributed_storage(config, **kwargs)
            elif storage_type == StorageType.HYBRID:
                instance = cls._create_hybrid_storage(config, **kwargs)
            else:
                raise ValueError(f"不支持的存储类型: {storage_type}")
            
            # 缓存实例
            cls._instances[instance_key] = instance
            cls._logger.info(f"✅ 创建存储实例成功: {storage_type.value}")
            
            return instance
            
        except Exception as e:
            cls._logger.error(f"创建存储实例失败: {storage_type.value}, 错误: {e}")
            raise
    
    @classmethod
    def _create_sqlite_storage(
        cls, 
        config: Optional[Dict[str, Any]] = None, 
        **kwargs
    ) -> SQLiteAdapter:
        """创建SQLite存储适配器"""
        db_path = kwargs.get('db_path')
        if not db_path and config:
            db_path = config.get('db_path', 'output/data/db/sqlite/quantification.db')
        
        if not db_path:
            db_path = 'output/data/db/sqlite/quantification.db'
        
        # 确保目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        return SQLiteAdapter(
            db_path=db_path,
            auto_create_tables=kwargs.get('auto_create_tables', True)
        )
    
    @classmethod
    def _create_distributed_storage(
        cls, 
        config: Optional[Dict[str, Any]] = None, 
        **kwargs
    ) -> DistributedDataAdapter:
        """创建分布式存储适配器"""
        config_file = kwargs.get('config_file')
        if not config_file and config:
            config_file = config.get('config_file', 'config/distributed_database.json')
        
        if not config_file:
            config_file = 'config/distributed_database.json'
        
        # 检查配置文件是否存在
        if not os.path.exists(config_file):
            cls._logger.warning(f"分布式数据库配置文件不存在: {config_file}")
            # 回退到SQLite
            cls._logger.info("回退到SQLite存储")
            return cls._create_sqlite_storage(config, **kwargs)
        
        return DistributedDataAdapter(config_file=config_file)
    
    @classmethod
    def _create_hybrid_storage(
        cls, 
        config: Optional[Dict[str, Any]] = None, 
        **kwargs
    ) -> StorageInterface:
        """创建混合存储适配器（优先分布式，回退SQLite）"""
        try:
            # 尝试创建分布式存储
            return cls._create_distributed_storage(config, **kwargs)
        except Exception as e:
            cls._logger.warning(f"分布式存储创建失败，回退到SQLite: {e}")
            return cls._create_sqlite_storage(config, **kwargs)
    
    @classmethod
    def _generate_instance_key(
        cls,
        storage_type: StorageType,
        config: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> str:
        """生成实例键"""
        key_parts = [storage_type.value]
        
        if storage_type == StorageType.SQLITE:
            db_path = kwargs.get('db_path')
            if not db_path and config:
                db_path = config.get('db_path')
            if db_path:
                key_parts.append(db_path)
        elif storage_type == StorageType.DISTRIBUTED:
            config_file = kwargs.get('config_file')
            if not config_file and config:
                config_file = config.get('config_file')
            if config_file:
                key_parts.append(config_file)
        
        return ":".join(key_parts)
    
    @classmethod
    def get_storage(
        cls,
        storage_type: Union[str, StorageType],
        config: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Optional[StorageInterface]:
        """
        获取已存在的存储实例
        
        参数:
            storage_type: 存储类型
            config: 配置参数
            **kwargs: 额外参数
            
        返回:
            Optional[StorageInterface]: 存储实例，如果不存在则返回None
        """
        if isinstance(storage_type, str):
            storage_type = StorageType(storage_type.lower())
        
        instance_key = cls._generate_instance_key(storage_type, config, **kwargs)
        return cls._instances.get(instance_key)
    
    @classmethod
    def close_storage(
        cls,
        storage_type: Union[str, StorageType],
        config: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> bool:
        """
        关闭存储实例
        
        参数:
            storage_type: 存储类型
            config: 配置参数
            **kwargs: 额外参数
            
        返回:
            bool: 是否成功关闭
        """
        if isinstance(storage_type, str):
            storage_type = StorageType(storage_type.lower())
        
        instance_key = cls._generate_instance_key(storage_type, config, **kwargs)
        
        if instance_key in cls._instances:
            try:
                instance = cls._instances[instance_key]
                if hasattr(instance, 'disconnect'):
                    instance.disconnect()
                del cls._instances[instance_key]
                cls._logger.info(f"✅ 关闭存储实例: {storage_type.value}")
                return True
            except Exception as e:
                cls._logger.error(f"关闭存储实例失败: {storage_type.value}, 错误: {e}")
                return False
        
        return True
    
    @classmethod
    def close_all_storages(cls) -> bool:
        """
        关闭所有存储实例
        
        返回:
            bool: 是否成功关闭所有实例
        """
        success = True
        
        for instance_key, instance in list(cls._instances.items()):
            try:
                if hasattr(instance, 'disconnect'):
                    instance.disconnect()
                del cls._instances[instance_key]
                cls._logger.debug(f"关闭存储实例: {instance_key}")
            except Exception as e:
                cls._logger.error(f"关闭存储实例失败: {instance_key}, 错误: {e}")
                success = False
        
        cls._instances.clear()
        cls._logger.info("✅ 所有存储实例已关闭")
        return success
    
    @classmethod
    def get_all_instances(cls) -> Dict[str, StorageInterface]:
        """
        获取所有存储实例
        
        返回:
            Dict[str, StorageInterface]: 所有存储实例
        """
        return cls._instances.copy()
    
    @classmethod
    def get_instance_stats(cls) -> Dict[str, Any]:
        """
        获取实例统计信息
        
        返回:
            Dict[str, Any]: 统计信息
        """
        stats = {
            'total_instances': len(cls._instances),
            'instances': {}
        }
        
        for instance_key, instance in cls._instances.items():
            instance_stats = {
                'type': type(instance).__name__,
                'connected': False
            }
            
            try:
                if hasattr(instance, 'is_connected'):
                    instance_stats['connected'] = instance.is_connected()
                
                # 获取特定类型的统计信息
                if hasattr(instance, 'get_cluster_stats'):
                    instance_stats['cluster_stats'] = instance.get_cluster_stats()
                elif hasattr(instance, 'get_performance_stats'):
                    instance_stats['performance_stats'] = instance.get_performance_stats()
                
            except Exception as e:
                instance_stats['error'] = str(e)
            
            stats['instances'][instance_key] = instance_stats
        
        return stats

# 便捷函数
def create_storage(
    storage_type: Union[str, StorageType] = StorageType.HYBRID,
    **kwargs
) -> StorageInterface:
    """
    创建存储适配器的便捷函数
    
    参数:
        storage_type: 存储类型，默认为混合模式
        **kwargs: 额外参数
        
    返回:
        StorageInterface: 存储适配器实例
    """
    return DistributedStorageFactory.create_storage(storage_type, **kwargs)

def get_default_storage() -> StorageInterface:
    """
    获取默认存储适配器
    
    返回:
        StorageInterface: 默认存储适配器实例
    """
    return create_storage(StorageType.HYBRID)

def close_all_storages():
    """关闭所有存储实例"""
    DistributedStorageFactory.close_all_storages()
