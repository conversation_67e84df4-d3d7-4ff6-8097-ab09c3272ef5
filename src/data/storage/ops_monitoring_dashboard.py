#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运维监控仪表板
- 实时性能监控和可视化
- 容量规划和趋势分析
- 自动化运维决策支持
"""

import time
import json
import threading
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import deque
import statistics
from datetime import datetime, timedelta

from src.utils.logging.logger_factory import get_logger
from src.data.storage.cluster_monitor import ClusterMonitor

# 避免循环导入，使用TYPE_CHECKING
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from src.data.storage.distributed_data_adapter import DistributedDataAdapter

@dataclass
class PerformanceTrend:
    """性能趋势数据"""
    timestamp: float
    tps: float
    response_time: float
    error_rate: float
    cpu_usage: float
    memory_usage: float
    connection_count: int
    health_score: float

@dataclass
class CapacityMetrics:
    """容量指标"""
    current_load: float
    peak_load: float
    avg_load: float
    growth_rate: float
    estimated_capacity: float
    utilization_rate: float
    bottleneck_component: str

@dataclass
class OperationalAlert:
    """运维告警"""
    alert_type: str
    severity: str
    component: str
    message: str
    recommendation: str
    auto_action: Optional[str]
    timestamp: float

class OpsMonitoringDashboard:
    """运维监控仪表板"""
    
    def __init__(self, distributed_adapter: 'DistributedDataAdapter'):
        self.adapter = distributed_adapter
        self.logger = get_logger(__name__)
        
        # 性能趋势数据
        self.performance_history: deque = deque(maxlen=1440)  # 24小时数据(分钟级)
        self.hourly_aggregates: deque = deque(maxlen=168)     # 7天数据(小时级)
        self.daily_aggregates: deque = deque(maxlen=30)       # 30天数据(日级)
        
        # 容量规划数据
        self.capacity_metrics: Dict[str, CapacityMetrics] = {}
        
        # 运维告警
        self.operational_alerts: deque = deque(maxlen=1000)
        
        # 监控线程
        self.monitoring_thread = None
        self.monitoring_active = False
        self.data_lock = threading.RLock()
        
        # 自动化运维配置
        self.auto_scaling_enabled = False
        self.auto_optimization_enabled = True
        
        self.logger.info("✅ 运维监控仪表板初始化完成")
    
    def start_ops_monitoring(self):
        """启动运维监控"""
        if self.monitoring_active:
            self.logger.warning("运维监控已在运行")
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._ops_monitoring_worker, daemon=True)
        self.monitoring_thread.start()
        
        # 启动集群监控
        self.adapter.start_cluster_monitoring()
        
        self.logger.info("✅ 运维监控已启动")
    
    def stop_ops_monitoring(self):
        """停止运维监控"""
        self.monitoring_active = False
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=5)
        
        # 停止集群监控
        self.adapter.stop_cluster_monitoring()
        
        self.logger.info("✅ 运维监控已停止")
    
    def _ops_monitoring_worker(self):
        """运维监控工作线程"""
        last_minute = 0
        last_hour = 0
        last_day = 0
        
        while self.monitoring_active:
            try:
                current_time = time.time()
                current_minute = int(current_time // 60)
                current_hour = int(current_time // 3600)
                current_day = int(current_time // 86400)
                
                # 收集性能数据
                self._collect_performance_data()
                
                # 分析容量指标
                self._analyze_capacity_metrics()
                
                # 检查运维告警
                self._check_operational_alerts()
                
                # 执行自动化运维
                if self.auto_optimization_enabled:
                    self._execute_auto_optimization()
                
                # 数据聚合
                if current_minute != last_minute:
                    self._aggregate_minute_data()
                    last_minute = current_minute
                
                if current_hour != last_hour:
                    self._aggregate_hour_data()
                    last_hour = current_hour
                
                if current_day != last_day:
                    self._aggregate_day_data()
                    last_day = current_day
                
                time.sleep(30)  # 30秒监控间隔
                
            except Exception as e:
                self.logger.error(f"运维监控工作线程错误: {e}")
                time.sleep(10)
    
    def _collect_performance_data(self):
        """收集性能数据"""
        try:
            # 获取集群状态
            cluster_status = self.adapter.get_cluster_status()
            
            if not cluster_status.get('cluster_metrics'):
                return
            
            cluster_metrics = cluster_status['cluster_metrics']
            node_metrics = cluster_status.get('node_metrics', {})
            
            # 计算平均CPU和内存使用率
            cpu_usage = 0
            memory_usage = 0
            connection_count = 0
            
            if node_metrics:
                cpu_values = [m['cpu_usage'] for m in node_metrics.values()]
                memory_values = [m['memory_usage'] for m in node_metrics.values()]
                connection_values = [m['connection_count'] for m in node_metrics.values()]
                
                cpu_usage = statistics.mean(cpu_values) if cpu_values else 0
                memory_usage = statistics.mean(memory_values) if memory_values else 0
                connection_count = sum(connection_values)
            
            # 创建性能趋势数据
            trend_data = PerformanceTrend(
                timestamp=time.time(),
                tps=cluster_metrics.get('total_queries', 0) / 60,  # 估算TPS
                response_time=cluster_metrics.get('avg_cluster_response_time', 0),
                error_rate=cluster_metrics.get('total_errors', 0) / max(cluster_metrics.get('total_queries', 1), 1),
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                connection_count=connection_count,
                health_score=cluster_metrics.get('cluster_health_score', 0)
            )
            
            with self.data_lock:
                self.performance_history.append(trend_data)
                
        except Exception as e:
            self.logger.error(f"收集性能数据失败: {e}")
    
    def _analyze_capacity_metrics(self):
        """分析容量指标"""
        try:
            if len(self.performance_history) < 10:
                return
            
            with self.data_lock:
                recent_data = list(self.performance_history)[-60:]  # 最近30分钟
                
                # 分析TPS容量
                tps_values = [d.tps for d in recent_data]
                current_tps = statistics.mean(tps_values[-5:]) if len(tps_values) >= 5 else 0
                peak_tps = max(tps_values) if tps_values else 0
                avg_tps = statistics.mean(tps_values) if tps_values else 0
                
                # 计算增长率
                if len(tps_values) >= 30:
                    early_avg = statistics.mean(tps_values[:15])
                    late_avg = statistics.mean(tps_values[-15:])
                    growth_rate = (late_avg - early_avg) / max(early_avg, 1) * 100
                else:
                    growth_rate = 0
                
                # 估算容量
                estimated_capacity = peak_tps * 1.5  # 预留50%容量
                utilization_rate = current_tps / max(estimated_capacity, 1) * 100
                
                # 识别瓶颈
                bottleneck = self._identify_bottleneck(recent_data)
                
                self.capacity_metrics['tps'] = CapacityMetrics(
                    current_load=current_tps,
                    peak_load=peak_tps,
                    avg_load=avg_tps,
                    growth_rate=growth_rate,
                    estimated_capacity=estimated_capacity,
                    utilization_rate=utilization_rate,
                    bottleneck_component=bottleneck
                )
                
        except Exception as e:
            self.logger.error(f"分析容量指标失败: {e}")
    
    def _identify_bottleneck(self, data: List[PerformanceTrend]) -> str:
        """识别系统瓶颈"""
        if not data:
            return "unknown"
        
        # 分析各项指标
        avg_cpu = statistics.mean([d.cpu_usage for d in data])
        avg_memory = statistics.mean([d.memory_usage for d in data])
        avg_response_time = statistics.mean([d.response_time for d in data])
        avg_error_rate = statistics.mean([d.error_rate for d in data])
        
        # 识别瓶颈
        if avg_cpu > 80:
            return "cpu"
        elif avg_memory > 85:
            return "memory"
        elif avg_response_time > 1000:
            return "io"
        elif avg_error_rate > 0.05:
            return "application"
        else:
            return "none"
    
    def _check_operational_alerts(self):
        """检查运维告警"""
        try:
            if not self.capacity_metrics:
                return
            
            current_time = time.time()
            
            # 检查TPS容量告警
            tps_metrics = self.capacity_metrics.get('tps')
            if tps_metrics:
                if tps_metrics.utilization_rate > 80:
                    alert = OperationalAlert(
                        alert_type="capacity",
                        severity="warning",
                        component="tps",
                        message=f"TPS使用率过高: {tps_metrics.utilization_rate:.1f}%",
                        recommendation="考虑扩容或优化查询性能",
                        auto_action="scale_out" if self.auto_scaling_enabled else None,
                        timestamp=current_time
                    )
                    self._add_operational_alert(alert)
                
                if tps_metrics.growth_rate > 50:
                    alert = OperationalAlert(
                        alert_type="trend",
                        severity="info",
                        component="tps",
                        message=f"TPS增长率较高: {tps_metrics.growth_rate:.1f}%",
                        recommendation="监控负载趋势，准备扩容计划",
                        auto_action=None,
                        timestamp=current_time
                    )
                    self._add_operational_alert(alert)
                
                # 瓶颈告警
                if tps_metrics.bottleneck_component != "none":
                    alert = OperationalAlert(
                        alert_type="bottleneck",
                        severity="warning",
                        component=tps_metrics.bottleneck_component,
                        message=f"检测到系统瓶颈: {tps_metrics.bottleneck_component}",
                        recommendation=self._get_bottleneck_recommendation(tps_metrics.bottleneck_component),
                        auto_action=f"optimize_{tps_metrics.bottleneck_component}",
                        timestamp=current_time
                    )
                    self._add_operational_alert(alert)
                    
        except Exception as e:
            self.logger.error(f"检查运维告警失败: {e}")
    
    def _get_bottleneck_recommendation(self, bottleneck: str) -> str:
        """获取瓶颈优化建议"""
        recommendations = {
            "cpu": "增加CPU核心数或优化查询算法",
            "memory": "增加内存容量或优化缓存配置",
            "io": "优化磁盘IO或增加SSD存储",
            "application": "检查应用程序错误和优化代码",
            "network": "检查网络带宽和延迟"
        }
        return recommendations.get(bottleneck, "进行详细的性能分析")
    
    def _add_operational_alert(self, alert: OperationalAlert):
        """添加运维告警"""
        with self.data_lock:
            # 检查是否已存在相同告警
            existing = any(
                a.alert_type == alert.alert_type and 
                a.component == alert.component and
                alert.timestamp - a.timestamp < 300  # 5分钟内不重复
                for a in self.operational_alerts
            )
            
            if not existing:
                self.operational_alerts.append(alert)
                self.logger.warning(f"运维告警: {alert.message}")
    
    def _execute_auto_optimization(self):
        """执行自动化优化"""
        try:
            # 检查是否有可自动执行的告警
            with self.data_lock:
                recent_alerts = [
                    a for a in self.operational_alerts
                    if a.auto_action and time.time() - a.timestamp < 300
                ]
            
            for alert in recent_alerts:
                if alert.auto_action == "optimize_cpu":
                    self._auto_optimize_cpu()
                elif alert.auto_action == "optimize_memory":
                    self._auto_optimize_memory()
                elif alert.auto_action == "optimize_io":
                    self._auto_optimize_io()
                    
        except Exception as e:
            self.logger.error(f"自动化优化执行失败: {e}")
    
    def _auto_optimize_cpu(self):
        """自动CPU优化"""
        self.logger.info("执行自动CPU优化: 调整连接池大小")
        # 这里可以实现具体的CPU优化逻辑
        
    def _auto_optimize_memory(self):
        """自动内存优化"""
        self.logger.info("执行自动内存优化: 清理缓存")
        # 这里可以实现具体的内存优化逻辑
        
    def _auto_optimize_io(self):
        """自动IO优化"""
        self.logger.info("执行自动IO优化: 优化查询路由")
        # 这里可以实现具体的IO优化逻辑
    
    def _aggregate_minute_data(self):
        """聚合分钟级数据"""
        # 实现分钟级数据聚合逻辑
        pass
    
    def _aggregate_hour_data(self):
        """聚合小时级数据"""
        # 实现小时级数据聚合逻辑
        pass
    
    def _aggregate_day_data(self):
        """聚合日级数据"""
        # 实现日级数据聚合逻辑
        pass
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取仪表板数据"""
        with self.data_lock:
            current_time = time.time()
            
            # 最近性能数据
            recent_performance = list(self.performance_history)[-60:] if self.performance_history else []
            
            # 当前状态
            current_status = {
                "timestamp": current_time,
                "cluster_health": recent_performance[-1].health_score if recent_performance else 0,
                "current_tps": recent_performance[-1].tps if recent_performance else 0,
                "avg_response_time": recent_performance[-1].response_time if recent_performance else 0,
                "error_rate": recent_performance[-1].error_rate if recent_performance else 0,
                "cpu_usage": recent_performance[-1].cpu_usage if recent_performance else 0,
                "memory_usage": recent_performance[-1].memory_usage if recent_performance else 0,
                "connection_count": recent_performance[-1].connection_count if recent_performance else 0
            }
            
            # 性能趋势
            performance_trends = {
                "timestamps": [p.timestamp for p in recent_performance],
                "tps": [p.tps for p in recent_performance],
                "response_times": [p.response_time for p in recent_performance],
                "error_rates": [p.error_rate for p in recent_performance],
                "cpu_usage": [p.cpu_usage for p in recent_performance],
                "memory_usage": [p.memory_usage for p in recent_performance],
                "health_scores": [p.health_score for p in recent_performance]
            }
            
            # 容量指标
            capacity_data = {
                name: asdict(metrics) for name, metrics in self.capacity_metrics.items()
            }
            
            # 运维告警
            recent_alerts = [
                asdict(alert) for alert in list(self.operational_alerts)[-10:]
            ]
            
            return {
                "current_status": current_status,
                "performance_trends": performance_trends,
                "capacity_metrics": capacity_data,
                "operational_alerts": recent_alerts,
                "monitoring_status": self.monitoring_active,
                "auto_optimization_enabled": self.auto_optimization_enabled
            }
    
    def get_capacity_report(self) -> Dict[str, Any]:
        """获取容量规划报告"""
        with self.data_lock:
            if not self.capacity_metrics:
                return {"error": "容量数据不足"}
            
            tps_metrics = self.capacity_metrics.get('tps')
            if not tps_metrics:
                return {"error": "TPS容量数据不足"}
            
            # 容量预测
            days_to_capacity = 0
            if tps_metrics.growth_rate > 0:
                remaining_capacity = tps_metrics.estimated_capacity - tps_metrics.current_load
                days_to_capacity = remaining_capacity / (tps_metrics.current_load * tps_metrics.growth_rate / 100) * 30
            
            return {
                "current_utilization": tps_metrics.utilization_rate,
                "growth_rate": tps_metrics.growth_rate,
                "estimated_capacity": tps_metrics.estimated_capacity,
                "days_to_capacity": max(0, days_to_capacity),
                "bottleneck": tps_metrics.bottleneck_component,
                "recommendations": [
                    f"当前容量使用率: {tps_metrics.utilization_rate:.1f}%",
                    f"预计{days_to_capacity:.0f}天后达到容量上限" if days_to_capacity > 0 else "容量充足",
                    f"主要瓶颈: {tps_metrics.bottleneck_component}",
                    self._get_bottleneck_recommendation(tps_metrics.bottleneck_component)
                ]
            }
    
    def enable_auto_optimization(self, enabled: bool = True):
        """启用/禁用自动优化"""
        self.auto_optimization_enabled = enabled
        self.logger.info(f"自动优化已{'启用' if enabled else '禁用'}")
    
    def close(self):
        """关闭运维监控仪表板"""
        self.stop_ops_monitoring()
        self.logger.info("✅ 运维监控仪表板已关闭")
