#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分布式数据库压力测试管理器
- 大规模并发读写测试
- 分布式事务压力测试
- 数据同步性能测试
- 集群监控压力测试
"""

import time
import threading
import random
import uuid
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import pandas as pd

from src.utils.logging.logger_factory import get_logger
from src.data.storage.distributed_data_adapter import DistributedDataAdapter

@dataclass
class StressTestConfig:
    """压力测试配置"""
    # 基础配置
    test_duration: int = 300  # 测试持续时间(秒)
    concurrent_threads: int = 10  # 并发线程数
    operations_per_thread: int = 100  # 每线程操作数
    
    # 数据配置
    stock_count: int = 1000  # 股票数量
    records_per_batch: int = 100  # 每批记录数
    
    # 测试类型
    enable_read_test: bool = True
    enable_write_test: bool = True
    enable_transaction_test: bool = True
    enable_sync_test: bool = True
    
    # 性能目标
    target_tps: int = 1000  # 目标TPS
    max_response_time: float = 1.0  # 最大响应时间(秒)
    max_error_rate: float = 0.01  # 最大错误率

@dataclass
class TestResult:
    """测试结果"""
    test_name: str
    start_time: float
    end_time: float
    total_operations: int
    successful_operations: int
    failed_operations: int
    avg_response_time: float
    max_response_time: float
    min_response_time: float
    tps: float
    error_rate: float
    errors: List[str]

@dataclass
class StressTestReport:
    """压力测试报告"""
    test_config: StressTestConfig
    test_results: List[TestResult]
    cluster_metrics_before: Dict[str, Any]
    cluster_metrics_after: Dict[str, Any]
    performance_summary: Dict[str, Any]
    recommendations: List[str]

class StressTestManager:
    """压力测试管理器"""
    
    def __init__(self, distributed_adapter: DistributedDataAdapter):
        self.adapter = distributed_adapter
        self.logger = get_logger(__name__)
        
        # 测试数据
        self.test_stocks = self._generate_test_stocks()
        self.test_results: List[TestResult] = []
        
        # 线程安全
        self.results_lock = threading.Lock()
        self.operation_counter = 0
        self.error_counter = 0
        
        self.logger.info("✅ 压力测试管理器初始化完成")
    
    def _generate_test_stocks(self, count: int = 1000) -> List[str]:
        """生成测试股票代码"""
        stocks = []
        
        # 深圳股票
        for i in range(count // 3):
            stocks.append(f"{i:06d}.SZ")
        
        # 上海股票
        for i in range(600000, 600000 + count // 3):
            stocks.append(f"{i}.SH")
        
        # 创业板股票
        for i in range(300000, 300000 + count // 3):
            stocks.append(f"{i}.SZ")
        
        return stocks[:count]
    
    def run_comprehensive_stress_test(self, config: StressTestConfig) -> StressTestReport:
        """运行综合压力测试"""
        self.logger.info(f"🚀 开始综合压力测试")
        self.logger.info(f"配置: {config.concurrent_threads} 线程, {config.test_duration}s, 目标TPS: {config.target_tps}")
        
        # 获取测试前集群状态
        cluster_metrics_before = self.adapter.get_cluster_status()
        
        # 启动集群监控
        self.adapter.start_cluster_monitoring()
        
        try:
            # 1. 数据写入压力测试
            if config.enable_write_test:
                write_result = self._run_write_stress_test(config)
                self.test_results.append(write_result)
            
            # 2. 数据读取压力测试
            if config.enable_read_test:
                read_result = self._run_read_stress_test(config)
                self.test_results.append(read_result)
            
            # 3. 分布式事务压力测试
            if config.enable_transaction_test:
                tx_result = self._run_transaction_stress_test(config)
                self.test_results.append(tx_result)
            
            # 4. 数据同步压力测试
            if config.enable_sync_test:
                sync_result = self._run_sync_stress_test(config)
                self.test_results.append(sync_result)
            
            # 获取测试后集群状态
            cluster_metrics_after = self.adapter.get_cluster_status()
            
            # 生成性能摘要
            performance_summary = self._generate_performance_summary()
            
            # 生成建议
            recommendations = self._generate_recommendations(config)
            
            # 创建测试报告
            report = StressTestReport(
                test_config=config,
                test_results=self.test_results.copy(),
                cluster_metrics_before=cluster_metrics_before,
                cluster_metrics_after=cluster_metrics_after,
                performance_summary=performance_summary,
                recommendations=recommendations
            )
            
            self.logger.info("✅ 综合压力测试完成")
            return report
            
        finally:
            # 停止集群监控
            self.adapter.stop_cluster_monitoring()
    
    def _run_write_stress_test(self, config: StressTestConfig) -> TestResult:
        """运行写入压力测试"""
        self.logger.info("📝 开始写入压力测试")
        
        start_time = time.time()
        response_times = []
        errors = []
        successful_ops = 0
        failed_ops = 0
        
        def write_worker(thread_id: int):
            nonlocal successful_ops, failed_ops
            
            for i in range(config.operations_per_thread):
                try:
                    # 生成测试数据
                    test_data = self._generate_test_data(config.records_per_batch, thread_id, i)
                    
                    # 执行写入操作
                    op_start = time.time()
                    rows_saved = self.adapter.save('daily', test_data)
                    op_time = time.time() - op_start
                    
                    with self.results_lock:
                        response_times.append(op_time)
                        if rows_saved > 0:
                            successful_ops += 1
                        else:
                            failed_ops += 1
                            errors.append(f"写入失败: 线程{thread_id}, 操作{i}")
                
                except Exception as e:
                    with self.results_lock:
                        failed_ops += 1
                        errors.append(f"写入异常: 线程{thread_id}, 操作{i}, 错误: {str(e)}")
        
        # 并发执行写入测试
        with ThreadPoolExecutor(max_workers=config.concurrent_threads) as executor:
            futures = [executor.submit(write_worker, i) for i in range(config.concurrent_threads)]
            for future in as_completed(futures):
                future.result()
        
        end_time = time.time()
        duration = end_time - start_time
        total_ops = successful_ops + failed_ops
        
        return TestResult(
            test_name="写入压力测试",
            start_time=start_time,
            end_time=end_time,
            total_operations=total_ops,
            successful_operations=successful_ops,
            failed_operations=failed_ops,
            avg_response_time=sum(response_times) / len(response_times) if response_times else 0,
            max_response_time=max(response_times) if response_times else 0,
            min_response_time=min(response_times) if response_times else 0,
            tps=total_ops / duration if duration > 0 else 0,
            error_rate=failed_ops / total_ops if total_ops > 0 else 0,
            errors=errors[:10]  # 只保留前10个错误
        )
    
    def _run_read_stress_test(self, config: StressTestConfig) -> TestResult:
        """运行读取压力测试"""
        self.logger.info("📖 开始读取压力测试")
        
        start_time = time.time()
        response_times = []
        errors = []
        successful_ops = 0
        failed_ops = 0
        
        def read_worker(thread_id: int):
            nonlocal successful_ops, failed_ops
            
            for i in range(config.operations_per_thread):
                try:
                    # 随机选择股票代码
                    stock_code = random.choice(self.test_stocks[:100])  # 使用前100只股票
                    
                    # 执行查询操作
                    op_start = time.time()
                    result = self.adapter.query('daily', conditions={'ts_code': stock_code}, limit=100)
                    op_time = time.time() - op_start
                    
                    with self.results_lock:
                        response_times.append(op_time)
                        successful_ops += 1
                
                except Exception as e:
                    with self.results_lock:
                        failed_ops += 1
                        errors.append(f"读取异常: 线程{thread_id}, 操作{i}, 错误: {str(e)}")
        
        # 并发执行读取测试
        with ThreadPoolExecutor(max_workers=config.concurrent_threads) as executor:
            futures = [executor.submit(read_worker, i) for i in range(config.concurrent_threads)]
            for future in as_completed(futures):
                future.result()
        
        end_time = time.time()
        duration = end_time - start_time
        total_ops = successful_ops + failed_ops
        
        return TestResult(
            test_name="读取压力测试",
            start_time=start_time,
            end_time=end_time,
            total_operations=total_ops,
            successful_operations=successful_ops,
            failed_operations=failed_ops,
            avg_response_time=sum(response_times) / len(response_times) if response_times else 0,
            max_response_time=max(response_times) if response_times else 0,
            min_response_time=min(response_times) if response_times else 0,
            tps=total_ops / duration if duration > 0 else 0,
            error_rate=failed_ops / total_ops if total_ops > 0 else 0,
            errors=errors[:10]
        )
    
    def _run_transaction_stress_test(self, config: StressTestConfig) -> TestResult:
        """运行分布式事务压力测试"""
        self.logger.info("🔄 开始分布式事务压力测试")
        
        start_time = time.time()
        response_times = []
        errors = []
        successful_ops = 0
        failed_ops = 0
        
        def transaction_worker(thread_id: int):
            nonlocal successful_ops, failed_ops
            
            for i in range(config.operations_per_thread // 10):  # 事务测试减少操作数
                try:
                    # 创建事务操作
                    operations = [
                        {
                            'operation_type': 'INSERT',
                            'table_name': 'transaction_test',
                            'data': {
                                'account_id': f'ACC{thread_id:03d}{i:03d}',
                                'amount': random.uniform(-1000, 1000),
                                'transaction_type': random.choice(['DEBIT', 'CREDIT'])
                            }
                        }
                    ]
                    
                    # 执行分布式事务
                    op_start = time.time()
                    with self.adapter.distributed_transaction(operations) as tx_id:
                        pass  # 事务自动提交
                    op_time = time.time() - op_start
                    
                    with self.results_lock:
                        response_times.append(op_time)
                        successful_ops += 1
                
                except Exception as e:
                    with self.results_lock:
                        failed_ops += 1
                        errors.append(f"事务异常: 线程{thread_id}, 操作{i}, 错误: {str(e)}")
        
        # 并发执行事务测试
        with ThreadPoolExecutor(max_workers=min(config.concurrent_threads, 5)) as executor:
            futures = [executor.submit(transaction_worker, i) for i in range(min(config.concurrent_threads, 5))]
            for future in as_completed(futures):
                future.result()
        
        end_time = time.time()
        duration = end_time - start_time
        total_ops = successful_ops + failed_ops
        
        return TestResult(
            test_name="分布式事务压力测试",
            start_time=start_time,
            end_time=end_time,
            total_operations=total_ops,
            successful_operations=successful_ops,
            failed_operations=failed_ops,
            avg_response_time=sum(response_times) / len(response_times) if response_times else 0,
            max_response_time=max(response_times) if response_times else 0,
            min_response_time=min(response_times) if response_times else 0,
            tps=total_ops / duration if duration > 0 else 0,
            error_rate=failed_ops / total_ops if total_ops > 0 else 0,
            errors=errors[:10]
        )
    
    def _run_sync_stress_test(self, config: StressTestConfig) -> TestResult:
        """运行数据同步压力测试"""
        self.logger.info("🔄 开始数据同步压力测试")
        
        start_time = time.time()
        response_times = []
        errors = []
        successful_ops = 0
        failed_ops = 0
        
        try:
            # 执行多次同步测试
            for i in range(5):  # 执行5次同步测试
                op_start = time.time()
                
                # 触发数据同步
                sync_task_id = self.adapter.sync_table('daily', 'incremental')
                
                # 等待同步完成
                time.sleep(2)
                
                # 检查同步状态
                sync_stats = self.adapter.get_sync_stats()
                
                op_time = time.time() - op_start
                response_times.append(op_time)
                
                if sync_stats.get('success_tasks', 0) > 0:
                    successful_ops += 1
                else:
                    failed_ops += 1
                    errors.append(f"同步失败: 第{i+1}次")
        
        except Exception as e:
            failed_ops += 1
            errors.append(f"同步异常: {str(e)}")
        
        end_time = time.time()
        duration = end_time - start_time
        total_ops = successful_ops + failed_ops
        
        return TestResult(
            test_name="数据同步压力测试",
            start_time=start_time,
            end_time=end_time,
            total_operations=total_ops,
            successful_operations=successful_ops,
            failed_operations=failed_ops,
            avg_response_time=sum(response_times) / len(response_times) if response_times else 0,
            max_response_time=max(response_times) if response_times else 0,
            min_response_time=min(response_times) if response_times else 0,
            tps=total_ops / duration if duration > 0 else 0,
            error_rate=failed_ops / total_ops if total_ops > 0 else 0,
            errors=errors
        )
    
    def _generate_test_data(self, batch_size: int, thread_id: int, operation_id: int) -> pd.DataFrame:
        """生成测试数据"""
        data = []
        base_date = 20240101
        
        for i in range(batch_size):
            stock_idx = (thread_id * 1000 + operation_id * batch_size + i) % len(self.test_stocks)
            data.append({
                'ts_code': self.test_stocks[stock_idx],
                'trade_date': str(base_date + i % 30),  # 30天的数据
                'close': round(random.uniform(10.0, 100.0), 2),
                'volume': random.randint(100000, 10000000),
                'high': round(random.uniform(10.0, 100.0), 2),
                'low': round(random.uniform(10.0, 100.0), 2),
                'open': round(random.uniform(10.0, 100.0), 2)
            })
        
        return pd.DataFrame(data)
    
    def _generate_performance_summary(self) -> Dict[str, Any]:
        """生成性能摘要"""
        if not self.test_results:
            return {}
        
        total_ops = sum(r.total_operations for r in self.test_results)
        total_successful = sum(r.successful_operations for r in self.test_results)
        total_failed = sum(r.failed_operations for r in self.test_results)
        
        avg_tps = sum(r.tps for r in self.test_results) / len(self.test_results)
        avg_response_time = sum(r.avg_response_time for r in self.test_results) / len(self.test_results)
        max_response_time = max(r.max_response_time for r in self.test_results)
        overall_error_rate = total_failed / total_ops if total_ops > 0 else 0
        
        return {
            'total_operations': total_ops,
            'successful_operations': total_successful,
            'failed_operations': total_failed,
            'overall_tps': avg_tps,
            'avg_response_time': avg_response_time,
            'max_response_time': max_response_time,
            'overall_error_rate': overall_error_rate,
            'test_count': len(self.test_results)
        }
    
    def _generate_recommendations(self, config: StressTestConfig) -> List[str]:
        """生成优化建议"""
        recommendations = []
        summary = self._generate_performance_summary()
        
        if summary.get('overall_tps', 0) < config.target_tps:
            recommendations.append(f"TPS ({summary.get('overall_tps', 0):.1f}) 低于目标 ({config.target_tps})，建议优化数据库连接池和索引")
        
        if summary.get('max_response_time', 0) > config.max_response_time:
            recommendations.append(f"最大响应时间 ({summary.get('max_response_time', 0):.3f}s) 超过目标 ({config.max_response_time}s)，建议优化查询和增加缓存")
        
        if summary.get('overall_error_rate', 0) > config.max_error_rate:
            recommendations.append(f"错误率 ({summary.get('overall_error_rate', 0):.3f}) 超过目标 ({config.max_error_rate})，建议检查错误日志和优化错误处理")
        
        if not recommendations:
            recommendations.append("性能表现良好，满足所有目标指标")
        
        return recommendations

    def generate_test_report(self, report: StressTestReport) -> str:
        """生成测试报告"""
        report_lines = []

        # 报告标题
        report_lines.append("=" * 80)
        report_lines.append("🚀 分布式数据库压力测试报告")
        report_lines.append("=" * 80)
        report_lines.append("")

        # 测试配置
        report_lines.append("📋 测试配置:")
        report_lines.append(f"  - 并发线程数: {report.test_config.concurrent_threads}")
        report_lines.append(f"  - 每线程操作数: {report.test_config.operations_per_thread}")
        report_lines.append(f"  - 测试持续时间: {report.test_config.test_duration}s")
        report_lines.append(f"  - 目标TPS: {report.test_config.target_tps}")
        report_lines.append(f"  - 最大响应时间: {report.test_config.max_response_time}s")
        report_lines.append(f"  - 最大错误率: {report.test_config.max_error_rate}")
        report_lines.append("")

        # 测试结果
        report_lines.append("📊 测试结果:")
        for result in report.test_results:
            report_lines.append(f"  🔸 {result.test_name}:")
            report_lines.append(f"    - 总操作数: {result.total_operations}")
            report_lines.append(f"    - 成功操作: {result.successful_operations}")
            report_lines.append(f"    - 失败操作: {result.failed_operations}")
            report_lines.append(f"    - TPS: {result.tps:.2f}")
            report_lines.append(f"    - 平均响应时间: {result.avg_response_time:.3f}s")
            report_lines.append(f"    - 最大响应时间: {result.max_response_time:.3f}s")
            report_lines.append(f"    - 错误率: {result.error_rate:.3f}")
            if result.errors:
                report_lines.append(f"    - 错误示例: {result.errors[0]}")
            report_lines.append("")

        # 性能摘要
        if report.performance_summary:
            summary = report.performance_summary
            report_lines.append("📈 性能摘要:")
            report_lines.append(f"  - 总操作数: {summary['total_operations']}")
            report_lines.append(f"  - 成功操作数: {summary['successful_operations']}")
            report_lines.append(f"  - 失败操作数: {summary['failed_operations']}")
            report_lines.append(f"  - 整体TPS: {summary['overall_tps']:.2f}")
            report_lines.append(f"  - 平均响应时间: {summary['avg_response_time']:.3f}s")
            report_lines.append(f"  - 最大响应时间: {summary['max_response_time']:.3f}s")
            report_lines.append(f"  - 整体错误率: {summary['overall_error_rate']:.3f}")
            report_lines.append("")

        # 集群状态对比
        if report.cluster_metrics_before and report.cluster_metrics_after:
            report_lines.append("🔍 集群状态对比:")

            before = report.cluster_metrics_before.get('cluster_metrics', {})
            after = report.cluster_metrics_after.get('cluster_metrics', {})

            if before and after:
                report_lines.append("  测试前 -> 测试后:")
                report_lines.append(f"  - 在线节点: {before.get('online_nodes', 0)} -> {after.get('online_nodes', 0)}")
                report_lines.append(f"  - 集群健康分数: {before.get('cluster_health_score', 0):.1f} -> {after.get('cluster_health_score', 0):.1f}")
                report_lines.append(f"  - 总查询数: {before.get('total_queries', 0)} -> {after.get('total_queries', 0)}")
                report_lines.append(f"  - 总错误数: {before.get('total_errors', 0)} -> {after.get('total_errors', 0)}")
            report_lines.append("")

        # 优化建议
        if report.recommendations:
            report_lines.append("💡 优化建议:")
            for i, recommendation in enumerate(report.recommendations, 1):
                report_lines.append(f"  {i}. {recommendation}")
            report_lines.append("")

        # 结论
        report_lines.append("🎯 测试结论:")
        summary = report.performance_summary
        if summary:
            if (summary['overall_tps'] >= report.test_config.target_tps and
                summary['max_response_time'] <= report.test_config.max_response_time and
                summary['overall_error_rate'] <= report.test_config.max_error_rate):
                report_lines.append("  ✅ 系统性能满足所有目标指标，可以投入生产使用")
            else:
                report_lines.append("  ⚠️  系统性能未完全达到目标，建议根据优化建议进行调优")

        report_lines.append("")
        report_lines.append("=" * 80)

        return "\n".join(report_lines)
