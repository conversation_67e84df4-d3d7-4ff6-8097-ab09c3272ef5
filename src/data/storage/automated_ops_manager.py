#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化运维管理器
- 自动故障检测和恢复
- 智能性能调优
- 预防性维护
"""

import time
import threading
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import json

from src.utils.logging.logger_factory import get_logger

# 避免循环导入，使用TYPE_CHECKING
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from src.data.storage.distributed_data_adapter import DistributedDataAdapter

class ActionType(Enum):
    """运维动作类型"""
    RESTART_SERVICE = "restart_service"
    SCALE_OUT = "scale_out"
    SCALE_IN = "scale_in"
    OPTIMIZE_CONFIG = "optimize_config"
    CLEAR_CACHE = "clear_cache"
    REBALANCE_LOAD = "rebalance_load"
    BACKUP_DATA = "backup_data"
    UPDATE_CONFIG = "update_config"

class ActionStatus(Enum):
    """动作状态"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class AutomatedAction:
    """自动化运维动作"""
    action_id: str
    action_type: ActionType
    target_component: str
    parameters: Dict[str, Any]
    trigger_condition: str
    status: ActionStatus
    created_at: float
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None

@dataclass
class MaintenanceTask:
    """维护任务"""
    task_id: str
    task_name: str
    task_type: str
    schedule: str  # cron格式
    last_run: Optional[float] = None
    next_run: Optional[float] = None
    enabled: bool = True

class AutomatedOpsManager:
    """自动化运维管理器"""
    
    def __init__(self, distributed_adapter: 'DistributedDataAdapter'):
        self.adapter = distributed_adapter
        self.logger = get_logger(__name__)
        
        # 运维动作队列
        self.action_queue: List[AutomatedAction] = []
        self.action_history: List[AutomatedAction] = []
        
        # 维护任务
        self.maintenance_tasks: Dict[str, MaintenanceTask] = {}
        
        # 运维规则
        self.automation_rules: Dict[str, Dict[str, Any]] = {}
        
        # 执行器线程
        self.executor_thread = None
        self.executor_active = False
        self.action_lock = threading.RLock()
        
        # 初始化默认规则和任务
        self._setup_default_rules()
        self._setup_default_maintenance_tasks()
        
        self.logger.info("✅ 自动化运维管理器初始化完成")
    
    def _setup_default_rules(self):
        """设置默认自动化规则"""
        self.automation_rules = {
            "high_cpu_usage": {
                "condition": "cpu_usage > 90",
                "action": ActionType.OPTIMIZE_CONFIG,
                "parameters": {"component": "cpu", "optimization_level": "aggressive"},
                "cooldown": 300  # 5分钟冷却时间
            },
            "high_memory_usage": {
                "condition": "memory_usage > 95",
                "action": ActionType.CLEAR_CACHE,
                "parameters": {"cache_type": "all"},
                "cooldown": 180
            },
            "high_error_rate": {
                "condition": "error_rate > 0.1",
                "action": ActionType.RESTART_SERVICE,
                "parameters": {"service": "database_pool"},
                "cooldown": 600
            },
            "slow_response": {
                "condition": "avg_response_time > 2000",
                "action": ActionType.REBALANCE_LOAD,
                "parameters": {"strategy": "round_robin"},
                "cooldown": 300
            },
            "capacity_warning": {
                "condition": "utilization_rate > 85",
                "action": ActionType.SCALE_OUT,
                "parameters": {"scale_factor": 1.2},
                "cooldown": 1800  # 30分钟
            }
        }
    
    def _setup_default_maintenance_tasks(self):
        """设置默认维护任务"""
        self.maintenance_tasks = {
            "daily_backup": MaintenanceTask(
                task_id="daily_backup",
                task_name="每日数据备份",
                task_type="backup",
                schedule="0 2 * * *"  # 每天凌晨2点
            ),
            "weekly_optimization": MaintenanceTask(
                task_id="weekly_optimization",
                task_name="每周性能优化",
                task_type="optimization",
                schedule="0 3 * * 0"  # 每周日凌晨3点
            ),
            "monthly_cleanup": MaintenanceTask(
                task_id="monthly_cleanup",
                task_name="每月清理任务",
                task_type="cleanup",
                schedule="0 4 1 * *"  # 每月1号凌晨4点
            ),
            "health_check": MaintenanceTask(
                task_id="health_check",
                task_name="健康检查",
                task_type="health_check",
                schedule="*/15 * * * *"  # 每15分钟
            )
        }
    
    def start_automation(self):
        """启动自动化运维"""
        if self.executor_active:
            self.logger.warning("自动化运维已在运行")
            return
        
        self.executor_active = True
        self.executor_thread = threading.Thread(target=self._automation_executor, daemon=True)
        self.executor_thread.start()
        
        self.logger.info("✅ 自动化运维已启动")
    
    def stop_automation(self):
        """停止自动化运维"""
        self.executor_active = False
        if self.executor_thread and self.executor_thread.is_alive():
            self.executor_thread.join(timeout=5)
        
        self.logger.info("✅ 自动化运维已停止")
    
    def _automation_executor(self):
        """自动化执行器"""
        while self.executor_active:
            try:
                # 检查触发条件
                self._check_automation_triggers()
                
                # 执行待处理动作
                self._execute_pending_actions()
                
                # 检查维护任务
                self._check_maintenance_tasks()
                
                time.sleep(30)  # 30秒检查间隔
                
            except Exception as e:
                import traceback
                self.logger.error(f"自动化执行器错误: {e}")
                self.logger.error(f"错误详情: {traceback.format_exc()}")
                time.sleep(10)
    
    def _check_automation_triggers(self):
        """检查自动化触发条件"""
        try:
            # 获取当前系统状态
            cluster_status = self.adapter.get_cluster_status()

            # 检查cluster_status是否为None或不包含必要数据
            if not cluster_status or not cluster_status.get('cluster_metrics'):
                return
            
            cluster_metrics = cluster_status['cluster_metrics']
            node_metrics = cluster_status.get('node_metrics', {})
            
            # 计算系统指标
            system_metrics = self._calculate_system_metrics(cluster_metrics, node_metrics)
            
            # 检查每个规则
            for rule_name, rule_config in self.automation_rules.items():
                if self._evaluate_condition(rule_config['condition'], system_metrics):
                    # 检查冷却时间
                    if self._check_cooldown(rule_name, rule_config.get('cooldown', 300)):
                        self._trigger_automated_action(rule_name, rule_config, system_metrics)
                        
        except Exception as e:
            self.logger.error(f"检查自动化触发条件失败: {e}")
    
    def _calculate_system_metrics(self, cluster_metrics: Dict, node_metrics: Dict) -> Dict[str, float]:
        """计算系统指标"""
        metrics = {}
        
        # 集群级指标
        metrics['health_score'] = cluster_metrics.get('cluster_health_score', 0)
        metrics['avg_response_time'] = cluster_metrics.get('avg_cluster_response_time', 0)
        metrics['total_queries'] = cluster_metrics.get('total_queries', 0)
        metrics['total_errors'] = cluster_metrics.get('total_errors', 0)
        metrics['error_rate'] = metrics['total_errors'] / max(metrics['total_queries'], 1)
        
        # 节点级指标聚合
        if node_metrics:
            cpu_values = [m['cpu_usage'] for m in node_metrics.values()]
            memory_values = [m['memory_usage'] for m in node_metrics.values()]
            
            metrics['cpu_usage'] = sum(cpu_values) / len(cpu_values) if cpu_values else 0
            metrics['memory_usage'] = sum(memory_values) / len(memory_values) if memory_values else 0
            metrics['max_cpu_usage'] = max(cpu_values) if cpu_values else 0
            metrics['max_memory_usage'] = max(memory_values) if memory_values else 0
        
        # 容量指标（模拟）
        metrics['utilization_rate'] = min(metrics.get('cpu_usage', 0), metrics.get('memory_usage', 0))
        
        return metrics
    
    def _evaluate_condition(self, condition: str, metrics: Dict[str, float]) -> bool:
        """评估触发条件"""
        try:
            # 简单的条件评估（生产环境应使用更安全的表达式解析器）
            for metric_name, value in metrics.items():
                condition = condition.replace(metric_name, str(value))
            
            # 安全的条件评估
            allowed_operators = ['>', '<', '>=', '<=', '==', '!=', 'and', 'or']
            if any(op in condition for op in ['import', 'exec', 'eval', '__']):
                return False
            
            return eval(condition)
            
        except Exception as e:
            self.logger.error(f"条件评估失败: {condition}, 错误: {e}")
            return False
    
    def _check_cooldown(self, rule_name: str, cooldown_seconds: int) -> bool:
        """检查冷却时间"""
        current_time = time.time()
        
        # 查找最近的相同规则执行
        recent_actions = [
            action for action in self.action_history
            if (action.trigger_condition == rule_name and 
                current_time - action.created_at < cooldown_seconds)
        ]
        
        return len(recent_actions) == 0
    
    def _trigger_automated_action(self, rule_name: str, rule_config: Dict, metrics: Dict):
        """触发自动化动作"""
        action_id = f"{rule_name}_{int(time.time())}"
        
        action = AutomatedAction(
            action_id=action_id,
            action_type=rule_config['action'],
            target_component=rule_config['parameters'].get('component', 'system'),
            parameters=rule_config['parameters'],
            trigger_condition=rule_name,
            status=ActionStatus.PENDING,
            created_at=time.time()
        )
        
        with self.action_lock:
            self.action_queue.append(action)
        
        self.logger.info(f"触发自动化动作: {rule_name} -> {action.action_type.value}")
    
    def _execute_pending_actions(self):
        """执行待处理动作"""
        with self.action_lock:
            pending_actions = [a for a in self.action_queue if a.status == ActionStatus.PENDING]
        
        for action in pending_actions:
            try:
                self._execute_action(action)
            except Exception as e:
                self.logger.error(f"执行动作失败: {action.action_id}, 错误: {e}")
                action.status = ActionStatus.FAILED
                action.error_message = str(e)
                action.completed_at = time.time()
    
    def _execute_action(self, action: AutomatedAction):
        """执行具体动作"""
        action.status = ActionStatus.RUNNING
        action.started_at = time.time()
        
        try:
            if action.action_type == ActionType.OPTIMIZE_CONFIG:
                result = self._optimize_configuration(action.parameters)
            elif action.action_type == ActionType.CLEAR_CACHE:
                result = self._clear_cache(action.parameters)
            elif action.action_type == ActionType.RESTART_SERVICE:
                result = self._restart_service(action.parameters)
            elif action.action_type == ActionType.REBALANCE_LOAD:
                result = self._rebalance_load(action.parameters)
            elif action.action_type == ActionType.SCALE_OUT:
                result = self._scale_out(action.parameters)
            elif action.action_type == ActionType.BACKUP_DATA:
                result = self._backup_data(action.parameters)
            else:
                result = {"status": "not_implemented", "message": f"动作类型 {action.action_type.value} 未实现"}
            
            action.result = result
            action.status = ActionStatus.SUCCESS if result.get('status') == 'success' else ActionStatus.FAILED
            
        except Exception as e:
            action.status = ActionStatus.FAILED
            action.error_message = str(e)
            action.result = {"status": "error", "message": str(e)}
        
        finally:
            action.completed_at = time.time()
            
            # 移动到历史记录
            with self.action_lock:
                if action in self.action_queue:
                    self.action_queue.remove(action)
                self.action_history.append(action)
                
                # 保持历史记录大小
                if len(self.action_history) > 1000:
                    self.action_history = self.action_history[-500:]
    
    def _optimize_configuration(self, parameters: Dict) -> Dict[str, Any]:
        """优化配置"""
        component = parameters.get('component', 'system')
        optimization_level = parameters.get('optimization_level', 'moderate')
        
        self.logger.info(f"执行配置优化: {component}, 级别: {optimization_level}")
        
        # 模拟配置优化
        optimizations = []
        
        if component == 'cpu':
            optimizations.append("调整线程池大小")
            optimizations.append("优化查询缓存")
        elif component == 'memory':
            optimizations.append("调整缓冲区大小")
            optimizations.append("优化内存分配")
        
        return {
            "status": "success",
            "optimizations": optimizations,
            "component": component,
            "level": optimization_level
        }
    
    def _clear_cache(self, parameters: Dict) -> Dict[str, Any]:
        """清理缓存"""
        cache_type = parameters.get('cache_type', 'all')
        
        self.logger.info(f"执行缓存清理: {cache_type}")
        
        # 模拟缓存清理
        cleared_caches = []
        
        if cache_type in ['all', 'query']:
            cleared_caches.append("查询缓存")
        if cache_type in ['all', 'connection']:
            cleared_caches.append("连接池缓存")
        if cache_type in ['all', 'metadata']:
            cleared_caches.append("元数据缓存")
        
        return {
            "status": "success",
            "cleared_caches": cleared_caches,
            "cache_type": cache_type
        }
    
    def _restart_service(self, parameters: Dict) -> Dict[str, Any]:
        """重启服务"""
        service = parameters.get('service', 'unknown')
        
        self.logger.info(f"执行服务重启: {service}")
        
        # 模拟服务重启
        if service == 'database_pool':
            # 这里可以实现实际的连接池重启逻辑
            return {
                "status": "success",
                "service": service,
                "action": "连接池已重启"
            }
        
        return {
            "status": "success",
            "service": service,
            "action": f"服务 {service} 重启完成"
        }
    
    def _rebalance_load(self, parameters: Dict) -> Dict[str, Any]:
        """重新平衡负载"""
        strategy = parameters.get('strategy', 'round_robin')
        
        self.logger.info(f"执行负载重平衡: {strategy}")
        
        return {
            "status": "success",
            "strategy": strategy,
            "action": "负载已重新平衡"
        }
    
    def _scale_out(self, parameters: Dict) -> Dict[str, Any]:
        """扩容"""
        scale_factor = parameters.get('scale_factor', 1.2)
        
        self.logger.info(f"执行扩容: 扩容因子 {scale_factor}")
        
        return {
            "status": "success",
            "scale_factor": scale_factor,
            "action": "扩容计划已制定"
        }
    
    def _backup_data(self, parameters: Dict) -> Dict[str, Any]:
        """数据备份"""
        backup_type = parameters.get('backup_type', 'incremental')
        
        self.logger.info(f"执行数据备份: {backup_type}")
        
        return {
            "status": "success",
            "backup_type": backup_type,
            "action": "数据备份已完成"
        }
    
    def _check_maintenance_tasks(self):
        """检查维护任务"""
        current_time = time.time()
        
        for task in self.maintenance_tasks.values():
            if not task.enabled:
                continue
            
            # 简化的调度检查（生产环境应使用专业的调度库）
            if task.last_run is None or current_time - task.last_run > 3600:  # 1小时最小间隔
                self._execute_maintenance_task(task)
                task.last_run = current_time
    
    def _execute_maintenance_task(self, task: MaintenanceTask):
        """执行维护任务"""
        self.logger.info(f"执行维护任务: {task.task_name}")
        
        if task.task_type == "backup":
            self._trigger_automated_action(
                f"maintenance_{task.task_id}",
                {
                    'action': ActionType.BACKUP_DATA,
                    'parameters': {'backup_type': 'scheduled'}
                },
                {}
            )
        elif task.task_type == "optimization":
            self._trigger_automated_action(
                f"maintenance_{task.task_id}",
                {
                    'action': ActionType.OPTIMIZE_CONFIG,
                    'parameters': {'component': 'system', 'optimization_level': 'moderate'}
                },
                {}
            )
        elif task.task_type == "health_check":
            # 执行健康检查
            cluster_status = self.adapter.get_cluster_status()
            if cluster_status and cluster_status.get('cluster_metrics'):
                health_score = cluster_status.get('cluster_metrics', {}).get('cluster_health_score', 0)

                if health_score < 80:
                    self.logger.warning(f"健康检查发现问题: 健康分数 {health_score}")
            else:
                self.logger.debug("集群状态不可用，跳过健康检查")
    
    def get_automation_status(self) -> Dict[str, Any]:
        """获取自动化状态"""
        with self.action_lock:
            return {
                "automation_active": self.executor_active,
                "pending_actions": len([a for a in self.action_queue if a.status == ActionStatus.PENDING]),
                "running_actions": len([a for a in self.action_queue if a.status == ActionStatus.RUNNING]),
                "recent_actions": [
                    {
                        "action_id": a.action_id,
                        "action_type": a.action_type.value,
                        "status": a.status.value,
                        "created_at": a.created_at,
                        "trigger": a.trigger_condition
                    }
                    for a in self.action_history[-10:]
                ],
                "automation_rules": list(self.automation_rules.keys()),
                "maintenance_tasks": [
                    {
                        "task_id": t.task_id,
                        "task_name": t.task_name,
                        "enabled": t.enabled,
                        "last_run": t.last_run
                    }
                    for t in self.maintenance_tasks.values()
                ]
            }
    
    def add_automation_rule(self, rule_name: str, rule_config: Dict[str, Any]):
        """添加自动化规则"""
        self.automation_rules[rule_name] = rule_config
        self.logger.info(f"添加自动化规则: {rule_name}")
    
    def remove_automation_rule(self, rule_name: str):
        """移除自动化规则"""
        if rule_name in self.automation_rules:
            del self.automation_rules[rule_name]
            self.logger.info(f"移除自动化规则: {rule_name}")
    
    def enable_maintenance_task(self, task_id: str, enabled: bool = True):
        """启用/禁用维护任务"""
        if task_id in self.maintenance_tasks:
            self.maintenance_tasks[task_id].enabled = enabled
            self.logger.info(f"维护任务 {task_id} 已{'启用' if enabled else '禁用'}")
    
    def close(self):
        """关闭自动化运维管理器"""
        self.stop_automation()
        self.logger.info("✅ 自动化运维管理器已关闭")
