#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分布式数据库操作接口

{{ AURA-X: Add - 创建分布式数据库高级操作接口，提供便捷的数据操作方法. Approval: 寸止(ID:分布式数据库完善). }}

提供分布式数据库的高级操作接口：
- 分布式数据插入和查询
- 跨节点数据聚合
- 分片数据管理
- 事务处理和一致性保证
"""

import logging
import pandas as pd
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, date
from dataclasses import dataclass

from src.data.storage.distributed_db_factory import get_distributed_db_manager
from src.utils.logging.logger_factory import get_logger


@dataclass
class DistributedQueryResult:
    """分布式查询结果"""
    data: pd.DataFrame
    node_count: int
    total_rows: int
    execution_time: float
    success_nodes: List[str]
    failed_nodes: List[str]


class DistributedDataOperations:
    """分布式数据操作类"""
    
    def __init__(self, manager_name: str = "default"):
        """
        初始化分布式数据操作
        
        参数:
            manager_name: 分布式数据库管理器名称
        """
        self.manager_name = manager_name
        self.logger = get_logger(__name__)
        self._manager = None
    
    @property
    def manager(self):
        """获取分布式数据库管理器"""
        if self._manager is None:
            self._manager = get_distributed_db_manager(self.manager_name)
        return self._manager
    
    def insert_market_data(self, data: pd.DataFrame, table_name: str = "daily") -> bool:
        """
        插入市场数据
        
        参数:
            data: 市场数据DataFrame
            table_name: 表名
            
        返回:
            bool: 是否成功
        """
        try:
            if data.empty:
                self.logger.warning("数据为空，跳过插入")
                return True
            
            # 按股票代码分组数据
            operations = []
            for ts_code, group_data in data.groupby('ts_code'):
                # 构建插入SQL
                columns = ', '.join(group_data.columns)
                placeholders = ', '.join([f':{col}' for col in group_data.columns])
                sql = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
                
                # 转换数据为字典列表
                records = group_data.to_dict('records')
                
                for record in records:
                    operations.append({
                        'sql': sql,
                        'params': record,
                        'shard_key': ts_code
                    })
            
            # 执行批量插入
            self.manager.execute_batch_write(operations)
            
            self.logger.info(f"✅ 成功插入市场数据: {len(data)}行到表{table_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"插入市场数据失败: {e}")
            return False
    
    def query_market_data(self, 
                         symbols: List[str],
                         start_date: Optional[Union[str, date]] = None,
                         end_date: Optional[Union[str, date]] = None,
                         table_name: str = "daily",
                         fields: Optional[List[str]] = None) -> DistributedQueryResult:
        """
        查询市场数据
        
        参数:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            table_name: 表名
            fields: 字段列表
            
        返回:
            DistributedQueryResult: 查询结果
        """
        start_time = datetime.now()
        
        try:
            # 构建查询SQL
            if fields:
                field_str = ', '.join(fields)
            else:
                field_str = '*'
            
            sql = f"SELECT {field_str} FROM {table_name} WHERE ts_code IN ({','.join(['%s'] * len(symbols))})"
            params = symbols
            
            if start_date:
                sql += " AND trade_date >= %s"
                params.append(str(start_date))
            
            if end_date:
                sql += " AND trade_date <= %s"
                params.append(str(end_date))
            
            sql += " ORDER BY ts_code, trade_date"
            
            # 执行分布式查询
            result_df = self.manager.execute_distributed_query(sql, params)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return DistributedQueryResult(
                data=result_df,
                node_count=len(self.manager.read_nodes),
                total_rows=len(result_df),
                execution_time=execution_time,
                success_nodes=self.manager.read_nodes.copy(),
                failed_nodes=[]
            )
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self.logger.error(f"查询市场数据失败: {e}")
            
            return DistributedQueryResult(
                data=pd.DataFrame(),
                node_count=0,
                total_rows=0,
                execution_time=execution_time,
                success_nodes=[],
                failed_nodes=self.manager.read_nodes.copy()
            )
    
    def aggregate_data(self, 
                      sql: str,
                      params: Optional[Union[tuple, dict]] = None,
                      aggregation_func: str = "sum") -> pd.DataFrame:
        """
        跨节点数据聚合
        
        参数:
            sql: 聚合SQL语句
            params: 参数
            aggregation_func: 聚合函数 (sum, avg, count, max, min)
            
        返回:
            pd.DataFrame: 聚合结果
        """
        try:
            # 获取各节点的结果
            results = self.manager.execute_distributed_query(sql, params, merge_results=False)
            
            if not results:
                return pd.DataFrame()
            
            # 根据聚合函数处理结果
            if aggregation_func.lower() == "sum":
                return pd.concat(results).groupby(level=0).sum().reset_index()
            elif aggregation_func.lower() == "avg":
                return pd.concat(results).groupby(level=0).mean().reset_index()
            elif aggregation_func.lower() == "count":
                return pd.concat(results).groupby(level=0).count().reset_index()
            elif aggregation_func.lower() == "max":
                return pd.concat(results).groupby(level=0).max().reset_index()
            elif aggregation_func.lower() == "min":
                return pd.concat(results).groupby(level=0).min().reset_index()
            else:
                # 默认合并所有结果
                return pd.concat(results, ignore_index=True)
                
        except Exception as e:
            self.logger.error(f"数据聚合失败: {e}")
            return pd.DataFrame()
    
    def get_data_distribution(self) -> Dict[str, Any]:
        """
        获取数据分布统计
        
        返回:
            Dict[str, Any]: 数据分布信息
        """
        try:
            distribution = {}
            
            # 获取集群统计
            cluster_stats = self.manager.get_cluster_stats()
            distribution['cluster'] = cluster_stats
            
            # 获取各节点的数据量统计
            node_data = {}
            for node_id in self.manager.read_nodes:
                try:
                    # 查询各表的数据量
                    tables = ['daily', 'weekly', 'monthly', 'income', 'balancesheet', 'cashflow']
                    table_counts = {}
                    
                    for table in tables:
                        try:
                            sql = f"SELECT COUNT(*) as count FROM {table}"
                            # 使用分布式查询而不是execute_read
                            result_df = self.manager.execute_distributed_query(sql)
                            if not result_df.empty:
                                table_counts[table] = result_df.iloc[0]['count']
                            else:
                                table_counts[table] = 0
                        except Exception as table_error:
                            self.logger.debug(f"表{table}不存在或查询失败: {table_error}")
                            table_counts[table] = 0
                    
                    node_data[node_id] = table_counts
                    
                except Exception as e:
                    self.logger.warning(f"获取节点{node_id}数据统计失败: {e}")
                    node_data[node_id] = {}
            
            distribution['nodes'] = node_data
            
            return distribution
            
        except Exception as e:
            self.logger.error(f"获取数据分布统计失败: {e}")
            return {}
    
    def optimize_data_distribution(self) -> bool:
        """
        优化数据分布
        
        返回:
            bool: 是否成功
        """
        try:
            self.logger.info("开始优化数据分布...")
            
            # 获取当前数据分布
            distribution = self.get_data_distribution()
            
            # 分析数据倾斜情况
            node_data = distribution.get('nodes', {})
            if not node_data:
                self.logger.warning("无法获取节点数据统计")
                return False
            
            # 计算各节点总数据量
            node_totals = {}
            for node_id, tables in node_data.items():
                node_totals[node_id] = sum(tables.values())
            
            if not node_totals:
                self.logger.warning("所有节点数据量为0")
                return True
            
            # 检查数据倾斜
            max_count = max(node_totals.values())
            min_count = min(node_totals.values())
            
            if max_count > 0:
                skew_ratio = max_count / min_count if min_count > 0 else float('inf')
                
                if skew_ratio > 2.0:  # 数据倾斜超过2倍
                    self.logger.warning(f"检测到数据倾斜，倾斜比例: {skew_ratio:.2f}")
                    # 这里可以实现数据重平衡逻辑
                    # 由于涉及数据迁移，暂时只记录警告
                else:
                    self.logger.info(f"数据分布良好，倾斜比例: {skew_ratio:.2f}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"优化数据分布失败: {e}")
            return False


# 便捷函数
def get_distributed_operations(manager_name: str = "default") -> DistributedDataOperations:
    """
    获取分布式数据操作实例
    
    参数:
        manager_name: 管理器名称
        
    返回:
        DistributedDataOperations: 操作实例
    """
    return DistributedDataOperations(manager_name)
