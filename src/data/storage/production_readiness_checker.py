#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产就绪检查器
- 验证分布式数据库系统的生产就绪状态
- 检查所有组件的配置和性能
- 生成部署建议和检查清单
"""

import time
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

from src.utils.logging.logger_factory import get_logger
from src.data.storage.distributed_data_adapter import DistributedDataAdapter
from src.data.storage.mysql_performance_optimizer import MySQLPerformanceOptimizer
from src.data.storage.stress_test_manager import StressTestManager, StressTestConfig

class CheckStatus(Enum):
    """检查状态"""
    PASS = "✅ PASS"
    WARNING = "⚠️ WARNING"
    FAIL = "❌ FAIL"
    INFO = "ℹ️ INFO"

@dataclass
class CheckResult:
    """检查结果"""
    component: str
    check_name: str
    status: CheckStatus
    message: str
    details: Optional[Dict[str, Any]] = None
    recommendation: Optional[str] = None

@dataclass
class ProductionReadinessReport:
    """生产就绪报告"""
    overall_status: CheckStatus
    check_results: List[CheckResult]
    performance_summary: Dict[str, Any]
    deployment_recommendations: List[str]
    risk_assessment: Dict[str, str]
    next_steps: List[str]

class ProductionReadinessChecker:
    """生产就绪检查器"""
    
    def __init__(self, distributed_adapter: DistributedDataAdapter):
        self.adapter = distributed_adapter
        self.logger = get_logger(__name__)
        
        # 初始化子组件
        self.mysql_optimizer = MySQLPerformanceOptimizer(distributed_adapter)
        self.stress_tester = StressTestManager(distributed_adapter)
        
        # 检查结果
        self.check_results: List[CheckResult] = []
        
        self.logger.info("✅ 生产就绪检查器初始化完成")
    
    def run_comprehensive_readiness_check(self) -> ProductionReadinessReport:
        """运行综合生产就绪检查"""
        self.logger.info("🚀 开始综合生产就绪检查")
        
        self.check_results.clear()
        
        # 1. 基础连接检查
        self._check_basic_connectivity()
        
        # 2. 集群健康检查
        self._check_cluster_health()
        
        # 3. 数据库配置检查
        self._check_database_configuration()
        
        # 4. 性能基准检查
        self._check_performance_benchmarks()
        
        # 5. 分布式事务检查
        self._check_distributed_transactions()
        
        # 6. 监控和告警检查
        self._check_monitoring_and_alerting()
        
        # 7. 数据同步检查
        self._check_data_synchronization()
        
        # 8. 安全配置检查
        self._check_security_configuration()
        
        # 生成综合报告
        report = self._generate_readiness_report()
        
        self.logger.info("✅ 综合生产就绪检查完成")
        return report
    
    def _check_basic_connectivity(self):
        """检查基础连接"""
        try:
            # 检查分布式存储连接
            is_connected = self.adapter.connect()
            if is_connected:
                self.check_results.append(CheckResult(
                    component="基础连接",
                    check_name="分布式存储连接",
                    status=CheckStatus.PASS,
                    message="分布式存储连接正常"
                ))
            else:
                self.check_results.append(CheckResult(
                    component="基础连接",
                    check_name="分布式存储连接",
                    status=CheckStatus.FAIL,
                    message="分布式存储连接失败",
                    recommendation="检查数据库连接配置和网络连通性"
                ))
            
            # 检查节点连接状态
            cluster_stats = self.adapter.get_cluster_stats()
            online_ratio = cluster_stats['online_nodes'] / cluster_stats['total_nodes']
            
            if online_ratio == 1.0:
                self.check_results.append(CheckResult(
                    component="基础连接",
                    check_name="节点连接状态",
                    status=CheckStatus.PASS,
                    message=f"所有节点在线 ({cluster_stats['online_nodes']}/{cluster_stats['total_nodes']})",
                    details=cluster_stats
                ))
            elif online_ratio >= 0.75:
                self.check_results.append(CheckResult(
                    component="基础连接",
                    check_name="节点连接状态",
                    status=CheckStatus.WARNING,
                    message=f"部分节点离线 ({cluster_stats['online_nodes']}/{cluster_stats['total_nodes']})",
                    details=cluster_stats,
                    recommendation="检查离线节点的连接状态"
                ))
            else:
                self.check_results.append(CheckResult(
                    component="基础连接",
                    check_name="节点连接状态",
                    status=CheckStatus.FAIL,
                    message=f"大量节点离线 ({cluster_stats['online_nodes']}/{cluster_stats['total_nodes']})",
                    details=cluster_stats,
                    recommendation="立即检查集群网络和节点状态"
                ))
                
        except Exception as e:
            self.check_results.append(CheckResult(
                component="基础连接",
                check_name="连接检查",
                status=CheckStatus.FAIL,
                message=f"连接检查失败: {str(e)}",
                recommendation="检查系统配置和依赖"
            ))
    
    def _check_cluster_health(self):
        """检查集群健康状态"""
        try:
            # 启动监控
            self.adapter.start_cluster_monitoring()
            time.sleep(5)  # 等待监控数据收集
            
            cluster_status = self.adapter.get_cluster_status()
            
            if cluster_status.get('cluster_metrics'):
                metrics = cluster_status['cluster_metrics']
                health_score = metrics.get('cluster_health_score', 0)
                
                if health_score >= 90:
                    status = CheckStatus.PASS
                    message = f"集群健康状态优秀 (分数: {health_score:.1f})"
                elif health_score >= 70:
                    status = CheckStatus.WARNING
                    message = f"集群健康状态良好 (分数: {health_score:.1f})"
                    recommendation = "监控集群性能，考虑优化配置"
                else:
                    status = CheckStatus.FAIL
                    message = f"集群健康状态较差 (分数: {health_score:.1f})"
                    recommendation = "立即检查集群配置和节点状态"
                
                self.check_results.append(CheckResult(
                    component="集群健康",
                    check_name="健康分数",
                    status=status,
                    message=message,
                    details={"health_score": health_score, "metrics": metrics},
                    recommendation=recommendation if status != CheckStatus.PASS else None
                ))
            
            # 检查告警状态
            alert_summary = self.adapter.get_alert_summary()
            active_alerts = alert_summary.get('active_alerts_count', 0)
            
            if active_alerts == 0:
                self.check_results.append(CheckResult(
                    component="集群健康",
                    check_name="告警状态",
                    status=CheckStatus.PASS,
                    message="无活跃告警"
                ))
            elif active_alerts <= 2:
                self.check_results.append(CheckResult(
                    component="集群健康",
                    check_name="告警状态",
                    status=CheckStatus.WARNING,
                    message=f"存在 {active_alerts} 个活跃告警",
                    recommendation="检查并处理活跃告警"
                ))
            else:
                self.check_results.append(CheckResult(
                    component="集群健康",
                    check_name="告警状态",
                    status=CheckStatus.FAIL,
                    message=f"存在 {active_alerts} 个活跃告警",
                    recommendation="立即处理所有活跃告警"
                ))
            
            self.adapter.stop_cluster_monitoring()
            
        except Exception as e:
            self.check_results.append(CheckResult(
                component="集群健康",
                check_name="健康检查",
                status=CheckStatus.FAIL,
                message=f"集群健康检查失败: {str(e)}",
                recommendation="检查监控系统配置"
            ))
    
    def _check_database_configuration(self):
        """检查数据库配置"""
        try:
            config_status = self.mysql_optimizer.verify_mysql_configuration()
            
            if 'error' in config_status:
                self.check_results.append(CheckResult(
                    component="数据库配置",
                    check_name="MySQL配置验证",
                    status=CheckStatus.FAIL,
                    message=f"MySQL配置验证失败: {config_status['error']}",
                    recommendation="检查MySQL连接和权限"
                ))
                return
            
            # 检查关键配置参数
            max_connections = int(config_status.get('max_connections', 0))
            if max_connections >= 500:
                status = CheckStatus.PASS
                message = f"最大连接数配置合适 ({max_connections})"
            elif max_connections >= 200:
                status = CheckStatus.WARNING
                message = f"最大连接数偏低 ({max_connections})"
                recommendation = "建议增加max_connections到500以上"
            else:
                status = CheckStatus.FAIL
                message = f"最大连接数过低 ({max_connections})"
                recommendation = "必须增加max_connections到500以上"
            
            self.check_results.append(CheckResult(
                component="数据库配置",
                check_name="连接数配置",
                status=status,
                message=message,
                details={"max_connections": max_connections},
                recommendation=recommendation if status != CheckStatus.PASS else None
            ))
            
            # 检查InnoDB配置
            innodb_available = config_status.get('innodb_available', False)
            if innodb_available:
                self.check_results.append(CheckResult(
                    component="数据库配置",
                    check_name="InnoDB存储引擎",
                    status=CheckStatus.PASS,
                    message="InnoDB存储引擎可用"
                ))
            else:
                self.check_results.append(CheckResult(
                    component="数据库配置",
                    check_name="InnoDB存储引擎",
                    status=CheckStatus.FAIL,
                    message="InnoDB存储引擎不可用",
                    recommendation="启用InnoDB存储引擎"
                ))
                
        except Exception as e:
            self.check_results.append(CheckResult(
                component="数据库配置",
                check_name="配置检查",
                status=CheckStatus.FAIL,
                message=f"数据库配置检查失败: {str(e)}",
                recommendation="检查数据库连接和配置"
            ))
    
    def _check_performance_benchmarks(self):
        """检查性能基准"""
        try:
            # 运行快速性能测试
            test_config = StressTestConfig(
                test_duration=30,
                concurrent_threads=3,
                operations_per_thread=10,
                target_tps=30,
                max_response_time=1.0,
                max_error_rate=0.05
            )
            
            stress_report = self.stress_tester.run_comprehensive_stress_test(test_config)
            summary = stress_report.performance_summary
            
            # 检查TPS
            if summary['overall_tps'] >= test_config.target_tps:
                self.check_results.append(CheckResult(
                    component="性能基准",
                    check_name="TPS性能",
                    status=CheckStatus.PASS,
                    message=f"TPS达标 ({summary['overall_tps']:.1f} >= {test_config.target_tps})",
                    details={"tps": summary['overall_tps'], "target": test_config.target_tps}
                ))
            else:
                self.check_results.append(CheckResult(
                    component="性能基准",
                    check_name="TPS性能",
                    status=CheckStatus.WARNING,
                    message=f"TPS未达标 ({summary['overall_tps']:.1f} < {test_config.target_tps})",
                    details={"tps": summary['overall_tps'], "target": test_config.target_tps},
                    recommendation="优化数据库配置和查询性能"
                ))
            
            # 检查错误率
            if summary['overall_error_rate'] <= test_config.max_error_rate:
                self.check_results.append(CheckResult(
                    component="性能基准",
                    check_name="错误率",
                    status=CheckStatus.PASS,
                    message=f"错误率合格 ({summary['overall_error_rate']:.3f} <= {test_config.max_error_rate})",
                    details={"error_rate": summary['overall_error_rate'], "target": test_config.max_error_rate}
                ))
            else:
                self.check_results.append(CheckResult(
                    component="性能基准",
                    check_name="错误率",
                    status=CheckStatus.FAIL,
                    message=f"错误率过高 ({summary['overall_error_rate']:.3f} > {test_config.max_error_rate})",
                    details={"error_rate": summary['overall_error_rate'], "target": test_config.max_error_rate},
                    recommendation="检查和修复系统错误"
                ))
                
        except Exception as e:
            self.check_results.append(CheckResult(
                component="性能基准",
                check_name="性能测试",
                status=CheckStatus.FAIL,
                message=f"性能基准测试失败: {str(e)}",
                recommendation="检查系统负载和配置"
            ))
    
    def _check_distributed_transactions(self):
        """检查分布式事务"""
        try:
            # 测试分布式事务功能
            operations = [
                {
                    'operation_type': 'INSERT',
                    'table_name': 'transaction_test',
                    'data': {
                        'account_id': 'PROD_TEST_001',
                        'amount': 100.00,
                        'transaction_type': 'CREDIT'
                    }
                }
            ]
            
            # 启动事务清理
            self.adapter.start_transaction_cleanup()
            
            # 测试事务
            with self.adapter.distributed_transaction(operations) as tx_id:
                pass
            
            self.check_results.append(CheckResult(
                component="分布式事务",
                check_name="事务功能",
                status=CheckStatus.PASS,
                message="分布式事务功能正常",
                details={"test_transaction_id": tx_id}
            ))
            
            # 检查活跃事务
            active_transactions = self.adapter.get_active_transactions()
            if len(active_transactions) == 0:
                self.check_results.append(CheckResult(
                    component="分布式事务",
                    check_name="事务清理",
                    status=CheckStatus.PASS,
                    message="事务清理机制正常"
                ))
            else:
                self.check_results.append(CheckResult(
                    component="分布式事务",
                    check_name="事务清理",
                    status=CheckStatus.WARNING,
                    message=f"存在 {len(active_transactions)} 个活跃事务",
                    recommendation="检查事务清理配置"
                ))
            
            self.adapter.stop_transaction_cleanup()
            
        except Exception as e:
            self.check_results.append(CheckResult(
                component="分布式事务",
                check_name="事务测试",
                status=CheckStatus.FAIL,
                message=f"分布式事务测试失败: {str(e)}",
                recommendation="检查分布式事务配置"
            ))
    
    def _check_monitoring_and_alerting(self):
        """检查监控和告警"""
        try:
            # 检查监控功能
            self.adapter.start_cluster_monitoring()
            time.sleep(3)
            
            cluster_status = self.adapter.get_cluster_status()
            monitoring_active = cluster_status.get('monitoring_status', False)
            
            if monitoring_active:
                self.check_results.append(CheckResult(
                    component="监控告警",
                    check_name="监控功能",
                    status=CheckStatus.PASS,
                    message="集群监控功能正常"
                ))
            else:
                self.check_results.append(CheckResult(
                    component="监控告警",
                    check_name="监控功能",
                    status=CheckStatus.FAIL,
                    message="集群监控功能异常",
                    recommendation="检查监控系统配置"
                ))
            
            self.adapter.stop_cluster_monitoring()
            
        except Exception as e:
            self.check_results.append(CheckResult(
                component="监控告警",
                check_name="监控检查",
                status=CheckStatus.FAIL,
                message=f"监控系统检查失败: {str(e)}",
                recommendation="检查监控系统依赖"
            ))
    
    def _check_data_synchronization(self):
        """检查数据同步"""
        try:
            # 检查同步功能
            sync_stats = self.adapter.get_sync_stats()
            
            self.check_results.append(CheckResult(
                component="数据同步",
                check_name="同步功能",
                status=CheckStatus.PASS,
                message="数据同步功能可用",
                details=sync_stats
            ))
            
        except Exception as e:
            self.check_results.append(CheckResult(
                component="数据同步",
                check_name="同步检查",
                status=CheckStatus.WARNING,
                message=f"数据同步检查异常: {str(e)}",
                recommendation="检查数据同步配置"
            ))
    
    def _check_security_configuration(self):
        """检查安全配置"""
        # 基础安全检查
        self.check_results.append(CheckResult(
            component="安全配置",
            check_name="连接安全",
            status=CheckStatus.INFO,
            message="建议在生产环境启用SSL连接",
            recommendation="配置SSL证书和加密连接"
        ))
        
        self.check_results.append(CheckResult(
            component="安全配置",
            check_name="访问控制",
            status=CheckStatus.INFO,
            message="建议配置防火墙和访问控制",
            recommendation="限制数据库访问IP和端口"
        ))
    
    def _generate_readiness_report(self) -> ProductionReadinessReport:
        """生成生产就绪报告"""
        # 计算整体状态
        fail_count = sum(1 for r in self.check_results if r.status == CheckStatus.FAIL)
        warning_count = sum(1 for r in self.check_results if r.status == CheckStatus.WARNING)
        
        if fail_count > 0:
            overall_status = CheckStatus.FAIL
        elif warning_count > 3:
            overall_status = CheckStatus.WARNING
        else:
            overall_status = CheckStatus.PASS
        
        # 生成性能摘要
        performance_summary = {
            "total_checks": len(self.check_results),
            "passed_checks": sum(1 for r in self.check_results if r.status == CheckStatus.PASS),
            "warning_checks": warning_count,
            "failed_checks": fail_count,
            "info_checks": sum(1 for r in self.check_results if r.status == CheckStatus.INFO)
        }
        
        # 生成部署建议
        deployment_recommendations = []
        if overall_status == CheckStatus.PASS:
            deployment_recommendations.append("系统已准备好生产部署")
            deployment_recommendations.append("建议在低峰期进行部署")
            deployment_recommendations.append("部署后持续监控系统性能")
        elif overall_status == CheckStatus.WARNING:
            deployment_recommendations.append("系统基本满足生产要求，但存在优化空间")
            deployment_recommendations.append("建议先处理警告项再进行部署")
            deployment_recommendations.append("部署后加强监控")
        else:
            deployment_recommendations.append("系统尚未准备好生产部署")
            deployment_recommendations.append("必须先解决所有失败项")
            deployment_recommendations.append("重新运行检查确认修复效果")
        
        # 风险评估
        risk_assessment = {
            "数据安全": "中等 - 建议启用加密和访问控制",
            "系统稳定性": "高 - 通过压力测试验证" if overall_status != CheckStatus.FAIL else "低 - 存在系统问题",
            "性能表现": "高 - 满足性能基准" if fail_count == 0 else "中等 - 需要性能优化",
            "运维复杂度": "中等 - 需要专业运维团队"
        }
        
        # 下一步行动
        next_steps = []
        if overall_status == CheckStatus.PASS:
            next_steps.extend([
                "准备生产环境部署计划",
                "配置生产环境监控和告警",
                "准备数据备份和恢复方案",
                "制定运维手册和应急预案"
            ])
        else:
            next_steps.extend([
                "修复所有失败的检查项",
                "优化警告项的配置",
                "重新运行生产就绪检查",
                "进行更全面的压力测试"
            ])
        
        return ProductionReadinessReport(
            overall_status=overall_status,
            check_results=self.check_results,
            performance_summary=performance_summary,
            deployment_recommendations=deployment_recommendations,
            risk_assessment=risk_assessment,
            next_steps=next_steps
        )
