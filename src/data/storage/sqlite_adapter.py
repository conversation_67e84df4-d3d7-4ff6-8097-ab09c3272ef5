"""
SQLite数据存储适配器
- 提供SQLite数据库的存取功能
- 支持事务处理和并发控制
- 实现数据表自动创建和索引管理
- 支持多种数据格式的存储和查询
"""

import os
import sqlite3
import pandas as pd
import numpy as np
import json
import logging
from typing import Dict, List, Any, Optional, Union, Tuple, Callable
from datetime import datetime
import time
from functools import wraps

from src.data.storage.storage_interface import StorageInterface, ConnectionError, QueryError, TransactionError, DataError
from src.utils.config.config_factory import ConfigFactory
from src.data.storage.connection_pool_manager import get_pool_manager, ConnectionPoolConfig
from src.data.storage.batch_processor import BatchProcessor

class SQLiteAdapter(StorageInterface):
    """
    SQLite存储适配器类，用于将数据存储到SQLite数据库
    
    特性:
    - 支持标准存储接口
    - 支持事务处理
    - 支持数据备份和恢复
    - 支持表结构自动创建和优化
    """
    
    def __init__(self, db_path: str = None, config_file: str = 'database', 
                 env: str = None, auto_create_tables: bool = True):
        """
        初始化SQLite存储适配器
        
        参数:
            db_path: 数据库文件路径，默认从配置获取
            config_file: 配置文件名，默认为'database'
            env: 环境名称，用于加载不同环境配置
            auto_create_tables: 是否自动创建表
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.conn = None
        self.connected = False
        self.in_transaction = False
        
        # 获取项目根目录作为配置目录 - 修复配置目录路径
        config_dir = os.path.abspath(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'config'))
        
        # 加载配置
        self.config_factory = ConfigFactory()
        try:
            self.config = self.config_factory.load_config(config_file, config_dir)
        except FileNotFoundError:
            self.logger.warning(f"找不到配置文件: {config_file}.yaml，使用默认配置")
            self.config = {}
        
        # 获取SQLite配置
        self.sqlite_config = self.config.get('sqlite', {})
        
        # 设置数据库路径
        if db_path:
            self.db_path = db_path
        else:
            self.db_path = self.sqlite_config.get('database', 'output/data/db/sqlite/quantification.db')
            
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # 表结构缓存
        self._table_schemas = {}
        
        # 自动创建表
        self.auto_create_tables = auto_create_tables
        
        # 缓存配置
        self.pragma_settings = self.sqlite_config.get('pragma', {
            'journal_mode': 'WAL',
            'synchronous': 1,
            'foreign_keys': 1,
            'cache_size': -1024 * 32,  # 32MB
            'mmap_size': 1024 * 1024 * 512  # 512MB
        })
        
        # 连接超时设置
        self.timeout = self.sqlite_config.get('timeout', 30.0)
        
        # 初始化连接池
        self.pool_manager = get_pool_manager()
        self.pool_name = f"sqlite_{id(self)}"

        # 创建连接池
        pool_config = ConnectionPoolConfig(
            pool_size=5,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=3600
        )
        self.pool_manager.create_sqlite_pool(self.db_path, self.pool_name, pool_config)

        # 初始化批量处理器
        self.batch_processor = BatchProcessor(self.pool_name)

        # 记录日志
        self.logger.info(f"SQLite适配器初始化完成，数据库路径: {self.db_path}，连接池: {self.pool_name}")
    
    def connect(self, **kwargs) -> bool:
        """
        连接到SQLite数据库
        
        参数:
            **kwargs: 可选参数，用于覆盖默认配置
                
        返回:
            bool: 连接是否成功
            
        异常:
            ConnectionError: 连接失败时抛出
        """
        if self.connected:
            return True
            
        try:
            # 更新配置
            if kwargs:
                if 'db_path' in kwargs and kwargs['db_path']:
                    self.db_path = kwargs['db_path']
                    os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
                
                if 'timeout' in kwargs:
                    self.timeout = float(kwargs['timeout'])
            
            # 连接数据库
            self.conn = sqlite3.connect(
                self.db_path, 
                timeout=self.timeout,
                check_same_thread=False,
                isolation_level=None  # 启用手动事务管理
            )
            
            # 配置数据库
            for key, value in self.pragma_settings.items():
                self.conn.execute(f"PRAGMA {key} = {value}")
                
            # 尝试启用扩展功能，但这个功能在某些SQLite版本中可能不可用
            try:
                self.conn.enable_load_extension(True)
            except AttributeError:
                self.logger.warning("SQLite扩展功能不可用，跳过启用")
            
            # 设置行工厂函数，返回字典
            self.conn.row_factory = sqlite3.Row
            
            self.connected = True
            self.logger.info(f"已连接到SQLite数据库: {self.db_path}")
            return True
        except Exception as e:
            self.connected = False
            self.logger.error(f"连接SQLite数据库失败: {str(e)}")
            raise ConnectionError(f"连接SQLite数据库失败: {str(e)}")
    
    def disconnect(self) -> bool:
        """
        断开与SQLite数据库的连接
        
        返回:
            bool: 是否成功断开连接
            
        异常:
            ConnectionError: 断开连接失败时抛出
        """
        if self.conn:
            try:
                # 如果有未提交的事务，回滚
                if self.in_transaction:
                    try:
                        self.conn.execute("ROLLBACK")
                    except Exception:
                        self.logger.warning("回滚未完成事务失败，继续关闭连接")
                    self.in_transaction = False
                
                self.conn.close()
                self.conn = None
                self.connected = False
                self.logger.info("已断开与SQLite数据库的连接")
                return True
            except Exception as e:
                self.logger.error(f"断开SQLite数据库连接失败: {str(e)}")
                raise ConnectionError(f"断开SQLite数据库连接失败: {str(e)}")
        
        self.connected = False
        return True
    
    def is_connected(self) -> bool:
        """
        检查是否已连接到SQLite数据库
        
        返回:
            bool: 是否已连接
        """
        if not self.conn or not self.connected:
            return False
            
        try:
            # 执行简单查询以验证连接
            self.conn.execute("SELECT 1").fetchone()
            return True
        except:
            self.connected = False
            return False
    
    def save(self, table: str, data: Union[pd.DataFrame, List[Dict[str, Any]], Dict[str, Any]], 
             if_exists: str = 'append', index: bool = False, **kwargs) -> int:
        """
        保存数据到指定表
        
        参数:
            table: 表名
            data: 要保存的数据，可以是DataFrame、字典列表或单个字典
            if_exists: 如果表已存在，如何处理，可选值: 'fail', 'replace', 'append'
            index: 是否保存DataFrame的索引
            **kwargs: 额外参数
            
        返回:
            int: 受影响的行数
            
        异常:
            ConnectionError: 未连接时抛出
            DataError: 数据保存失败时抛出
        """
        if not self.is_connected():
            self.connect()
        
        try:
            # 确保表名是有效的标识符
            table = self._sanitize_name(table)
            
            # 将字典或字典列表转换为DataFrame
            if isinstance(data, dict):
                data = pd.DataFrame([data])
            elif isinstance(data, list) and all(isinstance(item, dict) for item in data):
                data = pd.DataFrame(data)
                
            if not isinstance(data, pd.DataFrame):
                raise DataError(f"不支持的数据类型: {type(data)}")
                
            # 检查表是否存在
            table_exists = self.table_exists(table)
            
            # 如果表不存在且auto_create_tables为True，则创建表
            if not table_exists and self.auto_create_tables:
                schema = self._infer_schema(data)
                self.create_table(table, schema)
                table_exists = True
                
            # 根据if_exists参数处理已存在的表
            if table_exists and if_exists == 'fail':
                raise DataError(f"表已存在: {table}")
            elif table_exists and if_exists == 'replace':
                self.drop_table(table)
                schema = self._infer_schema(data)
                self.create_table(table, schema)
                
            # 将DataFrame保存到数据库
            affected_rows = 0
            
            if not data.empty:
                # 处理NaN值
                for col in data.select_dtypes(include=['float']).columns:
                    data[col] = data[col].fillna(np.nan)
                    
                # 将日期时间列转换为字符串
                for col in data.select_dtypes(include=['datetime64']).columns:
                    data[col] = data[col].dt.strftime('%Y-%m-%d %H:%M:%S')
                
                # 保存数据
                try:
                    data.to_sql(table, self.conn, if_exists='append', index=index)
                    affected_rows = len(data)
                except Exception as e:
                    self.logger.error(f"保存数据到表失败: {table}, 错误: {str(e)}")
                    raise DataError(f"保存数据到表失败: {table}, 错误: {str(e)}")
            
            return affected_rows
        except Exception as e:
            self.logger.error(f"保存数据失败: {str(e)}")
            if not isinstance(e, (ConnectionError, DataError)):
                raise DataError(f"保存数据失败: {str(e)}")
            raise

    def bulk_save(
        self,
        data: pd.DataFrame,
        table: str,
        if_exists: str = 'append',
        index: bool = False,
        enable_optimization: bool = True
    ) -> int:
        """
        高性能批量保存数据

        参数:
            data: 要保存的DataFrame
            table: 目标表名
            if_exists: 表存在时的处理方式
            index: 是否包含索引
            enable_optimization: 是否启用优化

        返回:
            int: 影响的行数
        """
        if data.empty:
            return 0

        if enable_optimization:
            # 使用批量处理器进行优化处理
            stats = self.batch_processor.bulk_insert(
                data=data,
                table_name=table,
                if_exists=if_exists,
                index=index,
                method='multi'
            )
            return stats.processed_records
        else:
            # 使用原有方法
            return self.save(table, data, if_exists, index)
    
    def query(self, table: str, conditions: Dict[str, Any] = None, 
              fields: List[str] = None, order_by: str = None, 
              limit: int = None, offset: int = None,
              as_dict: bool = False, **kwargs) -> Union[pd.DataFrame, List[Dict[str, Any]]]:
        """
        从指定表查询数据
        
        参数:
            table: 表名
            conditions: 查询条件
            fields: 要返回的字段列表
            order_by: 排序字段
            limit: 返回的最大行数
            offset: 结果集的偏移量
            as_dict: 是否返回字典列表而不是DataFrame
            **kwargs: 额外参数
            
        返回:
            Union[pd.DataFrame, List[Dict[str, Any]]]: 查询结果
            
        异常:
            ConnectionError: 未连接时抛出
            QueryError: 查询失败时抛出
        """
        if not self.is_connected():
            self.connect()
            
        try:
            # 确保表名是有效的标识符
            table = self._sanitize_name(table)
            
            # 检查表是否存在
            if not self.table_exists(table):
                raise QueryError(f"表不存在: {table}")
            
            # 构建SQL语句
            fields_str = "*"
            if fields:
                fields_str = ", ".join([self._sanitize_name(f) for f in fields])
                
            sql = f"SELECT {fields_str} FROM {table}"
            
            # 构建WHERE子句
            params = []
            if conditions:
                where_clauses = []
                for key, value in conditions.items():
                    if value is None:
                        where_clauses.append(f"{self._sanitize_name(key)} IS NULL")
                    else:
                        where_clauses.append(f"{self._sanitize_name(key)} = ?")
                        params.append(value)
                
                if where_clauses:
                    sql += " WHERE " + " AND ".join(where_clauses)
            
            # 添加ORDER BY子句
            if order_by:
                sql += f" ORDER BY {order_by}"
                
            # 添加LIMIT和OFFSET子句
            if limit is not None:
                sql += f" LIMIT {limit}"
                
            if offset is not None:
                sql += f" OFFSET {offset}"
            
            # 如果不在事务中，使用新连接执行查询以确保看到最新数据
            # 这样可以避免看到回滚前的数据
            if not self.in_transaction:
                # 临时创建一个新连接仅用于此查询
                conn = sqlite3.connect(
                    self.db_path, 
                    timeout=self.timeout,
                    check_same_thread=False,
                    isolation_level=None  # 自动提交模式
                )
                conn.row_factory = sqlite3.Row
                
                # 执行查询
                cursor = conn.execute(sql, params)
                
                if as_dict:
                    # 返回字典列表
                    result = [dict(row) for row in cursor.fetchall()]
                    conn.close()
                    return result
                else:
                    # 返回DataFrame
                    columns = [column[0] for column in cursor.description]
                    rows = cursor.fetchall()
                    conn.close()
                    
                    if not rows:
                        return pd.DataFrame(columns=columns)
                        
                    # 将行转换为列表的列表
                    data = [list(row) for row in rows]
                    
                    return pd.DataFrame(data, columns=columns)
            else:
                # 在事务中使用当前连接执行查询
                cursor = self.conn.execute(sql, params)
                
                if as_dict:
                    # 返回字典列表
                    return [dict(row) for row in cursor.fetchall()]
                else:
                    # 返回DataFrame
                    columns = [column[0] for column in cursor.description]
                    rows = cursor.fetchall()
                    
                    if not rows:
                        return pd.DataFrame(columns=columns)
                        
                    # 将行转换为列表的列表
                    data = [list(row) for row in rows]
                    
                    return pd.DataFrame(data, columns=columns)
        except Exception as e:
            self.logger.error(f"查询数据失败: {str(e)}")
            if not isinstance(e, (ConnectionError, QueryError)):
                raise QueryError(f"查询数据失败: {str(e)}")
            raise
    
    def update(self, table: str, data: Union[Dict[str, Any], pd.Series], 
               conditions: Dict[str, Any] = None) -> int:
        """
        更新表中的数据
        
        参数:
            table: 表名
            data: 要更新的数据
            conditions: 更新条件
            
        返回:
            int: 受影响的行数
            
        异常:
            ConnectionError: 未连接时抛出
            QueryError: 更新失败时抛出
        """
        if not self.is_connected():
            self.connect()
            
        try:
            # 确保表名是有效的标识符
            table = self._sanitize_name(table)
            
            # 检查表是否存在
            if not self.table_exists(table):
                raise QueryError(f"表不存在: {table}")
                
            # 处理条件为None的情况
            if conditions is None:
                conditions = {}
                
            # 将Series转换为字典
            if isinstance(data, pd.Series):
                data = data.to_dict()
                
            if not data:
                return 0
                
            # 构建SQL语句
            set_clauses = []
            params = []
            
            for key, value in data.items():
                set_clauses.append(f"{self._sanitize_name(key)} = ?")
                params.append(value)
                
            sql = f"UPDATE {table} SET " + ", ".join(set_clauses)
            
            # 添加WHERE子句
            if conditions:
                where_clauses = []
                for key, value in conditions.items():
                    if value is None:
                        where_clauses.append(f"{self._sanitize_name(key)} IS NULL")
                    else:
                        where_clauses.append(f"{self._sanitize_name(key)} = ?")
                        params.append(value)
                
                if where_clauses:
                    sql += " WHERE " + " AND ".join(where_clauses)
            
            # 执行更新
            cursor = self.conn.execute(sql, params)
            
            # 返回受影响的行数
            return cursor.rowcount
        except Exception as e:
            self.logger.error(f"更新数据失败: {str(e)}")
            if not isinstance(e, (ConnectionError, QueryError)):
                raise QueryError(f"更新数据失败: {str(e)}")
            raise
    
    def delete(self, table: str, conditions: Dict[str, Any] = None) -> int:
        """
        从表中删除数据
        
        参数:
            table: 表名
            conditions: 删除条件
            
        返回:
            int: 受影响的行数
            
        异常:
            ConnectionError: 未连接时抛出
            QueryError: 删除失败时抛出
        """
        if not self.is_connected():
            self.connect()
            
        try:
            # 确保表名是有效的标识符
            table = self._sanitize_name(table)
            
            # 检查表是否存在
            if not self.table_exists(table):
                raise QueryError(f"表不存在: {table}")
                
            # 构建SQL语句
            sql = f"DELETE FROM {table}"
            
            # 添加WHERE子句
            params = []
            if conditions:
                where_clauses = []
                for key, value in conditions.items():
                    if value is None:
                        where_clauses.append(f"{self._sanitize_name(key)} IS NULL")
                    else:
                        where_clauses.append(f"{self._sanitize_name(key)} = ?")
                        params.append(value)
                
                if where_clauses:
                    sql += " WHERE " + " AND ".join(where_clauses)
            
            # 执行删除
            cursor = self.conn.execute(sql, params)
            
            # 返回受影响的行数
            return cursor.rowcount
        except Exception as e:
            self.logger.error(f"删除数据失败: {str(e)}")
            if not isinstance(e, (ConnectionError, QueryError)):
                raise QueryError(f"删除数据失败: {str(e)}")
            raise
    
    def begin_transaction(self) -> bool:
        """
        开始事务
        
        返回:
            bool: 是否成功开始事务
            
        异常:
            ConnectionError: 未连接到数据库时抛出
            TransactionError: 开始事务失败时抛出
        """
        if not self.is_connected():
            raise ConnectionError("未连接到数据库")
            
        try:
            if self.in_transaction:
                self.logger.warning("已经在事务中，不能开始新事务")
                return True
            
            # 确保没有活动的事务
            try:
                cursor = self.conn.execute("PRAGMA transaction_active")
                result = cursor.fetchone()
                if result and result[0] == 1:
                    self.logger.warning("SQLite已有活动事务，强制回滚")
                    self.conn.execute("ROLLBACK")
            except Exception as e:
                self.logger.warning(f"检查事务状态失败: {str(e)}")
            
            # 设置隔离级别并开始事务
            self.conn.isolation_level = "DEFERRED"  # 默认级别，提供基本隔离
            self.conn.execute("BEGIN TRANSACTION")
            
            # 验证事务是否真的开始了
            try:
                cursor = self.conn.execute("PRAGMA transaction_active")
                result = cursor.fetchone()
                if result and result[0] == 1:
                    self.in_transaction = True
                    self.logger.info("事务已开始")
                else:
                    self.logger.warning("事务未能正确开始")
                    raise TransactionError("事务未能正确开始")
            except Exception as e:
                self.logger.warning(f"验证事务状态失败: {str(e)}")
                # 假设事务已开始
                self.in_transaction = True
                self.logger.info("假设事务已开始（无法验证）")
            
            return True
        except Exception as e:
            self.logger.error(f"开始事务失败: {str(e)}")
            raise TransactionError(f"开始事务失败: {str(e)}")
    
    def commit_transaction(self) -> bool:
        """
        提交事务
        
        返回:
            bool: 是否成功提交事务
            
        异常:
            ConnectionError: 未连接时抛出
            TransactionError: 提交事务失败时抛出
        """
        if not self.is_connected():
            raise ConnectionError("未连接到数据库")
            
        try:
            if not self.in_transaction:
                self.logger.warning("没有正在进行的事务")
                return True
            
            # 检查事务状态
            try:
                cursor = self.conn.execute("PRAGMA transaction_active")
                result = cursor.fetchone()
                if not result or result[0] == 0:
                    self.logger.warning("SQLite没有活动的事务，跳过提交")
                    self.in_transaction = False
                    return True
            except Exception as e:
                self.logger.warning(f"检查事务状态失败: {str(e)}")
                
            self.conn.execute("COMMIT")
            self.in_transaction = False
            self.logger.info("事务已提交")
            return True
        except Exception as e:
            self.logger.error(f"提交事务失败: {str(e)}")
            raise TransactionError(f"提交事务失败: {str(e)}")
    
    def rollback_transaction(self) -> bool:
        """
        回滚事务
        
        返回:
            bool: 是否成功回滚
            
        异常:
            ConnectionError: 未连接到数据库时抛出
            TransactionError: 回滚事务失败时抛出
        """
        if not self.connected or not self.conn:
            raise ConnectionError("未连接到数据库")
            
        try:
            if not self.in_transaction:
                self.logger.warning("没有正在进行的事务")
                return True
            
            # 检查是否有活动的事务，更安全的方式
            try:
                cursor = self.conn.execute("PRAGMA transaction_active")
                result = cursor.fetchone()
                transaction_active = result[0] if result else 0
            except Exception as e:
                # 如果检查失败，假设有活动事务以确保安全
                transaction_active = 1
                self.logger.warning(f"检查事务状态失败，假设有活动事务: {str(e)}")
            
            # 如果没有活动事务但in_transaction为True，只重置标志位
            if transaction_active == 0:
                self.logger.warning("SQLite没有活动的事务，但in_transaction标志为True，重置标志位")
                self.in_transaction = False
                return True
                
            # 强制回滚所有更改
            try:
                # 重置游标并执行回滚
                try:
                    # 先尝试释放所有游标
                    self.conn.execute("ROLLBACK")
                except Exception:
                    # 如果上面失败，使用更强的方式
                    self.conn.isolation_level = None  # 切换到自动提交模式
                    self.conn.execute("ROLLBACK")
                
                self.logger.info("事务已回滚")
            except Exception as e:
                # 如果回滚失败，检查是否是因为没有活动事务
                if "no transaction is active" in str(e):
                    self.logger.warning("尝试回滚不存在的事务，重置标志位")
                    self.in_transaction = False
                    return True
                else:
                    # 其他错误则重新抛出
                    raise
            
            # 重置事务状态
            self.in_transaction = False
            
            # 确保事务真的被回滚了
            try:
                cursor = self.conn.execute("PRAGMA transaction_active")
                result = cursor.fetchone()
                if result and result[0] == 1:
                    self.logger.warning("事务回滚后仍处于活动状态，尝试再次回滚")
                    self.conn.execute("ROLLBACK")
                    
                    # 最后的检查
                    cursor = self.conn.execute("PRAGMA transaction_active")
                    result = cursor.fetchone()
                    if result and result[0] == 1:
                        self.logger.error("无法完全回滚事务")
                        raise TransactionError("无法完全回滚事务")
            except Exception as e:
                self.logger.warning(f"检查事务状态失败: {str(e)}")
            
            # 执行VACUUM来清理数据库并确保所有更改都被撤销
            try:
                if not self.in_transaction:
                    self.conn.execute("VACUUM")
            except Exception as e:
                self.logger.warning(f"执行VACUUM清理失败: {str(e)}")
            
            return True
        except Exception as e:
            self.logger.error(f"回滚事务失败: {str(e)}")
            # 如果回滚失败，重置事务标志
            self.in_transaction = False
            raise TransactionError(f"回滚事务失败: {str(e)}")
    
    def execute_in_transaction(self, func: Callable, *args, **kwargs) -> Any:
        """
        在事务中执行函数
        
        参数:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        返回:
            Any: 函数的返回值
            
        异常:
            ConnectionError: 未连接时抛出
            TransactionError: 事务处理失败时抛出
        """
        if not self.is_connected():
            self.connect()
            
        # 记录原来的事务状态
        was_in_transaction = self.in_transaction
        
        try:
            # 如果没有在事务中，开始一个新事务
            if not was_in_transaction:
                self.begin_transaction()
                
            # 执行函数
            result = func(*args, **kwargs)
            
            # 如果我们开始了一个新事务，提交它
            if not was_in_transaction:
                self.commit_transaction()
                
            return result
        except Exception as e:
            # 如果我们开始了一个新事务，在发生异常时回滚
            if not was_in_transaction:
                self.rollback_transaction()
            
            self.logger.error(f"在事务中执行函数失败: {str(e)}")
            raise
    
    def execute(self, sql: str, params: tuple = None) -> int:
        """
        执行SQL语句
        
        参数:
            sql: SQL语句
            params: 绑定参数
            
        返回:
            int: 影响的行数
            
        异常:
            ConnectionError: 未连接时抛出
            QueryError: 执行SQL失败时抛出
        """
        if not self.is_connected():
            raise ConnectionError("未连接到数据库")
            
        try:
            cursor = self.conn.cursor()
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
                
            # 自动提交，如果没有在事务中
            if not self.in_transaction:
                self.conn.commit()
                
            # 返回影响的行数
            return cursor.rowcount
        except Exception as e:
            self.logger.error(f"执行SQL失败: {str(e)}")
            if not self.in_transaction:
                self.conn.rollback()
            raise QueryError(f"执行SQL失败: {str(e)}")
    
    def table_exists(self, table: str) -> bool:
        """
        检查表是否存在
        
        参数:
            table: 表名
            
        返回:
            bool: 表是否存在
            
        异常:
            ConnectionError: 未连接时抛出
        """
        if not self.is_connected():
            self.connect()
            
        try:
            # 确保表名是有效的标识符
            table = self._sanitize_name(table)
            
            # 查询sqlite_master表
            cursor = self.conn.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                (table,)
            )
            
            return cursor.fetchone() is not None
        except Exception as e:
            self.logger.error(f"检查表是否存在失败: {str(e)}")
            raise ConnectionError(f"检查表是否存在失败: {str(e)}")
    
    def create_table(self, table: str, schema: Dict[str, str], if_not_exists: bool = True) -> bool:
        """
        创建表
        
        参数:
            table: 表名
            schema: 表结构，格式为 {字段名: 类型}
            if_not_exists: 如果表已存在，是否忽略错误
            
        返回:
            bool: 是否成功创建表
            
        异常:
            ConnectionError: 未连接时抛出
            QueryError: 创建表失败时抛出
        """
        if not self.is_connected():
            self.connect()
            
        try:
            # 确保表名是有效的标识符
            table = self._sanitize_name(table)
            
            # 如果表已存在且不忽略错误，则抛出异常
            if self.table_exists(table) and not if_not_exists:
                raise QueryError(f"表已存在: {table}")
                
            # 构建建表SQL语句
            return self._create_table_from_schema(table, schema, if_not_exists)
        except Exception as e:
            self.logger.error(f"创建表失败: {str(e)}")
            if not isinstance(e, (ConnectionError, QueryError)):
                raise QueryError(f"创建表失败: {str(e)}")
            raise
    
    def drop_table(self, table: str, if_exists: bool = True) -> bool:
        """
        删除表
        
        参数:
            table: 表名
            if_exists: 如果表不存在，是否忽略错误
            
        返回:
            bool: 是否成功删除表
            
        异常:
            ConnectionError: 未连接时抛出
            QueryError: 删除表失败时抛出
        """
        if not self.is_connected():
            self.connect()
            
        try:
            # 确保表名是有效的标识符
            table = self._sanitize_name(table)
            
            # 如果表不存在且不忽略错误，则抛出异常
            if not self.table_exists(table) and not if_exists:
                raise QueryError(f"表不存在: {table}")
                
            # 如果表不存在且忽略错误，则直接返回
            if not self.table_exists(table) and if_exists:
                return True
                
            # 执行删除表操作
            self.conn.execute(f"DROP TABLE {table}")
            
            # 从缓存中移除表结构
            if table in self._table_schemas:
                del self._table_schemas[table]
                
            return True
        except Exception as e:
            self.logger.error(f"删除表失败: {str(e)}")
            if not isinstance(e, (ConnectionError, QueryError)):
                raise QueryError(f"删除表失败: {str(e)}")
            raise
            
    def backup(self, backup_path: str = None) -> bool:
        """
        备份数据库
        
        参数:
            backup_path: 备份文件路径，默认为数据库路径加上时间戳
            
        返回:
            bool: 是否成功备份
            
        异常:
            ConnectionError: 未连接时抛出
            QueryError: 备份失败时抛出
        """
        if not self.is_connected():
            self.connect()
            
        try:
            # 如果未提供备份路径，使用默认路径
            if not backup_path:
                timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
                backup_dir = os.path.join(os.path.dirname(self.db_path), 'backup')
                os.makedirs(backup_dir, exist_ok=True)
                backup_path = os.path.join(backup_dir, f"{os.path.basename(self.db_path)}.{timestamp}")
                
            # 创建备份数据库连接
            backup_conn = sqlite3.connect(backup_path)
            
            # 使用SQLite的备份API
            with backup_conn:
                self.conn.backup(backup_conn)
                
            backup_conn.close()
            
            self.logger.info(f"数据库已备份到: {backup_path}")
            return True
        except Exception as e:
            self.logger.error(f"备份数据库失败: {str(e)}")
            raise QueryError(f"备份数据库失败: {str(e)}")
            
    def restore(self, backup_path: str) -> bool:
        """
        从备份恢复数据库
        
        参数:
            backup_path: 备份文件路径
            
        返回:
            bool: 是否成功恢复
            
        异常:
            ConnectionError: 未连接时抛出
            QueryError: 恢复失败时抛出
        """
        if not os.path.exists(backup_path):
            raise QueryError(f"备份文件不存在: {backup_path}")
            
        try:
            # 断开当前连接
            if self.is_connected():
                self.disconnect()
                
            # 创建备份数据库连接
            backup_conn = sqlite3.connect(backup_path)
            
            # 创建新的数据库连接
            self.conn = sqlite3.connect(
                self.db_path, 
                timeout=self.timeout,
                check_same_thread=False,
                isolation_level=None
            )
            
            # 使用SQLite的备份API恢复数据
            with self.conn:
                backup_conn.backup(self.conn)
                
            backup_conn.close()
            self.connected = True
            
            # 重新配置数据库
            for key, value in self.pragma_settings.items():
                self.conn.execute(f"PRAGMA {key} = {value}")
                
            # 设置行工厂函数
            self.conn.row_factory = sqlite3.Row
            
            self.logger.info(f"数据库已从备份恢复: {backup_path}")
            return True
        except Exception as e:
            self.logger.error(f"从备份恢复数据库失败: {str(e)}")
            raise QueryError(f"从备份恢复数据库失败: {str(e)}")
            
    def get_table_schema(self, table: str) -> Dict[str, str]:
        """
        获取表结构
        
        参数:
            table: 表名
            
        返回:
            Dict[str, str]: 表结构，格式为 {字段名: 类型}
            
        异常:
            ConnectionError: 未连接时抛出
            QueryError: 获取表结构失败时抛出
        """
        if not self.is_connected():
            self.connect()
            
        try:
            # 确保表名是有效的标识符
            table = self._sanitize_name(table)
            
            # 如果表不存在，则抛出异常
            if not self.table_exists(table):
                raise QueryError(f"表不存在: {table}")
                
            # 从缓存获取表结构
            if table in self._table_schemas:
                return self._table_schemas[table]
                
            # 查询表结构
            cursor = self.conn.execute(f"PRAGMA table_info({table})")
            
            # 解析表结构
            schema = {}
            for row in cursor.fetchall():
                column_name = row[1]
                column_type = row[2]
                schema[column_name] = column_type
                
            # 缓存表结构
            self._table_schemas[table] = schema
            
            return schema
        except Exception as e:
            self.logger.error(f"获取表结构失败: {str(e)}")
            if not isinstance(e, (ConnectionError, QueryError)):
                raise QueryError(f"获取表结构失败: {str(e)}")
            raise
            
    def get_table_stats(self, table: str) -> Dict[str, Any]:
        """
        获取表统计信息
        
        参数:
            table: 表名
            
        返回:
            Dict[str, Any]: 表统计信息
            
        异常:
            ConnectionError: 未连接时抛出
            QueryError: 获取表统计信息失败时抛出
        """
        if not self.is_connected():
            self.connect()
            
        try:
            # 确保表名是有效的标识符
            table = self._sanitize_name(table)
            
            # 如果表不存在，则抛出异常
            if not self.table_exists(table):
                raise QueryError(f"表不存在: {table}")
                
            # 获取表行数
            cursor = self.conn.execute(f"SELECT COUNT(*) FROM {table}")
            row_count = cursor.fetchone()[0]
            
            # 获取表结构
            schema = self.get_table_schema(table)
            
            # 获取表大小信息（SQLite没有直接的API，这是一个估计值）
            cursor = self.conn.execute("PRAGMA page_count")
            page_count = cursor.fetchone()[0]
            
            cursor = self.conn.execute("PRAGMA page_size")
            page_size = cursor.fetchone()[0]
            
            # 估计表大小（字节）
            estimated_size = page_count * page_size
            
            return {
                'table_name': table,
                'row_count': row_count,
                'column_count': len(schema),
                'columns': schema,
                'estimated_size_bytes': estimated_size,
                'estimated_size_mb': round(estimated_size / (1024 * 1024), 2),
                'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        except Exception as e:
            self.logger.error(f"获取表统计信息失败: {str(e)}")
            if not isinstance(e, (ConnectionError, QueryError)):
                raise QueryError(f"获取表统计信息失败: {str(e)}")
            raise
    
    def _sanitize_name(self, name: str) -> str:
        """
        确保名称是有效的SQLite标识符
        
        参数:
            name: 原始名称
            
        返回:
            str: 处理后的名称
        """
        # 去除非法字符
        name = ''.join(c for c in name if c.isalnum() or c == '_')
        
        # 确保不以数字开头
        if name and name[0].isdigit():
            name = 'f_' + name
            
        # 如果为空，返回默认名称
        if not name:
            name = 'column'
            
        return name
        
    def _infer_schema(self, df: pd.DataFrame) -> Dict[str, str]:
        """
        从DataFrame推断表结构
        
        参数:
            df: 数据框
            
        返回:
            Dict[str, str]: 表结构，格式为 {字段名: 类型}
        """
        schema = {}
        
        for column in df.columns:
            dtype = df[column].dtype
            
            # 将pandas/numpy类型映射到SQLite类型
            if pd.api.types.is_integer_dtype(dtype):
                schema[column] = 'INTEGER'
            elif pd.api.types.is_float_dtype(dtype):
                schema[column] = 'REAL'
            elif pd.api.types.is_datetime64_dtype(dtype):
                schema[column] = 'DATETIME'
            elif pd.api.types.is_bool_dtype(dtype):
                schema[column] = 'BOOLEAN'
            else:
                schema[column] = 'TEXT'
                
        return schema
        
    def _create_table_from_schema(self, table: str, schema: Dict[str, str], if_not_exists: bool = True) -> bool:
        """
        根据结构创建表
        
        参数:
            table: 表名
            schema: 表结构，格式为 {字段名: 类型}
            if_not_exists: 如果表已存在，是否忽略错误
            
        返回:
            bool: 是否成功创建表
        """
        # 构建SQL语句
        columns = []
        
        for column, column_type in schema.items():
            column_name = self._sanitize_name(column)
            columns.append(f"{column_name} {column_type}")
            
        # 添加IF NOT EXISTS子句
        if_not_exists_clause = "IF NOT EXISTS" if if_not_exists else ""
        
        # 创建表
        sql = f"CREATE TABLE {if_not_exists_clause} {table} ({', '.join(columns)})"
        
        self.conn.execute(sql)
        
        # 缓存表结构
        self._table_schemas[table] = schema
        
        self.logger.info(f"已创建表: {table}")
        return True

    def _insert_with_dedup(self, data, table_name, data_category):
        """
        使用去重机制插入数据

        参数:
            data: 要插入的DataFrame
            table_name: 表名
            data_category: 数据类别 ('market', 'financial', 'reference')

        返回:
            实际插入的记录数
        """
        if data.empty:
            return 0

        # 确保唯一约束存在
        self._ensure_unique_constraint(table_name, data_category)

        # 获取插入前的记录数
        cursor = self.conn.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        before_count = cursor.fetchone()[0]

        # 使用INSERT OR IGNORE插入数据
        try:
            data.to_sql(table_name, self.conn, if_exists='append', index=False, method='multi')
        except Exception as e:
            # 如果批量插入失败，尝试逐行插入
            self.logger.warning(f"批量插入失败，尝试逐行插入: {e}")
            for _, row in data.iterrows():
                try:
                    row.to_frame().T.to_sql(table_name, self.conn, if_exists='append', index=False)
                except Exception:
                    # 忽略重复数据错误
                    pass

        # 获取插入后的记录数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        after_count = cursor.fetchone()[0]

        actual_inserted = after_count - before_count
        if actual_inserted < len(data):
            self.logger.debug(f"去重生效: 尝试插入{len(data)}条，实际插入{actual_inserted}条")

        return actual_inserted

    def _ensure_unique_constraint(self, table_name, data_category):
        """
        确保表有适当的唯一约束

        参数:
            table_name: 表名
            data_category: 数据类别
        """
        cursor = self.conn.cursor()

        try:
            if data_category == 'market':
                # 市场数据：股票代码+交易日期唯一
                index_name = f"idx_{table_name}_unique"
                cursor.execute(f"""
                    CREATE UNIQUE INDEX IF NOT EXISTS {index_name}
                    ON {table_name}(ts_code, trade_date)
                """)
            elif data_category == 'financial':
                # 财务数据：股票代码+报告期唯一
                index_name = f"idx_{table_name}_unique"
                cursor.execute(f"""
                    CREATE UNIQUE INDEX IF NOT EXISTS {index_name}
                    ON {table_name}(ts_code, end_date)
                """)
            elif data_category == 'reference':
                # 参考数据：股票代码唯一
                index_name = f"idx_{table_name}_unique"
                cursor.execute(f"""
                    CREATE UNIQUE INDEX IF NOT EXISTS {index_name}
                    ON {table_name}(ts_code)
                """)

            self.conn.commit()

        except Exception as e:
            self.logger.debug(f"创建唯一约束失败: {e}")
            # 不抛出异常，因为约束可能已存在

    def save_market_data(self, data, data_type=None):
        """
        保存市场数据到SQLite数据库
        参数:
            data: 要保存的市场数据，通常是DataFrame
            data_type: 市场数据类型，如'daily'，将用作表名
        返回:
            保存的记录数量
        """
        if data is None or (isinstance(data, pd.DataFrame) and data.empty):
            self.logger.warning(f"尝试保存空的市场数据: {data_type}")
            return 0
        table_name = data_type or "market_data"
        if not self.is_connected():
            self.connect()
        try:
            if isinstance(data, pd.DataFrame):
                if not self.table_exists(table_name) and self.auto_create_tables:
                    schema = self._infer_schema(data)
                    self._create_table_from_schema(table_name, schema)
                data = data.copy()
                for col in data.select_dtypes(include=['datetime64']).columns:
                    data[col] = data[col].dt.strftime('%Y-%m-%d %H:%M:%S')
                # 使用INSERT OR IGNORE避免重复数据
                rows_affected = self._insert_with_dedup(data, table_name, 'market')
                self.logger.info(f"已保存 {rows_affected} 条市场数据到表 {table_name}")
                return rows_affected
            else:
                raise DataError(f"不支持的市场数据类型: {type(data)}")
        except Exception as e:
            self.logger.error(f"保存市场数据到表 {table_name} 失败: {str(e)}")
            raise DataError(f"保存市场数据到表 {table_name} 失败: {str(e)}")

    def save_reference_data(self, data, data_type=None):
        """
        保存参考数据到SQLite数据库
        参数:
            data: 要保存的参考数据，通常是DataFrame
            data_type: 参考数据类型，如'stock_list'，将用作表名
        返回:
            保存的记录数量
        """
        if data is None or (isinstance(data, pd.DataFrame) and data.empty):
            self.logger.warning(f"尝试保存空的参考数据: {data_type}")
            return 0
        table_name = data_type or "reference_data"
        if not self.is_connected():
            self.connect()
        try:
            if isinstance(data, pd.DataFrame):
                if not self.table_exists(table_name) and self.auto_create_tables:
                    schema = self._infer_schema(data)
                    self._create_table_from_schema(table_name, schema)
                data = data.copy()
                for col in data.select_dtypes(include=['datetime64']).columns:
                    data[col] = data[col].dt.strftime('%Y-%m-%d %H:%M:%S')
                data.to_sql(table_name, self.conn, if_exists='replace', index=False)
                rows_affected = len(data)
                self.logger.info(f"已保存 {rows_affected} 条参考数据到表 {table_name}")
                return rows_affected
            else:
                raise DataError(f"不支持的参考数据类型: {type(data)}")
        except Exception as e:
            self.logger.error(f"保存参考数据到表 {table_name} 失败: {str(e)}")
            raise DataError(f"保存参考数据到表 {table_name} 失败: {str(e)}")

    def save_financial_data(self, data, report_type=None):
        """
        保存财务数据到SQLite数据库
        参数:
            data: 要保存的财务数据，通常是DataFrame
            report_type: 财务数据类型，如'income'、'balancesheet'、'cashflow'，将用作表名
        返回:
            保存的记录数量
        """
        if data is None or (isinstance(data, pd.DataFrame) and data.empty):
            self.logger.warning(f"尝试保存空的财务数据: {report_type}")
            return 0
        table_name = report_type or "financial_data"
        if not self.is_connected():
            self.connect()
        try:
            if isinstance(data, pd.DataFrame):
                if not self.table_exists(table_name) and self.auto_create_tables:
                    schema = self._infer_schema(data)
                    self._create_table_from_schema(table_name, schema)
                data = data.copy()
                for col in data.select_dtypes(include=['datetime64']).columns:
                    data[col] = data[col].dt.strftime('%Y-%m-%d %H:%M:%S')
                # 使用INSERT OR IGNORE避免重复数据
                rows_affected = self._insert_with_dedup(data, table_name, 'financial')
                self.logger.info(f"已保存 {rows_affected} 条财务数据到表 {table_name}")
                return rows_affected
            else:
                raise DataError(f"不支持的财务数据类型: {type(data)}")
        except Exception as e:
            self.logger.error(f"保存财务数据到表 {table_name} 失败: {str(e)}")
            raise DataError(f"保存财务数据到表 {table_name} 失败: {str(e)}")

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        返回:
            Dict[str, Any]: 性能统计数据
        """
        stats = {}

        # 获取连接池统计
        pool_stats = self.pool_manager.get_pool_stats(self.pool_name)
        if pool_stats:
            stats['connection_pool'] = {
                'total_connections': pool_stats.total_connections,
                'active_connections': pool_stats.active_connections,
                'idle_connections': pool_stats.idle_connections,
                'pool_hits': pool_stats.pool_hits,
                'pool_misses': pool_stats.pool_misses,
                'connection_errors': pool_stats.connection_errors,
                'avg_connection_time': pool_stats.avg_connection_time
            }

        # 获取批量处理统计
        batch_stats = self.batch_processor.get_stats()
        stats['batch_processing'] = {
            'total_records': batch_stats.total_records,
            'processed_records': batch_stats.processed_records,
            'failed_records': batch_stats.failed_records,
            'duplicate_records': batch_stats.duplicate_records,
            'processing_time': batch_stats.processing_time,
            'throughput': batch_stats.throughput
        }

        return stats

    def execute_query(self, query: str, params: tuple = None) -> List[tuple]:
        """
        执行查询语句

        参数:
            query: SQL查询语句
            params: 查询参数

        返回:
            List[tuple]: 查询结果
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                return cursor.fetchall()
        except Exception as e:
            self.logger.error(f"执行查询失败: {e}")
            raise

    def __del__(self):
        """析构函数，清理连接池"""
        try:
            if hasattr(self, 'pool_manager') and hasattr(self, 'pool_name'):
                self.pool_manager.close_pool(self.pool_name)
        except Exception:
            pass