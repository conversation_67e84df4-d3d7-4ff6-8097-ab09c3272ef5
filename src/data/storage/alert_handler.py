#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
告警处理器
- 处理集群监控产生的告警
- 支持多种告警通知方式
- 提供告警抑制和聚合功能
"""

import time
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from src.utils.logging.logger_factory import get_logger
from src.data.storage.cluster_monitor import Alert, AlertLevel

@dataclass
class AlertConfig:
    """告警配置"""
    email_enabled: bool = False
    email_smtp_host: str = ""
    email_smtp_port: int = 587
    email_username: str = ""
    email_password: str = ""
    email_recipients: List[str] = None
    
    webhook_enabled: bool = False
    webhook_url: str = ""
    
    console_enabled: bool = True
    
    # 告警抑制配置
    suppress_duration: int = 300  # 5分钟内相同告警抑制
    max_alerts_per_minute: int = 10  # 每分钟最大告警数

class AlertHandler:
    """告警处理器"""
    
    def __init__(self, config: AlertConfig = None):
        self.config = config or AlertConfig()
        self.logger = get_logger(__name__)
        
        # 告警抑制
        self.last_alert_times: Dict[str, float] = {}
        self.alert_counts: Dict[str, int] = {}
        self.last_minute_reset = time.time()
        
        self.logger.info("✅ 告警处理器初始化完成")
    
    def handle_alert(self, alert: Alert):
        """处理告警"""
        try:
            # 检查告警抑制
            if self._should_suppress_alert(alert):
                self.logger.debug(f"告警被抑制: {alert.alert_id}")
                return
            
            # 更新告警统计
            self._update_alert_stats(alert)
            
            # 发送告警通知
            self._send_notifications(alert)
            
        except Exception as e:
            self.logger.error(f"处理告警失败: {alert.alert_id}, 错误: {e}")
    
    def _should_suppress_alert(self, alert: Alert) -> bool:
        """检查是否应该抑制告警"""
        current_time = time.time()
        
        # 重置每分钟计数器
        if current_time - self.last_minute_reset > 60:
            self.alert_counts.clear()
            self.last_minute_reset = current_time
        
        # 检查每分钟告警数量限制
        minute_key = f"{int(current_time // 60)}"
        current_count = self.alert_counts.get(minute_key, 0)
        if current_count >= self.config.max_alerts_per_minute:
            return True
        
        # 检查相同告警的抑制时间
        last_time = self.last_alert_times.get(alert.alert_id, 0)
        if current_time - last_time < self.config.suppress_duration:
            return True
        
        return False
    
    def _update_alert_stats(self, alert: Alert):
        """更新告警统计"""
        current_time = time.time()
        
        # 更新最后告警时间
        self.last_alert_times[alert.alert_id] = current_time
        
        # 更新每分钟计数
        minute_key = f"{int(current_time // 60)}"
        self.alert_counts[minute_key] = self.alert_counts.get(minute_key, 0) + 1
    
    def _send_notifications(self, alert: Alert):
        """发送告警通知"""
        # 控制台通知
        if self.config.console_enabled:
            self._send_console_notification(alert)
        
        # 邮件通知
        if self.config.email_enabled:
            self._send_email_notification(alert)
        
        # Webhook通知
        if self.config.webhook_enabled:
            self._send_webhook_notification(alert)
    
    def _send_console_notification(self, alert: Alert):
        """发送控制台通知"""
        level_emoji = {
            AlertLevel.INFO: "ℹ️",
            AlertLevel.WARNING: "⚠️",
            AlertLevel.ERROR: "❌",
            AlertLevel.CRITICAL: "🚨"
        }
        
        emoji = level_emoji.get(alert.level, "📢")
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(alert.timestamp))
        
        message = f"{emoji} [{alert.level.value.upper()}] {timestamp}"
        message += f"\n标题: {alert.title}"
        message += f"\n消息: {alert.message}"
        
        if alert.node_id:
            message += f"\n节点: {alert.node_id}"
        
        if alert.metric_name and alert.current_value is not None:
            message += f"\n指标: {alert.metric_name} = {alert.current_value}"
            if alert.threshold is not None:
                message += f" (阈值: {alert.threshold})"
        
        print(message)
        print("-" * 50)
    
    def _send_email_notification(self, alert: Alert):
        """发送邮件通知"""
        try:
            if not self.config.email_recipients:
                return

            # 简化的邮件通知（避免导入问题）
            self.logger.info(f"[邮件通知] {alert.level.value.upper()}: {alert.title}")
            self.logger.info(f"[邮件通知] 消息: {alert.message}")
            if alert.node_id:
                self.logger.info(f"[邮件通知] 节点: {alert.node_id}")

            # 在实际部署中，这里应该实现真正的邮件发送
            # 可以使用外部邮件服务或SMTP库

        except Exception as e:
            self.logger.error(f"邮件告警发送失败: {alert.alert_id}, 错误: {e}")
    
    def _format_email_body(self, alert: Alert) -> str:
        """格式化邮件内容"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(alert.timestamp))
        
        level_colors = {
            AlertLevel.INFO: "#17a2b8",
            AlertLevel.WARNING: "#ffc107",
            AlertLevel.ERROR: "#dc3545",
            AlertLevel.CRITICAL: "#dc3545"
        }
        
        color = level_colors.get(alert.level, "#6c757d")
        
        html = f"""
        <html>
        <body>
            <div style="font-family: Arial, sans-serif; max-width: 600px;">
                <div style="background-color: {color}; color: white; padding: 15px; border-radius: 5px 5px 0 0;">
                    <h2 style="margin: 0;">{alert.title}</h2>
                    <p style="margin: 5px 0 0 0;">级别: {alert.level.value.upper()}</p>
                </div>
                
                <div style="border: 1px solid #ddd; border-top: none; padding: 20px; border-radius: 0 0 5px 5px;">
                    <p><strong>消息:</strong> {alert.message}</p>
                    <p><strong>时间:</strong> {timestamp}</p>
                    
                    {f'<p><strong>节点:</strong> {alert.node_id}</p>' if alert.node_id else ''}
                    
                    {f'<p><strong>指标:</strong> {alert.metric_name}</p>' if alert.metric_name else ''}
                    
                    {f'<p><strong>当前值:</strong> {alert.current_value}</p>' if alert.current_value is not None else ''}
                    
                    {f'<p><strong>阈值:</strong> {alert.threshold}</p>' if alert.threshold is not None else ''}
                    
                    <hr style="margin: 20px 0;">
                    <p style="color: #6c757d; font-size: 12px;">
                        此告警由量化投资平台集群监控系统自动发送<br>
                        告警ID: {alert.alert_id}
                    </p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def _send_webhook_notification(self, alert: Alert):
        """发送Webhook通知"""
        try:
            # 简化的Webhook通知（避免requests依赖）
            payload = {
                'alert_id': alert.alert_id,
                'level': alert.level.value,
                'title': alert.title,
                'message': alert.message,
                'node_id': alert.node_id,
                'metric_name': alert.metric_name,
                'threshold': alert.threshold,
                'current_value': alert.current_value,
                'timestamp': alert.timestamp,
                'resolved': alert.resolved
            }

            self.logger.info(f"[Webhook通知] {json.dumps(payload, ensure_ascii=False, indent=2)}")

            # 在实际部署中，这里应该实现真正的HTTP请求
            # 可以使用urllib或requests库

        except Exception as e:
            self.logger.error(f"Webhook告警发送失败: {alert.alert_id}, 错误: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取告警处理统计"""
        current_time = time.time()
        
        # 清理过期的统计数据
        expired_keys = [
            key for key, timestamp in self.last_alert_times.items()
            if current_time - timestamp > 3600  # 1小时
        ]
        for key in expired_keys:
            del self.last_alert_times[key]
        
        return {
            'config': {
                'email_enabled': self.config.email_enabled,
                'webhook_enabled': self.config.webhook_enabled,
                'console_enabled': self.config.console_enabled,
                'suppress_duration': self.config.suppress_duration,
                'max_alerts_per_minute': self.config.max_alerts_per_minute
            },
            'stats': {
                'tracked_alerts': len(self.last_alert_times),
                'current_minute_alerts': sum(self.alert_counts.values()),
                'last_minute_reset': self.last_minute_reset
            }
        }

# 默认告警处理器实例
default_alert_handler = AlertHandler()

def setup_default_alert_handler(config: AlertConfig):
    """设置默认告警处理器"""
    global default_alert_handler
    default_alert_handler = AlertHandler(config)

def get_default_alert_handler() -> AlertHandler:
    """获取默认告警处理器"""
    return default_alert_handler
