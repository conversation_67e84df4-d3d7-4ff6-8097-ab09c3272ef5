#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分布式数据库管理器
- 支持多节点数据库集群管理
- 实现数据分片和负载均衡
- 提供读写分离和故障转移
- 支持一致性哈希和数据复制
"""

import asyncio
import hashlib
import time
import logging
import threading
import pandas as pd
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
import random

from src.utils.logging.logger_factory import get_logger
from src.data.storage.connection_pool_manager import get_pool_manager, ConnectionPoolConfig
from sqlalchemy import text

class NodeRole(Enum):
    """节点角色"""
    MASTER = "master"
    SLAVE = "slave"
    REPLICA = "replica"

class NodeStatus(Enum):
    """节点状态"""
    ONLINE = "online"
    OFFLINE = "offline"
    DEGRADED = "degraded"
    MAINTENANCE = "maintenance"

@dataclass
class DatabaseNode:
    """数据库节点配置"""
    node_id: str
    host: str
    port: int
    database: str
    username: str
    password: str
    role: NodeRole
    weight: int = 1
    max_connections: int = 100
    status: NodeStatus = NodeStatus.ONLINE
    last_heartbeat: float = field(default_factory=time.time)
    error_count: int = 0
    pool_name: Optional[str] = None

@dataclass
class ShardConfig:
    """分片配置"""
    shard_id: str
    shard_key: str  # 分片键字段名
    hash_range: Tuple[int, int]  # 哈希值范围
    master_nodes: List[str]  # 主节点ID列表
    replica_nodes: List[str]  # 副本节点ID列表

class DistributedDatabaseManager:
    """分布式数据库管理器"""
    
    def __init__(self, config_file: str = None, logger: Optional[logging.Logger] = None):
        """
        初始化分布式数据库管理器
        
        参数:
            config_file: 配置文件路径
            logger: 日志记录器
        """
        self.logger = logger or get_logger(__name__)
        self.pool_manager = get_pool_manager()
        
        # 节点管理
        self.nodes: Dict[str, DatabaseNode] = {}
        self.shards: Dict[str, ShardConfig] = {}
        
        # 一致性哈希环
        self.hash_ring: Dict[int, str] = {}
        self.virtual_nodes = 150  # 每个物理节点的虚拟节点数
        
        # 负载均衡
        self.read_nodes: List[str] = []
        self.write_nodes: List[str] = []
        
        # 健康检查
        self.health_check_interval = 30  # 秒
        self.max_error_count = 3
        self._health_check_task = None
        
        # 线程安全
        self._lock = threading.RLock()
        
        # 加载配置
        if config_file:
            self.load_config(config_file)
    
    def add_node(
        self, 
        node_id: str,
        host: str,
        port: int,
        database: str,
        username: str,
        password: str,
        role: NodeRole,
        weight: int = 1,
        db_type: str = "mysql"
    ) -> bool:
        """
        添加数据库节点
        
        参数:
            node_id: 节点ID
            host: 主机地址
            port: 端口号
            database: 数据库名
            username: 用户名
            password: 密码
            role: 节点角色
            weight: 权重
            db_type: 数据库类型
            
        返回:
            bool: 是否成功添加
        """
        with self._lock:
            try:
                # 创建节点对象
                node = DatabaseNode(
                    node_id=node_id,
                    host=host,
                    port=port,
                    database=database,
                    username=username,
                    password=password,
                    role=role,
                    weight=weight
                )
                
                # 创建连接池
                pool_config = ConnectionPoolConfig(
                    pool_size=20,
                    max_overflow=50,
                    pool_timeout=30,
                    pool_recycle=3600
                )
                
                if db_type.lower() == "mysql":
                    pool_name = self.pool_manager.create_mysql_pool(
                        host=host,
                        port=port,
                        user=username,
                        password=password,
                        database=database,
                        pool_name=f"distributed_{node_id}",
                        config=pool_config
                    )
                else:
                    # SQLite或其他数据库类型
                    pool_name = self.pool_manager.create_sqlite_pool(
                        db_path=database,
                        pool_name=f"distributed_{node_id}",
                        config=pool_config
                    )
                
                node.pool_name = pool_name
                self.nodes[node_id] = node
                
                # 更新哈希环
                self._update_hash_ring()
                
                # 更新读写节点列表
                self._update_node_lists()
                
                self.logger.info(f"✅ 添加数据库节点成功: {node_id} ({role.value})")
                return True
                
            except Exception as e:
                self.logger.error(f"添加数据库节点失败: {node_id}, 错误: {e}")
                return False
    
    def remove_node(self, node_id: str) -> bool:
        """
        移除数据库节点
        
        参数:
            node_id: 节点ID
            
        返回:
            bool: 是否成功移除
        """
        with self._lock:
            if node_id not in self.nodes:
                return False
            
            try:
                node = self.nodes[node_id]
                
                # 关闭连接池
                if node.pool_name:
                    self.pool_manager.close_pool(node.pool_name)
                
                # 从节点列表中移除
                del self.nodes[node_id]
                
                # 更新哈希环
                self._update_hash_ring()
                
                # 更新读写节点列表
                self._update_node_lists()
                
                self.logger.info(f"✅ 移除数据库节点成功: {node_id}")
                return True
                
            except Exception as e:
                self.logger.error(f"移除数据库节点失败: {node_id}, 错误: {e}")
                return False
    
    def get_write_node(self, shard_key: str = None) -> Optional[DatabaseNode]:
        """
        获取写入节点
        
        参数:
            shard_key: 分片键
            
        返回:
            DatabaseNode: 写入节点
        """
        with self._lock:
            if shard_key:
                # 基于分片键选择节点
                node_id = self._get_node_by_hash(shard_key)
                if node_id and node_id in self.nodes:
                    node = self.nodes[node_id]
                    if node.status == NodeStatus.ONLINE and node.role in [NodeRole.MASTER]:
                        return node
            
            # 从可用的写入节点中选择
            available_nodes = [
                self.nodes[node_id] for node_id in self.write_nodes
                if self.nodes[node_id].status == NodeStatus.ONLINE
            ]
            
            if not available_nodes:
                return None
            
            # 基于权重选择节点
            return self._weighted_random_choice(available_nodes)
    
    def get_read_node(self, shard_key: str = None, prefer_replica: bool = True) -> Optional[DatabaseNode]:
        """
        获取读取节点
        
        参数:
            shard_key: 分片键
            prefer_replica: 是否优先选择副本节点
            
        返回:
            DatabaseNode: 读取节点
        """
        with self._lock:
            available_nodes = []
            
            if shard_key:
                # 基于分片键选择节点
                node_id = self._get_node_by_hash(shard_key)
                if node_id and node_id in self.nodes:
                    node = self.nodes[node_id]
                    if node.status == NodeStatus.ONLINE:
                        available_nodes.append(node)
            
            if not available_nodes:
                # 从可用的读取节点中选择
                if prefer_replica:
                    # 优先选择副本节点
                    available_nodes = [
                        self.nodes[node_id] for node_id in self.read_nodes
                        if (self.nodes[node_id].status == NodeStatus.ONLINE and 
                            self.nodes[node_id].role in [NodeRole.SLAVE, NodeRole.REPLICA])
                    ]
                
                if not available_nodes:
                    # 如果没有副本节点，选择主节点
                    available_nodes = [
                        self.nodes[node_id] for node_id in self.read_nodes
                        if self.nodes[node_id].status == NodeStatus.ONLINE
                    ]
            
            if not available_nodes:
                return None
            
            # 基于权重选择节点
            return self._weighted_random_choice(available_nodes)
    
    def execute_write(self, sql: str, params: Union[tuple, dict] = None, shard_key: str = None) -> Any:
        """
        执行写入操作
        
        参数:
            sql: SQL语句
            params: 参数
            shard_key: 分片键
            
        返回:
            Any: 执行结果
        """
        node = self.get_write_node(shard_key)
        if not node:
            raise Exception("没有可用的写入节点")
        
        try:
            with self.pool_manager.get_connection(node.pool_name) as conn:
                if params:
                    result = conn.execute(text(sql), params)
                else:
                    result = conn.execute(text(sql))
                return result
        except Exception as e:
            self._handle_node_error(node.node_id, e)
            raise
    
    def execute_read(self, sql: str, params: Union[tuple, dict] = None, shard_key: str = None) -> Any:
        """
        执行读取操作
        
        参数:
            sql: SQL语句
            params: 参数
            shard_key: 分片键
            
        返回:
            Any: 查询结果
        """
        node = self.get_read_node(shard_key)
        if not node:
            raise Exception("没有可用的读取节点")
        
        try:
            with self.pool_manager.get_connection(node.pool_name) as conn:
                if params:
                    result = conn.execute(text(sql), params)
                else:
                    result = conn.execute(text(sql))
                return result
        except Exception as e:
            self._handle_node_error(node.node_id, e)
            raise
    
    def _get_node_by_hash(self, key: str) -> Optional[str]:
        """根据哈希值获取节点"""
        if not self.hash_ring:
            return None
        
        hash_value = self._hash_key(key)
        
        # 在哈希环中找到第一个大于等于hash_value的节点
        for ring_hash in sorted(self.hash_ring.keys()):
            if hash_value <= ring_hash:
                return self.hash_ring[ring_hash]
        
        # 如果没找到，返回环上的第一个节点
        first_hash = min(self.hash_ring.keys())
        return self.hash_ring[first_hash]
    
    def _hash_key(self, key: str) -> int:
        """计算键的哈希值"""
        return int(hashlib.md5(key.encode('utf-8')).hexdigest(), 16)
    
    def _update_hash_ring(self):
        """更新一致性哈希环"""
        self.hash_ring.clear()
        
        for node_id, node in self.nodes.items():
            if node.status == NodeStatus.ONLINE:
                # 为每个节点创建虚拟节点
                for i in range(self.virtual_nodes):
                    virtual_key = f"{node_id}:{i}"
                    hash_value = self._hash_key(virtual_key)
                    self.hash_ring[hash_value] = node_id
    
    def _update_node_lists(self):
        """更新读写节点列表"""
        self.read_nodes.clear()
        self.write_nodes.clear()
        
        for node_id, node in self.nodes.items():
            if node.status == NodeStatus.ONLINE:
                if node.role in [NodeRole.MASTER]:
                    self.write_nodes.append(node_id)
                
                # 所有在线节点都可以读取
                self.read_nodes.append(node_id)
    
    def _weighted_random_choice(self, nodes: List[DatabaseNode]) -> DatabaseNode:
        """基于权重的随机选择"""
        if not nodes:
            return None
        
        if len(nodes) == 1:
            return nodes[0]
        
        total_weight = sum(node.weight for node in nodes)
        if total_weight == 0:
            return random.choice(nodes)
        
        rand_weight = random.uniform(0, total_weight)
        current_weight = 0
        
        for node in nodes:
            current_weight += node.weight
            if rand_weight <= current_weight:
                return node
        
        return nodes[-1]
    
    def _handle_node_error(self, node_id: str, error: Exception):
        """处理节点错误"""
        if node_id not in self.nodes:
            return
        
        node = self.nodes[node_id]
        node.error_count += 1
        
        self.logger.warning(f"节点错误: {node_id}, 错误次数: {node.error_count}, 错误: {error}")
        
        if node.error_count >= self.max_error_count:
            node.status = NodeStatus.DEGRADED
            self._update_hash_ring()
            self._update_node_lists()
            self.logger.error(f"节点降级: {node_id}")
    
    def get_cluster_stats(self) -> Dict[str, Any]:
        """获取集群统计信息"""
        with self._lock:
            stats = {
                'total_nodes': len(self.nodes),
                'online_nodes': len([n for n in self.nodes.values() if n.status == NodeStatus.ONLINE]),
                'master_nodes': len([n for n in self.nodes.values() if n.role == NodeRole.MASTER]),
                'replica_nodes': len([n for n in self.nodes.values() if n.role in [NodeRole.SLAVE, NodeRole.REPLICA]]),
                'hash_ring_size': len(self.hash_ring),
                'read_nodes': len(self.read_nodes),
                'write_nodes': len(self.write_nodes),
                'nodes': {}
            }
            
            for node_id, node in self.nodes.items():
                stats['nodes'][node_id] = {
                    'status': node.status.value,
                    'role': node.role.value,
                    'error_count': node.error_count,
                    'weight': node.weight,
                    'last_heartbeat': node.last_heartbeat
                }
            
            return stats
    
    def load_config(self, config_file: str):
        """加载配置文件"""
        try:
            # {{ AURA-X: Modify - 使用ConfigFactory统一配置管理，消除直接读取JSON. Approval: 寸止(ID:架构一致性修复). }}
            from src.utils.config.config_factory import config_factory
            import os

            # 解析配置文件路径
            if os.path.isabs(config_file):
                # 绝对路径，提取文件名和目录
                config_dir = os.path.dirname(config_file)
                config_name = os.path.splitext(os.path.basename(config_file))[0]
            else:
                # 相对路径，使用默认config目录
                config_dir = 'config'
                config_name = os.path.splitext(config_file)[0]

            # 使用ConfigFactory加载配置
            config = config_factory.load_config(config_name, config_dir)

            # 加载节点配置
            for node_config in config.get('nodes', []):
                self.add_node(
                    node_id=node_config['node_id'],
                    host=node_config['host'],
                    port=node_config['port'],
                    database=node_config['database'],
                    username=node_config['username'],
                    password=node_config['password'],
                    role=NodeRole(node_config['role']),
                    weight=node_config.get('weight', 1),
                    db_type=node_config.get('db_type', 'mysql')
                )

            self.logger.info(f"✅ 配置加载成功: {config_file}")

        except Exception as e:
            self.logger.error(f"加载配置失败: {config_file}, 错误: {e}")
    
    def execute_batch_write(self, operations: List[Dict[str, Any]]) -> List[Any]:
        """
        执行批量写入操作

        参数:
            operations: 操作列表，每个操作包含sql、params、shard_key

        返回:
            List[Any]: 执行结果列表
        """
        results = []

        # 按分片键分组操作
        grouped_ops = {}
        for op in operations:
            shard_key = op.get('shard_key', '')
            node = self.get_write_node(shard_key)
            if not node:
                raise Exception(f"没有可用的写入节点: shard_key={shard_key}")

            if node.node_id not in grouped_ops:
                grouped_ops[node.node_id] = []
            grouped_ops[node.node_id].append(op)

        # 并发执行各节点的操作
        for node_id, ops in grouped_ops.items():
            node = self.nodes[node_id]
            try:
                with self.pool_manager.get_connection(node.pool_name) as conn:
                    # 开启事务
                    trans = conn.begin()
                    try:
                        for op in ops:
                            sql = op['sql']
                            params = op.get('params')
                            if params:
                                result = conn.execute(text(sql), params)
                            else:
                                result = conn.execute(text(sql))
                            results.append(result)

                        trans.commit()
                    except Exception as e:
                        trans.rollback()
                        raise e

            except Exception as e:
                self._handle_node_error(node_id, e)
                raise

        return results

    def execute_distributed_query(self, sql: str, params: Union[tuple, dict] = None,
                                 merge_results: bool = True) -> Union[pd.DataFrame, List[pd.DataFrame]]:
        """
        执行分布式查询

        参数:
            sql: SQL语句
            params: 参数
            merge_results: 是否合并结果

        返回:
            Union[pd.DataFrame, List[pd.DataFrame]]: 查询结果
        """
        results = []

        # 在所有读节点上执行查询
        for node_id in self.read_nodes:
            node = self.nodes[node_id]
            try:
                with self.pool_manager.get_connection(node.pool_name) as conn:
                    if params:
                        df = pd.read_sql(sql, conn, params=params)
                    else:
                        df = pd.read_sql(sql, conn)

                    if not df.empty:
                        results.append(df)

            except Exception as e:
                self._handle_node_error(node_id, e)
                self.logger.warning(f"节点查询失败: {node_id}, 错误: {e}")
                continue

        if not results:
            return pd.DataFrame()

        if merge_results:
            # 合并所有结果
            return pd.concat(results, ignore_index=True)
        else:
            return results

    def start_health_check(self):
        """启动健康检查"""
        if self._health_check_task is None:
            self._health_check_task = asyncio.create_task(self._health_check_loop())
            self.logger.info("✅ 健康检查已启动")

    def stop_health_check(self):
        """停止健康检查"""
        if self._health_check_task:
            self._health_check_task.cancel()
            self._health_check_task = None
            self.logger.info("✅ 健康检查已停止")

    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._perform_health_check()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"健康检查异常: {e}")

    async def _perform_health_check(self):
        """执行健康检查"""
        with self._lock:
            for node_id, node in self.nodes.items():
                try:
                    # 简单的连接测试
                    with self.pool_manager.get_connection(node.pool_name) as conn:
                        conn.execute(text("SELECT 1"))

                    # 更新心跳时间
                    node.last_heartbeat = time.time()

                    # 如果节点之前是降级状态，现在恢复正常
                    if node.status == NodeStatus.DEGRADED:
                        node.status = NodeStatus.ONLINE
                        node.error_count = 0
                        self._update_hash_ring()
                        self._update_node_lists()
                        self.logger.info(f"节点恢复: {node_id}")

                except Exception as e:
                    self._handle_node_error(node_id, e)

    def close(self):
        """关闭分布式数据库管理器"""
        with self._lock:
            # 停止健康检查
            self.stop_health_check()

            # 关闭所有连接池
            for node in self.nodes.values():
                if node.pool_name:
                    self.pool_manager.close_pool(node.pool_name)

            self.nodes.clear()
            self.hash_ring.clear()
            self.read_nodes.clear()
            self.write_nodes.clear()

            self.logger.info("✅ 分布式数据库管理器已关闭")
