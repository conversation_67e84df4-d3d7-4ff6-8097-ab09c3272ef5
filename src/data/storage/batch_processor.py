#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量数据处理优化器
- 实现高性能批量插入和更新
- 支持事务管理和错误恢复
- 提供数据去重和冲突处理
"""

import time
import logging
import pandas as pd
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass
from contextlib import contextmanager
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

from src.utils.logging.logger_factory import get_logger
from src.data.storage.connection_pool_manager import get_pool_manager

@dataclass
class BatchConfig:
    """批量处理配置"""
    batch_size: int = 10000
    max_batch_size: int = 50000
    commit_interval: int = 5
    max_retries: int = 3
    retry_delay: float = 1.0
    enable_dedup: bool = True
    conflict_resolution: str = "ignore"  # ignore, update, error

@dataclass
class BatchStats:
    """批量处理统计"""
    total_records: int = 0
    processed_records: int = 0
    failed_records: int = 0
    duplicate_records: int = 0
    processing_time: float = 0.0
    throughput: float = 0.0

class BatchProcessor:
    """高性能批量数据处理器"""
    
    def __init__(
        self, 
        pool_name: str,
        config: Optional[BatchConfig] = None,
        logger: Optional[logging.Logger] = None
    ):
        """
        初始化批量处理器
        
        参数:
            pool_name: 连接池名称
            config: 批量处理配置
            logger: 日志记录器
        """
        self.pool_name = pool_name
        self.config = config or BatchConfig()
        self.logger = logger or get_logger(__name__)
        self.pool_manager = get_pool_manager()
        
        # 统计信息
        self.stats = BatchStats()
        self._stats_lock = threading.Lock()
        
        # 表特定配置
        self.table_configs = {
            'daily': BatchConfig(batch_size=20000, conflict_resolution="ignore"),
            'daily_basic': BatchConfig(batch_size=15000, conflict_resolution="ignore"),
            'income': BatchConfig(batch_size=5000, conflict_resolution="update"),
            'balancesheet': BatchConfig(batch_size=5000, conflict_resolution="update"),
            'cashflow': BatchConfig(batch_size=5000, conflict_resolution="update"),
        }
    
    def bulk_insert(
        self, 
        data: pd.DataFrame, 
        table_name: str,
        if_exists: str = "append",
        index: bool = False,
        method: str = "multi"
    ) -> BatchStats:
        """
        批量插入数据
        
        参数:
            data: 要插入的数据
            table_name: 目标表名
            if_exists: 表存在时的处理方式
            index: 是否包含索引
            method: 插入方法
            
        返回:
            BatchStats: 处理统计信息
        """
        if data.empty:
            return BatchStats()
        
        # 获取表特定配置
        table_config = self.table_configs.get(table_name, self.config)
        
        start_time = time.time()
        stats = BatchStats(total_records=len(data))
        
        try:
            # 数据预处理
            processed_data = self._preprocess_data(data, table_name)
            
            # 去重处理
            if table_config.enable_dedup:
                processed_data, dup_count = self._deduplicate_data(processed_data, table_name)
                stats.duplicate_records = dup_count
            
            # 分批处理
            batch_size = min(table_config.batch_size, len(processed_data))
            
            with self.pool_manager.get_connection(self.pool_name) as conn:
                # 开始事务
                trans = conn.begin()
                
                try:
                    processed_count = 0
                    
                    for i in range(0, len(processed_data), batch_size):
                        batch_data = processed_data.iloc[i:i + batch_size]
                        
                        # 执行批量插入
                        self._execute_batch_insert(
                            conn, batch_data, table_name, 
                            table_config, if_exists, index, method
                        )
                        
                        processed_count += len(batch_data)
                        
                        # 定期提交事务
                        if (i // batch_size + 1) % table_config.commit_interval == 0:
                            trans.commit()
                            trans = conn.begin()
                    
                    # 提交最后的事务
                    trans.commit()
                    stats.processed_records = processed_count
                    
                except Exception as e:
                    trans.rollback()
                    self.logger.error(f"批量插入失败，事务已回滚: {e}")
                    raise
        
        except Exception as e:
            stats.failed_records = stats.total_records - stats.processed_records
            self.logger.error(f"批量插入数据失败: {e}")
            raise
        
        finally:
            # 更新统计信息
            stats.processing_time = time.time() - start_time
            if stats.processing_time > 0:
                stats.throughput = stats.processed_records / stats.processing_time
            
            self._update_global_stats(stats)
        
        self.logger.info(
            f"✅ 批量插入完成: {table_name}, "
            f"处理 {stats.processed_records}/{stats.total_records} 条记录, "
            f"耗时 {stats.processing_time:.2f}s, "
            f"吞吐量 {stats.throughput:.0f} 条/秒"
        )
        
        return stats
    
    def bulk_upsert(
        self, 
        data: pd.DataFrame, 
        table_name: str,
        conflict_columns: List[str],
        update_columns: Optional[List[str]] = None
    ) -> BatchStats:
        """
        批量插入或更新数据（UPSERT）
        
        参数:
            data: 要处理的数据
            table_name: 目标表名
            conflict_columns: 冲突检测列
            update_columns: 更新列（None表示更新所有列）
            
        返回:
            BatchStats: 处理统计信息
        """
        if data.empty:
            return BatchStats()
        
        table_config = self.table_configs.get(table_name, self.config)
        start_time = time.time()
        stats = BatchStats(total_records=len(data))
        
        try:
            # 数据预处理
            processed_data = self._preprocess_data(data, table_name)
            
            # 分批处理
            batch_size = min(table_config.batch_size, len(processed_data))
            
            with self.pool_manager.get_connection(self.pool_name) as conn:
                trans = conn.begin()
                
                try:
                    processed_count = 0
                    
                    for i in range(0, len(processed_data), batch_size):
                        batch_data = processed_data.iloc[i:i + batch_size]
                        
                        # 执行批量UPSERT
                        self._execute_batch_upsert(
                            conn, batch_data, table_name, 
                            conflict_columns, update_columns
                        )
                        
                        processed_count += len(batch_data)
                        
                        # 定期提交事务
                        if (i // batch_size + 1) % table_config.commit_interval == 0:
                            trans.commit()
                            trans = conn.begin()
                    
                    # 提交最后的事务
                    trans.commit()
                    stats.processed_records = processed_count
                    
                except Exception as e:
                    trans.rollback()
                    self.logger.error(f"批量UPSERT失败，事务已回滚: {e}")
                    raise
        
        except Exception as e:
            stats.failed_records = stats.total_records - stats.processed_records
            self.logger.error(f"批量UPSERT数据失败: {e}")
            raise
        
        finally:
            # 更新统计信息
            stats.processing_time = time.time() - start_time
            if stats.processing_time > 0:
                stats.throughput = stats.processed_records / stats.processing_time
            
            self._update_global_stats(stats)
        
        self.logger.info(
            f"✅ 批量UPSERT完成: {table_name}, "
            f"处理 {stats.processed_records}/{stats.total_records} 条记录, "
            f"耗时 {stats.processing_time:.2f}s, "
            f"吞吐量 {stats.throughput:.0f} 条/秒"
        )
        
        return stats
    
    def _preprocess_data(self, data: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """
        数据预处理
        
        参数:
            data: 原始数据
            table_name: 表名
            
        返回:
            pd.DataFrame: 预处理后的数据
        """
        processed_data = data.copy()
        
        # 处理NaN值
        for col in processed_data.select_dtypes(include=['float']).columns:
            processed_data[col] = processed_data[col].fillna(0)
        
        # 处理日期时间列
        for col in processed_data.select_dtypes(include=['datetime64']).columns:
            processed_data[col] = processed_data[col].dt.strftime('%Y-%m-%d %H:%M:%S')
        
        # 处理字符串列
        for col in processed_data.select_dtypes(include=['object']).columns:
            processed_data[col] = processed_data[col].fillna('')
        
        return processed_data
    
    def _deduplicate_data(self, data: pd.DataFrame, table_name: str) -> tuple:
        """
        数据去重
        
        参数:
            data: 要去重的数据
            table_name: 表名
            
        返回:
            tuple: (去重后的数据, 重复记录数)
        """
        original_count = len(data)
        
        # 根据表类型确定去重列
        if table_name in ['daily', 'daily_basic']:
            dedup_columns = ['ts_code', 'trade_date']
        elif table_name in ['income', 'balancesheet', 'cashflow']:
            dedup_columns = ['ts_code', 'end_date']
        else:
            # 默认使用所有列去重
            dedup_columns = data.columns.tolist()
        
        # 执行去重
        deduplicated_data = data.drop_duplicates(subset=dedup_columns, keep='last')
        duplicate_count = original_count - len(deduplicated_data)
        
        if duplicate_count > 0:
            self.logger.debug(f"数据去重: {table_name}, 移除 {duplicate_count} 条重复记录")
        
        return deduplicated_data, duplicate_count
    
    def _execute_batch_insert(
        self, 
        conn, 
        data: pd.DataFrame, 
        table_name: str,
        config: BatchConfig,
        if_exists: str,
        index: bool,
        method: str
    ):
        """执行批量插入"""
        try:
            data.to_sql(
                table_name, 
                conn, 
                if_exists=if_exists, 
                index=index, 
                method=method
            )
        except Exception as e:
            if config.conflict_resolution == "ignore":
                # 尝试逐行插入，忽略冲突
                self._insert_with_conflict_ignore(conn, data, table_name)
            else:
                raise e
    
    def _execute_batch_upsert(
        self, 
        conn, 
        data: pd.DataFrame, 
        table_name: str,
        conflict_columns: List[str],
        update_columns: Optional[List[str]]
    ):
        """执行批量UPSERT"""
        # 这里需要根据数据库类型生成不同的UPSERT SQL
        # 简化实现，使用pandas的to_sql方法
        data.to_sql(table_name, conn, if_exists='append', index=False, method='multi')
    
    def _insert_with_conflict_ignore(self, conn, data: pd.DataFrame, table_name: str):
        """忽略冲突的插入"""
        for _, row in data.iterrows():
            try:
                row.to_frame().T.to_sql(table_name, conn, if_exists='append', index=False)
            except Exception:
                # 忽略插入错误（通常是重复键错误）
                pass
    
    def _update_global_stats(self, batch_stats: BatchStats):
        """更新全局统计信息"""
        with self._stats_lock:
            self.stats.total_records += batch_stats.total_records
            self.stats.processed_records += batch_stats.processed_records
            self.stats.failed_records += batch_stats.failed_records
            self.stats.duplicate_records += batch_stats.duplicate_records
            self.stats.processing_time += batch_stats.processing_time
            
            if self.stats.processing_time > 0:
                self.stats.throughput = self.stats.processed_records / self.stats.processing_time
    
    def get_stats(self) -> BatchStats:
        """获取处理统计信息"""
        with self._stats_lock:
            return BatchStats(
                total_records=self.stats.total_records,
                processed_records=self.stats.processed_records,
                failed_records=self.stats.failed_records,
                duplicate_records=self.stats.duplicate_records,
                processing_time=self.stats.processing_time,
                throughput=self.stats.throughput
            )
    
    def reset_stats(self):
        """重置统计信息"""
        with self._stats_lock:
            self.stats = BatchStats()

    def close(self):
        """关闭批量处理器"""
        # 清理资源
        self.reset_stats()
        self.logger.debug(f"批量处理器已关闭: {self.pool_name}")
