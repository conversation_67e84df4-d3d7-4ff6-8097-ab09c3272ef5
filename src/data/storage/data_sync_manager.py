#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据同步管理器
- 实现主从节点间的数据同步
- 支持增量同步和全量同步
- 提供数据一致性检查和修复
"""

import time
import threading
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import pandas as pd
from sqlalchemy import text

from src.utils.logging.logger_factory import get_logger
from src.data.storage.distributed_db_manager import DistributedDatabaseManager, NodeRole

class SyncType(Enum):
    """同步类型"""
    FULL = "full"           # 全量同步
    INCREMENTAL = "incremental"  # 增量同步
    REPAIR = "repair"       # 修复同步

class SyncStatus(Enum):
    """同步状态"""
    PENDING = "pending"     # 等待中
    RUNNING = "running"     # 运行中
    SUCCESS = "success"     # 成功
    FAILED = "failed"       # 失败
    CANCELLED = "cancelled" # 已取消

@dataclass
class SyncTask:
    """同步任务"""
    task_id: str
    sync_type: SyncType
    source_node: str
    target_nodes: List[str]
    table_name: str
    status: SyncStatus
    created_at: float
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    error_message: Optional[str] = None
    records_synced: int = 0
    
@dataclass
class SyncStats:
    """同步统计"""
    total_tasks: int = 0
    pending_tasks: int = 0
    running_tasks: int = 0
    success_tasks: int = 0
    failed_tasks: int = 0
    total_records_synced: int = 0
    avg_sync_time: float = 0.0

class DataSyncManager:
    """数据同步管理器"""
    
    def __init__(self, db_manager: DistributedDatabaseManager):
        self.db_manager = db_manager
        self.logger = get_logger(__name__)
        
        # 同步任务管理
        self.sync_tasks: Dict[str, SyncTask] = {}
        self.sync_queue: List[str] = []
        self.sync_lock = threading.Lock()
        
        # 同步配置
        self.sync_interval = 30  # 自动同步间隔(秒)
        self.max_concurrent_syncs = 3  # 最大并发同步数
        self.sync_batch_size = 1000  # 同步批次大小
        
        # 同步线程
        self.sync_thread = None
        self.sync_running = False
        
        self.logger.info("✅ 数据同步管理器初始化完成")
    
    def start_auto_sync(self):
        """启动自动同步"""
        if self.sync_running:
            self.logger.warning("自动同步已在运行")
            return
        
        self.sync_running = True
        self.sync_thread = threading.Thread(target=self._auto_sync_worker, daemon=True)
        self.sync_thread.start()
        self.logger.info("✅ 自动同步已启动")
    
    def stop_auto_sync(self):
        """停止自动同步"""
        self.sync_running = False
        if self.sync_thread and self.sync_thread.is_alive():
            self.sync_thread.join(timeout=5)
        self.logger.info("✅ 自动同步已停止")
    
    def _auto_sync_worker(self):
        """自动同步工作线程"""
        while self.sync_running:
            try:
                # 检查是否需要同步
                self._check_and_sync()
                
                # 等待下次检查
                for _ in range(self.sync_interval):
                    if not self.sync_running:
                        break
                    time.sleep(1)
                    
            except Exception as e:
                self.logger.error(f"自动同步工作线程错误: {e}")
                time.sleep(5)
    
    def _check_and_sync(self):
        """检查并执行同步"""
        try:
            # 获取主节点
            master_node = self.db_manager.get_write_node()
            if not master_node:
                return
            
            # 获取所有副本节点
            replica_nodes = [
                node for node in self.db_manager.nodes.values()
                if node.role == NodeRole.REPLICA
            ]
            
            if not replica_nodes:
                return
            
            # 检查需要同步的表
            tables_to_sync = self._get_tables_to_sync(master_node.node_id)
            
            for table_name in tables_to_sync:
                # 为每个表创建同步任务
                target_node_ids = [node.node_id for node in replica_nodes]
                self.create_sync_task(
                    sync_type=SyncType.INCREMENTAL,
                    source_node=master_node.node_id,
                    target_nodes=target_node_ids,
                    table_name=table_name
                )
            
        except Exception as e:
            self.logger.error(f"检查同步失败: {e}")
    
    def _get_tables_to_sync(self, source_node: str) -> List[str]:
        """获取需要同步的表列表"""
        try:
            node = self.db_manager.nodes.get(source_node)
            if not node:
                return []
            
            with self.db_manager.pool_manager.get_connection(node.pool_name) as conn:
                if 'mysql' in source_node:
                    # MySQL查询表列表
                    result = conn.execute(text("SHOW TABLES")).fetchall()
                    return [row[0] for row in result if row[0] in ['daily', 'daily_basic', 'weekly', 'monthly']]
                else:
                    # SQLite查询表列表
                    result = conn.execute(text(
                        "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
                    )).fetchall()
                    return [row[0] for row in result if row[0] in ['daily', 'daily_basic', 'weekly', 'monthly']]
                    
        except Exception as e:
            self.logger.error(f"获取表列表失败: {e}")
            return []
    
    def create_sync_task(
        self,
        sync_type: SyncType,
        source_node: str,
        target_nodes: List[str],
        table_name: str
    ) -> str:
        """创建同步任务"""
        task_id = f"sync_{int(time.time() * 1000)}_{table_name}"
        
        task = SyncTask(
            task_id=task_id,
            sync_type=sync_type,
            source_node=source_node,
            target_nodes=target_nodes,
            table_name=table_name,
            status=SyncStatus.PENDING,
            created_at=time.time()
        )
        
        with self.sync_lock:
            self.sync_tasks[task_id] = task
            self.sync_queue.append(task_id)
        
        self.logger.info(f"创建同步任务: {task_id} ({sync_type.value})")
        
        # 立即执行同步
        self._execute_sync_task(task_id)
        
        return task_id
    
    def _execute_sync_task(self, task_id: str):
        """执行同步任务"""
        with self.sync_lock:
            task = self.sync_tasks.get(task_id)
            if not task or task.status != SyncStatus.PENDING:
                return
            
            task.status = SyncStatus.RUNNING
            task.started_at = time.time()
        
        try:
            self.logger.info(f"开始执行同步任务: {task_id}")
            
            # 根据同步类型执行不同的同步策略
            if task.sync_type == SyncType.FULL:
                records_synced = self._full_sync(task)
            elif task.sync_type == SyncType.INCREMENTAL:
                records_synced = self._incremental_sync(task)
            elif task.sync_type == SyncType.REPAIR:
                records_synced = self._repair_sync(task)
            else:
                raise ValueError(f"不支持的同步类型: {task.sync_type}")
            
            # 更新任务状态
            with self.sync_lock:
                task.status = SyncStatus.SUCCESS
                task.completed_at = time.time()
                task.records_synced = records_synced
            
            self.logger.info(f"同步任务完成: {task_id}, 同步记录数: {records_synced}")
            
        except Exception as e:
            # 更新任务状态为失败
            with self.sync_lock:
                task.status = SyncStatus.FAILED
                task.completed_at = time.time()
                task.error_message = str(e)
            
            self.logger.error(f"同步任务失败: {task_id}, 错误: {e}")
    
    def _full_sync(self, task: SyncTask) -> int:
        """全量同步"""
        source_node = self.db_manager.nodes.get(task.source_node)
        if not source_node:
            raise Exception(f"源节点不存在: {task.source_node}")
        
        total_synced = 0
        
        # 获取源数据
        with self.db_manager.pool_manager.get_connection(source_node.pool_name) as source_conn:
            # 查询总记录数
            count_result = source_conn.execute(text(f"SELECT COUNT(*) FROM {task.table_name}")).fetchone()
            total_records = count_result[0] if count_result else 0
            
            if total_records == 0:
                return 0
            
            # 分批同步数据
            offset = 0
            while offset < total_records:
                # 获取一批数据
                data_result = source_conn.execute(text(
                    f"SELECT * FROM {task.table_name} LIMIT {self.sync_batch_size} OFFSET {offset}"
                )).fetchall()
                
                if not data_result:
                    break
                
                # 获取列名
                columns = list(data_result[0].keys()) if hasattr(data_result[0], 'keys') else []
                
                # 转换为DataFrame
                if hasattr(data_result[0], '_asdict'):
                    # 如果是命名元组
                    data_df = pd.DataFrame([row._asdict() for row in data_result])
                elif hasattr(data_result[0], 'keys'):
                    # 如果是字典类型的行
                    data_df = pd.DataFrame([dict(row) for row in data_result])
                else:
                    # 普通元组，需要获取列名
                    columns = list(data_result[0].keys()) if hasattr(data_result[0], 'keys') else []
                    data_df = pd.DataFrame([list(row) for row in data_result], columns=columns)
                
                # 同步到目标节点
                for target_node_id in task.target_nodes:
                    self._sync_data_to_node(target_node_id, task.table_name, data_df, replace=True)
                
                total_synced += len(data_result)
                offset += self.sync_batch_size
        
        return total_synced
    
    def _incremental_sync(self, task: SyncTask) -> int:
        """增量同步"""
        # 简化的增量同步：同步最近的数据
        source_node = self.db_manager.nodes.get(task.source_node)
        if not source_node:
            raise Exception(f"源节点不存在: {task.source_node}")
        
        total_synced = 0
        
        # 获取最近的数据（简化实现）
        with self.db_manager.pool_manager.get_connection(source_node.pool_name) as source_conn:
            # 查询最近的数据
            try:
                data_result = source_conn.execute(text(
                    f"SELECT * FROM {task.table_name} ORDER BY id DESC LIMIT {self.sync_batch_size}"
                )).fetchall()
            except Exception as e:
                # 如果按id排序失败，尝试不排序
                self.logger.warning(f"按id排序查询失败，尝试简单查询: {e}")
                data_result = source_conn.execute(text(
                    f"SELECT * FROM {task.table_name} LIMIT {self.sync_batch_size}"
                )).fetchall()
            
            if not data_result:
                return 0
            
            # 转换为DataFrame
            if hasattr(data_result[0], '_asdict'):
                # 如果是命名元组
                data_df = pd.DataFrame([row._asdict() for row in data_result])
            elif hasattr(data_result[0], 'keys'):
                # 如果是字典类型的行
                data_df = pd.DataFrame([dict(row) for row in data_result])
            else:
                # 普通元组，需要获取列名
                columns = list(data_result[0].keys()) if hasattr(data_result[0], 'keys') else []
                data_df = pd.DataFrame([list(row) for row in data_result], columns=columns)
            
            # 同步到目标节点
            for target_node_id in task.target_nodes:
                synced = self._sync_data_to_node(target_node_id, task.table_name, data_df, replace=False)
                total_synced += synced
        
        return total_synced
    
    def _repair_sync(self, task: SyncTask) -> int:
        """修复同步"""
        # 修复同步：检查数据一致性并修复差异
        return self._full_sync(task)  # 简化实现，使用全量同步
    
    def _sync_data_to_node(self, target_node_id: str, table_name: str, data_df: pd.DataFrame, replace: bool = False) -> int:
        """将数据同步到目标节点"""
        target_node = self.db_manager.nodes.get(target_node_id)
        if not target_node:
            self.logger.warning(f"目标节点不存在: {target_node_id}")
            return 0
        
        try:
            with self.db_manager.pool_manager.get_connection(target_node.pool_name) as target_conn:
                # 如果是替换模式，先清空表
                if replace:
                    target_conn.execute(text(f"DELETE FROM {table_name}"))
                
                # 插入数据 - 使用pandas的to_sql方法更可靠
                try:
                    # 使用pandas的to_sql方法进行批量插入
                    data_df.to_sql(
                        table_name,
                        target_conn,
                        if_exists='append',
                        index=False,
                        method='multi'
                    )
                except Exception as e:
                    # 如果批量插入失败，尝试逐行插入
                    self.logger.warning(f"批量插入失败，尝试逐行插入: {e}")
                    for _, row in data_df.iterrows():
                        try:
                            if 'mysql' in target_node_id:
                                # MySQL使用REPLACE INTO
                                columns = ', '.join([f"`{col}`" for col in row.index])
                                placeholders = ', '.join([f":{col}" for col in row.index])
                                sql = f"REPLACE INTO {table_name} ({columns}) VALUES ({placeholders})"
                                target_conn.execute(text(sql), row.to_dict())
                            else:
                                # SQLite使用INSERT OR REPLACE
                                columns = ', '.join([f"`{col}`" for col in row.index])
                                placeholders = ', '.join([f":{col}" for col in row.index])
                                sql = f"INSERT OR REPLACE INTO {table_name} ({columns}) VALUES ({placeholders})"
                                target_conn.execute(text(sql), row.to_dict())
                        except Exception as row_error:
                            self.logger.warning(f"跳过问题行: {row_error}")
                            continue
                
                target_conn.commit()
                return len(data_df)
                
        except Exception as e:
            self.logger.error(f"同步数据到节点失败: {target_node_id}, 错误: {e}")
            return 0
    
    def get_sync_stats(self) -> SyncStats:
        """获取同步统计"""
        with self.sync_lock:
            stats = SyncStats()
            stats.total_tasks = len(self.sync_tasks)
            
            sync_times = []
            
            for task in self.sync_tasks.values():
                if task.status == SyncStatus.PENDING:
                    stats.pending_tasks += 1
                elif task.status == SyncStatus.RUNNING:
                    stats.running_tasks += 1
                elif task.status == SyncStatus.SUCCESS:
                    stats.success_tasks += 1
                    stats.total_records_synced += task.records_synced
                    if task.started_at and task.completed_at:
                        sync_times.append(task.completed_at - task.started_at)
                elif task.status == SyncStatus.FAILED:
                    stats.failed_tasks += 1
            
            if sync_times:
                stats.avg_sync_time = sum(sync_times) / len(sync_times)
            
            return stats
    
    def get_task_status(self, task_id: str) -> Optional[SyncTask]:
        """获取任务状态"""
        with self.sync_lock:
            return self.sync_tasks.get(task_id)
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self.sync_lock:
            task = self.sync_tasks.get(task_id)
            if task and task.status == SyncStatus.PENDING:
                task.status = SyncStatus.CANCELLED
                if task_id in self.sync_queue:
                    self.sync_queue.remove(task_id)
                return True
            return False
    
    def check_data_consistency(self, table_name: str) -> Dict[str, Any]:
        """检查数据一致性"""
        consistency_report = {
            'table_name': table_name,
            'check_time': time.time(),
            'nodes': {},
            'is_consistent': True,
            'inconsistencies': []
        }
        
        try:
            # 检查所有节点的记录数
            for node_id, node in self.db_manager.nodes.items():
                
                try:
                    with self.db_manager.pool_manager.get_connection(node.pool_name) as conn:
                        result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}")).fetchone()
                        count = result[0] if result else 0
                        consistency_report['nodes'][node_id] = {
                            'record_count': count,
                            'role': node.role.value,
                            'status': 'online'
                        }
                except Exception as e:
                    consistency_report['nodes'][node_id] = {
                        'record_count': -1,
                        'role': node.role.value,
                        'status': 'error',
                        'error': str(e)
                    }
            
            # 检查一致性
            record_counts = [
                info['record_count'] for info in consistency_report['nodes'].values()
                if info['record_count'] >= 0
            ]
            
            if len(set(record_counts)) > 1:
                consistency_report['is_consistent'] = False
                consistency_report['inconsistencies'].append(
                    f"节点间记录数不一致: {dict(zip(consistency_report['nodes'].keys(), record_counts))}"
                )
            
        except Exception as e:
            self.logger.error(f"检查数据一致性失败: {e}")
            consistency_report['error'] = str(e)
        
        return consistency_report
    
    def force_sync_table(self, table_name: str, sync_type: SyncType = SyncType.FULL) -> str:
        """强制同步指定表"""
        master_node = self.db_manager.get_write_node()
        if not master_node:
            raise Exception("没有可用的主节点")
        
        replica_nodes = [
            node.node_id for node in self.db_manager.nodes.values()
            if node.role == NodeRole.REPLICA
        ]
        
        if not replica_nodes:
            raise Exception("没有可用的副本节点")
        
        return self.create_sync_task(
            sync_type=sync_type,
            source_node=master_node.node_id,
            target_nodes=replica_nodes,
            table_name=table_name
        )
    
    def close(self):
        """关闭同步管理器"""
        self.stop_auto_sync()
        self.logger.info("✅ 数据同步管理器已关闭")
