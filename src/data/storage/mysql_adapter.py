"""
MySQL存储适配器：用于MySQL数据库的数据存储和管理
- 实现标准存储接口
- 支持事务处理和并发控制
- 提供数据备份和恢复功能
- 支持表结构自动创建和管理
"""

import os
import pymysql
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union, Callable, Tuple
import logging
from datetime import datetime
import json
from contextlib import contextmanager

from .storage_interface import StorageInterface, ConnectionError, QueryError, TransactionError, DataError

class MySQLAdapter(StorageInterface):
    """
    MySQL存储适配器类，实现与MySQL数据库的交互
    """
    
    def __init__(self, host: str = None, port: int = 3306, user: str = None, 
                 password: str = None, database: str = None, charset: str = 'utf8mb4',
                 config_file: str = 'database', env: str = None,
                 auto_create_tables: bool = True):
        """
        初始化MySQL适配器
        
        参数：
            host: 数据库主机地址
            port: 数据库端口
            user: 用户名
            password: 密码
            database: 数据库名
            charset: 字符集
            config_file: 配置文件名
            env: 环境名称
            auto_create_tables: 是否自动创建表
        """
        from src.utils.config.config_factory import ConfigFactory
        
        self.logger = logging.getLogger(__name__)
        self.connected = False
        self.conn = None
        self.cursor = None
        self.auto_create_tables = auto_create_tables
        self._table_schemas = {}
        self.in_transaction = False  # 添加事务标志
        
        # 获取项目根目录作为配置目录 - 修复配置目录路径
        config_dir = os.path.abspath(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'config'))
        
        # 从配置文件加载配置
        if config_file:
            config_factory = ConfigFactory()
            try:
                config = config_factory.load_config(config_file, config_dir)
                mysql_config = config.get('mysql', {})
                self.host = host or mysql_config.get('host', 'localhost')
                self.port = port or mysql_config.get('port', 3306)
                self.user = user or mysql_config.get('user')
                self.password = password or mysql_config.get('password')
                self.database = database or mysql_config.get('database')
                self.charset = charset or mysql_config.get('charset', 'utf8mb4')
            except FileNotFoundError:
                self.logger.warning(f"找不到配置文件: {config_file}.yaml，使用提供的参数")
                self.host = host or 'localhost'
                self.port = port or 3306
                self.user = user
                self.password = password
                self.database = database
                self.charset = charset or 'utf8mb4'
        else:
            self.host = host or 'localhost'
            self.port = port
            self.user = user
            self.password = password
            self.database = database
            self.charset = charset
            
        # 连接池配置 - 针对量化投资平台优化
        self.pool_config = {
            'max_overflow': 20,      # 最大溢出连接数
            'pool_size': 10,         # 核心连接池大小
            'timeout': 30,           # 连接超时
            'recycle': 3600,         # 连接回收时间（1小时）
            'max_idle': 300,         # 最大空闲时间（5分钟）
            'ping_interval': 60      # 连接保活间隔
        }
        
    def connect(self, **kwargs) -> bool:
        """
        连接到MySQL数据库
        
        参数：
            **kwargs: 连接参数
            
        返回：
            bool: 是否连接成功
            
        异常：
            ConnectionError: 连接失败时抛出
        """
        try:
            if self.connected and self.conn:
                return True
                
            # 更新连接参数
            if kwargs:
                self.host = kwargs.get('host', self.host)
                self.port = kwargs.get('port', self.port)
                self.user = kwargs.get('user', self.user)
                self.password = kwargs.get('password', self.password)
                self.database = kwargs.get('database', self.database)
                self.charset = kwargs.get('charset', self.charset)
            
            # 连接数据库
            self.conn = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                charset=self.charset,
                cursorclass=pymysql.cursors.DictCursor
            )
            
            self.cursor = self.conn.cursor()
            self.connected = True
            self.logger.info(f"已连接到MySQL数据库: {self.host}:{self.port}/{self.database}")
            return True
            
        except Exception as e:
            self.connected = False
            self.logger.error(f"连接MySQL数据库失败: {str(e)}")
            raise ConnectionError(f"连接MySQL数据库失败: {str(e)}")
            
    def disconnect(self) -> bool:
        """
        断开与MySQL数据库的连接
        
        返回：
            bool: 是否成功断开连接
            
        异常：
            ConnectionError: 断开连接失败时抛出
        """
        try:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()
            self.connected = False
            self.logger.info("已断开MySQL数据库连接")
            return True
        except Exception as e:
            self.logger.error(f"断开MySQL数据库连接失败: {str(e)}")
            raise ConnectionError(f"断开MySQL数据库连接失败: {str(e)}")
            
    def is_connected(self) -> bool:
        """
        检查数据库连接状态
        
        返回：
            bool: 是否已连接
        """
        if not self.connected or not self.conn:
            return False
            
        try:
            self.cursor.execute("SELECT 1")
            return True
        except:
            self.connected = False
            return False
            
    def save(self, table: str, data: Union[pd.DataFrame, List[Dict[str, Any]], Dict[str, Any]], 
             if_exists: str = 'append', index: bool = False, **kwargs) -> int:
        """
        保存数据到指定表
        
        参数：
            table: 表名
            data: 要保存的数据
            if_exists: 如果表存在时的处理方式 ('fail', 'replace', 'append')
            index: 是否保存DataFrame的索引
            **kwargs: 额外参数
            
        返回：
            int: 保存的记录数
            
        异常：
            DataError: 数据保存失败时抛出
        """
        if not self.is_connected():
            raise ConnectionError("未连接到数据库")
            
        try:
            # 转换数据为DataFrame
            if isinstance(data, dict):
                df = pd.DataFrame([data])
            elif isinstance(data, list):
                df = pd.DataFrame(data)
            elif isinstance(data, pd.DataFrame):
                df = data.copy()
            else:
                raise DataError(f"不支持的数据类型: {type(data)}")
                
            # 处理表是否存在
            table_exists = self.table_exists(table)
            if table_exists and if_exists == 'fail':
                raise DataError(f"表 {table} 已存在")
            elif table_exists and if_exists == 'replace':
                self.drop_table(table)
                table_exists = False
                
            # 如果表不存在且允许自动创建
            if not table_exists and self.auto_create_tables:
                schema = self._infer_schema(df)
                self.create_table(table, schema)
                
            # 准备SQL语句
            columns = df.columns.tolist()
            placeholders = ', '.join(['%s'] * len(columns))
            column_str = ', '.join([self._sanitize_name(col) for col in columns])
            sql = f"INSERT INTO {self._sanitize_name(table)} ({column_str}) VALUES ({placeholders})"
            
            # 执行插入
            values = [tuple(row) for row in df.values]
            self.cursor.executemany(sql, values)
            self.conn.commit()
            
            return len(df)
            
        except Exception as e:
            self.conn.rollback()
            self.logger.error(f"保存数据失败: {str(e)}")
            raise DataError(f"保存数据失败: {str(e)}")
            
    def query(self, table: str, conditions: Dict[str, Any] = None, 
              fields: List[str] = None, order_by: str = None, 
              limit: int = None, offset: int = None,
              as_dict: bool = False, **kwargs) -> Union[pd.DataFrame, List[Dict[str, Any]]]:
        """
        查询数据
        
        参数：
            table: 表名
            conditions: 查询条件
            fields: 要查询的字段列表
            order_by: 排序字段
            limit: 返回记录数限制
            offset: 起始位置偏移
            as_dict: 是否返回字典列表
            **kwargs: 额外参数
            
        返回：
            Union[pd.DataFrame, List[Dict[str, Any]]]: 查询结果
            
        异常：
            QueryError: 查询失败时抛出
        """
        if not self.is_connected():
            raise ConnectionError("未连接到数据库")
            
        try:
            # 构建SQL语句
            fields_str = '*' if not fields else ', '.join([self._sanitize_name(f) for f in fields])
            sql = f"SELECT {fields_str} FROM {self._sanitize_name(table)}"
            
            # 添加查询条件
            params = []
            if conditions:
                where_clauses = []
                for key, value in conditions.items():
                    if isinstance(value, (list, tuple)):
                        placeholders = ', '.join(['%s'] * len(value))
                        where_clauses.append(f"{self._sanitize_name(key)} IN ({placeholders})")
                        params.extend(value)
                    else:
                        where_clauses.append(f"{self._sanitize_name(key)} = %s")
                        params.append(value)
                sql += " WHERE " + " AND ".join(where_clauses)
                
            # 添加排序
            if order_by:
                sql += f" ORDER BY {order_by}"
                
            # 添加分页
            if limit:
                sql += f" LIMIT {limit}"
                if offset:
                    sql += f" OFFSET {offset}"
                    
            # 执行查询
            self.cursor.execute(sql, params)
            results = self.cursor.fetchall()
            
            if as_dict:
                return results
            else:
                # 获取列名
                columns = self._get_column_names(self.cursor)
                
                # 创建DataFrame
                if results:
                    df = pd.DataFrame(results, columns=columns)
                    return df
                else:
                    # 如果没有结果，创建一个有正确列名的空DataFrame
                    if columns:
                        return pd.DataFrame(columns=columns)
                    elif fields:
                        # 如果提供了字段列表，使用它们作为列名
                        return pd.DataFrame(columns=fields)
                    else:
                        # 获取表的所有字段
                        try:
                            self.cursor.execute(f"DESCRIBE {self._sanitize_name(table)}")
                            table_columns = [row[0] for row in self.cursor.fetchall()]
                            return pd.DataFrame(columns=table_columns)
                        except Exception:
                            # 如果无法获取列名，返回空DataFrame
                            return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"查询数据失败: {str(e)}")
            raise QueryError(f"查询数据失败: {str(e)}")
            
    def update(self, table: str, data: Union[Dict[str, Any], pd.Series], 
               conditions: Dict[str, Any] = None) -> int:
        """
        更新数据
        
        参数：
            table: 表名
            data: 要更新的数据
            conditions: 更新条件
            
        返回：
            int: 更新的记录数
            
        异常：
            DataError: 更新失败时抛出
        """
        if not self.is_connected():
            raise ConnectionError("未连接到数据库")
            
        try:
            # 准备更新数据
            if isinstance(data, pd.Series):
                data = data.to_dict()
                
            # 构建SQL语句
            set_clauses = [f"{self._sanitize_name(k)} = %s" for k in data.keys()]
            sql = f"UPDATE {self._sanitize_name(table)} SET {', '.join(set_clauses)}"
            params = list(data.values())
            
            # 添加条件
            if conditions:
                where_clauses = [f"{self._sanitize_name(k)} = %s" for k in conditions.keys()]
                sql += f" WHERE {' AND '.join(where_clauses)}"
                params.extend(conditions.values())
                
            # 执行更新
            self.cursor.execute(sql, params)
            affected_rows = self.cursor.rowcount
            self.conn.commit()
            
            return affected_rows
            
        except Exception as e:
            self.conn.rollback()
            self.logger.error(f"更新数据失败: {str(e)}")
            raise DataError(f"更新数据失败: {str(e)}")
            
    def delete(self, table: str, conditions: Dict[str, Any] = None) -> int:
        """
        从表中删除数据
        
        参数:
            table: 表名
            conditions: 条件字典，如 {"id": 1} 或 {"id": [1, 2, 3]}
            
        返回:
            int: 删除的行数
            
        异常:
            ConnectionError: 未连接时抛出
            QueryError: 删除失败时抛出
        """
        if not self.is_connected():
            raise ConnectionError("未连接到数据库")
            
        try:
            # 构建SQL语句
            sql_parts = [f"DELETE FROM {self._sanitize_name(table)}"]
            
            # 处理条件
            params = []
            if conditions:
                where_clause, where_params = self._build_where_clause(conditions)
                sql_parts.append(f"WHERE {where_clause}")
                params.extend(where_params)
            
            # 执行查询
            sql = " ".join(sql_parts)
            self.cursor.execute(sql, tuple(params))
            
            # 提交更改
            if not self.in_transaction:
                self.conn.commit()
                
            # 返回影响的行数
            return self.cursor.rowcount
        except Exception as e:
            if not self.in_transaction:
                self.conn.rollback()
            self.logger.error(f"删除数据失败: {str(e)}")
            raise QueryError(f"删除数据失败: {str(e)}")
            
    def execute_query(self, sql: str, params: tuple = None) -> Union[pd.DataFrame, List[Dict[str, Any]]]:
        """
        执行自定义SQL查询
        
        参数:
            sql: SQL查询语句
            params: 查询参数
            
        返回:
            Union[pd.DataFrame, List[Dict[str, Any]]]: 查询结果
            
        异常:
            ConnectionError: 未连接时抛出
            QueryError: 查询失败时抛出
        """
        if not self.is_connected():
            raise ConnectionError("未连接到数据库")
            
        try:
            # 执行查询
            self.cursor.execute(sql, params)
            
            # 检查是否是SELECT查询
            if sql.strip().upper().startswith("SELECT"):
                # 获取结果
                results = self.cursor.fetchall()
                
                # 获取列名
                columns = self._get_column_names(self.cursor)
                
                # 返回DataFrame
                if results:
                    df = pd.DataFrame(results, columns=columns)
                    return df
                elif columns:
                    # 如果没有结果但有字段描述，返回空DataFrame
                    return pd.DataFrame(columns=columns)
                else:
                    # 非SELECT查询或无字段描述
                    return pd.DataFrame()
            else:
                # 非SELECT查询，返回影响的行数
                rows_affected = self.cursor.rowcount
                return pd.DataFrame([{'affected_rows': rows_affected}])
                
        except Exception as e:
            self.logger.error(f"执行查询失败: {str(e)}")
            raise QueryError(f"执行查询失败: {str(e)}")
            
    def begin_transaction(self) -> bool:
        """
        开始事务
        
        返回：
            bool: 是否成功开始事务
            
        异常：
            TransactionError: 开始事务失败时抛出
        """
        if not self.is_connected():
            raise ConnectionError("未连接到数据库")
            
        try:
            if self.in_transaction:
                self.logger.warning("已经在事务中，忽略")
                return True
                
            self.conn.begin()
            self.in_transaction = True
            return True
        except Exception as e:
            self.logger.error(f"开始事务失败: {str(e)}")
            raise TransactionError(f"开始事务失败: {str(e)}")
            
    def commit_transaction(self) -> bool:
        """
        提交事务
        
        返回：
            bool: 是否成功提交事务
            
        异常：
            TransactionError: 提交事务失败时抛出
        """
        if not self.is_connected():
            raise ConnectionError("未连接到数据库")
            
        try:
            if not self.in_transaction:
                self.logger.warning("没有活动的事务，忽略")
                return True
                
            self.conn.commit()
            self.in_transaction = False
            return True
        except Exception as e:
            self.logger.error(f"提交事务失败: {str(e)}")
            raise TransactionError(f"提交事务失败: {str(e)}")
            
    def rollback_transaction(self) -> bool:
        """
        回滚事务
        
        返回：
            bool: 是否成功回滚事务
            
        异常：
            TransactionError: 回滚事务失败时抛出
        """
        if not self.is_connected():
            raise ConnectionError("未连接到数据库")
            
        try:
            if not self.in_transaction:
                self.logger.warning("没有活动的事务，忽略")
                return True
                
            self.conn.rollback()
            self.in_transaction = False
            return True
        except Exception as e:
            self.logger.error(f"回滚事务失败: {str(e)}")
            raise TransactionError(f"回滚事务失败: {str(e)}")
            
    @contextmanager
    def transaction(self):
        """
        事务上下文管理器
        
        用法：
            with adapter.transaction():
                adapter.save(...)
                adapter.update(...)
        """
        try:
            self.begin_transaction()
            yield
            self.commit_transaction()
        except Exception as e:
            self.rollback_transaction()
            raise
            
    def table_exists(self, table: str) -> bool:
        """
        检查表是否存在
        
        参数：
            table: 表名
            
        返回：
            bool: 表是否存在
        """
        if not self.is_connected():
            raise ConnectionError("未连接到数据库")
            
        try:
            self.cursor.execute(
                "SELECT 1 FROM information_schema.tables WHERE table_schema = %s AND table_name = %s",
                (self.database, table)
            )
            result = self.cursor.fetchone()
            return result is not None
        except Exception as e:
            self.logger.error(f"检查表是否存在失败: {str(e)}")
            return False
            
    def create_table(self, table: str, schema: Dict[str, str], if_not_exists: bool = True) -> bool:
        """
        创建表
        
        参数：
            table: 表名
            schema: 表结构定义
            if_not_exists: 是否在表不存在时创建
            
        返回：
            bool: 是否成功创建表
            
        异常：
            DataError: 创建表失败时抛出
        """
        if not self.is_connected():
            raise ConnectionError("未连接到数据库")
            
        try:
            # 构建创建表的SQL语句
            columns = []
            for column, dtype in schema.items():
                columns.append(f"{self._sanitize_name(column)} {dtype}")
                
            sql = f"""
                CREATE TABLE {'IF NOT EXISTS ' if if_not_exists else ''}{self._sanitize_name(table)} (
                    {', '.join(columns)}
                )
            """
            
            self.cursor.execute(sql)
            self.conn.commit()
            
            # 缓存表结构
            self._table_schemas[table] = schema
            
            return True
            
        except Exception as e:
            self.conn.rollback()
            self.logger.error(f"创建表失败: {str(e)}")
            raise DataError(f"创建表失败: {str(e)}")
            
    def drop_table(self, table: str, if_exists: bool = True) -> bool:
        """
        删除表
        
        参数：
            table: 表名
            if_exists: 是否在表存在时删除
            
        返回：
            bool: 是否成功删除表
            
        异常：
            DataError: 删除表失败时抛出
        """
        if not self.is_connected():
            raise ConnectionError("未连接到数据库")
            
        try:
            sql = f"DROP TABLE {'IF EXISTS ' if if_exists else ''}{self._sanitize_name(table)}"
            self.cursor.execute(sql)
            self.conn.commit()
            
            # 清除表结构缓存
            self._table_schemas.pop(table, None)
            
            return True
            
        except Exception as e:
            self.conn.rollback()
            self.logger.error(f"删除表失败: {str(e)}")
            raise DataError(f"删除表失败: {str(e)}")
            
    def backup(self, backup_path: str = None) -> bool:
        """
        备份数据库
        
        参数：
            backup_path: 备份文件路径
            
        返回：
            bool: 是否成功备份
            
        异常：
            DataError: 备份失败时抛出
        """
        if not backup_path:
            backup_path = f"backup_{self.database}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
            
        try:
            import subprocess
            
            # 使用mysqldump命令备份
            cmd = [
                'mysqldump',
                f'-h{self.host}',
                f'-P{self.port}',
                f'-u{self.user}',
                f'-p{self.password}',
                self.database
            ]
            
            with open(backup_path, 'w') as f:
                subprocess.run(cmd, stdout=f, check=True)
                
            self.logger.info(f"数据库备份成功: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库备份失败: {str(e)}")
            raise DataError(f"数据库备份失败: {str(e)}")
            
    def restore(self, backup_path: str) -> bool:
        """
        从备份恢复数据库
        
        参数：
            backup_path: 备份文件路径
            
        返回：
            bool: 是否成功恢复
            
        异常：
            DataError: 恢复失败时抛出
        """
        try:
            import subprocess
            
            # 使用mysql命令恢复
            cmd = [
                'mysql',
                f'-h{self.host}',
                f'-P{self.port}',
                f'-u{self.user}',
                f'-p{self.password}',
                self.database
            ]
            
            with open(backup_path, 'r') as f:
                subprocess.run(cmd, stdin=f, check=True)
                
            self.logger.info(f"数据库恢复成功: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库恢复失败: {str(e)}")
            raise DataError(f"数据库恢复失败: {str(e)}")
            
    def get_table_schema(self, table: str) -> Dict[str, str]:
        """
        获取表结构
        
        参数：
            table: 表名
            
        返回：
            Dict[str, str]: 表结构定义
            
        异常：
            DataError: 获取表结构失败时抛出
        """
        if not self.is_connected():
            raise ConnectionError("未连接到数据库")
            
        try:
            # 如果已缓存，直接返回
            if table in self._table_schemas:
                return self._table_schemas[table]
                
            # 查询表结构
            self.cursor.execute("""
                SELECT column_name, data_type, character_maximum_length
                FROM information_schema.columns
                WHERE table_schema = %s AND table_name = %s
            """, (self.database, table))
            
            columns = self.cursor.fetchall()
            schema = {}
            
            for col in columns:
                dtype = col['data_type'].upper()
                if col['character_maximum_length']:
                    dtype += f"({col['character_maximum_length']})"
                schema[col['column_name']] = dtype
                
            # 缓存表结构
            self._table_schemas[table] = schema
            
            return schema
            
        except Exception as e:
            self.logger.error(f"获取表结构失败: {str(e)}")
            raise DataError(f"获取表结构失败: {str(e)}")
            
    def get_table_stats(self, table: str) -> Dict[str, Any]:
        """
        获取表统计信息
        
        参数：
            table: 表名
            
        返回：
            Dict[str, Any]: 表统计信息
            
        异常：
            DataError: 获取表统计信息失败时抛出
        """
        if not self.is_connected():
            raise ConnectionError("未连接到数据库")
            
        try:
            stats = {}
            
            # 获取表行数
            self.cursor.execute(f"SELECT COUNT(*) as count FROM {self._sanitize_name(table)}")
            stats['row_count'] = self.cursor.fetchone()['count']
            
            # 获取表大小
            self.cursor.execute("""
                SELECT 
                    data_length,
                    index_length,
                    create_time,
                    update_time
                FROM information_schema.tables
                WHERE table_schema = %s AND table_name = %s
            """, (self.database, table))
            
            result = self.cursor.fetchone()
            if result:
                stats['data_size'] = result['data_length']
                stats['index_size'] = result['index_length']
                stats['create_time'] = result['create_time']
                stats['update_time'] = result['update_time']
                
            return stats
            
        except Exception as e:
            self.logger.error(f"获取表统计信息失败: {str(e)}")
            raise DataError(f"获取表统计信息失败: {str(e)}")
            
    def _sanitize_name(self, name: str) -> str:
        """清理名称，去除不安全字符"""
        return name.replace('`', '').replace(';', '')
        
    def _get_column_names(self, cursor) -> List[str]:
        """
        从游标描述中获取列名列表
        
        参数:
            cursor: 数据库游标
            
        返回:
            List[str]: 列名列表
        """
        if cursor.description:
            return [col[0] for col in cursor.description]
        return []
    
    def _build_where_clause(self, conditions: Dict[str, Any]) -> Tuple[str, List[Any]]:
        """
        根据条件字典构建WHERE子句
        
        参数:
            conditions: 条件字典，如 {"id": 1} 或 {"id": [1, 2, 3]}
            
        返回:
            Tuple[str, List[Any]]: WHERE子句和参数列表的元组
        """
        clause_parts = []
        params = []
        
        for key, value in conditions.items():
            if value is None:
                clause_parts.append(f"{self._sanitize_name(key)} IS NULL")
            elif isinstance(value, (list, tuple)):
                if value:  # 确保列表不为空
                    placeholders = ', '.join(['%s'] * len(value))
                    clause_parts.append(f"{self._sanitize_name(key)} IN ({placeholders})")
                    params.extend(value)
            elif isinstance(value, dict) and 'op' in value and 'value' in value:
                # 支持操作符: {'op': '>', 'value': 100}
                op = value['op']
                op_value = value['value']
                clause_parts.append(f"{self._sanitize_name(key)} {op} %s")
                params.append(op_value)
            else:
                clause_parts.append(f"{self._sanitize_name(key)} = %s")
                params.append(value)
        
        if not clause_parts:
            return "1=1", []
            
        return " AND ".join(clause_parts), params
        
    def _infer_schema(self, df: pd.DataFrame) -> Dict[str, str]:
        """
        从DataFrame推断表结构
        
        参数：
            df: 数据框
            
        返回：
            Dict[str, str]: 表结构定义
        """
        type_map = {
            'int64': 'BIGINT',
            'int32': 'INT',
            'float64': 'DOUBLE',
            'float32': 'FLOAT',
            'bool': 'BOOLEAN',
            'datetime64[ns]': 'DATETIME',
            'object': 'VARCHAR(255)'
        }
        
        schema = {}
        for column, dtype in df.dtypes.items():
            if dtype.name in type_map:
                schema[column] = type_map[dtype.name]
            else:
                schema[column] = 'VARCHAR(255)'
                
        return schema