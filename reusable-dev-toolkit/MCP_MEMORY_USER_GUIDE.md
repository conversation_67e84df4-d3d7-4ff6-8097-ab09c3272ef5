# MCP记忆文件用户控制指南

## 📋 快速开始

### 1. 修改记忆内容
直接编辑 `.augment/rules/project_memory_current.yaml` 中的记忆内容：

```yaml
memories:
  preference:
    memory_items:
    - content: "修改这里的记忆内容"  # 系统会自动同步到MCP
```

### 2. 添加用户控制字段
在 `export_info` 部分添加控制字段：

```yaml
export_info:
  # 用户个人配置（会被系统保护）
  user_test_note: "我的测试备注"
  development_notes: "开发笔记"
  
  # 同步控制选项
  force_merge: true      # 强制智能合并
  force_update: false    # 不强制更新
  protect_user_changes: true  # 保护用户修改
```

## 🔧 控制选项说明

### force_merge: true
- **效果**：强制执行智能合并
- **结果**：保留用户修改 + 更新MCP数据
- **日志**：`🔀 用户设置force_merge=true，强制执行智能合并`

### force_update: true
- **效果**：强制使用MCP最新数据
- **结果**：备份用户修改 + 使用MCP数据
- **日志**：`🔄 用户设置force_update=true，强制使用MCP最新数据`

### 用户个人配置字段
- `user_test_note`: 用户测试备注
- `custom_config`: 用户自定义配置
- `personal_settings`: 个人设置
- `local_overrides`: 本地覆盖配置
- `development_notes`: 开发备注

**任何字段设置为非空值时，系统会自动保护用户修改**

## 🚀 使用场景

### 场景1：修改AI记忆内容
```yaml
# 直接修改content字段
memories:
  rule:
    memory_items:
    - content: "新的开发规范：使用TypeScript..."
```
**系统响应**：检测到记忆内容变化，自动同步到MCP系统

### 场景2：添加个人备注
```yaml
export_info:
  development_notes: "这个项目的特殊配置说明"
```
**系统响应**：检测到个人配置，保护用户修改

### 场景3：强制获取最新MCP数据
```yaml
export_info:
  force_update: true
  protect_user_changes: false
```
**系统响应**：备份用户修改，使用MCP最新数据

### 场景4：智能合并用户修改和MCP更新
```yaml
export_info:
  force_merge: true
```
**系统响应**：保留用户修改，同时更新MCP数据

## 🛡️ 安全保障

### 双重备份机制

#### 1. 用户修改备份：`project_memory_current_user_backup.yaml`
- **内容**：用户添加的控制字段和个人配置
- **包含**：用户注释、个人备注、控制选项等
- **触发时机**：当用户设置`force_update=true`时自动创建
- **用途**：保护用户的个人配置不丢失
- **示例内容**：
```yaml
export_info:
  user_test_note: "用户的测试备注"     # 用户个人配置
  development_notes: "开发笔记"       # 用户添加的内容
  force_merge: true                   # 用户控制选项
```

#### 2. MCP数据备份：`mcp_memory_backup.yaml`
- **内容**：MCP系统的原始记忆数据
- **包含**：AI记忆内容、系统元数据等
- **触发时机**：每次运行时自动更新
- **用途**：保持MCP系统数据的完整备份
- **示例内容**：
```yaml
memories:
  rule:
    memory_items:
    - content: "AI记忆内容..."        # MCP系统的记忆数据
```

### 备份文件对比表

| 特性 | user_backup.yaml | mcp_memory_backup.yaml |
|------|------------------|------------------------|
| **数据来源** | 用户修改的配置和控制字段 | MCP系统的记忆数据 |
| **主要内容** | 用户个人配置、控制选项、注释 | AI记忆内容、系统元数据 |
| **更新时机** | force_update=true时创建 | 每次运行时更新 |
| **文件大小** | 较小（仅用户配置） | 较大（完整记忆数据） |
| **恢复用途** | 恢复用户个人设置 | 恢复AI记忆内容 |
- **包含**：AI记忆内容、系统元数据等
- **触发**：每次运行时自动更新
- **用途**：保持MCP系统数据的完整备份

### 备份文件对比
```yaml
# project_memory_current_user_backup.yaml（用户配置备份）
export_info:
  user_test_note: "用户的测试备注"     # 用户个人配置
  development_notes: "开发笔记"       # 用户添加的内容
  force_merge: true                   # 用户控制选项

# mcp_memory_backup.yaml（MCP数据备份）
memories:
  rule:
    memory_items:
    - content: "AI记忆内容..."        # MCP系统的记忆数据
```

### 智能检测
- 记忆内容变化 → 同步到MCP
- 用户配置字段 → 保护修改
- 控制选项 → 精确执行

### 异常处理
- 默认保留用户修改
- 详细的操作日志
- 完整的审计记录

## 📖 完整模板

参考 `.augment/rules/user_control_template.yaml` 获取完整的字段模板和详细说明。

## 🔄 工作流程

1. **开发者修改文件** → 直接编辑 `project_memory_current.yaml`
2. **AI启动时检查** → 自动运行 `memory_sync.py --mcp`
3. **智能检测处理** → 根据用户设置自动选择策略
4. **AI获得更新** → 使用最新的记忆信息

**完全自动化，无需手动干预！**
