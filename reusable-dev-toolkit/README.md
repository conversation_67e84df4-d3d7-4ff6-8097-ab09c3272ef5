# Augment Development Toolkit

> 从量化投资平台项目中抽离的可复用开发工具集合

## 概述

本工具包包含了在量化投资平台开发过程中创建的通用开发工具，经过抽离和优化后可以复用到其他项目中。

## 工具模块

### 📝 Memory Management (记忆管理)
- **memory_sync.py**: 项目记忆同步工具，支持多种格式导出
- **correct_memory_strategy.py**: 记忆策略分析和优化工具
- **memory_integration_validator.py**: 记忆系统集成验证工具

### ✅ Validation Tools (验证工具)
- **development_checker.py**: 开发规范自动检查工具
- **memory_priority_test.py**: 记忆系统优先级测试工具

### 🔧 Rule Management (规则管理)
- 规则迁移和管理相关工具

## 安装和使用

### 基本要求
- Python 3.8+
- PyYAML
- 其他依赖见各工具文件

### 集成到项目
1. 复制所需的工具模块到目标项目
2. 根据项目结构调整导入路径
3. 参考各工具的文档字符串进行配置

## 工具特性

- **项目无关性**: 工具设计时考虑了通用性，减少了项目特定依赖
- **模块化设计**: 每个工具都是独立的模块，可以单独使用
- **完整文档**: 每个工具都包含详细的文档字符串和使用说明

## 贡献指南

如果您在使用过程中发现问题或有改进建议，欢迎提交Issue或Pull Request。

## 许可证

本工具包遵循MIT许可证。

---

*生成时间: 2025-07-26 22:31:15*
*来源项目: 量化投资平台*
