#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开发检查工具
用于验证新功能开发是否遵循项目记忆清单和架构规范

{{ AURA-X: Add - 创建自动化检查机制，防止重复开发和架构违规. Source: 代码重构阶段三记忆清单建立 }}
"""

import os
import ast
import sys
import json
from typing import Dict, List, Set, Any, Optional
from pathlib import Path
from dataclasses import dataclass

# 简化日志记录，避免复杂的导入依赖
class SimpleLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def warning(self, msg): print(f"WARNING: {msg}")
    def error(self, msg): print(f"ERROR: {msg}")

logger = SimpleLogger()


@dataclass
class ArchitectureViolation:
    """架构违规记录"""
    file_path: str
    violation_type: str
    description: str
    severity: str  # 'error', 'warning', 'info'
    suggestion: str


class DevelopmentChecker:
    """
    开发检查器
    
    {{ AURA-X: Add - 实现自动化架构检查，确保新开发遵循记忆清单规范. Source: 代码重构阶段三 }}
    """
    
    def __init__(self, project_root: str = None):
        """
        初始化开发检查器
        
        参数:
            project_root: 项目根目录路径
        """
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent.parent
        self.logger = logger
        
        # 架构规范配置
        self.architecture_rules = {
            # 数据获取规范
            'data_fetcher_rules': {
                'preferred_class': 'HybridDataFetcher',
                'deprecated_classes': ['DataFetcher', 'AsyncDataFetcher'],
                'required_imports': ['src.data.fetcher.hybrid_data_fetcher']
            },
            
            # 策略开发规范
            'strategy_rules': {
                'base_class': 'StrategyInterface',
                'required_methods': ['initialize', 'handle_data', 'generate_signals', 'generate_weights'],
                'factory_registration': 'StrategyFactory.register'
            },
            
            # 存储操作规范
            'storage_rules': {
                'factory_class': 'StorageFactory',
                'interface_class': 'StorageInterface',
                'preferred_adapters': ['SQLiteAdapter', 'MySQLAdapter']
            },
            
            # 缓存管理规范
            'cache_rules': {
                'manager_class': 'CacheManager',
                'interface_class': 'CacheInterface',
                'multi_level_class': 'MultiLevelCache'
            }
        }
        
        # 已知功能模块清单
        self.known_modules = {
            'data_layer': [
                'HybridDataFetcher', 'TushareAdapter', 'StorageFactory', 'CacheManager'
            ],
            'strategy_layer': [
                'StrategyInterface', 'MovingAverageStrategy', 'ValueInvestmentStrategy', 
                'EnhancedMultiFactorStrategy', 'StrategyOptimizer'
            ],
            'backtest_engine': [
                'VectorBacktestEngine', 'BacktestEngineFactory', 'PerformanceReport'
            ],
            'risk_management': [
                'VaRMonitor', 'DrawdownMonitor', 'RiskControlFactory', 'ScenarioAnalyzer'
            ],
            'portfolio': [
                'MeanVarianceOptimizer', 'RiskParityOptimizer', 'ConstraintManager', 'PortfolioAnalyzer'
            ],
            'web_services': [
                'BacktestService', 'DataService', 'MonitorService', 'WebSocketManager'
            ],
            'distributed_system': [
                'DistributedDatabaseManager', 'ClusterMonitor', 'DataSyncManager', 'TransactionManager'
            ]
        }
    
    def check_file(self, file_path: str) -> List[ArchitectureViolation]:
        """
        检查单个文件是否符合架构规范
        
        参数:
            file_path: 文件路径
            
        返回:
            List[ArchitectureViolation]: 违规记录列表
        """
        violations = []
        
        if not file_path.endswith('.py'):
            return violations
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析AST
            tree = ast.parse(content)
            
            # 检查各种规范
            violations.extend(self._check_data_fetcher_usage(file_path, tree, content))
            violations.extend(self._check_strategy_implementation(file_path, tree, content))
            violations.extend(self._check_storage_usage(file_path, tree, content))
            violations.extend(self._check_duplicate_functionality(file_path, tree, content))
            
        except Exception as e:
            self.logger.warning(f"检查文件 {file_path} 时出错: {e}")
        
        return violations
    
    def _check_data_fetcher_usage(self, file_path: str, tree: ast.AST, content: str) -> List[ArchitectureViolation]:
        """检查数据获取器使用规范"""
        violations = []

        # 跳过HybridDataFetcher内部实现和合理的继承
        if 'hybrid_data_fetcher.py' in file_path:
            return violations

        deprecated_classes = self.architecture_rules['data_fetcher_rules']['deprecated_classes']
        deprecated_usage = []

        for node in ast.walk(tree):
            # 检查直接实例化调用（不包括合理的继承）
            if isinstance(node, ast.Call) and isinstance(node.func, ast.Name):
                if node.func.id in deprecated_classes:
                    # 排除在类定义内部的super()调用
                    parent_class = self._find_parent_class(tree, node)
                    if not parent_class or not self._is_reasonable_inheritance(parent_class, deprecated_classes):
                        deprecated_usage.append(f"{node.func.id}() 实例化")

            # 检查导入后的直接使用（非继承）
            elif isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name) and isinstance(node.value, ast.Call):
                        if isinstance(node.value.func, ast.Name) and node.value.func.id in deprecated_classes:
                            deprecated_usage.append(f"{node.value.func.id}() 赋值")

        if deprecated_usage:
            violations.append(ArchitectureViolation(
                file_path=file_path,
                violation_type='deprecated_data_fetcher',
                description=f"使用了已弃用的数据获取器: {', '.join(set(deprecated_usage))}",
                severity='warning',
                suggestion=f"建议使用 DataFetcherManager.get_data_fetcher() 替代直接实例化"
            ))

        return violations

    def _find_parent_class(self, tree: ast.AST, node: ast.AST) -> Optional[ast.ClassDef]:
        """查找节点所在的父类"""
        for parent in ast.walk(tree):
            if isinstance(parent, ast.ClassDef):
                for child in ast.walk(parent):
                    if child is node:
                        return parent
        return None

    def _is_reasonable_inheritance(self, class_node: ast.ClassDef, deprecated_classes: List[str]) -> bool:
        """检查是否是合理的继承（如MarketData继承DataFetcher）"""
        for base in class_node.bases:
            if isinstance(base, ast.Name) and base.id in deprecated_classes:
                return True
        return False
    
    def _check_strategy_implementation(self, file_path: str, tree: ast.AST, content: str) -> List[ArchitectureViolation]:
        """检查策略实现规范"""
        violations = []

        # 排除Web模型类和其他非策略文件
        if any(path_part in file_path for path_part in ['models/', 'web/', 'api/', 'requests.py', 'responses.py']):
            return violations

        # 检查是否有策略类定义
        strategy_classes = []
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                # 更精确的策略类识别：
                # 1. 类名以Strategy结尾
                # 2. 在strategies目录中
                # 3. 排除明显的非策略类（如StrategyInfo, StrategyType等数据模型）
                if (node.name.endswith('Strategy') and
                    'strategies' in file_path and
                    not self._is_data_model_class(node, content)):
                    strategy_classes.append(node)

        for strategy_class in strategy_classes:
            # 检查是否继承了正确的基类（包括间接继承）
            base_classes = [base.id for base in strategy_class.bases if isinstance(base, ast.Name)]
            required_base = self.architecture_rules['strategy_rules']['base_class']

            # 检查直接继承或已知的策略基类
            valid_strategy_bases = [
                required_base, 'BaseStrategy', 'MovingAverageStrategy', 'ValueInvestmentStrategy',
                'MomentumStrategy', 'MovingAverageBase'  # 添加新的基类
            ]
            has_valid_base = any(base in base_classes for base in valid_strategy_bases)

            if not has_valid_base:
                violations.append(ArchitectureViolation(
                    file_path=file_path,
                    violation_type='strategy_inheritance',
                    description=f"策略类 {strategy_class.name} 未继承正确的基类",
                    severity='error',
                    suggestion=f"策略类应继承 {required_base}、BaseStrategy 或其他策略基类"
                ))

        return violations

    def _is_data_model_class(self, class_node: ast.ClassDef, content: str) -> bool:
        """判断是否是数据模型类（非策略类）"""
        # 检查是否有数据模型的特征
        data_model_indicators = [
            'BaseModel',  # Pydantic模型
            'dataclass',  # 数据类
            'NamedTuple', # 命名元组
            'TypedDict',  # 类型字典
        ]

        # 检查继承的基类
        for base in class_node.bases:
            if isinstance(base, ast.Name) and base.id in data_model_indicators:
                return True

        # 检查装饰器
        for decorator in class_node.decorator_list:
            if isinstance(decorator, ast.Name) and decorator.id == 'dataclass':
                return True

        # 检查类名模式（Info, Type, Request, Response等）
        model_suffixes = ['Info', 'Type', 'Request', 'Response', 'Config', 'Data', 'Model']
        if any(class_node.name.endswith(suffix) for suffix in model_suffixes):
            return True

        return False
    
    def _check_storage_usage(self, file_path: str, tree: ast.AST, content: str) -> List[ArchitectureViolation]:
        """检查存储操作规范"""
        violations = []

        # 排除工厂类内部的合理使用
        if self._is_storage_factory_file(file_path):
            return violations

        # 检查是否直接实例化存储适配器而不是使用工厂
        direct_storage_usage = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Call) and isinstance(node.func, ast.Name):
                if node.func.id in ['SQLiteAdapter', 'MySQLAdapter', 'RedisAdapter']:
                    direct_storage_usage.append(node.func.id)

        if direct_storage_usage:
            violations.append(ArchitectureViolation(
                file_path=file_path,
                violation_type='direct_storage_instantiation',
                description=f"直接实例化存储适配器: {', '.join(set(direct_storage_usage))}",
                severity='warning',
                suggestion="建议使用 StorageFactory.create() 方法创建存储实例"
            ))

        return violations

    def _is_storage_factory_file(self, file_path: str) -> bool:
        """判断是否是存储工厂文件"""
        factory_patterns = [
            'storage_factory.py',
            'distributed_storage_factory.py',
            'factory.py'
        ]
        return any(pattern in file_path for pattern in factory_patterns)
    
    def _check_duplicate_functionality(self, file_path: str, tree: ast.AST, content: str) -> List[ArchitectureViolation]:
        """检查重复功能"""
        violations = []

        # 排除合理的架构分离
        if self._should_skip_duplicate_check(file_path):
            return violations

        # 检查是否定义了与已知模块重复的类
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                class_name = node.name

                # 排除基类和合理的继承
                if self._is_reasonable_class_definition(node, file_path, class_name):
                    continue

                # 检查是否与已知模块重复
                for module_type, known_classes in self.known_modules.items():
                    if class_name in known_classes:
                        violations.append(ArchitectureViolation(
                            file_path=file_path,
                            violation_type='duplicate_functionality',
                            description=f"类 {class_name} 可能与现有功能重复",
                            severity='warning',
                            suggestion=f"请检查是否可以扩展现有的 {class_name} 而不是重新实现"
                        ))

        return violations

    def _should_skip_duplicate_check(self, file_path: str) -> bool:
        """判断是否应该跳过重复检查"""
        skip_patterns = [
            'node_modules/',  # 第三方库
            'venv/',          # 虚拟环境
            '__pycache__/',   # Python缓存
            '/base/',         # 基类目录
            'distributed_',   # 分布式版本
            '/frontend/',     # 前端代码
        ]
        return any(pattern in file_path for pattern in skip_patterns)

    def _is_reasonable_class_definition(self, class_node: ast.ClassDef, file_path: str, class_name: str) -> bool:
        """判断是否是合理的类定义（非重复）"""
        # 基类通常以Base结尾
        if class_name.endswith('Base'):
            return True

        # 统一版本的类是合理的重构
        if class_name.startswith('Unified'):
            return True

        # Web服务层的抽象是合理的
        if 'services/' in file_path and class_name.endswith('Service'):
            return True

        # 前端和后端的同名类是合理的分离
        if 'frontend/' in file_path or file_path.endswith('.js'):
            return True

        # 分布式版本的类是合理的
        if 'distributed' in file_path.lower():
            return True

        # 优化器模块中的类是合理的架构分离
        if '/optimization/' in file_path:
            return True

        # 报告模块中的类是合理的架构分离
        if '/reports/' in file_path:
            return True

        # 兼容性包装类是合理的重构
        if self._is_compatibility_wrapper_file(file_path):
            return True

        # 检查是否是继承关系而非重复实现
        if self._has_inheritance_relationship(class_node):
            return True

        return False

    def _has_inheritance_relationship(self, class_node: ast.ClassDef) -> bool:
        """检查类是否有继承关系"""
        return len(class_node.bases) > 0

    def _is_compatibility_wrapper_file(self, file_path: str) -> bool:
        """判断是否是兼容性包装文件"""
        # 检查文件内容是否包含兼容性包装的标识
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 检查是否包含兼容性包装的关键词
                compatibility_keywords = [
                    '兼容性包装',
                    '保持向后兼容',
                    '委托给统一',
                    '_unified_',
                    '_get_factory',
                    'Approval: 寸止(ID:'
                ]
                return any(keyword in content for keyword in compatibility_keywords)
        except Exception:
            return False
    
    def check_project(self, target_dirs: List[str] = None) -> Dict[str, Any]:
        """
        检查整个项目
        
        参数:
            target_dirs: 目标检查目录列表
            
        返回:
            Dict: 检查结果
        """
        if target_dirs is None:
            target_dirs = ['src']
        
        all_violations = []
        checked_files = 0
        
        for target_dir in target_dirs:
            dir_path = self.project_root / target_dir
            if not dir_path.exists():
                continue
            
            for py_file in dir_path.rglob('*.py'):
                violations = self.check_file(str(py_file))
                all_violations.extend(violations)
                checked_files += 1
        
        # 统计结果
        violation_stats = {
            'total': len(all_violations),
            'error': len([v for v in all_violations if v.severity == 'error']),
            'warning': len([v for v in all_violations if v.severity == 'warning']),
            'info': len([v for v in all_violations if v.severity == 'info'])
        }
        
        return {
            'checked_files': checked_files,
            'violations': all_violations,
            'statistics': violation_stats,
            'summary': self._generate_summary(all_violations)
        }
    
    def _generate_summary(self, violations: List[ArchitectureViolation]) -> Dict[str, Any]:
        """生成检查摘要"""
        violation_types = {}
        for violation in violations:
            violation_type = violation.violation_type
            if violation_type not in violation_types:
                violation_types[violation_type] = 0
            violation_types[violation_type] += 1
        
        return {
            'most_common_violations': sorted(violation_types.items(), key=lambda x: x[1], reverse=True)[:5],
            'files_with_violations': len(set(v.file_path for v in violations)),
            'recommendations': self._generate_recommendations(violations)
        }
    
    def _generate_recommendations(self, violations: List[ArchitectureViolation]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于违规类型生成建议
        violation_types = set(v.violation_type for v in violations)
        
        if 'deprecated_data_fetcher' in violation_types:
            recommendations.append("统一使用 HybridDataFetcher 作为数据获取接口")
        
        if 'strategy_inheritance' in violation_types:
            recommendations.append("确保所有策略类继承正确的基类")
        
        if 'duplicate_functionality' in violation_types:
            recommendations.append("检查并移除重复功能，优先扩展现有模块")
        
        if 'direct_storage_instantiation' in violation_types:
            recommendations.append("使用工厂模式创建存储和缓存实例")
        
        return recommendations


def main():
    """主函数"""
    checker = DevelopmentChecker()
    
    print("🔍 开始项目架构检查...")
    result = checker.check_project()
    
    print(f"\n📊 检查结果:")
    print(f"检查文件数: {result['checked_files']}")
    print(f"发现违规: {result['statistics']['total']} 个")
    print(f"  - 错误: {result['statistics']['error']} 个")
    print(f"  - 警告: {result['statistics']['warning']} 个")
    print(f"  - 信息: {result['statistics']['info']} 个")
    
    if result['violations']:
        print(f"\n⚠️  主要违规类型:")
        for violation_type, count in result['summary']['most_common_violations']:
            print(f"  - {violation_type}: {count} 个")
        
        print(f"\n💡 改进建议:")
        for recommendation in result['summary']['recommendations']:
            print(f"  - {recommendation}")
        
        print(f"\n📝 详细违规记录:")
        for violation in result['violations'][:10]:  # 只显示前10个
            print(f"  [{violation.severity.upper()}] {violation.file_path}")
            print(f"    {violation.description}")
            print(f"    建议: {violation.suggestion}")
            print()
    else:
        print("\n✅ 未发现架构违规，项目结构良好！")


if __name__ == "__main__":
    main()
