#!/usr/bin/env python3
"""
AI启动时自动检查脚本

这个脚本在AI启动时自动运行，检查记忆系统状态并提供必要的提醒。
可以通过环境变量或配置文件控制检查行为。

使用方法:
1. 直接运行: python ai_startup_check.py
2. 静默模式: python ai_startup_check.py --silent
3. 指定项目: python ai_startup_check.py --project /path/to/project
4. 快速检查: python ai_startup_check.py --quick

作者: AURA-X 协议 (Cunzhi Edition)
版本: 1.0.0
"""

import sys
import os
import argparse
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

try:
    from reusable_dev_toolkit.memory_management.memory_sync import ai_startup_check, MemorySync
except ImportError:
    # 如果导入失败，尝试相对导入
    try:
        from memory_sync import ai_startup_check, MemorySync
    except ImportError:
        print("❌ 无法导入记忆同步模块，请检查安装")
        sys.exit(1)


def load_config(project_path: str = None) -> Dict[str, Any]:
    """
    加载AI启动检查配置

    参数:
        project_path: 项目路径

    返回:
        Dict[str, Any]: 配置字典
    """
    if project_path is None:
        project_path = str(Path(__file__).parent.parent.parent)

    config_file = Path(project_path) / "reusable-dev-toolkit" / "memory_management" / "config" / "ai_startup_config.yaml"

    # 默认配置
    default_config = {
        'startup_check': {
            'enabled': True,
            'mode': 'quick',
            'silent': False,
            'check_interval_hours': 24
        },
        'checks': {
            'mcp_connection': True,
            'user_control_files': True,
            'file_integrity': True,
            'backup_status': True,
            'memory_consistency': False
        },
        'output': {
            'show_icons': True,
            'show_recommendations': True,
            'show_warnings': True,
            'max_warnings': 3,
            'max_recommendations': 2
        }
    }

    if not config_file.exists():
        return default_config

    try:
        import yaml
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 合并默认配置
        for key, value in default_config.items():
            if key not in config:
                config[key] = value
            elif isinstance(value, dict):
                for subkey, subvalue in value.items():
                    if subkey not in config[key]:
                        config[key][subkey] = subvalue

        return config

    except Exception as e:
        print(f"⚠️  配置文件加载失败，使用默认配置: {e}")
        return default_config


def should_run_check(config: Dict[str, Any], project_path: str = None) -> bool:
    """
    判断是否应该运行检查（基于时间间隔）

    参数:
        config: 配置字典
        project_path: 项目路径

    返回:
        bool: 是否应该运行检查
    """
    if not config['startup_check']['enabled']:
        return False

    interval_hours = config['startup_check']['check_interval_hours']
    if interval_hours <= 0:
        return True  # 每次都检查

    if project_path is None:
        project_path = str(Path(__file__).parent.parent.parent)

    # 检查上次检查时间
    last_check_file = Path(project_path) / ".augment" / "logs" / "last_startup_check.txt"

    if not last_check_file.exists():
        return True  # 首次检查

    try:
        from datetime import datetime, timedelta

        with open(last_check_file, 'r') as f:
            last_check_str = f.read().strip()

        last_check = datetime.fromisoformat(last_check_str)
        now = datetime.now()

        return (now - last_check).total_seconds() > interval_hours * 3600

    except Exception:
        return True  # 出错时默认检查


def save_check_time(project_path: str = None):
    """
    保存检查时间

    参数:
        project_path: 项目路径
    """
    if project_path is None:
        project_path = str(Path(__file__).parent.parent.parent)

    logs_dir = Path(project_path) / ".augment" / "logs"
    logs_dir.mkdir(parents=True, exist_ok=True)

    last_check_file = logs_dir / "last_startup_check.txt"

    try:
        from datetime import datetime
        with open(last_check_file, 'w') as f:
            f.write(datetime.now().isoformat())
    except Exception as e:
        pass  # 忽略保存失败


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="AI启动时记忆系统状态检查",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python ai_startup_check.py                    # 标准检查
  python ai_startup_check.py --silent           # 静默模式
  python ai_startup_check.py --quick            # 快速检查
  python ai_startup_check.py --project /path    # 指定项目路径
        """
    )
    
    parser.add_argument(
        '--project', '-p',
        type=str,
        default=None,
        help='项目路径（默认为当前项目）'
    )
    
    parser.add_argument(
        '--silent', '-s',
        action='store_true',
        help='静默模式（减少输出）'
    )
    
    parser.add_argument(
        '--quick', '-q',
        action='store_true',
        help='快速检查模式（仅检查关键状态）'
    )
    
    parser.add_argument(
        '--json',
        action='store_true',
        help='以JSON格式输出结果'
    )
    
    return parser.parse_args()


def quick_check(project_path: str = None) -> Dict[str, Any]:
    """
    快速检查模式 - 仅检查最关键的状态
    
    参数:
        project_path: 项目路径
        
    返回:
        Dict[str, Any]: 简化的检查结果
    """
    if project_path is None:
        project_path = str(Path(__file__).parent.parent.parent)
    
    sync = MemorySync(project_path)
    
    # 快速检查关键文件 - 更新到新的文件位置
    memory_data_dir = Path(project_path) / "reusable-dev-toolkit" / "memory_management" / "data"
    current_memory_file = memory_data_dir / "project_memory_current.yaml"
    backup_file = memory_data_dir / "mcp_memory_backup.yaml"
    
    result = {
        'mode': 'quick',
        'timestamp': sync._get_current_timestamp(),
        'memory_file_exists': current_memory_file.exists(),
        'backup_exists': backup_file.exists(),
        'mcp_available': sync._check_mcp_availability(),
        'status': 'ok'
    }
    
    # 检查用户控制字段
    if current_memory_file.exists():
        try:
            with open(current_memory_file, 'r', encoding='utf-8') as f:
                content = f.read()
            result['has_user_controls'] = 'user_modified:' in content or 'force_merge:' in content
        except:
            result['has_user_controls'] = False
    else:
        result['has_user_controls'] = False
    
    # 判断整体状态
    if not result['memory_file_exists']:
        result['status'] = 'needs_setup'
    elif not result['backup_exists']:
        result['status'] = 'needs_sync'
    
    return result


def format_json_output(result: Dict[str, Any]) -> str:
    """格式化JSON输出"""
    import json
    return json.dumps(result, indent=2, ensure_ascii=False)


def main():
    """主函数"""
    args = parse_arguments()

    try:
        # 加载配置
        config = load_config(args.project)

        # 检查是否应该运行检查
        if not should_run_check(config, args.project):
            if not args.silent and not args.json:
                print("⏭️  [AI启动] 跳过检查（未到检查间隔）")
            sys.exit(0)

        # 确定检查模式
        check_mode = args.quick or config['startup_check']['mode'] == 'quick'
        silent_mode = args.silent or config['startup_check']['silent']

        if check_mode:
            # 快速检查模式
            result = quick_check(args.project)

            if args.json:
                print(format_json_output(result))
            elif not silent_mode:
                show_icons = config['output']['show_icons']

                status_icon = {
                    'ok': '✅' if show_icons else '[OK]',
                    'needs_setup': '⚠️' if show_icons else '[SETUP]',
                    'needs_sync': '📋' if show_icons else '[SYNC]'
                }.get(result['status'], '❓' if show_icons else '[UNKNOWN]')

                print(f"🚀 [AI启动] 快速检查完成 {status_icon}")
                print(f"   状态: {result['status']}")

                if result['status'] == 'needs_setup' and config['output']['show_recommendations']:
                    print("   建议: 运行 MCP 同步创建记忆文件")
                elif result['status'] == 'needs_sync' and config['output']['show_recommendations']:
                    print("   建议: 运行 MCP 同步更新备份")
        else:
            # 完整检查模式
            result = ai_startup_check(args.project, silent_mode)

            if args.json:
                print(format_json_output(result))
            elif not silent_mode:
                # 根据配置过滤输出
                if config['output']['show_warnings'] and 'warnings' in result:
                    max_warnings = config['output']['max_warnings']
                    if len(result['warnings']) > max_warnings:
                        print(f"⚠️  [AI启动] 显示前{max_warnings}个警告（共{len(result['warnings'])}个）:")
                        for warning in result['warnings'][:max_warnings]:
                            print(f"    • {warning}")
                        print(f"    ... 还有{len(result['warnings']) - max_warnings}个警告")

                if config['output']['show_recommendations'] and 'recommendations' in result:
                    max_recs = config['output']['max_recommendations']
                    if len(result['recommendations']) > max_recs:
                        print(f"💡 [AI启动] 显示前{max_recs}个建议（共{len(result['recommendations'])}个）:")
                        for rec in result['recommendations'][:max_recs]:
                            print(f"    • {rec}")
                        print(f"    ... 还有{len(result['recommendations']) - max_recs}个建议")

        # 保存检查时间
        save_check_time(args.project)

        # 根据检查结果设置退出码
        if 'errors' in result and result['errors']:
            sys.exit(1)  # 有错误
        elif 'warnings' in result and result['warnings']:
            sys.exit(2)  # 有警告
        else:
            sys.exit(0)  # 正常

    except KeyboardInterrupt:
        if not args.silent:
            print("\n🛑 检查被用户中断")
        sys.exit(130)
    except Exception as e:
        if not args.silent:
            print(f"❌ 检查过程发生异常: {e}")
        if args.json:
            print(format_json_output({'status': 'error', 'error': str(e)}))
        sys.exit(1)


if __name__ == "__main__":
    main()
