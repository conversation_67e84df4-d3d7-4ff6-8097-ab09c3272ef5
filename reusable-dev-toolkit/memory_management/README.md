# Memory Management Module

## 📁 目录结构

```
memory_management/
├── config/                          # 工具配置文件
│   └── ai_startup_config.yaml      # AI启动检查配置
├── data/                            # 记忆数据文件
│   ├── mcp_memory_backup.yaml      # MCP记忆备份
│   ├── project_memory_current.yaml # 当前项目记忆
│   └── project_memory_current_user_backup.yaml # 用户备份
├── templates/                       # 模板文件
│   └── user_control_template.yaml  # 用户控制模板
├── ai_startup_check.py             # AI启动检查脚本
├── memory_sync.py                  # 记忆同步工具
├── mcp_memory_optimizer.py         # 记忆优化器
└── README.md                       # 本文件
```

## 🔧 配置文件说明

### config/ai_startup_config.yaml
- **用途**: 控制AI启动时记忆系统检查的行为
- **配置项**: 检查模式、输出设置、自动修复、通知设置等
- **使用**: 由 `ai_startup_check.py` 读取

### data/目录
- **mcp_memory_backup.yaml**: MCP系统记忆的完整备份
- **project_memory_current.yaml**: 用户可编辑的当前记忆文件
- **project_memory_current_user_backup.yaml**: 用户修改的备份

### templates/user_control_template.yaml
- **用途**: 为用户提供完整的控制字段模板
- **内容**: 包含所有可用的用户控制选项和详细说明

## 🚀 使用方法

### 1. AI启动检查
```bash
python reusable-dev-toolkit/memory_management/ai_startup_check.py
```

### 2. 记忆同步
```bash
python reusable-dev-toolkit/memory_management/memory_sync.py --mcp
```

### 3. 修改记忆内容
直接编辑 `data/project_memory_current.yaml` 文件

## 📖 详细文档

参考 [MCP_MEMORY_USER_GUIDE.md](../MCP_MEMORY_USER_GUIDE.md) 获取完整的使用指南。

## 🔄 文件重组说明

这些文件原本位于 `.augment/rules/` 目录下，现已重新组织到此处：

**重组原因**:
1. 这些文件本质上都是记忆管理工具的组成部分
2. 按功能分类更符合软件工程最佳实践
3. 保持工具的完整性和可移植性
4. 便于维护和版本控制

**迁移完成**:
- ✅ 文件已移动到新位置
- ✅ 相关工具的路径引用已更新
- ✅ 文档已同步更新
- ✅ 保持向后兼容性
