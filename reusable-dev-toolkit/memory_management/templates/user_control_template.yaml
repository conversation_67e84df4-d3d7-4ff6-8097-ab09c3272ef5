# ========================================
# MCP记忆文件用户控制字段模板
# 文件：reusable-dev-toolkit/memory_management/data/project_memory_current.yaml
# 用途：开发者可以复制以下字段到export_info部分来控制系统行为
# ========================================

export_info:
  # ========================================
  # === 系统元数据（自动管理，请勿修改） ===
  # ========================================
  exporter_version: 2.0.0  # MCP记忆导出工具版本
  project_path: /path/to/project  # 项目根路径
  purpose: 验证记忆系统优化效果并建立本地备份  # 导出目的
  timestamp: '2025-07-30 16:00:00'  # 最后更新时间戳
  merged_at: '2025-07-30 16:00:00'  # 最后合并时间（如果有）
  merge_note: 智能合并操作说明  # 合并操作说明（如果有）
  
  # ========================================
  # === 用户控制字段（开发者可修改） ===
  # ========================================
  user_modified: true  # 标记用户已修改文件，影响系统处理策略
  user_modification_note: "用户修改说明"  # 用户修改说明，便于追踪变更原因
  
  # ========================================
  # === 用户个人配置（修改这些字段会被系统保护） ===
  # 效果：任何字段设置为非空值时，系统会自动保护用户修改，不会被MCP数据覆盖
  # 使用场景：添加个人备注、测试配置、本地设置等
  # ========================================
  user_test_note: ""  
  # 示例：user_test_note: "测试新功能" 
  # 效果：系统检测到个人配置，自动保护用户修改
  
  custom_config: ""   
  # 示例：custom_config: "本地开发配置" 
  # 效果：系统保护此文件不被MCP数据覆盖
  
  personal_settings: ""  
  # 示例：personal_settings: "个人偏好设置" 
  # 效果：系统保护用户个人配置
  
  local_overrides: ""    
  # 示例：local_overrides: "覆盖默认行为" 
  # 效果：系统保护本地覆盖配置
  
  development_notes: ""  
  # 示例：development_notes: "开发过程记录" 
  # 效果：系统保护开发备注
  
  # ========================================
  # === 同步控制选项（精确控制系统行为） ===
  # ========================================
  force_merge: false     
  # 强制智能合并：true时无论其他条件，强制执行智能合并
  # 效果：保留用户修改 + 更新MCP最新数据 + 添加合并审计记录
  # 使用场景：需要同时保留用户修改和获取MCP更新时
  # 示例：force_merge: true → 系统输出"🔀 用户设置force_merge=true，强制执行智能合并"
  
  force_update: false     
  # 强制使用MCP最新数据：true时强制用MCP数据覆盖本地文件
  # 效果：自动备份用户修改到 *_user_backup.yaml + 使用MCP最新数据
  # 使用场景：确认要放弃本地修改，使用MCP最新数据时
  # 示例：force_update: true → 系统输出"🔄 用户设置force_update=true，强制使用MCP最新数据"
  
  protect_user_changes: true  
  # 保护用户修改：false时关闭用户修改保护机制
  # 效果：即使有用户个人配置，也不会自动保护
  # 使用场景：明确要让系统覆盖用户修改时
  # 示例：protect_user_changes: false → 系统忽略用户个人配置保护

# ========================================
# === 使用方法 ===
# ========================================

# 1. 修改记忆内容（会自动同步到MCP系统）：
# memories:
#   preference:
#     memory_items:
#     - content: "修改这里的记忆内容"  # 系统检测变化并同步到MCP

# 2. 添加个人备注（会被系统保护）：
# export_info:
#   user_test_note: "我的测试备注"  # 系统保护此修改
#   development_notes: "开发笔记"   # 系统保护此修改

# 3. 控制同步行为：
# export_info:
#   force_merge: true      # 强制智能合并
#   force_update: true     # 强制使用MCP最新数据
#   protect_user_changes: false  # 关闭用户修改保护

# ========================================
# === 系统行为说明 ===
# ========================================

# 检测到用户修改记忆内容时：
# → 📝 检测到用户修改了记忆内容：['category:id']
# → 📤 同步到MCP系统
# → ✅ 本地文件更新为最新状态

# 检测到用户个人配置时：
# → 📋 检测到用户个人配置，保护用户修改
# → 🔒 自动保留用户修改

# 用户设置force_merge=true时：
# → 🔀 用户设置force_merge=true，强制执行智能合并
# → 🔀 正在执行智能合并...
# → ✅ 智能合并完成：保留用户控制字段，更新MCP记忆数据
# → 🛡️ 所有用户控制字段都会被保护（包括个人配置和控制选项）

# 用户设置force_update=true时：
# → 🔄 用户设置force_update=true，强制使用MCP最新数据
# → 📦 用户修改已备份到 *_user_backup.yaml（包含所有用户控制字段）
# → ✅ 已用MCP最新数据更新文件

# ========================================
# === 安全保障 ===
# ========================================

# 1. 多重备份保护：
#    - 用户修改自动备份（*_user_backup.yaml）
#    - MCP数据备份（mcp_memory_backup.yaml）
#    - 异常处理默认保留用户修改

# 2. 智能检测机制：
#    - 记忆内容变化检测（自动同步到MCP）
#    - 用户配置字段检测（自动保护）
#    - 控制选项优先级处理

# 3. 操作审计：
#    - 详细的操作日志输出
#    - 合并操作审计记录
#    - 时间戳和操作说明
