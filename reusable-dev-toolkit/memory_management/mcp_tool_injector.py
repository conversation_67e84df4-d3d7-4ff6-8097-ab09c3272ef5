#!/usr/bin/env python3
"""
MCP工具注入器

此模块提供了一个MCP工具注入器，可以在运行时动态注入ji___工具，
使得在不同环境中都能够访问MCP记忆系统功能。

功能:
1. 动态注入ji___工具到全局命名空间
2. 提供MCP工具的模拟实现
3. 支持真实MCP环境和模拟环境的无缝切换
4. 提供MCP工具调用的统一接口
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any, Callable
import importlib.util


class MCPToolInjector:
    """MCP工具注入器"""
    
    def __init__(self, project_path: str = None):
        """
        初始化MCP工具注入器
        
        参数:
            project_path: 项目路径
        """
        self.project_path = project_path or str(Path(__file__).parent.parent.parent)
        self.ji_tool = None
        self.is_real_mcp = False
        self.injection_successful = False
        
    def inject_mcp_tool(self) -> bool:
        """
        注入MCP工具到全局命名空间
        
        返回:
            bool: 注入是否成功
        """
        try:
            print("🔧 开始注入MCP工具...")
            
            # 首先尝试获取真实的MCP工具
            real_tool = self._try_get_real_mcp_tool()
            
            if real_tool is not None:
                # 使用真实的MCP工具
                self.ji_tool = real_tool
                self.is_real_mcp = True
                print("✅ 成功获取真实MCP工具")
            else:
                # 使用模拟的MCP工具
                self.ji_tool = self._create_mock_mcp_tool()
                self.is_real_mcp = False
                print("🔄 使用模拟MCP工具")
            
            # 注入到全局命名空间
            self._inject_to_globals()
            
            # 验证注入结果
            if self._verify_injection():
                self.injection_successful = True
                print("✅ MCP工具注入成功")
                return True
            else:
                print("❌ MCP工具注入验证失败")
                return False
                
        except Exception as e:
            print(f"❌ MCP工具注入失败: {e}")
            return False
    
    def _try_get_real_mcp_tool(self) -> Optional[Callable]:
        """尝试获取真实的MCP工具"""
        try:
            # 方法1: 从全局命名空间获取
            if 'ji___' in globals():
                print("  ✅ 从globals()获取到真实MCP工具")
                return globals()['ji___']
            
            # 方法2: 从builtins获取
            import builtins
            if hasattr(builtins, 'ji___'):
                print("  ✅ 从builtins获取到真实MCP工具")
                return getattr(builtins, 'ji___')
            
            # 方法3: 从sys.modules查找
            for module_name, module in sys.modules.items():
                if hasattr(module, 'ji___'):
                    print(f"  ✅ 从sys.modules['{module_name}']获取到真实MCP工具")
                    return getattr(module, 'ji___')
            
            # 方法4: 尝试直接导入
            try:
                from ji___ import ji___
                print("  ✅ 通过导入获取到真实MCP工具")
                return ji___
            except ImportError:
                pass
            
            # 方法5: 通过inspect查找调用栈
            import inspect
            for frame_info in inspect.stack():
                frame_globals = frame_info.frame.f_globals
                if 'ji___' in frame_globals:
                    print("  ✅ 从调用栈获取到真实MCP工具")
                    return frame_globals['ji___']
            
            print("  ⚠️  无法获取真实MCP工具")
            return None
            
        except Exception as e:
            print(f"  ❌ 获取真实MCP工具时发生异常: {e}")
            return None
    
    def _create_mock_mcp_tool(self) -> Callable:
        """创建模拟的MCP工具"""
        def mock_ji_tool(action: str, project_path: str, category: str = None, content: str = None) -> str:
            """
            模拟的ji___工具实现

            参数:
                action: 操作类型 ("记忆" 或 "回忆")
                project_path: 项目路径
                category: 记忆类别
                content: 记忆内容

            返回:
                str: 模拟的返回结果
            """
            try:
                if action == "回忆":
                    # 模拟记忆回忆功能 - 安全操作
                    mock_memories = self._get_mock_memories(category)
                    # 添加数据来源标记
                    return f"[MOCK_DATA] {mock_memories}"
                elif action == "记忆":
                    # 🛡️ 安全措施：模拟模式下禁用写入操作
                    print("🚫 安全保护：模拟模式下禁止写入操作")
                    print("💡 如需写入记忆，请在真实MCP环境中运行")
                    return "❌ 模拟模式下禁止写入操作，以保护真实MCP记忆数据安全"
                else:
                    return f"❌ 不支持的操作: {action}"

            except Exception as e:
                return f"❌ 模拟MCP工具调用失败: {e}"

        return mock_ji_tool
    
    def _get_mock_memories(self, category: str) -> str:
        """获取模拟记忆内容"""
        mock_data = {
            'rule': """核心开发规范：1)实施改进时使用现有功能代码，减少重复新增类和API 2)数据获取统一使用DataFetcherManager，策略开发继承BaseStrategy，存储操作通过StorageFactory 3)开发前必须查询记忆清单确认是否已有类似功能，避免重复开发 4)PROJECT_MEMORY_CHECKLIST.md保持简洁记忆清单格式，重点记录组件名称、核心功能、主要API、使用场景""",
            'preference': """用户选择方案A：完善真实MCP工具集成，要求不生成总结性Markdown文档，不生成测试脚本，需要帮助编译和运行""",
            'pattern': """量化投资平台核心架构模式：1)数据层-HybridDataFetcher(统一接口)、DataFetcherManager(单例管理)、TushareAdapter(数据源)、SmartCacheManager(智能缓存) 2)策略层-BaseStrategy(基础接口)、StrategyFactory(工厂模式) 3)回测引擎-VectorBacktestEngine、UnifiedBacktestEngineFactory 4)风险管理-VaRMonitor、DrawdownMonitor、ExposureMonitor、ConcentrationMonitor 5)Web服务-BacktestService、DataService、MonitorService 6)分布式系统-DistributedDatabaseManager、ClusterMonitor""",
            'context': """量化投资平台项目状态：已完成企业级量化交易平台开发，具备专业级数据获取、策略执行、回测分析、风险管理、Web服务、分布式系统等核心功能。架构合规性99%+，支持异步数据获取、智能缓存、实时监控、运维自动化等企业级能力，系统已达到生产就绪状态"""
        }
        
        return mock_data.get(category, f"模拟{category}类记忆内容")
    
    def _inject_to_globals(self):
        """将MCP工具注入到全局命名空间"""
        try:
            # 注入到当前模块的globals
            globals()['ji___'] = self.ji_tool
            
            # 注入到builtins（如果可能）
            try:
                import builtins
                setattr(builtins, 'ji___', self.ji_tool)
            except Exception:
                pass
            
            # 注入到sys.modules中的相关模块
            try:
                current_module = sys.modules[__name__]
                setattr(current_module, 'ji___', self.ji_tool)
            except Exception:
                pass
                
        except Exception as e:
            print(f"  ⚠️  注入到全局命名空间时发生异常: {e}")
    
    def _verify_injection(self) -> bool:
        """验证MCP工具注入是否成功"""
        try:
            # 验证globals中是否存在
            if 'ji___' in globals():
                # 尝试调用验证
                test_result = globals()['ji___'](
                    action="回忆", 
                    project_path=self.project_path, 
                    category="rule"
                )
                if test_result:
                    return True
            
            return False
            
        except Exception as e:
            print(f"  ❌ 验证注入时发生异常: {e}")
            return False
    
    def get_injection_status(self) -> Dict[str, Any]:
        """获取注入状态信息"""
        return {
            'injection_successful': self.injection_successful,
            'is_real_mcp': self.is_real_mcp,
            'tool_available': self.ji_tool is not None,
            'project_path': self.project_path,
            'timestamp': datetime.now().isoformat()
        }


def inject_mcp_tool_globally(project_path: str = None) -> bool:
    """
    全局MCP工具注入函数
    
    参数:
        project_path: 项目路径
        
    返回:
        bool: 注入是否成功
    """
    injector = MCPToolInjector(project_path)
    return injector.inject_mcp_tool()


def get_mcp_tool() -> Optional[Callable]:
    """
    获取MCP工具实例
    
    返回:
        Callable: MCP工具函数，失败时返回None
    """
    if 'ji___' in globals():
        return globals()['ji___']
    
    # 如果不存在，尝试注入
    if inject_mcp_tool_globally():
        return globals().get('ji___')
    
    return None


if __name__ == "__main__":
    # 测试MCP工具注入器
    print("🚀 测试MCP工具注入器")
    
    injector = MCPToolInjector()
    success = injector.inject_mcp_tool()
    
    if success:
        print("✅ MCP工具注入测试成功")
        
        # 测试工具调用
        ji_tool = get_mcp_tool()
        if ji_tool:
            result = ji_tool(action="回忆", project_path=injector.project_path, category="rule")
            print(f"📄 测试调用结果: {result[:100]}...")
    else:
        print("❌ MCP工具注入测试失败")
    
    # 显示状态
    status = injector.get_injection_status()
    print(f"📊 注入状态: {status}")
