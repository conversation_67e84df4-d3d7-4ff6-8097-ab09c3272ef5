#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
记忆系统集成验证工具
验证不同记忆系统在AI交互中的实际工作机制和上下文传递

{{ AURA-X: Add - 创建记忆系统集成验证工具. Source: 用户需要验证记忆内容是否真正传递给AI系统 }}
"""

import os
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any


class MemoryIntegrationValidator:
    """
    记忆系统集成验证器
    
    验证记忆系统在AI交互中的工作机制
    """
    
    def __init__(self, project_root: str = None):
        """
        初始化验证器
        
        参数:
            project_root: 项目根目录路径
        """
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent.parent
        
        # 记忆系统路径
        self.memory_paths = {
            'rules_directory': self.project_root / '.augment' / 'rules',
            'vscode_memory': self._find_vscode_memory_path(),
            'local_sync_files': {
                'markdown': self.project_root / 'docs' / 'PROJECT_MEMORY_EXPORT.md',
                'yaml': self.project_root / '.augment' / 'rules' / 'project_memory.yaml',
                'json': self.project_root / '.augment' / 'rules' / 'project_memory.json'
            }
        }
    
    def _find_vscode_memory_path(self) -> Path:
        """查找VSCode记忆文件路径"""
        vscode_storage_base = Path.home() / "Library" / "Application Support" / "Code" / "User" / "workspaceStorage"
        
        if vscode_storage_base.exists():
            for workspace_dir in vscode_storage_base.iterdir():
                if workspace_dir.is_dir():
                    augment_dir = workspace_dir / "Augment.vscode-augment"
                    if augment_dir.exists():
                        memory_file = augment_dir / "Augment-Memories"
                        if memory_file.exists():
                            return memory_file
        
        return None
    
    def analyze_memory_content_sizes(self) -> Dict[str, Any]:
        """分析各记忆系统的内容大小"""
        size_analysis = {
            'timestamp': datetime.now().isoformat(),
            'memory_systems': {},
            'total_size': 0,
            'potential_truncation_risk': False
        }
        
        # 分析.augment/rules目录
        if self.memory_paths['rules_directory'].exists():
            rules_size = 0
            rules_files = []
            for file_path in self.memory_paths['rules_directory'].rglob('*.md'):
                file_size = file_path.stat().st_size
                rules_size += file_size
                rules_files.append({
                    'file': str(file_path.relative_to(self.project_root)),
                    'size': file_size
                })
            
            size_analysis['memory_systems']['rules_directory'] = {
                'total_size': rules_size,
                'file_count': len(rules_files),
                'files': rules_files
            }
            size_analysis['total_size'] += rules_size
        
        # 分析VSCode记忆文件
        if self.memory_paths['vscode_memory'] and self.memory_paths['vscode_memory'].exists():
            vscode_size = self.memory_paths['vscode_memory'].stat().st_size
            size_analysis['memory_systems']['vscode_memory'] = {
                'total_size': vscode_size,
                'file_path': str(self.memory_paths['vscode_memory'])
            }
            size_analysis['total_size'] += vscode_size
        
        # 分析本地同步文件
        sync_files_size = 0
        sync_files = []
        for format_type, file_path in self.memory_paths['local_sync_files'].items():
            if file_path.exists():
                file_size = file_path.stat().st_size
                sync_files_size += file_size
                sync_files.append({
                    'format': format_type,
                    'file': str(file_path.relative_to(self.project_root)),
                    'size': file_size
                })
        
        size_analysis['memory_systems']['local_sync_files'] = {
            'total_size': sync_files_size,
            'file_count': len(sync_files),
            'files': sync_files
        }
        size_analysis['total_size'] += sync_files_size
        
        # 评估截断风险（假设上下文限制为100KB）
        context_limit = 100 * 1024  # 100KB
        if size_analysis['total_size'] > context_limit:
            size_analysis['potential_truncation_risk'] = True
            size_analysis['truncation_analysis'] = {
                'total_size': size_analysis['total_size'],
                'context_limit': context_limit,
                'excess_size': size_analysis['total_size'] - context_limit,
                'risk_level': 'high' if size_analysis['total_size'] > context_limit * 2 else 'medium'
            }
        
        return size_analysis
    
    def create_memory_integration_test(self) -> Dict[str, Any]:
        """创建记忆系统集成测试"""
        test_marker = f"MEMORY_INTEGRATION_TEST_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        test_plan = {
            'test_marker': test_marker,
            'test_objectives': [
                'Verify MCP memory system integration',
                'Verify rules directory auto-loading',
                'Verify VSCode memory integration',
                'Test context length handling'
            ],
            'test_methods': {
                'mcp_memory_test': {
                    'method': 'Add unique test marker to MCP memory',
                    'verification': 'Check if AI can reference the marker',
                    'marker_content': f'记忆集成测试标记：{test_marker}，用于验证MCP记忆系统是否正确集成到AI对话上下文中'
                },
                'rules_directory_test': {
                    'method': 'Add test marker to rules file',
                    'verification': 'Check if AI can reference rules content',
                    'file_path': '.augment/rules/integration_test.md'
                },
                'context_length_test': {
                    'method': 'Analyze total memory content size',
                    'verification': 'Check for potential truncation issues',
                    'size_analysis': 'Run size analysis function'
                }
            },
            'expected_outcomes': {
                'mcp_memory': 'AI should be able to reference MCP memory content',
                'rules_directory': 'AI should be able to reference rules files',
                'vscode_memory': 'AI may or may not reference VSCode memory (workspace-specific)',
                'context_handling': 'AI should handle context length appropriately'
            }
        }
        
        return test_plan
    
    def create_integration_test_file(self, test_marker: str) -> str:
        """创建集成测试文件"""
        test_content = f"""---
type: "integration_test"
created: "{datetime.now().isoformat()}"
---

# 记忆系统集成测试

## 测试标记
{test_marker}

## 测试目的
验证.augment/rules目录中的文件是否会自动加载到AI对话上下文中。

## 验证方法
如果AI能够引用此测试标记，说明rules目录内容已成功集成。

## 测试时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        test_file_path = self.memory_paths['rules_directory'] / 'integration_test.md'
        
        # 确保目录存在
        test_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入测试文件
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        return str(test_file_path)
    
    def run_integration_validation(self) -> Dict[str, Any]:
        """运行完整的集成验证"""
        validation_result = {
            'timestamp': datetime.now().isoformat(),
            'validation_steps': [],
            'size_analysis': None,
            'test_plan': None,
            'recommendations': []
        }
        
        # 步骤1: 分析内容大小
        print("📊 分析记忆内容大小...")
        size_analysis = self.analyze_memory_content_sizes()
        validation_result['size_analysis'] = size_analysis
        validation_result['validation_steps'].append('size_analysis_completed')
        
        # 步骤2: 创建集成测试计划
        print("🧪 创建集成测试计划...")
        test_plan = self.create_memory_integration_test()
        validation_result['test_plan'] = test_plan
        validation_result['validation_steps'].append('test_plan_created')
        
        # 步骤3: 创建测试文件
        print("📝 创建集成测试文件...")
        test_file = self.create_integration_test_file(test_plan['test_marker'])
        validation_result['test_file_created'] = test_file
        validation_result['validation_steps'].append('test_file_created')
        
        # 步骤4: 生成建议
        recommendations = []
        
        if size_analysis['potential_truncation_risk']:
            recommendations.append('警告：记忆内容总大小可能超出上下文限制，存在截断风险')
            recommendations.append('建议：定期清理过时的记忆内容或使用更精简的记忆格式')
        
        recommendations.extend([
            '测试方法：询问AI是否能看到测试标记来验证集成效果',
            '验证MCP记忆：询问AI关于项目的技术实现细节',
            '验证rules目录：询问AI关于项目开发规范',
            '监控上下文：观察AI回复中是否引用了记忆内容'
        ])
        
        validation_result['recommendations'] = recommendations
        
        return validation_result


def main():
    """主函数 - 运行记忆系统集成验证"""
    print("🔍 记忆系统集成验证工具")
    print("=" * 60)
    
    validator = MemoryIntegrationValidator()
    
    print("🚀 开始集成验证...")
    result = validator.run_integration_validation()
    
    print("\n📊 内容大小分析:")
    size_analysis = result['size_analysis']
    print(f"  总大小: {size_analysis['total_size']:,} 字节")
    
    for system_name, system_data in size_analysis['memory_systems'].items():
        if system_data:
            print(f"  {system_name}: {system_data['total_size']:,} 字节")
    
    if size_analysis['potential_truncation_risk']:
        print(f"  ⚠️ 截断风险: {size_analysis['truncation_analysis']['risk_level']}")
        print(f"  超出大小: {size_analysis['truncation_analysis']['excess_size']:,} 字节")
    else:
        print("  ✅ 无截断风险")
    
    print(f"\n🧪 集成测试:")
    test_plan = result['test_plan']
    print(f"  测试标记: {test_plan['test_marker']}")
    print(f"  测试文件: {result['test_file_created']}")
    
    print(f"\n💡 验证建议:")
    for rec in result['recommendations']:
        print(f"  - {rec}")
    
    print(f"\n✅ 验证工具运行完成！")
    print("🔍 现在可以通过询问AI是否能看到测试标记来验证集成效果")


if __name__ == "__main__":
    main()
