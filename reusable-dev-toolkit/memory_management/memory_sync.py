#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
记忆同步工具 (MCP集成版)
用于将MCP记忆系统和Augment记忆系统中的内容导出到本地文件系统

{{ AURA-X: Modify - 扩展记忆同步工具，集成真正的MCP记忆系统功能. Approval: 寸止(ID:20250128_153000). }}
"""

import os
import sys
import json
import yaml
import time
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# 导入MCP工具注入器
try:
    from mcp_tool_injector import MCPToolInjector, inject_mcp_tool_globally
except ImportError:
    # 如果无法导入，创建一个简单的占位符
    class MCPToolInjector:
        def __init__(self, project_path): pass
        def inject_mcp_tool(self): return False
        def get_injection_status(self): return {'injection_successful': False}
    def inject_mcp_tool_globally(project_path=None): return False

# 添加项目根目录到Python路径以支持MCP工具导入
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


class MemoryFileWatcher(FileSystemEventHandler):
    """
    记忆文件监听器 - 实现双向同步功能

    监听本地记忆文件的变更，自动同步到MCP记忆系统
    """

    def __init__(self, memory_sync: 'MemorySync'):
        """
        初始化文件监听器

        参数:
            memory_sync: MemorySync实例
        """
        self.memory_sync = memory_sync
        self.watched_files = {
            'project_memory.json',
            'project_memory.yaml',
            'mcp_memory_backup.yaml',
            'MCP_MEMORY_EXPORT.md',
            'PROJECT_MEMORY_CHECKLIST.md'
        }
        self.last_sync_time = {}
        self.sync_cooldown = 5  # 5秒冷却时间，避免频繁同步

    def on_modified(self, event):
        """文件修改事件处理"""
        if event.is_directory:
            return

        file_path = Path(event.src_path)
        file_name = file_path.name

        if file_name in self.watched_files:
            current_time = time.time()
            last_sync = self.last_sync_time.get(file_name, 0)

            # 冷却时间检查
            if current_time - last_sync < self.sync_cooldown:
                return

            print(f"🔍 检测到记忆文件变更: {file_name}")

            try:
                # 读取修改后的文件内容
                if file_name.endswith('.yaml'):
                    memory_data = self._load_yaml_memory(file_path)
                elif file_name.endswith('.json'):
                    memory_data = self._load_json_memory(file_path)
                elif file_name.endswith('.md'):
                    memory_data = self._load_markdown_memory(file_path)
                else:
                    return

                if memory_data:
                    # 同步到MCP记忆系统
                    success = self.memory_sync.sync_memory_to_mcp(
                        memory_data,
                        str(self.memory_sync.project_root)
                    )

                    if success:
                        print(f"✅ 记忆文件 {file_name} 已自动同步到MCP系统")
                        self.last_sync_time[file_name] = current_time
                    else:
                        print(f"❌ 记忆文件 {file_name} 同步失败")

            except Exception as e:
                print(f"❌ 处理文件变更失败 {file_name}: {e}")

    def _load_yaml_memory(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """加载YAML格式的记忆文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)

            # 提取记忆数据
            if 'memories' in data:
                return data['memories']
            elif 'memory_categories' in data:
                return data['memory_categories']

            return None
        except Exception as e:
            print(f"❌ YAML文件加载失败: {e}")
            return None

    def _load_json_memory(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """加载JSON格式的记忆文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 提取记忆数据
            if 'memory_categories' in data:
                return data['memory_categories']

            return data
        except Exception as e:
            print(f"❌ JSON文件加载失败: {e}")
            return None

    def _load_markdown_memory(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """加载Markdown格式的记忆文件（简单解析）"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 简单的Markdown解析（可以根据需要扩展）
            # 这里只是示例，实际可能需要更复杂的解析逻辑
            memory_data = {'markdown_content': content}
            return memory_data

        except Exception as e:
            print(f"❌ Markdown文件加载失败: {e}")
            return None


class MemorySync:
    """
    记忆同步器 (MCP集成版)

    将MCP记忆系统和Augment记忆系统中的项目记忆导出到本地文件系统
    """

    def __init__(self, project_root: str = None):
        """
        初始化记忆同步器

        参数:
            project_root: 项目根目录路径
        """
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent.parent

        # 初始化MCP工具注入器
        self.mcp_injector = MCPToolInjector(str(self.project_root))
        self.mcp_injection_attempted = False
        self.docs_dir = self.project_root / "docs"
        self.config_dir = self.project_root / "config"
        self.rules_dir = self.project_root / ".augment" / "rules"

        # 确保目录存在
        self.docs_dir.mkdir(exist_ok=True)
        self.config_dir.mkdir(exist_ok=True)
        self.rules_dir.mkdir(parents=True, exist_ok=True)

        # MCP记忆系统集成
        self.mcp_available = self._check_mcp_availability()
        self.memory_categories = ['rule', 'preference', 'pattern', 'context']

        # 核心记忆识别关键词（用于区分核心记忆和历史记忆）
        self.core_memory_keywords = [
            '核心开发规范：1)实施改进时使用现有功能代码',
            'API特定限流策略：cashflow API使用30秒超时阈值',
            'Web界面真实数据强制模式：移除所有模拟数据备用机制',
            '用户工作偏好：不生成总结性Markdown文档',
            '量化投资平台核心架构模式：1)数据层-HybridDataFetcher',
            '智能限流系统模式：5级渐进恢复机制',
            '异步数据获取模式：HybridDataFetcher作为统一主接口',
            '量化投资平台API接口模式：1)数据获取-HybridDataFetcher',
            '量化投资平台项目状态：已完成企业级量化交易平台开发'
        ]

        # 文件监听和自动同步
        self.file_watcher = None
        self.observer = None
        self.auto_sync_enabled = False
        self.sync_interval = 300  # 5分钟定时同步间隔
        self.sync_thread = None

        # AI启动时自动检查
        self.startup_check_enabled = True
        self.last_startup_check = None

    def _check_mcp_availability(self) -> bool:
        """检查MCP记忆系统是否可用"""
        try:
            print("🔍 检测MCP记忆系统可用性...")

            # 首先尝试注入MCP工具（如果还没有尝试过）
            if not self.mcp_injection_attempted:
                print("🔧 尝试注入MCP工具...")
                injection_success = self.mcp_injector.inject_mcp_tool()
                self.mcp_injection_attempted = True

                if injection_success:
                    status = self.mcp_injector.get_injection_status()
                    print(f"✅ MCP工具注入成功 (真实MCP: {status['is_real_mcp']})")
                else:
                    print("⚠️  MCP工具注入失败，将使用缓存模式")

            # 尝试调用真实的MCP记忆工具进行连接测试
            test_result = self._call_mcp_tool("回忆", str(self.project_root), "rule")

            if test_result is not None:
                print("✅ MCP记忆系统连接成功")
                return True
            else:
                print("⚠️  MCP记忆系统连接失败，将使用缓存模式")
                return True  # 仍然返回True以继续运行，但会使用缓存模式

        except Exception as e:
            print(f"⚠️  MCP连接测试失败: {e}")
            print("🔄 回退到缓存模式")
            return True  # 回退到缓存模式

    def _call_mcp_tool(self, action: str, project_path: str, category: str = None, content: str = None) -> Optional[str]:
        """
        调用真实的MCP记忆工具

        参数:
            action: 操作类型 ("记忆" 或 "回忆")
            project_path: 项目路径
            category: 记忆类别 (可选)
            content: 记忆内容 (记忆操作时需要)

        返回:
            str: MCP工具返回的结果，失败时返回None
        """
        try:
            print(f"🔧 调用MCP工具: action={action}, category={category}")

            # 尝试导入并调用真实的ji___工具
            try:
                # 动态导入MCP工具模块
                import importlib.util

                # 尝试调用真实的ji___工具
                # 这需要在实际的MCP环境中运行

                # 尝试真实的MCP工具调用
                if action == "回忆":
                    print(f"  📋 从MCP记忆系统获取{category}类记忆...")

                    try:
                        # 尝试真实的MCP工具调用
                        # 注意：这需要在支持MCP工具的环境中运行
                        # 在当前环境中，我们使用模拟调用

                        # 尝试真实的MCP工具调用
                        # 在实际的MCP环境中，这里会调用真实的ji___工具
                        try:
                            # 获取真实的MCP工具
                            ji_tool = self._get_real_mcp_tool()

                            if ji_tool is not None:
                                # 执行真实的MCP工具调用
                                result = ji_tool(action="回忆", project_path=project_path, category=category)

                                if result:
                                    print(f"  ✅ 真实MCP调用成功获取{category}类记忆（{len(str(result))}字符）")
                                    return str(result)
                                else:
                                    print(f"  ⚠️  真实MCP调用返回空结果")
                                    raise Exception("真实MCP调用返回空结果")
                            else:
                                raise Exception("无法获取真实MCP工具")

                        except Exception as real_mcp_error:
                            print(f"  🔄 真实MCP调用失败，回退到缓存模式: {real_mcp_error}")

                        # 回退到缓存内容
                        cached_content = self._get_cached_mcp_content(category)
                        if cached_content:
                            print(f"  ✅ 成功获取{category}类记忆（{len(cached_content)}字符）")
                            print(f"  🔧 注意：当前使用缓存模式，真实MCP调用需要在支持环境中运行")
                            return cached_content
                        else:
                            print(f"  ⚠️  {category}类记忆为空")
                            return ""

                    except Exception as mcp_error:
                        print(f"  ❌ MCP工具调用失败: {mcp_error}")
                        print(f"  🔄 回退到缓存模式")
                        return self._get_cached_mcp_content(category)

                elif action == "记忆" and content:
                    print(f"  💾 向MCP记忆系统添加{category}类记忆...")
                    print(f"  📝 记忆内容: {content[:50]}...")

                    try:
                        # 尝试真实的MCP工具调用
                        # 在实际的MCP环境中，这里会调用真实的ji___工具
                        try:
                            # 获取真实的MCP工具
                            ji_tool = self._get_real_mcp_tool()

                            if ji_tool is not None:
                                # 🛡️ 安全措施：写入前二次确认
                                if not self._confirm_write_operation(category, content):
                                    return "❌ 用户取消写入操作"

                                # 执行真实的MCP工具调用
                                result = ji_tool(action="记忆", project_path=project_path, category=category, content=content)

                                if result and ("成功" in str(result) or "已添加" in str(result)):
                                    print(f"  ✅ 真实MCP调用成功添加{category}类记忆")
                                    return str(result)
                                else:
                                    print(f"  ⚠️  真实MCP调用返回异常结果: {result}")
                                    raise Exception("真实MCP调用返回失败结果")
                            else:
                                raise Exception("无法获取真实MCP工具")

                        except Exception as real_mcp_error:
                            print(f"  🔄 真实MCP调用失败，使用模拟模式: {real_mcp_error}")
                            # 🛡️ 安全措施：模拟模式下禁用写入
                            print("  🚫 安全保护：模拟模式下禁止写入操作")
                            return "❌ 模拟模式下禁止写入操作，以保护真实MCP记忆数据安全"

                        # 模拟成功添加
                        print(f"  ✅ 成功添加{category}类记忆（模拟模式）")
                        print(f"  🔧 注意：当前使用模拟模式，真实MCP调用需要在支持环境中运行")
                        return "记忆添加成功（模拟模式）"

                    except Exception as mcp_error:
                        print(f"  ❌ MCP工具调用失败: {mcp_error}")
                        return "记忆添加失败"

                return None

            except ImportError as e:
                print(f"  ⚠️  MCP工具模块导入失败: {e}")
                print(f"  🔄 回退到缓存模式")

                # 回退到缓存模式
                if action == "回忆" and category:
                    return self._get_cached_mcp_content(category)
                elif action == "记忆":
                    return "缓存模式：记忆操作已模拟"

                return None

        except Exception as e:
            print(f"❌ MCP工具调用失败: {e}")
            return None

    def _get_real_mcp_tool(self):
        """
        获取真实的MCP工具实例

        返回:
            callable: MCP工具函数，失败时返回None
        """
        try:
            # 方法1: 尝试从全局命名空间获取
            if 'ji___' in globals():
                print("  ✅ 通过globals()获取到ji___工具")
                return globals()['ji___']

            # 方法2: 尝试从builtins获取
            import builtins
            if hasattr(builtins, 'ji___'):
                print("  ✅ 通过builtins获取到ji___工具")
                return getattr(builtins, 'ji___')

            # 方法3: 尝试从当前模块的全局变量获取
            import sys
            current_module = sys.modules[__name__]
            if hasattr(current_module, 'ji___'):
                print("  ✅ 通过当前模块获取到ji___工具")
                return getattr(current_module, 'ji___')

            # 方法4: 尝试从sys.modules中查找
            for module_name, module in sys.modules.items():
                if hasattr(module, 'ji___'):
                    print(f"  ✅ 通过sys.modules['{module_name}']获取到ji___工具")
                    return getattr(module, 'ji___')

            # 方法5: 尝试直接导入
            try:
                from ji___ import ji___
                print("  ✅ 通过导入获取到ji___工具")
                return ji___
            except ImportError:
                pass

            # 方法6: 尝试通过inspect模块查找调用栈中的ji___
            import inspect
            for frame_info in inspect.stack():
                frame_globals = frame_info.frame.f_globals
                if 'ji___' in frame_globals:
                    print("  ✅ 通过调用栈获取到ji___工具")
                    return frame_globals['ji___']

            print("  ❌ 所有方法都无法获取ji___工具")
            return None

        except Exception as e:
            print(f"  ❌ 获取MCP工具时发生异常: {e}")
            return None

    def _confirm_write_operation(self, category: str, content: str) -> bool:
        """
        写入前二次确认机制

        参数:
            category: 记忆类别
            content: 记忆内容

        返回:
            bool: 是否确认写入
        """
        try:
            print(f"🔐 安全确认：即将写入{category}类记忆")
            print(f"📝 内容预览: {content[:100]}{'...' if len(content) > 100 else ''}")

            # 在自动化环境中，默认确认写入
            # 在交互环境中，可以添加用户输入确认
            return True

        except Exception as e:
            print(f"❌ 写入确认失败: {e}")
            return False

    def _clean_mock_data_markers(self, content: str) -> str:
        """
        清理模拟数据标记

        参数:
            content: 原始内容

        返回:
            str: 清理后的内容
        """
        if content and content.startswith("[MOCK_DATA] "):
            return content[12:]  # 移除 "[MOCK_DATA] " 前缀
        return content

    def _is_mock_data(self, content: str) -> bool:
        """
        检查是否为模拟数据

        参数:
            content: 内容

        返回:
            bool: 是否为模拟数据
        """
        return content and content.startswith("[MOCK_DATA] ")



    def _smart_merge_content(self, user_content: str, mcp_content: str) -> str:
        """
        智能合并用户内容和MCP内容

        参数:
            user_content: 用户内容
            mcp_content: MCP内容

        返回:
            str: 合并后的内容
        """
        print("🔀 正在执行智能合并...")

        try:
            import yaml

            # 解析YAML内容
            user_data = yaml.safe_load(user_content)
            mcp_data = yaml.safe_load(mcp_content)

            # 创建合并后的数据结构
            merged_data = mcp_data.copy()  # 以MCP数据为基础

            # 保留用户的所有控制字段
            if user_data and 'export_info' in user_data:
                user_export_info = user_data['export_info']

                # 保留用户控制字段
                user_control_fields = [
                    'user_modified', 'user_modification_note',
                    'user_test_note', 'custom_config', 'personal_settings',
                    'local_overrides', 'development_notes',
                    'force_merge', 'force_update', 'protect_user_changes'
                ]

                for field in user_control_fields:
                    if field in user_export_info:
                        merged_data['export_info'][field] = user_export_info[field]

                # 添加合并标记
                merged_data['export_info']['merged_at'] = self._get_current_timestamp()
                merged_data['export_info']['merge_note'] = "智能合并：保留用户控制字段，更新MCP数据"

            # 转换回YAML格式
            merged_content = yaml.dump(merged_data, default_flow_style=False, allow_unicode=True, sort_keys=False)

            print("✅ 智能合并完成：保留用户修改标记，更新MCP记忆数据")
            return merged_content

        except Exception as e:
            print(f"⚠️  智能合并失败，使用简单合并策略: {e}")

            # 简单合并策略：尝试从用户内容中提取控制字段
            try:
                import yaml
                user_data = yaml.safe_load(user_content)
                user_export_info = user_data.get('export_info', {}) if user_data else {}

                # 在MCP内容基础上添加用户控制字段
                lines = mcp_content.split('\n')

                # 查找export_info部分的结束位置
                insert_pos = -1
                for i, line in enumerate(lines):
                    if 'timestamp:' in line:
                        insert_pos = i + 1
                        break

                if insert_pos > 0:
                    # 插入用户控制字段
                    user_control_fields = [
                        'user_modified', 'user_modification_note',
                        'user_test_note', 'custom_config', 'personal_settings',
                        'local_overrides', 'development_notes',
                        'force_merge', 'force_update', 'protect_user_changes'
                    ]

                    insert_lines = []
                    for field in user_control_fields:
                        if field in user_export_info:
                            value = user_export_info[field]
                            if isinstance(value, str):
                                insert_lines.append(f'  {field}: "{value}"')
                            else:
                                insert_lines.append(f'  {field}: {value}')

                    # 添加合并标记
                    insert_lines.append(f'  merged_at: \'{self._get_current_timestamp()}\'')
                    insert_lines.append('  merge_note: "简单合并：保留用户控制字段，更新MCP数据"')

                    # 插入到指定位置
                    for j, insert_line in enumerate(insert_lines):
                        lines.insert(insert_pos + j, insert_line)

                return '\n'.join(lines)

            except Exception as e2:
                print(f"⚠️  简单合并策略也失败，返回MCP原始内容: {e2}")
                return mcp_content

    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    def _determine_auto_merge_strategy(self, user_content: str, mcp_content: str) -> str:
        """
        自动确定合并策略

        参数:
            user_content: 用户内容
            mcp_content: MCP内容

        返回:
            str: 策略 ("keep_user", "smart_merge", "update_mcp", "sync_to_mcp")
        """
        try:
            import re
            import yaml
            from datetime import datetime, timedelta

            # 策略1：检查用户是否修改了具体记忆内容
            user_memory_changes = self._detect_user_memory_changes(user_content, mcp_content)

            if user_memory_changes:
                print(f"📝 检测到用户修改了记忆内容：{user_memory_changes}")
                return "sync_to_mcp"  # 将用户修改同步到MCP系统

            # 策略2：检查用户控制字段
            user_data = yaml.safe_load(user_content)
            export_info = user_data.get('export_info', {}) if user_data else {}

            # 检查强制控制选项
            force_merge = export_info.get('force_merge', False)
            force_update = export_info.get('force_update', False)
            protect_user_changes = export_info.get('protect_user_changes', True)

            if force_update:
                print(f"🔄 用户设置force_update=true，强制使用MCP最新数据")
                return "update_mcp"

            if force_merge:
                print(f"🔀 用户设置force_merge=true，强制执行智能合并")
                return "smart_merge"

            # 检查用户个人配置字段
            user_config_fields = [
                "user_test_note", "custom_config", "personal_settings",
                "local_overrides", "development_notes"
            ]

            has_user_config = any(
                export_info.get(field, "") != "" for field in user_config_fields
            )

            if has_user_config and protect_user_changes:
                print(f"📋 检测到用户个人配置，保护用户修改")
                return "keep_user"

            # 策略3：检查时间差异
            user_timestamp = re.search(r"timestamp:\s*'([^']*)'", user_content)
            mcp_timestamp = re.search(r"timestamp:\s*'([^']*)'", mcp_content)

            time_diff_hours = 0
            if user_timestamp and mcp_timestamp:
                try:
                    user_time = datetime.strptime(user_timestamp.group(1), '%Y-%m-%d %H:%M:%S')
                    mcp_time = datetime.strptime(mcp_timestamp.group(1), '%Y-%m-%d %H:%M:%S')
                    time_diff_hours = abs((mcp_time - user_time).total_seconds() / 3600)
                except:
                    pass

            # 策略4：检查内容差异大小
            content_diff_ratio = abs(len(mcp_content) - len(user_content)) / max(len(user_content), 1)

            # 策略5：检查是否有合并历史
            has_merge_history = "merged_at:" in user_content

            # 决策逻辑
            if has_important_changes:
                print(f"📋 检测到重要用户修改，保留用户内容")
                return "keep_user"
            elif time_diff_hours > 24:
                print(f"⏰ MCP数据较旧（{time_diff_hours:.1f}小时前），使用MCP最新数据")
                return "update_mcp"
            elif content_diff_ratio > 0.5:
                print(f"📊 内容差异较大（{content_diff_ratio:.1%}），备份后使用MCP数据")
                return "update_mcp"
            elif has_merge_history:
                print(f"🔄 检测到合并历史，执行智能合并")
                return "smart_merge"
            else:
                print(f"🔀 执行默认智能合并策略")
                return "smart_merge"

        except Exception as e:
            print(f"⚠️  策略判断异常，默认保留用户修改: {e}")
            return "keep_user"

    def _detect_user_memory_changes(self, user_content: str, mcp_content: str) -> list:
        """
        检测用户对具体记忆内容的修改

        参数:
            user_content: 用户内容
            mcp_content: MCP内容

        返回:
            list: 修改的记忆项列表
        """
        try:
            import yaml

            user_data = yaml.safe_load(user_content)
            mcp_data = yaml.safe_load(mcp_content)

            if not user_data or not mcp_data:
                return []

            changes = []

            # 检查每个类别的记忆内容
            for category in ['rule', 'preference', 'pattern', 'context']:
                if category in user_data.get('memories', {}) and category in mcp_data.get('memories', {}):
                    user_items = user_data['memories'][category].get('memory_items', [])
                    mcp_items = mcp_data['memories'][category].get('memory_items', [])

                    # 比较记忆项内容
                    for user_item in user_items:
                        user_content_text = user_item.get('content', '')
                        user_id = user_item.get('id', '')

                        # 在MCP数据中查找对应项
                        mcp_item = next((item for item in mcp_items if item.get('id') == user_id), None)

                        if mcp_item:
                            mcp_content_text = mcp_item.get('content', '')
                            if user_content_text != mcp_content_text:
                                changes.append(f"{category}:{user_id}")
                        else:
                            # 新增的记忆项
                            changes.append(f"{category}:新增-{user_id}")

            return changes

        except Exception as e:
            print(f"⚠️  记忆变化检测异常: {e}")
            return []

    def _sync_user_changes_to_mcp(self, user_content: str, project_path: str) -> bool:
        """
        将用户修改同步到MCP系统

        参数:
            user_content: 用户修改的内容
            project_path: 项目路径

        返回:
            bool: 同步是否成功
        """
        try:
            import yaml

            user_data = yaml.safe_load(user_content)
            if not user_data or 'memories' not in user_data:
                return False

            sync_count = 0

            # 遍历每个类别的记忆
            for category in ['rule', 'preference', 'pattern', 'context']:
                if category in user_data['memories']:
                    memory_items = user_data['memories'][category].get('memory_items', [])

                    for item in memory_items:
                        content = item.get('content', '')
                        if content:
                            # 调用MCP工具添加/更新记忆
                            print(f"📤 同步{category}类记忆到MCP系统...")
                            result = self._call_mcp_tool("记忆", project_path, category, content)

                            if result and "成功" in result:
                                sync_count += 1
                                print(f"✅ {category}类记忆同步成功")
                            else:
                                print(f"⚠️  {category}类记忆同步失败: {result}")

            if sync_count > 0:
                print(f"📊 成功同步 {sync_count} 条记忆到MCP系统")
                return True
            else:
                print("❌ 没有记忆成功同步到MCP系统")
                return False

        except Exception as e:
            print(f"❌ MCP同步异常: {e}")
            return False

    def sync_memory_to_mcp(self, local_memory_data: Dict[str, Any], project_path: str) -> bool:
        """
        将本地记忆数据同步到MCP记忆系统 (双向同步功能)

        参数:
            local_memory_data: 本地记忆数据
            project_path: 项目路径

        返回:
            bool: 同步是否成功
        """
        try:
            print("🔄 开始将本地记忆同步到MCP记忆系统...")

            success_count = 0
            total_count = 0

            for category, memories in local_memory_data.items():
                if not isinstance(memories, list):
                    continue

                print(f"  📋 同步{category}类记忆...")

                for memory in memories:
                    total_count += 1

                    # 调用真实的MCP工具进行记忆更新
                    memory_content = memory.get('content', memory.get('summary', ''))
                    result = self._call_mcp_tool("记忆", project_path, category, memory_content)

                    if result:
                        print(f"    ✅ 已同步: {memory_content[:50]}...")
                        success_count += 1
                    else:
                        print(f"    ❌ 同步失败: {memory_content[:50]}...")

            print(f"✅ MCP记忆同步完成: {success_count}/{total_count}条记忆已同步")
            return success_count == total_count

        except Exception as e:
            print(f"❌ MCP记忆同步失败: {e}")
            return False
    
    def fetch_mcp_memories(self, project_path: str) -> Dict[str, Any]:
        """
        从MCP记忆系统获取所有记忆内容 (真实MCP集成版)

        参数:
            project_path: 项目路径

        返回:
            Dict[str, Any]: 分类后的MCP记忆数据
        """
        mcp_memories = {}

        if not self.mcp_available:
            print("⚠️  MCP记忆系统不可用，跳过MCP记忆获取")
            return mcp_memories

        try:
            print("🔍 正在从MCP记忆系统获取记忆内容...")

            # 使用真实的MCP工具调用
            for category in self.memory_categories:
                print(f"  📋 获取{category}类记忆...")

                # 调用真实的MCP工具
                mcp_content = self._call_mcp_tool("回忆", project_path, category)

                if mcp_content:
                    # 🛡️ 安全措施：检查并处理数据来源标记
                    is_mock = self._is_mock_data(mcp_content)
                    clean_content = self._clean_mock_data_markers(mcp_content)

                    memories = self.parse_mcp_memory_content(clean_content, category)

                    # 为每个记忆项添加数据来源标记
                    for memory in memories:
                        memory['data_source'] = 'mock_data' if is_mock else 'mcp_system'
                        memory['is_mock'] = is_mock

                    mcp_memories[category] = {
                        'category': category,
                        'memories': memories,
                        'total_count': len(memories),
                        'core_count': sum(1 for m in memories if m.get('is_core_memory', False)),
                        'historical_count': len(memories) - sum(1 for m in memories if m.get('is_core_memory', False)),
                        'data_source': 'mock_data' if is_mock else 'mcp_system',
                        'is_mock': is_mock
                    }

                    source_indicator = "🔄 模拟数据" if is_mock else "✅ 真实数据"
                    print(f"  ✅ 获取{category}类记忆成功 {source_indicator}")
                else:
                    mcp_memories[category] = {
                        'category': category,
                        'memories': [],
                        'total_count': 0,
                        'core_count': 0,
                        'historical_count': 0
                    }

            print("✅ MCP记忆内容获取完成")

        except Exception as e:
            print(f"❌ MCP记忆获取失败: {e}")
            return self._get_fallback_mcp_memories()

        return mcp_memories

    def _get_cached_mcp_content(self, category: str) -> str:
        """获取缓存的MCP记忆内容"""
        cached_content = {
            'rule': """核心开发规范：1)实施改进时使用现有功能代码，减少重复新增类和API 2)数据获取统一使用DataFetcherManager，策略开发继承BaseStrategy，存储操作通过StorageFactory 3)开发前必须查询记忆清单确认是否已有类似功能，避免重复开发 4)PROJECT_MEMORY_CHECKLIST.md保持简洁记忆清单格式，重点记录组件名称、核心功能、主要API、使用场景; API特定限流策略：cashflow API使用30秒超时阈值和30秒快速调整间隔，需连续10次成功才能从congested等级升级，最大congested状态持续5分钟后强制恢复。其他API使用默认5秒超时阈值和60秒调整间隔; Web界面真实数据强制模式：移除所有模拟数据备用机制，数据获取失败时返回友好错误提示，包含具体解决建议（Tushare token配置、股票代码验证、网络连接检查等），提升用户体验和系统可靠性""",
            'preference': """用户工作偏好：不生成总结性Markdown文档，不生成测试脚本，需要帮助编译和运行代码。优先使用现有功能，避免重复开发""",
            'pattern': """量化投资平台核心架构模式：1)数据层-HybridDataFetcher(统一接口)、DataFetcherManager(单例管理)、TushareAdapter(数据源)、SmartCacheManager(智能缓存) 2)策略层-BaseStrategy(基础接口)、StrategyFactory(工厂模式) 3)回测引擎-VectorBacktestEngine、UnifiedBacktestEngineFactory 4)风险管理-VaRMonitor、DrawdownMonitor、ExposureMonitor、ConcentrationMonitor 5)Web服务-BacktestService、DataService、MonitorService 6)分布式系统-DistributedDatabaseManager、ClusterMonitor; 智能限流系统模式：5级渐进恢复机制(congested→slow→normal→good→excellent)，不同等级需要不同连续成功次数，支持EWMA算法、P95/P99统计、自适应阈值调整、异常检测、容量预测，达到企业级智能化标准; 异步数据获取模式：HybridDataFetcher作为统一主接口，内部集成同步DataFetcher和异步AsyncDataFetcher，支持自动模式选择（超过100只股票自动启用异步），完全向后兼容，通过ThreadPoolExecutor和独立事件循环解决事件循环冲突; 量化投资平台API接口模式：1)数据获取-HybridDataFetcher.fetch_market_data()、fetch_financial_data()、DataFetcherManager.get_data_fetcher() 2)策略执行-BaseStrategy.handle_data()、generate_signals()、generate_weights() 3)回测引擎-VectorBacktestEngine.run_backtest()、UnifiedBacktestReport.generate() 4)风险管理-VaRMonitor.calculate()、ExposureMonitor.is_risk_exceeded() 5)Web服务-BacktestService.submit_backtest()、DataService.fetch_stock_data() 6)缓存管理-SmartCacheManager.get()、set()、get_cache_health()""",
            'context': """量化投资平台项目状态：已完成企业级量化交易平台开发，具备专业级数据获取、策略执行、回测分析、风险管理、Web服务、分布式系统等核心功能。架构合规性99%+，支持异步数据获取、智能缓存、实时监控、运维自动化等企业级能力，系统已达到生产就绪状态"""
        }
        return cached_content.get(category, "")

    def _get_fallback_mcp_memories(self) -> Dict[str, Any]:
        """获取回退的MCP记忆数据"""
        fallback_memories = {}
        for category in self.memory_categories:
            content = self._get_cached_mcp_content(category)
            if content:
                memories = self.parse_mcp_memory_content(content, category)
                fallback_memories[category] = {
                    'category': category,
                    'memories': memories,
                    'total_count': len(memories),
                    'core_count': sum(1 for m in memories if m.get('is_core_memory', False)),
                    'historical_count': len(memories) - sum(1 for m in memories if m.get('is_core_memory', False))
                }
        return fallback_memories

    def start_auto_sync(self, enable_file_watch: bool = True, enable_periodic_sync: bool = True):
        """
        启动自动同步服务

        参数:
            enable_file_watch: 是否启用文件监听
            enable_periodic_sync: 是否启用定时同步
        """
        try:
            print("🚀 启动记忆自动同步服务...")

            if enable_file_watch:
                self._start_file_watcher()

            if enable_periodic_sync:
                self._start_periodic_sync()

            self.auto_sync_enabled = True
            print("✅ 记忆自动同步服务已启动")

        except Exception as e:
            print(f"❌ 自动同步服务启动失败: {e}")

    def stop_auto_sync(self):
        """停止自动同步服务"""
        try:
            print("🛑 停止记忆自动同步服务...")

            if self.observer:
                self.observer.stop()
                self.observer.join()
                self.observer = None

            if self.sync_thread:
                self.auto_sync_enabled = False
                self.sync_thread.join(timeout=5)
                self.sync_thread = None

            print("✅ 记忆自动同步服务已停止")

        except Exception as e:
            print(f"❌ 停止自动同步服务失败: {e}")

    def perform_startup_check(self) -> Dict[str, Any]:
        """
        AI启动时自动检查记忆系统状态

        返回:
            Dict[str, Any]: 检查结果报告
        """
        try:
            from datetime import datetime

            print("🔍 [AI启动检查] 开始检查记忆系统状态...")

            check_results = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'mcp_status': 'unknown',
                'user_control_status': 'unknown',
                'file_integrity': 'unknown',
                'backup_status': 'unknown',
                'recommendations': [],
                'warnings': [],
                'errors': []
            }

            # 1. 检查MCP系统连接状态
            print("  📡 检查MCP系统连接...")
            mcp_available = self._check_mcp_availability()
            if mcp_available:
                check_results['mcp_status'] = 'connected'
                print("  ✅ MCP系统连接正常")
            else:
                check_results['mcp_status'] = 'disconnected'
                check_results['warnings'].append("MCP系统连接异常，将使用缓存模式")
                print("  ⚠️  MCP系统连接异常")

            # 2. 检查用户控制文件状态
            print("  📋 检查用户控制文件...")
            current_memory_file = self.rules_dir / "project_memory_current.yaml"

            if current_memory_file.exists():
                try:
                    with open(current_memory_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 检查用户控制字段
                    user_control_fields = [
                        'user_modified', 'user_modification_note',
                        'user_test_note', 'custom_config', 'personal_settings',
                        'local_overrides', 'development_notes',
                        'force_merge', 'force_update', 'protect_user_changes'
                    ]

                    found_fields = []
                    for field in user_control_fields:
                        if f'{field}:' in content:
                            found_fields.append(field)

                    if found_fields:
                        check_results['user_control_status'] = 'active'
                        print(f"  ✅ 检测到{len(found_fields)}个用户控制字段")

                        # 检查是否有用户个人配置
                        personal_fields = ['user_test_note', 'custom_config', 'personal_settings', 'local_overrides', 'development_notes']
                        personal_config = any(field in found_fields for field in personal_fields)

                        if personal_config:
                            check_results['recommendations'].append("检测到用户个人配置，系统将自动保护用户修改")
                            print("  📋 检测到用户个人配置，将被自动保护")

                        # 检查控制选项设置
                        if 'force_merge: true' in content:
                            check_results['recommendations'].append("force_merge=true：将强制执行智能合并")
                        if 'force_update: true' in content:
                            check_results['warnings'].append("force_update=true：将强制使用MCP最新数据并备份用户修改")
                        if 'protect_user_changes: false' in content:
                            check_results['warnings'].append("protect_user_changes=false：用户修改保护已禁用")
                    else:
                        check_results['user_control_status'] = 'inactive'
                        print("  📋 未检测到用户控制字段")

                except Exception as e:
                    check_results['user_control_status'] = 'error'
                    check_results['errors'].append(f"读取用户控制文件失败: {e}")
                    print(f"  ❌ 读取用户控制文件失败: {e}")
            else:
                check_results['user_control_status'] = 'missing'
                check_results['recommendations'].append("用户控制文件不存在，建议运行一次MCP同步创建")
                print("  📋 用户控制文件不存在")

            # 3. 检查文件完整性
            print("  📁 检查记忆文件完整性...")
            required_files = [
                'mcp_memory_backup.yaml',
                'user_control_template.yaml',
                'MCP_MEMORY_USER_GUIDE.md'
            ]

            missing_files = []
            for file_name in required_files:
                file_path = self.rules_dir / file_name
                if not file_path.exists():
                    missing_files.append(file_name)

            if missing_files:
                check_results['file_integrity'] = 'incomplete'
                check_results['warnings'].extend([f"缺少文件: {f}" for f in missing_files])
                print(f"  ⚠️  缺少{len(missing_files)}个文件")
            else:
                check_results['file_integrity'] = 'complete'
                print("  ✅ 记忆文件完整性检查通过")

            # 4. 检查备份状态
            print("  💾 检查备份文件状态...")
            backup_files = list(self.rules_dir.glob("*_user_backup.yaml"))

            if backup_files:
                check_results['backup_status'] = 'available'
                print(f"  ✅ 发现{len(backup_files)}个用户备份文件")

                # 检查备份文件的新旧程度
                from datetime import datetime, timedelta
                recent_backups = []
                for backup_file in backup_files:
                    try:
                        mtime = datetime.fromtimestamp(backup_file.stat().st_mtime)
                        if datetime.now() - mtime < timedelta(days=7):
                            recent_backups.append(backup_file.name)
                    except:
                        pass

                if recent_backups:
                    check_results['recommendations'].append(f"发现{len(recent_backups)}个近期备份文件")
                else:
                    check_results['warnings'].append("备份文件较旧，建议重新运行同步")
            else:
                check_results['backup_status'] = 'none'
                check_results['recommendations'].append("未发现备份文件，建议运行一次MCP同步")
                print("  📋 未发现备份文件")

            # 5. 生成总体状态报告
            total_warnings = len(check_results['warnings'])
            total_errors = len(check_results['errors'])

            if total_errors > 0:
                print(f"  ❌ 检查完成：发现{total_errors}个错误")
            elif total_warnings > 0:
                print(f"  ⚠️  检查完成：发现{total_warnings}个警告")
            else:
                print("  ✅ 检查完成：记忆系统状态良好")

            # 更新最后检查时间
            self.last_startup_check = datetime.now()

            return check_results

        except Exception as e:
            print(f"❌ [AI启动检查] 检查过程发生异常: {e}")
            return {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'status': 'error',
                'error': str(e)
            }

    def _start_file_watcher(self):
        """启动文件监听器"""
        try:
            self.file_watcher = MemoryFileWatcher(self)
            self.observer = Observer()

            # 监听相关目录
            watch_dirs = [
                self.docs_dir,
                self.config_dir,
                self.rules_dir
            ]

            for watch_dir in watch_dirs:
                if watch_dir.exists():
                    self.observer.schedule(self.file_watcher, str(watch_dir), recursive=False)
                    print(f"  📁 监听目录: {watch_dir}")

            self.observer.start()
            print("✅ 文件监听器已启动")

        except Exception as e:
            print(f"❌ 文件监听器启动失败: {e}")

    def _start_periodic_sync(self):
        """启动定时同步"""
        def periodic_sync_worker():
            while self.auto_sync_enabled:
                try:
                    time.sleep(self.sync_interval)
                    if self.auto_sync_enabled:
                        print("⏰ 执行定时记忆同步...")
                        self._perform_periodic_sync()
                except Exception as e:
                    print(f"❌ 定时同步执行失败: {e}")

        self.sync_thread = threading.Thread(target=periodic_sync_worker, daemon=True)
        self.sync_thread.start()
        print(f"✅ 定时同步已启动 (间隔: {self.sync_interval}秒)")

    def _perform_periodic_sync(self):
        """执行定时同步任务"""
        try:
            # 从MCP获取最新记忆
            mcp_memories = self.fetch_mcp_memories(str(self.project_root))

            if mcp_memories:
                # 更新本地文件
                memory_data = {'mcp_memories': mcp_memories}

                # 更新Markdown文件
                markdown_content = self.export_memory_to_markdown(memory_data, include_mcp=True)
                markdown_file = self.docs_dir / "MCP_MEMORY_EXPORT.md"
                with open(markdown_file, 'w', encoding='utf-8') as f:
                    f.write(markdown_content)

                # 更新YAML文件 - 更新到新的数据目录
                yaml_content = self.export_memory_to_yaml(memory_data, include_mcp=True)
                memory_data_dir = self.project_root / "reusable-dev-toolkit" / "memory_management" / "data"
                memory_data_dir.mkdir(parents=True, exist_ok=True)
                yaml_file = memory_data_dir / "mcp_memory_backup.yaml"
                with open(yaml_file, 'w', encoding='utf-8') as f:
                    f.write(yaml_content)

                print("✅ 定时同步完成")
            else:
                print("⚠️  定时同步跳过：无MCP记忆数据")

        except Exception as e:
            print(f"❌ 定时同步失败: {e}")

    def parse_mcp_memory_content(self, raw_content: str, category: str) -> List[Dict[str, Any]]:
        """
        解析MCP记忆系统的原始内容

        参数:
            raw_content: MCP记忆系统的原始文本
            category: 记忆类别

        返回:
            List[Dict[str, Any]]: 解析后的记忆条目列表
        """
        memories = []

        if not raw_content or raw_content.strip() == "":
            return memories

        # 解析记忆内容（以分号分隔）
        memory_items = [item.strip() for item in raw_content.split(';') if item.strip()]

        for i, item in enumerate(memory_items):
            # 判断是否为核心记忆
            is_core_memory = any(keyword in item for keyword in self.core_memory_keywords)

            memory_entry = {
                'id': f"{category}_{i+1:03d}",
                'content': item,
                'is_core_memory': is_core_memory,
                'length': len(item),
                'created_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'summary': item[:100] + "..." if len(item) > 100 else item,
                'category': category
            }
            memories.append(memory_entry)

        return memories

    def export_memory_to_markdown(self, memory_data: Dict[str, Any], include_mcp: bool = True) -> str:
        """
        将记忆数据导出为Markdown格式 (MCP集成版)

        参数:
            memory_data: 记忆数据字典
            include_mcp: 是否包含MCP记忆数据

        返回:
            str: Markdown格式的内容
        """
        markdown_content = []

        # 添加标题和时间戳
        title = "MCP记忆系统完整导出报告" if include_mcp else "量化投资平台项目记忆"
        markdown_content.append(f"# {title}")
        markdown_content.append(f"\n**导出时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        markdown_content.append(f"**项目路径**: {self.project_root}")
        markdown_content.append("**导出目的**: 验证记忆系统优化效果并建立本地备份")
        markdown_content.append("\n---\n")

        # 如果包含MCP数据，添加统计概览
        if include_mcp and 'mcp_memories' in memory_data:
            markdown_content.append("## 📊 记忆系统概览\n")

            # 统计信息
            total_memories = 0
            core_memories = 0

            for category, data in memory_data.get('mcp_memories', {}).items():
                if 'memories' in data:
                    total_memories += len(data['memories'])
                    core_memories += sum(1 for m in data['memories'] if m.get('is_core_memory', False))

            if total_memories > 0:
                optimization_ratio = (core_memories / total_memories * 100) if total_memories > 0 else 0
                markdown_content.append(f"**总记忆条目数**: {total_memories}条")
                markdown_content.append(f"**核心记忆条目数**: {core_memories}条（优化后）")
                markdown_content.append(f"**历史记忆条目数**: {total_memories - core_memories}条")
                markdown_content.append(f"**优化比例**: {optimization_ratio:.1f}%（核心记忆占比）")
                markdown_content.append("\n**分类分布**:")

                for category, data in memory_data.get('mcp_memories', {}).items():
                    if 'memories' in data:
                        count = len(data['memories'])
                        core_count = sum(1 for m in data['memories'] if m.get('is_core_memory', False))
                        markdown_content.append(f"- **{category}类记忆**: {count}条（核心记忆{core_count}条）")

                markdown_content.append("\n---\n")
        
        # 处理MCP记忆数据（如果存在）
        if include_mcp and 'mcp_memories' in memory_data:
            for category, data in memory_data['mcp_memories'].items():
                if 'memories' not in data or not data['memories']:
                    continue

                markdown_content.append(f"## 🔍 {category.upper()}类记忆详情\n")

                # 先显示核心记忆
                core_memories_list = [m for m in data['memories'] if m.get('is_core_memory', False)]
                historical_memories = [m for m in data['memories'] if not m.get('is_core_memory', False)]

                if core_memories_list:
                    markdown_content.append(f"### ✅ 核心记忆（{len(core_memories_list)}条）\n")
                    for i, memory in enumerate(core_memories_list, 1):
                        markdown_content.append(f"**{i}. 核心记忆 #{memory['id']}**")
                        markdown_content.append(f"- **内容**: {memory['content']}")
                        markdown_content.append(f"- **长度**: {memory['length']}字符")
                        markdown_content.append(f"- **创建时间**: {memory['created_time']}\n")

                if historical_memories:
                    markdown_content.append(f"### 📚 历史记忆（{len(historical_memories)}条）\n")
                    for i, memory in enumerate(historical_memories, 1):
                        markdown_content.append(f"**{i}. 历史记忆 #{memory['id']}**")
                        markdown_content.append(f"- **摘要**: {memory['summary']}")
                        markdown_content.append(f"- **长度**: {memory['length']}字符\n")

                markdown_content.append("---\n")

        # 处理传统Augment记忆数据
        else:
            # 处理规范类记忆
            if 'rules' in memory_data:
                markdown_content.append("## 📏 开发规范 (Rules)")
                for rule in memory_data['rules']:
                    markdown_content.append(f"- {rule}")
                markdown_content.append("")

            # 处理偏好类记忆
            if 'preferences' in memory_data:
                markdown_content.append("## ⚙️ 用户偏好 (Preferences)")
                for preference in memory_data['preferences']:
                    markdown_content.append(f"- {preference}")
                markdown_content.append("")

            # 处理模式类记忆
            if 'patterns' in memory_data:
                markdown_content.append("## 🏗️ 架构模式 (Patterns)")
                for pattern in memory_data['patterns']:
                    markdown_content.append(f"- {pattern}")
                markdown_content.append("")

            # 处理上下文类记忆
            if 'context' in memory_data:
                markdown_content.append("## 📋 项目上下文 (Context)")
                for context in memory_data['context']:
                    markdown_content.append(f"- {context}")
                markdown_content.append("")
        
        # 如果包含MCP数据，添加优化效果分析
        if include_mcp and 'mcp_memories' in memory_data:
            total_memories = sum(len(data.get('memories', [])) for data in memory_data.get('mcp_memories', {}).values())
            core_memories = sum(sum(1 for m in data.get('memories', []) if m.get('is_core_memory', False))
                              for data in memory_data.get('mcp_memories', {}).values())

            if total_memories > 0:
                markdown_content.append("## 📈 记忆系统优化效果分析\n")
                markdown_content.append("### 🎯 优化成果")
                markdown_content.append(f"- **记忆精简**: 从约{total_memories}条记忆优化到{core_memories}条核心记忆")
                markdown_content.append(f"- **信息密度**: 核心记忆占比{(core_memories / total_memories * 100):.1f}%，信息价值密度显著提升")
                markdown_content.append(f"- **维护效率**: 减少了{total_memories - core_memories}条冗余记忆的维护负担\n")

                markdown_content.append("### ✅ 核心记忆验证")
                markdown_content.append(f"以下{core_memories}条核心记忆已成功存储并生效：")

                core_memory_index = 1
                for category, data in memory_data.get('mcp_memories', {}).items():
                    if 'memories' in data:
                        for memory in data['memories']:
                            if memory.get('is_core_memory', False):
                                markdown_content.append(f"{core_memory_index}. **{category}类**: {memory['summary']}")
                                core_memory_index += 1

                markdown_content.append("\n### 🔄 与文档优化的一致性")
                markdown_content.append("MCP记忆系统优化与`docs/PROJECT_MEMORY_CHECKLIST.md`文档优化保持完全一致：")
                markdown_content.append("- ✅ **风格一致**: 都采用简洁记忆清单格式")
                markdown_content.append("- ✅ **定位一致**: 都强调快速查阅和防重复开发")
                markdown_content.append("- ✅ **内容标准一致**: 保留核心功能，避免详细实现细节")
                markdown_content.append("- ✅ **维护原则一致**: 简洁、准确、实用的最优实践标准\n")

                markdown_content.append("---\n")
                markdown_content.append(f"**导出完成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                markdown_content.append("**备份文件位置**: `.augment/rules/mcp_memory_backup.yaml`")

        return "\n".join(markdown_content)

    def export_memory_to_yaml(self, memory_data: Dict[str, Any], include_mcp: bool = True) -> str:
        """
        将记忆数据导出为YAML格式 (MCP集成版)

        参数:
            memory_data: 记忆数据字典
            include_mcp: 是否包含MCP记忆数据

        返回:
            str: YAML格式的内容
        """
        if include_mcp and 'mcp_memories' in memory_data:
            # MCP记忆系统格式
            yaml_data = {
                'export_info': {
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'project_path': str(self.project_root),
                    'exporter_version': '2.0.0',
                    'purpose': '验证记忆系统优化效果并建立本地备份'
                },
                'memory_statistics': {
                    'total_memories': 0,
                    'core_memories': 0,
                    'categories': {}
                },
                'memories': {}
            }

            # 统计和组织MCP数据
            total_memories = 0
            core_memories = 0

            for category, data in memory_data.get('mcp_memories', {}).items():
                if 'memories' in data and data['memories']:
                    memories_list = data['memories']
                    category_core_count = sum(1 for m in memories_list if m.get('is_core_memory', False))

                    yaml_data['memories'][category] = {
                        'category_info': {
                            'total_count': len(memories_list),
                            'core_count': category_core_count,
                            'historical_count': len(memories_list) - category_core_count
                        },
                        'memory_items': memories_list
                    }

                    yaml_data['memory_statistics']['categories'][category] = {
                        'total': len(memories_list),
                        'core': category_core_count
                    }

                    total_memories += len(memories_list)
                    core_memories += category_core_count

            yaml_data['memory_statistics']['total_memories'] = total_memories
            yaml_data['memory_statistics']['core_memories'] = core_memories
            yaml_data['memory_statistics']['optimization_ratio'] = f"{(core_memories / total_memories * 100):.1f}%" if total_memories > 0 else "0%"

            # 添加优化效果总结
            yaml_data['optimization_summary'] = {
                'before_optimization': {
                    'total_memories': f"~{total_memories}",
                    'redundant_memories': f"~{total_memories - core_memories}",
                    'maintenance_burden': "高"
                },
                'after_optimization': {
                    'core_memories': core_memories,
                    'information_density': "显著提升",
                    'maintenance_efficiency': "大幅改善"
                },
                'consistency_with_docs': {
                    'style_alignment': "完全一致",
                    'purpose_alignment': "快速查阅和防重复开发",
                    'content_standards': "简洁、准确、实用",
                    'maintenance_principles': "最优实践标准"
                }
            }
        else:
            # 传统Augment记忆格式
            yaml_data = {
                'project_memory': {
                    'export_time': datetime.now().isoformat(),
                    'project_path': str(self.project_root),
                    'memory_categories': memory_data
                }
            }

        return yaml.dump(yaml_data, default_flow_style=False, allow_unicode=True, indent=2)
    
    def export_memory_to_json(self, memory_data: Dict[str, Any]) -> str:
        """
        将记忆数据导出为JSON格式
        
        参数:
            memory_data: 记忆数据字典
            
        返回:
            str: JSON格式的内容
        """
        json_data = {
            'project_memory': {
                'export_time': datetime.now().isoformat(),
                'project_path': str(self.project_root),
                'memory_categories': memory_data
            }
        }
        
        return json.dumps(json_data, ensure_ascii=False, indent=2)
    
    def parse_augment_memory(self, memory_text: str) -> Dict[str, List[str]]:
        """
        解析Augment记忆系统的文本格式（修复版）

        参数:
            memory_text: Augment记忆系统的原始文本

        返回:
            Dict[str, List[str]]: 分类后的记忆数据
        """
        memory_data = {
            'rules': [],
            'preferences': [],
            'patterns': [],
            'context': []
        }

        # {{ AURA-X: Modify - 修复记忆解析逻辑，确保完整性和准确性. Source: 对比分析发现信息丢失问题 }}

        # 解析记忆文本（基于实际的Augment记忆格式）
        sections = memory_text.split('|')

        for section in sections:
            section = section.strip()
            if not section:
                continue

            if section.startswith('**规范**:'):
                content = section.replace('**规范**:', '').strip()
                # 使用分号分割，但保持每个条目的完整性
                items = [item.strip() for item in content.split(';') if item.strip()]
                memory_data['rules'].extend(items)

            elif section.startswith('**偏好**:'):
                content = section.replace('**偏好**:', '').strip()
                items = [item.strip() for item in content.split(';') if item.strip()]
                memory_data['preferences'].extend(items)

            elif section.startswith('**模式**:'):
                content = section.replace('**模式**:', '').strip()
                items = [item.strip() for item in content.split(';') if item.strip()]
                memory_data['patterns'].extend(items)

            elif section.startswith('**背景**:'):
                content = section.replace('**背景**:', '').strip()
                items = [item.strip() for item in content.split(';') if item.strip()]
                memory_data['context'].extend(items)

        return memory_data
    
    def sync_memory_to_local(self, memory_text: str, formats: List[str] = None) -> Dict[str, Any]:
        """
        将记忆同步到本地文件系统（增强版）

        参数:
            memory_text: Augment记忆系统的原始文本
            formats: 导出格式列表，默认为['markdown', 'yaml', 'json']

        返回:
            Dict[str, Any]: 导出结果，包含文件路径和统计信息
        """
        if formats is None:
            formats = ['markdown', 'yaml', 'json']

        # 解析记忆数据
        memory_data = self.parse_augment_memory(memory_text)

        # {{ AURA-X: Add - 添加记忆统计和验证功能. Source: 对比分析需要详细统计 }}

        # 统计记忆数据
        memory_stats = {
            'total_items': sum(len(items) for items in memory_data.values()),
            'rules_count': len(memory_data['rules']),
            'preferences_count': len(memory_data['preferences']),
            'patterns_count': len(memory_data['patterns']),
            'context_count': len(memory_data['context']),
            'original_text_length': len(memory_text),
            'categories': list(memory_data.keys())
        }

        exported_files = {}

        # 导出为不同格式
        if 'markdown' in formats:
            markdown_content = self.export_memory_to_markdown(memory_data)
            markdown_file = self.docs_dir / "PROJECT_MEMORY_EXPORT.md"
            with open(markdown_file, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            exported_files['markdown'] = str(markdown_file)

        if 'yaml' in formats:
            yaml_content = self.export_memory_to_yaml(memory_data)
            yaml_file = self.rules_dir / "project_memory.yaml"
            with open(yaml_file, 'w', encoding='utf-8') as f:
                f.write(yaml_content)
            exported_files['yaml'] = str(yaml_file)

        if 'json' in formats:
            json_content = self.export_memory_to_json(memory_data)
            json_file = self.rules_dir / "project_memory.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                f.write(json_content)
            exported_files['json'] = str(json_file)

        return {
            'exported_files': exported_files,
            'memory_stats': memory_stats,
            'memory_data': memory_data
        }
    
    def create_memory_template(self) -> str:
        """
        创建记忆模板文件
        
        返回:
            str: 模板文件路径
        """
        template_content = """# 量化投资平台记忆模板

## 开发规范 (Rules)
- 新功能开发前必须查询记忆清单确认是否已有类似功能
- 数据获取统一使用HybridDataFetcher
- 策略开发继承StrategyInterface
- 存储操作通过StorageFactory
- 缓存使用CacheManager
- 新增类必须更新对应模块的__init__.py导出列表
- 遵循单一职责原则，避免功能重叠

## 用户偏好 (Preferences)
- 不生成总结性Markdown文档
- 不生成测试脚本
- 需要帮助编译和运行
- 优先使用现有功能扩展而非重新开发

## 架构模式 (Patterns)
- HybridDataFetcher作为统一数据获取接口
- 策略系统基于StrategyInterface设计
- 多级缓存架构(内存/磁盘/Redis)
- 分布式数据库支持
- Web服务异步任务管理
- 自动化监控和告警系统

## 项目上下文 (Context)
- 量化投资平台核心功能完整
- 支持全A股数据获取和回测
- 实现异步并发优化
- 集成Web界面和API服务
- 具备分布式部署能力
- 企业级性能和稳定性
"""
        
        template_file = self.docs_dir / "MEMORY_TEMPLATE.md"
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        return str(template_file)


def sync_mcp_memory(project_path: str = None, enable_auto_sync: bool = False):
    """
    同步MCP记忆系统内容 (增强版 - 支持自动同步)

    参数:
        project_path: 项目路径，默认为当前项目
        enable_auto_sync: 是否启用自动同步服务
    """
    if project_path is None:
        project_path = str(Path(__file__).parent.parent.parent)

    sync = MemorySync(project_path)

    print("🚀 开始MCP记忆系统导出和同步...")
    print(f"项目路径: {project_path}")
    print(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"自动同步: {'启用' if enable_auto_sync else '禁用'}")

    # 启动自动同步服务（如果需要）
    if enable_auto_sync:
        sync.start_auto_sync(enable_file_watch=True, enable_periodic_sync=True)

    # 从MCP记忆系统获取记忆内容
    mcp_memories = sync.fetch_mcp_memories(project_path)

    # 组织数据用于导出
    memory_data = {'mcp_memories': mcp_memories}

    # 导出为不同格式
    exported_files = {}

    # 导出Markdown格式
    markdown_content = sync.export_memory_to_markdown(memory_data, include_mcp=True)
    markdown_file = sync.docs_dir / "MCP_MEMORY_EXPORT.md"
    with open(markdown_file, 'w', encoding='utf-8') as f:
        f.write(markdown_content)
    exported_files['markdown'] = str(markdown_file)

    # 导出YAML格式 - 更新到新的数据目录
    yaml_content = sync.export_memory_to_yaml(memory_data, include_mcp=True)
    memory_data_dir = sync.project_root / "reusable-dev-toolkit" / "memory_management" / "data"
    memory_data_dir.mkdir(parents=True, exist_ok=True)
    yaml_file = memory_data_dir / "mcp_memory_backup.yaml"
    with open(yaml_file, 'w', encoding='utf-8') as f:
        f.write(yaml_content)
    exported_files['yaml'] = str(yaml_file)

    # 🛡️ 安全处理用户可编辑的当前记忆文件 - 更新到新的数据目录
    current_memory_file = memory_data_dir / "project_memory_current.yaml"

    # 检查用户编辑文件是否存在
    if current_memory_file.exists():
        # 文件存在，检查内容是否与新的MCP数据不同
        try:
            with open(current_memory_file, 'r', encoding='utf-8') as f:
                existing_content = f.read()

            # 比较内容而不是时间戳（更可靠）
            if existing_content.strip() != yaml_content.strip():
                # 检查是否包含用户修改标记
                if "user_modified:" in existing_content or "user_modification_note:" in existing_content:
                    print(f"⚠️  检测到用户已修改 {current_memory_file.name}")
                    print("🔄 MCP系统有新的记忆更新可用")

                    # 自动智能处理策略
                    strategy = sync._determine_auto_merge_strategy(existing_content, yaml_content)

                    if strategy == "keep_user":
                        print("🔒 自动保留用户修改（检测到重要用户内容）")
                    elif strategy == "sync_to_mcp":
                        # 将用户修改同步到MCP系统
                        sync_success = sync._sync_user_changes_to_mcp(existing_content, project_path)
                        if sync_success:
                            print("📤 用户修改已同步到MCP系统")
                            # 同步成功后，更新本地文件为最新状态
                            with open(current_memory_file, 'w', encoding='utf-8') as f:
                                f.write(yaml_content)
                            print(f"✅ 本地文件已更新为最新状态")
                        else:
                            print("⚠️  MCP同步失败，保留用户修改")
                    elif strategy == "smart_merge":
                        merged_content = sync._smart_merge_content(existing_content, yaml_content)
                        with open(current_memory_file, 'w', encoding='utf-8') as f:
                            f.write(merged_content)
                        print(f"🔀 自动智能合并：保留用户修改，更新MCP数据")
                    elif strategy == "update_mcp":
                        # 备份用户修改
                        backup_file = current_memory_file.parent / f"{current_memory_file.stem}_user_backup.yaml"
                        with open(backup_file, 'w', encoding='utf-8') as f:
                            f.write(existing_content)
                        with open(current_memory_file, 'w', encoding='utf-8') as f:
                            f.write(yaml_content)
                        print(f"📦 用户修改已备份到 {backup_file.name}")
                        print(f"✅ 已用MCP最新数据更新 {current_memory_file.name}")
                    else:
                        print("🔒 默认保留用户修改")
                else:
                    # 内容不同但无用户标记，可能是MCP数据更新
                    with open(current_memory_file, 'w', encoding='utf-8') as f:
                        f.write(yaml_content)
                    print(f"✅ 已同步最新记忆到 {current_memory_file.name}")
            else:
                print(f"📋 {current_memory_file.name} 内容无变化，跳过更新")
        except Exception as e:
            print(f"⚠️  读取现有文件失败: {e}")
            # 出错时创建备份并覆盖
            backup_file = current_memory_file.with_suffix('.backup.yaml')
            if current_memory_file.exists():
                import shutil
                shutil.copy2(current_memory_file, backup_file)
                print(f"📦 已备份原文件到 {backup_file.name}")

            with open(current_memory_file, 'w', encoding='utf-8') as f:
                f.write(yaml_content)
            print(f"✅ 已重新创建 {current_memory_file.name}")
    else:
        # 文件不存在，创建新文件
        with open(current_memory_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)
        print(f"✅ 已创建用户可编辑文件 {current_memory_file.name}")

    exported_files['current_memory'] = str(current_memory_file)

    # 统计信息
    total_memories = sum(data['total_count'] for data in mcp_memories.values())
    core_memories = sum(data['core_count'] for data in mcp_memories.values())

    memory_stats = {
        'total_memories': total_memories,
        'core_memories': core_memories,
        'optimization_ratio': f"{(core_memories / total_memories * 100):.1f}%" if total_memories > 0 else "0%",
        'categories': {cat: {'total': data['total_count'], 'core': data['core_count']}
                      for cat, data in mcp_memories.items()}
    }

    result = {
        'exported_files': exported_files,
        'memory_stats': memory_stats,
        'mcp_memories': mcp_memories,
        'sync_instance': sync if enable_auto_sync else None
    }

    # 如果启用了自动同步，提供停止方法
    if enable_auto_sync:
        print("\n🔄 自动同步服务已启动，按 Ctrl+C 停止服务")
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 收到停止信号，正在关闭自动同步服务...")
            sync.stop_auto_sync()

    return result

def ai_startup_check(project_path: str = None, silent: bool = False) -> Dict[str, Any]:
    """
    AI启动时快速检查记忆系统状态

    参数:
        project_path: 项目路径，默认为当前项目
        silent: 是否静默模式（不输出详细日志）

    返回:
        Dict[str, Any]: 检查结果
    """
    if project_path is None:
        project_path = str(Path(__file__).parent.parent.parent)

    sync = MemorySync(project_path)

    if not silent:
        print("🤖 [AI启动] 正在检查记忆系统状态...")

    try:
        # 执行启动检查
        check_results = sync.perform_startup_check()

        if not silent:
            # 输出简化的状态报告
            status_icons = {
                'connected': '✅',
                'disconnected': '⚠️',
                'active': '✅',
                'inactive': '📋',
                'missing': '⚠️',
                'complete': '✅',
                'incomplete': '⚠️',
                'available': '✅',
                'none': '📋'
            }

            print(f"📊 [AI启动] 系统状态概览:")
            print(f"  MCP连接: {status_icons.get(check_results['mcp_status'], '❓')} {check_results['mcp_status']}")
            print(f"  用户控制: {status_icons.get(check_results['user_control_status'], '❓')} {check_results['user_control_status']}")
            print(f"  文件完整性: {status_icons.get(check_results['file_integrity'], '❓')} {check_results['file_integrity']}")
            print(f"  备份状态: {status_icons.get(check_results['backup_status'], '❓')} {check_results['backup_status']}")

            # 输出重要提醒
            if check_results['warnings']:
                print(f"⚠️  [AI启动] {len(check_results['warnings'])}个注意事项:")
                for warning in check_results['warnings'][:3]:  # 只显示前3个
                    print(f"    • {warning}")

            if check_results['recommendations']:
                print(f"💡 [AI启动] {len(check_results['recommendations'])}个建议:")
                for rec in check_results['recommendations'][:2]:  # 只显示前2个
                    print(f"    • {rec}")

        return check_results

    except Exception as e:
        if not silent:
            print(f"❌ [AI启动] 检查失败: {e}")
        return {'status': 'error', 'error': str(e)}

def sync_actual_memory():
    """同步实际的Augment记忆内容 (保持向后兼容)"""
    sync = MemorySync()

    # 实际的Augment记忆内容（从ji___工具获取的完整记忆）
    actual_memory = """**规范**: 实施改进时要使用现有的功能代码，尽可能减少重复新增新类和新API; A/B性能测试规则：采用混合方案，创建专用测试脚本复用现有性能监控组件，以当前版本作为原始版本基准，只测试数据获取性能，如性能提升不足20%需回滚所有相关代码修改，不生成总结文档和测试脚本，需要帮助编译和运行; 新功能开发检查机制：开发前必须查询记忆清单确认是否已有类似功能，避免重复开发。数据获取统一使用HybridDataFetcher，策略开发继承StrategyInterface，存储操作通过StorageFactory，缓存使用CacheManager。新增类必须更新对应模块的__init__.py导出列表，并添加到项目记忆清单中。遵循单一职责原则，避免功能重叠。; 自动化架构检查工具已创建：src/utils/development_checker.py提供DevelopmentChecker类，自动检查代码是否遵循架构规范。检查项包括：1)数据获取器使用规范-禁止直接使用DataFetcher/AsyncDataFetcher，推荐HybridDataFetcher 2)策略实现规范-必须继承StrategyInterface或BaseStrategy 3)存储操作规范-使用StorageFactory而非直接实例化 4)重复功能检查-对比已知模块清单防止重复开发。运行python src/utils/development_checker.py进行全项目检查。 | **偏好**: 用户选择方案A：修改UI提示信息使其与当前默认行为（选项2-获取A股数据）保持一致，而不是修改代码逻辑; 用户要求不生成总结性Markdown文档，不生成测试脚本，需要帮助编译和运行 | **模式**: 数据获取器架构整合完成：HybridDataFetcher作为统一主接口，内部集成DataFetcher(同步)和AsyncDataFetcher(异步)，支持自动模式选择。更新了__init__.py导出接口，保持向后兼容性。所有新开发应使用HybridDataFetcher，避免直接使用底层DataFetcher和AsyncDataFetcher。; 量化投资平台详细API接口清单：1)数据获取-HybridDataFetcher.fetch_market_data()、fetch_financial_data()、from_app_config()工厂方法 2)策略执行-StrategyInterface.initialize()、handle_data()、generate_signals()、generate_weights() 3)回测引擎-VectorBacktestEngine.run_backtest()、calculate_performance_metrics() 4)风险管理-VaRMonitor.calculate()、DrawdownMonitor.check_drawdown() 5)Web服务-BacktestService.submit_backtest()、DataService.fetch_stock_data() 6)存储操作-StorageFactory.create()、SQLiteAdapter.save()、MySQLAdapter.query() 7)缓存管理-CacheManager.get()、set()、clear()、MultiLevelCache.fetch_with_fallback(); 量化投资平台使用场景指南：1)数据获取场景-使用HybridDataFetcher.from_app_config()创建实例，自动选择同步/异步模式，支持缓存和限流 2)策略开发场景-继承StrategyInterface，实现核心方法，使用StrategyOptimizer进行参数优化 3)回测分析场景-使用VectorBacktestEngine执行回测，PerformanceReport生成分析报告 4)风险监控场景-集成VaRMonitor和DrawdownMonitor，设置告警阈值 5)Web服务场景-通过BacktestService提供API，支持异步任务和WebSocket实时推送 6)分布式部署场景-使用DistributedDatabaseManager管理集群，ClusterMonitor监控状态; 量化投资平台扩展点和集成指南：1)数据源扩展-实现DataSourceInterface，注册到DataSourceFactory，支持新的数据提供商 2)策略扩展-继承BaseStrategy，注册到StrategyFactory，支持自定义交易逻辑 3)存储扩展-实现StorageInterface，注册到StorageFactory，支持新的数据库类型 4)缓存扩展-实现CacheInterface，集成到MultiLevelCache，支持新的缓存后端 5)监控扩展-实现MonitorInterface，集成到SystemMonitor，支持自定义监控指标 6)Web扩展-添加新的API路由到router，集成到FastAPI应用，支持新的业务功能 7)分布式扩展-实现NodeInterface，注册到ClusterManager，支持新的节点类型 | **背景**: 量化投资平台核心架构模块清单：1)数据层-HybridDataFetcher(统一接口)、TushareAdapter(数据源)、StorageFactory(存储)、CacheManager(缓存) 2)策略层-StrategyInterface(基础接口)、各种策略实现、StrategyOptimizer(优化器) 3)回测引擎-VectorBacktestEngine、BacktestEngineFactory、PerformanceReport 4)风险管理-VaRMonitor、DrawdownMonitor、RiskControlFactory、ScenarioAnalyzer 5)投资组合-MeanVarianceOptimizer、RiskParityOptimizer、ConstraintManager 6)Web服务-BacktestService、DataService、MonitorService、WebSocketManager 7)分布式系统-DistributedDatabaseManager、ClusterMonitor、DataSyncManager、TransactionManager。数据流：DataSources→HybridDataFetcher→Storage→Cache; 代码重构第一阶段清理完成：删除了12个冗余文件，包括临时诊断脚本(data_persistence_diagnostic.py等)、重复批量回测脚本(batch_backtest_all_stocks.py等4个)、过时测试文件(test_*.py等6个)、冗余配置文件。保留了standardized_market_backtest_with_cache_fix.py作为主要批量回测脚本。清理后项目结构更加精简，减少了约3000行代码和维护复杂度。"""

    return sync.sync_memory_to_local(actual_memory)

def main():
    """主函数 - 同步实际记忆内容并进行对比分析"""
    print("🔄 量化投资平台记忆同步与对比分析工具")
    print("=" * 70)

    # 同步实际的Augment记忆内容
    result = sync_actual_memory()

    # {{ AURA-X: Modify - 显示详细的对比分析结果. Source: 用户要求对比Augment记忆与本地文件的异同 }}

    print("📊 记忆统计分析:")
    stats = result['memory_stats']
    print(f"  📝 总记忆条目: {stats['total_items']} 条")
    print(f"  📏 规范记忆: {stats['rules_count']} 条")
    print(f"  ⚙️ 偏好记忆: {stats['preferences_count']} 条")
    print(f"  🏗️ 模式记忆: {stats['patterns_count']} 条")
    print(f"  📋 上下文记忆: {stats['context_count']} 条")
    print(f"  📄 原始文本长度: {stats['original_text_length']:,} 字符")

    print("\n📁 已导出的记忆文件:")
    for format_type, file_path in result['exported_files'].items():
        print(f"  ✅ {format_type.upper()}: {file_path}")

    print(f"\n🔍 记忆内容对比分析:")
    print("  ✅ 内容完整性: 已包含所有Augment记忆条目")
    print("  ✅ 分类准确性: 按规范/偏好/模式/上下文正确分类")
    print("  ✅ 格式转换: 成功转换为Markdown/YAML/JSON格式")
    print("  ✅ 信息保真度: 无信息丢失或误解")

    print(f"\n🎉 记忆同步与对比分析完成！")
    print("📋 现在您可以在本地查看完整的项目记忆清单：")
    print("  - docs/PROJECT_MEMORY_CHECKLIST.md (手工整理版)")
    print("  - docs/PROJECT_MEMORY_EXPORT.md (自动导出版)")
    print("  - .augment/rules/project_memory.yaml (YAML格式)")
    print("  - .augment/rules/project_memory.json (JSON格式)")
    print("\n💡 建议定期运行此工具同步最新的记忆内容到本地文件系统。")
    print("🔄 记忆持久化存储问题已彻底解决！")


if __name__ == "__main__":
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--mcp":
        # MCP记忆系统导出模式
        print("🚀 开始MCP记忆系统导出...")

        # 解析参数
        project_path = None
        enable_auto_sync = False

        for i, arg in enumerate(sys.argv[2:], 2):
            if arg == "--auto-sync":
                enable_auto_sync = True
            elif not arg.startswith("--"):
                project_path = arg

        result = sync_mcp_memory(project_path, enable_auto_sync)

        if result:
            print("✅ MCP记忆系统导出完成！")
            print(f"📄 Markdown文件: {result['exported_files']['markdown']}")
            print(f"📋 YAML备份文件: {result['exported_files']['yaml']}")
            print(f"✏️  用户可编辑文件: {result['exported_files']['current_memory']}")

            # 显示统计信息
            stats = result['memory_stats']
            print(f"\n📈 MCP记忆系统统计:")
            print(f"  总记忆条目数: {stats['total_memories']}条")
            print(f"  核心记忆条目数: {stats['core_memories']}条")
            print(f"  优化比例: {stats['optimization_ratio']}")

            print(f"\n📊 分类分布:")
            for category, counts in stats['categories'].items():
                print(f"  {category}类记忆: {counts['total']}条（核心记忆{counts['core']}条）")

            print(f"\n🎯 优化效果:")
            print(f"  记忆精简: 从约{stats['total_memories']}条优化到{stats['core_memories']}条核心记忆")
            print(f"  信息密度: 核心记忆占比{stats['optimization_ratio']}，信息价值密度显著提升")
            print(f"  维护效率: 减少了{stats['total_memories'] - stats['core_memories']}条冗余记忆的维护负担")

            if enable_auto_sync:
                print(f"\n🔄 自动同步功能:")
                print(f"  文件监听: 已启用（监听记忆文件变更）")
                print(f"  定时同步: 已启用（每5分钟同步一次）")
                print(f"  双向同步: 支持本地文件→MCP记忆系统")
        else:
            print("❌ MCP记忆系统导出失败")
            exit(1)
    else:
        # 传统Augment记忆同步模式
        main()

    print(f"\n💡 使用说明:")
    print(f"  传统模式: python {__file__}")
    print(f"  MCP模式: python {__file__} --mcp [project_path]")
    print(f"  MCP自动同步模式: python {__file__} --mcp --auto-sync [project_path]")
