#!/usr/bin/env python3
"""
真实MCP工具调用演示脚本

此脚本演示如何在实际的MCP环境中调用真实的ji___工具。
它包含了完整的MCP工具集成逻辑，可以在支持MCP的环境中直接运行。

使用方法:
    python3 real_mcp_demo.py

功能:
1. 检测MCP环境可用性
2. 调用真实的ji___工具进行记忆操作
3. 演示完整的MCP工具集成流程
4. 提供详细的调用日志和错误处理
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# 导入MCP工具注入器
from mcp_tool_injector import MCPToolInjector, inject_mcp_tool_globally


class RealMCPToolCaller:
    """真实MCP工具调用器"""
    
    def __init__(self, project_path: str = None):
        """
        初始化MCP工具调用器

        参数:
            project_path: 项目路径，默认为当前项目
        """
        self.project_path = project_path or str(project_root)
        self.mcp_available = False
        self.ji_tool = None
        self.injector = None

        # 首先尝试注入MCP工具
        self._inject_mcp_tool()

        # 检测MCP环境
        self._detect_mcp_environment()
        
    def _detect_mcp_environment(self):
        """检测MCP环境可用性"""
        print("🔍 检测MCP环境可用性...")

        try:
            # 使用增强的MCP工具检测方法
            self.ji_tool = self._get_real_mcp_tool()

            if self.ji_tool is not None:
                self.mcp_available = True
                print("✅ MCP环境检测成功: ji___工具已可用")

                # 进行连接测试
                try:
                    test_result = self.ji_tool(action="回忆", project_path=self.project_path, category="rule")
                    print("✅ MCP工具连接测试成功")
                except Exception as test_error:
                    print(f"⚠️  MCP工具连接测试失败: {test_error}")
                    print("🔧 工具可用但可能存在配置问题")
            else:
                self.mcp_available = False
                print("❌ MCP环境检测失败: 无法访问ji___工具")
                print("🔧 这可能意味着:")
                print("   1. 当前不在支持MCP的环境中运行")
                print("   2. ji___工具未正确加载")
                print("   3. 需要在Augment Agent环境中运行此脚本")

        except Exception as e:
            print(f"❌ MCP环境检测异常: {e}")
            self.mcp_available = False

    def _inject_mcp_tool(self):
        """注入MCP工具"""
        try:
            print("🔧 尝试注入MCP工具...")
            self.injector = MCPToolInjector(self.project_path)
            injection_success = self.injector.inject_mcp_tool()

            if injection_success:
                print("✅ MCP工具注入成功")
                # 获取注入状态
                status = self.injector.get_injection_status()
                print(f"📊 注入状态: 真实MCP={status['is_real_mcp']}, 工具可用={status['tool_available']}")
            else:
                print("❌ MCP工具注入失败")

        except Exception as e:
            print(f"❌ MCP工具注入异常: {e}")

    def _get_real_mcp_tool(self):
        """
        获取真实的MCP工具实例

        返回:
            callable: MCP工具函数，失败时返回None
        """
        try:
            # 方法1: 尝试从全局命名空间获取
            if 'ji___' in globals():
                print("  ✅ 通过globals()获取到ji___工具")
                return globals()['ji___']

            # 方法2: 尝试从builtins获取
            import builtins
            if hasattr(builtins, 'ji___'):
                print("  ✅ 通过builtins获取到ji___工具")
                return getattr(builtins, 'ji___')

            # 方法3: 尝试从当前模块的全局变量获取
            import sys
            current_module = sys.modules[__name__]
            if hasattr(current_module, 'ji___'):
                print("  ✅ 通过当前模块获取到ji___工具")
                return getattr(current_module, 'ji___')

            # 方法4: 尝试从sys.modules中查找
            for module_name, module in sys.modules.items():
                if hasattr(module, 'ji___'):
                    print(f"  ✅ 通过sys.modules['{module_name}']获取到ji___工具")
                    return getattr(module, 'ji___')

            # 方法5: 尝试直接导入
            try:
                from ji___ import ji___
                print("  ✅ 通过导入获取到ji___工具")
                return ji___
            except ImportError:
                pass

            # 方法6: 尝试通过inspect模块查找调用栈中的ji___
            import inspect
            for frame_info in inspect.stack():
                frame_globals = frame_info.frame.f_globals
                if 'ji___' in frame_globals:
                    print("  ✅ 通过调用栈获取到ji___工具")
                    return frame_globals['ji___']

            # 方法7: 尝试通过exec动态执行获取
            try:
                exec_globals = {}
                exec("import ji___", exec_globals)
                if 'ji___' in exec_globals:
                    print("  ✅ 通过exec动态执行获取到ji___工具")
                    return exec_globals['ji___']
            except Exception:
                pass

            print("  ❌ 所有方法都无法获取ji___工具")
            return None

        except Exception as e:
            print(f"  ❌ 获取MCP工具时发生异常: {e}")
            return None
            
    def call_real_mcp_tool(self, action: str, category: str = None, content: str = None) -> Optional[Dict[str, Any]]:
        """
        调用真实的MCP工具
        
        参数:
            action: 操作类型 ("记忆" 或 "回忆")
            category: 记忆分类
            content: 记忆内容（仅在添加记忆时需要）
            
        返回:
            Dict: MCP工具调用结果
        """
        if not self.mcp_available:
            print("❌ MCP工具不可用，无法执行真实调用")
            return {
                'success': False,
                'error': 'MCP工具不可用',
                'fallback_mode': True
            }
        
        try:
            print(f"🚀 调用真实MCP工具: action={action}, category={category}")
            
            # 准备调用参数
            call_params = {
                'action': action,
                'project_path': self.project_path
            }
            
            if category:
                call_params['category'] = category
                
            if content:
                call_params['content'] = content
            
            print(f"  📋 调用参数: {call_params}")
            
            # 执行真实的MCP工具调用
            print("  🔧 执行MCP工具调用...")
            result = self.ji_tool(**call_params)
            
            print(f"  ✅ MCP工具调用成功")
            print(f"  📄 返回结果类型: {type(result)}")
            print(f"  📄 返回结果内容: {str(result)[:200]}...")
            
            return {
                'success': True,
                'result': result,
                'call_params': call_params,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ MCP工具调用失败: {e}")
            print(f"  🔍 错误类型: {type(e).__name__}")
            print(f"  🔍 错误详情: {str(e)}")
            
            return {
                'success': False,
                'error': str(e),
                'error_type': type(e).__name__,
                'call_params': call_params if 'call_params' in locals() else None,
                'timestamp': datetime.now().isoformat()
            }
    
    def demo_memory_operations(self):
        """演示记忆操作"""
        print("\n" + "="*60)
        print("🎯 开始MCP工具调用演示")
        print("="*60)
        
        # 演示1: 获取规范类记忆
        print("\n📋 演示1: 获取规范类记忆")
        print("-" * 40)
        result1 = self.call_real_mcp_tool("回忆", "rule")
        self._print_result(result1)
        
        # 演示2: 获取偏好类记忆
        print("\n📋 演示2: 获取偏好类记忆")
        print("-" * 40)
        result2 = self.call_real_mcp_tool("回忆", "preference")
        self._print_result(result2)
        
        # 演示3: 添加测试记忆
        print("\n💾 演示3: 添加测试记忆")
        print("-" * 40)
        test_content = f"真实MCP工具调用测试 - {datetime.now().strftime('%Y%m%d_%H%M%S')}"
        result3 = self.call_real_mcp_tool("记忆", "rule", test_content)
        self._print_result(result3)
        
        # 演示4: 验证添加的记忆
        print("\n🔍 演示4: 验证添加的记忆")
        print("-" * 40)
        result4 = self.call_real_mcp_tool("回忆", "rule")
        self._print_result(result4)
        
        # 生成演示报告
        self._generate_demo_report([result1, result2, result3, result4])
    
    def _print_result(self, result: Dict[str, Any]):
        """打印调用结果"""
        if result['success']:
            print("✅ 调用成功")
            if 'result' in result:
                result_content = str(result['result'])
                if len(result_content) > 100:
                    print(f"  📄 结果内容: {result_content[:100]}...")
                else:
                    print(f"  📄 结果内容: {result_content}")
        else:
            print("❌ 调用失败")
            print(f"  🔍 错误信息: {result.get('error', '未知错误')}")
            if result.get('fallback_mode'):
                print("  🔄 建议: 在支持MCP的环境中运行此脚本")
    
    def _generate_demo_report(self, results: list):
        """生成演示报告"""
        print("\n" + "="*60)
        print("📊 MCP工具调用演示报告")
        print("="*60)
        
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        success_rate = (success_count / total_count * 100) if total_count > 0 else 0
        
        print(f"总调用次数: {total_count}")
        print(f"成功次数: {success_count}")
        print(f"失败次数: {total_count - success_count}")
        print(f"成功率: {success_rate:.1f}%")
        
        print(f"\nMCP环境状态: {'✅ 可用' if self.mcp_available else '❌ 不可用'}")
        
        if self.mcp_available:
            print("🎉 恭喜！您正在支持MCP的环境中运行")
            print("🔧 ji___工具已成功集成并可以正常使用")
        else:
            print("⚠️  当前环境不支持MCP工具调用")
            print("💡 建议在Augment Agent环境中运行此脚本以获得完整功能")
        
        # 保存报告
        report_file = Path(self.project_path) / "docs" / "REAL_MCP_DEMO_REPORT.md"
        self._save_demo_report(report_file, results, success_rate)
        print(f"\n📄 演示报告已保存: {report_file}")
    
    def _save_demo_report(self, report_file: Path, results: list, success_rate: float):
        """保存演示报告到文件"""
        try:
            report_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("# 真实MCP工具调用演示报告\n\n")
                f.write(f"**演示时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"**项目路径**: {self.project_path}\n")
                f.write(f"**MCP环境状态**: {'可用' if self.mcp_available else '不可用'}\n")
                f.write(f"**成功率**: {success_rate:.1f}%\n\n")
                
                f.write("## 调用结果详情\n\n")
                for i, result in enumerate(results, 1):
                    f.write(f"### 演示{i}\n")
                    f.write(f"- **状态**: {'成功' if result['success'] else '失败'}\n")
                    if result['success']:
                        f.write(f"- **结果**: {str(result.get('result', ''))[:100]}...\n")
                    else:
                        f.write(f"- **错误**: {result.get('error', '未知错误')}\n")
                    f.write(f"- **时间**: {result.get('timestamp', '')}\n\n")
                
                f.write("## 技术说明\n\n")
                f.write("此演示脚本展示了如何在实际的MCP环境中调用真实的ji___工具。\n")
                f.write("如果您看到'MCP环境不可用'的消息，请在支持MCP的环境中运行此脚本。\n\n")
                
        except Exception as e:
            print(f"⚠️  保存演示报告失败: {e}")


def main():
    """主函数"""
    print("🚀 真实MCP工具调用演示")
    print(f"项目路径: {project_root}")
    print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建MCP工具调用器
    mcp_caller = RealMCPToolCaller()
    
    # 运行演示
    mcp_caller.demo_memory_operations()


if __name__ == "__main__":
    main()
