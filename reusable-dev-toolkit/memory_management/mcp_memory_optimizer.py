#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP记忆优化器
由于ji___工具不支持DELETE操作，通过创建优化版本的记忆导出来清理冗余内容

{{ AURA-X: Add - 创建MCP记忆优化器. Source: 需要清理MCP记忆中的冗余规则内容 }}
"""

import yaml
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Set


class MCPMemoryOptimizer:
    """
    MCP记忆优化器
    
    由于无法直接删除MCP记忆，通过创建优化版本来清理冗余
    """
    
    def __init__(self, project_root: str = None):
        """
        初始化优化器
        
        参数:
            project_root: 项目根目录路径
        """
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent.parent
        self.mcp_memory_file = self.project_root / '.augment' / 'rules' / 'project_memory.yaml'
        self.rules_dir = self.project_root / '.augment' / 'rules'
        
        # 已迁移的规则文件
        self.migrated_files = {
            'development_standards': self.rules_dir / 'development-standards.md',
            'user_preferences': self.rules_dir / 'user-preferences.md'
        }
    
    def load_current_memory(self) -> Dict[str, Any]:
        """加载当前MCP记忆内容"""
        if not self.mcp_memory_file.exists():
            return {}
        
        try:
            with open(self.mcp_memory_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"加载MCP记忆失败: {e}")
            return {}
    
    def extract_migrated_rules(self) -> Set[str]:
        """提取已迁移到.augment/rules/文件中的规则"""
        migrated_rules = set()
        
        for file_type, file_path in self.migrated_files.items():
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 提取规则内容（去除序号和格式）
                    lines = content.split('\n')
                    for line in lines:
                        line = line.strip()
                        if line and (line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.')) or 
                                   line.startswith(('10.', '11.', '12.', '13.', '14.', '15.', '16.', '17.', '18.'))):
                            # 移除序号，获取纯规则内容
                            rule_content = line.split('.', 1)[1].strip() if '.' in line else line
                            if len(rule_content) > 20:  # 过滤掉太短的内容
                                migrated_rules.add(rule_content)
                
                except Exception as e:
                    print(f"读取迁移文件失败 {file_path}: {e}")
        
        return migrated_rules
    
    def identify_redundant_memories(self, memory_data: Dict[str, Any], migrated_rules: Set[str]) -> Dict[str, List[int]]:
        """识别冗余的记忆条目"""
        redundant_indices = {
            'rules': [],
            'preferences': [],
            'patterns': [],
            'context': []
        }
        
        if 'project_memory' not in memory_data or 'memory_categories' not in memory_data['project_memory']:
            return redundant_indices
        
        categories = memory_data['project_memory']['memory_categories']
        
        for category_name, category_items in categories.items():
            if category_name in redundant_indices and isinstance(category_items, list):
                for i, item in enumerate(category_items):
                    # 检查是否与已迁移的规则重复
                    item_clean = item.strip()
                    
                    # 计算与已迁移规则的相似度
                    for migrated_rule in migrated_rules:
                        similarity = self._calculate_similarity(item_clean, migrated_rule)
                        if similarity > 0.8:  # 80%相似度阈值
                            redundant_indices[category_name].append(i)
                            break
        
        return redundant_indices
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0
    
    def create_optimized_memory(self, memory_data: Dict[str, Any], redundant_indices: Dict[str, List[int]]) -> Dict[str, Any]:
        """创建优化后的记忆数据"""
        optimized_data = memory_data.copy()
        
        if 'project_memory' in optimized_data and 'memory_categories' in optimized_data['project_memory']:
            categories = optimized_data['project_memory']['memory_categories']
            
            # 移除冗余条目（从后往前删除以保持索引正确）
            for category_name, indices in redundant_indices.items():
                if category_name in categories and isinstance(categories[category_name], list):
                    # 按降序排列索引，从后往前删除
                    for index in sorted(indices, reverse=True):
                        if 0 <= index < len(categories[category_name]):
                            del categories[category_name][index]
            
            # 更新导出时间
            optimized_data['project_memory']['export_time'] = datetime.now().isoformat()
            optimized_data['project_memory']['optimization_info'] = {
                'optimized_at': datetime.now().isoformat(),
                'removed_redundant_items': sum(len(indices) for indices in redundant_indices.values()),
                'optimization_reason': 'Removed rules migrated to .augment/rules/ files'
            }
        
        return optimized_data
    
    def generate_optimization_report(self, redundant_indices: Dict[str, List[int]], migrated_rules: Set[str]) -> Dict[str, Any]:
        """生成优化报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'migrated_rules_count': len(migrated_rules),
            'redundant_items_by_category': {},
            'total_redundant_items': 0,
            'optimization_impact': {},
            'recommendations': []
        }
        
        # 统计冗余条目
        for category, indices in redundant_indices.items():
            report['redundant_items_by_category'][category] = len(indices)
            report['total_redundant_items'] += len(indices)
        
        # 优化影响分析
        report['optimization_impact'] = {
            'context_size_reduction': f"预计减少 {report['total_redundant_items']} 个记忆条目",
            'ai_context_quality': '提高AI对话上下文的精准度',
            'maintenance_efficiency': '减少记忆管理复杂度',
            'rule_consistency': '避免规则版本冲突'
        }
        
        # 建议
        report['recommendations'] = [
            '使用优化后的记忆导出文件替换现有文件',
            '继续使用.augment/rules/目录管理正式规则',
            '保持MCP记忆专注于技术实现上下文',
            '定期运行优化工具清理冗余内容'
        ]
        
        return report
    
    def save_optimized_memory(self, optimized_data: Dict[str, Any]) -> str:
        """保存优化后的记忆数据"""
        optimized_file = self.rules_dir / 'project_memory_optimized.yaml'
        
        try:
            with open(optimized_file, 'w', encoding='utf-8') as f:
                yaml.dump(optimized_data, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            return str(optimized_file)
        except Exception as e:
            return f"保存失败: {e}"
    
    def run_optimization(self) -> Dict[str, Any]:
        """运行完整的优化流程"""
        optimization_result = {
            'timestamp': datetime.now().isoformat(),
            'status': 'success',
            'steps_completed': [],
            'optimization_report': None,
            'optimized_file': None,
            'recommendations': []
        }
        
        print("📊 加载当前MCP记忆...")
        memory_data = self.load_current_memory()
        if not memory_data:
            optimization_result['status'] = 'failed'
            optimization_result['error'] = 'Failed to load MCP memory'
            return optimization_result
        optimization_result['steps_completed'].append('loaded_memory')
        
        print("🔍 提取已迁移的规则...")
        migrated_rules = self.extract_migrated_rules()
        optimization_result['steps_completed'].append('extracted_migrated_rules')
        
        print("🎯 识别冗余记忆条目...")
        redundant_indices = self.identify_redundant_memories(memory_data, migrated_rules)
        optimization_result['steps_completed'].append('identified_redundant_items')
        
        print("📝 生成优化报告...")
        optimization_report = self.generate_optimization_report(redundant_indices, migrated_rules)
        optimization_result['optimization_report'] = optimization_report
        optimization_result['steps_completed'].append('generated_report')
        
        print("🔧 创建优化后的记忆数据...")
        optimized_data = self.create_optimized_memory(memory_data, redundant_indices)
        optimization_result['steps_completed'].append('created_optimized_data')
        
        print("💾 保存优化后的记忆文件...")
        optimized_file = self.save_optimized_memory(optimized_data)
        optimization_result['optimized_file'] = optimized_file
        optimization_result['steps_completed'].append('saved_optimized_file')
        
        # 生成最终建议
        optimization_result['recommendations'] = [
            f'优化后移除了 {optimization_report["total_redundant_items"]} 个冗余条目',
            f'优化文件已保存: {optimized_file}',
            '建议使用优化后的文件替换现有的project_memory.yaml',
            '继续使用.augment/rules/目录管理可编辑规则'
        ]
        
        return optimization_result


def main():
    """主函数 - 运行MCP记忆优化"""
    print("🧹 MCP记忆优化器")
    print("=" * 60)
    
    optimizer = MCPMemoryOptimizer()
    
    print("🚀 开始MCP记忆优化...")
    result = optimizer.run_optimization()
    
    if result['status'] == 'failed':
        print(f"❌ 优化失败: {result.get('error', 'Unknown error')}")
        return
    
    print(f"\n📊 优化报告:")
    report = result['optimization_report']
    print(f"  已迁移规则: {report['migrated_rules_count']} 个")
    print(f"  冗余条目总数: {report['total_redundant_items']} 个")
    
    for category, count in report['redundant_items_by_category'].items():
        if count > 0:
            print(f"  {category}: {count} 个冗余条目")
    
    print(f"\n📁 优化文件: {result['optimized_file']}")
    
    print(f"\n💡 优化建议:")
    for rec in result['recommendations']:
        print(f"  - {rec}")
    
    print(f"\n✅ MCP记忆优化完成！")


if __name__ == "__main__":
    main()
