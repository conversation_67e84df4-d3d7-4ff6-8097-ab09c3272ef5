#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
规则迁移执行器
基于优化分析结果，执行规则从MCP记忆系统到.augment/rules目录的迁移

{{ AURA-X: Add - 创建规则迁移执行器. Source: 基于CRUD限制分析，需要迁移规则到可编辑存储 }}
"""

import yaml
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any


class RuleMigrationExecutor:
    """
    规则迁移执行器
    
    执行规则从MCP记忆系统到.augment/rules目录的迁移
    """
    
    def __init__(self, project_root: str = None):
        """
        初始化迁移执行器
        
        参数:
            project_root: 项目根目录路径
        """
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent.parent
        self.mcp_memory_file = self.project_root / '.augment' / 'rules' / 'project_memory.yaml'
        self.rules_dir = self.project_root / '.augment' / 'rules'
        
        # 确保目录存在
        self.rules_dir.mkdir(parents=True, exist_ok=True)
    
    def load_mcp_memory_rules(self) -> Dict[str, List[str]]:
        """加载MCP记忆中的规则"""
        if not self.mcp_memory_file.exists():
            return {}
        
        try:
            with open(self.mcp_memory_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            if 'project_memory' in data and 'memory_categories' in data['project_memory']:
                return data['project_memory']['memory_categories']
        except Exception as e:
            print(f"加载MCP记忆失败: {e}")
        
        return {}
    
    def categorize_rules_for_migration(self, memory_categories: Dict[str, List[str]]) -> Dict[str, List[str]]:
        """将规则按迁移目标分类"""
        migration_plan = {
            'to_development_standards': [],    # 迁移到开发标准文件
            'to_user_preferences': [],         # 迁移到用户偏好文件
            'keep_in_mcp': [],                # 保留在MCP记忆中
            'to_vscode_memory': []             # 迁移到VSCode记忆
        }
        
        # 处理规范类记忆
        if 'rules' in memory_categories:
            for rule in memory_categories['rules']:
                rule_lower = rule.lower()
                
                # 开发标准：正式规范、架构要求
                if any(keyword in rule_lower for keyword in ['开发前必须', '统一使用', '继承', '通过', '遵循', '架构检查', '开发检查']):
                    migration_plan['to_development_standards'].append(rule)
                # VSCode记忆：用户偏好
                elif any(keyword in rule_lower for keyword in ['不生成', '不编译', '帮助', '用户']):
                    migration_plan['to_vscode_memory'].append(rule)
                # 保留在MCP：技术实现细节
                else:
                    migration_plan['keep_in_mcp'].append(rule)
        
        # 处理偏好类记忆
        if 'preferences' in memory_categories:
            for pref in memory_categories['preferences']:
                pref_lower = pref.lower()
                
                # 用户偏好：个人工作偏好
                if any(keyword in pref_lower for keyword in ['用户选择', '不生成', '不编译', '帮助']):
                    migration_plan['to_user_preferences'].append(pref)
                # 保留在MCP：技术偏好
                else:
                    migration_plan['keep_in_mcp'].append(pref)
        
        # 模式和上下文记忆保留在MCP中（技术实现细节）
        for category in ['patterns', 'context']:
            if category in memory_categories:
                migration_plan['keep_in_mcp'].extend(memory_categories[category])
        
        return migration_plan
    
    def create_development_standards_file(self, rules: List[str]) -> str:
        """创建开发标准文件"""
        content = f"""---
type: "development_standards"
created: "{datetime.now().isoformat()}"
source: "migrated_from_mcp_memory"
---

# 量化投资平台开发标准

> 本文档包含从MCP记忆系统迁移的正式开发规范，用于团队共享和版本控制。

## 开发规范

"""
        
        for i, rule in enumerate(rules, 1):
            content += f"{i}. {rule}\n\n"
        
        content += f"""
## 维护说明

- 本文件由MCP记忆系统迁移而来
- 可以直接编辑修改，支持版本控制
- 修改后会自动应用到团队开发流程
- 迁移时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 相关文件

- MCP记忆备份: `.augment/rules/project_memory.yaml`
- 架构检查工具: `src/utils/development_checker.py`
- 记忆同步工具: `src/utils/memory_sync.py`
"""
        
        file_path = self.rules_dir / 'development-standards.md'
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return str(file_path)
    
    def create_user_preferences_file(self, preferences: List[str]) -> str:
        """创建用户偏好文件"""
        content = f"""---
type: "user_preferences"
created: "{datetime.now().isoformat()}"
source: "migrated_from_mcp_memory"
---

# 用户偏好配置

> 本文档包含从MCP记忆系统迁移的用户偏好设置。

## 用户偏好

"""
        
        for i, pref in enumerate(preferences, 1):
            content += f"{i}. {pref}\n\n"
        
        content += f"""
## 说明

- 这些偏好设置会影响AI助手的行为
- 可以根据需要修改和更新
- 迁移时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        file_path = self.rules_dir / 'user-preferences.md'
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return str(file_path)
    
    def update_vscode_memory(self, rules: List[str]) -> str:
        """更新VSCode记忆文件"""
        vscode_memory_path = self._find_vscode_memory_path()
        
        if not vscode_memory_path:
            return "VSCode记忆文件未找到"
        
        try:
            # 读取现有内容
            existing_content = ""
            if vscode_memory_path.exists():
                with open(vscode_memory_path, 'r', encoding='utf-8') as f:
                    existing_content = f.read()
            
            # 添加迁移的规则
            new_content = existing_content + f"""

## 从MCP记忆迁移的用户偏好 ({datetime.now().strftime('%Y-%m-%d')})

"""
            
            for rule in rules:
                new_content += f"- {rule}\n"
            
            # 写入更新的内容
            with open(vscode_memory_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            return str(vscode_memory_path)
            
        except Exception as e:
            return f"更新VSCode记忆失败: {e}"
    
    def _find_vscode_memory_path(self) -> Path:
        """查找VSCode记忆文件路径"""
        vscode_storage_base = Path.home() / "Library" / "Application Support" / "Code" / "User" / "workspaceStorage"
        
        if vscode_storage_base.exists():
            for workspace_dir in vscode_storage_base.iterdir():
                if workspace_dir.is_dir():
                    augment_dir = workspace_dir / "Augment.vscode-augment"
                    if augment_dir.exists():
                        memory_file = augment_dir / "Augment-Memories"
                        if memory_file.exists():
                            return memory_file
        
        return None
    
    def execute_migration(self) -> Dict[str, Any]:
        """执行完整的规则迁移"""
        migration_result = {
            'timestamp': datetime.now().isoformat(),
            'status': 'success',
            'actions_taken': [],
            'files_created': [],
            'migration_summary': {},
            'recommendations': []
        }
        
        print("📊 加载MCP记忆规则...")
        memory_categories = self.load_mcp_memory_rules()
        
        if not memory_categories:
            migration_result['status'] = 'failed'
            migration_result['error'] = 'Failed to load MCP memory rules'
            return migration_result
        
        print("🎯 分析规则迁移计划...")
        migration_plan = self.categorize_rules_for_migration(memory_categories)
        migration_result['migration_summary'] = {
            'to_development_standards': len(migration_plan['to_development_standards']),
            'to_user_preferences': len(migration_plan['to_user_preferences']),
            'keep_in_mcp': len(migration_plan['keep_in_mcp']),
            'to_vscode_memory': len(migration_plan['to_vscode_memory'])
        }
        
        # 执行迁移
        if migration_plan['to_development_standards']:
            print("📝 创建开发标准文件...")
            dev_standards_file = self.create_development_standards_file(migration_plan['to_development_standards'])
            migration_result['files_created'].append(dev_standards_file)
            migration_result['actions_taken'].append('Created development standards file')
        
        if migration_plan['to_user_preferences']:
            print("⚙️ 创建用户偏好文件...")
            user_prefs_file = self.create_user_preferences_file(migration_plan['to_user_preferences'])
            migration_result['files_created'].append(user_prefs_file)
            migration_result['actions_taken'].append('Created user preferences file')
        
        if migration_plan['to_vscode_memory']:
            print("📋 更新VSCode记忆...")
            vscode_result = self.update_vscode_memory(migration_plan['to_vscode_memory'])
            migration_result['actions_taken'].append(f'Updated VSCode memory: {vscode_result}')
        
        # 生成建议
        migration_result['recommendations'] = [
            '定期审查和更新.augment/rules/目录中的规则文件',
            '使用版本控制跟踪规则变更',
            '保持MCP记忆系统专注于技术实现上下文',
            '建立规则一致性检查机制',
            '考虑清理MCP记忆中已迁移的重复规则'
        ]
        
        return migration_result


def main():
    """主函数 - 执行规则迁移"""
    print("🚀 规则迁移执行器")
    print("=" * 60)
    
    executor = RuleMigrationExecutor()
    
    print("🔄 开始执行规则迁移...")
    result = executor.execute_migration()
    
    if result['status'] == 'failed':
        print(f"❌ 迁移失败: {result.get('error', 'Unknown error')}")
        return
    
    print(f"\n📊 迁移摘要:")
    summary = result['migration_summary']
    print(f"  开发标准文件: {summary['to_development_standards']} 个规则")
    print(f"  用户偏好文件: {summary['to_user_preferences']} 个规则")
    print(f"  保留在MCP: {summary['keep_in_mcp']} 个规则")
    print(f"  VSCode记忆: {summary['to_vscode_memory']} 个规则")
    
    print(f"\n📁 创建的文件:")
    for file_path in result['files_created']:
        print(f"  ✅ {file_path}")
    
    print(f"\n💡 后续建议:")
    for rec in result['recommendations']:
        print(f"  - {rec}")
    
    print(f"\n✅ 规则迁移完成！")


if __name__ == "__main__":
    main()
