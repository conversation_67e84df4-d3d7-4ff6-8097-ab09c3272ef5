#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主入口：项目启动和CLI入口
- 处理命令行参数
- 初始化系统配置
- 集成各模块功能
- 提供交互式界面
"""

import os
import sys
import argparse
import logging
import warnings
import traceback

# 导入工具模块
from src.utils.logging import setup_logging
from src.utils.ui_utils import display_menu_and_get_choice
from src.commands.command_factory import CommandFactory
from src.utils.temporary.path_utils import get_output_path
from src.utils.logging.logger_factory import get_logger
from src.utils.temporary.file_utils import FileUtils

# 将src目录加入sys.path，确保from src.xxx导入能被找到
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

# 会话标记变量，仅在本次运行有效
session_used = False

logger = get_logger(__name__)

# 尝试导入命令处理模块
try:
    from src.commands.env_command import check_environment
except ImportError:
    def check_environment(output_file=None):
        logger.warning("环境检查模块未加载")
        return 1

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='量化交易策略平台')
    parser.add_argument('--version', action='store_true', help='显示版本信息')
    
    # 创建子命令解析器
    subparsers = parser.add_subparsers(dest='subcommand', help='子命令')
    
    # 数据命令
    data_parser = subparsers.add_parser('data', help='数据获取与处理')
    data_parser.add_argument('--source', choices=['tushare', 'local'], default='tushare', help='数据源')
    data_parser.add_argument('--type', choices=['calendar', 'stock', 'index', 'fundamental'], required=True, help='数据类型')
    data_parser.add_argument('--code', help='股票代码')
    data_parser.add_argument('--start', help='开始日期，格式：YYYYMMDD')
    data_parser.add_argument('--end', help='结束日期，格式：YYYYMMDD')
    data_parser.add_argument('--output', help='输出文件')
    
    # 策略命令
    strategy_parser = subparsers.add_parser('strategy', help='策略开发与测试')
    strategy_parser.add_argument('--name', required=True, help='策略名称')
    strategy_parser.add_argument('--action', choices=['create', 'test', 'edit'], default='test', help='操作类型')
    strategy_parser.add_argument('--template', choices=['moving_average', 'factor', 'momentum'], help='策略模板')
    strategy_parser.add_argument('--params', help='策略参数(JSON格式)')
    
    # 回测命令
    backtest_parser = subparsers.add_parser('backtest', help='策略回测')
    backtest_parser.add_argument('--strategy', required=True, help='策略名称')
    backtest_parser.add_argument('--start', help='开始日期，格式：YYYYMMDD')
    backtest_parser.add_argument('--end', help='结束日期，格式：YYYYMMDD')
    backtest_parser.add_argument('--capital', type=float, help='初始资金')
    backtest_parser.add_argument('--params', help='策略参数(JSON格式)')
    backtest_parser.add_argument('--stocks', help='股票列表，逗号分隔')
    backtest_parser.add_argument('--benchmark', help='基准指数')
    backtest_parser.add_argument('--plot', action='store_true', help='是否生成图表')
    
    # 交易命令
    trade_parser = subparsers.add_parser('trade', help='策略交易')
    trade_parser.add_argument('--strategy', required=True, help='策略名称')
    trade_parser.add_argument('--mode', choices=['paper', 'real'], default='paper', help='交易模式')
    trade_parser.add_argument('--capital', type=float, help='初始资金')
    trade_parser.add_argument('--params', help='策略参数(JSON格式)')
    
    # 风控命令
    risk_parser = subparsers.add_parser('risk', help='风险管理')
    risk_parser.add_argument('--portfolio', required=True, help='组合文件')
    risk_parser.add_argument('--check', action='store_true', help='风险检查')
    risk_parser.add_argument('--report', help='生成风险报告')
    
    # 结果分析命令
    results_parser = subparsers.add_parser('results', help='结果分析')
    results_parser.add_argument('--file', required=True, help='回测结果文件')
    results_parser.add_argument('--output', help='输出文件')
    results_parser.add_argument('--plot', action='store_true', help='是否生成图表')
    
    # 自动化回测命令
    autobacktest_parser = subparsers.add_parser('autobacktest', help='自动化回测流程')
    autobacktest_parser.add_argument('--strategy', required=True, choices=['moving_average', 'factor', 'momentum'], help='策略名称')
    autobacktest_parser.add_argument('--start', help='开始日期，格式：YYYYMMDD')
    autobacktest_parser.add_argument('--end', help='结束日期，格式：YYYYMMDD')
    autobacktest_parser.add_argument('--stocks', help='股票列表，逗号分隔')
    autobacktest_parser.add_argument('--stock_pool', choices=['ashare'], help='使用预定义股票池')
    autobacktest_parser.add_argument('--pool_size', help='股票池大小，使用stock_pool时有效')
    autobacktest_parser.add_argument('--capital', type=float, help='初始资金')
    autobacktest_parser.add_argument('--params', help='策略参数(JSON格式)')
    autobacktest_parser.add_argument('--benchmark', default='000300.SH', help='基准指数')
    autobacktest_parser.add_argument('--plot', action='store_true', help='是否生成图表')
    
    # 环境命令
    env_parser = subparsers.add_parser('env', help='环境检查')
    env_parser.add_argument('--output', help='输出文件')
    
    args = parser.parse_args()
    return args

def show_version():
    """显示版本信息"""
    logger.info("\n量化交易策略平台")
    logger.info("版本: 0.1.0")
    logger.info("构建日期: 2025-01-15")
    logger.info("作者: 泽强Felix")
    logger.info("-------------------------------")

def handle_autobacktest_menu():
    """处理自动化回测菜单选项"""
    # {{ AURA-X: Modify - 修复导入路径，使用绝对导入. Approval: 寸止(ID:架构一致性修复). }}
    from src.utils.ui_utils import parse_autobacktest_args
    autobacktest_args = parse_autobacktest_args()
    try:
        from src.commands.autobacktest_command import run_autobacktest_command
        return run_autobacktest_command(autobacktest_args)
    except ImportError:
        logger.error("错误: 自动回测模块未找到")
        return 1

def handle_fetch_data_menu():
    """处理A股数据获取菜单选项"""
    from src.data.fetcher.data_fetcher_manager import get_data_fetcher
    from datetime import datetime, timedelta
    from src.utils.ui_utils import show_progress, confirm_action
    import asyncio

    # 🚀 异步并发优化：使用DataFetcherManager获取HybridDataFetcher实例
    fetcher = get_data_fetcher({
        'auto_async': True,
        'async_threshold': 100  # 超过100只股票自动启用异步模式
    })

    # 显示模式信息
    mode_info = fetcher.get_mode_info()
    print(f"\n🚀 HybridDataFetcher已初始化")
    print(f"   自动异步模式: {'✅' if mode_info['auto_async'] else '❌'}")
    print(f"   异步阈值: {mode_info['async_threshold']}只股票")
    print(f"   异步模式可用: {'✅' if mode_info['async_available'] else '❌'}")

    # 检查异步模式可用性
    if not mode_info['async_available']:
        print("⚠️ 异步模式不可用，将使用同步模式")
    # 显示股票数量选择菜单
    print("\n=== A股数据获取配置 ===")
    print("📊 请选择要获取的股票数量：")
    print("1. 100支股票（快速测试，约5分钟）")
    print("2. 500支股票（中等规模，约20分钟）")
    print("3. 1000支股票（大规模，约40分钟）")
    print("4. 全部股票（完整数据，约2-3小时）")
    print("0. 返回主菜单")

    choice = input("请输入选项编号: ").strip()

    if choice == "0":
        return 0
    elif choice == "1":
        stock_limit = 100
    elif choice == "2":
        stock_limit = 500
    elif choice == "3":
        stock_limit = 1000
    elif choice == "4":
        stock_limit = None  # 全部股票
    else:
        print("无效选项，使用默认值：100支股票")
        stock_limit = 100

    # 🚀 异步模式选择（仅在异步可用且股票数量较多时显示）
    if mode_info['async_available'] and stock_limit and stock_limit >= 100:
        print(f"\n⚡ 检测到{stock_limit}只股票，建议使用异步模式以获得最佳性能")
        print("🚀 请选择数据获取模式：")
        print("1. 自动模式（推荐）- 根据数据量自动选择最优模式")
        print("2. 异步模式（高性能）- 强制使用异步并发获取")
        print("3. 同步模式（兼容）- 使用传统同步获取")

        mode_choice = input("请输入模式选项编号 (默认1): ").strip() or "1"

        if mode_choice == "1":
            fetcher.set_auto_async(True, threshold=100)
            print("✅ 已启用自动模式")
        elif mode_choice == "2":
            fetcher.set_async_mode(True)
            print("✅ 已强制启用异步模式")
        elif mode_choice == "3":
            fetcher.set_async_mode(False)
            fetcher.set_auto_async(False)
            print("✅ 已启用同步模式")
        else:
            print("⚠️ 无效选项，使用自动模式")
    elif stock_limit and stock_limit < 100:
        print(f"\n📊 {stock_limit}只股票，将使用同步模式（最优选择）")

    # 显示时间范围选择菜单
    print("\n📅 请选择数据时间范围：")
    print("1. 1个月（最近30天，快速测试）")
    print("2. 3个月（最近90天，短期分析）")
    print("3. 6个月（最近180天，中期分析）")
    print("4. 1年（最近365天，年度分析）")
    print("5. 2年（最近730天，长期分析）")
    print("6. 自定义时间范围")

    time_choice = input("请输入时间范围选项编号: ").strip()

    if time_choice == "1":
        days = 30
        time_desc = "1个月"
    elif time_choice == "2":
        days = 90
        time_desc = "3个月"
    elif time_choice == "3":
        days = 180
        time_desc = "6个月"
    elif time_choice == "4":
        days = 365
        time_desc = "1年"
    elif time_choice == "5":
        days = 730
        time_desc = "2年"
    elif time_choice == "6":
        # 自定义时间范围
        try:
            start_input = input("请输入开始日期 (格式: YYYYMMDD): ").strip()
            end_input = input("请输入结束日期 (格式: YYYYMMDD, 回车使用今天): ").strip()

            start_date = datetime.strptime(start_input, '%Y%m%d')
            if end_input:
                end_date = datetime.strptime(end_input, '%Y%m%d')
            else:
                end_date = datetime.now()

            days = (end_date - start_date).days
            time_desc = f"自定义({start_input}至{end_date.strftime('%Y%m%d')})"
        except ValueError:
            print("日期格式错误，使用默认值：1年")
            days = 365
            time_desc = "1年"
            start_date = None
            end_date = None
    else:
        print("无效选项，使用默认值：1年")
        days = 365
        time_desc = "1年"

    logger.info("正在获取A股股票列表...")
    stock_list_df = fetcher.fetch_reference_data(data_type='stock_list')
    if stock_list_df.empty:
        logger.error("获取股票列表失败")
        return 1

    symbols = stock_list_df['ts_code'].tolist()
    total_stocks = len(symbols)

    # 应用股票数量限制
    if stock_limit and stock_limit < total_stocks:
        symbols = symbols[:stock_limit]
        logger.info(f"已限制为前 {stock_limit} 支股票（总共 {total_stocks} 支）")
    else:
        logger.info(f"将获取全部 {total_stocks} 支股票的数据")

    # 🚀 根据实际股票数量智能调整高性能参数
    actual_stock_count = len(symbols)

    if actual_stock_count <= 100:
        # 小规模：优化延迟
        max_concurrent = 30
        batch_size = 15
        thread_pool_size = 20
        config_desc = "小规模优化配置"
    elif actual_stock_count <= 500:
        # 中等规模：平衡性能
        max_concurrent = 60
        batch_size = 30
        thread_pool_size = 40
        config_desc = "中等规模优化配置"
    else:
        # 大规模：高级并发优化（基于excellent等级稳定运行）
        max_concurrent = 150  # 从100提升到150（50%提升）
        batch_size = 75       # 从50提升到75（50%提升）
        thread_pool_size = 90 # 从60提升到90（50%提升）
        config_desc = "大规模高级并发优化配置"

    # 🚀 在DataFetcher层重新配置高性能参数（通用架构）
    if hasattr(fetcher, 'enable_enhanced_mode'):
        fetcher.enable_enhanced_mode(
            max_concurrent=max_concurrent,
            enhanced_batch_size=batch_size,
            thread_pool_multiplier=2.0,
            performance_monitoring=True
        )

    print(f"🔧 根据{actual_stock_count}只股票调整为{config_desc}:")
    print(f"   最大并发: {max_concurrent}")
    print(f"   批量大小: {batch_size}")
    print(f"   线程池: {thread_pool_size}")

    # 设置数据获取时间范围
    if time_choice == "6" and 'start_date' in locals() and start_date:
        # 使用自定义时间范围
        start_str = start_date.strftime('%Y%m%d')
        end_str = end_date.strftime('%Y%m%d')
    else:
        # 使用相对时间范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        start_str = start_date.strftime('%Y%m%d')
        end_str = end_date.strftime('%Y%m%d')

    logger.info(f"数据获取时间范围（{time_desc}）: {start_str} 至 {end_str}")

    # 确认操作
    if not confirm_action(f"确认获取 {len(symbols)} 支股票的{time_desc}数据？", default=True, timeout=10):
        logger.info("用户取消操作")
        return 0

    # 🚀 定义数据获取任务（统一使用高性能实现）
    tasks = [
        ("日线数据", lambda: fetcher.fetch_market_data(symbols, data_type='daily', start_date=start_str, end_date=end_str)),
        ("利润表数据", lambda: fetcher.fetch_financial_data(symbols, report_type='income', start_date=start_str, end_date=end_str)),
        ("资产负债表数据", lambda: fetcher.fetch_financial_data(symbols, report_type='balance', start_date=start_str, end_date=end_str)),
        ("现金流量表数据", lambda: fetcher.fetch_financial_data(symbols, report_type='cash_flow', start_date=start_str, end_date=end_str))
    ]
    print("🚀 使用统一高性能数据获取")

    # 执行数据获取任务并显示进度
    total_tasks = len(tasks)
    results = {}

    print(f"\n开始获取 {len(symbols)} 支股票的数据...")
    print("=" * 50)

    for i, (task_name, task_func) in enumerate(tasks, 1):
        try:
            print(f"\n[{i}/{total_tasks}] 正在获取{task_name}...")
            show_progress(i-1, total_tasks, f"总体进度")

            data = task_func()
            record_count = len(data) if not data.empty else 0
            results[task_name] = record_count

            print(f"✅ 成功获取 {record_count} 条{task_name}")
            show_progress(i, total_tasks, f"总体进度")

        except Exception as e:
            logger.error(f"❌ 获取{task_name}失败: {e}")
            results[task_name] = 0

    # 尝试获取市值数据（可选）
    try:
        print(f"\n[额外] 正在获取市值数据...")
        market_cap_data = fetcher.fetch_market_data(symbols, data_type='market_cap', start_date=start_str, end_date=end_str)
        market_cap_count = len(market_cap_data) if not market_cap_data.empty else 0
        results["市值数据"] = market_cap_count
        print(f"✅ 成功获取 {market_cap_count} 条市值数据")
    except Exception as e:
        logger.warning(f"⚠️ 市值数据获取失败: {e}")
        results["市值数据"] = 0

    # 显示最终结果
    print("\n" + "=" * 50)
    print("📊 数据获取完成！汇总结果：")
    for data_type, count in results.items():
        status = "✅" if count > 0 else "❌"
        print(f"  {status} {data_type}: {count} 条记录")

    total_records = sum(results.values())
    print(f"\n📈 总计获取: {total_records} 条数据记录")

    # 🚀 高性能模式性能报告（基于DataFetcher统一架构）
    if hasattr(fetcher, 'get_performance_summary'):
        try:
            print("\n" + "=" * 50)
            print("🚀 高性能模式性能报告")
            print("=" * 50)

            performance = fetcher.get_performance_summary()
            enhanced_stats = performance.get('enhanced_stats', {})

            # 显示性能指标
            avg_throughput = enhanced_stats.get('avg_throughput', 0)
            max_throughput = enhanced_stats.get('max_throughput', 0)
            improvement = enhanced_stats.get('performance_improvement', 1.0)
            concurrent_requests = enhanced_stats.get('concurrent_requests', 0)
            batch_efficiency = enhanced_stats.get('batch_efficiency', 0.0)

            print(f"📊 性能指标:")
            print(f"   平均吞吐量: {avg_throughput:.1f} 行/秒")
            print(f"   峰值吞吐量: {max_throughput:.1f} 行/秒")
            print(f"   性能提升倍数: {improvement:.1f}x")
            print(f"   并发请求数: {concurrent_requests}")
            print(f"   批处理效率: {batch_efficiency:.1%}")

            # 基准对比
            baseline_throughput = 3189.7
            baseline_improvement = avg_throughput / baseline_throughput if baseline_throughput > 0 else 0

            print(f"\n🎯 基准对比:")
            print(f"   基准吞吐量: {baseline_throughput:.1f} 行/秒")
            print(f"   当前吞吐量: {avg_throughput:.1f} 行/秒")
            print(f"   基准提升: {baseline_improvement:.1f}倍")

            # 目标达成评估
            if baseline_improvement >= 5.0:
                print(f"   🏆 优秀！超出5倍目标上限")
            elif baseline_improvement >= 3.0:
                print(f"   🎉 达成第一周目标！(3-5倍性能提升)")
            elif baseline_improvement >= 2.0:
                print(f"   📈 接近目标，表现良好")
            else:
                print(f"   ⚠️ 需要进一步优化")

            # 配置信息
            config = performance.get('config', {})
            print(f"\n⚙️ 高性能配置:")
            print(f"   最大并发: {config.get('max_concurrent', 'N/A')}")
            print(f"   批量大小: {config.get('enhanced_batch_size', 'N/A')}")
            print(f"   线程池倍数: {config.get('thread_pool_multiplier', 'N/A')}")
            print(f"   性能监控: {'✅' if config.get('performance_monitoring', False) else '❌'}")

        except Exception as e:
            print(f"⚠️ 性能报告生成失败: {e}")
    else:
        print("\n📊 高性能模式基于DataFetcher统一架构，性能监控已集成")

    # 数据质量检查
    print("\n🔍 正在进行数据质量检查...")
    quality_report = perform_data_quality_check(fetcher, symbols, start_str, end_str, results)
    display_quality_report(quality_report, len(symbols), time_desc)

    print("=" * 50)

    # 🔧 显式清理异步资源，确保会话正确关闭
    try:
        # 方法1：尝试同步清理（最可靠）
        if hasattr(fetcher, 'cleanup_sync'):
            fetcher.cleanup_sync()
            logger.debug("✅ 同步资源清理完成")

        # 方法2：备选异步清理
        elif hasattr(fetcher, 'cleanup_async'):
            # 尝试在新的事件循环中清理异步资源
            def cleanup_fetcher():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(fetcher.cleanup_async())
                    logger.debug("✅ 异步资源清理完成")
                except Exception as e:
                    logger.debug(f"⚠️ 异步资源清理异常: {e}")
                finally:
                    loop.close()

            # 在线程池中执行清理，避免事件循环冲突
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(cleanup_fetcher)
                try:
                    future.result(timeout=3)  # 3秒超时
                except concurrent.futures.TimeoutError:
                    logger.debug("⚠️ 异步资源清理超时")
                except Exception as e:
                    logger.debug(f"⚠️ 异步资源清理失败: {e}")
    except Exception as e:
        logger.debug(f"⚠️ 异步资源清理过程异常: {e}")

    return 0

def perform_data_quality_check(fetcher, symbols, start_date, end_date, results):
    """执行数据质量检查"""
    import sqlite3
    import pandas as pd

    quality_report = {
        'completeness': {},
        'consistency': {},
        'coverage': {},
        'issues': []
    }

    try:
        # 连接数据库进行质量检查
        conn = sqlite3.connect('output/data/db/sqlite/quantification.db')

        # 检查日线数据完整性
        if results.get("日线数据", 0) > 0:
            daily_query = """
            SELECT ts_code, COUNT(*) as record_count,
                   MIN(trade_date) as start_date,
                   MAX(trade_date) as end_date
            FROM daily
            WHERE trade_date >= ? AND trade_date <= ?
            GROUP BY ts_code
            """
            daily_stats = pd.read_sql(daily_query, conn, params=[start_date, end_date])

            if not daily_stats.empty:
                avg_records = daily_stats['record_count'].mean()
                min_records = daily_stats['record_count'].min()
                max_records = daily_stats['record_count'].max()

                quality_report['completeness']['日线数据'] = {
                    'avg_records_per_stock': avg_records,
                    'min_records': min_records,
                    'max_records': max_records,
                    'stocks_with_data': len(daily_stats),
                    'expected_stocks': len(symbols)
                }

                # 检查数据覆盖率
                coverage_rate = len(daily_stats) / len(symbols) if symbols else 0
                quality_report['coverage']['日线数据'] = coverage_rate

                # 检查异常情况
                if min_records < avg_records * 0.8:  # 如果最少记录数少于平均值的80%
                    quality_report['issues'].append(f"部分股票日线数据不完整（最少{min_records}条，平均{avg_records:.0f}条）")

        # 检查财务数据完整性
        financial_tables = ['income', 'balance', 'cash_flow']
        financial_names = ['利润表数据', '资产负债表数据', '现金流量表数据']

        for table, name in zip(financial_tables, financial_names):
            if results.get(name, 0) > 0:
                financial_query = f"""
                SELECT ts_code, COUNT(*) as record_count
                FROM {table}
                WHERE end_date >= ? AND end_date <= ?
                GROUP BY ts_code
                """
                try:
                    financial_stats = pd.read_sql(financial_query, conn, params=[start_date, end_date])

                    if not financial_stats.empty:
                        coverage_rate = len(financial_stats) / len(symbols) if symbols else 0
                        quality_report['coverage'][name] = coverage_rate

                        avg_records = financial_stats['record_count'].mean()
                        quality_report['completeness'][name] = {
                            'avg_records_per_stock': avg_records,
                            'stocks_with_data': len(financial_stats),
                            'expected_stocks': len(symbols)
                        }

                        # 财务数据覆盖率通常较低是正常的
                        if coverage_rate < 0.3:
                            quality_report['issues'].append(f"{name}覆盖率较低（{coverage_rate:.1%}），这在财务数据中是正常的")
                except Exception as e:
                    quality_report['issues'].append(f"{name}质量检查失败: {str(e)}")

        # 检查数据一致性（日期范围）
        date_consistency_query = """
        SELECT 'daily' as table_name, MIN(trade_date) as min_date, MAX(trade_date) as max_date
        FROM daily
        WHERE trade_date >= ? AND trade_date <= ?
        """
        try:
            date_stats = pd.read_sql(date_consistency_query, conn, params=[start_date, end_date])
            if not date_stats.empty:
                quality_report['consistency']['date_range'] = {
                    'actual_start': date_stats.iloc[0]['min_date'],
                    'actual_end': date_stats.iloc[0]['max_date'],
                    'expected_start': start_date,
                    'expected_end': end_date
                }
        except Exception as e:
            quality_report['issues'].append(f"日期一致性检查失败: {str(e)}")

        conn.close()

    except Exception as e:
        quality_report['issues'].append(f"数据质量检查失败: {str(e)}")

    return quality_report

def display_quality_report(quality_report, total_stocks, time_desc):
    """显示数据质量报告"""
    print("\n📋 数据质量报告:")
    print("-" * 40)

    # 显示数据覆盖率
    if quality_report['coverage']:
        print("📊 数据覆盖率:")
        for data_type, coverage in quality_report['coverage'].items():
            status = "✅" if coverage > 0.8 else "⚠️" if coverage > 0.5 else "❌"
            print(f"  {status} {data_type}: {coverage:.1%} ({int(coverage * total_stocks)}/{total_stocks}支股票)")

    # 显示数据完整性
    if quality_report['completeness']:
        print("\n📈 数据完整性:")
        for data_type, stats in quality_report['completeness'].items():
            avg_records = stats['avg_records_per_stock']
            stocks_with_data = stats['stocks_with_data']
            print(f"  📊 {data_type}:")
            print(f"    - 平均每支股票: {avg_records:.1f} 条记录")
            print(f"    - 有数据股票数: {stocks_with_data} 支")

            if 'min_records' in stats and 'max_records' in stats:
                print(f"    - 记录数范围: {stats['min_records']} - {stats['max_records']} 条")

    # 显示数据一致性
    if quality_report['consistency']:
        print("\n🔍 数据一致性:")
        if 'date_range' in quality_report['consistency']:
            date_info = quality_report['consistency']['date_range']
            print(f"  📅 实际日期范围: {date_info['actual_start']} 至 {date_info['actual_end']}")
            print(f"  📅 预期日期范围: {date_info['expected_start']} 至 {date_info['expected_end']}")

    # 显示问题和建议
    if quality_report['issues']:
        print("\n⚠️ 发现的问题:")
        for i, issue in enumerate(quality_report['issues'], 1):
            print(f"  {i}. {issue}")
    else:
        print("\n✅ 未发现明显的数据质量问题")

    # 总体质量评分
    total_coverage = sum(quality_report['coverage'].values()) / len(quality_report['coverage']) if quality_report['coverage'] else 0
    issue_count = len(quality_report['issues'])

    if total_coverage > 0.8 and issue_count == 0:
        quality_score = "优秀"
        score_emoji = "🌟"
    elif total_coverage > 0.6 and issue_count <= 2:
        quality_score = "良好"
        score_emoji = "✅"
    elif total_coverage > 0.4 and issue_count <= 4:
        quality_score = "一般"
        score_emoji = "⚠️"
    else:
        quality_score = "需要改进"
        score_emoji = "❌"

    print(f"\n{score_emoji} 整体数据质量评分: {quality_score}")
    print(f"📊 平均覆盖率: {total_coverage:.1%}")
    print(f"⚠️ 问题数量: {issue_count} 个")

def handle_backtest_menu():
    """处理移动平均线策略回测菜单选项"""
    import argparse
    backtest_args = argparse.Namespace()
    backtest_args.strategy = 'moving_average'
    backtest_args.start = None
    backtest_args.end = None
    backtest_args.capital = 1000000
    backtest_args.params = None
    backtest_args.stocks = None
    backtest_args.benchmark = None
    backtest_args.plot = True
    try:
        from src.commands.backtest_command import run_backtest_command
        return run_backtest_command(backtest_args)
    except ImportError:
        logger.error("错误: 回测命令模块未找到")
        return 1

def handle_results_menu():
    """处理回测结果查看菜单选项"""
    import argparse
    results_args = argparse.Namespace()
    results_args.file = get_output_path('backtest_result.json')
    results_args.output = None
    results_args.plot = True
    try:
        from src.commands.results_command import run_results_command
        return run_results_command(results_args)
    except ImportError:
        logger.error("错误: 结果命令模块未找到")
        return 1

def handle_cache_performance_test():
    """处理缓存性能测试菜单选项"""
    from src.data.fetcher.data_fetcher_manager import get_cached_data_fetcher
    from datetime import datetime, timedelta
    from src.utils.config.config_factory import config_factory
    import time

    print("\n" + "="*60)
    print("🚀 缓存性能基准测试")
    print("="*60)

    # {{ AURA-X: Modify - 移除重复的token获取逻辑，由DataFetcherManager统一管理. Approval: 寸止(ID:阶段1.2). }}

    # {{ AURA-X: Modify - 使用DataFetcherManager统一管理，消除重复初始化. Approval: 寸止(ID:阶段1.2). }}
    # 创建数据获取器（使用完整配置，包含缓存）
    fetcher = get_cached_data_fetcher(
        cache_type='memory',
        cache_config={'ttl': 3600}  # 1小时TTL
    )

    # 测试参数
    test_symbols = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ']
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)  # 1个月数据
    start_str = start_date.strftime('%Y%m%d')
    end_str = end_date.strftime('%Y%m%d')

    print(f"📊 测试配置:")
    print(f"   测试股票: {len(test_symbols)}只")
    print(f"   时间范围: {start_str} 至 {end_str}")
    print(f"   数据类型: daily (日线数据)")

    # 第一轮测试：冷缓存（清空缓存）
    print(f"\n🧊 第一轮测试：冷缓存性能")
    print("-" * 40)

    # 清空缓存
    if hasattr(fetcher.sync_fetcher, 'smart_cache') and fetcher.sync_fetcher.smart_cache:
        fetcher.sync_fetcher.smart_cache.cache.clear()
        print("✅ 已清空缓存")

    cold_start_time = time.time()
    cold_data = fetcher.fetch_market_data(
        symbols=test_symbols,
        data_type='daily',
        start_date=start_str,
        end_date=end_str,
        use_cache=True,
        save=True  # 保存到数据库，测试增量更新器兼容性
    )
    cold_end_time = time.time()
    cold_duration = cold_end_time - cold_start_time
    cold_records = len(cold_data) if not cold_data.empty else 0
    cold_throughput = cold_records / cold_duration if cold_duration > 0 else 0

    print(f"   耗时: {cold_duration:.2f}秒")
    print(f"   记录数: {cold_records:,}条")
    print(f"   吞吐量: {cold_throughput:.1f}条/秒")

    # 第二轮测试：热缓存（相同数据）
    print(f"\n🔥 第二轮测试：热缓存性能")
    print("-" * 40)

    hot_start_time = time.time()
    hot_data = fetcher.fetch_market_data(
        symbols=test_symbols,
        data_type='daily',
        start_date=start_str,
        end_date=end_str,
        use_cache=True,
        save=True
    )
    hot_end_time = time.time()
    hot_duration = hot_end_time - hot_start_time
    hot_records = len(hot_data) if not hot_data.empty else 0
    hot_throughput = hot_records / hot_duration if hot_duration > 0 else 0

    print(f"   耗时: {hot_duration:.2f}秒")
    print(f"   记录数: {hot_records:,}条")
    print(f"   吞吐量: {hot_throughput:.1f}条/秒")

    # 计算缓存效果
    if cold_duration > 0 and hot_duration > 0:
        speedup = cold_duration / hot_duration
        print(f"   缓存加速比: {speedup:.1f}x")

    # 获取缓存统计信息
    print(f"\n📈 缓存统计信息")
    print("-" * 40)

    if hasattr(fetcher.sync_fetcher, 'smart_cache') and fetcher.sync_fetcher.smart_cache:
        cache_stats = fetcher.sync_fetcher.smart_cache.get_stats()
        print(f"   缓存命中数: {cache_stats.get('hits', 0)}")
        print(f"   缓存未命中数: {cache_stats.get('misses', 0)}")
        print(f"   缓存命中率: {cache_stats.get('hit_rate', 0):.1%}")
        print(f"   缓存设置数: {cache_stats.get('sets', 0)}")

        # 底层缓存统计
        if 'cache_size' in cache_stats:
            print(f"   缓存条目数: {cache_stats.get('cache_size', 0)}")

    # 第三轮测试：部分缓存（部分新数据）
    print(f"\n🔄 第三轮测试：部分缓存性能")
    print("-" * 40)

    # 使用不同的股票组合（部分重叠）
    partial_symbols = ['000001.SZ', '000002.SZ', '600519.SH', '000858.SZ', '002415.SZ']

    partial_start_time = time.time()
    partial_data = fetcher.fetch_market_data(
        symbols=partial_symbols,
        data_type='daily',
        start_date=start_str,
        end_date=end_str,
        use_cache=True,
        save=True
    )
    partial_end_time = time.time()
    partial_duration = partial_end_time - partial_start_time
    partial_records = len(partial_data) if not partial_data.empty else 0
    partial_throughput = partial_records / partial_duration if partial_duration > 0 else 0

    print(f"   耗时: {partial_duration:.2f}秒")
    print(f"   记录数: {partial_records:,}条")
    print(f"   吞吐量: {partial_throughput:.1f}条/秒")

    # 最终缓存统计
    print(f"\n📊 最终缓存统计")
    print("-" * 40)

    final_hit_rate = 0.0  # 默认值
    if hasattr(fetcher.sync_fetcher, 'smart_cache') and fetcher.sync_fetcher.smart_cache:
        final_stats = fetcher.sync_fetcher.smart_cache.get_stats()
        total_requests = final_stats.get('hits', 0) + final_stats.get('misses', 0)
        final_hit_rate = final_stats.get('hit_rate', 0)

        print(f"   总请求数: {total_requests}")
        print(f"   总命中数: {final_stats.get('hits', 0)}")
        print(f"   总未命中数: {final_stats.get('misses', 0)}")
        print(f"   最终命中率: {final_hit_rate:.1%}")
        print(f"   缓存条目数: {final_stats.get('cache_size', 0)}")
    else:
        print("   ⚠️ 无法获取缓存统计信息")

    # 性能总结
    print(f"\n🎯 性能基准总结")
    print("="*60)
    print(f"冷缓存吞吐量: {cold_throughput:.1f}条/秒")
    print(f"热缓存吞吐量: {hot_throughput:.1f}条/秒")
    print(f"部分缓存吞吐量: {partial_throughput:.1f}条/秒")

    if cold_throughput > 0:
        hot_improvement = ((hot_throughput - cold_throughput) / cold_throughput * 100) if cold_throughput > 0 else 0
        print(f"缓存性能提升: {hot_improvement:.1f}%")

    print(f"当前缓存命中率: {final_hit_rate:.1%}")
    print(f"优化目标命中率: 70-85%")

    if final_hit_rate < 0.7:
        print("⚠️  缓存命中率偏低，建议实施Redis多级缓存优化")
    else:
        print("✅ 缓存性能良好")

    return 0

def handle_cache_diagnosis():
    """处理缓存诊断菜单选项"""
    from src.data.fetcher.data_fetcher_manager import get_cached_data_fetcher
    from datetime import datetime, timedelta
    import time

    print("\n" + "="*60)
    print("🔍 缓存系统深度诊断")
    print("="*60)

    # {{ AURA-X: Modify - 使用DataFetcherManager统一管理，消除重复初始化. Approval: 寸止(ID:阶段1.2). }}
    # 创建数据获取器（使用完整配置，包含缓存）
    fetcher = get_cached_data_fetcher(
        cache_type='memory',
        cache_config={'ttl': 3600}  # 1小时TTL
    )

    print(f"📊 缓存系统组件检查:")

    # 1. 检查SmartCacheManager是否存在
    print(f"\n1️⃣ SmartCacheManager检查")
    print("-" * 30)

    sync_fetcher = fetcher.sync_fetcher
    has_smart_cache = hasattr(sync_fetcher, 'smart_cache') and sync_fetcher.smart_cache is not None
    print(f"   SmartCacheManager存在: {'✅' if has_smart_cache else '❌'}")

    if has_smart_cache:
        smart_cache = sync_fetcher.smart_cache
        print(f"   缓存类型: {type(smart_cache).__name__}")
        print(f"   统计功能启用: {'✅' if smart_cache.enable_stats else '❌'}")
        print(f"   默认TTL: {smart_cache.default_ttl}秒")

        # 检查底层缓存实现
        if hasattr(smart_cache, 'cache'):
            cache_impl = smart_cache.cache
            print(f"   底层缓存类型: {type(cache_impl).__name__}")

            # 测试缓存基本功能
            test_key = "test_cache_key"
            test_value = {"test": "data", "timestamp": time.time()}

            print(f"\n   🧪 缓存功能测试:")

            # 测试设置
            set_result = smart_cache.set(test_key, test_value)
            print(f"   设置测试: {'✅' if set_result else '❌'}")

            # 测试获取
            get_result = smart_cache.get(test_key)
            get_success = get_result is not None and get_result.get('test') == 'data'
            print(f"   获取测试: {'✅' if get_success else '❌'}")

            # 测试统计
            stats = smart_cache.get_stats()
            print(f"   统计信息: hits={stats.get('hits', 0)}, misses={stats.get('misses', 0)}")

            # 清理测试数据
            if hasattr(cache_impl, 'delete'):
                cache_impl.delete(test_key)
    else:
        print(f"   ❌ SmartCacheManager未初始化")

    # 2. 检查缓存键生成
    print(f"\n2️⃣ 缓存键生成测试")
    print("-" * 30)

    if has_smart_cache:
        test_symbols = ['000001.SZ', '000002.SZ']
        test_start = '20250624'
        test_end = '20250724'

        cache_key = smart_cache.generate_cache_key(
            data_type='daily',
            symbols=test_symbols,
            start_date=test_start,
            end_date=test_end,
            fields=None,
            extra_params={'adjust': None}
        )

        print(f"   生成的缓存键: {cache_key}")
        print(f"   键长度: {len(cache_key)}字符")
        print(f"   键格式正确: {'✅' if ':' in cache_key else '❌'}")
    else:
        print(f"   ❌ 无法测试，SmartCacheManager不存在")

    return 0

def handle_ab_performance_test():
    """处理1000只股票A/B性能测试"""
    from src.data.fetcher.data_fetcher_manager import get_test_data_fetcher, get_cached_data_fetcher
    from datetime import datetime, timedelta
    import time
    import random

    print("\n" + "="*60)
    print("🚀 1000只股票A/B性能测试")
    print("="*60)

    # {{ AURA-X: Modify - 使用DataFetcherManager统一管理，消除重复初始化. Approval: 寸止(ID:阶段1.2). }}

    print(f"📊 修正后测试配置:")
    print(f"   测试股票数量: 50只（避免异步模式）")
    print(f"   时间范围: 2024.10 至 2024.12")
    print(f"   重复查询次数: 5次（体现缓存优势）")
    print(f"   测试轮数: 3轮")
    print(f"   测试间隔: 10秒")
    print(f"   测试重点: 缓存命中率和重复查询性能")
    print(f"   目标提升: 缓存命中时>10倍加速")

    # 获取股票列表
    print(f"\n📋 获取股票列表...")
    try:
        # {{ AURA-X: Modify - 使用DataFetcherManager获取测试用fetcher. Approval: 寸止(ID:阶段1.2). }}
        # 创建临时fetcher获取股票列表
        temp_fetcher = get_test_data_fetcher(disable_cache=True)  # 获取股票列表时不使用缓存

        stock_list_df = temp_fetcher.fetch_reference_data(data_type='stock_list')
        if stock_list_df.empty:
            logger.error("获取股票列表失败")
            return 1

        # 选择50只活跃股票（避免异步模式）
        all_symbols = stock_list_df['ts_code'].tolist()
        # 选择一些知名的活跃股票，确保有数据
        preferred_symbols = [
            '000001.SZ', '000002.SZ', '000858.SZ', '600000.SH', '600036.SH',
            '600519.SH', '000858.SZ', '002415.SZ', '600276.SH', '000568.SZ',
            '600887.SH', '002304.SZ', '000725.SZ', '600585.SH', '002142.SZ',
            '600031.SH', '000063.SZ', '600104.SH', '002027.SZ', '000166.SZ',
            '600048.SH', '000338.SZ', '600009.SH', '002230.SZ', '000876.SZ',
            '600028.SH', '000157.SZ', '600050.SH', '002081.SZ', '000792.SZ',
            '600015.SH', '000069.SZ', '600029.SH', '002065.SZ', '000629.SZ',
            '600018.SH', '000100.SZ', '600030.SH', '002024.SZ', '000623.SZ',
            '600019.SH', '000089.SZ', '600033.SH', '002007.SZ', '000625.SZ',
            '600020.SH', '000090.SZ', '600035.SH', '002008.SZ', '000626.SZ'
        ]

        # 从preferred_symbols中选择存在的股票
        test_symbols = [symbol for symbol in preferred_symbols if symbol in all_symbols]
        if len(test_symbols) < 50:
            # 如果preferred不够，随机补充
            remaining = [s for s in all_symbols if s not in test_symbols]
            test_symbols.extend(random.sample(remaining, min(50 - len(test_symbols), len(remaining))))

        test_symbols = test_symbols[:50]  # 确保只有50只
        print(f"   ✅ 已选择{len(test_symbols)}只股票进行测试")

    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
        return 1

    # 测试时间范围
    start_date = '20241001'
    end_date = '20241231'

    # A组测试：基准版本（禁用缓存优化）
    print(f"\n🅰️ A组测试：基准版本（禁用缓存）")
    print("="*50)

    baseline_results = []

    for round_num in range(1, 4):
        print(f"\n第{round_num}轮基准测试（重复查询5次）:")

        # {{ AURA-X: Modify - 使用DataFetcherManager获取基准测试fetcher. Approval: 寸止(ID:阶段1.2). }}
        # 创建基准版本fetcher（禁用缓存）
        baseline_fetcher = get_test_data_fetcher(disable_cache=True)  # 禁用缓存

        round_start_time = time.time()
        total_records = 0

        try:
            # 重复查询5次相同数据
            for query_num in range(1, 6):
                print(f"     查询{query_num}/5: ", end="", flush=True)

                query_start_time = time.time()
                baseline_data = baseline_fetcher.fetch_market_data(
                    symbols=test_symbols,
                    data_type='daily',
                    start_date=start_date,
                    end_date=end_date,
                    use_cache=False,
                    save=False,
                    enable_incremental=False  # 禁用增量更新
                )
                query_end_time = time.time()
                query_duration = query_end_time - query_start_time
                query_records = len(baseline_data) if not baseline_data.empty else 0
                total_records += query_records

                print(f"{query_duration:.2f}s, {query_records}条")

                # 查询间隔，避免API限流
                if query_num < 5:
                    time.sleep(2)

            round_end_time = time.time()
            total_duration = round_end_time - round_start_time
            avg_throughput = total_records / total_duration if total_duration > 0 else 0

            baseline_results.append({
                'round': round_num,
                'duration': total_duration,
                'records': total_records,
                'throughput': avg_throughput,
                'queries': 5
            })

            print(f"   总耗时: {total_duration:.2f}秒")
            print(f"   总记录数: {total_records:,}条")
            print(f"   平均吞吐量: {avg_throughput:.1f}条/秒")

        except Exception as e:
            logger.error(f"第{round_num}轮基准测试失败: {e}")
            baseline_results.append({
                'round': round_num,
                'duration': 0,
                'records': 0,
                'throughput': 0,
                'queries': 5
            })

        # 测试间隔
        if round_num < 3:
            print(f"   等待10秒后进行下一轮测试...")
            time.sleep(10)

    # B组测试：优化版本（启用缓存优化）
    print(f"\n🅱️ B组测试：优化版本（启用缓存）")
    print("="*50)

    optimized_results = []

    for round_num in range(1, 4):
        print(f"\n第{round_num}轮优化测试（重复查询5次）:")

        # {{ AURA-X: Modify - 使用DataFetcherManager获取优化测试fetcher. Approval: 寸止(ID:阶段1.2). }}
        # 创建优化版本fetcher（启用缓存）
        optimized_fetcher = get_cached_data_fetcher(
            cache_type='memory',
            cache_config={'ttl': 3600}
        )

        round_start_time = time.time()
        total_records = 0
        cache_hits = 0

        try:
            # 重复查询5次相同数据
            for query_num in range(1, 6):
                print(f"     查询{query_num}/5: ", end="", flush=True)

                query_start_time = time.time()
                optimized_data = optimized_fetcher.fetch_market_data(
                    symbols=test_symbols,
                    data_type='daily',
                    start_date=start_date,
                    end_date=end_date,
                    use_cache=True,
                    save=False,
                    enable_incremental=True  # 启用增量更新
                )
                query_end_time = time.time()
                query_duration = query_end_time - query_start_time
                query_records = len(optimized_data) if not optimized_data.empty else 0
                total_records += query_records

                # 检查缓存命中
                if hasattr(optimized_fetcher.sync_fetcher, 'smart_cache') and optimized_fetcher.sync_fetcher.smart_cache:
                    cache_stats = optimized_fetcher.sync_fetcher.smart_cache.get_stats()
                    current_hits = cache_stats.get('hits', 0)
                    if query_num > 1 and current_hits > cache_hits:
                        print(f"{query_duration:.2f}s, {query_records}条 ✅缓存命中")
                        cache_hits = current_hits
                    else:
                        print(f"{query_duration:.2f}s, {query_records}条")
                else:
                    print(f"{query_duration:.2f}s, {query_records}条")

                # 查询间隔，避免API限流
                if query_num < 5:
                    time.sleep(2)

            round_end_time = time.time()
            total_duration = round_end_time - round_start_time
            avg_throughput = total_records / total_duration if total_duration > 0 else 0

            # 获取最终缓存统计
            final_cache_stats = {}
            if hasattr(optimized_fetcher.sync_fetcher, 'smart_cache') and optimized_fetcher.sync_fetcher.smart_cache:
                final_cache_stats = optimized_fetcher.sync_fetcher.smart_cache.get_stats()

            optimized_results.append({
                'round': round_num,
                'duration': total_duration,
                'records': total_records,
                'throughput': avg_throughput,
                'queries': 5,
                'cache_stats': final_cache_stats
            })

            print(f"   总耗时: {total_duration:.2f}秒")
            print(f"   总记录数: {total_records:,}条")
            print(f"   平均吞吐量: {avg_throughput:.1f}条/秒")
            print(f"   缓存命中率: {final_cache_stats.get('hit_rate', 0):.1%}")
            print(f"   缓存命中数: {final_cache_stats.get('hits', 0)}")

        except Exception as e:
            logger.error(f"第{round_num}轮优化测试失败: {e}")
            optimized_results.append({
                'round': round_num,
                'duration': 0,
                'records': 0,
                'throughput': 0,
                'queries': 5,
                'cache_stats': {}
            })

        # 测试间隔
        if round_num < 3:
            print(f"   等待10秒后进行下一轮测试...")
            time.sleep(10)

    # 计算平均性能
    baseline_avg = {
        'duration': sum(r['duration'] for r in baseline_results) / len(baseline_results),
        'records': sum(r['records'] for r in baseline_results) / len(baseline_results),
        'throughput': sum(r['throughput'] for r in baseline_results) / len(baseline_results)
    }

    optimized_avg = {
        'duration': sum(r['duration'] for r in optimized_results) / len(optimized_results),
        'records': sum(r['records'] for r in optimized_results) / len(optimized_results),
        'throughput': sum(r['throughput'] for r in optimized_results) / len(optimized_results)
    }

    # 生成A/B对比报告
    print(f"\n📊 A/B性能对比报告")
    print("="*60)

    print(f"\n📈 平均性能对比:")
    print(f"   基准版本(A组):")
    print(f"     平均耗时: {baseline_avg['duration']:.2f}秒")
    print(f"     平均记录数: {baseline_avg['records']:,.0f}条")
    print(f"     平均吞吐量: {baseline_avg['throughput']:.1f}条/秒")

    print(f"\n   优化版本(B组):")
    print(f"     平均耗时: {optimized_avg['duration']:.2f}秒")
    print(f"     平均记录数: {optimized_avg['records']:,.0f}条")
    print(f"     平均吞吐量: {optimized_avg['throughput']:.1f}条/秒")

    # 计算缓存效果分析
    if baseline_avg['throughput'] > 0:
        improvement = ((optimized_avg['throughput'] - baseline_avg['throughput']) / baseline_avg['throughput']) * 100
        speedup = optimized_avg['throughput'] / baseline_avg['throughput']

        # 计算平均缓存命中率
        avg_cache_hit_rate = 0
        total_cache_hits = 0
        for result in optimized_results:
            cache_stats = result.get('cache_stats', {})
            avg_cache_hit_rate += cache_stats.get('hit_rate', 0)
            total_cache_hits += cache_stats.get('hits', 0)
        avg_cache_hit_rate = avg_cache_hit_rate / len(optimized_results) if optimized_results else 0

        print(f"\n🎯 缓存优化效果分析:")
        print(f"   重复查询场景吞吐量提升: {improvement:+.1f}%")
        print(f"   重复查询加速比: {speedup:.1f}x")
        print(f"   平均缓存命中率: {avg_cache_hit_rate:.1%}")
        print(f"   总缓存命中次数: {total_cache_hits}")

        print(f"\n📊 性能对比:")
        print(f"   基准版本(无缓存): {baseline_avg['throughput']:.1f}条/秒")
        print(f"   优化版本(有缓存): {optimized_avg['throughput']:.1f}条/秒")

        # 验证缓存优化效果
        if avg_cache_hit_rate >= 0.6:  # 60%命中率
            print(f"   ✅ 缓存命中率达标！(>60%)")
        else:
            print(f"   ⚠️  缓存命中率偏低，需要优化缓存策略")

        if speedup >= 2.0:  # 2倍加速
            print(f"   ✅ 缓存加速效果显著！(>2x)")
        elif speedup >= 1.2:  # 20%提升
            print(f"   ✅ 缓存优化有效！(>20%)")
        else:
            print(f"   ⚠️  缓存优化效果有限，需要进一步分析")

        # 分析缓存在重复查询中的价值
        if avg_cache_hit_rate > 0.5 and speedup > 1.5:
            print(f"   🎉 缓存系统在重复查询场景下表现优秀！")
        elif avg_cache_hit_rate > 0.3 and speedup > 1.2:
            print(f"   👍 缓存系统基本有效，有进一步优化空间")
        else:
            print(f"   🔧 缓存系统需要深度优化")

    print(f"\n🔍 详细测试数据:")
    print(f"   基准版本各轮结果:")
    for result in baseline_results:
        print(f"     第{result['round']}轮: {result['throughput']:.1f}条/秒")

    print(f"   优化版本各轮结果:")
    for result in optimized_results:
        print(f"     第{result['round']}轮: {result['throughput']:.1f}条/秒")

    return 0

def handle_redis_environment_check():
    """处理Redis环境检查"""
    import time

    print("\n" + "="*60)
    print("🔧 Redis环境检查")
    print("="*60)

    # 1. 检查Redis Python客户端
    print(f"\n1️⃣ Redis Python客户端检查")
    print("-" * 30)

    try:
        import redis
        print(f"   ✅ Redis客户端已安装: v{redis.__version__}")
    except ImportError:
        print(f"   ❌ Redis客户端未安装")
        print(f"   💡 安装命令: pip3 install redis>=4.3.0")
        return 1

    # 2. 检查Redis服务器连接
    print(f"\n2️⃣ Redis服务器连接检查")
    print("-" * 30)

    try:
        r = redis.Redis(host='localhost', port=6379, decode_responses=True)
        ping_result = r.ping()
        if ping_result:
            print(f"   ✅ Redis服务器连接成功")

            # 获取Redis服务器信息
            info = r.info()
            print(f"   Redis版本: {info.get('redis_version', 'Unknown')}")
            print(f"   运行模式: {info.get('redis_mode', 'Unknown')}")
            print(f"   内存使用: {info.get('used_memory_human', 'Unknown')}")
            print(f"   连接数: {info.get('connected_clients', 'Unknown')}")
        else:
            print(f"   ❌ Redis服务器ping失败")
            return 1

    except redis.ConnectionError as e:
        print(f"   ❌ Redis服务器连接失败: {e}")
        print(f"   💡 启动Redis服务: brew services start redis")
        return 1
    except Exception as e:
        print(f"   ❌ Redis连接异常: {e}")
        return 1

    # 3. 测试Redis基本操作
    print(f"\n3️⃣ Redis基本操作测试")
    print("-" * 30)

    try:
        # 测试设置和获取
        test_key = "quantification:test:redis_check"
        test_value = f"test_value_{int(time.time())}"

        r.set(test_key, test_value, ex=60)  # 60秒过期
        retrieved_value = r.get(test_key)

        if retrieved_value == test_value:
            print(f"   ✅ 设置/获取操作成功")
        else:
            print(f"   ❌ 设置/获取操作失败")
            return 1

        # 测试删除
        r.delete(test_key)
        deleted_value = r.get(test_key)

        if deleted_value is None:
            print(f"   ✅ 删除操作成功")
        else:
            print(f"   ❌ 删除操作失败")
            return 1

    except Exception as e:
        print(f"   ❌ Redis操作测试失败: {e}")
        return 1

    # 4. 检查Redis配置
    print(f"\n4️⃣ Redis配置检查")
    print("-" * 30)

    try:
        config = r.config_get()

        # 检查重要配置项
        max_memory = config.get('maxmemory', '0')
        max_memory_policy = config.get('maxmemory-policy', 'noeviction')
        save_config = config.get('save', '')

        print(f"   最大内存限制: {max_memory} ({'无限制' if max_memory == '0' else '有限制'})")
        print(f"   内存淘汰策略: {max_memory_policy}")
        print(f"   持久化配置: {save_config if save_config else '未配置'}")

        # 检查是否适合缓存使用
        if max_memory_policy in ['allkeys-lru', 'allkeys-lfu', 'volatile-lru', 'volatile-lfu']:
            print(f"   ✅ 内存淘汰策略适合缓存使用")
        else:
            print(f"   ⚠️  建议设置LRU或LFU淘汰策略以优化缓存性能")

    except Exception as e:
        print(f"   ⚠️  Redis配置检查失败: {e}")

    # 5. 性能基准测试
    print(f"\n5️⃣ Redis性能基准测试")
    print("-" * 30)

    try:
        # 写入性能测试
        start_time = time.time()
        test_data = {"test": "data", "timestamp": time.time(), "number": 12345}

        for i in range(1000):
            r.set(f"perf_test:{i}", str(test_data), ex=60)

        write_time = time.time() - start_time
        write_ops = 1000 / write_time

        print(f"   写入性能: {write_ops:.0f} ops/秒 ({write_time:.3f}秒/1000次)")

        # 读取性能测试
        start_time = time.time()

        for i in range(1000):
            r.get(f"perf_test:{i}")

        read_time = time.time() - start_time
        read_ops = 1000 / read_time

        print(f"   读取性能: {read_ops:.0f} ops/秒 ({read_time:.3f}秒/1000次)")

        # 清理测试数据
        for i in range(1000):
            r.delete(f"perf_test:{i}")

        # 性能评估
        if write_ops > 10000 and read_ops > 20000:
            print(f"   ✅ Redis性能优秀，适合高频缓存使用")
        elif write_ops > 5000 and read_ops > 10000:
            print(f"   ✅ Redis性能良好，适合缓存使用")
        else:
            print(f"   ⚠️  Redis性能一般，可能影响缓存效果")

    except Exception as e:
        print(f"   ❌ Redis性能测试失败: {e}")

    # 6. 总结和建议
    print(f"\n6️⃣ 环境检查总结")
    print("-" * 30)

    print(f"   ✅ Redis环境检查完成")
    print(f"   ✅ 所有基本功能正常")
    print(f"   ✅ 可以开始实施Redis多级缓存优化")

    print(f"\n💡 下一步建议:")
    print(f"   1. 实施RedisCache类")
    print(f"   2. 集成多级缓存架构")
    print(f"   3. 进行A/B性能测试验证")

    return 0

def handle_redis_cache_test():
    """处理Redis缓存测试"""
    from src.data.fetcher.data_fetcher_manager import get_cached_data_fetcher
    from datetime import datetime, timedelta
    import time

    print("\n" + "="*60)
    print("🚀 Redis缓存功能测试")
    print("="*60)

    # {{ AURA-X: Modify - 使用DataFetcherManager统一管理，消除重复初始化. Approval: 寸止(ID:阶段1.2). }}

    print(f"📊 测试配置:")
    print(f"   缓存类型: Redis")
    print(f"   测试股票: 10只")
    print(f"   时间范围: 最近1个月")
    print(f"   测试轮数: 3轮")

    # 测试股票
    test_symbols = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ',
                   '600519.SH', '002415.SZ', '600276.SH', '000568.SZ', '600887.SH']

    # 测试时间范围
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    start_str = start_date.strftime('%Y%m%d')
    end_str = end_date.strftime('%Y%m%d')

    # 第1轮：内存缓存基准测试
    print(f"\n🅰️ 第1轮：内存缓存基准测试")
    print("="*50)

    # {{ AURA-X: Modify - 使用DataFetcherManager统一管理，消除直接实例化. Approval: 寸止(ID:架构一致性修复). }}
    memory_fetcher = get_cached_data_fetcher(
        cache_type='memory',
        cache_config={'ttl': 3600}
    )

    memory_start_time = time.time()
    memory_data = memory_fetcher.fetch_market_data(
        symbols=test_symbols,
        data_type='daily',
        start_date=start_str,
        end_date=end_str,
        use_cache=True,
        save=False
    )
    memory_end_time = time.time()
    memory_duration = memory_end_time - memory_start_time
    memory_records = len(memory_data) if not memory_data.empty else 0
    memory_throughput = memory_records / memory_duration if memory_duration > 0 else 0

    print(f"   耗时: {memory_duration:.3f}秒")
    print(f"   记录数: {memory_records:,}条")
    print(f"   吞吐量: {memory_throughput:.1f}条/秒")

    # 获取内存缓存统计
    if hasattr(memory_fetcher.sync_fetcher, 'smart_cache') and memory_fetcher.sync_fetcher.smart_cache:
        memory_stats = memory_fetcher.sync_fetcher.smart_cache.get_stats()
        print(f"   缓存命中率: {memory_stats.get('hit_rate', 0):.1%}")

    # 第2轮：Redis缓存测试
    print(f"\n🅱️ 第2轮：Redis缓存测试")
    print("="*50)

    # {{ AURA-X: Modify - 使用DataFetcherManager获取Redis缓存fetcher. Approval: 寸止(ID:阶段1.2). }}
    redis_fetcher = get_cached_data_fetcher(
        cache_type='redis',
        cache_config={
            'host': 'localhost',
            'port': 6379,
            'db': 0,
            'default_expires': 3600,
            'key_prefix': 'quantification:test:',
            'serialization': 'json'
        }
    )

    redis_start_time = time.time()
    redis_data = redis_fetcher.fetch_market_data(
        symbols=test_symbols,
        data_type='daily',
        start_date=start_str,
        end_date=end_str,
        use_cache=True,
        save=False
    )
    redis_end_time = time.time()
    redis_duration = redis_end_time - redis_start_time
    redis_records = len(redis_data) if not redis_data.empty else 0
    redis_throughput = redis_records / redis_duration if redis_duration > 0 else 0

    print(f"   耗时: {redis_duration:.3f}秒")
    print(f"   记录数: {redis_records:,}条")
    print(f"   吞吐量: {redis_throughput:.1f}条/秒")

    # 获取Redis缓存统计
    if hasattr(redis_fetcher.sync_fetcher, 'smart_cache') and redis_fetcher.sync_fetcher.smart_cache:
        redis_stats = redis_fetcher.sync_fetcher.smart_cache.get_stats()
        print(f"   缓存命中率: {redis_stats.get('hit_rate', 0):.1%}")
        print(f"   Redis内存使用: {redis_stats.get('memory_usage', 'Unknown')}")
        print(f"   Redis缓存键数: {redis_stats.get('cache_size', 'Unknown')}")

    # 第3轮：Redis缓存命中测试
    print(f"\n🔥 第3轮：Redis缓存命中测试")
    print("="*50)

    redis_hit_start_time = time.time()
    redis_hit_data = redis_fetcher.fetch_market_data(
        symbols=test_symbols,
        data_type='daily',
        start_date=start_str,
        end_date=end_str,
        use_cache=True,
        save=False
    )
    redis_hit_end_time = time.time()
    redis_hit_duration = redis_hit_end_time - redis_hit_start_time
    redis_hit_records = len(redis_hit_data) if not redis_hit_data.empty else 0
    redis_hit_throughput = redis_hit_records / redis_hit_duration if redis_hit_duration > 0 else 0

    print(f"   耗时: {redis_hit_duration:.3f}秒")
    print(f"   记录数: {redis_hit_records:,}条")
    print(f"   吞吐量: {redis_hit_throughput:.1f}条/秒")

    # 获取最终Redis缓存统计
    if hasattr(redis_fetcher.sync_fetcher, 'smart_cache') and redis_fetcher.sync_fetcher.smart_cache:
        final_redis_stats = redis_fetcher.sync_fetcher.smart_cache.get_stats()
        print(f"   最终缓存命中率: {final_redis_stats.get('hit_rate', 0):.1%}")
        print(f"   总缓存命中数: {final_redis_stats.get('hits', 0)}")

    # 性能对比分析
    print(f"\n📊 性能对比分析")
    print("="*60)

    print(f"内存缓存性能: {memory_throughput:.1f}条/秒")
    print(f"Redis缓存性能: {redis_throughput:.1f}条/秒")
    print(f"Redis命中性能: {redis_hit_throughput:.1f}条/秒")

    if redis_throughput > 0 and memory_throughput > 0:
        redis_vs_memory = (redis_throughput / memory_throughput - 1) * 100
        print(f"Redis vs 内存: {redis_vs_memory:+.1f}%")

    if redis_hit_throughput > 0 and redis_throughput > 0:
        hit_speedup = redis_hit_throughput / redis_throughput
        print(f"Redis缓存加速比: {hit_speedup:.1f}x")

    # Redis缓存优势分析
    print(f"\n🎯 Redis缓存优势分析")
    print("-" * 40)

    if redis_hit_throughput > memory_throughput * 2:
        print(f"   ✅ Redis缓存命中性能优秀（>2倍内存缓存）")
    elif redis_hit_throughput > memory_throughput:
        print(f"   ✅ Redis缓存命中性能良好（>内存缓存）")
    else:
        print(f"   ⚠️  Redis缓存命中性能需要优化")

    print(f"   🔄 持久化: Redis支持数据持久化，重启后缓存仍然有效")
    print(f"   🌐 分布式: Redis支持多进程/多机器共享缓存")
    print(f"   📈 扩展性: Redis支持集群模式，可水平扩展")

    # 清理测试缓存
    print(f"\n🧹 清理测试缓存")
    print("-" * 40)

    try:
        if hasattr(redis_fetcher.sync_fetcher, 'smart_cache') and redis_fetcher.sync_fetcher.smart_cache:
            redis_cache = redis_fetcher.sync_fetcher.smart_cache.cache
            if hasattr(redis_cache, 'clear'):
                redis_cache.clear()
                print(f"   ✅ Redis测试缓存已清理")
    except Exception as e:
        print(f"   ⚠️  清理Redis缓存失败: {e}")

    print(f"\n🎉 Redis缓存测试完成！")
    print(f"   Redis缓存已成功集成到系统中")
    print(f"   可以开始实施多级缓存架构")

    return 0

def handle_multi_level_cache_test():
    """处理多级缓存测试"""
    from src.data.fetcher.data_fetcher_manager import get_cached_data_fetcher
    from datetime import datetime, timedelta
    import time

    print("\n" + "="*60)
    print("🚀 多级缓存架构测试")
    print("="*60)

    # {{ AURA-X: Modify - 使用DataFetcherManager统一管理，消除重复初始化. Approval: 寸止(ID:阶段1.2). }}

    print(f"📊 测试配置:")
    print(f"   缓存架构: L1(内存) + L2(Redis) + L3(磁盘)")
    print(f"   测试股票: 20只")
    print(f"   时间范围: 最近1个月")
    print(f"   测试轮数: 4轮")

    # 测试股票
    test_symbols = [
        '000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ',
        '600519.SH', '002415.SZ', '600276.SH', '000568.SZ', '600887.SH',
        '000063.SZ', '600104.SH', '002304.SZ', '600585.SH', '000725.SZ',
        '600031.SH', '002142.SZ', '600009.SH', '000776.SZ', '600028.SH'
    ]

    # 测试时间范围
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    start_str = start_date.strftime('%Y%m%d')
    end_str = end_date.strftime('%Y%m%d')

    # 多级缓存配置
    multi_cache_config = {
        'strategy': 'multi',
        'l1': {
            'type': 'memory',
            'max_size': 1000,
            'ttl': 1800,
            'enabled': True
        },
        'l2': {
            'type': 'redis',
            'host': 'localhost',
            'port': 6379,
            'db': 1,  # 使用db=1避免与其他测试冲突
            'ttl': 3600,
            'key_prefix': 'quantification:multi_test:',
            'serialization': 'json',
            'enabled': True
        },
        'l3': {
            'type': 'disk',
            'cache_dir': 'data/cache/multi_test',
            'ttl': 86400,
            'enabled': True
        },
        'penetration': {
            'l1_to_l2': True,
            'l2_to_l3': True,
            'write_back': True
        }
    }

    # 第1轮：多级缓存初始测试（冷启动）
    print(f"\n🥶 第1轮：多级缓存冷启动测试")
    print("="*50)

    # {{ AURA-X: Modify - 使用DataFetcherManager获取多级缓存fetcher. Approval: 寸止(ID:阶段1.2). }}
    multi_fetcher = get_cached_data_fetcher(
        cache_type='multi',
        cache_config=multi_cache_config
    )

    cold_start_time = time.time()
    cold_data = multi_fetcher.fetch_market_data(
        symbols=test_symbols,
        data_type='daily',
        start_date=start_str,
        end_date=end_str,
        use_cache=True,
        save=False
    )
    cold_end_time = time.time()
    cold_duration = cold_end_time - cold_start_time
    cold_records = len(cold_data) if not cold_data.empty else 0
    cold_throughput = cold_records / cold_duration if cold_duration > 0 else 0

    print(f"   耗时: {cold_duration:.3f}秒")
    print(f"   记录数: {cold_records:,}条")
    print(f"   吞吐量: {cold_throughput:.1f}条/秒")

    # 获取多级缓存统计
    if hasattr(multi_fetcher.sync_fetcher, 'smart_cache') and multi_fetcher.sync_fetcher.smart_cache:
        cold_stats = multi_fetcher.sync_fetcher.smart_cache.get_stats()
        print(f"   L1命中率: {cold_stats.get('l1_hit_rate', 0):.1%}")
        print(f"   L2命中率: {cold_stats.get('l2_hit_rate', 0):.1%}")
        print(f"   L3命中率: {cold_stats.get('l3_hit_rate', 0):.1%}")
        print(f"   总体命中率: {cold_stats.get('overall_hit_rate', 0):.1%}")

    # 第2轮：L1缓存命中测试（热数据）
    print(f"\n🔥 第2轮：L1缓存命中测试")
    print("="*50)

    l1_hit_start_time = time.time()
    l1_hit_data = multi_fetcher.fetch_market_data(
        symbols=test_symbols,
        data_type='daily',
        start_date=start_str,
        end_date=end_str,
        use_cache=True,
        save=False
    )
    l1_hit_end_time = time.time()
    l1_hit_duration = l1_hit_end_time - l1_hit_start_time
    l1_hit_records = len(l1_hit_data) if not l1_hit_data.empty else 0
    l1_hit_throughput = l1_hit_records / l1_hit_duration if l1_hit_duration > 0 else 0

    print(f"   耗时: {l1_hit_duration:.3f}秒")
    print(f"   记录数: {l1_hit_records:,}条")
    print(f"   吞吐量: {l1_hit_throughput:.1f}条/秒")

    # 获取L1命中后的统计
    if hasattr(multi_fetcher.sync_fetcher, 'smart_cache') and multi_fetcher.sync_fetcher.smart_cache:
        l1_stats = multi_fetcher.sync_fetcher.smart_cache.get_stats()
        print(f"   L1命中率: {l1_stats.get('l1_hit_rate', 0):.1%}")
        print(f"   L2命中率: {l1_stats.get('l2_hit_rate', 0):.1%}")
        print(f"   L3命中率: {l1_stats.get('l3_hit_rate', 0):.1%}")
        print(f"   总体命中率: {l1_stats.get('overall_hit_rate', 0):.1%}")
        print(f"   回写次数: {l1_stats.get('write_backs', 0)}")

    # 第3轮：清空L1缓存，测试L2缓存命中
    print(f"\n🌡️ 第3轮：L2缓存命中测试")
    print("="*50)

    # 清空L1缓存
    if hasattr(multi_fetcher.sync_fetcher, 'smart_cache') and multi_fetcher.sync_fetcher.smart_cache:
        cache = multi_fetcher.sync_fetcher.smart_cache.cache
        if hasattr(cache, 'l1_cache') and cache.l1_cache:
            cache.l1_cache.clear()
            print(f"   L1缓存已清空")

    l2_hit_start_time = time.time()
    l2_hit_data = multi_fetcher.fetch_market_data(
        symbols=test_symbols,
        data_type='daily',
        start_date=start_str,
        end_date=end_str,
        use_cache=True,
        save=False
    )
    l2_hit_end_time = time.time()
    l2_hit_duration = l2_hit_end_time - l2_hit_start_time
    l2_hit_records = len(l2_hit_data) if not l2_hit_data.empty else 0
    l2_hit_throughput = l2_hit_records / l2_hit_duration if l2_hit_duration > 0 else 0

    print(f"   耗时: {l2_hit_duration:.3f}秒")
    print(f"   记录数: {l2_hit_records:,}条")
    print(f"   吞吐量: {l2_hit_throughput:.1f}条/秒")

    # 第4轮：清空L1+L2缓存，测试L3缓存命中
    print(f"\n❄️ 第4轮：L3缓存命中测试")
    print("="*50)

    # 清空L1和L2缓存
    if hasattr(multi_fetcher.sync_fetcher, 'smart_cache') and multi_fetcher.sync_fetcher.smart_cache:
        cache = multi_fetcher.sync_fetcher.smart_cache.cache
        if hasattr(cache, 'l1_cache') and cache.l1_cache:
            cache.l1_cache.clear()
        if hasattr(cache, 'l2_cache') and cache.l2_cache:
            cache.l2_cache.clear()
        print(f"   L1和L2缓存已清空")

    l3_hit_start_time = time.time()
    l3_hit_data = multi_fetcher.fetch_market_data(
        symbols=test_symbols,
        data_type='daily',
        start_date=start_str,
        end_date=end_str,
        use_cache=True,
        save=False
    )
    l3_hit_end_time = time.time()
    l3_hit_duration = l3_hit_end_time - l3_hit_start_time
    l3_hit_records = len(l3_hit_data) if not l3_hit_data.empty else 0
    l3_hit_throughput = l3_hit_records / l3_hit_duration if l3_hit_duration > 0 else 0

    print(f"   耗时: {l3_hit_duration:.3f}秒")
    print(f"   记录数: {l3_hit_records:,}条")
    print(f"   吞吐量: {l3_hit_throughput:.1f}条/秒")

    # 获取最终统计
    if hasattr(multi_fetcher.sync_fetcher, 'smart_cache') and multi_fetcher.sync_fetcher.smart_cache:
        final_stats = multi_fetcher.sync_fetcher.smart_cache.get_stats()
        print(f"   最终L1命中率: {final_stats.get('l1_hit_rate', 0):.1%}")
        print(f"   最终L2命中率: {final_stats.get('l2_hit_rate', 0):.1%}")
        print(f"   最终L3命中率: {final_stats.get('l3_hit_rate', 0):.1%}")
        print(f"   最终总体命中率: {final_stats.get('overall_hit_rate', 0):.1%}")
        print(f"   总回写次数: {final_stats.get('write_backs', 0)}")

    return 0

def handle_simple_multi_cache_test():
    """处理简化的多级缓存测试"""
    from src.data.storage.cache.cache_factory import CacheFactory
    import time

    print("\n" + "="*60)
    print("🚀 简化多级缓存测试")
    print("="*60)

    print(f"📊 测试配置:")
    print(f"   缓存架构: L1(内存) + L2(Redis) + L3(磁盘)")
    print(f"   测试数据: 100个键值对")
    print(f"   测试轮数: 4轮")

    # 多级缓存配置
    multi_cache_config = {
        'l1': {
            'type': 'memory',
            'max_size': 1000,
            'ttl': 1800
        },
        'l2': {
            'type': 'redis',
            'host': 'localhost',
            'port': 6379,
            'db': 2,  # 使用db=2避免冲突
            'ttl': 3600,
            'key_prefix': 'quantification:simple_test:',
            'serialization': 'json'
        },
        'l3': {
            'type': 'disk',
            'cache_dir': 'data/cache/simple_test',
            'ttl': 86400,
            'max_size_mb': 100
        }
    }

    try:
        # 创建多级缓存
        print(f"\n🔧 创建多级缓存...")
        multi_cache = CacheFactory.create_cache('multi', multi_cache_config)
        print(f"   ✅ 多级缓存创建成功")

        # 第1轮：写入测试数据
        print(f"\n📝 第1轮：写入测试数据")
        print("="*50)

        test_data = {}
        write_start_time = time.time()

        for i in range(100):
            key = f"test_key_{i}"
            value = {
                'id': i,
                'name': f'test_item_{i}',
                'timestamp': time.time(),
                'data': [j for j in range(10)]
            }
            test_data[key] = value

            success = multi_cache.set(key, value, 3600)
            if not success:
                print(f"   ⚠️  写入失败: {key}")

        write_end_time = time.time()
        write_duration = write_end_time - write_start_time
        write_ops = 100 / write_duration if write_duration > 0 else 0

        print(f"   耗时: {write_duration:.3f}秒")
        print(f"   写入速度: {write_ops:.1f} ops/秒")

        # 获取统计信息
        stats = multi_cache.get_stats()
        print(f"   L1写入: {stats.get('l1_sets', 0)}")
        print(f"   L2写入: {stats.get('l2_sets', 0)}")
        print(f"   L3写入: {stats.get('l3_sets', 0)}")

        # 第2轮：L1缓存命中测试
        print(f"\n🔥 第2轮：L1缓存命中测试")
        print("="*50)

        l1_hit_start_time = time.time()
        l1_hit_count = 0

        for i in range(100):
            key = f"test_key_{i}"
            value = multi_cache.get(key)
            if value is not None:
                l1_hit_count += 1

        l1_hit_end_time = time.time()
        l1_hit_duration = l1_hit_end_time - l1_hit_start_time
        l1_hit_ops = 100 / l1_hit_duration if l1_hit_duration > 0 else 0

        print(f"   耗时: {l1_hit_duration:.3f}秒")
        print(f"   读取速度: {l1_hit_ops:.1f} ops/秒")
        print(f"   命中数量: {l1_hit_count}/100")

        # 获取L1命中后的统计
        stats = multi_cache.get_stats()
        print(f"   L1命中率: {stats.get('l1_hit_rate', 0):.1%}")
        print(f"   L2命中率: {stats.get('l2_hit_rate', 0):.1%}")
        print(f"   L3命中率: {stats.get('l3_hit_rate', 0):.1%}")
        print(f"   总体命中率: {stats.get('overall_hit_rate', 0):.1%}")

        # 第3轮：清空L1，测试L2缓存命中
        print(f"\n🌡️ 第3轮：L2缓存命中测试")
        print("="*50)

        # 清空L1缓存
        if hasattr(multi_cache, 'l1_cache') and multi_cache.l1_cache:
            multi_cache.l1_cache.clear()
            print(f"   L1缓存已清空")

        l2_hit_start_time = time.time()
        l2_hit_count = 0

        for i in range(100):
            key = f"test_key_{i}"
            value = multi_cache.get(key)
            if value is not None:
                l2_hit_count += 1

        l2_hit_end_time = time.time()
        l2_hit_duration = l2_hit_end_time - l2_hit_start_time
        l2_hit_ops = 100 / l2_hit_duration if l2_hit_duration > 0 else 0

        print(f"   耗时: {l2_hit_duration:.3f}秒")
        print(f"   读取速度: {l2_hit_ops:.1f} ops/秒")
        print(f"   命中数量: {l2_hit_count}/100")

        # 第4轮：清空L1+L2，测试L3缓存命中
        print(f"\n❄️ 第4轮：L3缓存命中测试")
        print("="*50)

        # 清空L1和L2缓存
        if hasattr(multi_cache, 'l1_cache') and multi_cache.l1_cache:
            multi_cache.l1_cache.clear()
        if hasattr(multi_cache, 'l2_cache') and multi_cache.l2_cache:
            multi_cache.l2_cache.clear()
        print(f"   L1和L2缓存已清空")

        l3_hit_start_time = time.time()
        l3_hit_count = 0

        for i in range(100):
            key = f"test_key_{i}"
            value = multi_cache.get(key)
            if value is not None:
                l3_hit_count += 1

        l3_hit_end_time = time.time()
        l3_hit_duration = l3_hit_end_time - l3_hit_start_time
        l3_hit_ops = 100 / l3_hit_duration if l3_hit_duration > 0 else 0

        print(f"   耗时: {l3_hit_duration:.3f}秒")
        print(f"   读取速度: {l3_hit_ops:.1f} ops/秒")
        print(f"   命中数量: {l3_hit_count}/100")

        # 获取最终统计
        final_stats = multi_cache.get_stats()
        print(f"\n📊 最终统计信息")
        print("="*50)
        print(f"   L1命中率: {final_stats.get('l1_hit_rate', 0):.1%}")
        print(f"   L2命中率: {final_stats.get('l2_hit_rate', 0):.1%}")
        print(f"   L3命中率: {final_stats.get('l3_hit_rate', 0):.1%}")
        print(f"   总体命中率: {final_stats.get('overall_hit_rate', 0):.1%}")
        print(f"   回写次数: {final_stats.get('write_backs', 0)}")
        print(f"   错误次数: {final_stats.get('errors', 0)}")

        # 性能对比分析
        print(f"\n🎯 性能对比分析")
        print("="*50)
        print(f"   写入性能: {write_ops:.1f} ops/秒")
        print(f"   L1读取性能: {l1_hit_ops:.1f} ops/秒")
        print(f"   L2读取性能: {l2_hit_ops:.1f} ops/秒")
        print(f"   L3读取性能: {l3_hit_ops:.1f} ops/秒")

        if l1_hit_ops > 0 and l2_hit_ops > 0:
            l1_vs_l2 = l1_hit_ops / l2_hit_ops
            print(f"   L1 vs L2加速比: {l1_vs_l2:.1f}x")

        if l2_hit_ops > 0 and l3_hit_ops > 0:
            l2_vs_l3 = l2_hit_ops / l3_hit_ops
            print(f"   L2 vs L3加速比: {l2_vs_l3:.1f}x")

        # 清理测试数据
        print(f"\n🧹 清理测试数据")
        print("-" * 40)

        multi_cache.clear()
        print(f"   ✅ 所有测试缓存已清理")

        print(f"\n🎉 简化多级缓存测试完成！")
        print(f"   多级缓存架构工作正常")
        print(f"   L1(内存) > L2(Redis) > L3(磁盘)性能梯度符合预期")

        return 0

    except Exception as e:
        print(f"\n❌ 多级缓存测试失败: {e}")
        logger.error(f"多级缓存测试异常: {e}")
        return 1

def handle_real_world_multi_cache_test():
    """处理真实场景的多级缓存性能测试"""
    from src.data.fetcher.data_fetcher_manager import get_data_fetcher, get_cached_data_fetcher
    from datetime import datetime, timedelta
    import time

    print("\n" + "="*60)
    print("🌍 真实场景多级缓存性能测试")
    print("="*60)

    print(f"📊 测试配置:")
    print(f"   缓存架构: 从app.yaml自动加载多级缓存配置")
    print(f"   测试股票: 50只热门股票")
    print(f"   时间范围: 最近2个月")
    print(f"   测试场景: 3轮对比测试")

    # 热门测试股票
    test_symbols = [
        '000001.SZ', '000002.SZ', '000858.SZ', '000063.SZ', '000725.SZ',
        '600000.SH', '600036.SH', '600519.SH', '600276.SH', '600887.SH',
        '600104.SH', '600585.SH', '600031.SH', '600009.SH', '600028.SH',
        '002415.SZ', '002304.SZ', '002142.SZ', '000568.SZ', '000776.SZ',
        '300014.SZ', '300015.SZ', '300059.SZ', '300124.SZ', '300750.SZ',
        '688009.SH', '688012.SH', '688036.SH', '688111.SH', '688169.SH',
        '000100.SZ', '000157.SZ', '000338.SZ', '000625.SZ', '000876.SZ',
        '600048.SH', '600050.SH', '600196.SH', '600309.SH', '600438.SH',
        '600703.SH', '600745.SH', '600837.SH', '600900.SH', '601012.SH',
        '601088.SH', '601166.SH', '601318.SH', '601398.SH', '601857.SH'
    ]

    # 测试时间范围
    end_date = datetime.now()
    start_date = end_date - timedelta(days=60)
    start_str = start_date.strftime('%Y%m%d')
    end_str = end_date.strftime('%Y%m%d')

    # 第1轮：无缓存基准测试
    print(f"\n🚫 第1轮：无缓存基准测试")
    print("="*50)

    # {{ AURA-X: Modify - 使用DataFetcherManager统一管理，消除直接实例化. Approval: 寸止(ID:架构一致性修复). }}
    no_cache_fetcher = get_data_fetcher({
        'use_cache': False,  # 禁用缓存
        'auto_async': False   # 使用同步模式确保一致性
    })

    no_cache_start_time = time.time()
    no_cache_data = no_cache_fetcher.fetch_market_data(
        symbols=test_symbols,
        data_type='daily',
        start_date=start_str,
        end_date=end_str,
        use_cache=False,
        save=False
    )
    no_cache_end_time = time.time()
    no_cache_duration = no_cache_end_time - no_cache_start_time
    no_cache_records = len(no_cache_data) if not no_cache_data.empty else 0
    no_cache_throughput = no_cache_records / no_cache_duration if no_cache_duration > 0 else 0

    print(f"   耗时: {no_cache_duration:.3f}秒")
    print(f"   记录数: {no_cache_records:,}条")
    print(f"   吞吐量: {no_cache_throughput:.1f}条/秒")
    print(f"   基准性能: {no_cache_throughput:.1f}条/秒")

    # 第2轮：多级缓存冷启动测试
    print(f"\n🥶 第2轮：多级缓存冷启动测试")
    print("="*50)

    # {{ AURA-X: Modify - 使用DataFetcherManager统一管理，消除直接实例化. Approval: 寸止(ID:架构一致性修复). }}
    multi_cache_fetcher = get_cached_data_fetcher(
        cache_type='multi',  # 使用多级缓存
        cache_config={'auto_async': False}   # 使用同步模式确保一致性
    )

    # 清空所有缓存确保冷启动
    if hasattr(multi_cache_fetcher.sync_fetcher, 'smart_cache') and multi_cache_fetcher.sync_fetcher.smart_cache:
        cache = multi_cache_fetcher.sync_fetcher.smart_cache.cache
        if hasattr(cache, 'clear'):
            cache.clear()
            print(f"   所有缓存已清空，确保冷启动")

    cold_start_time = time.time()
    cold_data = multi_cache_fetcher.fetch_market_data(
        symbols=test_symbols,
        data_type='daily',
        start_date=start_str,
        end_date=end_str,
        use_cache=True,
        save=False
    )
    cold_end_time = time.time()
    cold_duration = cold_end_time - cold_start_time
    cold_records = len(cold_data) if not cold_data.empty else 0
    cold_throughput = cold_records / cold_duration if cold_duration > 0 else 0

    print(f"   耗时: {cold_duration:.3f}秒")
    print(f"   记录数: {cold_records:,}条")
    print(f"   吞吐量: {cold_throughput:.1f}条/秒")

    # 获取冷启动后的缓存统计
    if hasattr(multi_cache_fetcher.sync_fetcher, 'smart_cache') and multi_cache_fetcher.sync_fetcher.smart_cache:
        cold_stats = multi_cache_fetcher.sync_fetcher.smart_cache.get_stats()
        print(f"   L1命中率: {cold_stats.get('l1_hit_rate', 0):.1%}")
        print(f"   L2命中率: {cold_stats.get('l2_hit_rate', 0):.1%}")
        print(f"   L3命中率: {cold_stats.get('l3_hit_rate', 0):.1%}")
        print(f"   总体命中率: {cold_stats.get('overall_hit_rate', 0):.1%}")

    # 第3轮：多级缓存热启动测试
    print(f"\n🔥 第3轮：多级缓存热启动测试")
    print("="*50)

    hot_start_time = time.time()
    hot_data = multi_cache_fetcher.fetch_market_data(
        symbols=test_symbols,
        data_type='daily',
        start_date=start_str,
        end_date=end_str,
        use_cache=True,
        save=False
    )
    hot_end_time = time.time()
    hot_duration = hot_end_time - hot_start_time
    hot_records = len(hot_data) if not hot_data.empty else 0
    hot_throughput = hot_records / hot_duration if hot_duration > 0 else 0

    print(f"   耗时: {hot_duration:.3f}秒")
    print(f"   记录数: {hot_records:,}条")
    print(f"   吞吐量: {hot_throughput:.1f}条/秒")

    # 获取热启动后的缓存统计
    if hasattr(multi_cache_fetcher.sync_fetcher, 'smart_cache') and multi_cache_fetcher.sync_fetcher.smart_cache:
        hot_stats = multi_cache_fetcher.sync_fetcher.smart_cache.get_stats()
        print(f"   L1命中率: {hot_stats.get('l1_hit_rate', 0):.1%}")
        print(f"   L2命中率: {hot_stats.get('l2_hit_rate', 0):.1%}")
        print(f"   L3命中率: {hot_stats.get('l3_hit_rate', 0):.1%}")
        print(f"   总体命中率: {hot_stats.get('overall_hit_rate', 0):.1%}")
        print(f"   回写次数: {hot_stats.get('write_backs', 0)}")

    # 性能对比分析
    print(f"\n📊 性能对比分析")
    print("="*60)

    print(f"基准性能 (无缓存): {no_cache_throughput:.1f}条/秒")
    print(f"冷启动性能 (多级缓存): {cold_throughput:.1f}条/秒")
    print(f"热启动性能 (多级缓存): {hot_throughput:.1f}条/秒")

    # 计算性能提升
    if no_cache_throughput > 0:
        cold_improvement = (cold_throughput / no_cache_throughput - 1) * 100
        hot_improvement = (hot_throughput / no_cache_throughput - 1) * 100

        print(f"\n🚀 性能提升分析:")
        print(f"   冷启动 vs 基准: {cold_improvement:+.1f}%")
        print(f"   热启动 vs 基准: {hot_improvement:+.1f}%")

        if hot_throughput > cold_throughput:
            cache_speedup = hot_throughput / cold_throughput
            print(f"   缓存加速比: {cache_speedup:.1f}x")

    # 时间节省分析
    print(f"\n⏱️ 时间节省分析:")
    print(f"   基准耗时: {no_cache_duration:.3f}秒")
    print(f"   冷启动耗时: {cold_duration:.3f}秒")
    print(f"   热启动耗时: {hot_duration:.3f}秒")

    if no_cache_duration > 0:
        cold_time_saved = no_cache_duration - cold_duration
        hot_time_saved = no_cache_duration - hot_duration

        print(f"   冷启动节省: {cold_time_saved:.3f}秒")
        print(f"   热启动节省: {hot_time_saved:.3f}秒")

    # 数据一致性验证
    print(f"\n🔍 数据一致性验证:")
    print(f"   基准记录数: {no_cache_records:,}条")
    print(f"   冷启动记录数: {cold_records:,}条")
    print(f"   热启动记录数: {hot_records:,}条")

    if no_cache_records == cold_records == hot_records:
        print(f"   ✅ 数据一致性验证通过")
    else:
        print(f"   ⚠️  数据一致性存在差异")

    # 多级缓存效果评估
    print(f"\n🎯 多级缓存效果评估:")

    if hot_improvement > 100:
        print(f"   ✅ 缓存效果优秀 (>100%提升)")
    elif hot_improvement > 50:
        print(f"   ✅ 缓存效果良好 (>50%提升)")
    elif hot_improvement > 20:
        print(f"   ✅ 缓存效果一般 (>20%提升)")
    else:
        print(f"   ⚠️  缓存效果有限 (<20%提升)")

    # 缓存架构优势
    print(f"\n🏗️ 多级缓存架构优势:")
    print(f"   🚀 L1内存缓存: 超高速访问，适合热点数据")
    print(f"   🌐 L2 Redis缓存: 分布式共享，支持多进程访问")
    print(f"   💾 L3磁盘缓存: 持久化存储，重启后数据仍然有效")
    print(f"   🔄 智能回写: 下级命中时自动回写到上级缓存")
    print(f"   📊 性能监控: 详细的命中率和性能统计")

    # 清理测试缓存
    print(f"\n🧹 清理测试缓存")
    print("-" * 40)

    try:
        if hasattr(multi_cache_fetcher.sync_fetcher, 'smart_cache') and multi_cache_fetcher.sync_fetcher.smart_cache:
            cache = multi_cache_fetcher.sync_fetcher.smart_cache.cache
            if hasattr(cache, 'clear'):
                cache.clear()
                print(f"   ✅ 多级缓存已清理")
    except Exception as e:
        print(f"   ⚠️  清理缓存失败: {e}")

    print(f"\n🎉 真实场景多级缓存性能测试完成！")
    print(f"   多级缓存架构在实际数据获取场景下表现优秀")
    print(f"   L1+L2+L3三级缓存策略有效提升了系统性能")

    return 0

def handle_cache_performance_monitor():
    """处理缓存性能监控"""
    from src.data.fetcher.data_fetcher_manager import get_cached_data_fetcher
    import time
    import threading

    print("\n" + "="*60)
    print("📊 缓存性能实时监控")
    print("="*60)

    print(f"🎯 监控配置:")
    print(f"   缓存架构: 多级缓存 (L1+L2+L3)")
    print(f"   监控间隔: 5秒")
    print(f"   监控时长: 60秒")
    print(f"   测试负载: 持续数据获取")

    # {{ AURA-X: Modify - 使用DataFetcherManager统一管理，消除直接实例化. Approval: 寸止(ID:架构一致性修复). }}
    # 创建多级缓存数据获取器
    fetcher = get_cached_data_fetcher(
        cache_type='multi',
        cache_config={'auto_async': False}
    )

    # 测试股票列表
    test_symbols = [
        '000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '600519.SH',
        '000858.SZ', '002415.SZ', '600276.SH', '000568.SZ', '600887.SH'
    ]

    # 监控状态
    monitoring = True
    monitor_stats = {
        'total_requests': 0,
        'total_records': 0,
        'total_time': 0.0,
        'avg_throughput': 0.0,
        'cache_stats_history': []
    }

    def monitor_worker():
        """监控工作线程"""
        nonlocal monitoring, monitor_stats

        from datetime import datetime, timedelta
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        start_str = start_date.strftime('%Y%m%d')
        end_str = end_date.strftime('%Y%m%d')

        while monitoring:
            try:
                # 执行数据获取
                start_time = time.time()
                data = fetcher.fetch_market_data(
                    symbols=test_symbols,
                    data_type='daily',
                    start_date=start_str,
                    end_date=end_str,
                    use_cache=True,
                    save=False
                )
                end_time = time.time()

                # 更新统计
                duration = end_time - start_time
                records = len(data) if not data.empty else 0
                throughput = records / duration if duration > 0 else 0

                monitor_stats['total_requests'] += 1
                monitor_stats['total_records'] += records
                monitor_stats['total_time'] += duration
                monitor_stats['avg_throughput'] = monitor_stats['total_records'] / monitor_stats['total_time'] if monitor_stats['total_time'] > 0 else 0

                # 获取缓存统计
                if hasattr(fetcher.sync_fetcher, 'smart_cache') and fetcher.sync_fetcher.smart_cache:
                    cache_stats = fetcher.sync_fetcher.smart_cache.get_stats()
                    cache_stats['timestamp'] = time.time()
                    monitor_stats['cache_stats_history'].append(cache_stats)

                    # 保持最近20条记录
                    if len(monitor_stats['cache_stats_history']) > 20:
                        monitor_stats['cache_stats_history'].pop(0)

                # 等待间隔
                time.sleep(5)

            except Exception as e:
                logger.error(f"监控工作线程异常: {e}")
                time.sleep(5)

    def display_monitor():
        """显示监控信息"""
        nonlocal monitoring, monitor_stats

        start_time = time.time()

        while monitoring and (time.time() - start_time) < 60:
            try:
                # 清屏并显示监控信息
                print("\033[2J\033[H")  # 清屏
                print("="*60)
                print("📊 缓存性能实时监控")
                print("="*60)

                current_time = time.strftime("%Y-%m-%d %H:%M:%S")
                elapsed = time.time() - start_time

                print(f"⏰ 当前时间: {current_time}")
                print(f"⏱️  监控时长: {elapsed:.1f}秒 / 60秒")
                print(f"🔄 总请求数: {monitor_stats['total_requests']}")
                print(f"📊 总记录数: {monitor_stats['total_records']:,}")
                print(f"⚡ 平均吞吐量: {monitor_stats['avg_throughput']:.1f}条/秒")

                # 显示最新的缓存统计
                if monitor_stats['cache_stats_history']:
                    latest_stats = monitor_stats['cache_stats_history'][-1]

                    print(f"\n🎯 多级缓存统计:")
                    print(f"   L1命中率: {latest_stats.get('l1_hit_rate', 0):.1%}")
                    print(f"   L2命中率: {latest_stats.get('l2_hit_rate', 0):.1%}")
                    print(f"   L3命中率: {latest_stats.get('l3_hit_rate', 0):.1%}")
                    print(f"   总体命中率: {latest_stats.get('overall_hit_rate', 0):.1%}")
                    print(f"   回写次数: {latest_stats.get('write_backs', 0)}")
                    print(f"   错误次数: {latest_stats.get('errors', 0)}")

                    # L1缓存详情
                    print(f"\n🚀 L1内存缓存:")
                    print(f"   命中数: {latest_stats.get('l1_hits', 0)}")
                    print(f"   未命中数: {latest_stats.get('l1_misses', 0)}")
                    print(f"   缓存大小: {latest_stats.get('l1_cache_size', 0)}")

                    # L2缓存详情
                    print(f"\n🌐 L2 Redis缓存:")
                    print(f"   命中数: {latest_stats.get('l2_hits', 0)}")
                    print(f"   未命中数: {latest_stats.get('l2_misses', 0)}")
                    print(f"   缓存大小: {latest_stats.get('l2_cache_size', 0)}")
                    print(f"   内存使用: {latest_stats.get('l2_memory_usage', 'Unknown')}")

                    # L3缓存详情
                    print(f"\n💾 L3磁盘缓存:")
                    print(f"   命中数: {latest_stats.get('l3_hits', 0)}")
                    print(f"   未命中数: {latest_stats.get('l3_misses', 0)}")
                    print(f"   缓存大小: {latest_stats.get('l3_cache_size', 0)}")

                # 显示性能趋势
                if len(monitor_stats['cache_stats_history']) >= 2:
                    print(f"\n📈 性能趋势 (最近5次):")
                    recent_stats = monitor_stats['cache_stats_history'][-5:]

                    for i, stats in enumerate(recent_stats):
                        timestamp = stats.get('timestamp', 0)
                        time_str = time.strftime("%H:%M:%S", time.localtime(timestamp))
                        hit_rate = stats.get('overall_hit_rate', 0)
                        print(f"   {time_str}: 命中率 {hit_rate:.1%}")

                print(f"\n💡 按 Ctrl+C 停止监控")
                print("="*60)

                time.sleep(5)

            except KeyboardInterrupt:
                monitoring = False
                break
            except Exception as e:
                logger.error(f"显示监控信息异常: {e}")
                time.sleep(5)

    try:
        print(f"\n🚀 启动缓存性能监控...")

        # 启动监控工作线程
        monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        monitor_thread.start()

        # 等待一下让监控线程开始工作
        time.sleep(2)

        # 显示监控信息
        display_monitor()

    except KeyboardInterrupt:
        print(f"\n\n⏹️  用户中断监控")
    except Exception as e:
        print(f"\n\n❌ 监控异常: {e}")
        logger.error(f"缓存性能监控异常: {e}")
    finally:
        monitoring = False

        # 显示最终统计
        print(f"\n📊 监控总结:")
        print(f"   总请求数: {monitor_stats['total_requests']}")
        print(f"   总记录数: {monitor_stats['total_records']:,}")
        print(f"   总耗时: {monitor_stats['total_time']:.3f}秒")
        print(f"   平均吞吐量: {monitor_stats['avg_throughput']:.1f}条/秒")

        if monitor_stats['cache_stats_history']:
            final_stats = monitor_stats['cache_stats_history'][-1]
            print(f"   最终命中率: {final_stats.get('overall_hit_rate', 0):.1%}")
            print(f"   总回写次数: {final_stats.get('write_backs', 0)}")

        print(f"\n🎉 缓存性能监控完成！")

    return 0

def handle_cache_preload():
    """处理缓存预热"""
    from src.data.storage.cache.cache_preloader import CachePreloader
    from src.utils.config.config_factory import config_factory
    import time

    print("\n" + "="*60)
    print("🔥 缓存预热管理")
    print("="*60)

    # 获取Tushare配置
    tushare_config = config_factory.get_tushare_config()
    api_config = tushare_config.get('api', {})
    token = api_config.get('token', '')

    if not token:
        logger.error("未找到Tushare API Token，请在config/data_source.yaml中配置")
        return 1

    try:
        # 创建缓存预热器
        preloader = CachePreloader(tushare_token=token)

        # 获取预热状态
        status = preloader.get_preload_status()

        print(f"📊 预热配置状态:")
        print(f"   启用状态: {'✅ 已启用' if status['enabled'] else '❌ 未启用'}")
        print(f"   预热股票数: {status['symbols_count']}只")
        print(f"   数据类型: {', '.join(status['data_types'])}")
        print(f"   时间范围: {status['date_range'][0]} - {status['date_range'][1]}")

        # 显示操作选项
        print(f"\n🎯 可用操作:")
        print(f"   1. 执行缓存预热")
        print(f"   2. 强制预热（忽略配置）")
        print(f"   3. 查看预热状态")
        print(f"   4. 清空预热缓存")
        print(f"   5. 返回主菜单")

        while True:
            choice = input(f"\n请选择操作 (1-5): ").strip()

            if choice == "1":
                # 执行缓存预热
                print(f"\n🚀 开始缓存预热...")
                start_time = time.time()

                result = preloader.preload_cache(force=False)

                end_time = time.time()
                total_time = end_time - start_time

                if result['status'] == 'skipped':
                    print(f"   ⏭️  预热已跳过: {result['reason']}")
                elif result['status'] == 'completed':
                    summary = result['summary']
                    print(f"\n✅ 预热完成!")
                    print(f"   总股票数: {summary['total_symbols']}")
                    print(f"   成功股票数: {summary['successful_symbols']}")
                    print(f"   失败股票数: {summary['failed_symbols']}")
                    print(f"   总记录数: {summary['total_records']:,}")
                    print(f"   总耗时: {summary['total_time']:.3f}秒")
                    print(f"   平均吞吐量: {summary['avg_throughput']:.1f}条/秒")

                    # 显示缓存改进情况
                    if 'cache_improvement' in result:
                        improvement = result['cache_improvement']
                        print(f"\n📈 缓存改进:")
                        print(f"   L1缓存增加: {improvement['l1_cache_size_increase']}条")
                        print(f"   L2缓存增加: {improvement['l2_cache_size_increase']}条")
                        print(f"   L3缓存增加: {improvement['l3_cache_size_increase']}条")
                        print(f"   总写入增加: {improvement['total_sets_increase']}次")
                else:
                    print(f"   ❌ 预热失败: {result.get('error', '未知错误')}")

            elif choice == "2":
                # 强制预热
                print(f"\n🔥 开始强制预热...")
                start_time = time.time()

                result = preloader.preload_cache(force=True)

                end_time = time.time()
                total_time = end_time - start_time

                if result['status'] == 'completed':
                    summary = result['summary']
                    print(f"\n✅ 强制预热完成!")
                    print(f"   总股票数: {summary['total_symbols']}")
                    print(f"   成功股票数: {summary['successful_symbols']}")
                    print(f"   失败股票数: {summary['failed_symbols']}")
                    print(f"   总记录数: {summary['total_records']:,}")
                    print(f"   总耗时: {summary['total_time']:.3f}秒")
                    print(f"   平均吞吐量: {summary['avg_throughput']:.1f}条/秒")
                else:
                    print(f"   ❌ 强制预热失败: {result.get('error', '未知错误')}")

            elif choice == "3":
                # 查看预热状态
                print(f"\n📊 预热状态详情:")
                status = preloader.get_preload_status()

                print(f"   启用状态: {'✅ 已启用' if status['enabled'] else '❌ 未启用'}")
                print(f"   预热股票数: {status['symbols_count']}只")
                print(f"   数据类型: {', '.join(status['data_types'])}")
                print(f"   时间范围: {status['date_range'][0]} - {status['date_range'][1]}")

                if status['last_preload_stats'] and status['last_preload_stats']['preload_start_time']:
                    stats = status['last_preload_stats']
                    print(f"\n📈 上次预热统计:")
                    print(f"   总记录数: {stats['total_records']:,}")
                    print(f"   总耗时: {stats['total_time']:.3f}秒")
                    print(f"   平均吞吐量: {stats['avg_throughput']:.1f}条/秒")
                else:
                    print(f"\n📈 尚未执行过预热")

            elif choice == "4":
                # 清空预热缓存
                print(f"\n🧹 清空预热缓存...")

                if preloader.clear_preloaded_cache():
                    print(f"   ✅ 预热缓存已清空")
                else:
                    print(f"   ❌ 清空预热缓存失败")

            elif choice == "5":
                # 返回主菜单
                print(f"\n↩️  返回主菜单")
                break

            else:
                print(f"   ❌ 无效选择，请输入1-5")

        return 0

    except Exception as e:
        print(f"\n❌ 缓存预热管理异常: {e}")
        logger.error(f"缓存预热管理异常: {e}")
        return 1

def handle_ops_automation_menu():
    """处理运维自动化管理菜单选项"""
    try:
        from src.monitoring import OpsAutomationManager, IntelligentOpsEngine, OpsDashboard
        import time
        import threading

        print("\n🔧 运维自动化管理系统")
        print("=" * 50)

        # 创建运维自动化组件
        print("📊 初始化运维自动化组件...")
        ops_manager = OpsAutomationManager()
        engine = IntelligentOpsEngine()
        dashboard = OpsDashboard(ops_manager, engine, port=8002)

        print("✅ 运维自动化组件初始化完成")

        # 启动系统
        print("🚀 启动运维自动化系统...")
        ops_manager.start()
        dashboard.start()

        print("✅ 运维自动化系统已启动")
        print("🌐 Web仪表板地址: http://localhost:8002")
        print("\n📋 功能说明:")
        print("   - 实时系统监控和告警")
        print("   - 智能异常检测和预测")
        print("   - 自动化故障恢复")
        print("   - 容量规划建议")
        print("   - 性能优化洞察")

        print("\n⚠️ 系统将持续运行，按 Ctrl+C 停止")

        try:
            # 保持系统运行
            while True:
                time.sleep(10)

                # 显示简要统计
                stats = ops_manager.get_automation_stats()
                engine_stats = engine.get_engine_stats()

                print(f"\r📊 运行状态 - 告警:{stats['alerts_generated']} 任务:{stats['tasks_executed']} 异常:{engine_stats['anomalies_detected']}", end="", flush=True)

        except KeyboardInterrupt:
            print("\n\n🛑 收到停止信号，正在关闭系统...")

        finally:
            # 停止系统
            dashboard.stop()
            ops_manager.stop()
            print("✅ 运维自动化系统已停止")

        return 0

    except ImportError as e:
        logger.error(f"错误: 运维自动化模块未找到 - {e}")
        print("❌ 运维自动化模块未找到，请检查模块安装")
        return 1
    except Exception as e:
        logger.error(f"运维自动化管理异常: {e}")
        print(f"❌ 运维自动化管理异常: {e}")
        return 1

def handle_cache_preload_test():
    """处理缓存预热自动测试"""
    from src.data.storage.cache.cache_preloader import CachePreloader
    from src.utils.config.config_factory import config_factory
    import time

    print("\n" + "="*60)
    print("🔥 缓存预热自动测试")
    print("="*60)

    # 获取Tushare配置
    tushare_config = config_factory.get_tushare_config()
    api_config = tushare_config.get('api', {})
    token = api_config.get('token', '')

    if not token:
        logger.error("未找到Tushare API Token，请在config/data_source.yaml中配置")
        return 1

    try:
        print(f"📊 测试配置:")
        print(f"   预热股票: 5只热门股票")
        print(f"   数据类型: daily")
        print(f"   时间范围: 最近1个月")
        print(f"   测试模式: 强制预热")

        # 创建缓存预热器
        preloader = CachePreloader(tushare_token=token)

        # 获取预热前的缓存状态
        print(f"\n📈 预热前缓存状态:")
        if hasattr(preloader.fetcher.sync_fetcher, 'smart_cache') and preloader.fetcher.sync_fetcher.smart_cache:
            before_stats = preloader.fetcher.sync_fetcher.smart_cache.get_stats()
            print(f"   L1缓存大小: {before_stats.get('l1_cache_size', 0)}")
            print(f"   L2缓存大小: {before_stats.get('l2_cache_size', 0)}")
            print(f"   L3缓存大小: {before_stats.get('l3_cache_size', 0)}")
            print(f"   总体命中率: {before_stats.get('overall_hit_rate', 0):.1%}")

        # 执行强制预热
        print(f"\n🚀 开始强制预热...")
        start_time = time.time()

        result = preloader.preload_cache(force=True)

        end_time = time.time()
        total_time = end_time - start_time

        # 显示预热结果
        if result['status'] == 'completed':
            summary = result['summary']
            print(f"\n✅ 预热完成!")
            print(f"   总股票数: {summary['total_symbols']}")
            print(f"   成功股票数: {summary['successful_symbols']}")
            print(f"   失败股票数: {summary['failed_symbols']}")
            print(f"   总记录数: {summary['total_records']:,}")
            print(f"   总耗时: {summary['total_time']:.3f}秒")
            print(f"   平均吞吐量: {summary['avg_throughput']:.1f}条/秒")

            # 显示缓存改进情况
            if 'cache_improvement' in result:
                improvement = result['cache_improvement']
                print(f"\n📈 缓存改进:")
                print(f"   L1缓存增加: {improvement['l1_cache_size_increase']}条")
                print(f"   L2缓存增加: {improvement['l2_cache_size_increase']}条")
                print(f"   L3缓存增加: {improvement['l3_cache_size_increase']}条")
                print(f"   总写入增加: {improvement['total_sets_increase']}次")
        else:
            print(f"   ❌ 预热失败: {result.get('error', '未知错误')}")
            return 1

        # 获取预热后的缓存状态
        print(f"\n📊 预热后缓存状态:")
        if hasattr(preloader.fetcher.sync_fetcher, 'smart_cache') and preloader.fetcher.sync_fetcher.smart_cache:
            after_stats = preloader.fetcher.sync_fetcher.smart_cache.get_stats()
            print(f"   L1缓存大小: {after_stats.get('l1_cache_size', 0)}")
            print(f"   L2缓存大小: {after_stats.get('l2_cache_size', 0)}")
            print(f"   L3缓存大小: {after_stats.get('l3_cache_size', 0)}")
            print(f"   总体命中率: {after_stats.get('overall_hit_rate', 0):.1%}")
            print(f"   L1命中数: {after_stats.get('l1_hits', 0)}")
            print(f"   L2命中数: {after_stats.get('l2_hits', 0)}")
            print(f"   L3命中数: {after_stats.get('l3_hits', 0)}")
            print(f"   回写次数: {after_stats.get('write_backs', 0)}")

        # 测试预热效果
        print(f"\n🎯 测试预热效果...")
        test_symbols = ['000001.SZ', '000002.SZ', '600000.SH']

        from datetime import datetime, timedelta
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        start_str = start_date.strftime('%Y%m%d')
        end_str = end_date.strftime('%Y%m%d')

        test_start_time = time.time()
        test_data = preloader.fetcher.fetch_market_data(
            symbols=test_symbols,
            data_type='daily',
            start_date=start_str,
            end_date=end_str,
            use_cache=True,
            save=False
        )
        test_end_time = time.time()
        test_duration = test_end_time - test_start_time
        test_records = len(test_data) if not test_data.empty else 0
        test_throughput = test_records / test_duration if test_duration > 0 else 0

        print(f"   测试查询耗时: {test_duration:.3f}秒")
        print(f"   测试记录数: {test_records:,}条")
        print(f"   测试吞吐量: {test_throughput:.1f}条/秒")

        # 获取最终缓存统计
        if hasattr(preloader.fetcher.sync_fetcher, 'smart_cache') and preloader.fetcher.sync_fetcher.smart_cache:
            final_stats = preloader.fetcher.sync_fetcher.smart_cache.get_stats()
            print(f"   最终命中率: {final_stats.get('overall_hit_rate', 0):.1%}")

        print(f"\n🎉 缓存预热自动测试完成!")
        print(f"   预热功能工作正常")
        print(f"   多级缓存架构验证成功")
        print(f"   缓存预热提升了后续查询性能")

        return 0

    except Exception as e:
        print(f"\n❌ 缓存预热测试异常: {e}")
        logger.error(f"缓存预热测试异常: {e}")
        return 1

def main():
    """主函数"""
    # 设置日志记录
    setup_logging()
    logging.debug("主函数开始执行")
    
    # 处理命令行参数
    args = parse_args()
    
    # 显示版本信息和环境配置，启动即执行
    show_version()
    env_output_path = get_output_path('env_check.txt')
    check_environment(env_output_path)
    logger.info(f"环境检查结果已输出到: {env_output_path}")
    
    # 如果指定了版本参数，直接返回
    if args.version:
        return 0
    
    # 如果没有子命令，显示交互式菜单
    if not args.subcommand:
        # 注意：不要在此处标记会话已使用，让ui_utils.py中的display_menu_and_get_choice检查会话状态
        
        # 显示菜单并获取用户选择
        choice, is_timeout = display_menu_and_get_choice()
        
        # 选择后标记会话已使用 (无论是用户选择还是超时自动选择)
        global session_used
        session_used = True
        
        # 根据选择执行相应操作
        if choice == "0":
            logger.info("退出程序")
            return 0
        elif choice == "1":
            return handle_autobacktest_menu()
        elif choice == "2":
            return handle_fetch_data_menu()
        elif choice == "3":
            return handle_backtest_menu()
        elif choice == "4":
            return handle_results_menu()
        elif choice == "5":
            return handle_ops_automation_menu()
        elif choice == "6":
            return handle_cache_performance_test()
        elif choice == "7":
            return handle_cache_diagnosis()
        elif choice == "8":
            return handle_ab_performance_test()
        elif choice == "9":
            return handle_redis_environment_check()
        elif choice == "10":
            return handle_redis_cache_test()
        elif choice == "11":
            return handle_multi_level_cache_test()
        elif choice == "12":
            return handle_simple_multi_cache_test()
        elif choice == "13":
            return handle_real_world_multi_cache_test()
        elif choice == "14":
            return handle_cache_performance_monitor()
        elif choice == "15":
            return handle_cache_preload()
        elif choice == "16":
            return handle_cache_preload_test()
        else:
            logger.warning(f"无效选项: {choice}")
            return 1
    
    # 处理子命令
    command_handler = CommandFactory.get_command_handler(args.subcommand)
    
    if command_handler:
        try:
            if args.subcommand == 'env':
                return command_handler(args.output)
            else:
                return command_handler(args)
        except Exception as e:
            logger.error(f"执行命令 {args.subcommand} 时发生错误: {e}")
            logger.error(traceback.format_exc())
            return 1
    else:
        logger.error(f"未知命令: {args.subcommand}")
        return 1

if __name__ == "__main__":
    # 忽略不必要的警告
    warnings.filterwarnings("ignore", category=FutureWarning)
    # {{ AURA-X: Add - 解决urllib3 SSL兼容性警告. Approval: 寸止(ID:1737742800). }}
    warnings.filterwarnings("ignore", message="urllib3 v2 only supports OpenSSL 1.1.1+")

    # 运行主函数
    exit_code = main()
    
    # 删除会话标记文件，确保下次运行时被识别为首次运行
    # try:
    #     session_marker_file = os.path.join('output', '.session_marker')
    #     if os.path.exists(session_marker_file):
    #         FileUtils.remove_file(session_marker_file)
    #         logging.debug(f"已删除会话标记文件: {session_marker_file}")
    # except Exception as e:
    #     logging.error(f"删除会话标记文件时出错: {e}")
    
    # 退出程序
    sys.exit(exit_code) 