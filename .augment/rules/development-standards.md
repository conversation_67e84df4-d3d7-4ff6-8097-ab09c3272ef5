---
type: "manual"
---

# 量化投资平台开发标准

> 本文档包含从MCP记忆系统迁移的正式开发规范，用于团队共享和版本控制。

## 开发规范

1. **新功能开发检查机制**：开发前必须查询记忆清单（`docs/PROJECT_MEMORY_CHECKLIST.md`）确认是否已有类似功能，避免重复开发。该文档采用简洁记忆清单格式，重点记录：
   - 组件名称和核心功能描述
   - 主要API接口列表
   - 使用场景和架构关系
   - 避免详细性能数据和实现细节

   新增类必须更新对应模块的__init__.py导出列表，并添加到项目记忆清单中。遵循单一职责原则，避免功能重叠。

2. **记忆清单使用规范**：
   - **快速查阅原则**：AI助手应优先通过关键词搜索快速定位相关组件
   - **防重复开发**：重点关注组件名称、核心功能和API接口，判断是否存在可复用功能
   - **架构集成指导**：基于文档中的架构关系信息，选择合适的集成方案
   - **避免过度依赖**：文档为记忆清单而非详细技术文档，具体实现细节需查阅源码

3. **记忆清单维护标准**：
   - **内容原则**：保持简洁，突出核心功能，避免详细性能数据和实现细节
   - **更新时机**：新增组件、API变更、架构调整时及时更新
   - **格式统一**：遵循既定的组件描述格式，保持文档一致性
   - **准确性验证**：定期验证API记录的准确性，确保与实际代码一致

4. **自动化架构检查工具**：`reusable-dev-toolkit/validation_tools/development_checker.py`提供DevelopmentChecker类，自动检查代码是否遵循架构规范。检查项包括：1)数据获取器使用规范-禁止直接使用DataFetcher/AsyncDataFetcher，推荐DataFetcherManager 2)策略实现规范-必须继承StrategyInterface或BaseStrategy 3)存储操作规范-使用StorageFactory而非直接实例化 4)重复功能检查-对比已知模块清单防止重复开发。运行python reusable-dev-toolkit/validation_tools/development_checker.py进行全项目检查。

## 记忆清单最优实践

### AI助手使用指南

1. **查阅策略**：
   - 使用关键词搜索快速定位相关组件（如：缓存、数据获取、监控等）
   - 重点关注组件名称、核心功能描述和API接口列表
   - 基于架构关系信息选择合适的集成方案

2. **决策流程**：
   - 第一步：搜索是否存在相似功能的组件
   - 第二步：分析现有组件的API是否满足需求
   - 第三步：确定是扩展现有组件还是创建新组件
   - 第四步：基于架构关系设计集成方案

3. **避免误用**：
   - 不要依赖文档中的具体性能数据进行技术决策
   - 不要将记忆清单当作详细的技术文档使用
   - 具体的实现细节和参数配置需查阅源码

### 文档维护指南

1. **内容标准**：
   - **应包含**：组件名称、核心功能、主要API、使用场景、架构关系
   - **应避免**：详细性能数据、具体实现细节、测试结果、优化百分比

2. **更新原则**：
   - 新增组件时：添加简洁的功能描述和主要API
   - API变更时：及时更新接口列表，保持准确性
   - 架构调整时：更新组件间的依赖关系描述

3. **质量控制**：
   - 定期验证API记录与实际代码的一致性
   - 保持描述格式的统一性和简洁性
   - 避免信息冗余和过度详细化

## 维护说明

- 本文件由MCP记忆系统迁移而来
- 可以直接编辑修改，支持版本控制
- 修改后会自动应用到团队开发流程
- 迁移时间: 2025-07-26 22:07:14

## 相关文件

- **项目记忆清单**: `docs/PROJECT_MEMORY_CHECKLIST.md` - 简洁的组件功能和API接口记忆清单，用于防重复开发和架构一致性指导
- **MCP记忆备份**: `.augment/rules/project_memory.yaml` - 完整记忆系统导出
- **架构检查工具**: `reusable-dev-toolkit/validation_tools/development_checker.py` - 自动化架构规范检查
- **记忆同步工具**: `reusable-dev-toolkit/memory_management/memory_sync.py` - 记忆系统同步工具
