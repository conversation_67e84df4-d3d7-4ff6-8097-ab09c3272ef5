# AI启动时自动检查配置文件
# 
# 这个文件控制AI启动时记忆系统检查的行为
# 修改此文件后，下次AI启动时会自动应用新配置

# === 基本设置 ===
startup_check:
  # 是否启用AI启动时自动检查
  enabled: true
  
  # 检查模式: "quick" | "full"
  # quick: 快速检查，仅验证关键文件和状态
  # full: 完整检查，包括详细的系统状态分析
  mode: "full"
  
  # 是否静默模式（减少输出信息）
  silent: false
  
  # 检查间隔（小时）- 避免频繁检查
  # 设置为0表示每次启动都检查
  check_interval_hours: 0

# === 检查项目配置 ===
checks:
  # MCP系统连接检查
  mcp_connection: true
  
  # 用户控制文件检查
  user_control_files: true
  
  # 文件完整性检查
  file_integrity: true
  
  # 备份状态检查
  backup_status: true
  
  # 记忆内容一致性检查
  memory_consistency: false  # 较耗时，默认关闭

# === 输出设置 ===
output:
  # 显示详细状态图标
  show_icons: true
  
  # 显示建议信息
  show_recommendations: true
  
  # 显示警告信息
  show_warnings: true
  
  # 最大显示的警告数量
  max_warnings: 3
  
  # 最大显示的建议数量
  max_recommendations: 2

# === 自动修复设置 ===
auto_fix:
  # 是否启用自动修复
  enabled: false
  
  # 自动创建缺失的配置文件
  create_missing_files: false
  
  # 自动修复文件权限问题
  fix_permissions: false
  
  # 自动同步过期的备份
  sync_outdated_backups: false

# === 通知设置 ===
notifications:
  # 检查完成后是否显示总结
  show_summary: true
  
  # 发现问题时是否高亮显示
  highlight_issues: true
  
  # 是否显示性能统计
  show_performance: false

# === 高级设置 ===
advanced:
  # 检查超时时间（秒）
  timeout_seconds: 30
  
  # 并发检查任务数
  max_concurrent_checks: 3
  
  # 是否缓存检查结果
  cache_results: true
  
  # 缓存有效期（分钟）
  cache_duration_minutes: 60

# === 用户自定义设置 ===
# 用户可以在这里添加个人偏好设置
user_preferences:
  # 个人备注
  user_note: ""
  
  # 自定义检查项目
  custom_checks: []
  
  # 忽略的警告类型
  ignored_warnings: []
  
  # 优先显示的信息类型
  priority_info: ["errors", "warnings", "recommendations"]

# === 开发者设置 ===
# 仅在开发环境中使用
developer:
  # 是否启用调试模式
  debug_mode: false
  
  # 是否输出详细日志
  verbose_logging: false
  
  # 是否保存检查历史
  save_check_history: false
  
  # 检查历史保存路径
  history_file: ".augment/logs/startup_check_history.json"

# === 配置文件元信息 ===
meta:
  version: "1.0.0"
  created_by: "AURA-X 协议 (Cunzhi Edition)"
  last_modified: "2025-07-30"
  description: "AI启动时记忆系统检查配置"
  
  # 配置文件自动更新设置
  auto_update: false
  update_source: ""
  
  # 配置验证
  validate_on_load: true
  strict_mode: false
