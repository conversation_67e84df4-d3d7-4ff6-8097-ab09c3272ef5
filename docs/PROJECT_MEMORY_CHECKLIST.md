# 量化投资平台项目记忆清单

> 本文档记录了项目的核心架构模块、API接口、使用场景和开发规范，用于防止重复开发和确保架构一致性。

## 🏗️ 核心架构模块清单

### 1. 数据层 (Data Layer)

#### 数据获取组件
- **DataFetcherManager**: 数据获取器管理器，单例模式管理HybridDataFetcher实例
  - 线程安全的单例实现，消除重复初始化（解决main.py中9次重复问题）
  - 实例池管理：支持多实例池，按键名管理不同配置的实例
  - 配置热重载：支持配置变更检测和回调机制
  - 性能监控：集成统计分析和健康检查
  - 统一配置管理和全局访问接口，支持配置覆盖
  - 支持缓存策略统一管理（memory/redis/multi/disk）
  - 便捷函数：`get_data_fetcher()`, `get_manager_stats()`, `cleanup_data_fetcher_instances()`, `reload_data_fetcher_config()`
- **HybridDataFetcher**: 统一数据获取接口，支持同步/异步自动切换
  - 智能模式切换：基于数据量自动选择同步/异步模式（100只股票阈值）
  - 高性能异步处理：支持批处理和智能限流
  - 集成性能监控和统计，支持详细的获取性能分析
  - API: `fetch_market_data()`, `fetch_financial_data()`, `get_performance_stats()`
- **TushareAdapter**: Tushare数据源适配器，支持限流和重试

#### 异步数据处理组件 - 新增
- **AsyncDataFetcher**: 高性能异步数据获取器
  - 集成异步TushareAdapter和异步批处理器
  - 自适应限流机制，智能调节请求频率
  - 支持大规模并发数据获取，性能提升5-10倍
  - API: `fetch_async()`, `batch_fetch_async()`, `get_async_stats()`
- **HighPerformanceAsyncTushareAdapter**: 异步Tushare适配器
  - 高并发异步请求处理，支持智能限流
  - 异步批处理配置，优化大规模数据获取
  - API: `fetch_data_async()`, `batch_fetch_async()`, `get_rate_limit_status()`
- **AsyncBatchProcessor**: 异步批处理器
  - 支持大规模数据的异步批处理
  - 智能批次分割和并发控制
  - API: `process_batch_async()`, `get_batch_stats()`

#### 实时数据处理组件
- **RealTimeDataManager**: 实时数据管理器，统一管理实时数据处理流程
  - 数据接收缓存、转换处理、过滤验证、技术指标计算、事件通知分发
  - 企业级性能监控和统计功能，支持实时性能指标收集
  - 事件驱动架构，支持多种数据类型的事件处理器注册和管理
  - API: `process_data()`, `get_performance_stats()`, `add_event_handler()`
- **RealTimeDataStream**: 实时数据流处理器，支持WebSocket和消息队列数据流处理
  - 异步消息队列处理，支持10000条消息缓冲
  - 标准化数据格式转换，连接生命周期管理
  - API: `start_websocket_stream()`, `stop_stream()`, `get_stream_stats()`
- **RealTimeStrategyExecutor**: 实时策略执行器，支持实时策略信号生成和交易执行
  - 集成风险控制检查、订单生成管理、持仓管理、性能监控
  - 支持事件驱动的策略执行，实时信号生成和交易决策
  - API: `start()`, `stop()`, `get_performance_stats()`, `get_positions()`

#### 数据缓存组件
- **DataCache**: 高性能内存数据缓存，支持LRU和TTL双重淘汰策略
  - 线程安全的OrderedDict实现，支持自动清理和统计
  - 详细的缓存命中率统计和性能监控
  - API: `get()`, `put()`, `delete()`, `get_stats()`, `clear()`
- **SymbolDataCache**: 按资产分组的数据缓存，支持多级缓存管理
  - 基于访问频率的智能淘汰策略
  - 支持按资产类型的缓存配置和管理
  - API: `get()`, `put()`, `get_symbol_stats()`, `cleanup_expired()`
- **SmartCacheManager**: 智能缓存管理器
  - 多策略缓存算法：支持LRU、LFU、FIFO、自适应、基于时间的缓存策略
  - 智能压缩：自动数据压缩和优先级管理（4级优先级系统）
  - 增强的键生成：智能版本管理和键压缩
  - 健康监控：缓存健康评分和自动性能优化
  - 预加载机制：支持回调注册和批量预热
  - 智能缓存键生成，包含时间范围和数据版本管理
  - 基于数据类型和大小的智能TTL策略
  - 缓存命中率统计和监控，支持缓存预热功能
  - API: `get()`, `set()`, `generate_cache_key()`, `get_cache_health()`, `optimize_performance()`, `warmup_cache()`
- **CacheManager**: 多级缓存系统(内存/磁盘/Redis)

#### 数据转换组件
- **KLineGenerator**: K线生成器，支持多种时间周期的K线实时生成
- **TechnicalIndicatorCalculator**: 技术指标计算器，支持MA、EMA、RSI、MACD等常用指标

#### 数据过滤组件
- **DataFilter**: 数据过滤器基类，提供统一的数据过滤接口
- **FilterChain**: 过滤器链，支持多个过滤器的链式处理

#### 存储组件
- **StorageFactory**: 存储工厂，支持SQLite/MySQL/Redis等
- **DistributedDatabaseManager**: 分布式数据库管理器，支持多节点集群管理
  - 一致性哈希分片、读写分离、故障转移
  - 支持MySQL主从、SQLite缓存等多种节点类型
  - 批量写入、分布式查询、健康检查等核心功能
  - API: `add_node()`, `execute_write()`, `execute_distributed_query()`, `get_cluster_stats()`
- **DistributedDatabaseFactory**: 分布式数据库工厂，单例模式管理多个分布式实例
  - 配置文件加载、字典配置、生命周期管理
  - 便捷函数：`get_distributed_db_manager()`, `create_test_distributed_db_manager()`
- **DistributedDataOperations**: 分布式数据高级操作接口
  - 市场数据插入查询、跨节点聚合、数据分布优化
  - API: `insert_market_data()`, `query_market_data()`, `aggregate_data()`, `optimize_data_distribution()`

#### 分布式数据管理组件 - 新增
- **DistributedTransactionManager**: 分布式事务管理器
  - 支持跨节点ACID事务，实现两阶段提交协议
  - 事务超时管理、死锁检测、自动回滚机制
  - API: `begin_transaction()`, `commit_transaction()`, `rollback_transaction()`, `get_transaction_stats()`
- **DistributedDataAdapter**: 分布式数据适配器
  - 统一的分布式数据访问接口，支持多种存储后端
  - 集成批处理器、同步管理器、事务管理器
  - API: `read_data()`, `write_data()`, `batch_operation()`, `sync_data()`
- **ClusterMonitor**: 集群监控器
  - 实时监控集群节点状态、性能指标、健康评分
  - 支持告警系统和自动故障检测
  - API: `get_cluster_status()`, `get_node_metrics()`, `add_alert_rule()`

### 2. 策略层 (Strategy Layer)
- **StrategyInterface**: 策略基础接口
- **MovingAverageStrategy**: 移动平均线策略
- **ValueInvestmentStrategy**: 价值投资策略
- **EnhancedMultiFactorStrategy**: 多因子策略
- **StrategyOptimizer**: 策略参数优化器（兼容性包装，内部使用UnifiedStrategyOptimizer）

### 3. 统一优化模块 (Optimization Module) - 新增
- **BaseOptimizer**: 优化器基类，提供统一的优化框架
  - 支持多目标优化、参数网格搜索、贝叶斯优化
  - 内置并行处理、结果缓存、进度跟踪功能
  - 统一的优化结果格式和性能评估接口
  - API: `optimize()`, `get_best_params()`, `get_optimization_history()`, `evaluate_performance()`
- **UnifiedStrategyOptimizer**: 统一策略优化器，整合策略参数优化功能
  - 消除原有StrategyOptimizer中的重复代码
  - 支持多种策略类型的参数优化和性能评估
  - 集成回测验证、交叉验证、样本外测试
  - 支持增强移动平均、多因子、价值投资等策略优化
  - API: `optimize_strategy()`, `evaluate_strategy()`, `get_optimization_report()`, `run_comprehensive_optimization()`
- **OptimizerFactory**: 优化器工厂，支持创建不同类型的优化器
  - 统一的优化器创建接口，支持配置驱动
  - 支持策略优化器、投资组合优化器等多种类型
  - API: `create_optimizer()`, `register_optimizer()`, `get_available_optimizers()`

### 4. 回测引擎 (Backtest Engine)
- **VectorBacktestEngine**: 向量化回测引擎
- **UnifiedBacktestEngineFactory**: 统一回测引擎工厂
  - 整合注册式工厂和字符串路径工厂功能
  - 支持双重注册模式（类对象+字符串路径）
  - 市场类型自动映射，支持34个市场类型
  - 配置驱动的引擎创建和动态导入
  - 支持向量化、事件驱动、A股、期货、数字货币等多种引擎
  - API: `create_engine()`, `create_for_market()`, `register_engine_class()`, `register_engine_path()`
- **BacktestEngineFactory**: 回测引擎工厂（兼容性包装，内部使用UnifiedBacktestEngineFactory）
- **PerformanceReport**: 性能分析报告（兼容性包装，内部使用UnifiedBacktestReport）
- **MetricsCalculator**: 绩效指标计算器

### 5. 统一报告模块 (Report Module) - 新增
- **BaseReport**: 报告基类，提供统一的报告生成框架
  - 统一的指标计算、图表生成、表格创建接口
  - 支持HTML、JSON、PDF等多格式导出
  - 响应式HTML模板，统一的图表样式和中文字体配置
  - API: `calculate_metrics()`, `generate_charts()`, `create_tables()`, `export_report()`
- **UnifiedBacktestReport**: 统一回测报告，整合策略回测性能分析功能
  - 专业的回测报告生成（净值曲线、回撤分析、月度收益热力图）
  - 消除原有PerformanceReport中的重复代码
  - 支持策略vs基准对比分析、滚动指标图、交易统计表
  - API: `generate_html_report()`, `plot_equity_curve()`, `plot_drawdowns()`, `create_performance_table()`
- **UnifiedSystemReport**: 统一系统报告，整合系统性能监控报告功能
  - 支持数据获取、Web性能、系统监控等多种报告类型
  - 统一的性能指标可视化和分析（成功率饼图、响应时间分布、吞吐量时间序列）
  - API: `generate_performance_charts()`, `create_metrics_summary()`, `plot_success_rate()`
- **ReportFactory**: 报告工厂，支持创建不同类型的报告
  - 统一的报告创建接口，支持便捷函数
  - 支持回测报告、系统报告等多种报告类型
  - API: `create_report()`, `create_backtest_report()`, `create_system_report()`

### 6. 风险管理 (Risk Management)
- **VaRMonitor**: VaR风险监控
- **DrawdownMonitor**: 回撤监控
- **VolatilityMonitor**: 波动率监控
- **ExposureMonitor**: 敞口风险监控器
  - 监控投资组合的敞口风险，支持多种敞口计算方法
  - API: `calculate()`, `is_risk_exceeded()`, `get_risk_level()`
- **ConcentrationMonitor**: 集中度风险监控器
  - 监控投资组合的集中度风险，支持多种集中度计算方法
  - API: `calculate()`, `is_risk_exceeded()`, `get_risk_level()`
- **RiskControlFactory**: 风险控制工厂
  - 支持双重注册模式，创建风险控制流水线
- **ScenarioAnalyzer**: 情景分析器
  - 支持多种情景生成方法和风险指标计算

### 7. 投资组合 (Portfolio)
- **MeanVarianceOptimizer**: 均值方差优化
- **RiskParityOptimizer**: 风险平价优化
- **ConstraintManager**: 约束管理器
- **PortfolioAnalyzer**: 组合分析器

### 8. 配置管理 (Configuration Management)
- **ConfigFactory**: 统一配置工厂，支持配置加载、验证、缓存和版本管理
- **ConfigLoader**: 配置加载器（已被ConfigFactory替代）
- **ConfigValidator**: 配置验证器（集成在ConfigFactory中）

### 9. Web服务 (Web Services)
- **BacktestService**: 回测服务
- **DataService**: 数据服务
- **MonitorService**: 监控服务
- **WebSocketManager**: 实时通信管理

### 10. 监控系统 (Monitoring System)

#### 基础监控组件
- **SystemMonitor**: 系统监控器，提供CPU、内存、磁盘、网络等系统指标监控
- **SystemMetrics**: 系统指标数据模型
- **DatabaseMetrics**: 数据库指标数据模型
- **RateLimiterDashboard**: 限流器监控仪表板

#### 运维自动化组件
- **OpsAutomationManager**: 运维自动化管理器，统一管理系统监控、告警、自动化运维任务和故障自愈功能
  - 支持5种默认自动化规则：高CPU使用率处理、磁盘空间不足处理、高内存使用率处理、数据库慢查询优化、系统健康检查
  - 具备告警管理、任务队列、冷却机制、性能统计等企业级功能
  - API: `start()`, `stop()`, `get_automation_stats()`, `add_alert_handler()`, `add_automation_rule()`
- **Alert**: 告警信息数据模型，支持INFO、WARNING、ERROR、CRITICAL四个级别
- **AutomationRule**: 自动化规则数据模型，定义触发条件和执行动作
- **AutomationTask**: 自动化任务数据模型，跟踪任务执行状态和结果
- **AlertLevel**: 告警级别枚举
- **AutomationAction**: 自动化操作枚举，支持7种操作类型

#### 智能运维引擎
- **IntelligentOpsEngine**: 智能运维决策引擎，基于机器学习的智能运维决策系统
  - 异常检测：基于IsolationForest算法的智能异常检测
  - 容量预测：线性趋势分析，支持24小时容量预测
  - 性能洞察：自动分析CPU、内存、磁盘、网络、数据库性能
  - 自适应阈值：动态调整告警阈值，减少误报
  - API: `add_metrics()`, `detect_anomalies()`, `predict_capacity()`, `generate_performance_insights()`
- **AnomalyDetection**: 异常检测结果数据模型
- **CapacityPrediction**: 容量预测结果数据模型
- **PerformanceInsight**: 性能洞察数据模型

#### 运维监控仪表板
- **OpsDashboard**: 运维监控仪表板，提供Web界面的运维监控和管理功能
  - 实时系统监控、告警管理、自动化任务管理、性能分析、容量规划
  - 基于Flask的现代化Web界面，30秒自动刷新
  - RESTful API接口，支持第三方集成
  - API: `start()`, `stop()`

### 11. 分布式系统 (Distributed System)
- **DistributedDatabaseManager**: 分布式数据库管理器（已移至数据层）
- **DistributedDatabaseFactory**: 分布式数据库工厂（已移至数据层）
- **DistributedDataOperations**: 分布式数据操作接口（已移至数据层）
- **DistributedTransactionManager**: 分布式事务管理器（已移至数据层）
- **DistributedDataAdapter**: 分布式数据适配器（已移至数据层）
- **ClusterMonitor**: 集群监控器（已移至数据层）
- **DataSyncManager**: 数据同步管理

## 📋 详细API接口清单

### 数据获取接口
- `HybridDataFetcher.fetch_market_data()`: 获取市场数据
- `HybridDataFetcher.fetch_financial_data()`: 获取财务数据
- `HybridDataFetcher.from_app_config()`: 工厂方法创建实例
- `HybridDataFetcher.get_performance_stats()`: 获取性能统计
- `DataFetcherManager.get_data_fetcher()`: 获取数据获取器实例
- `DataFetcherManager.get_cached_data_fetcher()`: 获取缓存数据获取器
- `DataFetcherManager.get_pool_status()`: 获取实例池状态
- `DataFetcherManager.cleanup_invalid_instances()`: 清理无效实例
- `DataFetcherManager.force_reload_config()`: 强制重新加载配置
- `DataFetcherManager.register_config_change_callback()`: 注册配置变更回调
- `get_manager_stats()`: 获取管理器统计信息（便捷函数）
- `cleanup_data_fetcher_instances()`: 清理无效实例（便捷函数）
- `reload_data_fetcher_config()`: 重新加载配置（便捷函数）

### 异步数据处理接口
- `AsyncDataFetcher.fetch_async()`: 异步获取数据
- `AsyncDataFetcher.batch_fetch_async()`: 异步批量获取数据
- `AsyncDataFetcher.get_async_stats()`: 获取异步性能统计
- `HighPerformanceAsyncTushareAdapter.fetch_data_async()`: 异步获取Tushare数据
- `HighPerformanceAsyncTushareAdapter.get_rate_limit_status()`: 获取限流状态
- `AsyncBatchProcessor.process_batch_async()`: 异步批处理
- `AsyncBatchProcessor.get_batch_stats()`: 获取批处理统计

### 智能缓存接口
- `SmartCacheManager.get()`: 智能获取缓存数据
- `SmartCacheManager.set()`: 智能设置缓存数据
- `SmartCacheManager.generate_cache_key()`: 生成智能缓存键
- `SmartCacheManager.get_cache_health()`: 获取缓存健康状态
- `SmartCacheManager.optimize_performance()`: 自动性能优化
- `SmartCacheManager.register_preload_callback()`: 注册预加载回调函数
- `SmartCacheManager.get_performance_report()`: 获取性能报告
- `SmartCacheManager.warmup_cache()`: 缓存预热
- `DataCache.get_stats()`: 获取缓存统计
- `SymbolDataCache.get_symbol_stats()`: 获取资产缓存统计

### 实时数据处理接口
- `get_realtime_data_manager()`: 获取实时数据管理器实例
- `get_realtime_data_stream()`: 获取实时数据流处理器实例
- `RealTimeDataManager.process_data()`: 处理实时数据
- `RealTimeDataManager.get_performance_stats()`: 获取性能统计
- `RealTimeDataManager.add_event_handler()`: 添加事件处理器
- `RealTimeDataManager.add_technical_indicator()`: 添加技术指标
- `RealTimeDataStream.start_websocket_stream()`: 启动WebSocket数据流
- `RealTimeDataStream.stop_stream()`: 停止数据流
- `RealTimeDataStream.get_stream_stats()`: 获取数据流统计
- `RealTimeStrategyExecutor.start()`: 启动实时策略执行
- `RealTimeStrategyExecutor.stop()`: 停止实时策略执行
- `RealTimeStrategyExecutor.get_performance_stats()`: 获取策略执行统计
- `RealTimeStrategyExecutor.get_positions()`: 获取当前持仓
- `RealTimeStrategyExecutor.get_recent_signals()`: 获取最近信号

### 分布式数据库接口
- `DistributedDatabaseManager.add_node()`: 添加数据库节点
- `DistributedDatabaseManager.execute_write()`: 执行写入操作
- `DistributedDatabaseManager.execute_distributed_query()`: 执行分布式查询
- `DistributedDatabaseManager.get_cluster_stats()`: 获取集群统计
- `DistributedDatabaseFactory.create_manager()`: 创建分布式管理器
- `DistributedDatabaseFactory.get_or_create_manager()`: 获取或创建管理器
- `DistributedDataOperations.insert_market_data()`: 插入市场数据
- `DistributedDataOperations.query_market_data()`: 查询市场数据
- `DistributedDataOperations.aggregate_data()`: 跨节点数据聚合
- `DistributedDataOperations.optimize_data_distribution()`: 优化数据分布

### 分布式事务管理接口
- `DistributedTransactionManager.begin_transaction()`: 开始分布式事务
- `DistributedTransactionManager.commit_transaction()`: 提交事务
- `DistributedTransactionManager.rollback_transaction()`: 回滚事务
- `DistributedTransactionManager.get_transaction_stats()`: 获取事务统计
- `DistributedDataAdapter.read_data()`: 读取分布式数据
- `DistributedDataAdapter.write_data()`: 写入分布式数据
- `DistributedDataAdapter.batch_operation()`: 批量操作
- `ClusterMonitor.get_cluster_status()`: 获取集群状态
- `ClusterMonitor.get_node_metrics()`: 获取节点指标
- `ClusterMonitor.add_alert_rule()`: 添加告警规则

### 策略执行接口
- `StrategyInterface.initialize()`: 策略初始化
- `StrategyInterface.handle_data()`: 处理数据
- `StrategyInterface.generate_signals()`: 生成交易信号
- `StrategyInterface.generate_weights()`: 生成权重

### 统一优化接口
- `UnifiedStrategyOptimizer.optimize_strategy()`: 优化策略参数
- `UnifiedStrategyOptimizer.evaluate_strategy()`: 评估策略性能
- `UnifiedStrategyOptimizer.get_optimization_report()`: 获取优化报告
- `UnifiedStrategyOptimizer.run_comprehensive_optimization()`: 运行综合优化
- `BaseOptimizer.optimize()`: 执行优化
- `BaseOptimizer.get_best_params()`: 获取最佳参数
- `BaseOptimizer.get_optimization_history()`: 获取优化历史
- `OptimizerFactory.create_optimizer()`: 创建优化器实例
- `OptimizerFactory.register_optimizer()`: 注册优化器类型

### 回测引擎接口
- `VectorBacktestEngine.run_backtest()`: 执行回测
- `calculate_performance_metrics()`: 计算性能指标
- `UnifiedBacktestEngineFactory.create_engine()`: 创建回测引擎
- `UnifiedBacktestEngineFactory.create_for_market()`: 为特定市场创建引擎
- `UnifiedBacktestEngineFactory.register_engine_class()`: 注册引擎类
- `UnifiedBacktestEngineFactory.register_engine_path()`: 注册引擎路径
- `get_backtest_engine_factory()`: 获取全局工厂实例

### 统一报告接口
- `UnifiedBacktestReport.generate_html_report()`: 生成HTML回测报告
- `UnifiedBacktestReport.plot_equity_curve()`: 绘制净值曲线
- `UnifiedBacktestReport.plot_drawdowns()`: 绘制回撤分析图
- `UnifiedBacktestReport.create_performance_table()`: 创建性能指标表
- `UnifiedSystemReport.generate_performance_charts()`: 生成性能图表
- `UnifiedSystemReport.create_metrics_summary()`: 创建指标摘要
- `UnifiedSystemReport.plot_success_rate()`: 绘制成功率图表
- `ReportFactory.create_report()`: 创建报告
- `ReportFactory.create_backtest_report()`: 创建回测报告
- `ReportFactory.create_system_report()`: 创建系统报告
- `BaseReport.calculate_metrics()`: 计算指标
- `BaseReport.generate_charts()`: 生成图表
- `BaseReport.export_report()`: 导出报告

### 风险管理接口
- `VaRMonitor.calculate()`: 计算VaR
- `DrawdownMonitor.check_drawdown()`: 检查回撤

### 配置管理接口
- `ConfigFactory.load_config()`: 加载配置文件
- `ConfigFactory.get_value()`: 获取配置值
- `ConfigFactory.validate_config()`: 验证配置有效性
- `ConfigFactory.get_tushare_config()`: 获取Tushare配置
- `ConfigFactory.get_tushare_token()`: 获取Tushare API Token

### Web服务接口
- `BacktestService.submit_backtest()`: 提交回测任务
- `DataService.fetch_stock_data()`: 获取股票数据

### 存储操作接口
- `StorageFactory.create()`: 创建存储实例
- `SQLiteAdapter.save()`: 保存数据
- `MySQLAdapter.query()`: 查询数据

### 缓存管理接口
- `CacheManager.get()`: 获取缓存
- `CacheManager.set()`: 设置缓存
- `CacheManager.clear()`: 清理缓存
- `MultiLevelCache.fetch_with_fallback()`: 多级缓存获取
- `DataCache.put()`: 缓存数据
- `DataCache.get()`: 获取缓存数据
- `SymbolDataCache.put()`: 按资产缓存数据
- `SymbolDataCache.get()`: 按资产获取缓存数据

### 运维自动化接口
- `OpsAutomationManager.start()`: 启动运维自动化管理器
- `OpsAutomationManager.stop()`: 停止运维自动化管理器
- `OpsAutomationManager.get_automation_stats()`: 获取自动化统计
- `OpsAutomationManager.add_alert_handler()`: 添加告警处理器
- `OpsAutomationManager.add_automation_rule()`: 添加自动化规则
- `IntelligentOpsEngine.add_metrics()`: 添加系统指标数据
- `IntelligentOpsEngine.detect_anomalies()`: 检测异常
- `IntelligentOpsEngine.predict_capacity()`: 预测容量需求
- `IntelligentOpsEngine.generate_performance_insights()`: 生成性能洞察
- `OpsDashboard.start()`: 启动运维仪表板Web服务
- `OpsDashboard.stop()`: 停止运维仪表板Web服务

### 系统监控接口
- `SystemMonitor.start_monitoring()`: 启动系统监控
- `SystemMonitor.stop_monitoring()`: 停止系统监控
- `SystemMonitor.get_current_metrics()`: 获取当前系统指标
- `SystemMonitor.get_metrics_history()`: 获取历史指标数据

## 🎯 使用场景指南

### 1. 数据获取场景
使用`HybridDataFetcher.from_app_config()`创建实例，自动选择同步/异步模式，支持缓存和限流。

### 2. 策略开发场景
继承`StrategyInterface`，实现核心方法，使用`StrategyOptimizer`进行参数优化。

### 3. 回测分析场景
使用`VectorBacktestEngine`执行回测，`PerformanceReport`生成分析报告。

### 4. 风险监控场景
集成`VaRMonitor`和`DrawdownMonitor`，设置告警阈值。

### 5. 配置管理场景
使用`ConfigFactory.load_config()`统一加载配置，支持配置验证、缓存和版本管理。通过`get_tushare_config()`获取数据源配置。

### 6. Web服务场景
通过`BacktestService`提供API，支持异步任务和WebSocket实时推送。

### 7. 实时数据处理场景
使用`get_realtime_data_manager()`创建实时数据管理器，支持高频数据处理、实时技术指标计算、事件驱动架构。通过`RealTimeStrategyExecutor`实现实时策略执行，支持实时信号生成、风险控制、订单管理。

### 8. 实时策略执行场景
创建`RealTimeStrategyExecutor`实例，集成策略和数据管理器，启动实时策略执行。支持实时数据流处理、信号生成、风险控制检查、订单执行、持仓管理和性能监控。

### 9. 运维自动化管理场景
使用`OpsAutomationManager`创建运维自动化管理器，启动系统监控、告警和自动化任务。支持5种默认自动化规则，包括高CPU使用率处理、磁盘空间不足处理、高内存使用率处理、数据库慢查询优化、系统健康检查。通过`OpsDashboard`提供Web界面监控和管理。

### 10. 智能运维监控场景
创建`IntelligentOpsEngine`智能运维引擎，添加系统指标数据进行机器学习分析。支持异常检测(IsolationForest算法)、容量预测(线性趋势分析)、性能洞察生成。通过Web API接口集成到现有监控系统。

### 11. 分布式部署场景
使用`DistributedDatabaseFactory.get_distributed_db_manager()`创建分布式管理器，支持多节点集群、读写分离、故障转移。通过`DistributedDataOperations`进行高级数据操作，包括市场数据插入查询、跨节点聚合、数据分布优化。

### 12. 分布式数据管理场景
- **集群创建**: 使用配置文件或字典创建多节点分布式集群
- **数据插入**: 通过`insert_market_data()`按分片键自动分布数据到不同节点
- **分布式查询**: 使用`query_market_data()`跨节点查询并自动合并结果
- **数据聚合**: 通过`aggregate_data()`实现跨节点的sum/avg/count等聚合操作
- **集群监控**: 使用`get_cluster_stats()`获取节点状态、数据分布等统计信息

## 🔧 扩展点和集成指南

### 1. 数据源扩展
实现`DataSourceInterface`，注册到`DataSourceFactory`，支持新的数据提供商。

### 2. 策略扩展
继承`BaseStrategy`，注册到`StrategyFactory`，支持自定义交易逻辑。

### 3. 存储扩展
实现`StorageInterface`，注册到`StorageFactory`，支持新的数据库类型。

### 4. 缓存扩展
实现`CacheInterface`，集成到`MultiLevelCache`，支持新的缓存后端。

### 5. 实时数据处理扩展
通过`RealTimeDataManager.add_technical_indicator()`添加自定义技术指标，通过`add_event_handler()`注册自定义事件处理器，支持新的数据类型和处理逻辑。

### 6. 运维自动化扩展
通过`OpsAutomationManager.add_automation_rule()`添加自定义自动化规则，通过`add_alert_handler()`注册自定义告警处理器。实现自定义自动化操作和智能决策算法，支持新的监控指标和故障恢复策略。

### 7. 监控扩展
实现`MonitorInterface`，集成到`SystemMonitor`，支持自定义监控指标。

### 8. Web扩展
添加新的API路由到router，集成到FastAPI应用，支持新的业务功能。

### 9. 分布式扩展
通过`DistributedDatabaseManager.add_node()`添加新的数据库节点，支持MySQL、SQLite等多种数据库类型。实现自定义分片策略和负载均衡算法。

## 📏 开发规范和检查机制

### 新功能开发检查规则
1. **开发前必须查询记忆清单**确认是否已有类似功能，避免重复开发
2. **数据获取统一使用DataFetcherManager**，通过get_data_fetcher()等便捷函数获取HybridDataFetcher实例，避免直接实例化
3. **实时数据处理使用RealTimeDataManager**，通过get_realtime_data_manager()获取实例
4. **运维自动化使用OpsAutomationManager**，通过统一接口管理监控、告警和自动化任务
5. **策略开发继承StrategyInterface**或BaseStrategy
6. **存储操作通过StorageFactory**，避免直接实例化适配器
7. **缓存使用CacheManager**或MultiLevelCache
8. **新增类必须更新对应模块的__init__.py导出列表**
9. **遵循单一职责原则**，避免功能重叠

### 自动化检查工具
- **工具位置**: `reusable-dev-toolkit/validation_tools/development_checker.py`
- **运行命令**: `python3 reusable-dev-toolkit/validation_tools/development_checker.py`
- **检查项目**: 
  - 数据获取器使用规范
  - 策略实现规范
  - 存储操作规范
  - 重复功能检查

## 🔗 模块依赖关系

```
数据流: DataSources → HybridDataFetcher → Storage → Cache
实时数据流: RealTimeDataStream → RealTimeDataManager → Cache → EventHandlers
策略流: Strategy → BacktestEngine → PerformanceReport
实时策略流: RealTimeDataManager → RealTimeStrategyExecutor → OrderManager → PositionManager
运维自动化流: SystemMonitor → OpsAutomationManager → AutomationTasks → AlertHandlers
智能运维流: SystemMetrics → IntelligentOpsEngine → AnomalyDetection → PerformanceInsights
风险流: RiskMonitor → RiskControl → AlertSystem
Web流: API → Services → DataFetcher → Database
```

## 📊 架构检查结果

最近一次检查结果（376个文件）：
- 发现违规: 3个（已大幅改善）
- 错误: 0个（已全部修复）
- 警告: 3个（已大幅改善）

主要修正成果：
- **UnifiedBacktestEngineFactory**: 支持34个市场类型，完善工厂模式
- **RiskControlFactory**: 实现双重注册模式和流水线
- **ScenarioAnalyzer**: 完整实现情景分析功能
- **新增监控组件**: ExposureMonitor、ConcentrationMonitor

## 🚀 系统性能特征

### 数据获取性能
- **异步处理能力**：支持高吞吐量数据获取
- **智能限流**：财务数据精确限流控制
- **批处理优化**：智能批次分割和并发控制

### 缓存系统性能
- **多级缓存架构**：L1内存 + L2 Redis + L3磁盘缓存
- **智能压缩**：自动数据压缩，显著减少内存使用
- **自适应策略**：支持多种缓存策略动态切换

### 系统可靠性
- **实例管理**：实例池避免重复创建，提升效率
- **配置热重载**：支持配置变更的快速响应
- **自动恢复**：健康检查和自动清理机制

---

## 🔧 相关工具和配置

### 开发工具
- **架构检查工具**: `reusable-dev-toolkit/validation_tools/development_checker.py`
- **记忆同步工具**: `reusable-dev-toolkit/memory_management/memory_sync.py`
- **记忆集成验证**: `reusable-dev-toolkit/memory_management/memory_integration_validator.py`

### 配置文件
- **项目配置**: `src/utils/config/config_factory.py` - 配置工厂类
- **数据源配置**: `config/data_source.yaml` - 数据源配置文件
- **应用配置**: `config/app.yaml` - 应用配置文件

### 实时数据处理模块
- **模块位置**: `src/trading/realtime/` - 实时数据处理模块目录
- **核心组件**: `realtime_data_manager.py` - 实时数据管理器
- **数据流处理**: `realtime_data_manager.py` - 数据流处理器（集成在管理器中）
- **策略执行**: `realtime_strategy_executor.py` - 实时策略执行器
- **数据缓存**: `data_cache.py` - 高性能数据缓存
- **数据转换**: `data_transformer.py` - K线生成和技术指标计算
- **数据过滤**: `data_filter.py` - 数据过滤和验证

### 规则文件
- **开发标准**: `.augment/rules/development-standards.md` - 正式开发规范
- **用户偏好**: `.augment/rules/user-preferences.md` - 用户偏好配置
- **MCP记忆备份**: `.augment/rules/project_memory.yaml` - 完整记忆导出

---

**注意**: 本文档应与代码同步更新，新增功能时必须更新相应的记忆清单。

**最近更新**:
- 2025-07-28: 最优实践优化完成 - DataFetcherManager和SmartCacheManager优化，架构合规性大幅改善
  - DataFetcherManager：实例池管理、配置热重载、性能监控、健康检查
  - SmartCacheManager：多策略算法、智能压缩、优先级管理、健康监控
  - 架构修正：新增ExposureMonitor和ConcentrationMonitor监控组件
- 2025-07-27: 添加监控和运维自动化能力，包含OpsAutomationManager、IntelligentOpsEngine、OpsDashboard等核心组件
- 2025-07-27: 添加实时数据处理能力，包含RealTimeDataManager、RealTimeDataStream、RealTimeStrategyExecutor等核心组件
- 2025-07-26: 工具路径更新至reusable-dev-toolkit目录
