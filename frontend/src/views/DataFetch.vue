<template>
  <div class="data-fetch">
    <el-card class="page-header" shadow="never">
      <div class="header-content">
        <div class="header-left">
          <h2>数据获取</h2>
          <p>配置参数并获取股票市场数据</p>
        </div>
      </div>
    </el-card>

    <el-row :gutter="20">
      <el-col :span="16">
        <el-card class="config-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>数据获取配置</span>
            </div>
          </template>

          <el-form 
            ref="fetchFormRef"
            :model="fetchForm" 
            :rules="fetchRules"
            label-width="120px"
          >
            <el-form-item label="数据类型" prop="dataTypes">
              <el-checkbox-group v-model="fetchForm.dataTypes">
                <el-checkbox label="daily">日线数据</el-checkbox>
                <el-checkbox label="weekly">周线数据</el-checkbox>
                <el-checkbox label="monthly">月线数据</el-checkbox>
                <el-checkbox label="fundamental">基本面数据</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="时间范围" prop="dateRange">
              <el-date-picker
                v-model="fetchForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYYMMDD"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="股票代码">
              <el-input
                v-model="fetchForm.stockCodes"
                type="textarea"
                :rows="3"
                placeholder="请输入股票代码，多个代码用逗号分隔，如：000001.SZ,000002.SZ（留空则获取全市场数据）"
              />
            </el-form-item>

            <el-form-item label="数量限制" prop="limit">
              <el-input-number
                v-model="fetchForm.limit"
                :min="1"
                :max="10000"
                :step="100"
                controls-position="right"
                style="width: 200px"
              />
            </el-form-item>

            <el-form-item>
              <el-button 
                type="primary" 
                size="large"
                :loading="isRunning"
                @click="startFetch"
              >
                <el-icon><Download /></el-icon>
                {{ isRunning ? '数据获取中...' : '开始获取' }}
              </el-button>
              <el-button size="large" @click="resetForm">
                <el-icon><RefreshLeft /></el-icon>
                重置配置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card class="status-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>获取状态</span>
            </div>
          </template>

          <div v-if="currentTask" class="task-status">
            <div class="status-item">
              <span class="status-label">任务ID:</span>
              <span class="status-value">{{ currentTask.task_id }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">状态:</span>
              <el-tag :type="getStatusType(currentTask.status)">
                {{ getStatusText(currentTask.status) }}
              </el-tag>
            </div>
            <div class="status-item">
              <span class="status-label">进度:</span>
              <el-progress :percentage="currentTask.progress || 0" />
            </div>
          </div>

          <div v-else class="no-task">
            <el-empty description="暂无进行中的任务" />
          </div>
        </el-card>

        <el-card class="history-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>获取历史</span>
            </div>
          </template>

          <div class="history-list">
            <div v-for="(record, index) in fetchHistory" :key="index" class="history-item">
              <div class="history-info">
                <span class="history-type">{{ record.dataTypes.join(', ') }}</span>
                <span class="history-time">{{ record.time }}</span>
              </div>
              <el-tag size="small" :type="getStatusType(record.status)">
                {{ getStatusText(record.status) }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useStrategiesStore } from '../stores/strategies'
import { ElMessage } from 'element-plus'
// {{ AURA-X: Add - 导入统一API工具和通用函数. Approval: 寸止(ID:重构阶段3). }}
import { dataApi } from '../utils/api'
import { formatDate, formatNumber } from '../utils/common'

const strategiesStore = useStrategiesStore()

const fetchFormRef = ref()
const isRunning = ref(false)
const currentTask = ref(null)

// 数据获取表单
const fetchForm = ref({
  dataTypes: ['daily'],
  dateRange: ['20240101', '20241231'],
  stockCodes: '',
  limit: 1000
})

// 表单验证规则
const fetchRules = {
  dataTypes: [
    { required: true, message: '请选择至少一种数据类型', trigger: 'change' }
  ],
  dateRange: [
    { required: true, message: '请选择时间范围', trigger: 'change' }
  ],
  limit: [
    { required: true, message: '请输入数量限制', trigger: 'blur' }
  ]
}

// 获取历史记录
const fetchHistory = ref([])

// 获取状态标签类型
const getStatusType = (status) => {
  const typeMap = {
    'pending': 'info',
    'running': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    'pending': '等待中',
    'running': '进行中',
    'completed': '已完成',
    'failed': '失败'
  }
  return textMap[status] || status
}

// 开始数据获取
const startFetch = async () => {
  try {
    await fetchFormRef.value.validate()
    
    isRunning.value = true
    
    const fetchData = {
      stock_codes: fetchForm.value.stockCodes ? 
        fetchForm.value.stockCodes.split(',').map(code => code.trim()) : null,
      start_date: fetchForm.value.dateRange[0],
      end_date: fetchForm.value.dateRange[1],
      data_types: fetchForm.value.dataTypes,
      limit: fetchForm.value.limit
    }
    
    const result = await strategiesStore.fetchData(fetchData)
    
    ElMessage.success('数据获取任务已启动，任务ID: ' + result.task_id)
    
    // 设置当前任务
    currentTask.value = {
      task_id: result.task_id,
      status: 'running',
      progress: 0
    }
    
    // 添加到历史记录
    fetchHistory.value.unshift({
      dataTypes: fetchForm.value.dataTypes,
      time: new Date().toLocaleString(),
      status: 'running'
    })
    
    // 开始监控任务进度
    monitorTaskProgress(result.task_id)
    
  } catch (error) {
    ElMessage.error('数据获取启动失败: ' + error.message)
  } finally {
    isRunning.value = false
  }
}

// 监控任务进度
const monitorTaskProgress = (taskId) => {
  const interval = setInterval(async () => {
    try {
      // {{ AURA-X: Modify - 使用统一API工具，消除重复的fetch代码. Approval: 寸止(ID:重构阶段3). }}
      const result = await dataApi.getTaskStatus(taskId)
      if (result.success && currentTask.value) {
        const status = result.data
        currentTask.value.progress = status.progress || 0
        currentTask.value.status = status.status

        // 更新历史记录中的状态
        if (fetchHistory.value.length > 0) {
          fetchHistory.value[0].status = status.status
        }

        // 任务完成或失败时停止监控
        if (status.status === 'completed') {
          ElMessage.success('数据获取完成')
          clearInterval(interval)
        } else if (status.status === 'failed') {
          ElMessage.error('数据获取失败: ' + (status.error_message || '未知错误'))
          clearInterval(interval)
        }
      }
    } catch (error) {
      console.error('获取任务状态失败:', error)
      // 继续监控，不中断
    }
  }, 2000) // 每2秒检查一次
}

// 重置表单
const resetForm = () => {
  fetchFormRef.value.resetFields()
}

// {{ AURA-X: Modify - 使用统一API工具，消除重复的fetch代码. Approval: 寸止(ID:重构阶段3). }}
// 获取数据获取历史
const loadFetchHistory = async () => {
  try {
    const result = await dataApi.getTaskHistory()
    if (result.success) {
      fetchHistory.value = result.data || []
    }
  } catch (error) {
    console.warn('获取数据获取历史失败:', error)
    // 保持空数组，不显示错误给用户
  }
}

onMounted(() => {
  // 页面加载时获取历史记录
  loadFetchHistory()
})
</script>

<style scoped>
.data-fetch {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  border: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
}

.config-card, .status-card, .history-card {
  border: none;
  margin-bottom: 20px;
}

.card-header {
  font-weight: 500;
}

.task-status .status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.status-label {
  font-weight: 500;
  color: #606266;
}

.status-value {
  color: #303133;
  font-family: monospace;
}

.no-task {
  text-align: center;
  padding: 40px 0;
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-info {
  display: flex;
  flex-direction: column;
}

.history-type {
  font-weight: 500;
  color: #303133;
}

.history-time {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
