<template>
  <div class="backtest">
    <el-card class="page-header" shadow="never">
      <div class="header-content">
        <div class="header-left">
          <h2>策略回测</h2>
          <p>配置参数并执行量化策略回测</p>
        </div>
      </div>
    </el-card>

    <el-row :gutter="20">
      <!-- 回测配置 -->
      <el-col :span="16">
        <el-card class="config-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>回测配置</span>
            </div>
          </template>

          <el-form 
            ref="backtestFormRef"
            :model="backtestForm" 
            :rules="backtestRules"
            label-width="120px"
          >
            <el-form-item label="选择策略" prop="strategy">
              <el-select 
                v-model="backtestForm.strategy" 
                placeholder="请选择回测策略"
                style="width: 100%"
                @change="onStrategyChange"
              >
                <el-option
                  v-for="strategy in strategiesStore.strategies"
                  :key="strategy.key"
                  :label="strategy.name"
                  :value="strategy.key"
                >
                  <div class="strategy-option">
                    <span>{{ strategy.name }}</span>
                    <el-tag size="small" :type="getRiskLevelType(strategy.risk_level)">
                      {{ strategy.risk_level }}风险
                    </el-tag>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="回测时间" prop="dateRange">
              <el-date-picker
                v-model="backtestForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYYMMDD"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="初始资金" prop="initialCapital">
              <el-input-number
                v-model="backtestForm.initialCapital"
                :min="10000"
                :max="100000000"
                :step="10000"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="股票池">
              <el-input
                v-model="backtestForm.stockCodes"
                type="textarea"
                :rows="3"
                placeholder="请输入股票代码，多个代码用逗号分隔，如：000001.SZ,000002.SZ（留空则使用全市场）"
              />
            </el-form-item>

            <!-- 策略参数 -->
            <div v-if="selectedStrategyInfo" class="strategy-params">
              <h4>策略参数</h4>
              <el-form-item
                v-for="(param, key) in selectedStrategyInfo.parameters"
                :key="key"
                :label="key"
              >
                <el-input-number
                  v-if="param.type === 'int' || param.type === 'float'"
                  v-model="backtestForm.parameters[key]"
                  :min="param.range ? param.range[0] : undefined"
                  :max="param.range ? param.range[1] : undefined"
                  :step="param.type === 'int' ? 1 : 0.01"
                  :precision="param.type === 'int' ? 0 : 2"
                  controls-position="right"
                  style="width: 200px"
                />
                <el-select
                  v-else-if="param.options"
                  v-model="backtestForm.parameters[key]"
                  style="width: 200px"
                >
                  <el-option
                    v-for="option in param.options"
                    :key="option"
                    :label="option"
                    :value="option"
                  />
                </el-select>
                <el-input
                  v-else
                  v-model="backtestForm.parameters[key]"
                  style="width: 200px"
                />
              </el-form-item>
            </div>

            <el-form-item>
              <el-button 
                type="primary" 
                size="large"
                :loading="isRunning"
                @click="runBacktest"
              >
                <el-icon><VideoPlay /></el-icon>
                {{ isRunning ? '回测执行中...' : '开始回测' }}
              </el-button>
              <el-button size="large" @click="resetForm">
                <el-icon><RefreshLeft /></el-icon>
                重置配置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 策略信息 -->
      <el-col :span="8">
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>策略信息</span>
            </div>
          </template>

          <div v-if="selectedStrategyInfo" class="strategy-info">
            <div class="info-item">
              <h4>{{ selectedStrategyInfo.name }}</h4>
              <el-tag :type="getRiskLevelType(selectedStrategyInfo.risk_level)">
                {{ selectedStrategyInfo.risk_level }}风险
              </el-tag>
            </div>

            <div class="info-item">
              <p class="strategy-description">{{ selectedStrategyInfo.description }}</p>
            </div>

            <div class="info-item">
              <span class="info-label">适用市场:</span>
              <div class="market-tags">
                <el-tag 
                  v-for="market in selectedStrategyInfo.suitable_market" 
                  :key="market"
                  size="small"
                  type="info"
                  class="market-tag"
                >
                  {{ market }}
                </el-tag>
              </div>
            </div>
          </div>

          <div v-else class="no-strategy">
            <el-empty description="请先选择回测策略" />
          </div>
        </el-card>

        <!-- 回测历史 -->
        <el-card class="history-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>最近回测</span>
            </div>
          </template>

          <div class="history-list">
            <div v-for="(record, index) in backtestHistory" :key="index" class="history-item">
              <div class="history-info">
                <span class="history-strategy">{{ record.strategy }}</span>
                <span class="history-time">{{ record.time }}</span>
              </div>
              <el-button size="small" type="text" @click="viewResult(record)">
                查看结果
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStrategiesStore } from '../stores/strategies'
import { ElMessage } from 'element-plus'
// {{ AURA-X: Add - 导入统一API工具和通用函数. Approval: 寸止(ID:重构阶段3). }}
import { backtestApi } from '../utils/api'
import { formatDate, formatNumber } from '../utils/common'

const route = useRoute()
const router = useRouter()
const strategiesStore = useStrategiesStore()

const backtestFormRef = ref()
const isRunning = ref(false)

// 回测表单
const backtestForm = ref({
  strategy: '',
  dateRange: ['20240101', '20241231'],
  initialCapital: 1000000,
  stockCodes: '',
  parameters: {}
})

// 表单验证规则
const backtestRules = {
  strategy: [
    { required: true, message: '请选择回测策略', trigger: 'change' }
  ],
  dateRange: [
    { required: true, message: '请选择回测时间范围', trigger: 'change' }
  ],
  initialCapital: [
    { required: true, message: '请输入初始资金', trigger: 'blur' }
  ]
}

// 回测历史记录
const backtestHistory = ref([
  { strategy: '移动平均线策略', time: '2025-01-25 14:30', status: 'completed' },
  { strategy: '双均线策略', time: '2025-01-25 13:15', status: 'completed' },
  { strategy: '价值投资策略', time: '2025-01-25 11:45', status: 'failed' }
])

// 当前选择的策略信息
const selectedStrategyInfo = computed(() => {
  return strategiesStore.strategies.find(s => s.key === backtestForm.value.strategy)
})

// 获取风险等级标签类型
const getRiskLevelType = (riskLevel) => {
  const typeMap = {
    '低': 'success',
    '中等': 'warning', 
    '中高': 'danger',
    '高': 'danger'
  }
  return typeMap[riskLevel] || 'info'
}

// 策略变更处理
const onStrategyChange = (strategyKey) => {
  const strategy = strategiesStore.strategies.find(s => s.key === strategyKey)
  if (strategy && strategy.parameters) {
    // 初始化策略参数默认值
    const params = {}
    Object.entries(strategy.parameters).forEach(([key, param]) => {
      params[key] = param.default
    })
    backtestForm.value.parameters = params
  }
}

// 执行回测
const runBacktest = async () => {
  try {
    await backtestFormRef.value.validate()
    
    isRunning.value = true
    
    const backtestData = {
      strategy: backtestForm.value.strategy,
      start_date: backtestForm.value.dateRange[0],
      end_date: backtestForm.value.dateRange[1],
      initial_capital: backtestForm.value.initialCapital,
      stock_codes: backtestForm.value.stockCodes ? 
        backtestForm.value.stockCodes.split(',').map(code => code.trim()) : null,
      parameters: backtestForm.value.parameters
    }
    
    const result = await strategiesStore.runBacktest(backtestData)
    
    ElMessage.success('回测任务已启动，任务ID: ' + result.task_id)
    
    // 添加到历史记录
    backtestHistory.value.unshift({
      strategy: selectedStrategyInfo.value.name,
      time: new Date().toLocaleString(),
      status: 'running',
      taskId: result.task_id
    })
    
    // 跳转到结果页面
    setTimeout(() => {
      router.push('/results')
    }, 2000)
    
  } catch (error) {
    ElMessage.error('回测启动失败: ' + error.message)
  } finally {
    isRunning.value = false
  }
}

// 重置表单
const resetForm = () => {
  backtestFormRef.value.resetFields()
  backtestForm.value.parameters = {}
}

// 查看回测结果
const viewResult = (record) => {
  router.push('/results')
}

// 页面加载时的处理
onMounted(async () => {
  // 加载策略列表
  await strategiesStore.fetchStrategies()
  
  // 如果URL中有策略参数，自动选择
  if (route.query.strategy) {
    backtestForm.value.strategy = route.query.strategy
    onStrategyChange(route.query.strategy)
  }
})
</script>

<style scoped>
.backtest {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  border: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
}

.config-card, .info-card, .history-card {
  border: none;
  margin-bottom: 20px;
}

.card-header {
  font-weight: 500;
}

.strategy-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.strategy-params {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.strategy-params h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.strategy-info .info-item {
  margin-bottom: 15px;
}

.strategy-info h4 {
  margin: 0 0 8px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.strategy-description {
  color: #606266;
  line-height: 1.6;
  margin: 0;
}

.info-label {
  font-size: 12px;
  color: #909399;
  margin-right: 8px;
}

.market-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.market-tag {
  margin: 0;
}

.no-strategy {
  text-align: center;
  padding: 40px 0;
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-info {
  display: flex;
  flex-direction: column;
}

.history-strategy {
  font-weight: 500;
  color: #303133;
}

.history-time {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
