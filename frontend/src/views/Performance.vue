<template>
  <div class="performance">
    <el-card class="page-header" shadow="never">
      <div class="header-content">
        <div class="header-left">
          <h2>性能监控</h2>
          <p>实时监控系统性能指标</p>
        </div>
      </div>
    </el-card>

    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="metric-card" shadow="hover">
          <el-statistic
            title="API响应时间"
            :value="performanceData.api_response_time || 0"
            :precision="3"
            suffix="秒"
          />
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card" shadow="hover">
          <el-statistic
            title="数据获取速度"
            :value="performanceData.data_fetch_speed || 0"
            suffix="行/秒"
          />
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card" shadow="hover">
          <el-statistic
            title="缓存命中率"
            :value="performanceData.cache_hit_rate || 0"
            :precision="1"
            suffix="%"
          />
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card" shadow="hover">
          <el-statistic
            title="并发用户数"
            :value="performanceData.concurrent_users || 0"
            suffix="人"
          />
        </el-card>
      </el-col>
    </el-row>

    <el-card class="chart-card" shadow="never">
      <div class="chart-placeholder">
        <el-empty description="性能图表开发中..." />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import wsManager from '../utils/websocket'

const performanceData = ref({
  api_response_time: 0,
  data_fetch_speed: 0,
  cache_hit_rate: 0,
  concurrent_users: 0
})

// 获取性能数据
const loadPerformanceData = async () => {
  try {
    const response = await fetch('/api/system/performance')
    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        performanceData.value = result.data
      }
    }
  } catch (error) {
    console.warn('获取性能数据失败:', error)
  }
}

// WebSocket消息处理
const handlePerformanceUpdate = (data) => {
  performanceData.value = { ...performanceData.value, ...data.data }
}

onMounted(async () => {
  // 初始加载性能数据
  await loadPerformanceData()

  // 设置WebSocket监听器
  wsManager.on('performance_update', handlePerformanceUpdate)

  // 订阅性能数据更新
  setTimeout(() => {
    wsManager.subscribe('performance_update')
  }, 1000)
})

onUnmounted(() => {
  wsManager.off('performance_update', handlePerformanceUpdate)
})
</script>

<style scoped>
.performance {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  border: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
}

.metric-card {
  margin-bottom: 20px;
  text-align: center;
}

.chart-card {
  border: none;
  min-height: 400px;
}

.chart-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
