<template>
  <div class="home">
    <!-- 欢迎横幅 -->
    <el-card class="welcome-card" shadow="hover">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1>🚀 欢迎使用量化投资平台</h1>
          <p>世界级量化投资平台，集成数据获取、策略回测、性能监控于一体</p>
        </div>
        <div class="welcome-stats">
          <el-statistic title="数据记录" :value="systemStats.total_records || 0" suffix="条" />
          <el-statistic title="股票覆盖" :value="systemStats.stock_count || 0" suffix="支" />
          <el-statistic title="数据质量" :value="systemStats.data_quality || 0" suffix="%" />
        </div>
      </div>
    </el-card>

    <!-- 系统状态卡片 -->
    <el-row :gutter="20" class="status-cards">
      <el-col :span="6">
        <el-card class="status-card" shadow="hover">
          <div class="status-item">
            <el-icon class="status-icon success"><CircleCheckFilled /></el-icon>
            <div class="status-info">
              <h3>系统状态</h3>
              <p>{{ systemInfo.status || '检查中' }}</p>
              <div class="real-time-indicator" v-if="isWebSocketConnected">
                <el-icon class="pulse"><Connection /></el-icon>
                <span>实时</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="status-card" shadow="hover">
          <div class="status-item">
            <el-icon class="status-icon primary"><DataBoard /></el-icon>
            <div class="status-info">
              <h3>CPU使用率</h3>
              <p>{{ realTimeData.cpu_usage || 0 }}%</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="status-card" shadow="hover">
          <div class="status-item">
            <el-icon class="status-icon warning"><TrendCharts /></el-icon>
            <div class="status-info">
              <h3>内存使用率</h3>
              <p>{{ realTimeData.memory_usage || 0 }}%</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="status-card" shadow="hover">
          <div class="status-item">
            <el-icon class="status-icon info"><Monitor /></el-icon>
            <div class="status-info">
              <h3>活跃连接</h3>
              <p>{{ realTimeData.active_connections || 0 }} 个</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时数据面板 -->
    <el-card class="realtime-panel" shadow="hover">
      <template #header>
        <div class="panel-header">
          <span>实时数据监控</span>
          <el-tag :type="isWebSocketConnected ? 'success' : 'danger'" size="small">
            {{ isWebSocketConnected ? '已连接' : '未连接' }}
          </el-tag>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="8">
          <div class="metric-item">
            <span class="metric-label">数据获取速度</span>
            <span class="metric-value">{{ realTimeData.data_fetch_speed || 0 }} 行/秒</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="metric-item">
            <span class="metric-label">缓存命中率</span>
            <span class="metric-value">{{ ((realTimeData.cache_hit_rate || 0) * 100).toFixed(1) }}%</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="metric-item">
            <span class="metric-label">API调用次数</span>
            <span class="metric-value">{{ realTimeData.api_calls || 0 }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 功能特性 -->
    <el-row :gutter="20" class="feature-cards">
      <el-col :span="8">
        <el-card class="feature-card" shadow="hover">
          <template #header>
            <div class="feature-header">
              <el-icon class="feature-icon"><Download /></el-icon>
              <span>数据获取</span>
            </div>
          </template>
          <div class="feature-content">
            <p>高性能数据获取系统，支持多级缓存和异步处理，数据获取速度达到850行/秒</p>
            <el-button type="primary" size="small" @click="$router.push('/data-fetch')">
              立即使用
            </el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="feature-card" shadow="hover">
          <template #header>
            <div class="feature-header">
              <el-icon class="feature-icon"><VideoPlay /></el-icon>
              <span>策略回测</span>
            </div>
          </template>
          <div class="feature-content">
            <p>多种量化策略回测，包含技术分析、基本面分析、机器学习等6大策略体系</p>
            <el-button type="primary" size="small" @click="$router.push('/backtest')">
              开始回测
            </el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="feature-card" shadow="hover">
          <template #header>
            <div class="feature-header">
              <el-icon class="feature-icon"><Odometer /></el-icon>
              <span>性能监控</span>
            </div>
          </template>
          <div class="feature-content">
            <p>实时性能监控和智能告警系统，缓存命中率85%，API响应时间<50ms</p>
            <el-button type="primary" size="small" @click="$router.push('/performance')">
              查看监控
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-card class="quick-actions" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>快速操作</span>
        </div>
      </template>
      <div class="actions-grid">
        <el-button 
          v-for="action in quickActions" 
          :key="action.name"
          :type="action.type"
          size="large"
          @click="handleQuickAction(action)"
        >
          <el-icon><component :is="action.icon" /></el-icon>
          {{ action.name }}
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useSystemStore } from '../stores/system'
import { useStrategiesStore } from '../stores/strategies'
import wsManager from '../utils/websocket'
// {{ AURA-X: Add - 导入统一API工具. Approval: 寸止(ID:重构阶段3). }}
import { systemApi } from '../utils/api'

const router = useRouter()
const systemStore = useSystemStore()
const strategiesStore = useStrategiesStore()

const systemInfo = ref({})
const strategies = ref([])
const systemStats = ref({
  total_records: 0,
  stock_count: 0,
  data_quality: 0
})
const realTimeData = ref({
  cpu_usage: 0,
  memory_usage: 0,
  active_connections: 0,
  api_calls: 0,
  data_fetch_speed: 0,
  cache_hit_rate: 0
})
const isWebSocketConnected = ref(false)

const quickActions = [
  { name: '获取股票数据', type: 'primary', icon: 'Download', action: '/data-fetch' },
  { name: '查看股票列表', type: 'success', icon: 'List', action: '/stocks' },
  { name: '执行策略回测', type: 'warning', icon: 'VideoPlay', action: '/backtest' },
  { name: '查看回测结果', type: 'info', icon: 'PieChart', action: '/results' },
  { name: '性能监控', type: 'danger', icon: 'Monitor', action: '/performance' },
  { name: 'API文档', type: 'default', icon: 'Document', action: 'docs' }
]

const formatUptime = (uptime) => {
  if (!uptime) return '计算中...'
  
  const now = new Date()
  const startTime = new Date(uptime)
  const diff = now - startTime
  
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  return `${hours}小时${minutes}分钟`
}

const handleQuickAction = (action) => {
  if (action.action === 'docs') {
    window.open('/docs', '_blank')
  } else {
    router.push(action.action)
  }
}

// WebSocket消息处理
const handleSystemStatus = (data) => {
  realTimeData.value = data.data
}

// WebSocket连接状态检查
const checkWebSocketConnection = () => {
  isWebSocketConnected.value = wsManager.getConnectionState() === 'OPEN'
}

// {{ AURA-X: Modify - 使用统一API工具，消除重复的fetch代码. Approval: 寸止(ID:重构阶段3). }}
// 加载系统统计数据
const loadSystemStats = async () => {
  try {
    const result = await systemApi.getStats()
    if (result.success) {
      systemStats.value = result.data
    }
  } catch (error) {
    console.warn('获取系统统计失败:', error)
    // 使用默认值，不显示错误
  }
}

onMounted(async () => {
  try {
    await systemStore.fetchSystemInfo()
    systemInfo.value = systemStore.systemInfo

    await strategiesStore.fetchStrategies()
    strategies.value = strategiesStore.strategies

    // 加载系统统计数据
    await loadSystemStats()

    // 设置WebSocket监听器
    wsManager.on('system_status', handleSystemStatus)

    // 订阅系统状态数据
    setTimeout(() => {
      wsManager.subscribe('system_status')
      checkWebSocketConnection()
    }, 1000)

    // 定期检查连接状态
    const connectionCheckInterval = setInterval(checkWebSocketConnection, 5000)

    // 清理函数
    onUnmounted(() => {
      clearInterval(connectionCheckInterval)
      wsManager.off('system_status', handleSystemStatus)
    })

  } catch (error) {
    console.error('Failed to load home data:', error)
  }
})

onUnmounted(() => {
  wsManager.off('system_status', handleSystemStatus)
})
</script>

<style scoped>
.home {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-card {
  margin-bottom: 20px;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h1 {
  color: #303133;
  margin-bottom: 10px;
}

.welcome-text p {
  color: #606266;
  font-size: 16px;
}

.welcome-stats {
  display: flex;
  gap: 40px;
}

.status-cards {
  margin-bottom: 20px;
}

.status-card {
  height: 100px;
}

.status-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.status-icon {
  font-size: 32px;
  margin-right: 15px;
}

.status-icon.success {
  color: #67c23a;
}

.status-icon.primary {
  color: #409eff;
}

.status-icon.warning {
  color: #e6a23c;
}

.status-icon.info {
  color: #909399;
}

.status-info h3 {
  margin: 0 0 5px 0;
  color: #303133;
}

.status-info p {
  margin: 0;
  color: #606266;
}

.feature-cards {
  margin-bottom: 20px;
}

.feature-card {
  height: 200px;
}

.feature-header {
  display: flex;
  align-items: center;
}

.feature-icon {
  font-size: 20px;
  margin-right: 8px;
}

.feature-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 120px;
}

.feature-content p {
  color: #606266;
  line-height: 1.6;
}

.quick-actions {
  margin-bottom: 20px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.actions-grid .el-button {
  height: 50px;
  font-size: 14px;
}

.real-time-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #67c23a;
  margin-top: 4px;
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.realtime-panel {
  margin-bottom: 20px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #fafafa;
}

.metric-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}
</style>
