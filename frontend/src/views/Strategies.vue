<template>
  <div class="strategies">
    <el-card class="page-header" shadow="never">
      <div class="header-content">
        <div class="header-left">
          <h2>策略列表</h2>
          <p>查看和管理量化投资策略</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="refreshStrategies">
            <el-icon><Refresh /></el-icon>
            刷新策略
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 策略卡片网格 -->
    <div v-loading="strategiesStore.loading" class="strategies-grid">
      <el-card 
        v-for="strategy in strategiesStore.strategies" 
        :key="strategy.key"
        class="strategy-card"
        shadow="hover"
      >
        <template #header>
          <div class="strategy-header">
            <div class="strategy-title">
              <el-icon class="strategy-icon"><TrendCharts /></el-icon>
              <span>{{ strategy.name }}</span>
            </div>
            <el-tag 
              :type="getRiskLevelType(strategy.risk_level)" 
              size="small"
            >
              {{ strategy.risk_level }}风险
            </el-tag>
          </div>
        </template>

        <div class="strategy-content">
          <p class="strategy-description">{{ strategy.description }}</p>
          
          <div class="strategy-info">
            <div class="info-item">
              <span class="info-label">适用市场:</span>
              <div class="market-tags">
                <el-tag 
                  v-for="market in strategy.suitable_market" 
                  :key="market"
                  size="small"
                  type="info"
                  class="market-tag"
                >
                  {{ market }}
                </el-tag>
              </div>
            </div>
          </div>

          <div class="strategy-actions">
            <el-button 
              type="primary" 
              size="small"
              @click="runStrategy(strategy)"
            >
              <el-icon><VideoPlay /></el-icon>
              执行回测
            </el-button>
            <el-button 
              size="small"
              @click="viewDetails(strategy)"
            >
              <el-icon><View /></el-icon>
              查看详情
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 策略详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="selectedStrategy?.name"
      width="600px"
    >
      <div v-if="selectedStrategy" class="strategy-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="策略名称">
            {{ selectedStrategy.name }}
          </el-descriptions-item>
          <el-descriptions-item label="风险等级">
            <el-tag :type="getRiskLevelType(selectedStrategy.risk_level)">
              {{ selectedStrategy.risk_level }}风险
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="策略描述" :span="2">
            {{ selectedStrategy.description }}
          </el-descriptions-item>
          <el-descriptions-item label="适用市场" :span="2">
            <div class="market-tags">
              <el-tag 
                v-for="market in selectedStrategy.suitable_market" 
                :key="market"
                size="small"
                type="info"
                class="market-tag"
              >
                {{ market }}
              </el-tag>
            </div>
          </el-descriptions-item>
        </el-descriptions>

        <div class="strategy-parameters">
          <h4>策略参数</h4>
          <el-table :data="getParametersArray(selectedStrategy.parameters)" size="small">
            <el-table-column prop="name" label="参数名称" />
            <el-table-column prop="type" label="参数类型" />
            <el-table-column prop="default" label="默认值" />
            <el-table-column prop="range" label="取值范围" />
          </el-table>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button 
            type="primary" 
            @click="runStrategyFromDialog"
          >
            <el-icon><VideoPlay /></el-icon>
            执行回测
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStrategiesStore } from '../stores/strategies'
import { ElMessage } from 'element-plus'

const router = useRouter()
const strategiesStore = useStrategiesStore()

const detailDialogVisible = ref(false)
const selectedStrategy = ref(null)

// 获取风险等级标签类型
const getRiskLevelType = (riskLevel) => {
  const typeMap = {
    '低': 'success',
    '中等': 'warning', 
    '中高': 'danger',
    '高': 'danger'
  }
  return typeMap[riskLevel] || 'info'
}

// 刷新策略列表
const refreshStrategies = async () => {
  try {
    await strategiesStore.fetchStrategies()
    ElMessage.success('策略列表刷新成功')
  } catch (error) {
    ElMessage.error('刷新失败: ' + error.message)
  }
}

// 执行策略回测
const runStrategy = (strategy) => {
  router.push({
    path: '/backtest',
    query: { strategy: strategy.key }
  })
}

// 查看策略详情
const viewDetails = (strategy) => {
  selectedStrategy.value = strategy
  detailDialogVisible.value = true
}

// 从对话框执行回测
const runStrategyFromDialog = () => {
  detailDialogVisible.value = false
  runStrategy(selectedStrategy.value)
}

// 获取参数数组（用于表格显示）
const getParametersArray = (parameters) => {
  if (!parameters) return []
  
  return Object.entries(parameters).map(([key, value]) => ({
    name: key,
    type: value.type || 'unknown',
    default: value.default || '-',
    range: value.range ? `${value.range[0]} - ${value.range[1]}` : (value.options ? value.options.join(', ') : '-')
  }))
}

// 页面加载时获取数据
onMounted(() => {
  refreshStrategies()
})
</script>

<style scoped>
.strategies {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  border: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
}

.strategies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.strategy-card {
  height: 280px;
  transition: transform 0.2s;
}

.strategy-card:hover {
  transform: translateY(-2px);
}

.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.strategy-title {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.strategy-icon {
  font-size: 18px;
  margin-right: 8px;
  color: #409eff;
}

.strategy-content {
  display: flex;
  flex-direction: column;
  height: 180px;
}

.strategy-description {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 15px;
  flex: 1;
}

.strategy-info {
  margin-bottom: 15px;
}

.info-item {
  margin-bottom: 8px;
}

.info-label {
  font-size: 12px;
  color: #909399;
  margin-right: 8px;
}

.market-tags {
  display: inline-flex;
  gap: 4px;
  flex-wrap: wrap;
}

.market-tag {
  margin: 0;
}

.strategy-actions {
  display: flex;
  gap: 10px;
  margin-top: auto;
}

.strategy-details {
  padding: 10px 0;
}

.strategy-parameters {
  margin-top: 20px;
}

.strategy-parameters h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-descriptions__body) {
  background-color: #fafafa;
}
</style>
