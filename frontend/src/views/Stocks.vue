<template>
  <div class="stocks">
    <el-card class="page-header" shadow="never">
      <div class="header-content">
        <div class="header-left">
          <h2>股票列表</h2>
          <p>查看和管理股票基础信息</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="refreshStocks">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 股票列表表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="table-header">
          <span>股票列表 (共 {{ stocksStore.total }} 只)</span>
          <div class="table-actions">
            <el-button size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="stocksStore.loading"
        :data="filteredStocks"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="ts_code" label="股票代码" width="120" sortable>
          <template #default="{ row }">
            <el-tag type="primary" size="small">{{ row.ts_code }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="symbol" label="代码" width="100" />
        
        <el-table-column prop="name" label="股票名称" width="150" sortable>
          <template #default="{ row }">
            <span class="stock-name">{{ row.name }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="area" label="所属地域" width="120" />
        
        <el-table-column prop="industry" label="所属行业" width="120" sortable>
          <template #default="{ row }">
            <el-tag size="small" type="info">{{ row.industry }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="market" label="市场类型" width="100">
          <template #default="{ row }">
            <el-tag 
              size="small" 
              :type="getMarketType(row.market)"
            >
              {{ row.market }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="list_date" label="上市日期" width="120" sortable />
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="viewDetails(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button size="small" type="success" @click="addToWatchlist(row)">
              <el-icon><Star /></el-icon>
              关注
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredStocks.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStocksStore } from '../stores/stocks'
import { ElMessage, ElMessageBox } from 'element-plus'

const stocksStore = useStocksStore()

// 筛选表单
const filterForm = ref({
  code: '',
  name: '',
  industry: '',
  market: ''
})

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 20
})

// 计算过滤后的股票列表
const filteredStocks = computed(() => {
  let filtered = stocksStore.stocks

  if (filterForm.value.code) {
    filtered = filtered.filter(stock => 
      stock.ts_code.toLowerCase().includes(filterForm.value.code.toLowerCase()) ||
      stock.symbol.toLowerCase().includes(filterForm.value.code.toLowerCase())
    )
  }

  if (filterForm.value.name) {
    filtered = filtered.filter(stock => 
      stock.name.includes(filterForm.value.name)
    )
  }

  if (filterForm.value.industry) {
    filtered = filtered.filter(stock => 
      stock.industry === filterForm.value.industry
    )
  }

  if (filterForm.value.market) {
    filtered = filtered.filter(stock => 
      stock.market === filterForm.value.market
    )
  }

  return filtered
})

// 获取市场类型标签颜色
const getMarketType = (market) => {
  const typeMap = {
    '主板': 'primary',
    '创业板': 'success',
    '科创板': 'warning'
  }
  return typeMap[market] || 'info'
}

// 刷新股票数据
const refreshStocks = async () => {
  try {
    await stocksStore.fetchStocks({ limit: 100 })
    ElMessage.success('股票数据刷新成功')
  } catch (error) {
    ElMessage.error('刷新失败: ' + error.message)
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.value.currentPage = 1
  ElMessage.info(`找到 ${filteredStocks.value.length} 只股票`)
}

// 重置筛选
const handleReset = () => {
  filterForm.value = {
    code: '',
    name: '',
    industry: '',
    market: ''
  }
  pagination.value.currentPage = 1
}

// 排序处理
const handleSortChange = ({ column, prop, order }) => {
  console.log('排序变化:', { column, prop, order })
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
}

const handleCurrentChange = (page) => {
  pagination.value.currentPage = page
}

// 查看详情
const viewDetails = (stock) => {
  ElMessageBox.alert(
    `股票代码: ${stock.ts_code}\n股票名称: ${stock.name}\n所属行业: ${stock.industry}\n所属地域: ${stock.area}\n市场类型: ${stock.market}`,
    '股票详情',
    {
      confirmButtonText: '确定'
    }
  )
}

// 添加到关注列表
const addToWatchlist = (stock) => {
  ElMessage.success(`已将 ${stock.name}(${stock.ts_code}) 添加到关注列表`)
}

// 导出数据
const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

// 页面加载时获取数据
onMounted(() => {
  refreshStocks()
})
</script>

<style scoped>
.stocks {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  border: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
}

.filter-card {
  margin-bottom: 20px;
  border: none;
}

.table-card {
  border: none;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stock-name {
  font-weight: 500;
  color: #303133;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background-color: #fafafa;
}
</style>
