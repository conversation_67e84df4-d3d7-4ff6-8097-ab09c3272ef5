<template>
  <div class="realtime">
    <el-card class="page-header" shadow="never">
      <div class="header-content">
        <div class="header-left">
          <h2>实时数据</h2>
          <p>实时股价和系统监控数据</p>
        </div>
        <div class="header-right">
          <el-tag :type="connectionStatus.type" size="large">
            <el-icon><Connection /></el-icon>
            {{ connectionStatus.text }}
          </el-tag>
        </div>
      </div>
    </el-card>

    <el-row :gutter="20">
      <!-- 实时股价 -->
      <el-col :span="16">
        <el-card class="stock-prices-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>实时股价</span>
              <el-button size="small" @click="toggleStockPriceSubscription">
                {{ isStockPriceSubscribed ? '取消订阅' : '订阅股价' }}
              </el-button>
            </div>
          </template>

          <div v-if="stockPrices.length > 0" class="stock-list">
            <div 
              v-for="stock in stockPrices" 
              :key="stock.ts_code"
              class="stock-item"
              :class="{ 'price-up': stock.change > 0, 'price-down': stock.change < 0 }"
            >
              <div class="stock-info">
                <div class="stock-name">{{ stock.name }}</div>
                <div class="stock-code">{{ stock.ts_code }}</div>
              </div>
              <div class="stock-price">
                <div class="current-price">¥{{ stock.price }}</div>
                <div class="price-change">
                  <span class="change-amount">{{ stock.change > 0 ? '+' : '' }}{{ stock.change }}</span>
                  <span class="change-percent">({{ stock.change_pct > 0 ? '+' : '' }}{{ stock.change_pct }}%)</span>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="no-data">
            <el-empty description="暂无实时股价数据">
              <el-button type="primary" @click="subscribeStockPrices">
                开始订阅
              </el-button>
            </el-empty>
          </div>
        </el-card>
      </el-col>

      <!-- 系统监控 -->
      <el-col :span="8">
        <el-card class="system-monitor-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>系统监控</span>
              <el-button size="small" @click="toggleSystemMonitoring">
                {{ isSystemMonitored ? '停止监控' : '开始监控' }}
              </el-button>
            </div>
          </template>

          <div class="monitor-metrics">
            <div class="metric-item">
              <div class="metric-label">CPU使用率</div>
              <el-progress 
                :percentage="systemMetrics.cpu_usage" 
                :color="getProgressColor(systemMetrics.cpu_usage)"
              />
              <div class="metric-value">{{ systemMetrics.cpu_usage }}%</div>
            </div>

            <div class="metric-item">
              <div class="metric-label">内存使用率</div>
              <el-progress 
                :percentage="systemMetrics.memory_usage" 
                :color="getProgressColor(systemMetrics.memory_usage)"
              />
              <div class="metric-value">{{ systemMetrics.memory_usage }}%</div>
            </div>

            <div class="metric-item">
              <div class="metric-label">数据获取速度</div>
              <div class="metric-value-large">{{ systemMetrics.data_fetch_speed }} 行/秒</div>
            </div>

            <div class="metric-item">
              <div class="metric-label">缓存命中率</div>
              <div class="metric-value-large">{{ (systemMetrics.cache_hit_rate * 100).toFixed(1) }}%</div>
            </div>

            <div class="metric-item">
              <div class="metric-label">活跃连接数</div>
              <div class="metric-value-large">{{ systemMetrics.active_connections }}</div>
            </div>
          </div>
        </el-card>

        <!-- 连接日志 -->
        <el-card class="connection-log-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>连接日志</span>
              <el-button size="small" @click="clearLogs">清空</el-button>
            </div>
          </template>

          <div class="log-container">
            <div 
              v-for="(log, index) in connectionLogs" 
              :key="index"
              class="log-item"
              :class="log.type"
            >
              <span class="log-time">{{ formatTime(log.timestamp) }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import wsManager from '../utils/websocket'

// 响应式数据
const stockPrices = ref([])
const systemMetrics = ref({
  cpu_usage: 0,
  memory_usage: 0,
  data_fetch_speed: 0,
  cache_hit_rate: 0,
  active_connections: 0
})
const connectionLogs = ref([])
const isStockPriceSubscribed = ref(false)
const isSystemMonitored = ref(false)

// 连接状态
const connectionStatus = computed(() => {
  const state = wsManager.getConnectionState()
  switch (state) {
    case 'OPEN':
      return { type: 'success', text: '已连接' }
    case 'CONNECTING':
      return { type: 'warning', text: '连接中' }
    case 'CLOSED':
      return { type: 'danger', text: '已断开' }
    default:
      return { type: 'info', text: '未知状态' }
  }
})

// WebSocket消息处理
const handleStockPrices = (data) => {
  stockPrices.value = data.data
  addLog('info', `收到股价数据: ${data.data.length}只股票`)
}

const handleSystemStatus = (data) => {
  systemMetrics.value = data.data
  addLog('info', '系统状态已更新')
}

// 添加日志
const addLog = (type, message) => {
  connectionLogs.value.unshift({
    type,
    message,
    timestamp: new Date()
  })
  
  // 限制日志数量
  if (connectionLogs.value.length > 50) {
    connectionLogs.value = connectionLogs.value.slice(0, 50)
  }
}

// 订阅股价数据
const subscribeStockPrices = () => {
  if (wsManager.subscribe('stock_prices')) {
    isStockPriceSubscribed.value = true
    addLog('success', '已订阅股价数据')
  } else {
    addLog('error', '订阅股价数据失败')
  }
}

// 切换股价订阅
const toggleStockPriceSubscription = () => {
  if (isStockPriceSubscribed.value) {
    isStockPriceSubscribed.value = false
    stockPrices.value = []
    addLog('warning', '已取消股价数据订阅')
  } else {
    subscribeStockPrices()
  }
}

// 开始系统监控
const startSystemMonitoring = () => {
  if (wsManager.subscribe('system_status')) {
    isSystemMonitored.value = true
    addLog('success', '已开始系统监控')
  } else {
    addLog('error', '开始系统监控失败')
  }
}

// 切换系统监控
const toggleSystemMonitoring = () => {
  if (isSystemMonitored.value) {
    isSystemMonitored.value = false
    addLog('warning', '已停止系统监控')
  } else {
    startSystemMonitoring()
  }
}

// 清空日志
const clearLogs = () => {
  connectionLogs.value = []
}

// 格式化时间
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

onMounted(() => {
  // 设置WebSocket监听器
  wsManager.on('stock_prices', handleStockPrices)
  wsManager.on('system_status', handleSystemStatus)
  
  // 添加连接日志
  addLog('info', 'WebSocket监听器已设置')
  
  // 自动开始系统监控
  setTimeout(() => {
    startSystemMonitoring()
  }, 1000)
})

onUnmounted(() => {
  // 清理监听器
  wsManager.off('stock_prices', handleStockPrices)
  wsManager.off('system_status', handleSystemStatus)
})
</script>

<style scoped>
.realtime {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  border: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
}

.stock-prices-card, .system-monitor-card, .connection-log-card {
  border: none;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.stock-list {
  max-height: 400px;
  overflow-y: auto;
}

.stock-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  transition: all 0.3s;
}

.stock-item:hover {
  background-color: #f5f7fa;
}

.stock-item.price-up {
  border-left: 3px solid #67c23a;
}

.stock-item.price-down {
  border-left: 3px solid #f56c6c;
}

.stock-info {
  display: flex;
  flex-direction: column;
}

.stock-name {
  font-weight: 500;
  color: #303133;
}

.stock-code {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.stock-price {
  text-align: right;
}

.current-price {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.price-change {
  font-size: 12px;
  margin-top: 2px;
}

.price-up .price-change {
  color: #67c23a;
}

.price-down .price-change {
  color: #f56c6c;
}

.monitor-metrics {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.metric-value {
  font-size: 12px;
  color: #909399;
  text-align: right;
}

.metric-value-large {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  text-align: center;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  gap: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
}

.log-time {
  color: #909399;
  min-width: 80px;
}

.log-message {
  color: #606266;
}

.log-item.success .log-message {
  color: #67c23a;
}

.log-item.error .log-message {
  color: #f56c6c;
}

.log-item.warning .log-message {
  color: #e6a23c;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
