<template>
  <div class="results">
    <el-card class="page-header" shadow="never">
      <div class="header-content">
        <div class="header-left">
          <h2>回测结果</h2>
          <p>查看和分析策略回测结果</p>
        </div>
      </div>
    </el-card>

    <el-card class="results-card" shadow="never" v-if="results.length === 0">
      <div class="results-placeholder">
        <el-empty description="暂无回测结果">
          <el-button type="primary" @click="$router.push('/backtest')">
            开始回测
          </el-button>
        </el-empty>
      </div>
    </el-card>

    <!-- 回测结果列表 -->
    <div v-else>
      <el-card v-for="result in results" :key="result.task_id" class="result-item" shadow="never">
        <template #header>
          <div class="result-header">
            <div class="result-title">
              <h3>{{ result.strategy_name }}</h3>
              <el-tag :type="getResultTagType(result.metrics.total_return)">
                {{ (result.metrics.total_return * 100).toFixed(2) }}%
              </el-tag>
            </div>
            <div class="result-meta">
              <span>{{ result.start_date }} - {{ result.end_date }}</span>
              <span>初始资金: ¥{{ formatNumber(result.initial_capital) }}</span>
            </div>
          </div>
        </template>

        <div class="result-content">
          <div class="metrics-grid">
            <div class="metric-item">
              <div class="metric-label">总收益率</div>
              <div class="metric-value" :class="getValueClass(result.metrics.total_return)">
                {{ (result.metrics.total_return * 100).toFixed(2) }}%
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-label">年化收益率</div>
              <div class="metric-value" :class="getValueClass(result.metrics.annualized_return)">
                {{ (result.metrics.annualized_return * 100).toFixed(2) }}%
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-label">夏普比率</div>
              <div class="metric-value" :class="getValueClass(result.metrics.sharpe_ratio)">
                {{ result.metrics.sharpe_ratio.toFixed(2) }}
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-label">最大回撤</div>
              <div class="metric-value negative">
                {{ (result.metrics.max_drawdown * 100).toFixed(2) }}%
              </div>
            </div>
          </div>

          <div class="result-actions">
            <el-button size="small" @click="viewDetails(result)">查看详情</el-button>
            <el-button size="small" type="primary" @click="downloadReport(result)">下载报告</el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const results = ref([])
const loading = ref(false)

// 获取回测结果列表
const loadResults = async () => {
  loading.value = true
  try {
    const response = await fetch('/api/backtest/results')
    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        results.value = result.data || []
      }
    }
  } catch (error) {
    console.error('获取回测结果失败:', error)
    ElMessage.error('获取回测结果失败')
  } finally {
    loading.value = false
  }
}

// 格式化数字
const formatNumber = (num) => {
  return new Intl.NumberFormat('zh-CN').format(num)
}

// 获取结果标签类型
const getResultTagType = (value) => {
  if (value > 0.1) return 'success'
  if (value > 0) return 'warning'
  return 'danger'
}

// 获取数值样式类
const getValueClass = (value) => {
  if (value > 0) return 'positive'
  if (value < 0) return 'negative'
  return ''
}

// 查看详情
const viewDetails = (result) => {
  ElMessageBox.alert(
    `策略: ${result.strategy_name}\n` +
    `时间范围: ${result.start_date} - ${result.end_date}\n` +
    `总收益率: ${(result.metrics.total_return * 100).toFixed(2)}%\n` +
    `夏普比率: ${result.metrics.sharpe_ratio.toFixed(2)}\n` +
    `最大回撤: ${(result.metrics.max_drawdown * 100).toFixed(2)}%`,
    '回测结果详情',
    {
      confirmButtonText: '确定'
    }
  )
}

// 下载报告
const downloadReport = (result) => {
  ElMessage.info('报告下载功能开发中...')
}

onMounted(() => {
  loadResults()
})
</script>

<style scoped>
.results {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  border: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
}

.results-card {
  border: none;
  min-height: 400px;
}

.results-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

:deep(.el-card__body) {
  padding: 20px;
}

.result-item {
  margin-bottom: 20px;
  border: none;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.result-title h3 {
  margin: 0;
  color: #303133;
}

.result-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
  color: #909399;
  font-size: 14px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.metric-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.metric-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.metric-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.metric-value.positive {
  color: #67c23a;
}

.metric-value.negative {
  color: #f56c6c;
}

.result-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
