<template>
  <div class="logs">
    <el-card class="page-header" shadow="never">
      <div class="header-content">
        <div class="header-left">
          <h2>系统日志</h2>
          <p>查看系统运行日志和错误信息</p>
        </div>
      </div>
    </el-card>

    <el-card class="logs-card" shadow="never">
      <div class="logs-placeholder">
        <el-empty description="系统日志功能开发中..." />
      </div>
    </el-card>
  </div>
</template>

<script setup>
// 系统日志页面 - 简化版本
</script>

<style scoped>
.logs {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  border: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
}

.logs-card {
  border: none;
  min-height: 400px;
}

.logs-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
