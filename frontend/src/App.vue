<template>
  <div id="app">
    <el-container class="layout-container">
      <!-- 顶部导航栏 -->
      <el-header class="header">
        <div class="header-content">
          <div class="logo">
            <el-icon class="logo-icon"><TrendCharts /></el-icon>
            <span class="logo-text">量化投资平台</span>
          </div>
          
          <div class="header-actions">
            <el-badge :value="systemInfo.status === '运行中' ? 0 : 1" class="status-badge">
              <el-button 
                :type="systemInfo.status === '运行中' ? 'success' : 'danger'" 
                size="small" 
                round
                @click="checkSystemStatus"
              >
                <el-icon><Connection /></el-icon>
                {{ systemInfo.status || '检查中' }}
              </el-button>
            </el-badge>
            
            <el-dropdown @command="handleCommand">
              <el-button type="primary" size="small">
                <el-icon><User /></el-icon>
                系统菜单
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="docs">
                    <el-icon><Document /></el-icon>
                    API文档
                  </el-dropdown-item>
                  <el-dropdown-item command="about">
                    <el-icon><InfoFilled /></el-icon>
                    关于系统
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>

      <el-container>
        <!-- 侧边导航 -->
        <el-aside width="250px" class="sidebar">
          <el-menu
            :default-active="$route.path"
            class="sidebar-menu"
            router
            unique-opened
          >
            <el-menu-item index="/">
              <el-icon><HomeFilled /></el-icon>
              <span>首页概览</span>
            </el-menu-item>
            
            <el-sub-menu index="data">
              <template #title>
                <el-icon><DataBoard /></el-icon>
                <span>数据管理</span>
              </template>
              <el-menu-item index="/stocks">
                <el-icon><List /></el-icon>
                <span>股票列表</span>
              </el-menu-item>
              <el-menu-item index="/data-fetch">
                <el-icon><Download /></el-icon>
                <span>数据获取</span>
              </el-menu-item>
            </el-sub-menu>
            
            <el-sub-menu index="strategy">
              <template #title>
                <el-icon><TrendCharts /></el-icon>
                <span>策略回测</span>
              </template>
              <el-menu-item index="/strategies">
                <el-icon><Collection /></el-icon>
                <span>策略列表</span>
              </el-menu-item>
              <el-menu-item index="/backtest">
                <el-icon><VideoPlay /></el-icon>
                <span>执行回测</span>
              </el-menu-item>
              <el-menu-item index="/results">
                <el-icon><PieChart /></el-icon>
                <span>回测结果</span>
              </el-menu-item>
            </el-sub-menu>
            
            <el-sub-menu index="monitor">
              <template #title>
                <el-icon><Monitor /></el-icon>
                <span>系统监控</span>
              </template>
              <el-menu-item index="/performance">
                <el-icon><Odometer /></el-icon>
                <span>性能监控</span>
              </el-menu-item>
              <el-menu-item index="/realtime">
                <el-icon><Connection /></el-icon>
                <span>实时数据</span>
              </el-menu-item>
              <el-menu-item index="/logs">
                <el-icon><Document /></el-icon>
                <span>系统日志</span>
              </el-menu-item>
            </el-sub-menu>
          </el-menu>
        </el-aside>

        <!-- 主内容区域 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useSystemStore } from './stores/system'

const systemStore = useSystemStore()
const systemInfo = ref({
  status: '检查中'
})

const checkSystemStatus = async () => {
  try {
    await systemStore.fetchSystemInfo()
    systemInfo.value = systemStore.systemInfo
  } catch (error) {
    systemInfo.value.status = '连接失败'
  }
}

const handleCommand = (command) => {
  switch (command) {
    case 'docs':
      window.open('/docs', '_blank')
      break
    case 'about':
      ElMessageBox.alert(
        `量化投资平台 v1.0.0\n构建日期: 2025-01-25\n作者: 泽强Felix`,
        '关于系统',
        {
          confirmButtonText: '确定'
        }
      )
      break
  }
}

onMounted(() => {
  checkSystemStatus()
  // 每30秒检查一次系统状态
  setInterval(checkSystemStatus, 30000)
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
}

.logo-icon {
  font-size: 24px;
  margin-right: 10px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.status-badge {
  margin-right: 10px;
}

.sidebar {
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
}

.sidebar-menu {
  border: none;
  background: transparent;
}

.main-content {
  background: #ffffff;
  padding: 20px;
  overflow-y: auto;
}

:deep(.el-menu-item.is-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

:deep(.el-menu-item.is-active .el-icon) {
  color: white;
}
</style>
