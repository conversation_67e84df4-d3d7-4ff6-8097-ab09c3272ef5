import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加token等认证信息
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    let message = '请求失败'
    
    if (error.response) {
      // 服务器返回错误状态码
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          message = data.detail || '请求参数错误'
          break
        case 401:
          message = '未授权访问'
          break
        case 403:
          message = '禁止访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = data.detail || `请求失败 (${status})`
      }
    } else if (error.request) {
      // 网络错误
      message = '网络连接失败，请检查网络设置'
    } else {
      // 其他错误
      message = error.message || '未知错误'
    }
    
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// {{ AURA-X: Add - 扩展API工具，添加统一的API调用方法，消除重复代码. Approval: 寸止(ID:重构阶段3). }}

// 系统相关API
const systemApi = {
  /**
   * 获取系统统计信息
   */
  async getStats() {
    try {
      const response = await api.get('/system/stats')
      return response.data
    } catch (error) {
      console.warn('获取系统统计失败:', error)
      return { success: false, data: {} }
    }
  },

  /**
   * 获取系统性能数据
   */
  async getPerformance() {
    try {
      const response = await api.get('/system/performance')
      return response.data
    } catch (error) {
      console.warn('获取性能数据失败:', error)
      return { success: false, data: {} }
    }
  },

  /**
   * 获取系统状态
   */
  async getStatus() {
    try {
      const response = await api.get('/system/status')
      return response.data
    } catch (error) {
      console.warn('获取系统状态失败:', error)
      return { success: false, data: {} }
    }
  }
}

// 数据获取相关API
const dataApi = {
  /**
   * 启动数据获取任务
   * @param {Object} fetchData - 数据获取参数
   */
  async fetchData(fetchData) {
    const response = await api.post('/data/fetch', fetchData)
    return response.data
  },

  /**
   * 获取任务状态
   * @param {string} taskId - 任务ID
   */
  async getTaskStatus(taskId) {
    const response = await api.get(`/data/task/${taskId}`)
    return response.data
  },

  /**
   * 取消任务
   * @param {string} taskId - 任务ID
   */
  async cancelTask(taskId) {
    const response = await api.delete(`/data/task/${taskId}`)
    return response.data
  },

  /**
   * 获取任务历史
   */
  async getTaskHistory() {
    try {
      const response = await api.get('/data/fetch/history')
      return response.data
    } catch (error) {
      console.warn('获取任务历史失败:', error)
      return { success: false, data: [] }
    }
  }
}

// 回测相关API
const backtestApi = {
  /**
   * 启动回测任务
   * @param {Object} backtestData - 回测参数
   */
  async runBacktest(backtestData) {
    const response = await api.post('/backtest/run', backtestData)
    return response.data
  },

  /**
   * 获取回测结果
   * @param {string} taskId - 任务ID
   */
  async getBacktestResult(taskId) {
    const response = await api.get(`/backtest/result/${taskId}`)
    return response.data
  },

  /**
   * 获取回测历史
   */
  async getBacktestHistory() {
    const response = await api.get('/backtest/history')
    return response.data
  }
}

// 策略相关API
const strategyApi = {
  /**
   * 获取可用策略列表
   */
  async getStrategies() {
    const response = await api.get('/strategies')
    return response.data
  },

  /**
   * 获取策略详情
   * @param {string} strategyId - 策略ID
   */
  async getStrategy(strategyId) {
    const response = await api.get(`/strategies/${strategyId}`)
    return response.data
  }
}

// {{ AURA-X: Add - 统一导出所有API方法，优化导入方式. Approval: 寸止(ID:重构阶段3). }}
// 统一导出
export {
  systemApi,
  dataApi,
  backtestApi,
  strategyApi
}

export default api
