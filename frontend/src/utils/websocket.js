import { ElMessage } from 'element-plus'

class WebSocketManager {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000
    this.listeners = new Map()
    this.isConnecting = false
    this.isManualClose = false
  }

  connect(url = 'ws://127.0.0.1:8001/ws') {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return Promise.resolve()
    }

    this.isConnecting = true
    this.isManualClose = false

    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(url)

        this.ws.onopen = () => {
          console.log('WebSocket连接已建立')
          this.isConnecting = false
          this.reconnectAttempts = 0
          ElMessage.success('实时数据连接已建立')
          resolve()
        }

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            this.handleMessage(data)
          } catch (error) {
            console.error('解析WebSocket消息失败:', error)
          }
        }

        this.ws.onclose = (event) => {
          console.log('WebSocket连接已关闭', event.code, event.reason)
          this.isConnecting = false
          
          if (!this.isManualClose && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnect()
          }
        }

        this.ws.onerror = (error) => {
          console.error('WebSocket连接错误:', error)
          this.isConnecting = false
          reject(error)
        }

      } catch (error) {
        this.isConnecting = false
        reject(error)
      }
    })
  }

  reconnect() {
    if (this.isManualClose) return

    this.reconnectAttempts++
    console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
    
    setTimeout(() => {
      this.connect().catch(() => {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          ElMessage.error('WebSocket连接失败，请刷新页面重试')
        }
      })
    }, this.reconnectInterval)
  }

  handleMessage(data) {
    const { type } = data
    
    // 触发对应类型的监听器
    if (this.listeners.has(type)) {
      this.listeners.get(type).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('WebSocket消息处理错误:', error)
        }
      })
    }

    // 处理特殊消息类型
    switch (type) {
      case 'welcome':
        console.log('收到欢迎消息:', data.message)
        break
      case 'error':
        ElMessage.error(data.message)
        break
      case 'subscription_confirmed':
        console.log('订阅确认:', data.message)
        break
    }
  }

  send(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
      return true
    } else {
      console.warn('WebSocket未连接，无法发送消息')
      return false
    }
  }

  subscribe(dataType) {
    return this.send({
      type: 'subscribe',
      data_type: dataType
    })
  }

  ping() {
    return this.send({
      type: 'ping'
    })
  }

  on(type, callback) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, [])
    }
    this.listeners.get(type).push(callback)
  }

  off(type, callback) {
    if (this.listeners.has(type)) {
      const callbacks = this.listeners.get(type)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  close() {
    this.isManualClose = true
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  getConnectionState() {
    if (!this.ws) return 'CLOSED'
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'CONNECTING'
      case WebSocket.OPEN:
        return 'OPEN'
      case WebSocket.CLOSING:
        return 'CLOSING'
      case WebSocket.CLOSED:
        return 'CLOSED'
      default:
        return 'UNKNOWN'
    }
  }
}

// 创建全局WebSocket管理器实例
const wsManager = new WebSocketManager()

// 自动连接
wsManager.connect().catch(error => {
  console.error('初始WebSocket连接失败:', error)
})

// 心跳检测
setInterval(() => {
  if (wsManager.getConnectionState() === 'OPEN') {
    wsManager.ping()
  }
}, 30000) // 每30秒发送一次心跳

export default wsManager
