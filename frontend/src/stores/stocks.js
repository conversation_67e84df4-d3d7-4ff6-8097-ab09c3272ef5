import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '../utils/api'

export const useStocksStore = defineStore('stocks', () => {
  const stocks = ref([])
  const loading = ref(false)
  const error = ref(null)
  const total = ref(0)

  const fetchStocks = async (params = {}) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.get('/api/stocks', { params })
      if (response.data.success) {
        stocks.value = response.data.data
        total.value = response.data.data.length
      } else {
        throw new Error(response.data.message)
      }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearStocks = () => {
    stocks.value = []
    total.value = 0
    error.value = null
  }

  return {
    stocks,
    loading,
    error,
    total,
    fetchStocks,
    clearStocks
  }
})
