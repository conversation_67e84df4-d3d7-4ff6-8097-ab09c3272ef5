import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '../utils/api'

export const useStrategiesStore = defineStore('strategies', () => {
  const strategies = ref([])
  const loading = ref(false)
  const error = ref(null)

  const fetchStrategies = async () => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.get('/api/strategies')
      if (response.data.success) {
        strategies.value = response.data.data
      } else {
        throw new Error(response.data.message)
      }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const runBacktest = async (backtestData) => {
    try {
      const response = await api.post('/api/backtest', backtestData)
      if (response.data.success) {
        return response.data.data
      } else {
        throw new Error(response.data.message)
      }
    } catch (err) {
      throw err
    }
  }

  const fetchData = async (fetchData) => {
    try {
      const response = await api.post('/api/data/fetch', fetchData)
      if (response.data.success) {
        return response.data.data
      } else {
        throw new Error(response.data.message)
      }
    } catch (err) {
      throw err
    }
  }

  return {
    strategies,
    loading,
    error,
    fetchStrategies,
    runBacktest,
    fetchData
  }
})
