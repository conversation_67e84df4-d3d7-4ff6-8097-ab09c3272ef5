import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '../utils/api'

export const useSystemStore = defineStore('system', () => {
  const systemInfo = ref({
    platform_name: '',
    version: '',
    status: '检查中',
    uptime: '',
    features: []
  })
  
  const loading = ref(false)
  const error = ref(null)

  const fetchSystemInfo = async () => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.get('/api/system/info')
      if (response.data.success) {
        systemInfo.value = {
          ...response.data.data,
          status: '运行中'
        }
      } else {
        throw new Error(response.data.message)
      }
    } catch (err) {
      error.value = err.message
      systemInfo.value.status = '连接失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const checkHealth = async () => {
    try {
      const response = await api.get('/api/health')
      return response.data.success
    } catch (err) {
      return false
    }
  }

  return {
    systemInfo,
    loading,
    error,
    fetchSystemInfo,
    checkHealth
  }
})
