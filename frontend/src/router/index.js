import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue'),
    meta: { title: '首页概览' }
  },
  {
    path: '/stocks',
    name: 'Stocks',
    component: () => import('../views/Stocks.vue'),
    meta: { title: '股票列表' }
  },
  {
    path: '/data-fetch',
    name: 'DataFetch',
    component: () => import('../views/DataFetch.vue'),
    meta: { title: '数据获取' }
  },
  {
    path: '/strategies',
    name: 'Strategies',
    component: () => import('../views/Strategies.vue'),
    meta: { title: '策略列表' }
  },
  {
    path: '/backtest',
    name: 'Backtest',
    component: () => import('../views/Backtest.vue'),
    meta: { title: '执行回测' }
  },
  {
    path: '/results',
    name: 'Results',
    component: () => import('../views/Results.vue'),
    meta: { title: '回测结果' }
  },
  {
    path: '/performance',
    name: 'Performance',
    component: () => import('../views/Performance.vue'),
    meta: { title: '性能监控' }
  },
  {
    path: '/logs',
    name: 'Logs',
    component: () => import('../views/Logs.vue'),
    meta: { title: '系统日志' }
  },
  {
    path: '/realtime',
    name: 'RealTime',
    component: () => import('../views/RealTime.vue'),
    meta: { title: '实时数据' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

router.beforeEach((to, from, next) => {
  document.title = `${to.meta.title} - 量化投资平台`
  next()
})

export default router
