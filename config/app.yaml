# 应用总配置

# 应用基础配置
app:
  name: "量化交易策略平台"
  version: "0.1.0"
  log_level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  data_path: "db"
  
# 环境配置
environment:
  mode: "development"  # development, production, testing
  
# 数据源配置已移至data_source.yaml，但保留简化版本以便向后兼容
data_sources:
  default: "tushare"  # 默认数据源
  local: "local_file"  # 本地文件数据源
  
# 存储配置
storage:
  default: "mysql"  # 默认存储 - 已优化为MySQL
  # 默认路由规则
  routes:
    market_data: "mysql"     # 市场数据默认存储 - 使用优化后的MySQL
    financial_data: "mysql"  # 财务数据默认存储 - 使用优化后的MySQL
    factor_data: "mysql"     # 因子数据默认存储 - 使用优化后的MySQL
    backtest_results: "mysql"  # 回测结果默认存储 - 使用优化后的MySQL

# 数据缓存配置
data:
  cache_dir: "data/cache"  # 数据缓存目录

# 多级缓存配置 (基于真实场景测试优化)
cache:
  # 缓存策略: single(单级), multi(多级)
  strategy: "multi"

  # L1缓存: 内存缓存 (最热数据，超高速访问)
  l1:
    type: "memory"
    max_size: 2000        # 增加到2000条目，支持更多热数据
    ttl: 3600            # 1小时TTL，延长热数据保留时间
    enable_stats: true
    enabled: true

  # L2缓存: Redis缓存 (热数据，分布式共享)
  l2:
    type: "redis"
    host: "localhost"
    port: 6379
    db: 0                # 生产环境使用db=0
    password: null
    ttl: 7200            # 2小时TTL，平衡性能和数据新鲜度
    key_prefix: "quantification:prod:"  # 生产环境前缀
    connection_pool_size: 15  # 增加连接池大小
    socket_timeout: 3.0  # 减少超时时间，提高响应速度
    socket_connect_timeout: 3.0
    serialization: "json"  # JSON序列化，便于调试和监控
    enable_stats: true
    enabled: true

  # L3缓存: 磁盘缓存 (冷数据，持久化存储)
  l3:
    type: "disk"
    cache_dir: "data/cache/prod"  # 生产环境缓存目录
    ttl: 172800          # 48小时TTL，延长冷数据保留
    max_size_mb: 2048    # 增加到2GB，支持更多历史数据
    enable_stats: true
    enabled: true

  # 缓存穿透策略 (智能多级查找)
  penetration:
    l1_to_l2: true       # L1未命中时查询L2
    l2_to_l3: true       # L2未命中时查询L3
    write_back: true     # 启用回写策略，提高命中率

  # 缓存预热配置 (可选的性能优化)
  preload:
    enabled: false       # 默认关闭，按需启用
    symbols: [           # 热门股票预热列表
      "000001.SZ", "000002.SZ", "600000.SH", "600036.SH",
      "600519.SH", "000858.SZ", "002415.SZ", "600276.SH"
    ]
    data_types: ["daily", "basic"]  # 预热数据类型
    schedule: "0 8 * * 1-5"  # 工作日早8点预热

  # 缓存性能优化
  optimization:
    # 批量操作优化
    batch_size: 100      # 批量操作大小
    batch_timeout: 5.0   # 批量操作超时

    # 压缩优化
    compression:
      enabled: false     # 暂时关闭压缩，优先响应速度
      algorithm: "gzip"  # 压缩算法
      threshold: 1024    # 超过1KB才压缩

    # 监控和告警
    monitoring:
      enabled: true      # 启用监控
      hit_rate_threshold: 0.6  # 命中率告警阈值60%
      response_time_threshold: 1.0  # 响应时间告警阈值1秒
    
# 回测配置
backtest:
  default_capital: 1000000  # 默认初始资金
  slippage_model: "fixed"  # 滑点模型
  fee_model: "china_stock"  # 手续费模型
  benchmark: "000300.SH"  # 默认基准

# 风险控制配置
risk:
  max_position_pct: 0.1  # 单一持仓最大比例
  max_drawdown: 0.2     # 最大回撤限制
  stop_loss: 0.05       # 止损比例
  
# 系统资源配置
resources:
  max_workers: 4  # 最大工作线程数
  memory_limit: 0  # 内存限制(MB), 0表示不限制
  use_cache: true  # 是否使用缓存

# 运维自动化配置
ops_automation:
  # 基础配置
  enabled: true  # 是否启用运维自动化

  # 监控配置
  monitoring:
    check_interval: 30  # 监控检查间隔(秒)
    stats_update_interval: 10  # 统计更新间隔(秒)
    enable_performance_monitoring: true  # 启用性能监控

  # 告警配置
  alerting:
    # CPU告警阈值
    cpu_warning_threshold: 80  # CPU使用率警告阈值(%)
    cpu_critical_threshold: 90  # CPU使用率严重阈值(%)

    # 内存告警阈值
    memory_warning_threshold: 85  # 内存使用率警告阈值(%)
    memory_critical_threshold: 95  # 内存使用率严重阈值(%)

    # 磁盘告警阈值
    disk_warning_threshold: 80  # 磁盘使用率警告阈值(%)
    disk_critical_threshold: 90  # 磁盘使用率严重阈值(%)
    disk_free_warning_gb: 10  # 磁盘剩余空间警告阈值(GB)
    disk_free_critical_gb: 5   # 磁盘剩余空间严重阈值(GB)

    # 数据库告警阈值
    db_query_warning_ms: 500   # 数据库查询警告阈值(毫秒)
    db_query_critical_ms: 1000 # 数据库查询严重阈值(毫秒)

  # 自动化任务配置
  automation:
    # 任务执行配置
    task_queue_check_interval: 5  # 任务队列检查间隔(秒)
    task_timeout: 300  # 任务执行超时时间(秒)
    max_concurrent_tasks: 5  # 最大并发任务数

    # 默认冷却时间配置
    default_cooldown_minutes: 30  # 默认冷却时间(分钟)

    # 自动化操作配置
    actions:
      clear_cache:
        enabled: true
        cache_types: ["memory", "disk"]

      clean_logs:
        enabled: true
        retention_days: 7
        log_dirs: ["logs", "/var/log/quantification"]

      restart_service:
        enabled: false  # 默认禁用重启服务
        service_name: "quantification"

      optimize_database:
        enabled: true
        optimization_type: "index"

      backup_data:
        enabled: true
        backup_type: "incremental"
        backup_path: "/backup"

  # 智能运维引擎配置
  intelligence:
    # 异常检测配置
    anomaly_detection:
      enabled: true
      contamination: 0.1  # 异常数据比例
      min_samples: 100    # 最小训练样本数

    # 容量预测配置
    capacity_prediction:
      enabled: true
      prediction_horizon_hours: 24  # 预测时间窗口(小时)
      baseline_window_hours: 24     # 基线计算窗口(小时)

    # 性能洞察配置
    performance_insights:
      enabled: true

  # Web仪表板配置
  dashboard:
    enabled: true
    port: 8002  # 仪表板端口
    auto_refresh_interval: 30  # 自动刷新间隔(秒)
