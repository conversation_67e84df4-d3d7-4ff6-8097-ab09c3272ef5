# MySQL配置文件 - 针对量化投资平台优化
# 适用于MySQL 9.3.0，针对大数据量写入和查询优化

[mysqld]
# 基础配置
port = 3306
socket = /tmp/mysql.sock
datadir = /opt/homebrew/var/mysql
pid-file = /opt/homebrew/var/mysql/mysql.pid

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# 连接配置
max_connections = 200                    # 增加最大连接数
max_connect_errors = 1000               # 连接错误限制
connect_timeout = 10                    # 连接超时
wait_timeout = 28800                    # 等待超时
interactive_timeout = 28800             # 交互超时

# InnoDB存储引擎优化 - 核心优化
innodb_buffer_pool_size = 1G            # 缓冲池大小（根据可用内存调整）
innodb_buffer_pool_instances = 8        # 缓冲池实例数
# innodb_log_file_size = 256M           # MySQL 9.3.0中已移除此参数
innodb_redo_log_capacity = 256M         # 新的redo日志容量参数
innodb_log_buffer_size = 64M            # 日志缓冲大小
innodb_flush_log_at_trx_commit = 2      # 日志刷新策略（性能优化）
innodb_flush_method = O_DIRECT          # 刷新方法
innodb_file_per_table = 1               # 每表一个文件
innodb_open_files = 300                 # 打开文件数限制

# InnoDB写入优化
innodb_write_io_threads = 8             # 写入IO线程数
innodb_read_io_threads = 8              # 读取IO线程数
innodb_io_capacity = 2000               # IO容量
innodb_io_capacity_max = 4000           # 最大IO容量
innodb_lru_scan_depth = 2000            # LRU扫描深度

# 批量插入优化
bulk_insert_buffer_size = 64M           # 批量插入缓冲
innodb_autoinc_lock_mode = 2            # 自增锁模式
innodb_lock_wait_timeout = 50           # 锁等待超时

# 查询优化
tmp_table_size = 256M                   # 临时表大小
max_heap_table_size = 256M              # 内存表大小
join_buffer_size = 256M                 # 连接缓冲大小
sort_buffer_size = 2M                   # 排序缓冲大小
read_buffer_size = 2M                   # 读取缓冲大小
read_rnd_buffer_size = 4M               # 随机读取缓冲大小

# 表缓存优化
table_open_cache = 2000                 # 表缓存
table_definition_cache = 1400           # 表定义缓存
open_files_limit = 65535                # 打开文件限制

# 线程缓存
thread_cache_size = 50                  # 线程缓存大小
thread_stack = 256K                     # 线程栈大小

# 二进制日志（开发环境可关闭以提高性能）
log-bin = /opt/homebrew/var/mysql/mysql-bin
binlog_format = ROW
max_binlog_size = 100M
binlog_expire_logs_seconds = 604800     # 7天 = 7*24*3600秒 (替代expire_logs_days)
sync_binlog = 0                         # 性能优化：异步写入

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /opt/homebrew/var/mysql/slow.log
long_query_time = 2                     # 慢查询阈值（秒）
log_queries_not_using_indexes = 1       # 记录未使用索引的查询

# 错误日志
log-error = /opt/homebrew/var/mysql/error.log

# 通用查询日志（开发环境可开启）
general_log = 0                         # 关闭以提高性能
general_log_file = /opt/homebrew/var/mysql/general.log

# SQL模式
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# 时区设置
default-time-zone = '+08:00'

# 性能模式（可选开启）
performance_schema = ON
performance_schema_max_table_instances = 12500
performance_schema_max_table_handles = 4000

# 分区支持
partition = ON

[mysql]
# 客户端配置
default-character-set = utf8mb4
socket = /tmp/mysql.sock

[mysqldump]
# 备份配置
quick
lock-tables = false
single-transaction
default-character-set = utf8mb4
max_allowed_packet = 1024M

[client]
# 客户端通用配置
default-character-set = utf8mb4
socket = /tmp/mysql.sock
port = 3306

# 开发环境特殊配置
[mysqld_safe]
log-error = /opt/homebrew/var/mysql/error.log
pid-file = /opt/homebrew/var/mysql/mysql.pid

# 内存使用说明：
# 当前配置大约使用 1.5-2GB 内存
# 主要内存分配：
# - innodb_buffer_pool_size: 1GB
# - 其他缓冲区总计: 约500MB
# 
# 如果系统内存不足，可以调整以下参数：
# - innodb_buffer_pool_size 减少到 512M
# - join_buffer_size 减少到 128M
# - tmp_table_size 和 max_heap_table_size 减少到 128M
