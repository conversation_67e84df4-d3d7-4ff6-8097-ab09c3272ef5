# 日志配置

# 全局日志配置
version: 1
disable_existing_loggers: false

# 格式化器配置
formatters:
  standard:
    format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"
  detailed:
    format: "%(asctime)s [%(levelname)s] %(name)s (%(filename)s:%(lineno)d): %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"

# 处理器配置
handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout
    
  file:
    class: logging.FileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/app.log
    encoding: utf8
    
  error_file:
    class: logging.FileHandler
    level: ERROR
    formatter: detailed
    filename: logs/error.log
    encoding: utf8
    
  performance:
    class: logging.FileHandler
    level: INFO
    formatter: standard
    filename: logs/performance.log
    encoding: utf8

# 日志器配置
loggers:
  # 根日志器
  "":
    level: INFO
    handlers: [console, file]
    propagate: true
    
  # 数据模块日志器
  data:
    level: DEBUG
    handlers: [console, file]
    propagate: false
    
  # 策略模块日志器
  strategy:
    level: DEBUG
    handlers: [console, file]
    propagate: false
    
  # 回测模块日志器
  backtest:
    level: DEBUG
    handlers: [console, file, performance]
    propagate: false
    
  # 错误处理模块日志器
  error:
    level: ERROR
    handlers: [console, error_file]
    propagate: false
