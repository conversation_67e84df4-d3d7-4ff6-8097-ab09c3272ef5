{"cluster_name": "quantification_cluster", "description": "量化投资平台分布式数据库集群配置", "version": "1.0.0", "nodes": [{"node_id": "mysql_master", "host": "localhost", "port": 3306, "database": "quant_master", "username": "root", "password": "", "role": "master", "weight": 10, "db_type": "mysql", "description": "MySQL主节点 - 负责写入操作"}, {"node_id": "mysql_slave1", "host": "localhost", "port": 3306, "database": "quant_slave1", "username": "root", "password": "", "role": "replica", "weight": 8, "db_type": "mysql", "description": "MySQL从节点1 - 负责读取操作"}, {"node_id": "mysql_slave2", "host": "localhost", "port": 3306, "database": "quant_slave2", "username": "root", "password": "", "role": "replica", "weight": 8, "db_type": "mysql", "description": "MySQL从节点2 - 负责读取操作"}, {"node_id": "sqlite_cache", "host": "localhost", "port": 0, "database": "output/data/db/sqlite/mysql_cache.db", "username": "", "password": "", "role": "replica", "weight": 5, "db_type": "sqlite", "description": "SQLite缓存节点 - 本地缓存"}], "sharding": {"default_strategy": "hash", "virtual_nodes_per_physical": 150, "replication_factor": 2, "tables": {"daily": {"shard_key": "ts_code", "shard_count": 4, "strategy": "hash", "description": "日线数据按股票代码分片"}, "daily_basic": {"shard_key": "ts_code", "shard_count": 4, "strategy": "hash", "description": "日线基础数据按股票代码分片"}, "weekly": {"shard_key": "ts_code", "shard_count": 2, "strategy": "hash", "description": "周线数据按股票代码分片"}, "monthly": {"shard_key": "ts_code", "shard_count": 2, "strategy": "hash", "description": "月线数据按股票代码分片"}, "income": {"shard_key": "ts_code", "shard_count": 2, "strategy": "hash", "description": "利润表数据按股票代码分片"}, "balancesheet": {"shard_key": "ts_code", "shard_count": 2, "strategy": "hash", "description": "资产负债表数据按股票代码分片"}, "cashflow": {"shard_key": "ts_code", "shard_count": 2, "strategy": "hash", "description": "现金流量表数据按股票代码分片"}, "stock_list": {"shard_key": "ts_code", "shard_count": 1, "strategy": "single", "description": "股票列表不分片"}}}, "load_balancing": {"read_strategy": "weighted_round_robin", "write_strategy": "hash_based", "prefer_local_reads": true, "max_connections_per_node": 100, "connection_timeout": 30, "query_timeout": 60}, "failover": {"health_check_interval": 30, "max_error_count": 3, "retry_interval": 60, "auto_failover": true, "notification_enabled": false}, "performance": {"batch_size": 10000, "max_batch_size": 50000, "commit_interval": 5, "connection_pool_size": 20, "connection_pool_max_overflow": 50, "enable_query_cache": true, "cache_ttl": 300}, "monitoring": {"enable_metrics": true, "metrics_interval": 60, "slow_query_threshold": 1.0, "log_level": "INFO", "enable_performance_logging": true}, "security": {"enable_ssl": false, "ssl_cert_path": "", "ssl_key_path": "", "enable_encryption": false, "encryption_key": ""}}