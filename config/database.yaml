# 数据库配置

# SQLite配置
sqlite:
  database: "db/sqlite/quantification.db"
  
# MySQL配置
mysql:
  host: "localhost"
  port: 3306
  user: "root"
  password: ""
  database: "quantification"
  charset: "utf8mb4"
  
# Redis配置（缓存）
redis:
  host: "localhost"
  port: 6379
  db: 0
  password: ""
  
# 连接池配置
connection_pool:
  max_overflow: 10
  pool_size: 5
  timeout: 30
  
# 数据表配置
tables:
  # 市场数据表
  daily_price:
    name: "daily_price"
    schema:
      - name: "ts_code"
        type: "VARCHAR(20)"
        primary: true
      - name: "trade_date"
        type: "DATE"
        primary: true
      - name: "open"
        type: "FLOAT"
      - name: "high"
        type: "FLOAT"
      - name: "low"
        type: "FLOAT"
      - name: "close"
        type: "FLOAT"
      - name: "vol"
        type: "FLOAT"
      - name: "amount"
        type: "FLOAT"
    indexes:
      - name: "idx_daily_price_date"
        columns: ["trade_date"]
  
  # 股票基本信息表
  stock_basic:
    name: "stock_basic"
    schema:
      - name: "ts_code"
        type: "VARCHAR(20)"
        primary: true
      - name: "symbol"
        type: "VARCHAR(10)"
      - name: "name"
        type: "VARCHAR(100)"
      - name: "area"
        type: "VARCHAR(50)"
      - name: "industry"
        type: "VARCHAR(50)"
      - name: "market"
        type: "VARCHAR(20)"
      - name: "list_date"
        type: "DATE"
    indexes:
      - name: "idx_stock_basic_symbol"
        columns: ["symbol"]
        
  # 财务数据表
  income:
    name: "income"
    schema:
      - name: "ts_code"
        type: "VARCHAR(20)"
        primary: true
      - name: "ann_date"
        type: "DATE"
      - name: "f_ann_date"
        type: "DATE"
      - name: "end_date"
        type: "DATE"
        primary: true
      - name: "report_type"
        type: "INT"
      - name: "revenue"
        type: "FLOAT"
      - name: "n_income"
        type: "FLOAT"
    indexes:
      - name: "idx_income_date"
        columns: ["end_date"]
