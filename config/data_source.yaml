# 数据源配置

# Tushare数据源配置
tushare:
  # API配置
  api:
    token: "c4176f1746c8638b2ebff780654d7aceb0adb3a189a884eae65e2f95"  # Tushare令牌
    timeout: 30  # 请求超时时间(秒)
    
  # 数据调用配置
  rate_limit: 
    daily: 200000  # 每天调用次数上限
    
  # 行情数据API映射
  market_data:
    daily: "daily"  # 日线行情
    weekly: "weekly"  # 周线行情
    monthly: "monthly"  # 月线行情
    mins: "mins"  # 分钟线
    
  # 指数数据API映射
  index_data:
    daily: "index_daily"  # 指数日线
    weekly: "index_weekly"  # 指数周线
    monthly: "index_monthly"  # 指数月线
    
  # 基本面数据API映射
  fundamental:
    stock_basic: "stock_basic"  # 股票列表
    income: "income"  # 利润表
    balance: "balancesheet"  # 资产负债表
    cashflow: "cashflow"  # 现金流量表
    
  # 数据更新频率(天)
  update_freq:
    daily: 1  # 每天更新
    weekly: 7  # 每周更新
    monthly: 30  # 每月更新
    
  # 默认时间周期配置
  default_periods:
    start_date: "20230101"  # 默认开始日期
    end_date: "20231231"    # 默认结束日期
    
  # 数据获取策略配置
  fetch_strategy:
    period_threshold_days: 500  # 周期阈值（天数），默认500天
    batch_size_by_date: 30   # 按日期批量获取时一次获取多少只股票
    batch_size_by_stock: 10  # 按股票批量获取时一次获取多少天数据
    max_workers: 3       # 最大工作线程数
    
# 本地文件数据源配置
local_file:
  # 本地数据路径
  base_path: "db/data"
  
  # 文件类型映射
  file_type:
    csv: ".csv"
    parquet: ".parquet"
    pickle: ".pkl"
    hdf5: ".h5"
    
  # 数据格式
  format:
    date_format: "%Y%m%d"
    encoding: "utf-8"
    parse_dates: ["trade_date", "ann_date", "end_date"]
    
  # 目录结构
  directories:
    daily: "market/daily"
    weekly: "market/weekly"
    fundamental: "fundamental"
    index: "index"
