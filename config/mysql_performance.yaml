# MySQL性能优化配置
# 针对量化投资平台的大数据量写入和查询优化

# 基础连接配置
connection:
  host: "localhost"
  port: 3306
  user: "root"
  password: ""
  database: "quantification"
  charset: "utf8mb4"
  
# 连接池配置 - 针对高并发优化
connection_pool:
  # 连接池大小
  pool_size: 20              # 基础连接数
  max_overflow: 50           # 最大溢出连接数
  pool_timeout: 30           # 获取连接超时时间
  pool_recycle: 3600         # 连接回收时间（秒）
  pool_pre_ping: true        # 连接前ping检查
  
  # 连接参数优化
  connect_args:
    autocommit: false        # 禁用自动提交，使用批量事务
    connect_timeout: 10      # 连接超时
    read_timeout: 30         # 读取超时
    write_timeout: 30        # 写入超时
    charset: "utf8mb4"
    use_unicode: true
    
# 批量写入优化配置
batch_insert:
  # 批次大小配置
  default_batch_size: 10000   # 默认批次大小
  max_batch_size: 50000      # 最大批次大小
  
  # 不同表的优化批次大小
  table_specific:
    daily: 20000             # 日线数据
    daily_basic: 15000       # 日线基础数据
    income: 5000             # 利润表
    balancesheet: 5000       # 资产负债表
    cashflow: 5000           # 现金流量表
    
  # 写入策略
  insert_method: "bulk"      # bulk/batch/single
  ignore_duplicates: true    # 忽略重复数据
  on_duplicate_key: "ignore" # ignore/update/error
  
# 表结构优化配置
table_optimization:
  # 存储引擎
  engine: "InnoDB"           # InnoDB支持事务和外键
  
  # 字符集
  charset: "utf8mb4"
  collation: "utf8mb4_unicode_ci"
  
  # 行格式
  row_format: "DYNAMIC"      # 支持大字段和压缩
  
  # 分区策略
  partitioning:
    enabled: true
    strategy: "RANGE"        # 按时间范围分区
    partition_column: "trade_date"
    partition_interval: "MONTH"  # 按月分区
    
# 索引优化配置
index_optimization:
  # 自动创建索引
  auto_create_indexes: true
  
  # 通用索引策略
  common_indexes:
    - columns: ["trade_date"]
      type: "BTREE"
      name: "idx_trade_date"
    - columns: ["ts_code"]
      type: "BTREE" 
      name: "idx_ts_code"
    - columns: ["ts_code", "trade_date"]
      type: "BTREE"
      name: "idx_ts_code_date"
      
  # 表特定索引
  table_specific_indexes:
    daily:
      - columns: ["trade_date", "ts_code"]
        type: "BTREE"
        unique: true
        name: "uk_daily_date_code"
    daily_basic:
      - columns: ["trade_date", "ts_code"]
        type: "BTREE"
        unique: true
        name: "uk_basic_date_code"
        
# 查询优化配置
query_optimization:
  # 查询缓存
  query_cache:
    enabled: true
    size: "256M"
    
  # 连接优化
  join_optimization:
    join_buffer_size: "256M"
    sort_buffer_size: "2M"
    
  # 临时表优化
  tmp_table:
    tmp_table_size: "256M"
    max_heap_table_size: "256M"
    
# 内存优化配置
memory_optimization:
  # InnoDB缓冲池
  innodb_buffer_pool_size: "1G"    # 根据可用内存调整
  innodb_buffer_pool_instances: 8
  
  # 日志缓冲
  innodb_log_buffer_size: "64M"
  innodb_log_file_size: "256M"
  
  # 其他内存设置
  key_buffer_size: "256M"
  read_buffer_size: "2M"
  read_rnd_buffer_size: "4M"
  
# 事务优化配置
transaction_optimization:
  # 事务隔离级别
  isolation_level: "READ_COMMITTED"  # 减少锁竞争
  
  # 自动提交
  autocommit: false                  # 使用显式事务
  
  # 批量事务配置
  batch_transaction:
    enabled: true
    batch_size: 10000               # 每个事务的记录数
    commit_interval: 5              # 提交间隔（秒）
    
# 监控配置
monitoring:
  # 性能监控
  performance_monitoring:
    enabled: true
    slow_query_log: true
    slow_query_time: 2              # 慢查询阈值（秒）
    
  # 统计信息
  statistics:
    enabled: true
    update_interval: 300            # 统计更新间隔（秒）
    
  # 日志配置
  logging:
    general_log: false              # 关闭通用日志以提高性能
    error_log: true
    binary_log: false               # 开发环境可关闭
    
# 数据类型映射优化
data_type_mapping:
  # 优化的数据类型映射
  optimized_types:
    # 股票代码
    ts_code: "VARCHAR(12)"          # 足够长度，避免TEXT
    
    # 日期时间
    trade_date: "DATE"              # 使用DATE而非DATETIME
    datetime: "DATETIME(3)"         # 毫秒精度
    
    # 价格数据
    price: "DECIMAL(10,3)"          # 精确的价格存储
    amount: "DECIMAL(20,2)"         # 大金额存储
    
    # 数量
    volume: "BIGINT UNSIGNED"       # 无符号大整数
    
    # 比率
    ratio: "DECIMAL(8,4)"           # 比率数据
    
# 备份和恢复配置
backup_recovery:
  # 自动备份
  auto_backup:
    enabled: false                  # 开发环境可关闭
    schedule: "0 2 * * *"          # 每天凌晨2点
    retention_days: 7
    
  # 备份压缩
  compression:
    enabled: true
    algorithm: "gzip"
    
# 安全配置
security:
  # SSL配置
  ssl:
    enabled: false                  # 开发环境可关闭
    
  # 用户权限
  user_privileges:
    read_only: false
    max_connections: 100
    
# 开发环境特殊配置
development:
  # 调试模式
  debug_mode: true
  
  # 性能分析
  profiling:
    enabled: true
    slow_query_analysis: true
    
  # 测试数据
  test_data:
    auto_generate: false
    sample_size: 1000
