#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
量化投资平台Web服务启动脚本
- 启动FastAPI Web服务
- 配置开发/生产环境
- 提供命令行参数支持
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging(log_level: str = "INFO"):
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )

def check_dependencies():
    """检查必要的依赖"""
    required_packages = [
        "fastapi",
        "uvicorn",
        "pydantic",
        "psutil"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少必要依赖: {', '.join(missing_packages)}")
        print("请运行: pip install fastapi uvicorn pydantic psutil")
        return False
    
    return True

def create_directories():
    """创建必要的目录"""
    directories = [
        "logs",
        "output",
        "src/web/static",
        "src/web/templates"
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ 目录已创建: {directory}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="量化投资平台Web服务")
    parser.add_argument(
        "--host", 
        default="0.0.0.0", 
        help="服务器主机地址 (默认: 0.0.0.0)"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=8000, 
        help="服务器端口 (默认: 8000)"
    )
    parser.add_argument(
        "--reload", 
        action="store_true", 
        help="启用自动重载 (开发模式)"
    )
    parser.add_argument(
        "--log-level", 
        choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
        default="INFO",
        help="日志级别 (默认: INFO)"
    )
    parser.add_argument(
        "--workers", 
        type=int, 
        default=1,
        help="工作进程数 (生产模式, 默认: 1)"
    )
    parser.add_argument(
        "--env", 
        choices=["development", "production"], 
        default="development",
        help="运行环境 (默认: development)"
    )
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    print("🚀 启动量化投资平台Web服务...")
    print(f"📍 项目根目录: {project_root}")
    print(f"🌐 服务地址: http://{args.host}:{args.port}")
    print(f"🔧 运行环境: {args.env}")
    print(f"📊 日志级别: {args.log_level}")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建必要目录
    create_directories()
    
    # 设置环境变量
    os.environ["PYTHONPATH"] = str(project_root)
    os.environ["QUANTIFICATION_ENV"] = args.env
    
    try:
        import uvicorn
        
        # 配置uvicorn参数
        uvicorn_config = {
            "app": "src.web.app:app",
            "host": args.host,
            "port": args.port,
            "log_level": args.log_level.lower(),
            "access_log": True,
        }
        
        if args.env == "development":
            # 开发环境配置
            uvicorn_config.update({
                "reload": args.reload,
                "reload_dirs": [str(project_root / "src")],
                "reload_excludes": ["*.pyc", "*.pyo", "__pycache__"]
            })
            print("🔄 开发模式: 启用自动重载")
        else:
            # 生产环境配置
            uvicorn_config.update({
                "workers": args.workers,
                "loop": "uvloop",
                "http": "httptools"
            })
            print(f"🏭 生产模式: {args.workers} 个工作进程")
        
        print("\n" + "="*50)
        print("📚 API文档地址:")
        print(f"   Swagger UI: http://{args.host}:{args.port}/docs")
        print(f"   ReDoc:      http://{args.host}:{args.port}/redoc")
        print("="*50 + "\n")
        
        # 启动服务器
        uvicorn.run(**uvicorn_config)
        
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        logger.error(f"启动服务失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
