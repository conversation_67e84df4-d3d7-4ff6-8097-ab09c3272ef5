# 核心库 - 基础数据处理和分析
numpy>=1.20.0
pandas>=1.3.0
scipy>=1.7.0
matplotlib>=3.4.0      # 基础可视化
seaborn>=0.11.0       # 高级可视化
# plotly>=5.3.0        # 交互式可视化（暂不需要）
# jupyter>=1.0.0       # 开发环境（可选）
pytz>=2021.1

# 数据获取 - A股数据
tushare>=1.2.60         # A股数据获取
# akshare>=1.0.0       # 多市场数据获取（暂不需要）
pandas-datareader>=0.10.0  # 全球市场数据获取（暂不需要）
yfinance>=0.1.63        # Yahoo财经数据获取
pymongo>=4.0.1         # 数据存储
sqlalchemy>=1.4.0      # SQL ORM
arctic>=1.79.3         # 时间序列数据库
pytables>=3.7.0        # HDF5数据存储
pyarrow>=7.0.0         # 高效数据交换格式

# 数据存储 - 基础存储
pymysql>=1.0.0         # MySQL连接器 
# 不需要单独安装sqlite3，它是Python标准库的一部分

# 文件存储 - CSV和Parquet支持
# pyarrow>=6.0.0       # Parquet支持（暂不需要）
# tables>=3.6.0        # HDF5支持（暂不需要）
# openpyxl>=3.0.0      # Excel支持（暂不需要）

# 量化分析 - 策略开发必备
statsmodels>=0.13.0    # 统计模型
scikit-learn>=1.0.0    # 机器学习
ta-lib>=0.4.24        # 技术分析指标 - 需要额外安装C库，暂时注释掉
pyfolio>=0.9.2         # 投资组合分析
empyrical>=0.5.5       # 绩效指标
arch>=5.0.0            # GARCH模型支持

# 优化工具 - 组合优化
cvxpy>=1.2.0         # 凸优化
pypfopt>=1.5.0         # 投资组合优化

# 并行处理 - 性能优化
# ray>=1.9.0           # 分布式计算（暂不需要）
joblib>=1.1.0          # 本地并行

# Web应用与监控 - 暂不需要
flask>=2.0.0           # Web服务
# dash>=2.0.0          # 交互式仪表盘
# apscheduler>=3.8.0   # 定时任务调度

# 日志和配置 - 基础工具
pyyaml>=6.0.0          # YAML解析
loguru>=0.5.0          # 高级日志

# 测试工具 - 开发必备
pytest>=7.0.0          # 单元测试
pytest-cov>=2.12.0     # 测试覆盖率
pytest-asyncio>=0.18.0  # 异步测试

# 其他工具 - 开发辅助
tqdm>=4.62.0           # 进度条
requests>=2.26.0       # HTTP请求
python-dotenv>=0.19.0  # 环境变量管理

# Web框架和报告生成
jinja2>=3.0.0

# 策略与回测
backtrader>=1.9.76.123

# 风险管理
pypfopt>=1.5.0  # 投资组合优化

# 交易接口
ccxt>=1.60.0  # 加密货币交易所API
alpaca-trade-api>=2.0.0  # Alpaca交易API
tdameritrade>=0.5.0  # TD Ameritrade API
ib_insync>=0.9.70  # Interactive Brokers API
futu-api>=5.0.0  # 富途API

# 实时数据处理
redis>=4.3.0  # 缓存和消息队列
kafka-python>=2.0.2  # 消息队列
websocket-client>=1.2.0  # WebSocket客户端
aiohttp>=3.8.0  # 异步HTTP客户端
asyncio>=3.4.3  # 异步IO
motor>=3.0.0  # 异步MongoDB驱动

# Web 开发
fastapi>=0.75.0  # API框架
uvicorn>=0.17.0  # ASGI服务器
pydantic>=1.9.0  # 数据验证

# 工具与测试
hypothesis>=6.0.0  # 测试数据生成
black>=22.1.0  # 代码格式化
flake8>=4.0.0  # 代码检查
mypy>=0.930  # 类型检查
jupyterlab>=3.3.0  # 交互式开发环境

# 文档
sphinx>=4.4.0  # 文档生成
sphinx-rtd-theme>=1.0.0  # 文档主题
